#!/bin/bash

# Script de démarrage pour PresencePro en mode développement

echo "🚀 Démarrage de PresencePro avec Supabase en mode développement..."

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si nous sommes dans le bon répertoire
if [ ! -f "README.md" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Ce script doit être exécuté depuis la racine du projet PresencePro"
    exit 1
fi

# Fonction pour démarrer le backend
start_backend() {
    print_status "Démarrage du backend Django..."
    cd backend
    
    # Appliquer les migrations
    print_status "Application des migrations..."
    python3 manage.py migrate
    
    # Démarrer le serveur Django
    print_success "Backend Django démarré sur http://localhost:8000"
    python3 manage.py runserver &
    BACKEND_PID=$!
    cd ..
}

# Fonction pour démarrer le frontend
start_frontend() {
    print_status "Démarrage du frontend React..."
    cd frontend
    
    # Vérifier si node_modules existe
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules non trouvé. Installation des dépendances..."
        npm install
    fi
    
    # Démarrer le serveur React
    print_success "Frontend React démarré sur http://localhost:3000"
    npm start &
    FRONTEND_PID=$!
    cd ..
}

# Fonction pour nettoyer les processus à l'arrêt
cleanup() {
    print_status "Arrêt des serveurs..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    print_success "Serveurs arrêtés"
    exit 0
}

# Capturer Ctrl+C pour nettoyer proprement
trap cleanup SIGINT

# Démarrer les services
start_backend
sleep 3
start_frontend

print_success "PresencePro est maintenant en cours d'exécution !"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend:  http://localhost:8000"
echo "👨‍💼 Admin:    http://localhost:8000/admin"
echo ""
echo "Appuyez sur Ctrl+C pour arrêter tous les services"

# Attendre indéfiniment
wait
