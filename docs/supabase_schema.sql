-- PresencePro Database Schema for Supabase PostgreSQL
-- Execute this script in the Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'teacher', 'student');
CREATE TYPE attendance_status AS ENUM ('present', 'absent', 'late', 'excused');
CREATE TYPE attendance_method AS ENUM ('manual', 'face_recognition', 'qr_code');

-- ==================== USERS TABLE ====================
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(150) UNIQUE NOT NULL,
  email VARCHAR(254) UNIQUE NOT NULL,
  first_name VARCHAR(150),
  last_name VARCHAR(150),
  role user_role DEFAULT 'student',
  phone_number VARCHAR(20),
  date_of_birth DATE,
  address TEXT,
  profile_picture TEXT,
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ==================== STUDENT GROUPS TABLE ====================
CREATE TABLE student_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  academic_year VARCHAR(20),
  level VARCHAR(50),
  specialization VARCHAR(100),
  student_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ==================== COURSES TABLE ====================
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL,
  description TEXT,
  teacher_id UUID REFERENCES users(id) ON DELETE SET NULL,
  student_group_id UUID REFERENCES student_groups(id) ON DELETE SET NULL,
  schedule JSONB,
  academic_year VARCHAR(20),
  semester VARCHAR(10),
  credits INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ==================== ATTENDANCE TABLE ====================
CREATE TABLE attendance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES users(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  time TIME NOT NULL,
  status attendance_status DEFAULT 'present',
  method attendance_method DEFAULT 'manual',
  confidence DECIMAL(5,2),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique attendance per student per course per day
  UNIQUE(student_id, course_id, date)
);

-- ==================== INDEXES ====================
-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- Courses indexes
CREATE INDEX idx_courses_teacher ON courses(teacher_id);
CREATE INDEX idx_courses_group ON courses(student_group_id);
CREATE INDEX idx_courses_code ON courses(code);
CREATE INDEX idx_courses_active ON courses(is_active);

-- Attendance indexes
CREATE INDEX idx_attendance_student ON attendance(student_id);
CREATE INDEX idx_attendance_course ON attendance(course_id);
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_status ON attendance(status);

-- ==================== ROW LEVEL SECURITY ====================
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- ==================== USERS POLICIES ====================
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Admins and teachers can view all profiles
CREATE POLICY "Admins and teachers can view all profiles" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'teacher')
    )
  );

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Only admins can insert new users
CREATE POLICY "Only admins can create users" ON users
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Only admins can delete users
CREATE POLICY "Only admins can delete users" ON users
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- ==================== STUDENT GROUPS POLICIES ====================
-- Everyone can view student groups
CREATE POLICY "Everyone can view student groups" ON student_groups
  FOR SELECT USING (true);

-- Only admins can manage student groups
CREATE POLICY "Only admins can manage student groups" ON student_groups
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- ==================== COURSES POLICIES ====================
-- Everyone can view courses
CREATE POLICY "Everyone can view courses" ON courses
  FOR SELECT USING (true);

-- Admins and teachers can create courses
CREATE POLICY "Admins and teachers can create courses" ON courses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'teacher')
    )
  );

-- Teachers can update their own courses, admins can update all
CREATE POLICY "Teachers can update own courses" ON courses
  FOR UPDATE USING (
    auth.uid() = teacher_id OR
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Only admins can delete courses
CREATE POLICY "Only admins can delete courses" ON courses
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- ==================== ATTENDANCE POLICIES ====================
-- Students can view their own attendance
CREATE POLICY "Students can view own attendance" ON attendance
  FOR SELECT USING (
    auth.uid() = student_id OR
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'teacher')
    )
  );

-- Teachers can manage attendance for their courses
CREATE POLICY "Teachers can manage course attendance" ON attendance
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM courses 
      WHERE id = course_id 
      AND teacher_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- ==================== FUNCTIONS ====================
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_groups_updated_at BEFORE UPDATE ON student_groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_updated_at BEFORE UPDATE ON attendance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==================== SAMPLE DATA ====================
-- Insert sample admin user (password will be set via Supabase Auth)
INSERT INTO users (id, username, email, first_name, last_name, role, is_active) VALUES
  ('00000000-0000-0000-0000-000000000001', 'admin', '<EMAIL>', 'Admin', 'System', 'admin', true);

-- Insert sample teacher
INSERT INTO users (id, username, email, first_name, last_name, role, is_active) VALUES
  ('00000000-0000-0000-0000-000000000002', 'prof.martin', '<EMAIL>', 'Jean', 'Martin', 'teacher', true);

-- Insert sample students
INSERT INTO users (id, username, email, first_name, last_name, role, is_active) VALUES
  ('00000000-0000-0000-0000-000000000003', 'marie.dubois', '<EMAIL>', 'Marie', 'Dubois', 'student', true),
  ('00000000-0000-0000-0000-000000000004', 'pierre.martin', '<EMAIL>', 'Pierre', 'Martin', 'student', true);

-- Insert sample student group
INSERT INTO student_groups (id, name, description, academic_year, level, specialization, student_count) VALUES
  ('00000000-0000-0000-0000-000000000001', 'L3 Informatique', 'Licence 3 Informatique', '2023-2024', 'L3', 'Informatique', 25);

-- Insert sample course
INSERT INTO courses (id, name, code, description, teacher_id, student_group_id, academic_year, semester, credits, schedule) VALUES
  ('00000000-0000-0000-0000-000000000001', 
   'Mathématiques Avancées', 
   'MATH301', 
   'Cours de mathématiques niveau avancé',
   '00000000-0000-0000-0000-000000000002',
   '00000000-0000-0000-0000-000000000001',
   '2023-2024',
   'S1',
   6,
   '[{"id": "1", "dayOfWeek": 1, "startTime": "09:00", "endTime": "11:00", "room": "A101", "building": "Bâtiment A"}]'::jsonb);

-- Insert sample attendance
INSERT INTO attendance (student_id, course_id, date, time, status, method) VALUES
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001', CURRENT_DATE, '09:00', 'present', 'manual'),
  ('00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000001', CURRENT_DATE, '09:05', 'late', 'manual');
