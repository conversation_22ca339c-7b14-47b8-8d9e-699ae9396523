# Configuration Firebase pour PresencePro

Ce guide vous explique comment configurer Firebase pour PresencePro avec vos informations de base de données.

## 📋 Informations de votre projet Firebase

Votre projet Firebase est déjà configuré avec les informations suivantes :

```javascript
{
  apiKey: "AIzaSyCMoA8Up7238cV0siW9-zOUvh4M9cz0_UE",
  authDomain: "presencepro-3f21f.firebaseapp.com",
  projectId: "presencepro-3f21f",
  storageBucket: "presencepro-3f21f.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:abb3656875005cb23831d2"
}
```

## 🔧 Configuration actuelle

### ✅ Frontend (React)
Les fichiers suivants ont été configurés avec vos informations :
- `frontend/src/config/firebase.ts` - Configuration Firebase
- `frontend/.env` - Variables d'environnement
- `frontend/.env.example` - Exemple de configuration

### ⚠️ Backend (Django) - Configuration incomplète
Pour que le backend fonctionne avec Firebase, vous devez :

1. **Générer une clé de service Firebase :**
   - Aller sur [Firebase Console](https://console.firebase.google.com/)
   - Sélectionner votre projet "presencepro-3f21f"
   - Aller dans "Paramètres du projet" (icône engrenage)
   - Onglet "Comptes de service"
   - Cliquer sur "Générer une nouvelle clé privée"
   - Télécharger le fichier JSON

2. **Configurer le backend :**
   - Ouvrir le fichier JSON téléchargé
   - Copier les valeurs dans `backend/.env` :

```bash
# Configuration Firebase pour PresencePro
FIREBASE_PROJECT_ID=presencepro-3f21f
FIREBASE_PRIVATE_KEY_ID=votre_private_key_id_du_json
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nvotre_private_key_du_json\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=votre_client_email_du_json
FIREBASE_CLIENT_ID=votre_client_id_du_json
```

## 🚀 Services Firebase à activer

Dans votre console Firebase, assurez-vous d'activer :

### 1. Authentication
- Aller dans "Authentication" > "Sign-in method"
- Activer "Email/Password"
- Optionnel : Activer d'autres méthodes (Google, Facebook, etc.)

### 2. Firestore Database
- Aller dans "Firestore Database"
- Créer une base de données
- Choisir le mode "Test" pour commencer (règles ouvertes)
- Sélectionner une région proche de vos utilisateurs

### 3. Storage
- Aller dans "Storage"
- Commencer avec les règles par défaut
- Sera utilisé pour stocker les photos de profil et images de reconnaissance faciale

## 📊 Structure de données Firestore recommandée

```
presencepro-3f21f/
├── users/
│   ├── {userId}/
│   │   ├── username: string
│   │   ├── email: string
│   │   ├── firstName: string
│   │   ├── lastName: string
│   │   ├── role: "admin" | "teacher" | "student"
│   │   ├── profilePicture: string (URL)
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   └── ...
├── courses/
│   ├── {courseId}/
│   │   ├── name: string
│   │   ├── code: string
│   │   ├── teacherId: string
│   │   ├── studentGroupId: string
│   │   ├── schedule: array
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   └── ...
├── attendance/
│   ├── {attendanceId}/
│   │   ├── studentId: string
│   │   ├── courseId: string
│   │   ├── date: string
│   │   ├── time: string
│   │   ├── status: "present" | "absent" | "late"
│   │   ├── method: "manual" | "face_recognition"
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   └── ...
└── studentGroups/
    ├── {groupId}/
    │   ├── name: string
    │   ├── academicYear: string
    │   ├── level: string
    │   ├── students: array of userIds
    │   ├── createdAt: timestamp
    │   └── updatedAt: timestamp
    └── ...
```

## 🔒 Règles de sécurité Firestore

Voici des règles de sécurité de base pour commencer :

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Règles pour les utilisateurs
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'teacher'];
    }
    
    // Règles pour les cours
    match /courses/{courseId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'teacher'];
    }
    
    // Règles pour les présences
    match /attendance/{attendanceId} {
      allow read, write: if request.auth != null;
    }
    
    // Règles pour les groupes d'étudiants
    match /studentGroups/{groupId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

## 🧪 Test de la configuration

Une fois la configuration terminée :

1. **Démarrer l'application :**
   ```bash
   ./start-dev.sh
   ```

2. **Accéder au test Firebase :**
   - Se connecter avec le compte admin (admin/admin123)
   - Aller sur http://localhost:3000/firebase-test
   - Exécuter les tests de connexion

3. **Vérifier les fonctionnalités :**
   - Test de connexion Firestore ✅
   - Test d'authentification Firebase
   - Test de lecture/écriture des données

## 🔧 Dépannage

### Erreur "Firebase not initialized"
- Vérifier que toutes les variables d'environnement sont définies
- Redémarrer le serveur React après modification du .env

### Erreur "Permission denied"
- Vérifier les règles de sécurité Firestore
- S'assurer que l'authentification est activée

### Erreur "Invalid API key"
- Vérifier que l'API key est correcte dans la configuration
- S'assurer que l'API key n'a pas été régénérée

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifier la console Firebase pour les erreurs
2. Consulter les logs du navigateur (F12)
3. Vérifier les logs du serveur Django
4. Utiliser la page de test Firebase intégrée

## 🎯 Prochaines étapes

Une fois Firebase configuré :
1. Implémenter l'authentification Firebase complète
2. Migrer les données utilisateur vers Firestore
3. Développer les modules de reconnaissance faciale
4. Configurer le stockage des images
5. Optimiser les règles de sécurité pour la production
