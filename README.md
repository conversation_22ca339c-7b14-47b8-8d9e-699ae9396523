# PresencePro - Système de Gestion de Présence avec Reconnaissance Faciale

## Description
PresencePro est un système complet de gestion de présence utilisant la reconnaissance faciale pour les établissements d'enseignement. Le système propose trois types d'utilisateurs avec des dashboards distincts et une authentification sécurisée.

## Architecture

### Technologies utilisées
- **Backend** : Django + Python avec Supabase SDK
- **Frontend** : React.js + TypeScript + Material-UI
- **Base de données** : PostgreSQL via Supabase + Supabase Storage
- **Authentification** : Supabase Authentication avec système de rôles
- **Reconnaissance faciale** : face-recognition (Python) + TensorFlow
- **Caméra** : MediaDevices.getUserMedia() API

### Types d'utilisateurs
1. **Administrateur** : Gestion complète des utilisateurs, cours et rapports
2. **Enseignant** : Gestion des présences et consultation des rapports
3. **Étudiant** : Consultation de l'emploi du temps et historique des présences

## Structure du projet

```
PresencePro/
├── backend/                    # API Django
│   ├── presencepro/           # Configuration principale
│   ├── apps/                  # Applications Django
│   │   ├── authentication/    # Authentification et rôles
│   │   ├── users/            # Gestion des utilisateurs
│   │   ├── courses/          # Gestion des cours
│   │   ├── attendance/       # Gestion des présences
│   │   └── face_recognition/ # Reconnaissance faciale
│   └── requirements.txt      # Dépendances Python
├── frontend/                  # Application React
│   ├── src/
│   │   ├── components/       # Composants réutilisables
│   │   ├── pages/           # Pages principales
│   │   ├── contexts/        # Gestion d'état
│   │   ├── services/        # Services API
│   │   └── utils/           # Utilitaires
│   └── package.json         # Dépendances Node.js
└── docker-compose.yml       # Orchestration des services
```

## Fonctionnalités principales

### Reconnaissance faciale
- Capture vidéo en temps réel via webcam
- Détection et reconnaissance automatique des visages
- Enregistrement automatique avec horodatage
- Système de tolérance pour variations d'éclairage

### Dashboard Administrateur
- Gestion complète des utilisateurs (CRUD)
- Gestion des étudiants avec photos de profil
- Configuration des cours et planning
- Rapports et statistiques globales

### Dashboard Enseignant
- Vue d'ensemble des cours assignés
- Interface de prise de présence manuelle
- Consultation et modification des présences
- Génération de rapports détaillés

### Dashboard Étudiant
- Consultation de l'emploi du temps
- Historique des présences avec pourcentages
- Calendrier des cours avec statut
- Notifications pour absences

## Installation et démarrage

### Prérequis
- Python 3.9+
- Node.js 16+
- Docker (optionnel)
- Compte Supabase avec projet configuré

### Configuration Supabase
1. Créer un projet Supabase
2. Configurer PostgreSQL et Storage
3. Activer Authentication
4. Récupérer les clés API

### Démarrage rapide

#### Backend Django
```bash
cd backend
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

#### Frontend React
```bash
cd frontend
npm install
npm start
```

#### Avec Docker
```bash
docker-compose up --build
```

## Configuration

### Variables d'environnement

#### Backend (.env)
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key
DJANGO_SECRET_KEY=your-django-secret-key
DEBUG=True
```

#### Frontend (.env)
```bash
REACT_APP_SUPABASE_URL=https://your-project.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your-anon-key
```

### Configuration Supabase
1. Créer un projet sur [supabase.com](https://supabase.com)
2. Récupérer l'URL et les clés API dans Settings → API
3. Exécuter le script SQL d'initialisation : `docs/supabase_schema.sql`
4. Configurer les variables d'environnement

## Utilisation

### Première connexion
1. Créer un compte administrateur via l'interface Django Admin
2. Se connecter avec les identifiants administrateur
3. Créer les enseignants et étudiants via le dashboard admin
4. Configurer les cours et groupes
5. Commencer la prise de présence

### Prise de présence
1. L'enseignant accède à son dashboard
2. Sélectionne le cours en cours
3. Active la reconnaissance faciale
4. Les étudiants se présentent devant la caméra
5. Le système enregistre automatiquement les présences

## Développement

### Structure des API
- `/api/auth/` - Authentification
- `/api/users/` - Gestion des utilisateurs
- `/api/courses/` - Gestion des cours
- `/api/attendance/` - Gestion des présences
- `/api/face-recognition/` - Reconnaissance faciale

### Tests
```bash
# Backend
cd backend
python manage.py test

# Frontend
cd frontend
npm test
```

## Déploiement

### Production
1. Configurer les variables d'environnement de production
2. Construire l'application React
3. Déployer Django avec un serveur WSGI (Gunicorn)
4. Configurer un reverse proxy (Nginx)
5. Activer HTTPS

## Support et contribution

Pour toute question ou contribution, veuillez consulter la documentation technique dans le dossier `docs/` ou créer une issue sur le repository.

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.
