version: '3.8'

services:
  # Base de données PostgreSQL
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: presencepro
      POSTGRES_USER: presencepro_user
      POSTGRES_PASSWORD: presencepro_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - presencepro_network

  # Backend Django
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ./backend:/app
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=**********************************************************/presencepro
    depends_on:
      - db
    networks:
      - presencepro_network

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    command: npm start
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend
    networks:
      - presencepro_network

  # Redis pour le cache (optionnel)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - presencepro_network

volumes:
  postgres_data:
  media_volume:

networks:
  presencepro_network:
    driver: bridge
