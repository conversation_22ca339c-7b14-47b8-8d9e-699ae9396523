/**
 * Script de test pour vérifier la connexion Supabase
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://avndwxjnowyeolrljchj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2bmR3eGpub3d5ZW9scmxqY2hqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1ODE4MTAsImV4cCI6MjA2NTE1NzgxMH0.esbQXrtZhjHJhfr2jXO8NCt9OBwnlK3MocOPkRpEwLs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  console.log('🧪 Test de connexion Supabase...');
  console.log('URL:', supabaseUrl);
  console.log('Key:', supabaseAnonKey.substring(0, 20) + '...');

  try {
    // Test 1: Vérifier la connexion de base
    console.log('\n1️⃣ Test de connexion de base...');
    const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true });
    
    if (error) {
      console.error('❌ Erreur de connexion:', error);
      return;
    }
    
    console.log('✅ Connexion réussie! Nombre d\'utilisateurs:', data);

    // Test 2: Tester l'authentification
    console.log('\n2️⃣ Test d\'authentification...');
    const { data: session } = await supabase.auth.getSession();
    console.log('Session actuelle:', session.session ? 'Connecté' : 'Non connecté');

    // Test 3: Tester la connexion avec un utilisateur existant
    console.log('\n3️⃣ Test de <NAME_EMAIL>...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (authError) {
      console.error('❌ Erreur de connexion:', authError.message);
    } else {
      console.log('✅ Connexion réussie pour:', authData.user?.email);
      
      // Test 4: Récupérer le profil utilisateur
      console.log('\n4️⃣ Test de récupération du profil...');
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('email', '<EMAIL>')
        .single();

      if (userError) {
        console.error('❌ Erreur de récupération du profil:', userError.message);
      } else {
        console.log('✅ Profil récupéré:', userData);
      }

      // Déconnexion
      await supabase.auth.signOut();
      console.log('👋 Déconnexion effectuée');
    }

  } catch (error) {
    console.error('❌ Erreur générale:', error);
  }
}

testConnection();
