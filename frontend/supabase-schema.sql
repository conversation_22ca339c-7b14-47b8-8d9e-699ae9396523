-- Schema SQL pour PresencePro avec Supabase
-- Exécutez ces commandes dans l'éditeur SQL de Supabase

-- =====================================================
-- 1. TABLE DES UTILISATEURS
-- =====================================================

CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(150) UNIQUE NOT NULL,
  email VARCHAR(254) UNIQUE NOT NULL,
  first_name VARCHAR(150) NOT NULL,
  last_name VARCHAR(150) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'student' CHECK (role IN ('admin', 'teacher', 'student')),
  phone_number VARCHAR(20),
  date_of_birth DATE,
  address TEXT,
  profile_picture TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. TABLE DES GROUPES D'ÉTUDIANTS
-- =====================================================

CREATE TABLE IF NOT EXISTS student_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  academic_year VARCHAR(20) NOT NULL,
  level VARCHAR(50) NOT NULL,
  specialization VARCHAR(100),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. TABLE DES COURS
-- =====================================================

CREATE TABLE IF NOT EXISTS courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL,
  description TEXT,
  credits INTEGER DEFAULT 3,
  semester VARCHAR(20) NOT NULL,
  academic_year VARCHAR(20) NOT NULL,
  teacher_id UUID REFERENCES users(id) ON DELETE SET NULL,
  student_group_id UUID REFERENCES student_groups(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. TABLE DES SESSIONS DE COURS
-- =====================================================

CREATE TABLE IF NOT EXISTS course_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  room VARCHAR(50),
  building VARCHAR(100),
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. TABLE DES PRÉSENCES
-- =====================================================

CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  session_id UUID REFERENCES course_sessions(id) ON DELETE SET NULL,
  date DATE NOT NULL,
  time TIMESTAMP WITH TIME ZONE NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'excused')),
  method VARCHAR(30) NOT NULL DEFAULT 'manual' CHECK (method IN ('manual', 'face_recognition', 'qr_code')),
  confidence DECIMAL(3,2), -- Pour la reconnaissance faciale (0.00 à 1.00)
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Index unique pour éviter les doublons
  UNIQUE(student_id, course_id, date)
);

-- =====================================================
-- 6. TABLE DES ENCODAGES FACIAUX
-- =====================================================

CREATE TABLE IF NOT EXISTS face_encodings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  encoding DECIMAL[] NOT NULL, -- Array de nombres pour l'encodage facial
  confidence DECIMAL(3,2) NOT NULL DEFAULT 0.9,
  image_url TEXT, -- URL de l'image source
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. TABLE DES INSCRIPTIONS AUX COURS
-- =====================================================

CREATE TABLE IF NOT EXISTS course_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  enrollment_date DATE DEFAULT CURRENT_DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Un étudiant ne peut être inscrit qu'une fois par cours
  UNIQUE(student_id, course_id)
);

-- =====================================================
-- 8. TABLE DES NOTIFICATIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  is_read BOOLEAN DEFAULT false,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 9. INDEXES POUR LES PERFORMANCES
-- =====================================================

-- Index pour les recherches fréquentes
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_courses_teacher ON courses(teacher_id);
CREATE INDEX IF NOT EXISTS idx_courses_group ON courses(student_group_id);
CREATE INDEX IF NOT EXISTS idx_attendance_student ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_course ON attendance(course_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
CREATE INDEX IF NOT EXISTS idx_face_encodings_user ON face_encodings(user_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_student ON course_enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course ON course_enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id);

-- =====================================================
-- 10. FONCTIONS ET TRIGGERS
-- =====================================================

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_attendance_updated_at BEFORE UPDATE ON attendance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_face_encodings_updated_at BEFORE UPDATE ON face_encodings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 11. POLITIQUES RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Activer RLS sur toutes les tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE face_encodings ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Politiques pour les utilisateurs (les utilisateurs peuvent voir leurs propres données)
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid()::text = id::text);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid()::text = id::text);

-- Politiques pour les encodages faciaux
CREATE POLICY "Users can manage own face encodings" ON face_encodings FOR ALL USING (auth.uid()::text = user_id::text);

-- Politiques pour les présences (les étudiants voient leurs présences, les profs voient celles de leurs cours)
CREATE POLICY "Students can view own attendance" ON attendance FOR SELECT USING (auth.uid()::text = student_id::text);
CREATE POLICY "Teachers can view course attendance" ON attendance FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM courses 
    WHERE courses.id = attendance.course_id 
    AND courses.teacher_id::text = auth.uid()::text
  )
);

-- Politiques pour les cours
CREATE POLICY "Everyone can view active courses" ON courses FOR SELECT USING (is_active = true);
CREATE POLICY "Teachers can manage own courses" ON courses FOR ALL USING (auth.uid()::text = teacher_id::text);

-- =====================================================
-- 12. DONNÉES DE TEST
-- =====================================================

-- Insérer un utilisateur admin de test
INSERT INTO users (id, username, email, first_name, last_name, role) 
VALUES (
  gen_random_uuid(),
  'admin',
  '<EMAIL>',
  'Admin',
  'PresencePro',
  'admin'
) ON CONFLICT (email) DO NOTHING;

-- Insérer un professeur de test
INSERT INTO users (id, username, email, first_name, last_name, role) 
VALUES (
  gen_random_uuid(),
  'prof_martin',
  '<EMAIL>',
  'Jean',
  'Martin',
  'teacher'
) ON CONFLICT (email) DO NOTHING;

-- Insérer des étudiants de test
INSERT INTO users (id, username, email, first_name, last_name, role) 
VALUES 
  (gen_random_uuid(), 'etudiant1', '<EMAIL>', 'Alice', 'Dupont', 'student'),
  (gen_random_uuid(), 'etudiant2', '<EMAIL>', 'Bob', 'Martin', 'student'),
  (gen_random_uuid(), 'etudiant3', '<EMAIL>', 'Claire', 'Bernard', 'student')
ON CONFLICT (email) DO NOTHING;

-- Insérer un groupe d'étudiants
INSERT INTO student_groups (id, name, academic_year, level) 
VALUES (
  gen_random_uuid(),
  'L3 Informatique',
  '2024-2025',
  'Licence 3'
) ON CONFLICT DO NOTHING;

-- Insérer un cours de test
INSERT INTO courses (id, name, code, description, teacher_id, student_group_id) 
VALUES (
  gen_random_uuid(),
  'Programmation Web',
  'INFO301',
  'Cours de développement web avec React et Node.js',
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  (SELECT id FROM student_groups WHERE name = 'L3 Informatique' LIMIT 1)
) ON CONFLICT (code) DO NOTHING;
