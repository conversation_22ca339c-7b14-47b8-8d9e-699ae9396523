const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://avndwxjnowyeolrljchj.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2bmR3eGpub3d5ZW9scmxqY2hqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1ODE4MTAsImV4cCI6MjA2NTE1NzgxMH0.esbQXrtZhjHJhfr2jXO8NCt9OBwnlK3MocOPkRpEwLs'
);

async function createTestUser() {
  try {
    console.log('Création d\'un utilisateur de test...');
    
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'test123456',
    });

    if (error) {
      console.error('Erreur:', error.message);
      return;
    }

    console.log('✅ Utilisateur créé avec succès !');
    console.log('📧 Email:', '<EMAIL>');
    console.log('🔑 Mot de passe:', 'test123456');
    console.log('🆔 ID utilisateur:', data.user?.id);
    
    if (data.user && !data.user.email_confirmed_at) {
      console.log('⚠️  Email non confirmé - vérifiez votre boîte mail ou désactivez la confirmation dans Supabase');
    }

  } catch (err) {
    console.error('Erreur:', err.message);
  }
}

createTestUser();
