-- Script pour corriger les politiques RLS de PresencePro
-- Exécutez ce script dans l'éditeur SQL de Supabase

-- 1. Supprimer toutes les politiques existantes pour éviter les conflits
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all users" ON users;
DROP POLICY IF EXISTS "Public read access" ON users;
DROP POLICY IF EXISTS "Authenticated users can read" ON users;
DROP POLICY IF EXISTS "Users can insert own profile" ON users;

-- 2. Désactiver temporairement RLS pour permettre l'insertion initiale
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE courses DISABLE ROW LEVEL SECURITY;
ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;
ALTER TABLE face_encodings DISABLE ROW LEVEL SECURITY;

-- 3. Créer des politiques simples et sécurisées

-- Politiques pour la table users
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Permettre aux utilisateurs authentifiés de lire tous les profils (nécessaire pour l'app)
CREATE POLICY "Authenticated users can read all profiles" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Permettre aux utilisateurs de créer leur propre profil
CREATE POLICY "Users can create own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Permettre aux utilisateurs de mettre à jour leur propre profil
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Permettre aux admins de tout gérer (basé sur le rôle dans la table)
CREATE POLICY "Admins can manage all users" ON users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Politiques pour la table courses
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;

-- Lecture pour tous les utilisateurs authentifiés
CREATE POLICY "Authenticated users can read courses" ON courses
    FOR SELECT USING (auth.role() = 'authenticated');

-- Les professeurs peuvent gérer leurs cours
CREATE POLICY "Teachers can manage own courses" ON courses
    FOR ALL USING (
        teacher_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'teacher')
        )
    );

-- Politiques pour la table attendance
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- Lecture pour tous les utilisateurs authentifiés
CREATE POLICY "Authenticated users can read attendance" ON attendance
    FOR SELECT USING (auth.role() = 'authenticated');

-- Les étudiants peuvent voir leurs propres présences
CREATE POLICY "Students can view own attendance" ON attendance
    FOR SELECT USING (student_id = auth.uid());

-- Les professeurs et admins peuvent gérer les présences
CREATE POLICY "Teachers and admins can manage attendance" ON attendance
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'teacher')
        )
    );

-- Politiques pour la table face_encodings
ALTER TABLE face_encodings ENABLE ROW LEVEL SECURITY;

-- Lecture pour tous les utilisateurs authentifiés
CREATE POLICY "Authenticated users can read face encodings" ON face_encodings
    FOR SELECT USING (auth.role() = 'authenticated');

-- Les utilisateurs peuvent gérer leurs propres encodages
CREATE POLICY "Users can manage own face encodings" ON face_encodings
    FOR ALL USING (user_id = auth.uid());

-- Les admins peuvent gérer tous les encodages
CREATE POLICY "Admins can manage all face encodings" ON face_encodings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- 4. Créer une fonction pour vérifier les rôles (optionnel, pour plus de sécurité)
CREATE OR REPLACE FUNCTION auth.user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM users 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Afficher un message de confirmation
DO $$
BEGIN
    RAISE NOTICE 'Politiques RLS mises à jour avec succès !';
    RAISE NOTICE 'Vous pouvez maintenant exécuter le script de synchronisation des utilisateurs.';
END $$;
