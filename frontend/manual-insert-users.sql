-- Script pour insérer manuellement les utilisateurs dans la table users
-- Utilisez les IDs obtenus lors de la création des comptes d'authentification

-- Désactiver RLS temporairement
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Insérer les utilisateurs avec les IDs corrects
-- (Remplacez les IDs par ceux obtenus lors de l'exécution du script sync-users)

INSERT INTO users (id, username, email, first_name, last_name, role, is_active, created_at, updated_at) VALUES
('a83e15bc-8ec8-4f79-bdd9-dcd53a8855a4', 'admin', '<EMAIL>', 'Admin', 'PresencePro', 'admin', true, NOW(), NOW()),
('3403a727-a265-407d-ad0b-839353c699c9', 'prof_martin', '<EMAIL>', '<PERSON>', '<PERSON>', 'teacher', true, NOW(), NOW()),
('39ff9acd-900e-4649-8419-0a415f88b380', 'etudiant1', '<EMAIL>', 'Alice', 'Du<PERSON>', 'student', true, NOW(), NOW()),
('b6a9ef04-d0ad-4c4d-bba9-6195cc484dd9', 'etudiant2', '<EMAIL>', '<PERSON>', 'Martin', 'student', true, NOW(), NOW()),
('3b173552-e63a-4b01-9df7-79e4c77e2f41', 'etudiant3', '<EMAIL>', 'Claire', 'Bernard', 'student', true, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    role = EXCLUDED.role,
    updated_at = NOW();

-- Vérifier les insertions
SELECT id, username, email, first_name, last_name, role FROM users;

-- Message de confirmation
SELECT 'Utilisateurs insérés avec succès !' as message;
