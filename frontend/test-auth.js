/**
 * Script de test pour vérifier l'authentification après correction
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration Supabase
const SUPABASE_URL = 'https://avndwxjnowyeolrljchj.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.esbQXrtZhjHJhfr2jXO8NCt9OBwnlK3MocOPkRpEwLs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Test complet de l'authentification
 */
async function testAuthentication() {
  console.log('🧪 Test complet de l\'authentification PresencePro\n');

  const testAccounts = [
    { email: '<EMAIL>', password: 'admin123', expectedRole: 'admin' },
    { email: '<EMAIL>', password: 'teacher123', expectedRole: 'teacher' },
    { email: '<EMAIL>', password: 'student123', expectedRole: 'student' }
  ];

  for (const account of testAccounts) {
    console.log(`🔐 Test de connexion: ${account.email}`);
    
    try {
      // 1. Authentification Supabase
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: account.email,
        password: account.password
      });

      if (authError) {
        console.log(`   ❌ Échec authentification: ${authError.message}`);
        continue;
      }

      console.log(`   ✅ Authentification réussie`);
      console.log(`   🆔 User ID: ${authData.user?.id}`);

      // 2. Récupération du profil
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('email', account.email)
        .single();

      if (profileError) {
        console.log(`   ❌ Échec récupération profil: ${profileError.message}`);
      } else {
        console.log(`   ✅ Profil récupéré: ${profile.first_name} ${profile.last_name}`);
        console.log(`   👤 Rôle: ${profile.role} (attendu: ${account.expectedRole})`);
        
        if (profile.role === account.expectedRole) {
          console.log(`   🎯 Rôle correct !`);
        } else {
          console.log(`   ⚠️  Rôle incorrect`);
        }
      }

      // 3. Se déconnecter
      await supabase.auth.signOut();
      console.log(`   🚪 Déconnexion réussie\n`);

    } catch (error) {
      console.log(`   ❌ Erreur: ${error.message}\n`);
    }
  }
}

/**
 * Vérifier l'état des tables
 */
async function checkTables() {
  console.log('📋 Vérification des tables...\n');

  const tables = [
    { name: 'users', description: 'Profils utilisateurs' },
    { name: 'courses', description: 'Cours' },
    { name: 'attendance', description: 'Présences' },
    { name: 'face_encodings', description: 'Encodages faciaux' }
  ];

  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table.name)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.log(`❌ ${table.name}: ${error.message}`);
      } else {
        console.log(`✅ ${table.name}: ${count || 0} enregistrements (${table.description})`);
      }
    } catch (error) {
      console.log(`❌ ${table.name}: ${error.message}`);
    }
  }
}

/**
 * Fonction principale
 */
async function runTests() {
  console.log('🚀 Tests PresencePro - Diagnostic Complet\n');
  console.log('=' * 50);

  await checkTables();
  console.log('\n' + '=' * 50);
  await testAuthentication();

  console.log('🏁 Tests terminés !');
  console.log('\n💡 Si tous les tests passent, l\'application devrait fonctionner.');
  console.log('   Essayez de vous connecter sur http://localhost:3001');
}

// Exécuter les tests
if (require.main === module) {
  runTests().then(() => process.exit(0));
}

module.exports = { runTests };
