/**
 * Script pour configurer les données de démonstration dans Supabase
 * Exécutez ce script après avoir créé les tables avec le schema SQL
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration Supabase
const SUPABASE_URL = 'https://avndwxjnowyeolrljchj.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2bmR3eGpub3d5ZW9scmxqY2hqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1ODE4MTAsImV4cCI6MjA2NTE1NzgxMH0.esbQXrtZhjHJhfr2jXO8NCt9OBwnlK3MocOPkRpEwLs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Crée les utilisateurs de démonstration
 */
async function createDemoUsers() {
  console.log('🚀 Création des utilisateurs de démonstration...\n');

  const demoUsers = [
    {
      email: '<EMAIL>',
      password: 'admin123',
      userData: {
        username: 'admin',
        first_name: 'Admin',
        last_name: '<PERSON>sen<PERSON><PERSON><PERSON>',
        role: 'admin'
      }
    },
    {
      email: '<EMAIL>',
      password: 'teacher123',
      userData: {
        username: 'prof_martin',
        first_name: 'Jean',
        last_name: 'Martin',
        role: 'teacher'
      }
    },
    {
      email: '<EMAIL>',
      password: 'student123',
      userData: {
        username: 'etudiant1',
        first_name: 'Alice',
        last_name: 'Dupont',
        role: 'student'
      }
    },
    {
      email: '<EMAIL>',
      password: 'student123',
      userData: {
        username: 'etudiant2',
        first_name: 'Bob',
        last_name: 'Martin',
        role: 'student'
      }
    },
    {
      email: '<EMAIL>',
      password: 'student123',
      userData: {
        username: 'etudiant3',
        first_name: 'Claire',
        last_name: 'Bernard',
        role: 'student'
      }
    }
  ];

  for (const user of demoUsers) {
    try {
      console.log(`📧 Création de l'utilisateur: ${user.email}`);
      
      // 1. Créer l'utilisateur dans Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: user.email,
        password: user.password
      });

      if (authError) {
        if (authError.message.includes('already registered')) {
          console.log(`   ⚠️  Utilisateur déjà existant: ${user.email}`);
          continue;
        } else {
          throw authError;
        }
      }

      if (authData.user) {
        // 2. Créer le profil utilisateur dans notre table
        const { error: profileError } = await supabase
          .from('users')
          .insert([{
            id: authData.user.id,
            ...user.userData,
            email: user.email,
            is_active: true
          }]);

        if (profileError) {
          console.error(`   ❌ Erreur création profil: ${profileError.message}`);
        } else {
          console.log(`   ✅ Utilisateur créé avec succès: ${user.userData.first_name} ${user.userData.last_name}`);
        }
      }

    } catch (error) {
      console.error(`   ❌ Erreur pour ${user.email}:`, error.message);
    }
  }
}

/**
 * Crée les données de cours de démonstration
 */
async function createDemoCourses() {
  console.log('\n📚 Création des cours de démonstration...\n');

  // Récupérer l'ID du professeur
  const { data: teacher } = await supabase
    .from('users')
    .select('id')
    .eq('email', '<EMAIL>')
    .single();

  if (!teacher) {
    console.log('❌ Professeur non trouvé, impossible de créer les cours');
    return;
  }

  const demoCourses = [
    {
      name: 'Programmation Web',
      code: 'INFO301',
      description: 'Cours de développement web avec React et Node.js',
      teacher_id: teacher.id,
      semester: 'Automne 2024',
      academic_year: '2024-2025',
      credits: 3,
      is_active: true
    },
    {
      name: 'Base de Données',
      code: 'INFO302',
      description: 'Conception et gestion de bases de données relationnelles',
      teacher_id: teacher.id,
      semester: 'Automne 2024',
      academic_year: '2024-2025',
      credits: 4,
      is_active: true
    },
    {
      name: 'Intelligence Artificielle',
      code: 'INFO401',
      description: 'Introduction aux concepts et techniques d\'IA',
      teacher_id: teacher.id,
      semester: 'Automne 2024',
      academic_year: '2024-2025',
      credits: 3,
      is_active: true
    }
  ];

  for (const course of demoCourses) {
    try {
      const { error } = await supabase
        .from('courses')
        .insert([course]);

      if (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`   ⚠️  Cours déjà existant: ${course.name}`);
        } else {
          throw error;
        }
      } else {
        console.log(`   ✅ Cours créé: ${course.name} (${course.code})`);
      }
    } catch (error) {
      console.error(`   ❌ Erreur pour ${course.name}:`, error.message);
    }
  }
}

/**
 * Crée des données de présence de démonstration
 */
async function createDemoAttendance() {
  console.log('\n📊 Création des données de présence de démonstration...\n');

  // Récupérer les étudiants et cours
  const { data: students } = await supabase
    .from('users')
    .select('id')
    .eq('role', 'student');

  const { data: courses } = await supabase
    .from('courses')
    .select('id, name');

  if (!students || !courses || students.length === 0 || courses.length === 0) {
    console.log('❌ Pas d\'étudiants ou de cours trouvés');
    return;
  }

  // Créer des présences pour les 7 derniers jours
  const today = new Date();
  const attendanceData = [];

  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];

    for (const course of courses) {
      for (const student of students) {
        // Simuler différents statuts de présence
        const random = Math.random();
        let status;
        if (random < 0.7) status = 'present';
        else if (random < 0.85) status = 'late';
        else status = 'absent';

        attendanceData.push({
          student_id: student.id,
          course_id: course.id,
          date: dateStr,
          time: new Date(date.getTime() + Math.random() * 3600000).toISOString(), // Heure aléatoire
          status: status,
          method: Math.random() < 0.8 ? 'face_recognition' : 'manual',
          confidence: status !== 'absent' ? 0.85 + Math.random() * 0.15 : null,
          notes: status === 'late' ? 'Arrivé en retard' : null
        });
      }
    }
  }

  try {
    const { error } = await supabase
      .from('attendance')
      .insert(attendanceData);

    if (error) {
      console.error('❌ Erreur création présences:', error.message);
    } else {
      console.log(`✅ ${attendanceData.length} enregistrements de présence créés`);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

/**
 * Fonction principale
 */
async function setupDemoData() {
  console.log('🎯 Configuration des données de démonstration pour PresencePro\n');
  console.log('📋 Assurez-vous d\'avoir exécuté le schema SQL dans Supabase avant de continuer.\n');

  try {
    await createDemoUsers();
    await createDemoCourses();
    await createDemoAttendance();

    console.log('\n🎉 Configuration terminée avec succès !');
    console.log('\n📝 Comptes de test disponibles:');
    console.log('   👑 Admin: <EMAIL> / admin123');
    console.log('   👨‍🏫 Professeur: <EMAIL> / teacher123');
    console.log('   👩‍🎓 Étudiant: <EMAIL> / student123');
    console.log('   👨‍🎓 Étudiant: <EMAIL> / student123');
    console.log('   👩‍🎓 Étudiant: <EMAIL> / student123');

  } catch (error) {
    console.error('❌ Erreur lors de la configuration:', error);
  }
}

// Exécuter le script
if (require.main === module) {
  setupDemoData().then(() => process.exit(0));
}

module.exports = { setupDemoData };
