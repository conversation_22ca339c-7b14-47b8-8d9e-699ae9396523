/**
 * Script pour synchroniser les utilisateurs Supabase Auth avec la table users
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration Supabase
const SUPABASE_URL = 'https://avndwxjnowyeolrljchj.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2bmR3eGpub3d5ZW9scmxqY2hqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1ODE4MTAsImV4cCI6MjA2NTE1NzgxMH0.esbQXrtZhjHJhfr2jXO8NCt9OBwnlK3MocOPkRpEwLs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Mapping des emails vers les données utilisateur
 */
const userProfiles = {
  '<EMAIL>': {
    username: 'admin',
    first_name: 'Admin',
    last_name: '<PERSON>sen<PERSON><PERSON><PERSON>',
    role: 'admin'
  },
  '<EMAIL>': {
    username: 'prof_martin',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    role: 'teacher'
  },
  '<EMAIL>': {
    username: 'etudiant1',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    role: 'student'
  },
  '<EMAIL>': {
    username: 'etudiant2',
    first_name: 'Bob',
    last_name: 'Martin',
    role: 'student'
  },
  '<EMAIL>': {
    username: 'etudiant3',
    first_name: 'Claire',
    last_name: 'Bernard',
    role: 'student'
  }
};

/**
 * Crée les profils utilisateurs dans la table users
 */
async function createUserProfiles() {
  console.log('👥 Création des profils utilisateurs...\n');

  for (const [email, profile] of Object.entries(userProfiles)) {
    try {
      console.log(`📝 Création du profil pour ${email}...`);

      // D'abord, essayer de se connecter pour obtenir l'ID utilisateur
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: email,
        password: getPasswordForEmail(email)
      });

      if (authError) {
        console.log(`   ❌ Erreur d'authentification: ${authError.message}`);
        continue;
      }

      if (!authData.user) {
        console.log(`   ❌ Utilisateur non trouvé dans auth`);
        continue;
      }

      const userId = authData.user.id;
      console.log(`   🔑 ID utilisateur: ${userId}`);

      // Vérifier si le profil existe déjà
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('id')
        .eq('id', userId)
        .single();

      if (existingUser) {
        console.log(`   ⚠️  Profil déjà existant`);
        await supabase.auth.signOut();
        continue;
      }

      // Créer le profil utilisateur
      const { error: insertError } = await supabase
        .from('users')
        .insert([{
          id: userId,
          username: profile.username,
          email: email,
          first_name: profile.first_name,
          last_name: profile.last_name,
          role: profile.role,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);

      if (insertError) {
        console.log(`   ❌ Erreur création profil: ${insertError.message}`);
      } else {
        console.log(`   ✅ Profil créé avec succès`);
      }

      // Se déconnecter
      await supabase.auth.signOut();

    } catch (error) {
      console.log(`   ❌ Erreur: ${error.message}`);
    }
  }
}

/**
 * Obtient le mot de passe pour un email donné
 */
function getPasswordForEmail(email) {
  if (email.includes('admin')) return 'admin123';
  if (email.includes('martin') || email.includes('teacher')) return 'teacher123';
  return 'student123';
}

/**
 * Vérifie les profils créés
 */
async function verifyProfiles() {
  console.log('\n🔍 Vérification des profils créés...\n');

  for (const email of Object.keys(userProfiles)) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, email, first_name, last_name, role')
        .eq('email', email)
        .single();

      if (error) {
        console.log(`❌ ${email}: Non trouvé`);
      } else {
        console.log(`✅ ${email}: ${data.first_name} ${data.last_name} (${data.role})`);
      }
    } catch (error) {
      console.log(`❌ ${email}: Erreur - ${error.message}`);
    }
  }
}

/**
 * Test de connexion avec l'application
 */
async function testAppLogin() {
  console.log('\n🔐 Test de connexion application...\n');

  try {
    // Tester avec le compte admin
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (error) {
      console.log('❌ Échec de l\'authentification:', error.message);
      return;
    }

    console.log('✅ Authentification réussie');

    // Tester la récupération du profil
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (profileError) {
      console.log('❌ Échec récupération profil:', profileError.message);
    } else {
      console.log('✅ Profil récupéré:', profile.first_name, profile.last_name, `(${profile.role})`);
    }

    // Se déconnecter
    await supabase.auth.signOut();
    console.log('✅ Déconnexion réussie');

  } catch (error) {
    console.log('❌ Erreur test:', error.message);
  }
}

/**
 * Fonction principale
 */
async function syncUsers() {
  console.log('🔄 Synchronisation des utilisateurs PresencePro\n');

  try {
    await createUserProfiles();
    await verifyProfiles();
    await testAppLogin();

    console.log('\n🎉 Synchronisation terminée avec succès !');
    console.log('\n💡 Vous pouvez maintenant vous connecter à l\'application avec :');
    console.log('   👑 Admin: <EMAIL> / admin123');
    console.log('   👨‍🏫 Professeur: <EMAIL> / teacher123');
    console.log('   👩‍🎓 Étudiant: <EMAIL> / student123');

  } catch (error) {
    console.error('❌ Erreur lors de la synchronisation:', error);
  }
}

// Exécuter le script
if (require.main === module) {
  syncUsers().then(() => process.exit(0));
}

module.exports = { syncUsers };
