# Configuration de la Base de Données PresencePro

Ce guide vous explique comment configurer la base de données Supabase pour PresencePro avec le système de reconnaissance faciale et d'authentification basée sur les rôles.

## 🚀 Étapes de Configuration

### 1. **Créer les Tables**

1. Connectez-vous à votre dashboard Supabase : https://app.supabase.com
2. Sélectionnez votre projet PresencePro
3. Allez dans **SQL Editor**
4. Copiez et exécutez le contenu du fichier `supabase-schema.sql`

### 2. **Configurer l'Authentification**

1. Allez dans **Authentication** > **Settings**
2. Activez **Enable email confirmations** (optionnel pour le développement)
3. Dans **Auth Providers**, assurez-vous que **Email** est activé

### 3. **Configurer le Stockage**

1. Allez dans **Storage**
2. Créez un nouveau bucket appelé `images`
3. Configurez les politiques de sécurité :

```sql
-- Politique pour permettre l'upload d'images
CREATE POLICY "Allow authenticated users to upload images" ON storage.objects
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Politique pour permettre la lecture des images
CREATE POLICY "Allow public read access to images" ON storage.objects
FOR SELECT USING (bucket_id = 'images');

-- Politique pour permettre la mise à jour des images
CREATE POLICY "Allow users to update own images" ON storage.objects
FOR UPDATE USING (auth.uid()::text = (storage.foldername(name))[1]);
```

### 4. **Créer les Données de Démonstration**

Exécutez le script de configuration depuis le dossier frontend :

```bash
cd frontend
npm run setup-demo
```

Ce script va créer :
- ✅ 5 utilisateurs de test (1 admin, 1 professeur, 3 étudiants)
- ✅ 3 cours de démonstration
- ✅ Données de présence pour les 7 derniers jours

## 👥 Comptes de Test Créés

| Rôle | Email | Mot de passe | Description |
|------|-------|--------------|-------------|
| **Admin** | <EMAIL> | admin123 | Accès complet au système |
| **Professeur** | <EMAIL> | teacher123 | Gestion des cours et présences |
| **Étudiant** | <EMAIL> | student123 | Alice Dupont |
| **Étudiant** | <EMAIL> | student123 | Bob Martin |
| **Étudiant** | <EMAIL> | student123 | Claire Bernard |

## 🔧 Configuration des Variables d'Environnement

Assurez-vous que votre fichier `.env` contient :

```env
REACT_APP_SUPABASE_URL=https://avndwxjnowyeolrljchj.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2bmR3eGpub3d5ZW9scmxqY2hqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1ODE4MTAsImV4cCI6MjA2NTE1NzgxMH0.esbQXrtZhjHJhfr2jXO8NCt9OBwnlK3MocOPkRpEwLs
```

## 🎯 Test du Système

### 1. **Test de l'Authentification**

1. Démarrez l'application : `npm start`
2. Allez sur http://localhost:3001
3. Vous devriez voir la page de connexion
4. Testez les comptes de démonstration

### 2. **Test des Dashboards par Rôle**

- **Admin** → Redirigé vers `/admin-dashboard`
  - Gestion des utilisateurs
  - Gestion des photos étudiants
  - Reconnaissance faciale live
  
- **Professeur** → Redirigé vers `/teacher-dashboard`
  - Vue des cours assignés
  - Sessions de présence
  - Historique des présences
  
- **Étudiant** → Redirigé vers `/student-dashboard`
  - Historique personnel des présences
  - Enregistrement de photos faciales

### 3. **Test de la Reconnaissance Faciale**

1. Connectez-vous en tant qu'admin
2. Allez dans l'onglet "Photos Étudiants"
3. Sélectionnez un étudiant et ajoutez ses photos
4. Testez la reconnaissance live dans l'onglet correspondant

## 🔍 Vérification des Tables

Après l'exécution du script, vérifiez que ces tables contiennent des données :

```sql
-- Vérifier les utilisateurs
SELECT id, username, email, first_name, last_name, role FROM users;

-- Vérifier les cours
SELECT id, name, code, teacher_id FROM courses;

-- Vérifier les présences
SELECT COUNT(*) as total_attendance FROM attendance;

-- Vérifier les encodages faciaux (vide au début)
SELECT COUNT(*) as total_encodings FROM face_encodings;
```

## 🚨 Dépannage

### Erreur "User already registered"
- Normal si vous exécutez le script plusieurs fois
- Les utilisateurs existants sont ignorés

### Erreur de permissions
- Vérifiez que les politiques RLS sont correctement configurées
- Assurez-vous que l'utilisateur a les bonnes permissions

### Problème de connexion
- Vérifiez les variables d'environnement
- Vérifiez que Supabase est accessible

### Modèles face-api.js non chargés
- Exécutez `npm run download-models`
- Vérifiez votre connexion internet

## 📚 Ressources

- [Documentation Supabase](https://supabase.com/docs)
- [Guide d'authentification Supabase](https://supabase.com/docs/guides/auth)
- [Documentation face-api.js](https://github.com/justadudewhohacks/face-api.js)

---

**Note :** Ce système est configuré pour le développement. Pour la production, assurez-vous de :
- Changer tous les mots de passe par défaut
- Configurer des politiques de sécurité plus strictes
- Utiliser HTTPS
- Configurer la confirmation par email
