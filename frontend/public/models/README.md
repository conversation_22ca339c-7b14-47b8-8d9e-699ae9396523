# Modèles Face-API.js

Ce dossier doit contenir les modèles pré-entraînés de face-api.js pour la reconnaissance faciale.

## Mod<PERSON>les requis

Téléchargez les modèles suivants depuis le repository officiel face-api.js :
https://github.com/justadudewhohacks/face-api.js/tree/master/weights

### Modèles nécessaires :
1. **tiny_face_detector_model-weights_manifest.json**
2. **tiny_face_detector_model-shard1**
3. **face_landmark_68_model-weights_manifest.json**
4. **face_landmark_68_model-shard1**
5. **face_recognition_model-weights_manifest.json**
6. **face_recognition_model-shard1**
7. **face_recognition_model-shard2**
8. **face_expression_model-weights_manifest.json**
9. **face_expression_model-shard1**
10. **ssd_mobilenetv1_model-weights_manifest.json**
11. **ssd_mobilenetv1_model-shard1**
12. **ssd_mobilenetv1_model-shard2**

## Installation automatique

Vous pouvez télécharger automatiquement les modèles avec cette commande :

```bash
# Depuis le dossier frontend
npm run download-models
```

Ou manuellement :

```bash
cd public/models
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/tiny_face_detector_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/tiny_face_detector_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_landmark_68_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_landmark_68_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-shard2
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_expression_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_expression_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/ssd_mobilenetv1_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/ssd_mobilenetv1_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/ssd_mobilenetv1_model-shard2
```

## Taille des modèles

- **tiny_face_detector** : ~1.2MB (rapide, moins précis)
- **ssd_mobilenetv1** : ~5.4MB (plus lent, plus précis)
- **face_landmark_68** : ~350KB
- **face_recognition** : ~6.2MB
- **face_expression** : ~310KB

**Total : ~13MB**

## Notes

- Les modèles sont chargés automatiquement au démarrage de l'application
- Assurez-vous que tous les fichiers sont présents pour éviter les erreurs
- Les modèles sont mis en cache par le navigateur après le premier chargement
