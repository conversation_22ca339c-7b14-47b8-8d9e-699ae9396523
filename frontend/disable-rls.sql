-- <PERSON>ript pour désactiver temporairement RLS et permettre le développement
-- ATTENTION : À utiliser uniquement en développement !

-- Supprimer toutes les politiques existantes
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON users;
DROP POLICY IF EXISTS "Admins can manage all users" ON users;
DROP POLICY IF EXISTS "Public read access" ON users;
DROP POLICY IF EXISTS "Authenticated users can read" ON users;
DROP POLICY IF EXISTS "Users can insert own profile" ON users;
DROP POLICY IF EXISTS "Authenticated users can read all profiles" ON users;
DROP POLICY IF EXISTS "Users can create own profile" ON users;
DROP POLICY IF EXISTS "Teachers can manage own courses" ON users;
DROP POLICY IF EXISTS "Authenticated users can read courses" ON courses;
DROP POLICY IF EXISTS "Teachers and admins can manage attendance" ON attendance;
DROP POLICY IF EXISTS "Students can view own attendance" ON attendance;
DROP POLICY IF EXISTS "Authenticated users can read attendance" ON attendance;
DROP POLICY IF EXISTS "Authenticated users can read face encodings" ON face_encodings;
DROP POLICY IF EXISTS "Users can manage own face encodings" ON face_encodings;
DROP POLICY IF EXISTS "Admins can manage all face encodings" ON face_encodings;

-- Désactiver RLS sur toutes les tables
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE courses DISABLE ROW LEVEL SECURITY;
ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;
ALTER TABLE face_encodings DISABLE ROW LEVEL SECURITY;

-- Supprimer la fonction de rôle si elle existe
DROP FUNCTION IF EXISTS auth.user_role();

-- Message de confirmation
SELECT 'RLS désactivé - Vous pouvez maintenant synchroniser les utilisateurs' as message;
