[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts": "15", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts": "16", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts": "17", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts": "18", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx": "19", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx": "20", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx": "21", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx": "22", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx": "23", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx": "24", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx": "25", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx": "26", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts": "27", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx": "28", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx": "29"}, {"size": 554, "mtime": 1749575702457, "results": "30", "hashOfConfig": "31"}, {"size": 425, "mtime": 1749575702458, "results": "32", "hashOfConfig": "31"}, {"size": 4110, "mtime": 1749586899669, "results": "33", "hashOfConfig": "31"}, {"size": 6309, "mtime": 1749578426980, "results": "34", "hashOfConfig": "31"}, {"size": 10383, "mtime": 1749577748157, "results": "35", "hashOfConfig": "31"}, {"size": 6112, "mtime": 1749575999495, "results": "36", "hashOfConfig": "31"}, {"size": 1148, "mtime": 1749576090473, "results": "37", "hashOfConfig": "31"}, {"size": 10310, "mtime": 1749575901659, "results": "38", "hashOfConfig": "31"}, {"size": 7102, "mtime": 1749581353897, "results": "39", "hashOfConfig": "31"}, {"size": 6313, "mtime": 1749577231585, "results": "40", "hashOfConfig": "31"}, {"size": 5957, "mtime": 1749575859592, "results": "41", "hashOfConfig": "31"}, {"size": 10225, "mtime": 1749581298076, "results": "42", "hashOfConfig": "31"}, {"size": 3329, "mtime": 1749585585003, "results": "43", "hashOfConfig": "31"}, {"size": 1172, "mtime": 1749580921133, "results": "44", "hashOfConfig": "31"}, {"size": 9530, "mtime": 1749579214985, "results": "45", "hashOfConfig": "31"}, {"size": 7301, "mtime": 1749581085210, "results": "46", "hashOfConfig": "31"}, {"size": 14883, "mtime": 1749587079093, "results": "47", "hashOfConfig": "31"}, {"size": 783, "mtime": 1749580389420, "results": "48", "hashOfConfig": "31"}, {"size": 7413, "mtime": 1749585009875, "results": "49", "hashOfConfig": "31"}, {"size": 8456, "mtime": 1749584398532, "results": "50", "hashOfConfig": "31"}, {"size": 7468, "mtime": 1749584862772, "results": "51", "hashOfConfig": "31"}, {"size": 10015, "mtime": 1749585465222, "results": "52", "hashOfConfig": "31"}, {"size": 16192, "mtime": 1749587101550, "results": "53", "hashOfConfig": "31"}, {"size": 12490, "mtime": 1749586456187, "results": "54", "hashOfConfig": "31"}, {"size": 14719, "mtime": 1749587031546, "results": "55", "hashOfConfig": "31"}, {"size": 12368, "mtime": 1749586266884, "results": "56", "hashOfConfig": "31"}, {"size": 10242, "mtime": 1749587006699, "results": "57", "hashOfConfig": "31"}, {"size": 12142, "mtime": 1749586858535, "results": "58", "hashOfConfig": "31"}, {"size": 15357, "mtime": 1749586520310, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", ["147"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["148", "149"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts", ["150"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx", ["151", "152", "153"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx", ["154"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx", ["155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx", ["168", "169", "170", "171", "172", "173", "174"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx", ["175"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx", ["176", "177"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts", ["178"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx", ["179"], [], {"ruleId": "180", "severity": 1, "message": "181", "line": 8, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 8, "endColumn": 21}, {"ruleId": "180", "severity": 1, "message": "184", "line": 15, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 15, "endColumn": 10}, {"ruleId": "185", "severity": 1, "message": "186", "line": 41, "column": 6, "nodeType": "187", "endLine": 41, "endColumn": 8, "suggestions": "188"}, {"ruleId": "180", "severity": 1, "message": "189", "line": 102, "column": 15, "nodeType": "182", "messageId": "183", "endLine": 102, "endColumn": 19}, {"ruleId": "180", "severity": 1, "message": "184", "line": 13, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 13, "endColumn": 10}, {"ruleId": "180", "severity": 1, "message": "189", "line": 74, "column": 15, "nodeType": "182", "messageId": "183", "endLine": 74, "endColumn": 19}, {"ruleId": "180", "severity": 1, "message": "189", "line": 106, "column": 15, "nodeType": "182", "messageId": "183", "endLine": 106, "endColumn": 19}, {"ruleId": "180", "severity": 1, "message": "190", "line": 16, "column": 18, "nodeType": "182", "messageId": "183", "endLine": 16, "endColumn": 33}, {"ruleId": "180", "severity": 1, "message": "191", "line": 21, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 21, "endColumn": 12}, {"ruleId": "180", "severity": 1, "message": "192", "line": 22, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 22, "endColumn": 14}, {"ruleId": "180", "severity": 1, "message": "193", "line": 23, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 23, "endColumn": 13}, {"ruleId": "180", "severity": 1, "message": "194", "line": 24, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 24, "endColumn": 9}, {"ruleId": "180", "severity": 1, "message": "195", "line": 47, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 47, "endColumn": 11}, {"ruleId": "180", "severity": 1, "message": "196", "line": 48, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 48, "endColumn": 6}, {"ruleId": "180", "severity": 1, "message": "197", "line": 49, "column": 3, "nodeType": "182", "messageId": "183", "endLine": 49, "endColumn": 7}, {"ruleId": "180", "severity": 1, "message": "198", "line": 52, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 52, "endColumn": 32}, {"ruleId": "180", "severity": 1, "message": "199", "line": 81, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 81, "endColumn": 27}, {"ruleId": "180", "severity": 1, "message": "200", "line": 82, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 82, "endColumn": 17}, {"ruleId": "180", "severity": 1, "message": "201", "line": 89, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 89, "endColumn": 30}, {"ruleId": "185", "severity": 1, "message": "202", "line": 227, "column": 6, "nodeType": "187", "endLine": 227, "endColumn": 8, "suggestions": "203"}, {"ruleId": "180", "severity": 1, "message": "204", "line": 229, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 229, "endColumn": 15}, {"ruleId": "180", "severity": 1, "message": "205", "line": 22, "column": 16, "nodeType": "182", "messageId": "183", "endLine": 22, "endColumn": 24}, {"ruleId": "180", "severity": 1, "message": "206", "line": 23, "column": 11, "nodeType": "182", "messageId": "183", "endLine": 23, "endColumn": 19}, {"ruleId": "180", "severity": 1, "message": "207", "line": 24, "column": 15, "nodeType": "182", "messageId": "183", "endLine": 24, "endColumn": 27}, {"ruleId": "180", "severity": 1, "message": "208", "line": 34, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 34, "endColumn": 21}, {"ruleId": "180", "severity": 1, "message": "209", "line": 34, "column": 23, "nodeType": "182", "messageId": "183", "endLine": 34, "endColumn": 37}, {"ruleId": "180", "severity": 1, "message": "210", "line": 35, "column": 25, "nodeType": "182", "messageId": "183", "endLine": 35, "endColumn": 41}, {"ruleId": "180", "severity": 1, "message": "200", "line": 37, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 37, "endColumn": 17}, {"ruleId": "180", "severity": 1, "message": "211", "line": 73, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 73, "endColumn": 26}, {"ruleId": "185", "severity": 1, "message": "212", "line": 105, "column": 6, "nodeType": "187", "endLine": 105, "endColumn": 8, "suggestions": "213"}, {"ruleId": "185", "severity": 1, "message": "214", "line": 133, "column": 6, "nodeType": "187", "endLine": 133, "endColumn": 32, "suggestions": "215"}, {"ruleId": "180", "severity": 1, "message": "216", "line": 8, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 8, "endColumn": 22}, {"ruleId": "217", "severity": 1, "message": "218", "line": 254, "column": 27, "nodeType": "219", "messageId": "220", "endLine": 280, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'useFirebase' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testSupabaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["221"], "'data' is assigned a value but never used.", "'CloudUploadIcon' is defined but never used.", "'TextField' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'faceRecognitionService' is defined but never used.", "'attendanceRecords' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'faceRegistrationOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["222"], "'COLORS' is assigned a value but never used.", "'PlayIcon' is defined but never used.", "'StopIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'currentUser' is assigned a value but never used.", "'setCurrentUser' is assigned a value but never used.", "'setSessionActive' is assigned a value but never used.", "'detectionHistory' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'stopDetection'. Either include it or remove the dependency array.", ["223"], "React Hook useCallback has a missing dependency: 'performDetection'. Either include it or remove the dependency array.", ["224"], "'FaceEncoding' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'successCount'.", "ArrowFunctionExpression", "unsafeRefs", {"desc": "225", "fix": "226"}, {"desc": "227", "fix": "228"}, {"desc": "229", "fix": "230"}, {"desc": "231", "fix": "232"}, "Update the dependencies array to be: [testSupabaseConnection]", {"range": "233", "text": "234"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "235", "text": "236"}, "Update the dependencies array to be: [stopDetection]", {"range": "237", "text": "238"}, "Update the dependencies array to be: [isStreaming, isDetecting, performDetection]", {"range": "239", "text": "240"}, [1331, 1333], "[testSupabaseConnection]", [6987, 6989], "[loadDashboardData]", [2804, 2806], "[stopDetection]", [3595, 3621], "[isStreaming, isDetecting, performDetection]"]