[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/firebase.ts": "15"}, {"size": 554, "mtime": 1749575702457, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1749575702458, "results": "18", "hashOfConfig": "17"}, {"size": 3203, "mtime": 1749578555385, "results": "19", "hashOfConfig": "17"}, {"size": 6309, "mtime": 1749578426980, "results": "20", "hashOfConfig": "17"}, {"size": 10383, "mtime": 1749577748157, "results": "21", "hashOfConfig": "17"}, {"size": 6112, "mtime": 1749575999495, "results": "22", "hashOfConfig": "17"}, {"size": 1148, "mtime": 1749576090473, "results": "23", "hashOfConfig": "17"}, {"size": 10310, "mtime": 1749575901659, "results": "24", "hashOfConfig": "17"}, {"size": 7102, "mtime": 1749578588691, "results": "25", "hashOfConfig": "17"}, {"size": 6313, "mtime": 1749577231585, "results": "26", "hashOfConfig": "17"}, {"size": 5957, "mtime": 1749575859592, "results": "27", "hashOfConfig": "17"}, {"size": 7449, "mtime": 1749578747557, "results": "28", "hashOfConfig": "17"}, {"size": 8911, "mtime": 1749578772058, "results": "29", "hashOfConfig": "17"}, {"size": 5789, "mtime": 1749578704691, "results": "30", "hashOfConfig": "17"}, {"size": 1110, "mtime": 1749578258757, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", ["77"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["78", "79"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", ["80", "81"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/firebase.ts", [], [], {"ruleId": "82", "severity": 1, "message": "83", "line": 8, "column": 10, "nodeType": "84", "messageId": "85", "endLine": 8, "endColumn": 21}, {"ruleId": "82", "severity": 1, "message": "86", "line": 15, "column": 3, "nodeType": "84", "messageId": "85", "endLine": 15, "endColumn": 10}, {"ruleId": "87", "severity": 1, "message": "88", "line": 35, "column": 6, "nodeType": "89", "endLine": 35, "endColumn": 8, "suggestions": "90"}, {"ruleId": "82", "severity": 1, "message": "91", "line": 16, "column": 3, "nodeType": "84", "messageId": "85", "endLine": 16, "endColumn": 8}, {"ruleId": "82", "severity": 1, "message": "92", "line": 26, "column": 42, "nodeType": "84", "messageId": "85", "endLine": 26, "endColumn": 54}, "@typescript-eslint/no-unused-vars", "'useFirebase' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testFirebaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["93"], "'limit' is defined but never used.", "'StudentGroup' is defined but never used.", {"desc": "94", "fix": "95"}, "Update the dependencies array to be: [testFirebaseConnection]", {"range": "96", "text": "97"}, [901, 903], "[testFirebaseConnection]"]