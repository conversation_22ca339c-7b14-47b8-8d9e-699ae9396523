[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts": "15", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts": "16", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts": "17", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts": "18", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx": "19", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx": "20", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx": "21", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx": "22", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx": "23", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx": "24", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx": "25", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx": "26", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts": "27", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx": "28", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx": "29", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/StudentDashboard.tsx": "30", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/TeacherDashboard.tsx": "31"}, {"size": 554, "mtime": 1749575702457, "results": "32", "hashOfConfig": "33"}, {"size": 425, "mtime": 1749575702458, "results": "34", "hashOfConfig": "33"}, {"size": 5409, "mtime": 1749589045007, "results": "35", "hashOfConfig": "33"}, {"size": 7682, "mtime": 1749588437043, "results": "36", "hashOfConfig": "33"}, {"size": 10383, "mtime": 1749577748157, "results": "37", "hashOfConfig": "33"}, {"size": 8182, "mtime": 1749588629301, "results": "38", "hashOfConfig": "33"}, {"size": 1148, "mtime": 1749576090473, "results": "39", "hashOfConfig": "33"}, {"size": 10310, "mtime": 1749575901659, "results": "40", "hashOfConfig": "33"}, {"size": 7102, "mtime": 1749581353897, "results": "41", "hashOfConfig": "33"}, {"size": 6315, "mtime": 1749589207703, "results": "42", "hashOfConfig": "33"}, {"size": 5957, "mtime": 1749575859592, "results": "43", "hashOfConfig": "33"}, {"size": 10225, "mtime": 1749581298076, "results": "44", "hashOfConfig": "33"}, {"size": 3360, "mtime": 1749589391622, "results": "45", "hashOfConfig": "33"}, {"size": 1172, "mtime": 1749580921133, "results": "46", "hashOfConfig": "33"}, {"size": 9530, "mtime": 1749579214985, "results": "47", "hashOfConfig": "33"}, {"size": 7301, "mtime": 1749581085210, "results": "48", "hashOfConfig": "33"}, {"size": 15495, "mtime": 1749589310245, "results": "49", "hashOfConfig": "33"}, {"size": 783, "mtime": 1749580389420, "results": "50", "hashOfConfig": "33"}, {"size": 7413, "mtime": 1749585009875, "results": "51", "hashOfConfig": "33"}, {"size": 8456, "mtime": 1749584398532, "results": "52", "hashOfConfig": "33"}, {"size": 7468, "mtime": 1749584862772, "results": "53", "hashOfConfig": "33"}, {"size": 10015, "mtime": 1749585465222, "results": "54", "hashOfConfig": "33"}, {"size": 27481, "mtime": 1749588900608, "results": "55", "hashOfConfig": "33"}, {"size": 12490, "mtime": 1749586456187, "results": "56", "hashOfConfig": "33"}, {"size": 14719, "mtime": 1749587031546, "results": "57", "hashOfConfig": "33"}, {"size": 12368, "mtime": 1749586266884, "results": "58", "hashOfConfig": "33"}, {"size": 10242, "mtime": 1749587006699, "results": "59", "hashOfConfig": "33"}, {"size": 12142, "mtime": 1749586858535, "results": "60", "hashOfConfig": "33"}, {"size": 15357, "mtime": 1749586520310, "results": "61", "hashOfConfig": "33"}, {"size": 10956, "mtime": 1749588679953, "results": "62", "hashOfConfig": "33"}, {"size": 15143, "mtime": 1749588737823, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["157", "158"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts", ["159"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx", ["160", "161", "162"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx", ["163"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx", ["164", "165", "166", "167", "168", "169", "170", "171", "172"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx", ["173", "174", "175", "176", "177", "178", "179"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx", ["180"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx", ["181", "182"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts", ["183"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx", ["184"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/StudentDashboard.tsx", ["185", "186"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/TeacherDashboard.tsx", ["187", "188"], [], {"ruleId": "189", "severity": 1, "message": "190", "line": 15, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 15, "endColumn": 10}, {"ruleId": "193", "severity": 1, "message": "194", "line": 41, "column": 6, "nodeType": "195", "endLine": 41, "endColumn": 8, "suggestions": "196"}, {"ruleId": "189", "severity": 1, "message": "197", "line": 102, "column": 15, "nodeType": "191", "messageId": "192", "endLine": 102, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "190", "line": 13, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 13, "endColumn": 10}, {"ruleId": "189", "severity": 1, "message": "197", "line": 74, "column": 15, "nodeType": "191", "messageId": "192", "endLine": 74, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "197", "line": 106, "column": 15, "nodeType": "191", "messageId": "192", "endLine": 106, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "198", "line": 16, "column": 18, "nodeType": "191", "messageId": "192", "endLine": 16, "endColumn": 33}, {"ruleId": "189", "severity": 1, "message": "199", "line": 21, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 21, "endColumn": 12}, {"ruleId": "189", "severity": 1, "message": "200", "line": 44, "column": 11, "nodeType": "191", "messageId": "192", "endLine": 44, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "201", "line": 47, "column": 13, "nodeType": "191", "messageId": "192", "endLine": 47, "endColumn": 23}, {"ruleId": "189", "severity": 1, "message": "202", "line": 60, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 60, "endColumn": 11}, {"ruleId": "189", "severity": 1, "message": "203", "line": 61, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 61, "endColumn": 6}, {"ruleId": "189", "severity": 1, "message": "204", "line": 62, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 62, "endColumn": 7}, {"ruleId": "189", "severity": 1, "message": "205", "line": 98, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 98, "endColumn": 27}, {"ruleId": "193", "severity": 1, "message": "206", "line": 246, "column": 6, "nodeType": "195", "endLine": 246, "endColumn": 8, "suggestions": "207"}, {"ruleId": "189", "severity": 1, "message": "208", "line": 248, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 248, "endColumn": 15}, {"ruleId": "189", "severity": 1, "message": "209", "line": 22, "column": 16, "nodeType": "191", "messageId": "192", "endLine": 22, "endColumn": 24}, {"ruleId": "189", "severity": 1, "message": "210", "line": 23, "column": 11, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "211", "line": 24, "column": 15, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 27}, {"ruleId": "189", "severity": 1, "message": "212", "line": 34, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 34, "endColumn": 21}, {"ruleId": "189", "severity": 1, "message": "213", "line": 34, "column": 23, "nodeType": "191", "messageId": "192", "endLine": 34, "endColumn": 37}, {"ruleId": "189", "severity": 1, "message": "214", "line": 35, "column": 25, "nodeType": "191", "messageId": "192", "endLine": 35, "endColumn": 41}, {"ruleId": "189", "severity": 1, "message": "215", "line": 37, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 37, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "216", "line": 73, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 73, "endColumn": 26}, {"ruleId": "193", "severity": 1, "message": "217", "line": 105, "column": 6, "nodeType": "195", "endLine": 105, "endColumn": 8, "suggestions": "218"}, {"ruleId": "193", "severity": 1, "message": "219", "line": 133, "column": 6, "nodeType": "195", "endLine": 133, "endColumn": 32, "suggestions": "220"}, {"ruleId": "189", "severity": 1, "message": "221", "line": 8, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 8, "endColumn": 22}, {"ruleId": "222", "severity": 1, "message": "223", "line": 254, "column": 27, "nodeType": "224", "messageId": "225", "endLine": 280, "endColumn": 10}, {"ruleId": "189", "severity": 1, "message": "226", "line": 41, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 41, "endColumn": 17}, {"ruleId": "193", "severity": 1, "message": "227", "line": 75, "column": 6, "nodeType": "195", "endLine": 75, "endColumn": 12, "suggestions": "228"}, {"ruleId": "193", "severity": 1, "message": "229", "line": 118, "column": 6, "nodeType": "195", "endLine": 118, "endColumn": 12, "suggestions": "230"}, {"ruleId": "189", "severity": 1, "message": "231", "line": 127, "column": 11, "nodeType": "191", "messageId": "192", "endLine": 127, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'Divider' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testSupabaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["232"], "'data' is assigned a value but never used.", "'CloudUploadIcon' is defined but never used.", "'TextField' is defined but never used.", "'FaceIcon' is defined but never used.", "'UploadIcon' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'attendanceRecords' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["233"], "'COLORS' is assigned a value but never used.", "'PlayIcon' is defined but never used.", "'StopIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'currentUser' is assigned a value but never used.", "'setCurrentUser' is assigned a value but never used.", "'setSessionActive' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'detectionHistory' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'stopDetection'. Either include it or remove the dependency array.", ["234"], "React Hook useCallback has a missing dependency: 'performDetection'. Either include it or remove the dependency array.", ["235"], "'FaceEncoding' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'successCount'.", "ArrowFunctionExpression", "unsafeRefs", "'courses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudentData'. Either include it or remove the dependency array.", ["236"], "React Hook useEffect has a missing dependency: 'loadTeacherData'. Either include it or remove the dependency array.", ["237"], "'absent' is assigned a value but never used.", {"desc": "238", "fix": "239"}, {"desc": "240", "fix": "241"}, {"desc": "242", "fix": "243"}, {"desc": "244", "fix": "245"}, {"desc": "246", "fix": "247"}, {"desc": "248", "fix": "249"}, "Update the dependencies array to be: [testSupabaseConnection]", {"range": "250", "text": "251"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "252", "text": "253"}, "Update the dependencies array to be: [stopDetection]", {"range": "254", "text": "255"}, "Update the dependencies array to be: [isStreaming, isDetecting, performDetection]", {"range": "256", "text": "257"}, "Update the dependencies array to be: [loadStudentData, user]", {"range": "258", "text": "259"}, "Update the dependencies array to be: [loadTeacher<PERSON><PERSON>, user]", {"range": "260", "text": "261"}, [1331, 1333], "[testSupabaseConnection]", [7630, 7632], "[loadDashboardData]", [2804, 2806], "[stopDetection]", [3595, 3621], "[isStreaming, isDetecting, performDetection]", [1995, 2001], "[loadStudentData, user]", [3187, 3193], "[loadTeach<PERSON><PERSON><PERSON>, user]"]