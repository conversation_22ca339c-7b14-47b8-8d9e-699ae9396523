[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts": "15", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts": "16", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts": "17", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts": "18"}, {"size": 554, "mtime": 1749575702457, "results": "19", "hashOfConfig": "20"}, {"size": 425, "mtime": 1749575702458, "results": "21", "hashOfConfig": "20"}, {"size": 3415, "mtime": 1749581335791, "results": "22", "hashOfConfig": "20"}, {"size": 6309, "mtime": 1749578426980, "results": "23", "hashOfConfig": "20"}, {"size": 10383, "mtime": 1749577748157, "results": "24", "hashOfConfig": "20"}, {"size": 6112, "mtime": 1749575999495, "results": "25", "hashOfConfig": "20"}, {"size": 1148, "mtime": 1749576090473, "results": "26", "hashOfConfig": "20"}, {"size": 10310, "mtime": 1749575901659, "results": "27", "hashOfConfig": "20"}, {"size": 7102, "mtime": 1749581353897, "results": "28", "hashOfConfig": "20"}, {"size": 6313, "mtime": 1749577231585, "results": "29", "hashOfConfig": "20"}, {"size": 5957, "mtime": 1749575859592, "results": "30", "hashOfConfig": "20"}, {"size": 10225, "mtime": 1749581298076, "results": "31", "hashOfConfig": "20"}, {"size": 3343, "mtime": 1749580867739, "results": "32", "hashOfConfig": "20"}, {"size": 1172, "mtime": 1749580921133, "results": "33", "hashOfConfig": "20"}, {"size": 9530, "mtime": 1749579214985, "results": "34", "hashOfConfig": "20"}, {"size": 7301, "mtime": 1749581085210, "results": "35", "hashOfConfig": "20"}, {"size": 14314, "mtime": 1749581040686, "results": "36", "hashOfConfig": "20"}, {"size": 783, "mtime": 1749580389420, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", ["92"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["93", "94"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", ["95"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts", ["96"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts", ["97", "98", "99"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts", [], [], {"ruleId": "100", "severity": 1, "message": "101", "line": 8, "column": 10, "nodeType": "102", "messageId": "103", "endLine": 8, "endColumn": 21}, {"ruleId": "100", "severity": 1, "message": "104", "line": 15, "column": 3, "nodeType": "102", "messageId": "103", "endLine": 15, "endColumn": 10}, {"ruleId": "105", "severity": 1, "message": "106", "line": 41, "column": 6, "nodeType": "107", "endLine": 41, "endColumn": 8, "suggestions": "108"}, {"ruleId": "100", "severity": 1, "message": "109", "line": 7, "column": 42, "nodeType": "102", "messageId": "103", "endLine": 7, "endColumn": 54}, {"ruleId": "100", "severity": 1, "message": "110", "line": 102, "column": 15, "nodeType": "102", "messageId": "103", "endLine": 102, "endColumn": 19}, {"ruleId": "100", "severity": 1, "message": "109", "line": 6, "column": 42, "nodeType": "102", "messageId": "103", "endLine": 6, "endColumn": 54}, {"ruleId": "100", "severity": 1, "message": "111", "line": 6, "column": 66, "nodeType": "102", "messageId": "103", "endLine": 6, "endColumn": 82}, {"ruleId": "100", "severity": 1, "message": "112", "line": 6, "column": 84, "nodeType": "102", "messageId": "103", "endLine": 6, "endColumn": 100}, "@typescript-eslint/no-unused-vars", "'useFirebase' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testSupabaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["113"], "'StudentGroup' is defined but never used.", "'data' is assigned a value but never used.", "'AttendanceStatus' is defined but never used.", "'AttendanceMethod' is defined but never used.", {"desc": "114", "fix": "115"}, "Update the dependencies array to be: [testSupabaseConnection]", {"range": "116", "text": "117"}, [1331, 1333], "[testSupabaseConnection]"]