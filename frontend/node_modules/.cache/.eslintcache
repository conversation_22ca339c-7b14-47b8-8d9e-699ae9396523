[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/firebase.ts": "15", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts": "16"}, {"size": 554, "mtime": 1749575702457, "results": "17", "hashOfConfig": "18"}, {"size": 425, "mtime": 1749575702458, "results": "19", "hashOfConfig": "18"}, {"size": 3203, "mtime": 1749578555385, "results": "20", "hashOfConfig": "18"}, {"size": 6309, "mtime": 1749578426980, "results": "21", "hashOfConfig": "18"}, {"size": 10383, "mtime": 1749577748157, "results": "22", "hashOfConfig": "18"}, {"size": 6112, "mtime": 1749575999495, "results": "23", "hashOfConfig": "18"}, {"size": 1148, "mtime": 1749576090473, "results": "24", "hashOfConfig": "18"}, {"size": 10310, "mtime": 1749575901659, "results": "25", "hashOfConfig": "18"}, {"size": 7102, "mtime": 1749578588691, "results": "26", "hashOfConfig": "18"}, {"size": 6313, "mtime": 1749577231585, "results": "27", "hashOfConfig": "18"}, {"size": 5957, "mtime": 1749575859592, "results": "28", "hashOfConfig": "18"}, {"size": 10172, "mtime": 1749579356768, "results": "29", "hashOfConfig": "18"}, {"size": 8911, "mtime": 1749578772058, "results": "30", "hashOfConfig": "18"}, {"size": 5789, "mtime": 1749578704691, "results": "31", "hashOfConfig": "18"}, {"size": 1110, "mtime": 1749578258757, "results": "32", "hashOfConfig": "18"}, {"size": 9530, "mtime": 1749579214985, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", ["82"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["83", "84"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", ["85", "86"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/firebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts", [], [], {"ruleId": "87", "severity": 1, "message": "88", "line": 8, "column": 10, "nodeType": "89", "messageId": "90", "endLine": 8, "endColumn": 21}, {"ruleId": "87", "severity": 1, "message": "91", "line": 15, "column": 3, "nodeType": "89", "messageId": "90", "endLine": 15, "endColumn": 10}, {"ruleId": "92", "severity": 1, "message": "93", "line": 41, "column": 6, "nodeType": "94", "endLine": 41, "endColumn": 8, "suggestions": "95"}, {"ruleId": "87", "severity": 1, "message": "96", "line": 16, "column": 3, "nodeType": "89", "messageId": "90", "endLine": 16, "endColumn": 8}, {"ruleId": "87", "severity": 1, "message": "97", "line": 26, "column": 42, "nodeType": "89", "messageId": "90", "endLine": 26, "endColumn": 54}, "@typescript-eslint/no-unused-vars", "'useFirebase' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testFirebaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["98"], "'limit' is defined but never used.", "'StudentGroup' is defined but never used.", {"desc": "99", "fix": "100"}, "Update the dependencies array to be: [testFirebaseConnection]", {"range": "101", "text": "102"}, [1275, 1277], "[testFirebaseConnection]"]