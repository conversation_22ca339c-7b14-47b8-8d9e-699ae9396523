[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts": "15", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts": "16", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts": "17", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts": "18", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx": "19", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx": "20", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx": "21", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx": "22"}, {"size": 554, "mtime": 1749575702457, "results": "23", "hashOfConfig": "24"}, {"size": 425, "mtime": 1749575702458, "results": "25", "hashOfConfig": "24"}, {"size": 3405, "mtime": 1749585448926, "results": "26", "hashOfConfig": "24"}, {"size": 6309, "mtime": 1749578426980, "results": "27", "hashOfConfig": "24"}, {"size": 10383, "mtime": 1749577748157, "results": "28", "hashOfConfig": "24"}, {"size": 6112, "mtime": 1749575999495, "results": "29", "hashOfConfig": "24"}, {"size": 1148, "mtime": 1749576090473, "results": "30", "hashOfConfig": "24"}, {"size": 10310, "mtime": 1749575901659, "results": "31", "hashOfConfig": "24"}, {"size": 7102, "mtime": 1749581353897, "results": "32", "hashOfConfig": "24"}, {"size": 6313, "mtime": 1749577231585, "results": "33", "hashOfConfig": "24"}, {"size": 5957, "mtime": 1749575859592, "results": "34", "hashOfConfig": "24"}, {"size": 10225, "mtime": 1749581298076, "results": "35", "hashOfConfig": "24"}, {"size": 3329, "mtime": 1749585585003, "results": "36", "hashOfConfig": "24"}, {"size": 1172, "mtime": 1749580921133, "results": "37", "hashOfConfig": "24"}, {"size": 9530, "mtime": 1749579214985, "results": "38", "hashOfConfig": "24"}, {"size": 7301, "mtime": 1749581085210, "results": "39", "hashOfConfig": "24"}, {"size": 14264, "mtime": 1749585558017, "results": "40", "hashOfConfig": "24"}, {"size": 783, "mtime": 1749580389420, "results": "41", "hashOfConfig": "24"}, {"size": 7413, "mtime": 1749585009875, "results": "42", "hashOfConfig": "24"}, {"size": 8456, "mtime": 1749584398532, "results": "43", "hashOfConfig": "24"}, {"size": 7468, "mtime": 1749584862772, "results": "44", "hashOfConfig": "24"}, {"size": 10015, "mtime": 1749585465222, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", ["112"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["113", "114"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts", ["115"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx", ["116", "117", "118"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx", ["119"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx", [], [], {"ruleId": "120", "severity": 1, "message": "121", "line": 8, "column": 10, "nodeType": "122", "messageId": "123", "endLine": 8, "endColumn": 21}, {"ruleId": "120", "severity": 1, "message": "124", "line": 15, "column": 3, "nodeType": "122", "messageId": "123", "endLine": 15, "endColumn": 10}, {"ruleId": "125", "severity": 1, "message": "126", "line": 41, "column": 6, "nodeType": "127", "endLine": 41, "endColumn": 8, "suggestions": "128"}, {"ruleId": "120", "severity": 1, "message": "129", "line": 102, "column": 15, "nodeType": "122", "messageId": "123", "endLine": 102, "endColumn": 19}, {"ruleId": "120", "severity": 1, "message": "124", "line": 13, "column": 3, "nodeType": "122", "messageId": "123", "endLine": 13, "endColumn": 10}, {"ruleId": "120", "severity": 1, "message": "129", "line": 74, "column": 15, "nodeType": "122", "messageId": "123", "endLine": 74, "endColumn": 19}, {"ruleId": "120", "severity": 1, "message": "129", "line": 106, "column": 15, "nodeType": "122", "messageId": "123", "endLine": 106, "endColumn": 19}, {"ruleId": "120", "severity": 1, "message": "130", "line": 16, "column": 18, "nodeType": "122", "messageId": "123", "endLine": 16, "endColumn": 33}, "@typescript-eslint/no-unused-vars", "'useFirebase' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testSupabaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["131"], "'data' is assigned a value but never used.", "'CloudUploadIcon' is defined but never used.", {"desc": "132", "fix": "133"}, "Update the dependencies array to be: [testSupabaseConnection]", {"range": "134", "text": "135"}, [1331, 1333], "[testSupabaseConnection]"]