[{"/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx": "5", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx": "7", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts": "8", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx": "10", "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts": "11", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx": "12", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts": "13", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts": "14", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts": "15", "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts": "16", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts": "17", "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts": "18", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx": "19", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx": "20", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx": "21", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx": "22", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx": "23", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx": "24", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx": "25", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx": "26", "/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts": "27", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx": "28", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx": "29", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/StudentDashboard.tsx": "30", "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/TeacherDashboard.tsx": "31", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/TestConnection.tsx": "32", "/Users/<USER>/Documents/PresencePRO/frontend/src/components/DebugAuth.tsx": "33"}, {"size": 554, "mtime": 1749575702457, "results": "34", "hashOfConfig": "35"}, {"size": 425, "mtime": 1749575702458, "results": "36", "hashOfConfig": "35"}, {"size": 5689, "mtime": 1749658703603, "results": "37", "hashOfConfig": "35"}, {"size": 10869, "mtime": 1749658350568, "results": "38", "hashOfConfig": "35"}, {"size": 10383, "mtime": 1749577748157, "results": "39", "hashOfConfig": "35"}, {"size": 8707, "mtime": 1749591738274, "results": "40", "hashOfConfig": "35"}, {"size": 1148, "mtime": 1749576090473, "results": "41", "hashOfConfig": "35"}, {"size": 10310, "mtime": 1749575901659, "results": "42", "hashOfConfig": "35"}, {"size": 7102, "mtime": 1749581353897, "results": "43", "hashOfConfig": "35"}, {"size": 6315, "mtime": 1749589207703, "results": "44", "hashOfConfig": "35"}, {"size": 5961, "mtime": 1749592693261, "results": "45", "hashOfConfig": "35"}, {"size": 10225, "mtime": 1749581298076, "results": "46", "hashOfConfig": "35"}, {"size": 3360, "mtime": 1749589391622, "results": "47", "hashOfConfig": "35"}, {"size": 1172, "mtime": 1749580921133, "results": "48", "hashOfConfig": "35"}, {"size": 9533, "mtime": 1749592834983, "results": "49", "hashOfConfig": "35"}, {"size": 7301, "mtime": 1749581085210, "results": "50", "hashOfConfig": "35"}, {"size": 16268, "mtime": 1749592507768, "results": "51", "hashOfConfig": "35"}, {"size": 783, "mtime": 1749580389420, "results": "52", "hashOfConfig": "35"}, {"size": 7413, "mtime": 1749585009875, "results": "53", "hashOfConfig": "35"}, {"size": 8456, "mtime": 1749584398532, "results": "54", "hashOfConfig": "35"}, {"size": 7468, "mtime": 1749584862772, "results": "55", "hashOfConfig": "35"}, {"size": 10015, "mtime": 1749585465222, "results": "56", "hashOfConfig": "35"}, {"size": 27482, "mtime": 1749592747819, "results": "57", "hashOfConfig": "35"}, {"size": 12490, "mtime": 1749586456187, "results": "58", "hashOfConfig": "35"}, {"size": 14720, "mtime": 1749592731391, "results": "59", "hashOfConfig": "35"}, {"size": 12368, "mtime": 1749586266884, "results": "60", "hashOfConfig": "35"}, {"size": 10242, "mtime": 1749587006699, "results": "61", "hashOfConfig": "35"}, {"size": 12142, "mtime": 1749586858535, "results": "62", "hashOfConfig": "35"}, {"size": 15357, "mtime": 1749586520310, "results": "63", "hashOfConfig": "35"}, {"size": 10986, "mtime": 1749592767279, "results": "64", "hashOfConfig": "35"}, {"size": 15187, "mtime": 1749592784431, "results": "65", "hashOfConfig": "35"}, {"size": 3647, "mtime": 1749658542957, "results": "66", "hashOfConfig": "35"}, {"size": 3471, "mtime": 1749658669807, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lrtrxy", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/PresencePRO/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/DashboardPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Navbar.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx", ["167", "168"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts", ["169"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx", ["170", "171", "172"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx", ["173"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx", ["174", "175", "176", "177", "178", "179", "180", "181", "182"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx", ["183", "184", "185", "186", "187", "188", "189"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx", ["190"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx", ["191", "192"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts", ["193"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx", [], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx", ["194"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/StudentDashboard.tsx", ["195", "196"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/pages/TeacherDashboard.tsx", ["197", "198"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/TestConnection.tsx", ["199"], [], "/Users/<USER>/Documents/PresencePRO/frontend/src/components/DebugAuth.tsx", [], [], {"ruleId": "200", "severity": 1, "message": "201", "line": 15, "column": 3, "nodeType": "202", "messageId": "203", "endLine": 15, "endColumn": 10}, {"ruleId": "204", "severity": 1, "message": "205", "line": 41, "column": 6, "nodeType": "206", "endLine": 41, "endColumn": 8, "suggestions": "207"}, {"ruleId": "200", "severity": 1, "message": "208", "line": 102, "column": 15, "nodeType": "202", "messageId": "203", "endLine": 102, "endColumn": 19}, {"ruleId": "200", "severity": 1, "message": "201", "line": 13, "column": 3, "nodeType": "202", "messageId": "203", "endLine": 13, "endColumn": 10}, {"ruleId": "200", "severity": 1, "message": "208", "line": 74, "column": 15, "nodeType": "202", "messageId": "203", "endLine": 74, "endColumn": 19}, {"ruleId": "200", "severity": 1, "message": "208", "line": 106, "column": 15, "nodeType": "202", "messageId": "203", "endLine": 106, "endColumn": 19}, {"ruleId": "200", "severity": 1, "message": "209", "line": 16, "column": 18, "nodeType": "202", "messageId": "203", "endLine": 16, "endColumn": 33}, {"ruleId": "200", "severity": 1, "message": "210", "line": 21, "column": 3, "nodeType": "202", "messageId": "203", "endLine": 21, "endColumn": 12}, {"ruleId": "200", "severity": 1, "message": "211", "line": 44, "column": 11, "nodeType": "202", "messageId": "203", "endLine": 44, "endColumn": 19}, {"ruleId": "200", "severity": 1, "message": "212", "line": 47, "column": 13, "nodeType": "202", "messageId": "203", "endLine": 47, "endColumn": 23}, {"ruleId": "200", "severity": 1, "message": "213", "line": 60, "column": 3, "nodeType": "202", "messageId": "203", "endLine": 60, "endColumn": 11}, {"ruleId": "200", "severity": 1, "message": "214", "line": 61, "column": 3, "nodeType": "202", "messageId": "203", "endLine": 61, "endColumn": 6}, {"ruleId": "200", "severity": 1, "message": "215", "line": 62, "column": 3, "nodeType": "202", "messageId": "203", "endLine": 62, "endColumn": 7}, {"ruleId": "200", "severity": 1, "message": "216", "line": 98, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 98, "endColumn": 27}, {"ruleId": "204", "severity": 1, "message": "217", "line": 246, "column": 6, "nodeType": "206", "endLine": 246, "endColumn": 8, "suggestions": "218"}, {"ruleId": "200", "severity": 1, "message": "219", "line": 248, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 248, "endColumn": 15}, {"ruleId": "200", "severity": 1, "message": "220", "line": 22, "column": 16, "nodeType": "202", "messageId": "203", "endLine": 22, "endColumn": 24}, {"ruleId": "200", "severity": 1, "message": "221", "line": 23, "column": 11, "nodeType": "202", "messageId": "203", "endLine": 23, "endColumn": 19}, {"ruleId": "200", "severity": 1, "message": "222", "line": 24, "column": 15, "nodeType": "202", "messageId": "203", "endLine": 24, "endColumn": 27}, {"ruleId": "200", "severity": 1, "message": "223", "line": 34, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 34, "endColumn": 21}, {"ruleId": "200", "severity": 1, "message": "224", "line": 34, "column": 23, "nodeType": "202", "messageId": "203", "endLine": 34, "endColumn": 37}, {"ruleId": "200", "severity": 1, "message": "225", "line": 35, "column": 25, "nodeType": "202", "messageId": "203", "endLine": 35, "endColumn": 41}, {"ruleId": "200", "severity": 1, "message": "226", "line": 37, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 37, "endColumn": 17}, {"ruleId": "200", "severity": 1, "message": "227", "line": 73, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 73, "endColumn": 26}, {"ruleId": "204", "severity": 1, "message": "228", "line": 105, "column": 6, "nodeType": "206", "endLine": 105, "endColumn": 8, "suggestions": "229"}, {"ruleId": "204", "severity": 1, "message": "230", "line": 133, "column": 6, "nodeType": "206", "endLine": 133, "endColumn": 32, "suggestions": "231"}, {"ruleId": "200", "severity": 1, "message": "232", "line": 8, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 8, "endColumn": 22}, {"ruleId": "233", "severity": 1, "message": "234", "line": 254, "column": 27, "nodeType": "235", "messageId": "236", "endLine": 280, "endColumn": 10}, {"ruleId": "200", "severity": 1, "message": "237", "line": 41, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 41, "endColumn": 17}, {"ruleId": "204", "severity": 1, "message": "238", "line": 75, "column": 6, "nodeType": "206", "endLine": 75, "endColumn": 12, "suggestions": "239"}, {"ruleId": "204", "severity": 1, "message": "240", "line": 118, "column": 6, "nodeType": "206", "endLine": 118, "endColumn": 12, "suggestions": "241"}, {"ruleId": "200", "severity": 1, "message": "242", "line": 127, "column": 11, "nodeType": "202", "messageId": "203", "endLine": 127, "endColumn": 17}, {"ruleId": "204", "severity": 1, "message": "243", "line": 80, "column": 6, "nodeType": "206", "endLine": 80, "endColumn": 8, "suggestions": "244"}, "@typescript-eslint/no-unused-vars", "'Divider' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'testSupabaseConnection'. Either include it or remove the dependency array.", "ArrayExpression", ["245"], "'data' is assigned a value but never used.", "'CloudUploadIcon' is defined but never used.", "'TextField' is defined but never used.", "'FaceIcon' is defined but never used.", "'UploadIcon' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'attendanceRecords' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["246"], "'COLORS' is assigned a value but never used.", "'PlayIcon' is defined but never used.", "'StopIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'currentUser' is assigned a value but never used.", "'setCurrentUser' is assigned a value but never used.", "'setSessionActive' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'detectionHistory' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'stopDetection'. Either include it or remove the dependency array.", ["247"], "React Hook useCallback has a missing dependency: 'performDetection'. Either include it or remove the dependency array.", ["248"], "'FaceEncoding' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'successCount'.", "ArrowFunctionExpression", "unsafeRefs", "'courses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudentData'. Either include it or remove the dependency array.", ["249"], "React Hook useEffect has a missing dependency: 'loadTeacherData'. Either include it or remove the dependency array.", ["250"], "'absent' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'testConnection'. Either include it or remove the dependency array.", ["251"], {"desc": "252", "fix": "253"}, {"desc": "254", "fix": "255"}, {"desc": "256", "fix": "257"}, {"desc": "258", "fix": "259"}, {"desc": "260", "fix": "261"}, {"desc": "262", "fix": "263"}, {"desc": "264", "fix": "265"}, "Update the dependencies array to be: [testSupabaseConnection]", {"range": "266", "text": "267"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "268", "text": "269"}, "Update the dependencies array to be: [stopDetection]", {"range": "270", "text": "271"}, "Update the dependencies array to be: [isStreaming, isDetecting, performDetection]", {"range": "272", "text": "273"}, "Update the dependencies array to be: [loadStudentData, user]", {"range": "274", "text": "275"}, "Update the dependencies array to be: [loadTeacher<PERSON><PERSON>, user]", {"range": "276", "text": "277"}, "Update the dependencies array to be: [testConnection]", {"range": "278", "text": "279"}, [1331, 1333], "[testSupabaseConnection]", [7631, 7633], "[loadDashboardData]", [2804, 2806], "[stopDetection]", [3595, 3621], "[isStreaming, isDetecting, performDetection]", [1995, 2001], "[loadStudentData, user]", [3187, 3193], "[loadTeach<PERSON><PERSON><PERSON>, user]", [2675, 2677], "[testConnection]"]