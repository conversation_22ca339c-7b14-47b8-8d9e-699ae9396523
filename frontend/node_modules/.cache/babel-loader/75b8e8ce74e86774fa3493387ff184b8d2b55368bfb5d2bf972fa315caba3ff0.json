{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Button, Card, CardContent, Typography, Alert, CircularProgress, Avatar, Grid, Paper, Divider } from '@mui/material';\nimport { Person as PersonIcon, Face as FaceIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { supabaseService } from '../../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SupabaseImageUpload = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState(null);\n  const [uploadedImages, setUploadedImages] = useState([]);\n\n  // Simuler un ID utilisateur pour les tests\n  const testUserId = 'test-user-123';\n  const handleFileUpload = async (file, type) => {\n    if (!file) return;\n\n    // Vérifier le type de fichier\n    if (!file.type.startsWith('image/')) {\n      setMessage({\n        type: 'error',\n        text: 'Veuillez sélectionner un fichier image valide.'\n      });\n      return;\n    }\n\n    // Vérifier la taille (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setMessage({\n        type: 'error',\n        text: 'La taille du fichier ne doit pas dépasser 5MB.'\n      });\n      return;\n    }\n    setLoading(true);\n    setMessage(null);\n    try {\n      let imageUrl;\n      if (type === 'profile') {\n        imageUrl = await supabaseService.uploadProfileImage(testUserId, file);\n      } else {\n        imageUrl = await supabaseService.uploadFaceImage(testUserId, file);\n      }\n\n      // Ajouter l'image à la liste\n      const newImage = {\n        url: imageUrl,\n        type,\n        uploadedAt: new Date()\n      };\n      setUploadedImages(prev => [...prev, newImage]);\n      setMessage({\n        type: 'success',\n        text: `Image ${type === 'profile' ? 'de profil' : 'faciale'} uploadée avec succès !`\n      });\n    } catch (error) {\n      console.error('Erreur upload:', error);\n      setMessage({\n        type: 'error',\n        text: `Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteImage = async imageUrl => {\n    setLoading(true);\n    try {\n      await supabaseService.deleteImage(imageUrl);\n      setUploadedImages(prev => prev.filter(img => img.url !== imageUrl));\n      setMessage({\n        type: 'success',\n        text: 'Image supprimée avec succès !'\n      });\n    } catch (error) {\n      console.error('Erreur suppression:', error);\n      setMessage({\n        type: 'error',\n        text: `Erreur lors de la suppression: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const FileUploadButton = ({\n    type,\n    icon,\n    label\n  }) => /*#__PURE__*/_jsxDEV(Button, {\n    variant: \"contained\",\n    component: \"label\",\n    startIcon: icon,\n    disabled: loading,\n    sx: {\n      mb: 2,\n      width: '100%'\n    },\n    children: [label, /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      hidden: true,\n      accept: \"image/*\",\n      onChange: e => {\n        var _e$target$files;\n        const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n        if (file) {\n          handleFileUpload(file, type);\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Test Upload Images Supabase\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 3\n      },\n      children: \"Testez l'upload d'images vers le bucket Supabase \\\"images\\\"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: message.type,\n      sx: {\n        mb: 3\n      },\n      onClose: () => setMessage(null),\n      children: message.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Upload d'Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadButton, {\n              type: \"profile\",\n              icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 23\n              }, this),\n              label: \"Upload Image de Profil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadButton, {\n              type: \"face\",\n              icon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 23\n              }, this),\n              label: \"Upload Image Faciale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"center\",\n              mt: 2,\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: [\"Images Upload\\xE9es (\", uploadedImages.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), uploadedImages.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              children: \"Aucune image upload\\xE9e pour le moment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: uploadedImages.map((image, index) => /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 2,\n                  mb: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: image.url,\n                    sx: {\n                      width: 60,\n                      height: 60\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    flex: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      children: image.type === 'profile' ? 'Image de Profil' : 'Image Faciale'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: image.uploadedAt.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    color: \"error\",\n                    startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 38\n                    }, this),\n                    onClick: () => handleDeleteImage(image.url),\n                    disabled: loading,\n                    children: \"Supprimer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Informations Techniques\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          component: \"div\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Bucket Supabase:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), \" images\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 53\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Politiques configur\\xE9es:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 53\n          }, this), \"\\u2022 SELECT: Lecture publique des images\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 50\n          }, this), \"\\u2022 INSERT: Upload pour utilisateurs authentifi\\xE9s\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 60\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Formats accept\\xE9s:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), \" JPG, PNG, GIF, WebP\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 67\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Taille max:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), \" 5MB\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 45\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"ID utilisateur test:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), \" \", testUserId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(SupabaseImageUpload, \"106QDzX3FrQg0TvdKncs7P2AnYc=\");\n_c = SupabaseImageUpload;\nexport default SupabaseImageUpload;\nvar _c;\n$RefreshReg$(_c, \"SupabaseImageUpload\");", "map": {"version": 3, "names": ["React", "useState", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "Avatar", "Grid", "Paper", "Divider", "Person", "PersonIcon", "Face", "FaceIcon", "Delete", "DeleteIcon", "supabaseService", "jsxDEV", "_jsxDEV", "SupabaseImageUpload", "_s", "loading", "setLoading", "message", "setMessage", "uploadedImages", "setUploadedImages", "testUserId", "handleFileUpload", "file", "type", "startsWith", "text", "size", "imageUrl", "uploadProfileImage", "uploadFaceImage", "newImage", "url", "uploadedAt", "Date", "prev", "error", "console", "Error", "handleDeleteImage", "deleteImage", "filter", "img", "FileUploadButton", "icon", "label", "variant", "component", "startIcon", "disabled", "sx", "mb", "width", "children", "hidden", "accept", "onChange", "e", "_e$target$files", "target", "files", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "gutterBottom", "color", "severity", "onClose", "container", "spacing", "item", "xs", "md", "display", "justifyContent", "mt", "length", "map", "image", "index", "alignItems", "gap", "src", "height", "flex", "toLocaleString", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseImageUpload.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Alert,\n  CircularProgress,\n  Avatar,\n  Grid,\n  Paper,\n  Divider\n} from '@mui/material';\nimport {\n  CloudUpload as CloudUploadIcon,\n  Person as PersonIcon,\n  Face as FaceIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { supabaseService } from '../../services/supabaseService';\n\ninterface UploadedImage {\n  url: string;\n  type: 'profile' | 'face';\n  uploadedAt: Date;\n}\n\nconst SupabaseImageUpload: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);\n\n  // Simuler un ID utilisateur pour les tests\n  const testUserId = 'test-user-123';\n\n  const handleFileUpload = async (file: File, type: 'profile' | 'face') => {\n    if (!file) return;\n\n    // Vérifier le type de fichier\n    if (!file.type.startsWith('image/')) {\n      setMessage({ type: 'error', text: 'Veuillez sélectionner un fichier image valide.' });\n      return;\n    }\n\n    // Vérifier la taille (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setMessage({ type: 'error', text: 'La taille du fichier ne doit pas dépasser 5MB.' });\n      return;\n    }\n\n    setLoading(true);\n    setMessage(null);\n\n    try {\n      let imageUrl: string;\n      \n      if (type === 'profile') {\n        imageUrl = await supabaseService.uploadProfileImage(testUserId, file);\n      } else {\n        imageUrl = await supabaseService.uploadFaceImage(testUserId, file);\n      }\n\n      // Ajouter l'image à la liste\n      const newImage: UploadedImage = {\n        url: imageUrl,\n        type,\n        uploadedAt: new Date()\n      };\n\n      setUploadedImages(prev => [...prev, newImage]);\n      setMessage({ \n        type: 'success', \n        text: `Image ${type === 'profile' ? 'de profil' : 'faciale'} uploadée avec succès !` \n      });\n\n    } catch (error) {\n      console.error('Erreur upload:', error);\n      setMessage({ \n        type: 'error', \n        text: `Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}` \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteImage = async (imageUrl: string) => {\n    setLoading(true);\n    try {\n      await supabaseService.deleteImage(imageUrl);\n      setUploadedImages(prev => prev.filter(img => img.url !== imageUrl));\n      setMessage({ type: 'success', text: 'Image supprimée avec succès !' });\n    } catch (error) {\n      console.error('Erreur suppression:', error);\n      setMessage({ \n        type: 'error', \n        text: `Erreur lors de la suppression: ${error instanceof Error ? error.message : 'Erreur inconnue'}` \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const FileUploadButton: React.FC<{ type: 'profile' | 'face'; icon: React.ReactNode; label: string }> = ({ type, icon, label }) => (\n    <Button\n      variant=\"contained\"\n      component=\"label\"\n      startIcon={icon}\n      disabled={loading}\n      sx={{ mb: 2, width: '100%' }}\n    >\n      {label}\n      <input\n        type=\"file\"\n        hidden\n        accept=\"image/*\"\n        onChange={(e) => {\n          const file = e.target.files?.[0];\n          if (file) {\n            handleFileUpload(file, type);\n          }\n        }}\n      />\n    </Button>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Test Upload Images Supabase\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n        Testez l'upload d'images vers le bucket Supabase \"images\"\n      </Typography>\n\n      {/* Messages */}\n      {message && (\n        <Alert severity={message.type} sx={{ mb: 3 }} onClose={() => setMessage(null)}>\n          {message.text}\n        </Alert>\n      )}\n\n      <Grid container spacing={3}>\n        {/* Section Upload */}\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Upload d'Images\n              </Typography>\n              \n              <FileUploadButton\n                type=\"profile\"\n                icon={<PersonIcon />}\n                label=\"Upload Image de Profil\"\n              />\n              \n              <FileUploadButton\n                type=\"face\"\n                icon={<FaceIcon />}\n                label=\"Upload Image Faciale\"\n              />\n\n              {loading && (\n                <Box display=\"flex\" justifyContent=\"center\" mt={2}>\n                  <CircularProgress />\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Section Images Uploadées */}\n        <Grid xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Images Uploadées ({uploadedImages.length})\n              </Typography>\n              \n              {uploadedImages.length === 0 ? (\n                <Typography color=\"text.secondary\">\n                  Aucune image uploadée pour le moment\n                </Typography>\n              ) : (\n                <Box>\n                  {uploadedImages.map((image, index) => (\n                    <Paper key={index} sx={{ p: 2, mb: 2 }}>\n                      <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                        <Avatar\n                          src={image.url}\n                          sx={{ width: 60, height: 60 }}\n                        />\n                        <Box flex={1}>\n                          <Typography variant=\"subtitle2\">\n                            {image.type === 'profile' ? 'Image de Profil' : 'Image Faciale'}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {image.uploadedAt.toLocaleString()}\n                          </Typography>\n                        </Box>\n                        <Button\n                          size=\"small\"\n                          color=\"error\"\n                          startIcon={<DeleteIcon />}\n                          onClick={() => handleDeleteImage(image.url)}\n                          disabled={loading}\n                        >\n                          Supprimer\n                        </Button>\n                      </Box>\n                    </Paper>\n                  ))}\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Informations techniques */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Informations Techniques\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <Typography variant=\"body2\" component=\"div\">\n            <strong>Bucket Supabase:</strong> images<br />\n            <strong>Politiques configurées:</strong><br />\n            • SELECT: Lecture publique des images<br />\n            • INSERT: Upload pour utilisateurs authentifiés<br />\n            <strong>Formats acceptés:</strong> JPG, PNG, GIF, WebP<br />\n            <strong>Taille max:</strong> 5MB<br />\n            <strong>ID utilisateur test:</strong> {testUserId}\n          </Typography>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default SupabaseImageUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SAEEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,eAAe,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQjE,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAqD,IAAI,CAAC;EAChG,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAkB,EAAE,CAAC;;EAEzE;EACA,MAAM6B,UAAU,GAAG,eAAe;EAElC,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,IAAU,EAAEC,IAAwB,KAAK;IACvE,IAAI,CAACD,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnCP,UAAU,CAAC;QAAEM,IAAI,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAiD,CAAC,CAAC;MACrF;IACF;;IAEA;IACA,IAAIH,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BT,UAAU,CAAC;QAAEM,IAAI,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAiD,CAAC,CAAC;MACrF;IACF;IAEAV,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAIU,QAAgB;MAEpB,IAAIJ,IAAI,KAAK,SAAS,EAAE;QACtBI,QAAQ,GAAG,MAAMlB,eAAe,CAACmB,kBAAkB,CAACR,UAAU,EAAEE,IAAI,CAAC;MACvE,CAAC,MAAM;QACLK,QAAQ,GAAG,MAAMlB,eAAe,CAACoB,eAAe,CAACT,UAAU,EAAEE,IAAI,CAAC;MACpE;;MAEA;MACA,MAAMQ,QAAuB,GAAG;QAC9BC,GAAG,EAAEJ,QAAQ;QACbJ,IAAI;QACJS,UAAU,EAAE,IAAIC,IAAI,CAAC;MACvB,CAAC;MAEDd,iBAAiB,CAACe,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,QAAQ,CAAC,CAAC;MAC9Cb,UAAU,CAAC;QACTM,IAAI,EAAE,SAAS;QACfE,IAAI,EAAE,SAASF,IAAI,KAAK,SAAS,GAAG,WAAW,GAAG,SAAS;MAC7D,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtClB,UAAU,CAAC;QACTM,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,4BAA4BU,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACnB,OAAO,GAAG,iBAAiB;MAC9F,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,iBAAiB,GAAG,MAAOX,QAAgB,IAAK;IACpDZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMN,eAAe,CAAC8B,WAAW,CAACZ,QAAQ,CAAC;MAC3CR,iBAAiB,CAACe,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACV,GAAG,KAAKJ,QAAQ,CAAC,CAAC;MACnEV,UAAU,CAAC;QAAEM,IAAI,EAAE,SAAS;QAAEE,IAAI,EAAE;MAAgC,CAAC,CAAC;IACxE,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3ClB,UAAU,CAAC;QACTM,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,kCAAkCU,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACnB,OAAO,GAAG,iBAAiB;MACpG,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,gBAA8F,GAAGA,CAAC;IAAEnB,IAAI;IAAEoB,IAAI;IAAEC;EAAM,CAAC,kBAC3HjC,OAAA,CAAClB,MAAM;IACLoD,OAAO,EAAC,WAAW;IACnBC,SAAS,EAAC,OAAO;IACjBC,SAAS,EAAEJ,IAAK;IAChBK,QAAQ,EAAElC,OAAQ;IAClBmC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,GAE5BR,KAAK,eACNjC,OAAA;MACEY,IAAI,EAAC,MAAM;MACX8B,MAAM;MACNC,MAAM,EAAC,SAAS;MAChBC,QAAQ,EAAGC,CAAC,IAAK;QAAA,IAAAC,eAAA;QACf,MAAMnC,IAAI,IAAAmC,eAAA,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;QAChC,IAAInC,IAAI,EAAE;UACRD,gBAAgB,CAACC,IAAI,EAAEC,IAAI,CAAC;QAC9B;MACF;IAAE;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACT;EAED,oBACEpD,OAAA,CAACnB,GAAG;IAACyD,EAAE,EAAE;MAAEe,CAAC,EAAE;IAAE,CAAE;IAAAZ,QAAA,gBAChBzC,OAAA,CAACf,UAAU;MAACiD,OAAO,EAAC,IAAI;MAACoB,YAAY;MAAAb,QAAA,EAAC;IAEtC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbpD,OAAA,CAACf,UAAU;MAACiD,OAAO,EAAC,OAAO;MAACqB,KAAK,EAAC,gBAAgB;MAACjB,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,EAAC;IAElE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAGZ/C,OAAO,iBACNL,OAAA,CAACd,KAAK;MAACsE,QAAQ,EAAEnD,OAAO,CAACO,IAAK;MAAC0B,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAACkB,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC,IAAI,CAAE;MAAAmC,QAAA,EAC3EpC,OAAO,CAACS;IAAI;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,eAEDpD,OAAA,CAACX,IAAI;MAACqE,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,gBAEzBzC,OAAA,CAACX,IAAI;QAACuE,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACvBzC,OAAA,CAACjB,IAAI;UAAA0D,QAAA,eACHzC,OAAA,CAAChB,WAAW;YAAAyD,QAAA,gBACVzC,OAAA,CAACf,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACoB,YAAY;cAAAb,QAAA,EAAC;YAEtC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbpD,OAAA,CAAC+B,gBAAgB;cACfnB,IAAI,EAAC,SAAS;cACdoB,IAAI,eAAEhC,OAAA,CAACP,UAAU;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBnB,KAAK,EAAC;YAAwB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eAEFpD,OAAA,CAAC+B,gBAAgB;cACfnB,IAAI,EAAC,MAAM;cACXoB,IAAI,eAAEhC,OAAA,CAACL,QAAQ;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBnB,KAAK,EAAC;YAAsB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,EAEDjD,OAAO,iBACNH,OAAA,CAACnB,GAAG;cAACkF,OAAO,EAAC,MAAM;cAACC,cAAc,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAxB,QAAA,eAChDzC,OAAA,CAACb,gBAAgB;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPpD,OAAA,CAACX,IAAI;QAACwE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAClBzC,OAAA,CAACjB,IAAI;UAAA0D,QAAA,eACHzC,OAAA,CAAChB,WAAW;YAAAyD,QAAA,gBACVzC,OAAA,CAACf,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACoB,YAAY;cAAAb,QAAA,GAAC,uBAClB,EAAClC,cAAc,CAAC2D,MAAM,EAAC,GAC3C;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZ7C,cAAc,CAAC2D,MAAM,KAAK,CAAC,gBAC1BlE,OAAA,CAACf,UAAU;cAACsE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,EAAC;YAEnC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,gBAEbpD,OAAA,CAACnB,GAAG;cAAA4D,QAAA,EACDlC,cAAc,CAAC4D,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/BrE,OAAA,CAACV,KAAK;gBAAagD,EAAE,EAAE;kBAAEe,CAAC,EAAE,CAAC;kBAAEd,EAAE,EAAE;gBAAE,CAAE;gBAAAE,QAAA,eACrCzC,OAAA,CAACnB,GAAG;kBAACkF,OAAO,EAAC,MAAM;kBAACO,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAA9B,QAAA,gBAC7CzC,OAAA,CAACZ,MAAM;oBACLoF,GAAG,EAAEJ,KAAK,CAAChD,GAAI;oBACfkB,EAAE,EAAE;sBAAEE,KAAK,EAAE,EAAE;sBAAEiC,MAAM,EAAE;oBAAG;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFpD,OAAA,CAACnB,GAAG;oBAAC6F,IAAI,EAAE,CAAE;oBAAAjC,QAAA,gBACXzC,OAAA,CAACf,UAAU;sBAACiD,OAAO,EAAC,WAAW;sBAAAO,QAAA,EAC5B2B,KAAK,CAACxD,IAAI,KAAK,SAAS,GAAG,iBAAiB,GAAG;oBAAe;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACbpD,OAAA,CAACf,UAAU;sBAACiD,OAAO,EAAC,SAAS;sBAACqB,KAAK,EAAC,gBAAgB;sBAAAd,QAAA,EACjD2B,KAAK,CAAC/C,UAAU,CAACsD,cAAc,CAAC;oBAAC;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNpD,OAAA,CAAClB,MAAM;oBACLiC,IAAI,EAAC,OAAO;oBACZwC,KAAK,EAAC,OAAO;oBACbnB,SAAS,eAAEpC,OAAA,CAACH,UAAU;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1BwB,OAAO,EAAEA,CAAA,KAAMjD,iBAAiB,CAACyC,KAAK,CAAChD,GAAG,CAAE;oBAC5CiB,QAAQ,EAAElC,OAAQ;oBAAAsC,QAAA,EACnB;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC,GAvBIiB,KAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPpD,OAAA,CAACjB,IAAI;MAACuD,EAAE,EAAE;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAAxB,QAAA,eAClBzC,OAAA,CAAChB,WAAW;QAAAyD,QAAA,gBACVzC,OAAA,CAACf,UAAU;UAACiD,OAAO,EAAC,IAAI;UAACoB,YAAY;UAAAb,QAAA,EAAC;QAEtC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpD,OAAA,CAACT,OAAO;UAAC+C,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BpD,OAAA,CAACf,UAAU;UAACiD,OAAO,EAAC,OAAO;UAACC,SAAS,EAAC,KAAK;UAAAM,QAAA,gBACzCzC,OAAA;YAAAyC,QAAA,EAAQ;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,WAAO,eAAApD,OAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CpD,OAAA;YAAAyC,QAAA,EAAQ;UAAuB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAAApD,OAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACT,eAAApD,OAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,2DACI,eAAApD,OAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrDpD,OAAA;YAAAyC,QAAA,EAAQ;UAAiB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,wBAAoB,eAAApD,OAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5DpD,OAAA;YAAAyC,QAAA,EAAQ;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,QAAI,eAAApD,OAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCpD,OAAA;YAAAyC,QAAA,EAAQ;UAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC3C,UAAU;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClD,EAAA,CAtNID,mBAA6B;AAAA4E,EAAA,GAA7B5E,mBAA6B;AAwNnC,eAAeA,mBAAmB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}