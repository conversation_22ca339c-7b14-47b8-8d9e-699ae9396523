{"ast": null, "code": "import { CELL_SIZE } from './config';\nexport function pyramidDown(minFaceSize, scaleFactor, dims) {\n  var height = dims[0],\n    width = dims[1];\n  var m = CELL_SIZE / minFaceSize;\n  var scales = [];\n  var minLayer = Math.min(height, width) * m;\n  var exp = 0;\n  while (minLayer >= 12) {\n    scales.push(m * Math.pow(scaleFactor, exp));\n    minLayer = minLayer * scaleFactor;\n    exp += 1;\n  }\n  return scales;\n}", "map": {"version": 3, "names": ["CELL_SIZE", "pyramidDown", "minFaceSize", "scaleFactor", "dims", "height", "width", "m", "scales", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "exp", "push", "pow"], "sources": ["../../../src/mtcnn/pyramidDown.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,SAAS,QAAQ,UAAU;AAEpC,OAAM,SAAUC,WAAWA,CAACC,WAAmB,EAAEC,WAAmB,EAAEC,IAAc;EAE3E,IAAAC,MAAA,GAAAD,IAAA,GAAM;IAAEE,KAAA,GAAAF,IAAA,GAAK;EACpB,IAAMG,CAAC,GAAGP,SAAS,GAAGE,WAAW;EAEjC,IAAMM,MAAM,GAAG,EAAE;EAEjB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACN,MAAM,EAAEC,KAAK,CAAC,GAAGC,CAAC;EAC1C,IAAIK,GAAG,GAAG,CAAC;EACX,OAAOH,QAAQ,IAAI,EAAE,EAAE;IACrBD,MAAM,CAACK,IAAI,CAACN,CAAC,GAAGG,IAAI,CAACI,GAAG,CAACX,WAAW,EAAES,GAAG,CAAC,CAAC;IAC3CH,QAAQ,GAAGA,QAAQ,GAAGN,WAAW;IACjCS,GAAG,IAAI,CAAC;;EAGV,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}