{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { TinyFaceFeatureExtractor } from '../faceFeatureExtractor/TinyFaceFeatureExtractor';\nimport { FaceLandmark68NetBase } from './FaceLandmark68NetBase';\nvar FaceLandmark68TinyNet = /** @class */function (_super) {\n  __extends(FaceLandmark68TinyNet, _super);\n  function FaceLandmark68TinyNet(faceFeatureExtractor) {\n    if (faceFeatureExtractor === void 0) {\n      faceFeatureExtractor = new TinyFaceFeatureExtractor();\n    }\n    return _super.call(this, 'FaceLandmark68TinyNet', faceFeatureExtractor) || this;\n  }\n  FaceLandmark68TinyNet.prototype.getDefaultModelName = function () {\n    return 'face_landmark_68_tiny_model';\n  };\n  FaceLandmark68TinyNet.prototype.getClassifierChannelsIn = function () {\n    return 128;\n  };\n  return FaceLandmark68TinyNet;\n}(FaceLandmark68NetBase);\nexport { FaceLandmark68TinyNet };", "map": {"version": 3, "names": ["TinyFaceFeatureExtractor", "FaceLandmark68NetBase", "FaceLandmark68TinyNet", "_super", "__extends", "faceFeatureExtractor", "call", "prototype", "getDefaultModelName", "getClassifierChannelsIn"], "sources": ["../../../src/faceLandmarkNet/FaceLandmark68TinyNet.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,wBAAwB,QAAQ,kDAAkD;AAE3F,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,IAAAC,qBAAA,0BAAAC,MAAA;EAA2CC,SAAA,CAAAF,qBAAA,EAAAC,MAAA;EAEzC,SAAAD,sBAAYG,oBAA+E;IAA/E,IAAAA,oBAAA;MAAAA,oBAAA,OAAqDL,wBAAwB,EAAE;IAAA;WACzFG,MAAA,CAAAG,IAAA,OAAM,uBAAuB,EAAED,oBAAoB,CAAC;EACtD;EAEUH,qBAAA,CAAAK,SAAA,CAAAC,mBAAmB,GAA7B;IACE,OAAO,6BAA6B;EACtC,CAAC;EAESN,qBAAA,CAAAK,SAAA,CAAAE,uBAAuB,GAAjC;IACE,OAAO,GAAG;EACZ,CAAC;EACH,OAAAP,qBAAC;AAAD,CAAC,CAb0CD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}