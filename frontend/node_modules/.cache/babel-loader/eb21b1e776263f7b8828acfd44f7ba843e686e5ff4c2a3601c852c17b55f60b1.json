{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function pointwiseConvLayer(x, params, strides) {\n  return tf.tidy(function () {\n    var out = tf.conv2d(x, params.filters, strides, 'same');\n    out = tf.add(out, params.batch_norm_offset);\n    return tf.clipByValue(out, 0, 6);\n  });\n}", "map": {"version": 3, "names": ["tf", "pointwiseConv<PERSON><PERSON>er", "x", "params", "strides", "tidy", "out", "conv2d", "filters", "add", "batch_norm_offset", "clipByValue"], "sources": ["../../../src/ssdMobilenetv1/pointwiseConvLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAI3C,OAAM,SAAUC,kBAAkBA,CAChCC,CAAc,EACdC,MAA2B,EAC3BC,OAAyB;EAEzB,OAAOJ,EAAE,CAACK,IAAI,CAAC;IAEb,IAAIC,GAAG,GAAGN,EAAE,CAACO,MAAM,CAACL,CAAC,EAAEC,MAAM,CAACK,OAAO,EAAEJ,OAAO,EAAE,MAAM,CAAC;IACvDE,GAAG,GAAGN,EAAE,CAACS,GAAG,CAACH,GAAG,EAAEH,MAAM,CAACO,iBAAiB,CAAC;IAC3C,OAAOV,EAAE,CAACW,WAAW,CAACL,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAElC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}