{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function depthwiseSeparableConv(x, params, stride) {\n  return tf.tidy(function () {\n    var out = tf.separableConv2d(x, params.depthwise_filter, params.pointwise_filter, stride, 'same');\n    out = tf.add(out, params.bias);\n    return out;\n  });\n}", "map": {"version": 3, "names": ["tf", "depthwiseSeparableConv", "x", "params", "stride", "tidy", "out", "separableConv2d", "depthwise_filter", "pointwise_filter", "add", "bias"], "sources": ["../../../src/common/depthwiseSeparableConv.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAI3C,OAAM,SAAUC,sBAAsBA,CACpCC,CAAc,EACdC,MAA2B,EAC3BC,MAAwB;EAExB,OAAOJ,EAAE,CAACK,IAAI,CAAC;IACb,IAAIC,GAAG,GAAGN,EAAE,CAACO,eAAe,CAACL,CAAC,EAAEC,MAAM,CAACK,gBAAgB,EAAEL,MAAM,CAACM,gBAAgB,EAAEL,MAAM,EAAE,MAAM,CAAC;IACjGE,GAAG,GAAGN,EAAE,CAACU,GAAG,CAACJ,GAAG,EAAEH,MAAM,CAACQ,IAAI,CAAC;IAC9B,OAAOL,GAAG;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}