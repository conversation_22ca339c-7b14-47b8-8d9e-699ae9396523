{"ast": null, "code": "import { FaceExpressions } from '../faceExpressionNet/FaceExpressions';\nexport function isWithFaceExpressions(obj) {\n  return obj['expressions'] instanceof FaceExpressions;\n}\nexport function extendWithFaceExpressions(sourceObj, expressions) {\n  var extension = {\n    expressions: expressions\n  };\n  return Object.assign({}, sourceObj, extension);\n}", "map": {"version": 3, "names": ["FaceExpressions", "isWithFaceExpressions", "obj", "extendWithFaceExpressions", "sourceObj", "expressions", "extension", "Object", "assign"], "sources": ["../../../src/factories/WithFaceExpressions.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,eAAe,QAAQ,sCAAsC;AAMtE,OAAM,SAAUC,qBAAqBA,CAACC,GAAQ;EAC5C,OAAOA,GAAG,CAAC,aAAa,CAAC,YAAYF,eAAe;AACtD;AAEA,OAAM,SAAUG,yBAAyBA,CAGvCC,SAAkB,EAClBC,WAA4B;EAG5B,IAAMC,SAAS,GAAG;IAAED,WAAW,EAAAA;EAAA,CAAE;EACjC,OAAOE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEJ,SAAS,EAAEE,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}