{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { TinyYolov2Options } from '../tinyYolov2';\nvar TinyFaceDetectorOptions = /** @class */function (_super) {\n  __extends(TinyFaceDetectorOptions, _super);\n  function TinyFaceDetectorOptions() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._name = 'TinyFaceDetectorOptions';\n    return _this;\n  }\n  return TinyFaceDetectorOptions;\n}(TinyYolov2Options);\nexport { TinyFaceDetectorOptions };", "map": {"version": 3, "names": ["TinyYolov2Options", "TinyFaceDetectorOptions", "_super", "__extends", "_this", "apply", "arguments", "_name"], "sources": ["../../../src/tinyFaceDetector/TinyFaceDetectorOptions.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAA6BA,iBAAiB,QAAQ,eAAe;AAIrE,IAAAC,uBAAA,0BAAAC,MAAA;EAA6CC,SAAA,CAAAF,uBAAA,EAAAC,MAAA;EAA7C,SAAAD,wBAAA;IAAA,IAAAG,KAAA,GAAAF,MAAA,aAAAA,MAAA,CAAAG,KAAA,OAAAC,SAAA;IACYF,KAAA,CAAAG,KAAK,GAAW,yBAAyB;;EACrD;EAAA,OAAAN,uBAAC;AAAD,CAAC,CAF4CD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}