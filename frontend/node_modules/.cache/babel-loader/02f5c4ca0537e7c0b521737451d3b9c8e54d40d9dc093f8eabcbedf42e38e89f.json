{"ast": null, "code": "var Symbol = require('./_Symbol');\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n  symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\nmodule.exports = cloneSymbol;", "map": {"version": 3, "names": ["Symbol", "require", "symbol<PERSON>roto", "prototype", "undefined", "symbolValueOf", "valueOf", "cloneSymbol", "symbol", "Object", "call", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_cloneSymbol.js"], "sourcesContent": ["var Symbol = require('./_Symbol');\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n\nmodule.exports = cloneSymbol;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;;AAEjC;AACA,IAAIC,WAAW,GAAGF,MAAM,GAAGA,MAAM,CAACG,SAAS,GAAGC,SAAS;EACnDC,aAAa,GAAGH,WAAW,GAAGA,WAAW,CAACI,OAAO,GAAGF,SAAS;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACC,MAAM,EAAE;EAC3B,OAAOH,aAAa,GAAGI,MAAM,CAACJ,aAAa,CAACK,IAAI,CAACF,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AAChE;AAEAG,MAAM,CAACC,OAAO,GAAGL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}