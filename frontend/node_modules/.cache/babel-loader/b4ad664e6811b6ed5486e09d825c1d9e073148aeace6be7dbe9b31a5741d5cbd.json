{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { fetchOrThrow } from './fetchOrThrow';\nexport function fetchNetWeights(uri) {\n  return __awaiter(this, void 0, void 0, function () {\n    var _a;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          _a = Float32Array.bind;\n          return [4 /*yield*/, fetchOrThrow(uri)];\n        case 1:\n          return [4 /*yield*/, _b.sent().arrayBuffer()];\n        case 2:\n          return [2 /*return*/, new (_a.apply(Float32Array, [void 0, _b.sent()]))()];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["fetchOrThrow", "fetchNetWeights", "uri", "Float32Array", "bind", "_b", "sent", "arrayBuffer", "_a", "apply"], "sources": ["../../../src/dom/fetchNetWeights.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAE7C,OAAM,SAAgBC,eAAeA,CAACC,GAAW;;;;;;eACpCC,YAAY,CAAAC,IAAA;UAAQ,qBAAMJ,YAAY,CAACE,GAAG,CAAC;;UAA9B,qBAAOG,EAAA,CAAAC,IAAA,EAAuB,CAAEC,WAAW,EAAE;;UAArE,sBAAO,KAAAC,EAAA,CAAAC,KAAA,CAAIN,YAAY,WAACE,EAAA,CAAAC,IAAA,EAA6C,KAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}