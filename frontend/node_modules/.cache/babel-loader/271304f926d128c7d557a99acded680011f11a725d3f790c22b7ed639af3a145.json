{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { leaky } from './leaky';\nexport function convWithBatchNorm(x, params) {\n  return tf.tidy(function () {\n    var out = tf.pad(x, [[0, 0], [1, 1], [1, 1], [0, 0]]);\n    out = tf.conv2d(out, params.conv.filters, [1, 1], 'valid');\n    out = tf.sub(out, params.bn.sub);\n    out = tf.mul(out, params.bn.truediv);\n    out = tf.add(out, params.conv.bias);\n    return leaky(out);\n  });\n}", "map": {"version": 3, "names": ["tf", "leaky", "convWithBatchNorm", "x", "params", "tidy", "out", "pad", "conv2d", "conv", "filters", "sub", "bn", "mul", "truediv", "add", "bias"], "sources": ["../../../src/tinyYolov2/convWithBatchNorm.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,KAAK,QAAQ,SAAS;AAG/B,OAAM,SAAUC,iBAAiBA,CAACC,CAAc,EAAEC,MAAyB;EACzE,OAAOJ,EAAE,CAACK,IAAI,CAAC;IACb,IAAIC,GAAG,GAAGN,EAAE,CAACO,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAgB;IAEpEG,GAAG,GAAGN,EAAE,CAACQ,MAAM,CAACF,GAAG,EAAEF,MAAM,CAACK,IAAI,CAACC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;IAC1DJ,GAAG,GAAGN,EAAE,CAACW,GAAG,CAACL,GAAG,EAAEF,MAAM,CAACQ,EAAE,CAACD,GAAG,CAAC;IAChCL,GAAG,GAAGN,EAAE,CAACa,GAAG,CAACP,GAAG,EAAEF,MAAM,CAACQ,EAAE,CAACE,OAAO,CAAC;IACpCR,GAAG,GAAGN,EAAE,CAACe,GAAG,CAACT,GAAG,EAAEF,MAAM,CAACK,IAAI,CAACO,IAAI,CAAC;IAEnC,OAAOf,KAAK,CAACK,GAAG,CAAC;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}