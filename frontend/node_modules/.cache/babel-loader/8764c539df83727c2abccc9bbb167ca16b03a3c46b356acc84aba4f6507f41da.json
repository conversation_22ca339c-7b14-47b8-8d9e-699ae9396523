{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { extendWithAge } from '../factories/WithAge';\nimport { extendWithGender } from '../factories/WithGender';\nimport { ComposableTask } from './ComposableTask';\nimport { ComputeAllFaceDescriptorsTask, ComputeSingleFaceDescriptorTask } from './ComputeFaceDescriptorsTasks';\nimport { extractAllFacesAndComputeResults, extractSingleFaceAndComputeResult } from './extractFacesAndComputeResults';\nimport { nets } from './nets';\nimport { PredictAllFaceExpressionsTask, PredictAllFaceExpressionsWithFaceAlignmentTask, PredictSingleFaceExpressionsTask, PredictSingleFaceExpressionsWithFaceAlignmentTask } from './PredictFaceExpressionsTask';\nvar PredictAgeAndGenderTaskBase = /** @class */function (_super) {\n  __extends(PredictAgeAndGenderTaskBase, _super);\n  function PredictAgeAndGenderTaskBase(parentTask, input, extractedFaces) {\n    var _this = _super.call(this) || this;\n    _this.parentTask = parentTask;\n    _this.input = input;\n    _this.extractedFaces = extractedFaces;\n    return _this;\n  }\n  return PredictAgeAndGenderTaskBase;\n}(ComposableTask);\nexport { PredictAgeAndGenderTaskBase };\nvar PredictAllAgeAndGenderTask = /** @class */function (_super) {\n  __extends(PredictAllAgeAndGenderTask, _super);\n  function PredictAllAgeAndGenderTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictAllAgeAndGenderTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResults, ageAndGenderByFace;\n      var _this = this;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResults = _a.sent();\n            return [4 /*yield*/, extractAllFacesAndComputeResults(parentResults, this.input, function (faces) {\n              return __awaiter(_this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                  switch (_a.label) {\n                    case 0:\n                      return [4 /*yield*/, Promise.all(faces.map(function (face) {\n                        return nets.ageGenderNet.predictAgeAndGender(face);\n                      }))];\n                    case 1:\n                      return [2 /*return*/, _a.sent()];\n                  }\n                });\n              });\n            }, this.extractedFaces)];\n          case 2:\n            ageAndGenderByFace = _a.sent();\n            return [2 /*return*/, parentResults.map(function (parentResult, i) {\n              var _a = ageAndGenderByFace[i],\n                age = _a.age,\n                gender = _a.gender,\n                genderProbability = _a.genderProbability;\n              return extendWithAge(extendWithGender(parentResult, gender, genderProbability), age);\n            })];\n        }\n      });\n    });\n  };\n  PredictAllAgeAndGenderTask.prototype.withFaceExpressions = function () {\n    return new PredictAllFaceExpressionsTask(this, this.input);\n  };\n  return PredictAllAgeAndGenderTask;\n}(PredictAgeAndGenderTaskBase);\nexport { PredictAllAgeAndGenderTask };\nvar PredictSingleAgeAndGenderTask = /** @class */function (_super) {\n  __extends(PredictSingleAgeAndGenderTask, _super);\n  function PredictSingleAgeAndGenderTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictSingleAgeAndGenderTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResult, _a, age, gender, genderProbability;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResult = _b.sent();\n            if (!parentResult) {\n              return [2 /*return*/];\n            }\n            return [4 /*yield*/, extractSingleFaceAndComputeResult(parentResult, this.input, function (face) {\n              return nets.ageGenderNet.predictAgeAndGender(face);\n            }, this.extractedFaces)];\n          case 2:\n            _a = _b.sent(), age = _a.age, gender = _a.gender, genderProbability = _a.genderProbability;\n            return [2 /*return*/, extendWithAge(extendWithGender(parentResult, gender, genderProbability), age)];\n        }\n      });\n    });\n  };\n  PredictSingleAgeAndGenderTask.prototype.withFaceExpressions = function () {\n    return new PredictSingleFaceExpressionsTask(this, this.input);\n  };\n  return PredictSingleAgeAndGenderTask;\n}(PredictAgeAndGenderTaskBase);\nexport { PredictSingleAgeAndGenderTask };\nvar PredictAllAgeAndGenderWithFaceAlignmentTask = /** @class */function (_super) {\n  __extends(PredictAllAgeAndGenderWithFaceAlignmentTask, _super);\n  function PredictAllAgeAndGenderWithFaceAlignmentTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictAllAgeAndGenderWithFaceAlignmentTask.prototype.withFaceExpressions = function () {\n    return new PredictAllFaceExpressionsWithFaceAlignmentTask(this, this.input);\n  };\n  PredictAllAgeAndGenderWithFaceAlignmentTask.prototype.withFaceDescriptors = function () {\n    return new ComputeAllFaceDescriptorsTask(this, this.input);\n  };\n  return PredictAllAgeAndGenderWithFaceAlignmentTask;\n}(PredictAllAgeAndGenderTask);\nexport { PredictAllAgeAndGenderWithFaceAlignmentTask };\nvar PredictSingleAgeAndGenderWithFaceAlignmentTask = /** @class */function (_super) {\n  __extends(PredictSingleAgeAndGenderWithFaceAlignmentTask, _super);\n  function PredictSingleAgeAndGenderWithFaceAlignmentTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictSingleAgeAndGenderWithFaceAlignmentTask.prototype.withFaceExpressions = function () {\n    return new PredictSingleFaceExpressionsWithFaceAlignmentTask(this, this.input);\n  };\n  PredictSingleAgeAndGenderWithFaceAlignmentTask.prototype.withFaceDescriptor = function () {\n    return new ComputeSingleFaceDescriptorTask(this, this.input);\n  };\n  return PredictSingleAgeAndGenderWithFaceAlignmentTask;\n}(PredictSingleAgeAndGenderTask);\nexport { PredictSingleAgeAndGenderWithFaceAlignmentTask };", "map": {"version": 3, "names": ["extendWithAge", "extendWithGender", "ComposableTask", "ComputeAllFaceDescriptorsTask", "ComputeSingleFaceDescriptorTask", "extractAllFacesAndComputeResults", "extractSingleFaceAndComputeResult", "nets", "PredictAllFaceExpressionsTask", "PredictAllFaceExpressionsWithFaceAlignmentTask", "PredictSingleFaceExpressionsTask", "PredictSingleFaceExpressionsWithFaceAlignmentTask", "PredictAgeAndGenderTaskBase", "_super", "__extends", "parentTask", "input", "extractedFaces", "_this", "call", "PredictAllAgeAndGenderTask", "prototype", "run", "parentResults", "_a", "sent", "faces", "__awaiter", "Promise", "all", "map", "face", "ageGenderNet", "predictAgeAndGender", "ageAndGenderByFace", "parentResult", "i", "age", "gender", "genderProbability", "withFaceExpressions", "PredictSingleAgeAndGenderTask", "_b", "PredictAllAgeAndGenderWithFaceAlignmentTask", "withFaceDescriptors", "PredictSingleAgeAndGenderWithFaceAlignmentTask", "withFaceDescriptor"], "sources": ["../../../src/globalApi/PredictAgeAndGenderTask.ts"], "sourcesContent": [null], "mappings": ";AAIA,SAASA,aAAa,QAAiB,sBAAsB;AAG7D,SAASC,gBAAgB,QAAoB,yBAAyB;AACtE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,6BAA6B,EAAEC,+BAA+B,QAAQ,+BAA+B;AAC9G,SAASC,gCAAgC,EAAEC,iCAAiC,QAAQ,iCAAiC;AACrH,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SACEC,6BAA6B,EAC7BC,8CAA8C,EAC9CC,gCAAgC,EAChCC,iDAAiD,QAC5C,8BAA8B;AAErC,IAAAC,2BAAA,0BAAAC,MAAA;EAAyEC,SAAA,CAAAF,2BAAA,EAAAC,MAAA;EACvE,SAAAD,4BACYG,UAAkE,EAClEC,KAAgB,EAChBC,cAAuD;IAHnE,IAAAC,KAAA,GAKEL,MAAA,CAAAM,IAAA,MAAO;IAJGD,KAAA,CAAAH,UAAU,GAAVA,UAAU;IACVG,KAAA,CAAAF,KAAK,GAALA,KAAK;IACLE,KAAA,CAAAD,cAAc,GAAdA,cAAc;;EAG1B;EACF,OAAAL,2BAAC;AAAD,CAAC,CARwEV,cAAc;;AAUvF,IAAAkB,0BAAA,0BAAAP,MAAA;EAEUC,SAAA,CAAAM,0BAAA,EAAAP,MAAA;EAFV,SAAAO,2BAAA;;EA0BA;EAtBeA,0BAAA,CAAAC,SAAA,CAAAC,GAAG,GAAhB;;;;;;;YAEwB,qBAAM,IAAI,CAACP,UAAU;;YAArCQ,aAAa,GAAGC,EAAA,CAAAC,IAAA,EAAqB;YAEhB,qBAAMpB,gCAAgC,CAC/DkB,aAAa,EACb,IAAI,CAACP,KAAK,EACV,UAAMU,KAAK;cAAA,OAAAC,SAAA,CAAAT,KAAA;;;;sBAAI,qBAAMU,OAAO,CAACC,GAAG,CAACH,KAAK,CAACI,GAAG,CACxC,UAAAC,IAAI;wBAAI,OAAAxB,IAAI,CAACyB,YAAY,CAACC,mBAAmB,CAACF,IAAI,CAAoC;sBAA9E,CAA8E,CACvF,CAAC;;sBAFa,sBAAAP,EAAA,CAAAC,IAAA,EAEb;;;;aAAA,EACF,IAAI,CAACR,cAAc,CACpB;;YAPKiB,kBAAkB,GAAGV,EAAA,CAAAC,IAAA,EAO1B;YAED,sBAAOF,aAAa,CAACO,GAAG,CAAC,UAACK,YAAY,EAAEC,CAAC;cACjC,IAAAZ,EAAA,GAAAU,kBAAA,CAAAE,CAAA,CAA0D;gBAAxDC,GAAA,GAAAb,EAAA,CAAAa,GAAG;gBAAEC,MAAA,GAAAd,EAAA,CAAAc,MAAM;gBAAEC,iBAAA,GAAAf,EAAA,CAAAe,iBAA2C;cAChE,OAAOvC,aAAa,CAACC,gBAAgB,CAACkC,YAAY,EAAEG,MAAM,EAAEC,iBAAiB,CAAC,EAAEF,GAAG,CAAC;YACtF,CAAC,CAAC;;;;GACH;EAEDjB,0BAAA,CAAAC,SAAA,CAAAmB,mBAAmB,GAAnB;IACE,OAAO,IAAIhC,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAACQ,KAAK,CAAC;EAC5D,CAAC;EACH,OAAAI,0BAAC;AAAD,CAAC,CAxBSR,2BAA2B;;AA0BrC,IAAA6B,6BAAA,0BAAA5B,MAAA;EAEWC,SAAA,CAAA2B,6BAAA,EAAA5B,MAAA;EAFX,SAAA4B,8BAAA;;EAwBA;EApBeA,6BAAA,CAAApB,SAAA,CAAAC,GAAG,GAAhB;;;;;;YAEuB,qBAAM,IAAI,CAACP,UAAU;;YAApCoB,YAAY,GAAGO,EAAA,CAAAjB,IAAA,EAAqB;YAC1C,IAAI,CAACU,YAAY,EAAE;cACjB;;YAGyC,qBAAM7B,iCAAiC,CAChF6B,YAAY,EACZ,IAAI,CAACnB,KAAK,EACV,UAAAe,IAAI;cAAI,OAAAxB,IAAI,CAACyB,YAAY,CAACC,mBAAmB,CAACF,IAAI,CAAoC;YAA9E,CAA8E,EACtF,IAAI,CAACd,cAAc,CACpB;;YALKO,EAAA,GAAqCkB,EAAA,CAAAjB,IAAA,EAK1C,EALOY,GAAG,GAAAb,EAAA,CAAAa,GAAA,EAAEC,MAAM,GAAAd,EAAA,CAAAc,MAAA,EAAEC,iBAAiB,GAAAf,EAAA,CAAAe,iBAAA;YAOtC,sBAAOvC,aAAa,CAACC,gBAAgB,CAACkC,YAAY,EAAEG,MAAM,EAAEC,iBAAiB,CAAC,EAAEF,GAAG,CAAC;;;;GACrF;EAEDI,6BAAA,CAAApB,SAAA,CAAAmB,mBAAmB,GAAnB;IACE,OAAO,IAAI9B,gCAAgC,CAAC,IAAI,EAAE,IAAI,CAACM,KAAK,CAAC;EAC/D,CAAC;EACH,OAAAyB,6BAAC;AAAD,CAAC,CAtBU7B,2BAA2B;;AAwBtC,IAAA+B,2CAAA,0BAAA9B,MAAA;EAEUC,SAAA,CAAA6B,2CAAA,EAAA9B,MAAA;EAFV,SAAA8B,4CAAA;;EAWA;EAPEA,2CAAA,CAAAtB,SAAA,CAAAmB,mBAAmB,GAAnB;IACE,OAAO,IAAI/B,8CAA8C,CAAC,IAAI,EAAE,IAAI,CAACO,KAAK,CAAC;EAC7E,CAAC;EAED2B,2CAAA,CAAAtB,SAAA,CAAAuB,mBAAmB,GAAnB;IACE,OAAO,IAAIzC,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAACa,KAAK,CAAC;EAC5D,CAAC;EACH,OAAA2B,2CAAC;AAAD,CAAC,CATSvB,0BAA0B;;AAWpC,IAAAyB,8CAAA,0BAAAhC,MAAA;EAEUC,SAAA,CAAA+B,8CAAA,EAAAhC,MAAA;EAFV,SAAAgC,+CAAA;;EAWA;EAPEA,8CAAA,CAAAxB,SAAA,CAAAmB,mBAAmB,GAAnB;IACE,OAAO,IAAI7B,iDAAiD,CAAC,IAAI,EAAE,IAAI,CAACK,KAAK,CAAC;EAChF,CAAC;EAED6B,8CAAA,CAAAxB,SAAA,CAAAyB,kBAAkB,GAAlB;IACE,OAAO,IAAI1C,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAACY,KAAK,CAAC;EAC9D,CAAC;EACH,OAAA6B,8CAAC;AAAD,CAAC,CATSJ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}