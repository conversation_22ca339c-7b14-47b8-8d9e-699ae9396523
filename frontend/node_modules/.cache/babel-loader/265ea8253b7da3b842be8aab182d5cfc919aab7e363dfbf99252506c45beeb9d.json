{"ast": null, "code": "export function loadConvParamsFactory(extractWeightEntry) {\n  return function (prefix) {\n    var filters = extractWeightEntry(prefix + \"/filters\", 4);\n    var bias = extractWeightEntry(prefix + \"/bias\", 1);\n    return {\n      filters: filters,\n      bias: bias\n    };\n  };\n}", "map": {"version": 3, "names": ["loadConvParamsFactory", "extractWeightEntry", "prefix", "filters", "bias"], "sources": ["../../../src/common/loadConvParamsFactory.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAM,SAAUA,qBAAqBA,CAACC,kBAAqE;EACzG,OAAO,UAASC,MAAc;IAC5B,IAAMC,OAAO,GAAGF,kBAAkB,CAAiBC,MAAM,aAAU,EAAE,CAAC,CAAC;IACvE,IAAME,IAAI,GAAGH,kBAAkB,CAAiBC,MAAM,UAAO,EAAE,CAAC,CAAC;IAEjE,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;EAC1B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}