{"ast": null, "code": "var baseCreate = require('./_baseCreate'),\n  getPrototype = require('./_getPrototype'),\n  isPrototype = require('./_isPrototype');\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return typeof object.constructor == 'function' && !isPrototype(object) ? baseCreate(getPrototype(object)) : {};\n}\nmodule.exports = initCloneObject;", "map": {"version": 3, "names": ["baseCreate", "require", "getPrototype", "isPrototype", "initCloneObject", "object", "constructor", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_initCloneObject.js"], "sourcesContent": ["var baseCreate = require('./_baseCreate'),\n    getPrototype = require('./_getPrototype'),\n    isPrototype = require('./_isPrototype');\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\nmodule.exports = initCloneObject;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACzCE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAQ,OAAOA,MAAM,CAACC,WAAW,IAAI,UAAU,IAAI,CAACH,WAAW,CAACE,MAAM,CAAC,GACnEL,UAAU,CAACE,YAAY,CAACG,MAAM,CAAC,CAAC,GAChC,CAAC,CAAC;AACR;AAEAE,MAAM,CAACC,OAAO,GAAGJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}