{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { FaceDetection } from '../classes/FaceDetection';\nimport { isTensor3D, isTensor4D } from '../utils';\n/**\r\n * Extracts the tensors of the image regions containing the detected faces.\r\n * Useful if you want to compute the face descriptors for the face images.\r\n * Using this method is faster then extracting a canvas for each face and\r\n * converting them to tensors individually.\r\n *\r\n * @param imageTensor The image tensor that face detection has been performed on.\r\n * @param detections The face detection results or face bounding boxes for that image.\r\n * @returns Tensors of the corresponding image region for each detected face.\r\n */\nexport function extractFaceTensors(imageTensor, detections) {\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      if (!isTensor3D(imageTensor) && !isTensor4D(imageTensor)) {\n        throw new Error('extractFaceTensors - expected image tensor to be 3D or 4D');\n      }\n      if (isTensor4D(imageTensor) && imageTensor.shape[0] > 1) {\n        throw new Error('extractFaceTensors - batchSize > 1 not supported');\n      }\n      return [2 /*return*/, tf.tidy(function () {\n        var _a = imageTensor.shape.slice(isTensor4D(imageTensor) ? 1 : 0),\n          imgHeight = _a[0],\n          imgWidth = _a[1],\n          numChannels = _a[2];\n        var boxes = detections.map(function (det) {\n          return det instanceof FaceDetection ? det.forSize(imgWidth, imgHeight).box : det;\n        }).map(function (box) {\n          return box.clipAtImageBorders(imgWidth, imgHeight);\n        });\n        var faceTensors = boxes.map(function (_a) {\n          var x = _a.x,\n            y = _a.y,\n            width = _a.width,\n            height = _a.height;\n          return tf.slice3d(imageTensor.as3D(imgHeight, imgWidth, numChannels), [y, x, 0], [height, width, numChannels]);\n        });\n        return faceTensors;\n      })];\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "FaceDetection", "isTensor3D", "isTensor4D", "extractFaceTensors", "imageTensor", "detections", "Error", "shape", "tidy", "_a", "slice", "imgHeight", "imgWidth", "numChannels", "boxes", "map", "det", "forSize", "box", "clipAtImageBorders", "faceTensors", "x", "y", "width", "height", "slice3d", "as3D"], "sources": ["../../../src/dom/extractFaceTensors.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,EAAEC,UAAU,QAAQ,UAAU;AAEjD;;;;;;;;;;AAUA,OAAM,SAAgBC,kBAAkBA,CACtCC,WAAsC,EACtCC,UAAuC;;;MAGvC,IAAI,CAACJ,UAAU,CAACG,WAAW,CAAC,IAAI,CAACF,UAAU,CAACE,WAAW,CAAC,EAAE;QACxD,MAAM,IAAIE,KAAK,CAAC,2DAA2D,CAAC;;MAG9E,IAAIJ,UAAU,CAACE,WAAW,CAAC,IAAIA,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QACvD,MAAM,IAAID,KAAK,CAAC,kDAAkD,CAAC;;MAGrE,sBAAOP,EAAE,CAACS,IAAI,CAAC;QACP,IAAAC,EAAA,GAAAL,WAAA,CAAAG,KAAA,CAAAG,KAAA,CAAAR,UAAA,CAAAE,WAAA,UAA6F;UAA5FO,SAAA,GAAAF,EAAA,GAAS;UAAEG,QAAA,GAAAH,EAAA,GAAQ;UAAEI,WAAA,GAAAJ,EAAA,GAAuE;QAEnG,IAAMK,KAAK,GAAGT,UAAU,CAACU,GAAG,CAC1B,UAAAC,GAAG;UAAI,OAAAA,GAAG,YAAYhB,aAAa,GAC/BgB,GAAG,CAACC,OAAO,CAACL,QAAQ,EAAED,SAAS,CAAC,CAACO,GAAG,GACpCF,GAAG;QAFA,CAEA,CACR,CACED,GAAG,CAAC,UAAAG,GAAG;UAAI,OAAAA,GAAG,CAACC,kBAAkB,CAACP,QAAQ,EAAED,SAAS,CAAC;QAA3C,CAA2C,CAAC;QAE1D,IAAMS,WAAW,GAAGN,KAAK,CAACC,GAAG,CAAC,UAACN,EAAuB;cAArBY,CAAA,GAAAZ,EAAA,CAAAY,CAAC;YAAEC,CAAA,GAAAb,EAAA,CAAAa,CAAC;YAAEC,KAAA,GAAAd,EAAA,CAAAc,KAAK;YAAEC,MAAA,GAAAf,EAAA,CAAAe,MAAM;UAClD,OAAAzB,EAAE,CAAC0B,OAAO,CAACrB,WAAW,CAACsB,IAAI,CAACf,SAAS,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAE,CAACS,CAAC,EAAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAACG,MAAM,EAAED,KAAK,EAAEV,WAAW,CAAC,CAAC;QAAvG,CAAuG,CACxG;QAED,OAAOO,WAAW;MACpB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}