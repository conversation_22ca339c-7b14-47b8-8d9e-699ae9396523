{"ast": null, "code": "import { AgeGenderNet } from '../ageGenderNet/AgeGenderNet';\nimport { FaceExpressionNet } from '../faceExpressionNet/FaceExpressionNet';\nimport { FaceLandmark68Net } from '../faceLandmarkNet/FaceLandmark68Net';\nimport { FaceLandmark68TinyNet } from '../faceLandmarkNet/FaceLandmark68TinyNet';\nimport { FaceRecognitionNet } from '../faceRecognitionNet/FaceRecognitionNet';\nimport { Mtcnn } from '../mtcnn/Mtcnn';\nimport { SsdMobilenetv1 } from '../ssdMobilenetv1/SsdMobilenetv1';\nimport { TinyFaceDetector } from '../tinyFaceDetector/TinyFaceDetector';\nimport { TinyYolov2 } from '../tinyYolov2';\nexport var nets = {\n  ssdMobilenetv1: new SsdMobilenetv1(),\n  tinyFaceDetector: new TinyFaceDetector(),\n  tinyYolov2: new TinyYolov2(),\n  mtcnn: new Mtcnn(),\n  faceLandmark68Net: new FaceLandmark68Net(),\n  faceLandmark68TinyNet: new FaceLandmark68TinyNet(),\n  faceRecognitionNet: new FaceRecognitionNet(),\n  faceExpressionNet: new FaceExpressionNet(),\n  ageGenderNet: new AgeGenderNet()\n};\n/**\r\n * Attempts to detect all faces in an image using SSD Mobilenetv1 Network.\r\n *\r\n * @param input The input image.\r\n * @param options (optional, default: see SsdMobilenetv1Options constructor for default parameters).\r\n * @returns Bounding box of each face with score.\r\n */\nexport var ssdMobilenetv1 = function (input, options) {\n  return nets.ssdMobilenetv1.locateFaces(input, options);\n};\n/**\r\n * Attempts to detect all faces in an image using the Tiny Face Detector.\r\n *\r\n * @param input The input image.\r\n * @param options (optional, default: see TinyFaceDetectorOptions constructor for default parameters).\r\n * @returns Bounding box of each face with score.\r\n */\nexport var tinyFaceDetector = function (input, options) {\n  return nets.tinyFaceDetector.locateFaces(input, options);\n};\n/**\r\n * Attempts to detect all faces in an image using the Tiny Yolov2 Network.\r\n *\r\n * @param input The input image.\r\n * @param options (optional, default: see TinyYolov2Options constructor for default parameters).\r\n * @returns Bounding box of each face with score.\r\n */\nexport var tinyYolov2 = function (input, options) {\n  return nets.tinyYolov2.locateFaces(input, options);\n};\n/**\r\n * Attempts to detect all faces in an image and the 5 point face landmarks\r\n * of each detected face using the MTCNN Network.\r\n *\r\n * @param input The input image.\r\n * @param options (optional, default: see MtcnnOptions constructor for default parameters).\r\n * @returns Bounding box of each face with score and 5 point face landmarks.\r\n */\nexport var mtcnn = function (input, options) {\n  return nets.mtcnn.forward(input, options);\n};\n/**\r\n * Detects the 68 point face landmark positions of the face shown in an image.\r\n *\r\n * @param inputs The face image extracted from the bounding box of a face. Can\r\n * also be an array of input images, which will be batch processed.\r\n * @returns 68 point face landmarks or array thereof in case of batch input.\r\n */\nexport var detectFaceLandmarks = function (input) {\n  return nets.faceLandmark68Net.detectLandmarks(input);\n};\n/**\r\n * Detects the 68 point face landmark positions of the face shown in an image\r\n * using a tinier version of the 68 point face landmark model, which is slightly\r\n * faster at inference, but also slightly less accurate.\r\n *\r\n * @param inputs The face image extracted from the bounding box of a face. Can\r\n * also be an array of input images, which will be batch processed.\r\n * @returns 68 point face landmarks or array thereof in case of batch input.\r\n */\nexport var detectFaceLandmarksTiny = function (input) {\n  return nets.faceLandmark68TinyNet.detectLandmarks(input);\n};\n/**\r\n * Computes a 128 entry vector (face descriptor / face embeddings) from the face shown in an image,\r\n * which uniquely represents the features of that persons face. The computed face descriptor can\r\n * be used to measure the similarity between faces, by computing the euclidean distance of two\r\n * face descriptors.\r\n *\r\n * @param inputs The face image extracted from the aligned bounding box of a face. Can\r\n * also be an array of input images, which will be batch processed.\r\n * @returns Face descriptor with 128 entries or array thereof in case of batch input.\r\n */\nexport var computeFaceDescriptor = function (input) {\n  return nets.faceRecognitionNet.computeFaceDescriptor(input);\n};\n/**\r\n * Recognizes the facial expressions from a face image.\r\n *\r\n * @param inputs The face image extracted from the bounding box of a face. Can\r\n * also be an array of input images, which will be batch processed.\r\n * @returns Facial expressions with corresponding probabilities or array thereof in case of batch input.\r\n */\nexport var recognizeFaceExpressions = function (input) {\n  return nets.faceExpressionNet.predictExpressions(input);\n};\n/**\r\n * Predicts age and gender from a face image.\r\n *\r\n * @param inputs The face image extracted from the bounding box of a face. Can\r\n * also be an array of input images, which will be batch processed.\r\n * @returns Predictions with age, gender and gender probability or array thereof in case of batch input.\r\n */\nexport var predictAgeAndGender = function (input) {\n  return nets.ageGenderNet.predictAgeAndGender(input);\n};\nexport var loadSsdMobilenetv1Model = function (url) {\n  return nets.ssdMobilenetv1.load(url);\n};\nexport var loadTinyFaceDetectorModel = function (url) {\n  return nets.tinyFaceDetector.load(url);\n};\nexport var loadMtcnnModel = function (url) {\n  return nets.mtcnn.load(url);\n};\nexport var loadTinyYolov2Model = function (url) {\n  return nets.tinyYolov2.load(url);\n};\nexport var loadFaceLandmarkModel = function (url) {\n  return nets.faceLandmark68Net.load(url);\n};\nexport var loadFaceLandmarkTinyModel = function (url) {\n  return nets.faceLandmark68TinyNet.load(url);\n};\nexport var loadFaceRecognitionModel = function (url) {\n  return nets.faceRecognitionNet.load(url);\n};\nexport var loadFaceExpressionModel = function (url) {\n  return nets.faceExpressionNet.load(url);\n};\nexport var loadAgeGenderModel = function (url) {\n  return nets.ageGenderNet.load(url);\n};\n// backward compatibility\nexport var loadFaceDetectionModel = loadSsdMobilenetv1Model;\nexport var locateFaces = ssdMobilenetv1;\nexport var detectLandmarks = detectFaceLandmarks;", "map": {"version": 3, "names": ["AgeGenderNet", "FaceExpressionNet", "FaceLandmark68Net", "FaceLandmark68TinyNet", "FaceRecognitionNet", "Mtcnn", "SsdMobilenetv1", "TinyFaceDetector", "TinyYolov2", "nets", "ssdMobilenetv1", "tinyFaceDetector", "tinyYolov2", "mtcnn", "faceLandmark68Net", "faceLandmark68TinyNet", "faceRecognitionNet", "faceExpressionNet", "ageGenderNet", "input", "options", "locateFaces", "forward", "detectFaceLandmarks", "detectLandmarks", "detectFaceLandmarksTiny", "computeFaceDescriptor", "recognizeFaceExpressions", "predictExpressions", "predictAgeAndGender", "loadSsdMobilenetv1Model", "url", "load", "loadTinyFaceDetectorModel", "loadMtcnnModel", "loadTinyYolov2Model", "loadFaceLandmarkModel", "loadFaceLandmarkTinyModel", "loadFaceRecognitionModel", "loadFaceExpressionModel", "loadAgeGenderModel", "loadFaceDetectionModel"], "sources": ["../../../src/globalApi/nets.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAM3D,SAASC,iBAAiB,QAAQ,wCAAwC;AAE1E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,QAAQ,0CAA0C;AAG7E,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,SAASC,cAAc,QAAQ,kCAAkC;AAEjE,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,SAA6BC,UAAU,QAAQ,eAAe;AAE9D,OAAO,IAAMC,IAAI,GAAG;EAClBC,cAAc,EAAE,IAAIJ,cAAc,EAAE;EACpCK,gBAAgB,EAAE,IAAIJ,gBAAgB,EAAE;EACxCK,UAAU,EAAE,IAAIJ,UAAU,EAAE;EAC5BK,KAAK,EAAE,IAAIR,KAAK,EAAE;EAClBS,iBAAiB,EAAE,IAAIZ,iBAAiB,EAAE;EAC1Ca,qBAAqB,EAAE,IAAIZ,qBAAqB,EAAE;EAClDa,kBAAkB,EAAE,IAAIZ,kBAAkB,EAAE;EAC5Ca,iBAAiB,EAAE,IAAIhB,iBAAiB,EAAE;EAC1CiB,YAAY,EAAE,IAAIlB,YAAY;CAC/B;AAED;;;;;;;AAOA,OAAO,IAAMU,cAAc,GAAG,SAAAA,CAACS,KAAgB,EAAEC,OAA8B;EAC7E,OAAAX,IAAI,CAACC,cAAc,CAACW,WAAW,CAACF,KAAK,EAAEC,OAAO,CAAC;AAA/C,CAA+C;AAEjD;;;;;;;AAOA,OAAO,IAAMT,gBAAgB,GAAG,SAAAA,CAACQ,KAAgB,EAAEC,OAAgC;EACjF,OAAAX,IAAI,CAACE,gBAAgB,CAACU,WAAW,CAACF,KAAK,EAAEC,OAAO,CAAC;AAAjD,CAAiD;AAEnD;;;;;;;AAOA,OAAO,IAAMR,UAAU,GAAG,SAAAA,CAACO,KAAgB,EAAEC,OAA2B;EACtE,OAAAX,IAAI,CAACG,UAAU,CAACS,WAAW,CAACF,KAAK,EAAEC,OAAO,CAAC;AAA3C,CAA2C;AAE7C;;;;;;;;AAQA,OAAO,IAAMP,KAAK,GAAG,SAAAA,CAACM,KAAgB,EAAEC,OAAqB;EAC3D,OAAAX,IAAI,CAACI,KAAK,CAACS,OAAO,CAACH,KAAK,EAAEC,OAAO,CAAC;AAAlC,CAAkC;AAEpC;;;;;;;AAOA,OAAO,IAAMG,mBAAmB,GAAG,SAAAA,CAACJ,KAAgB;EAClD,OAAAV,IAAI,CAACK,iBAAiB,CAACU,eAAe,CAACL,KAAK,CAAC;AAA7C,CAA6C;AAE/C;;;;;;;;;AASA,OAAO,IAAMM,uBAAuB,GAAG,SAAAA,CAACN,KAAgB;EACtD,OAAAV,IAAI,CAACM,qBAAqB,CAACS,eAAe,CAACL,KAAK,CAAC;AAAjD,CAAiD;AAEnD;;;;;;;;;;AAUA,OAAO,IAAMO,qBAAqB,GAAG,SAAAA,CAACP,KAAgB;EACpD,OAAAV,IAAI,CAACO,kBAAkB,CAACU,qBAAqB,CAACP,KAAK,CAAC;AAApD,CAAoD;AAGtD;;;;;;;AAOA,OAAO,IAAMQ,wBAAwB,GAAG,SAAAA,CAACR,KAAgB;EACvD,OAAAV,IAAI,CAACQ,iBAAiB,CAACW,kBAAkB,CAACT,KAAK,CAAC;AAAhD,CAAgD;AAElD;;;;;;;AAOA,OAAO,IAAMU,mBAAmB,GAAG,SAAAA,CAACV,KAAgB;EAClD,OAAAV,IAAI,CAACS,YAAY,CAACW,mBAAmB,CAACV,KAAK,CAAC;AAA5C,CAA4C;AAE9C,OAAO,IAAMW,uBAAuB,GAAG,SAAAA,CAACC,GAAW;EAAK,OAAAtB,IAAI,CAACC,cAAc,CAACsB,IAAI,CAACD,GAAG,CAAC;AAA7B,CAA6B;AACrF,OAAO,IAAME,yBAAyB,GAAG,SAAAA,CAACF,GAAW;EAAK,OAAAtB,IAAI,CAACE,gBAAgB,CAACqB,IAAI,CAACD,GAAG,CAAC;AAA/B,CAA+B;AACzF,OAAO,IAAMG,cAAc,GAAG,SAAAA,CAACH,GAAW;EAAK,OAAAtB,IAAI,CAACI,KAAK,CAACmB,IAAI,CAACD,GAAG,CAAC;AAApB,CAAoB;AACnE,OAAO,IAAMI,mBAAmB,GAAG,SAAAA,CAACJ,GAAW;EAAK,OAAAtB,IAAI,CAACG,UAAU,CAACoB,IAAI,CAACD,GAAG,CAAC;AAAzB,CAAyB;AAC7E,OAAO,IAAMK,qBAAqB,GAAG,SAAAA,CAACL,GAAW;EAAK,OAAAtB,IAAI,CAACK,iBAAiB,CAACkB,IAAI,CAACD,GAAG,CAAC;AAAhC,CAAgC;AACtF,OAAO,IAAMM,yBAAyB,GAAG,SAAAA,CAACN,GAAW;EAAK,OAAAtB,IAAI,CAACM,qBAAqB,CAACiB,IAAI,CAACD,GAAG,CAAC;AAApC,CAAoC;AAC9F,OAAO,IAAMO,wBAAwB,GAAG,SAAAA,CAACP,GAAW;EAAK,OAAAtB,IAAI,CAACO,kBAAkB,CAACgB,IAAI,CAACD,GAAG,CAAC;AAAjC,CAAiC;AAC1F,OAAO,IAAMQ,uBAAuB,GAAG,SAAAA,CAACR,GAAW;EAAK,OAAAtB,IAAI,CAACQ,iBAAiB,CAACe,IAAI,CAACD,GAAG,CAAC;AAAhC,CAAgC;AACxF,OAAO,IAAMS,kBAAkB,GAAG,SAAAA,CAACT,GAAW;EAAK,OAAAtB,IAAI,CAACS,YAAY,CAACc,IAAI,CAACD,GAAG,CAAC;AAA3B,CAA2B;AAE9E;AACA,OAAO,IAAMU,sBAAsB,GAAGX,uBAAuB;AAC7D,OAAO,IAAMT,WAAW,GAAGX,cAAc;AACzC,OAAO,IAAMc,eAAe,GAAGD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}