{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { toNetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { normalize } from '../ops';\nimport { denseBlock4 } from './denseBlock';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nvar FaceFeatureExtractor = /** @class */function (_super) {\n  __extends(FaceFeatureExtractor, _super);\n  function FaceFeatureExtractor() {\n    return _super.call(this, 'FaceFeatureExtractor') || this;\n  }\n  FaceFeatureExtractor.prototype.forwardInput = function (input) {\n    var params = this.params;\n    if (!params) {\n      throw new Error('FaceFeatureExtractor - load model before inference');\n    }\n    return tf.tidy(function () {\n      var batchTensor = input.toBatchTensor(112, true);\n      var meanRgb = [122.782, 117.001, 104.298];\n      var normalized = normalize(batchTensor, meanRgb).div(tf.scalar(255));\n      var out = denseBlock4(normalized, params.dense0, true);\n      out = denseBlock4(out, params.dense1);\n      out = denseBlock4(out, params.dense2);\n      out = denseBlock4(out, params.dense3);\n      out = tf.avgPool(out, [7, 7], [2, 2], 'valid');\n      return out;\n    });\n  };\n  FaceFeatureExtractor.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  FaceFeatureExtractor.prototype.getDefaultModelName = function () {\n    return 'face_feature_extractor_model';\n  };\n  FaceFeatureExtractor.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMap(weightMap);\n  };\n  FaceFeatureExtractor.prototype.extractParams = function (weights) {\n    return extractParams(weights);\n  };\n  return FaceFeatureExtractor;\n}(NeuralNetwork);\nexport { FaceFeatureExtractor };", "map": {"version": 3, "names": ["tf", "toNetInput", "NeuralNetwork", "normalize", "denseBlock4", "extractParams", "extractParamsFromWeigthMap", "FaceFeatureExtractor", "_super", "__extends", "call", "prototype", "forwardInput", "input", "params", "Error", "tidy", "batchTensor", "toBatchTensor", "meanRgb", "normalized", "div", "scalar", "out", "dense0", "dense1", "dense2", "dense3", "avgPool", "forward", "_a", "apply", "_b", "sent", "getDefaultModelName", "weightMap", "weights"], "sources": ["../../../src/faceFeatureExtractor/FaceFeatureExtractor.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AAGzE,IAAAC,oBAAA,0BAAAC,MAAA;EAA0CC,SAAA,CAAAF,oBAAA,EAAAC,MAAA;EAExC,SAAAD,qBAAA;WACEC,MAAA,CAAAE,IAAA,OAAM,sBAAsB,CAAC;EAC/B;EAEOH,oBAAA,CAAAI,SAAA,CAAAC,YAAY,GAAnB,UAAoBC,KAAe;IAEzB,IAAAC,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;;IAGvE,OAAOf,EAAE,CAACgB,IAAI,CAAC;MACb,IAAMC,WAAW,GAAGJ,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC;MAClD,IAAMC,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC3C,IAAMC,UAAU,GAAGjB,SAAS,CAACc,WAAW,EAAEE,OAAO,CAAC,CAACE,GAAG,CAACrB,EAAE,CAACsB,MAAM,CAAC,GAAG,CAAC,CAAgB;MAErF,IAAIC,GAAG,GAAGnB,WAAW,CAACgB,UAAU,EAAEN,MAAM,CAACU,MAAM,EAAE,IAAI,CAAC;MACtDD,GAAG,GAAGnB,WAAW,CAACmB,GAAG,EAAET,MAAM,CAACW,MAAM,CAAC;MACrCF,GAAG,GAAGnB,WAAW,CAACmB,GAAG,EAAET,MAAM,CAACY,MAAM,CAAC;MACrCH,GAAG,GAAGnB,WAAW,CAACmB,GAAG,EAAET,MAAM,CAACa,MAAM,CAAC;MACrCJ,GAAG,GAAGvB,EAAE,CAAC4B,OAAO,CAACL,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;MAE9C,OAAOA,GAAG;IACZ,CAAC,CAAC;EACJ,CAAC;EAEYhB,oBAAA,CAAAI,SAAA,CAAAkB,OAAO,GAApB,UAAqBhB,KAAgB;;;;;;YAC5BiB,EAAA,OAAI,CAAClB,YAAY;YAAC,qBAAMX,UAAU,CAACY,KAAK,CAAC;;YAAhD,sBAAOiB,EAAA,CAAAC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAES1B,oBAAA,CAAAI,SAAA,CAAAuB,mBAAmB,GAA7B;IACE,OAAO,8BAA8B;EACvC,CAAC;EAES3B,oBAAA,CAAAI,SAAA,CAAAL,0BAA0B,GAApC,UAAqC6B,SAA4B;IAC/D,OAAO7B,0BAA0B,CAAC6B,SAAS,CAAC;EAC9C,CAAC;EAES5B,oBAAA,CAAAI,SAAA,CAAAN,aAAa,GAAvB,UAAwB+B,OAAqB;IAC3C,OAAO/B,aAAa,CAAC+B,OAAO,CAAC;EAC/B,CAAC;EACH,OAAA7B,oBAAC;AAAD,CAAC,CA5CyCL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}