{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function leaky(x) {\n  return tf.tidy(function () {\n    var min = tf.mul(x, tf.scalar(0.10000000149011612));\n    return tf.add(tf.relu(tf.sub(x, min)), min);\n    //return tf.maximum(x, min)\n  });\n}", "map": {"version": 3, "names": ["tf", "leaky", "x", "tidy", "min", "mul", "scalar", "add", "relu", "sub"], "sources": ["../../../src/tinyYolov2/leaky.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,OAAM,SAAUC,KAAKA,CAACC,CAAc;EAClC,OAAOF,EAAE,CAACG,IAAI,CAAC;IACb,IAAMC,GAAG,GAAGJ,EAAE,CAACK,GAAG,CAACH,CAAC,EAAEF,EAAE,CAACM,MAAM,CAAC,mBAAmB,CAAC,CAAC;IACrD,OAAON,EAAE,CAACO,GAAG,CAACP,EAAE,CAACQ,IAAI,CAACR,EAAE,CAACS,GAAG,CAACP,CAAC,EAAEE,GAAG,CAAC,CAAC,EAAEA,GAAG,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}