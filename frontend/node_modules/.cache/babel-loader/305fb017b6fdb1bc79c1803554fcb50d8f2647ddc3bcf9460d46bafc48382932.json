{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Hook personnalisé pour utiliser Supabase dans PresencePro\n */\n\nimport { useState, useEffect } from 'react';\nimport { supabase } from '../config/supabase';\nimport { supabaseService } from '../services/supabaseService';\nimport { UserRole } from '../types';\nexport const useSupabase = () => {\n  _s();\n  const [supabaseUser, setSupabaseUser] = useState(null);\n  const [user, setUser] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Écouter les changements d'authentification Supabase\n  useEffect(() => {\n    // Récupérer la session actuelle\n    const getSession = async () => {\n      const {\n        data: {\n          session\n        },\n        error\n      } = await supabase.auth.getSession();\n      if (error) {\n        console.error('Erreur lors de la récupération de la session:', error);\n        setError(error.message);\n      } else {\n        setSession(session);\n        setSupabaseUser((session === null || session === void 0 ? void 0 : session.user) || null);\n        if (session !== null && session !== void 0 && session.user) {\n          await loadUserData(session.user.id);\n        }\n      }\n      setLoading(false);\n    };\n    getSession();\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setLoading(true);\n      setError(null);\n      try {\n        setSession(session);\n        setSupabaseUser((session === null || session === void 0 ? void 0 : session.user) || null);\n        if (session !== null && session !== void 0 && session.user) {\n          await loadUserData(session.user.id);\n        } else {\n          setUser(null);\n        }\n      } catch (err) {\n        console.error('Erreur lors du changement d\\'état d\\'authentification:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    });\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  // Charger les données utilisateur depuis la base de données\n  const loadUserData = async userId => {\n    try {\n      const userData = await supabaseService.getUserById(userId);\n      setUser(userData);\n    } catch (err) {\n      console.error('Erreur lors du chargement des données utilisateur:', err);\n      setError(err.message);\n    }\n  };\n\n  // Connexion\n  const signIn = async (email, password) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) throw error;\n\n      // Les données utilisateur seront chargées automatiquement via onAuthStateChange\n    } catch (err) {\n      console.error('Erreur de connexion:', err);\n      setError(getSupabaseErrorMessage(err.message));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Inscription\n  const signUp = async (email, password, userData) => {\n    setLoading(true);\n    setError(null);\n    try {\n      // 1. Créer l'utilisateur dans Supabase Auth\n      const {\n        data: authData,\n        error: authError\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            first_name: userData.firstName,\n            last_name: userData.lastName,\n            username: userData.username\n          }\n        }\n      });\n      if (authError) throw authError;\n      if (authData.user) {\n        // 2. Créer le profil utilisateur dans la base de données\n        const newUserData = {\n          username: userData.username || email.split('@')[0],\n          email: authData.user.email || email,\n          firstName: userData.firstName || '',\n          lastName: userData.lastName || '',\n          fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n          role: userData.role || UserRole.STUDENT,\n          roleDisplay: userData.roleDisplay || 'Étudiant',\n          phoneNumber: userData.phoneNumber,\n          dateOfBirth: userData.dateOfBirth,\n          address: userData.address,\n          profilePicture: userData.profilePicture,\n          isActive: true,\n          dateJoined: new Date().toISOString(),\n          lastLogin: new Date().toISOString()\n        };\n\n        // Utiliser l'ID de Supabase Auth comme ID utilisateur\n        await supabaseService.createUserWithId(authData.user.id, newUserData);\n      }\n    } catch (err) {\n      console.error('Erreur d\\'inscription:', err);\n      setError(getSupabaseErrorMessage(err.message));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Déconnexion\n  const logout = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) throw error;\n\n      // Les données seront nettoyées automatiquement via onAuthStateChange\n    } catch (err) {\n      console.error('Erreur de déconnexion:', err);\n      setError('Erreur lors de la déconnexion');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Actualiser les données utilisateur\n  const refreshUserData = async () => {\n    if (!supabaseUser) return;\n    setLoading(true);\n    setError(null);\n    try {\n      await loadUserData(supabaseUser.id);\n    } catch (err) {\n      console.error('Erreur lors de l\\'actualisation des données:', err);\n      setError('Erreur lors de l\\'actualisation des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    supabaseUser,\n    user,\n    session,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  };\n};\n\n// Fonction utilitaire pour traduire les erreurs Supabase\n_s(useSupabase, \"nMMJT7KwobVTT1525EcyX0AQNik=\");\nconst getSupabaseErrorMessage = errorMessage => {\n  if (errorMessage.includes('Invalid login credentials')) {\n    return 'Email ou mot de passe incorrect';\n  }\n  if (errorMessage.includes('User already registered')) {\n    return 'Cette adresse email est déjà utilisée';\n  }\n  if (errorMessage.includes('Password should be at least')) {\n    return 'Le mot de passe doit contenir au moins 6 caractères';\n  }\n  if (errorMessage.includes('Unable to validate email address')) {\n    return 'Adresse email invalide';\n  }\n  if (errorMessage.includes('Email rate limit exceeded')) {\n    return 'Trop de tentatives. Veuillez réessayer plus tard';\n  }\n  if (errorMessage.includes('Network error')) {\n    return 'Erreur de connexion réseau';\n  }\n  return errorMessage || 'Une erreur est survenue. Veuillez réessayer';\n};\nexport default useSupabase;", "map": {"version": 3, "names": ["useState", "useEffect", "supabase", "supabaseService", "UserRole", "useSupabase", "_s", "supabaseUser", "setSupabaseUser", "user", "setUser", "session", "setSession", "loading", "setLoading", "error", "setError", "getSession", "data", "auth", "console", "message", "loadUserData", "id", "subscription", "onAuthStateChange", "event", "err", "unsubscribe", "userId", "userData", "getUserById", "signIn", "email", "password", "signInWithPassword", "getSupabaseErrorMessage", "signUp", "authData", "authError", "options", "first_name", "firstName", "last_name", "lastName", "username", "newUserData", "split", "fullName", "trim", "role", "STUDENT", "roleDisplay", "phoneNumber", "dateOfBirth", "address", "profilePicture", "isActive", "dateJoined", "Date", "toISOString", "lastLogin", "createUserWithId", "logout", "signOut", "refreshUserData", "errorMessage", "includes"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useSupabase.ts"], "sourcesContent": ["/**\n * Hook personnalisé pour utiliser Supabase dans PresencePro\n */\n\nimport { useState, useEffect } from 'react';\nimport { User as SupabaseUser, Session } from '@supabase/supabase-js';\nimport { supabase } from '../config/supabase';\nimport { supabaseService } from '../services/supabaseService';\nimport { User, UserRole } from '../types';\n\ninterface UseSupabaseReturn {\n  // État d'authentification\n  supabaseUser: SupabaseUser | null;\n  user: User | null;\n  session: Session | null;\n  loading: boolean;\n  error: string | null;\n  \n  // Méthodes d'authentification\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, userData: Partial<User>) => Promise<void>;\n  logout: () => Promise<void>;\n  \n  // Méthodes de données\n  refreshUserData: () => Promise<void>;\n}\n\nexport const useSupabase = (): UseSupabaseReturn => {\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);\n  const [user, setUser] = useState<User | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Écouter les changements d'authentification Supabase\n  useEffect(() => {\n    // Récupérer la session actuelle\n    const getSession = async () => {\n      const { data: { session }, error } = await supabase.auth.getSession();\n      if (error) {\n        console.error('Erreur lors de la récupération de la session:', error);\n        setError(error.message);\n      } else {\n        setSession(session);\n        setSupabaseUser(session?.user || null);\n        \n        if (session?.user) {\n          await loadUserData(session.user.id);\n        }\n      }\n      setLoading(false);\n    };\n\n    getSession();\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setLoading(true);\n        setError(null);\n        \n        try {\n          setSession(session);\n          setSupabaseUser(session?.user || null);\n          \n          if (session?.user) {\n            await loadUserData(session.user.id);\n          } else {\n            setUser(null);\n          }\n        } catch (err: any) {\n          console.error('Erreur lors du changement d\\'état d\\'authentification:', err);\n          setError(err.message);\n        } finally {\n          setLoading(false);\n        }\n      }\n    );\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  // Charger les données utilisateur depuis la base de données\n  const loadUserData = async (userId: string) => {\n    try {\n      const userData = await supabaseService.getUserById(userId);\n      setUser(userData);\n    } catch (err: any) {\n      console.error('Erreur lors du chargement des données utilisateur:', err);\n      setError(err.message);\n    }\n  };\n\n  // Connexion\n  const signIn = async (email: string, password: string): Promise<void> => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n\n      if (error) throw error;\n      \n      // Les données utilisateur seront chargées automatiquement via onAuthStateChange\n    } catch (err: any) {\n      console.error('Erreur de connexion:', err);\n      setError(getSupabaseErrorMessage(err.message));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Inscription\n  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<void> => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      // 1. Créer l'utilisateur dans Supabase Auth\n      const { data: authData, error: authError } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            first_name: userData.firstName,\n            last_name: userData.lastName,\n            username: userData.username\n          }\n        }\n      });\n\n      if (authError) throw authError;\n      \n      if (authData.user) {\n        // 2. Créer le profil utilisateur dans la base de données\n        const newUserData: Omit<User, 'id'> = {\n          username: userData.username || email.split('@')[0],\n          email: authData.user.email || email,\n          firstName: userData.firstName || '',\n          lastName: userData.lastName || '',\n          fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n          role: userData.role || UserRole.STUDENT,\n          roleDisplay: userData.roleDisplay || 'Étudiant',\n          phoneNumber: userData.phoneNumber,\n          dateOfBirth: userData.dateOfBirth,\n          address: userData.address,\n          profilePicture: userData.profilePicture,\n          isActive: true,\n          dateJoined: new Date().toISOString(),\n          lastLogin: new Date().toISOString()\n        };\n        \n        // Utiliser l'ID de Supabase Auth comme ID utilisateur\n        await supabaseService.createUserWithId(authData.user.id, newUserData);\n      }\n      \n    } catch (err: any) {\n      console.error('Erreur d\\'inscription:', err);\n      setError(getSupabaseErrorMessage(err.message));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Déconnexion\n  const logout = async (): Promise<void> => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) throw error;\n      \n      // Les données seront nettoyées automatiquement via onAuthStateChange\n    } catch (err: any) {\n      console.error('Erreur de déconnexion:', err);\n      setError('Erreur lors de la déconnexion');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Actualiser les données utilisateur\n  const refreshUserData = async (): Promise<void> => {\n    if (!supabaseUser) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      await loadUserData(supabaseUser.id);\n    } catch (err: any) {\n      console.error('Erreur lors de l\\'actualisation des données:', err);\n      setError('Erreur lors de l\\'actualisation des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    supabaseUser,\n    user,\n    session,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  };\n};\n\n// Fonction utilitaire pour traduire les erreurs Supabase\nconst getSupabaseErrorMessage = (errorMessage: string): string => {\n  if (errorMessage.includes('Invalid login credentials')) {\n    return 'Email ou mot de passe incorrect';\n  }\n  if (errorMessage.includes('User already registered')) {\n    return 'Cette adresse email est déjà utilisée';\n  }\n  if (errorMessage.includes('Password should be at least')) {\n    return 'Le mot de passe doit contenir au moins 6 caractères';\n  }\n  if (errorMessage.includes('Unable to validate email address')) {\n    return 'Adresse email invalide';\n  }\n  if (errorMessage.includes('Email rate limit exceeded')) {\n    return 'Trop de tentatives. Veuillez réessayer plus tard';\n  }\n  if (errorMessage.includes('Network error')) {\n    return 'Erreur de connexion réseau';\n  }\n  \n  return errorMessage || 'Une erreur est survenue. Veuillez réessayer';\n};\n\nexport default useSupabase;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE3C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAAeC,QAAQ,QAAQ,UAAU;AAmBzC,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAyB;EAAAC,EAAA;EAClD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGR,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACS,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAM;QAAEC,IAAI,EAAE;UAAEP;QAAQ,CAAC;QAAEI;MAAM,CAAC,GAAG,MAAMb,QAAQ,CAACiB,IAAI,CAACF,UAAU,CAAC,CAAC;MACrE,IAAIF,KAAK,EAAE;QACTK,OAAO,CAACL,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrEC,QAAQ,CAACD,KAAK,CAACM,OAAO,CAAC;MACzB,CAAC,MAAM;QACLT,UAAU,CAACD,OAAO,CAAC;QACnBH,eAAe,CAAC,CAAAG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEF,IAAI,KAAI,IAAI,CAAC;QAEtC,IAAIE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEF,IAAI,EAAE;UACjB,MAAMa,YAAY,CAACX,OAAO,CAACF,IAAI,CAACc,EAAE,CAAC;QACrC;MACF;MACAT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDG,UAAU,CAAC,CAAC;;IAEZ;IACA,MAAM;MAAEC,IAAI,EAAE;QAAEM;MAAa;IAAE,CAAC,GAAGtB,QAAQ,CAACiB,IAAI,CAACM,iBAAiB,CAChE,OAAOC,KAAK,EAAEf,OAAO,KAAK;MACxBG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACFJ,UAAU,CAACD,OAAO,CAAC;QACnBH,eAAe,CAAC,CAAAG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEF,IAAI,KAAI,IAAI,CAAC;QAEtC,IAAIE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEF,IAAI,EAAE;UACjB,MAAMa,YAAY,CAACX,OAAO,CAACF,IAAI,CAACc,EAAE,CAAC;QACrC,CAAC,MAAM;UACLb,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC,CAAC,OAAOiB,GAAQ,EAAE;QACjBP,OAAO,CAACL,KAAK,CAAC,wDAAwD,EAAEY,GAAG,CAAC;QAC5EX,QAAQ,CAACW,GAAG,CAACN,OAAO,CAAC;MACvB,CAAC,SAAS;QACRP,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CACF,CAAC;IAED,OAAO,MAAM;MACXU,YAAY,CAACI,WAAW,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMN,YAAY,GAAG,MAAOO,MAAc,IAAK;IAC7C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM3B,eAAe,CAAC4B,WAAW,CAACF,MAAM,CAAC;MAC1DnB,OAAO,CAACoB,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOH,GAAQ,EAAE;MACjBP,OAAO,CAACL,KAAK,CAAC,oDAAoD,EAAEY,GAAG,CAAC;MACxEX,QAAQ,CAACW,GAAG,CAACN,OAAO,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMW,MAAM,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAoB;IACvEpB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM;QAAEE,IAAI;QAAEH;MAAM,CAAC,GAAG,MAAMb,QAAQ,CAACiB,IAAI,CAACgB,kBAAkB,CAAC;QAC7DF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAInB,KAAK,EAAE,MAAMA,KAAK;;MAEtB;IACF,CAAC,CAAC,OAAOY,GAAQ,EAAE;MACjBP,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;MAC1CX,QAAQ,CAACoB,uBAAuB,CAACT,GAAG,CAACN,OAAO,CAAC,CAAC;MAC9C,MAAMM,GAAG;IACX,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuB,MAAM,GAAG,MAAAA,CAAOJ,KAAa,EAAEC,QAAgB,EAAEJ,QAAuB,KAAoB;IAChGhB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAM;QAAEE,IAAI,EAAEoB,QAAQ;QAAEvB,KAAK,EAAEwB;MAAU,CAAC,GAAG,MAAMrC,QAAQ,CAACiB,IAAI,CAACkB,MAAM,CAAC;QACtEJ,KAAK;QACLC,QAAQ;QACRM,OAAO,EAAE;UACPtB,IAAI,EAAE;YACJuB,UAAU,EAAEX,QAAQ,CAACY,SAAS;YAC9BC,SAAS,EAAEb,QAAQ,CAACc,QAAQ;YAC5BC,QAAQ,EAAEf,QAAQ,CAACe;UACrB;QACF;MACF,CAAC,CAAC;MAEF,IAAIN,SAAS,EAAE,MAAMA,SAAS;MAE9B,IAAID,QAAQ,CAAC7B,IAAI,EAAE;QACjB;QACA,MAAMqC,WAA6B,GAAG;UACpCD,QAAQ,EAAEf,QAAQ,CAACe,QAAQ,IAAIZ,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClDd,KAAK,EAAEK,QAAQ,CAAC7B,IAAI,CAACwB,KAAK,IAAIA,KAAK;UACnCS,SAAS,EAAEZ,QAAQ,CAACY,SAAS,IAAI,EAAE;UACnCE,QAAQ,EAAEd,QAAQ,CAACc,QAAQ,IAAI,EAAE;UACjCI,QAAQ,EAAE,GAAGlB,QAAQ,CAACY,SAAS,IAAI,EAAE,IAAIZ,QAAQ,CAACc,QAAQ,IAAI,EAAE,EAAE,CAACK,IAAI,CAAC,CAAC;UACzEC,IAAI,EAAEpB,QAAQ,CAACoB,IAAI,IAAI9C,QAAQ,CAAC+C,OAAO;UACvCC,WAAW,EAAEtB,QAAQ,CAACsB,WAAW,IAAI,UAAU;UAC/CC,WAAW,EAAEvB,QAAQ,CAACuB,WAAW;UACjCC,WAAW,EAAExB,QAAQ,CAACwB,WAAW;UACjCC,OAAO,EAAEzB,QAAQ,CAACyB,OAAO;UACzBC,cAAc,EAAE1B,QAAQ,CAAC0B,cAAc;UACvCC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;;QAED;QACA,MAAMzD,eAAe,CAAC2D,gBAAgB,CAACxB,QAAQ,CAAC7B,IAAI,CAACc,EAAE,EAAEuB,WAAW,CAAC;MACvE;IAEF,CAAC,CAAC,OAAOnB,GAAQ,EAAE;MACjBP,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEY,GAAG,CAAC;MAC5CX,QAAQ,CAACoB,uBAAuB,CAACT,GAAG,CAACN,OAAO,CAAC,CAAC;MAC9C,MAAMM,GAAG;IACX,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiD,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxCjD,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM;QAAED;MAAM,CAAC,GAAG,MAAMb,QAAQ,CAACiB,IAAI,CAAC6C,OAAO,CAAC,CAAC;MAC/C,IAAIjD,KAAK,EAAE,MAAMA,KAAK;;MAEtB;IACF,CAAC,CAAC,OAAOY,GAAQ,EAAE;MACjBP,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEY,GAAG,CAAC;MAC5CX,QAAQ,CAAC,+BAA+B,CAAC;MACzC,MAAMW,GAAG;IACX,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAA2B;IACjD,IAAI,CAAC1D,YAAY,EAAE;IAEnBO,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMM,YAAY,CAACf,YAAY,CAACgB,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOI,GAAQ,EAAE;MACjBP,OAAO,CAACL,KAAK,CAAC,8CAA8C,EAAEY,GAAG,CAAC;MAClEX,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLP,YAAY;IACZE,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,KAAK;IACLiB,MAAM;IACNK,MAAM;IACN0B,MAAM;IACNE;EACF,CAAC;AACH,CAAC;;AAED;AAAA3D,EAAA,CAjMaD,WAAW;AAkMxB,MAAM+B,uBAAuB,GAAI8B,YAAoB,IAAa;EAChE,IAAIA,YAAY,CAACC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;IACtD,OAAO,iCAAiC;EAC1C;EACA,IAAID,YAAY,CAACC,QAAQ,CAAC,yBAAyB,CAAC,EAAE;IACpD,OAAO,uCAAuC;EAChD;EACA,IAAID,YAAY,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EAAE;IACxD,OAAO,qDAAqD;EAC9D;EACA,IAAID,YAAY,CAACC,QAAQ,CAAC,kCAAkC,CAAC,EAAE;IAC7D,OAAO,wBAAwB;EACjC;EACA,IAAID,YAAY,CAACC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;IACtD,OAAO,kDAAkD;EAC3D;EACA,IAAID,YAAY,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;IAC1C,OAAO,4BAA4B;EACrC;EAEA,OAAOD,YAAY,IAAI,6CAA6C;AACtE,CAAC;AAED,eAAe7D,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}