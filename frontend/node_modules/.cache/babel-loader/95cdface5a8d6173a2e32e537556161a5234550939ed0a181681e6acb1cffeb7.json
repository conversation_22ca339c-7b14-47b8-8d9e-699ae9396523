{"ast": null, "code": "var eq = require('./eq');\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\nmodule.exports = assocIndexOf;", "map": {"version": 3, "names": ["eq", "require", "assocIndexOf", "array", "key", "length", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_assocIndexOf.js"], "sourcesContent": ["var eq = require('./eq');\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\nmodule.exports = assocIndexOf;\n"], "mappings": "AAAA,IAAIA,EAAE,GAAGC,OAAO,CAAC,MAAM,CAAC;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAChC,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACzB,OAAOA,MAAM,EAAE,EAAE;IACf,IAAIL,EAAE,CAACG,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC,EAAE;MAC7B,OAAOC,MAAM;IACf;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEAC,MAAM,CAACC,OAAO,GAAGL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}