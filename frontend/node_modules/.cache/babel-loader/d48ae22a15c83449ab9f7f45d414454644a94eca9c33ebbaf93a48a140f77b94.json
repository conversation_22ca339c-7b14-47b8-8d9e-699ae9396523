{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { bufferToImage } from './bufferToImage';\nimport { fetchOrThrow } from './fetchOrThrow';\nexport function fetchImage(uri) {\n  return __awaiter(this, void 0, void 0, function () {\n    var res, blob;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          return [4 /*yield*/, fetchOrThrow(uri)];\n        case 1:\n          res = _a.sent();\n          return [4 /*yield*/, res.blob()];\n        case 2:\n          blob = _a.sent();\n          if (!blob.type.startsWith('image/')) {\n            throw new Error(\"fetchImage - expected blob type to be of type image/*, instead have: \" + blob.type + \", for url: \" + res.url);\n          }\n          return [2 /*return*/, bufferToImage(blob)];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["bufferToImage", "fetchOrThrow", "fetchImage", "uri", "res", "_a", "sent", "blob", "type", "startsWith", "Error", "url"], "sources": ["../../../src/dom/fetchImage.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,OAAM,SAAgBC,UAAUA,CAACC,GAAW;;;;;;UAC9B,qBAAMF,YAAY,CAACE,GAAG,CAAC;;UAA7BC,GAAG,GAAGC,EAAA,CAAAC,IAAA,EAAuB;UACtB,qBAAOF,GAAG,CAAEG,IAAI,EAAE;;UAAzBA,IAAI,GAAGF,EAAA,CAAAC,IAAA,EAAkB;UAE/B,IAAI,CAACC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;YACnC,MAAM,IAAIC,KAAK,CAAC,0EAAwEH,IAAI,CAACC,IAAI,mBAAcJ,GAAG,CAACO,GAAK,CAAC;;UAE3H,sBAAOX,aAAa,CAACO,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}