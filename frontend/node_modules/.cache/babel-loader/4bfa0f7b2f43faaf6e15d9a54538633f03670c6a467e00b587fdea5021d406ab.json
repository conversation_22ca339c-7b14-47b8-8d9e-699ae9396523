{"ast": null, "code": "import { extractFCParamsFactory, extractWeightsFactory } from '../common';\nexport function extractParams(weights) {\n  var paramMappings = [];\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var extractFCParams = extractFCParamsFactory(extractWeights, paramMappings);\n  var age = extractFCParams(512, 1, 'fc/age');\n  var gender = extractFCParams(512, 2, 'fc/gender');\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    paramMappings: paramMappings,\n    params: {\n      fc: {\n        age: age,\n        gender: gender\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["extractFCParamsFactory", "extractWeightsFactory", "extractParams", "weights", "paramMappings", "_a", "extractWeights", "getRemainingWeights", "extractFCParams", "age", "gender", "length", "Error", "params", "fc"], "sources": ["../../../src/ageGenderNet/extractParams.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,qBAAqB,QAAsB,WAAW;AAGvF,OAAM,SAAUC,aAAaA,CAACC,OAAqB;EAEjD,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAAC,EAAA,GAAAJ,qBAAA,CAAAE,OAAA,CAG4B;IAFhCG,cAAA,GAAAD,EAAA,CAAAC,cAAc;IACdC,mBAAA,GAAAF,EAAA,CAAAE,mBACgC;EAElC,IAAMC,eAAe,GAAGR,sBAAsB,CAACM,cAAc,EAAEF,aAAa,CAAC;EAE7E,IAAMK,GAAG,GAAGD,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC;EAC7C,IAAME,MAAM,GAAGF,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC;EAEnD,IAAID,mBAAmB,EAAE,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCL,mBAAmB,EAAE,CAACI,MAAQ,CAAC;;EAGnF,OAAO;IACLP,aAAa,EAAAA,aAAA;IACbS,MAAM,EAAE;MAAEC,EAAE,EAAE;QAAEL,GAAG,EAAAA,GAAA;QAAEC,MAAM,EAAAA;MAAA;IAAE;GAC9B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}