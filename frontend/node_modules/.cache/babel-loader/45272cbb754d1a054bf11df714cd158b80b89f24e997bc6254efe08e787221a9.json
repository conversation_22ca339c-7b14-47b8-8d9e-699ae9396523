{"ast": null, "code": "export function extendWithFaceDescriptor(sourceObj, descriptor) {\n  var extension = {\n    descriptor: descriptor\n  };\n  return Object.assign({}, sourceObj, extension);\n}", "map": {"version": 3, "names": ["extendWithFaceDescriptor", "sourceObj", "descriptor", "extension", "Object", "assign"], "sources": ["../../../src/factories/WithFaceDescriptor.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAM,SAAUA,wBAAwBA,CAGtCC,SAAkB,EAClBC,UAAwB;EAGxB,IAAMC,SAAS,GAAG;IAAED,UAAU,EAAAA;EAAA,CAAE;EAChC,OAAOE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEJ,SAAS,EAAEE,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}