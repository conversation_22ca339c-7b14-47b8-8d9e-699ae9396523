{"ast": null, "code": "export function isNodejs() {\n  return typeof global === 'object' && typeof require === 'function' && typeof module !== 'undefined'\n  // issues with gatsby.js: module.exports is undefined\n  // && !!module.exports\n  && typeof process !== 'undefined' && !!process.version;\n}", "map": {"version": 3, "names": ["isNodejs", "global", "require", "module", "process", "version"], "sources": ["../../../src/env/isNodejs.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,QAAQA,CAAA;EACtB,OAAO,OAAOC,MAAM,KAAK,QAAQ,IAC5B,OAAOC,OAAO,KAAK,UAAU,IAC7B,OAAOC,MAAM,KAAK;EACrB;EACA;EAAA,GACG,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAO,CAACC,OAAO;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}