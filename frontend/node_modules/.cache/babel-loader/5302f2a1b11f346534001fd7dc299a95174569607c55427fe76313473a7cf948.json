{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n  isArray = require('./isArray'),\n  isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' || !isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag;\n}\nmodule.exports = isString;", "map": {"version": 3, "names": ["baseGetTag", "require", "isArray", "isObjectLike", "stringTag", "isString", "value", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/isString.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isArray = require('./isArray'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);\n}\n\nmodule.exports = isString;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;EAC9BE,YAAY,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA,IAAIG,SAAS,GAAG,iBAAiB;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,IAAI,QAAQ,IAC5B,CAACJ,OAAO,CAACI,KAAK,CAAC,IAAIH,YAAY,CAACG,KAAK,CAAC,IAAIN,UAAU,CAACM,KAAK,CAAC,IAAIF,SAAU;AAC9E;AAEAG,MAAM,CAACC,OAAO,GAAGH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}