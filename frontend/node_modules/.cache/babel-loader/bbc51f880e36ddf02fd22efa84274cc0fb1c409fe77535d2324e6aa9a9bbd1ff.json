{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function extractFCParamsFactory(extractWeights, paramMappings) {\n  return function (channelsIn, channelsOut, mappedPrefix) {\n    var fc_weights = tf.tensor2d(extractWeights(channelsIn * channelsOut), [channelsIn, channelsOut]);\n    var fc_bias = tf.tensor1d(extractWeights(channelsOut));\n    paramMappings.push({\n      paramPath: mappedPrefix + \"/weights\"\n    }, {\n      paramPath: mappedPrefix + \"/bias\"\n    });\n    return {\n      weights: fc_weights,\n      bias: fc_bias\n    };\n  };\n}", "map": {"version": 3, "names": ["tf", "extractFCParamsFactory", "extractWeights", "paramMappings", "channelsIn", "channelsOut", "mappedPrefix", "fc_weights", "tensor2d", "fc_bias", "tensor1d", "push", "<PERSON><PERSON><PERSON><PERSON>", "weights", "bias"], "sources": ["../../../src/common/extractFCParamsFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAK3C,OAAM,SAAUC,sBAAsBA,CACpCC,cAAsC,EACtCC,aAA6B;EAG7B,OAAO,UACLC,UAAkB,EAClBC,WAAmB,EACnBC,YAAoB;IAGpB,IAAMC,UAAU,GAAGP,EAAE,CAACQ,QAAQ,CAACN,cAAc,CAACE,UAAU,GAAGC,WAAW,CAAC,EAAE,CAACD,UAAU,EAAEC,WAAW,CAAC,CAAC;IACnG,IAAMI,OAAO,GAAGT,EAAE,CAACU,QAAQ,CAACR,cAAc,CAACG,WAAW,CAAC,CAAC;IAExDF,aAAa,CAACQ,IAAI,CAChB;MAAEC,SAAS,EAAKN,YAAY;IAAU,CAAE,EACxC;MAAEM,SAAS,EAAKN,YAAY;IAAO,CAAE,CACtC;IAED,OAAO;MACLO,OAAO,EAAEN,UAAU;MACnBO,IAAI,EAAEL;KACP;EACH,CAAC;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}