{"ast": null, "code": "import { env } from '../env';\nexport function isMediaLoaded(media) {\n  var _a = env.getEnv(),\n    Image = _a.Image,\n    Video = _a.Video;\n  return media instanceof Image && media.complete || media instanceof Video && media.readyState >= 3;\n}", "map": {"version": 3, "names": ["env", "isMediaLoaded", "media", "_a", "getEnv", "Image", "Video", "complete", "readyState"], "sources": ["../../../src/dom/isMediaLoaded.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAE5B,OAAM,SAAUC,aAAaA,CAACC,KAA0C;EAEhE,IAAAC,EAAA,GAAAH,GAAA,CAAAI,MAAA,EAA+B;IAA7BC,KAAA,GAAAF,EAAA,CAAAE,KAAK;IAAEC,KAAA,GAAAH,EAAA,CAAAG,KAAsB;EAErC,OAAQJ,KAAK,YAAYG,KAAK,IAAIH,KAAK,CAACK,QAAQ,IAC1CL,KAAK,YAAYI,KAAK,IAAIJ,KAAK,CAACM,UAAU,IAAI,CAAE;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}