{"ast": null, "code": "export * from './allFaces';\nexport * from './ComposableTask';\nexport * from './ComputeFaceDescriptorsTasks';\nexport * from './detectFaces';\nexport * from './DetectFacesTasks';\nexport * from './DetectFaceLandmarksTasks';\nexport * from './FaceMatcher';\nexport * from './nets';", "map": {"version": 3, "names": [], "sources": ["../../../src/globalApi/index.ts"], "sourcesContent": [null], "mappings": "AAAA,cAAc,YAAY;AAC1B,cAAc,kBAAkB;AAChC,cAAc,+BAA+B;AAC7C,cAAc,eAAe;AAC7B,cAAc,oBAAoB;AAClC,cAAc,4BAA4B;AAC1C,cAAc,eAAe;AAC7B,cAAc,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}