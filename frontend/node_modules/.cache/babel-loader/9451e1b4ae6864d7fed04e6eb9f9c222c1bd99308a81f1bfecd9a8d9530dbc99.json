{"ast": null, "code": "export function disposeUnusedWeightTensors(weightMap, paramMappings) {\n  Object.keys(weightMap).forEach(function (path) {\n    if (!paramMappings.some(function (pm) {\n      return pm.originalPath === path;\n    })) {\n      weightMap[path].dispose();\n    }\n  });\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "weightMap", "paramMappings", "Object", "keys", "for<PERSON>ach", "path", "some", "pm", "originalPath", "dispose"], "sources": ["../../../src/common/disposeUnusedWeightTensors.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,0BAA0BA,CAACC,SAAc,EAAEC,aAA6B;EACtFC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,CAAC,UAAAC,IAAI;IACjC,IAAI,CAACJ,aAAa,CAACK,IAAI,CAAC,UAAAC,EAAE;MAAI,OAAAA,EAAE,CAACC,YAAY,KAAKH,IAAI;IAAxB,CAAwB,CAAC,EAAE;MACvDL,SAAS,CAACK,IAAI,CAAC,CAACI,OAAO,EAAE;;EAE7B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}