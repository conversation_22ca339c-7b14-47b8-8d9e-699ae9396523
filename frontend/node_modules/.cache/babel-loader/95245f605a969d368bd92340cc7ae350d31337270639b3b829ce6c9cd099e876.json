{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Contexte d'authentification pour PresencePro\n */\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { apiService } from '../services/api';\n\n// État initial\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  permissions: [],\n  isLoading: false,\n  isInitialized: false\n};\n\n// Actions\n\n// Reducer\nfunction authReducer(state, action) {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        permissions: action.payload.permissions,\n        isLoading: false,\n        isInitialized: true\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        permissions: [],\n        isLoading: false\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: state.user ? {\n          ...state.user,\n          ...action.payload\n        } : null\n      };\n    case 'SET_INITIALIZED':\n      return {\n        ...state,\n        isInitialized: action.payload\n      };\n    default:\n      return state;\n  }\n}\n\n// Contexte\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('user');\n      if (token && userData) {\n        try {\n          // Vérifier si le token est toujours valide\n          const response = await apiService.getCurrentUser();\n          if (response.success && response.data) {\n            dispatch({\n              type: 'LOGIN_SUCCESS',\n              payload: {\n                user: response.data,\n                token,\n                permissions: JSON.parse(localStorage.getItem('permissions') || '[]')\n              }\n            });\n          } else {\n            // Token invalide, nettoyer le localStorage\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            localStorage.removeItem('permissions');\n          }\n        } catch (error) {\n          // Erreur de vérification, nettoyer le localStorage\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('user');\n          localStorage.removeItem('permissions');\n        }\n      }\n      dispatch({\n        type: 'SET_INITIALIZED',\n        payload: true\n      });\n    };\n    initializeAuth();\n  }, []);\n\n  // Fonction de connexion\n  const login = async credentials => {\n    dispatch({\n      type: 'SET_LOADING',\n      payload: true\n    });\n    try {\n      const response = await apiService.login(credentials);\n      if (response.success && response.user && response.token) {\n        // Sauvegarder dans le localStorage\n        localStorage.setItem('authToken', response.token);\n        localStorage.setItem('user', JSON.stringify(response.user));\n        localStorage.setItem('permissions', JSON.stringify(response.permissions || []));\n        dispatch({\n          type: 'LOGIN_SUCCESS',\n          payload: {\n            user: response.user,\n            token: response.token,\n            permissions: response.permissions || []\n          }\n        });\n        return true;\n      } else {\n        dispatch({\n          type: 'SET_LOADING',\n          payload: false\n        });\n        return false;\n      }\n    } catch (error) {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n      throw error;\n    }\n  };\n\n  // Fonction de déconnexion\n  const logout = async () => {\n    try {\n      await apiService.logout();\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    } finally {\n      // Nettoyer le localStorage\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('user');\n      localStorage.removeItem('permissions');\n      dispatch({\n        type: 'LOGOUT'\n      });\n    }\n  };\n\n  // Fonction de mise à jour de l'utilisateur\n  const updateUser = userData => {\n    dispatch({\n      type: 'UPDATE_USER',\n      payload: userData\n    });\n\n    // Mettre à jour le localStorage\n    if (state.user) {\n      const updatedUser = {\n        ...state.user,\n        ...userData\n      };\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n    }\n  };\n\n  // Fonction pour vérifier les permissions\n  const hasPermission = permission => {\n    return state.permissions.includes(permission);\n  };\n  const value = {\n    user: state.user,\n    token: state.token,\n    permissions: state.permissions,\n    isLoading: state.isLoading,\n    login,\n    logout,\n    updateUser,\n    hasPermission\n  };\n\n  // Ne pas rendre les enfants tant que l'initialisation n'est pas terminée\n  if (!state.isInitialized) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      },\n      children: \"Chargement...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "permissions", "isLoading", "isInitialized", "authReducer", "state", "action", "type", "payload", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "localStorage", "getItem", "userData", "response", "getCurrentUser", "success", "data", "JSON", "parse", "removeItem", "error", "login", "credentials", "setItem", "stringify", "logout", "console", "updateUser", "updatedUser", "hasPermission", "permission", "includes", "value", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Provider", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["/**\n * Contexte d'authentification pour PresencePro\n */\n\nimport React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { User, LoginCredentials, AuthContextType } from '../types';\nimport { apiService } from '../services/api';\nimport { useFirebase } from '../hooks/useFirebase';\n\n// État initial\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  permissions: string[];\n  isLoading: boolean;\n  isInitialized: boolean;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  permissions: [],\n  isLoading: false,\n  isInitialized: false,\n};\n\n// Actions\ntype AuthAction =\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string; permissions: string[] } }\n  | { type: 'LOGOUT' }\n  | { type: 'UPDATE_USER'; payload: Partial<User> }\n  | { type: 'SET_INITIALIZED'; payload: boolean };\n\n// Reducer\nfunction authReducer(state: AuthState, action: AuthAction): AuthState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    \n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        permissions: action.payload.permissions,\n        isLoading: false,\n        isInitialized: true,\n      };\n    \n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        permissions: [],\n        isLoading: false,\n      };\n    \n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: state.user ? { ...state.user, ...action.payload } : null,\n      };\n    \n    case 'SET_INITIALIZED':\n      return { ...state, isInitialized: action.payload };\n    \n    default:\n      return state;\n  }\n}\n\n// Contexte\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Provider\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('user');\n\n      if (token && userData) {\n        try {\n          // Vérifier si le token est toujours valide\n          const response = await apiService.getCurrentUser();\n          if (response.success && response.data) {\n            dispatch({\n              type: 'LOGIN_SUCCESS',\n              payload: {\n                user: response.data,\n                token,\n                permissions: JSON.parse(localStorage.getItem('permissions') || '[]'),\n              },\n            });\n          } else {\n            // Token invalide, nettoyer le localStorage\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            localStorage.removeItem('permissions');\n          }\n        } catch (error) {\n          // Erreur de vérification, nettoyer le localStorage\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('user');\n          localStorage.removeItem('permissions');\n        }\n      }\n      \n      dispatch({ type: 'SET_INITIALIZED', payload: true });\n    };\n\n    initializeAuth();\n  }, []);\n\n  // Fonction de connexion\n  const login = async (credentials: LoginCredentials): Promise<boolean> => {\n    dispatch({ type: 'SET_LOADING', payload: true });\n\n    try {\n      const response = await apiService.login(credentials);\n      \n      if (response.success && response.user && response.token) {\n        // Sauvegarder dans le localStorage\n        localStorage.setItem('authToken', response.token);\n        localStorage.setItem('user', JSON.stringify(response.user));\n        localStorage.setItem('permissions', JSON.stringify(response.permissions || []));\n\n        dispatch({\n          type: 'LOGIN_SUCCESS',\n          payload: {\n            user: response.user,\n            token: response.token,\n            permissions: response.permissions || [],\n          },\n        });\n\n        return true;\n      } else {\n        dispatch({ type: 'SET_LOADING', payload: false });\n        return false;\n      }\n    } catch (error) {\n      dispatch({ type: 'SET_LOADING', payload: false });\n      throw error;\n    }\n  };\n\n  // Fonction de déconnexion\n  const logout = async () => {\n    try {\n      await apiService.logout();\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    } finally {\n      // Nettoyer le localStorage\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('user');\n      localStorage.removeItem('permissions');\n\n      dispatch({ type: 'LOGOUT' });\n    }\n  };\n\n  // Fonction de mise à jour de l'utilisateur\n  const updateUser = (userData: Partial<User>) => {\n    dispatch({ type: 'UPDATE_USER', payload: userData });\n    \n    // Mettre à jour le localStorage\n    if (state.user) {\n      const updatedUser = { ...state.user, ...userData };\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n    }\n  };\n\n  // Fonction pour vérifier les permissions\n  const hasPermission = (permission: string): boolean => {\n    return state.permissions.includes(permission);\n  };\n\n  const value: AuthContextType = {\n    user: state.user,\n    token: state.token,\n    permissions: state.permissions,\n    isLoading: state.isLoading,\n    login,\n    logout,\n    updateUser,\n    hasPermission,\n  };\n\n  // Ne pas rendre les enfants tant que l'initialisation n'est pas terminée\n  if (!state.isInitialized) {\n    return (\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        alignItems: 'center', \n        height: '100vh',\n        fontSize: '18px'\n      }}>\n        Chargement...\n      </div>\n    );\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAE1F,SAASC,UAAU,QAAQ,iBAAiB;;AAG5C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,KAAK;EAChBC,aAAa,EAAE;AACjB,CAAC;;AAED;;AAQA;AACA,SAASC,WAAWA,CAACC,KAAgB,EAAEC,MAAkB,EAAa;EACpE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGF,KAAK;QAAEH,SAAS,EAAEI,MAAM,CAACE;MAAQ,CAAC;IAEhD,KAAK,eAAe;MAClB,OAAO;QACL,GAAGH,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE,OAAO,CAACT,IAAI;QACzBC,KAAK,EAAEM,MAAM,CAACE,OAAO,CAACR,KAAK;QAC3BC,WAAW,EAAEK,MAAM,CAACE,OAAO,CAACP,WAAW;QACvCC,SAAS,EAAE,KAAK;QAChBC,aAAa,EAAE;MACjB,CAAC;IAEH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGG,KAAK;QACRN,IAAI,EAAEM,KAAK,CAACN,IAAI,GAAG;UAAE,GAAGM,KAAK,CAACN,IAAI;UAAE,GAAGO,MAAM,CAACE;QAAQ,CAAC,GAAG;MAC5D,CAAC;IAEH,KAAK,iBAAiB;MACpB,OAAO;QAAE,GAAGH,KAAK;QAAEF,aAAa,EAAEG,MAAM,CAACE;MAAQ,CAAC;IAEpD;MACE,OAAOH,KAAK;EAChB;AACF;;AAEA;AACA,MAAMI,WAAW,gBAAGlB,aAAa,CAA8BmB,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGrB,UAAU,CAACW,WAAW,EAAEN,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMqB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMf,KAAK,GAAGgB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE7C,IAAIjB,KAAK,IAAIkB,QAAQ,EAAE;QACrB,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,MAAMxB,UAAU,CAACyB,cAAc,CAAC,CAAC;UAClD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;YACrCR,QAAQ,CAAC;cACPP,IAAI,EAAE,eAAe;cACrBC,OAAO,EAAE;gBACPT,IAAI,EAAEoB,QAAQ,CAACG,IAAI;gBACnBtB,KAAK;gBACLC,WAAW,EAAEsB,IAAI,CAACC,KAAK,CAACR,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI;cACrE;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAD,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;YACpCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;YAC/BT,YAAY,CAACS,UAAU,CAAC,aAAa,CAAC;UACxC;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd;UACAV,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;UACpCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;UAC/BT,YAAY,CAACS,UAAU,CAAC,aAAa,CAAC;QACxC;MACF;MAEAX,QAAQ,CAAC;QAAEP,IAAI,EAAE,iBAAiB;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACtD,CAAC;IAEDO,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,KAAK,GAAG,MAAOC,WAA6B,IAAuB;IACvEd,QAAQ,CAAC;MAAEP,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhD,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMxB,UAAU,CAACgC,KAAK,CAACC,WAAW,CAAC;MAEpD,IAAIT,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACpB,IAAI,IAAIoB,QAAQ,CAACnB,KAAK,EAAE;QACvD;QACAgB,YAAY,CAACa,OAAO,CAAC,WAAW,EAAEV,QAAQ,CAACnB,KAAK,CAAC;QACjDgB,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEN,IAAI,CAACO,SAAS,CAACX,QAAQ,CAACpB,IAAI,CAAC,CAAC;QAC3DiB,YAAY,CAACa,OAAO,CAAC,aAAa,EAAEN,IAAI,CAACO,SAAS,CAACX,QAAQ,CAAClB,WAAW,IAAI,EAAE,CAAC,CAAC;QAE/Ea,QAAQ,CAAC;UACPP,IAAI,EAAE,eAAe;UACrBC,OAAO,EAAE;YACPT,IAAI,EAAEoB,QAAQ,CAACpB,IAAI;YACnBC,KAAK,EAAEmB,QAAQ,CAACnB,KAAK;YACrBC,WAAW,EAAEkB,QAAQ,CAAClB,WAAW,IAAI;UACvC;QACF,CAAC,CAAC;QAEF,OAAO,IAAI;MACb,CAAC,MAAM;QACLa,QAAQ,CAAC;UAAEP,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACjD,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdZ,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjD,MAAMkB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMK,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMpC,UAAU,CAACoC,MAAM,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACR;MACAV,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;MACpCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;MAC/BT,YAAY,CAACS,UAAU,CAAC,aAAa,CAAC;MAEtCX,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAS,CAAC,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAIf,QAAuB,IAAK;IAC9CJ,QAAQ,CAAC;MAAEP,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEU;IAAS,CAAC,CAAC;;IAEpD;IACA,IAAIb,KAAK,CAACN,IAAI,EAAE;MACd,MAAMmC,WAAW,GAAG;QAAE,GAAG7B,KAAK,CAACN,IAAI;QAAE,GAAGmB;MAAS,CAAC;MAClDF,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEN,IAAI,CAACO,SAAS,CAACI,WAAW,CAAC,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIC,UAAkB,IAAc;IACrD,OAAO/B,KAAK,CAACJ,WAAW,CAACoC,QAAQ,CAACD,UAAU,CAAC;EAC/C,CAAC;EAED,MAAME,KAAsB,GAAG;IAC7BvC,IAAI,EAAEM,KAAK,CAACN,IAAI;IAChBC,KAAK,EAAEK,KAAK,CAACL,KAAK;IAClBC,WAAW,EAAEI,KAAK,CAACJ,WAAW;IAC9BC,SAAS,EAAEG,KAAK,CAACH,SAAS;IAC1ByB,KAAK;IACLI,MAAM;IACNE,UAAU;IACVE;EACF,CAAC;;EAED;EACA,IAAI,CAAC9B,KAAK,CAACF,aAAa,EAAE;IACxB,oBACEN,OAAA;MAAK0C,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAhC,QAAA,EAAC;IAEH;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACEnD,OAAA,CAACY,WAAW,CAACwC,QAAQ;IAACX,KAAK,EAAEA,KAAM;IAAA1B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAnC,EAAA,CA5IaF,YAAyC;AAAAuC,EAAA,GAAzCvC,YAAyC;AA6ItD,OAAO,MAAMwC,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAG7D,UAAU,CAACiB,WAAW,CAAC;EACvC,IAAI4C,OAAO,KAAK3C,SAAS,EAAE;IACzB,MAAM,IAAI4C,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe1C,WAAW;AAAC,IAAAyC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}