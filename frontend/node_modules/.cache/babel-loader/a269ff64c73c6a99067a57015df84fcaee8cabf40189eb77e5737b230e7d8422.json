{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function scale(x, params) {\n  return tf.add(tf.mul(x, params.weights), params.biases);\n}", "map": {"version": 3, "names": ["tf", "scale", "x", "params", "add", "mul", "weights", "biases"], "sources": ["../../../src/faceRecognitionNet/scaleLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAI3C,OAAM,SAAUC,KAAKA,CAACC,CAAc,EAAEC,MAAwB;EAC5D,OAAOH,EAAE,CAACI,GAAG,CAACJ,EAAE,CAACK,GAAG,CAACH,CAAC,EAAEC,MAAM,CAACG,OAAO,CAAC,EAAEH,MAAM,CAACI,MAAM,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}