{"ast": null, "code": "export var Gender;\n(function (Gender) {\n  Gender[\"FEMALE\"] = \"female\";\n  Gender[\"MALE\"] = \"male\";\n})(Gender || (Gender = {}));", "map": {"version": 3, "names": ["Gender"], "sources": ["../../../src/ageGenderNet/types.ts"], "sourcesContent": [null], "mappings": "AAUA,WAAYA,MAGX;AAHD,WAAYA,MAAM;EAChBA,MAAA,qBAAiB;EACjBA,MAAA,iBAAa;AACf,CAAC,EAHWA,MAAM,KAANA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}