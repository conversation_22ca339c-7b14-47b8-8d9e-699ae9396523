{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { disposeUnusedWeightTensors, extractWeightEntryFactory } from '../common';\nfunction extractorsFactory(weightMap, paramMappings) {\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  function extractConvParams(prefix) {\n    var filters = extractWeightEntry(prefix + \"/weights\", 4, prefix + \"/filters\");\n    var bias = extractWeightEntry(prefix + \"/bias\", 1);\n    return {\n      filters: filters,\n      bias: bias\n    };\n  }\n  function extractFCParams(prefix) {\n    var weights = extractWeightEntry(prefix + \"/weights\", 2);\n    var bias = extractWeightEntry(prefix + \"/bias\", 1);\n    return {\n      weights: weights,\n      bias: bias\n    };\n  }\n  function extractPReluParams(paramPath) {\n    return extractWeightEntry(paramPath, 1);\n  }\n  function extractSharedParams(prefix) {\n    var conv1 = extractConvParams(prefix + \"/conv1\");\n    var prelu1_alpha = extractPReluParams(prefix + \"/prelu1_alpha\");\n    var conv2 = extractConvParams(prefix + \"/conv2\");\n    var prelu2_alpha = extractPReluParams(prefix + \"/prelu2_alpha\");\n    var conv3 = extractConvParams(prefix + \"/conv3\");\n    var prelu3_alpha = extractPReluParams(prefix + \"/prelu3_alpha\");\n    return {\n      conv1: conv1,\n      prelu1_alpha: prelu1_alpha,\n      conv2: conv2,\n      prelu2_alpha: prelu2_alpha,\n      conv3: conv3,\n      prelu3_alpha: prelu3_alpha\n    };\n  }\n  function extractPNetParams() {\n    var sharedParams = extractSharedParams('pnet');\n    var conv4_1 = extractConvParams('pnet/conv4_1');\n    var conv4_2 = extractConvParams('pnet/conv4_2');\n    return __assign(__assign({}, sharedParams), {\n      conv4_1: conv4_1,\n      conv4_2: conv4_2\n    });\n  }\n  function extractRNetParams() {\n    var sharedParams = extractSharedParams('rnet');\n    var fc1 = extractFCParams('rnet/fc1');\n    var prelu4_alpha = extractPReluParams('rnet/prelu4_alpha');\n    var fc2_1 = extractFCParams('rnet/fc2_1');\n    var fc2_2 = extractFCParams('rnet/fc2_2');\n    return __assign(__assign({}, sharedParams), {\n      fc1: fc1,\n      prelu4_alpha: prelu4_alpha,\n      fc2_1: fc2_1,\n      fc2_2: fc2_2\n    });\n  }\n  function extractONetParams() {\n    var sharedParams = extractSharedParams('onet');\n    var conv4 = extractConvParams('onet/conv4');\n    var prelu4_alpha = extractPReluParams('onet/prelu4_alpha');\n    var fc1 = extractFCParams('onet/fc1');\n    var prelu5_alpha = extractPReluParams('onet/prelu5_alpha');\n    var fc2_1 = extractFCParams('onet/fc2_1');\n    var fc2_2 = extractFCParams('onet/fc2_2');\n    var fc2_3 = extractFCParams('onet/fc2_3');\n    return __assign(__assign({}, sharedParams), {\n      conv4: conv4,\n      prelu4_alpha: prelu4_alpha,\n      fc1: fc1,\n      prelu5_alpha: prelu5_alpha,\n      fc2_1: fc2_1,\n      fc2_2: fc2_2,\n      fc2_3: fc2_3\n    });\n  }\n  return {\n    extractPNetParams: extractPNetParams,\n    extractRNetParams: extractRNetParams,\n    extractONetParams: extractONetParams\n  };\n}\nexport function extractParamsFromWeigthMap(weightMap) {\n  var paramMappings = [];\n  var _a = extractorsFactory(weightMap, paramMappings),\n    extractPNetParams = _a.extractPNetParams,\n    extractRNetParams = _a.extractRNetParams,\n    extractONetParams = _a.extractONetParams;\n  var pnet = extractPNetParams();\n  var rnet = extractRNetParams();\n  var onet = extractONetParams();\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: {\n      pnet: pnet,\n      rnet: rnet,\n      onet: onet\n    },\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "extractWeightEntryFactory", "extractorsFactory", "weightMap", "paramMappings", "extractWeightEntry", "extractConvParams", "prefix", "filters", "bias", "extractFCParams", "weights", "extractPReluParams", "<PERSON><PERSON><PERSON><PERSON>", "extractSharedParams", "conv1", "prelu1_alpha", "conv2", "prelu2_alpha", "conv3", "prelu3_alpha", "extractPNetParams", "sharedParams", "conv4_1", "conv4_2", "__assign", "extractRNetParams", "fc1", "prelu4_alpha", "fc2_1", "fc2_2", "extractONetParams", "conv4", "prelu5_alpha", "fc2_3", "extractParamsFromWeigthMap", "_a", "pnet", "rnet", "onet", "params"], "sources": ["../../../src/mtcnn/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": ";AAEA,SAAqBA,0BAA0B,EAAEC,yBAAyB,QAAgC,WAAW;AAGrH,SAASC,iBAAiBA,CAACC,SAAc,EAAEC,aAA6B;EAEtE,IAAMC,kBAAkB,GAAGJ,yBAAyB,CAACE,SAAS,EAAEC,aAAa,CAAC;EAE9E,SAASE,iBAAiBA,CAACC,MAAc;IACvC,IAAMC,OAAO,GAAGH,kBAAkB,CAAiBE,MAAM,aAAU,EAAE,CAAC,EAAKA,MAAM,aAAU,CAAC;IAC5F,IAAME,IAAI,GAAGJ,kBAAkB,CAAiBE,MAAM,UAAO,EAAE,CAAC,CAAC;IAEjE,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;EAC1B;EAEA,SAASC,eAAeA,CAACH,MAAc;IACrC,IAAMI,OAAO,GAAGN,kBAAkB,CAAiBE,MAAM,aAAU,EAAE,CAAC,CAAC;IACvE,IAAME,IAAI,GAAGJ,kBAAkB,CAAiBE,MAAM,UAAO,EAAE,CAAC,CAAC;IAEjE,OAAO;MAAEI,OAAO,EAAAA,OAAA;MAAEF,IAAI,EAAAA;IAAA,CAAE;EAC1B;EAEA,SAASG,kBAAkBA,CAACC,SAAiB;IAC3C,OAAOR,kBAAkB,CAAcQ,SAAS,EAAE,CAAC,CAAC;EACtD;EAEA,SAASC,mBAAmBA,CAACP,MAAc;IAEzC,IAAMQ,KAAK,GAAGT,iBAAiB,CAAIC,MAAM,WAAQ,CAAC;IAClD,IAAMS,YAAY,GAAGJ,kBAAkB,CAAIL,MAAM,kBAAe,CAAC;IACjE,IAAMU,KAAK,GAAGX,iBAAiB,CAAIC,MAAM,WAAQ,CAAC;IAClD,IAAMW,YAAY,GAAGN,kBAAkB,CAAIL,MAAM,kBAAe,CAAC;IACjE,IAAMY,KAAK,GAAGb,iBAAiB,CAAIC,MAAM,WAAQ,CAAC;IAClD,IAAMa,YAAY,GAAGR,kBAAkB,CAAIL,MAAM,kBAAe,CAAC;IAEjE,OAAO;MAAEQ,KAAK,EAAAA,KAAA;MAAEC,YAAY,EAAAA,YAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,YAAY,EAAAA,YAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,YAAY,EAAAA;IAAA,CAAE;EAC1E;EAEA,SAASC,iBAAiBA,CAAA;IAExB,IAAMC,YAAY,GAAGR,mBAAmB,CAAC,MAAM,CAAC;IAChD,IAAMS,OAAO,GAAGjB,iBAAiB,CAAC,cAAc,CAAC;IACjD,IAAMkB,OAAO,GAAGlB,iBAAiB,CAAC,cAAc,CAAC;IAEjD,OAAAmB,QAAA,CAAAA,QAAA,KAAYH,YAAY;MAAEC,OAAO,EAAAA,OAAA;MAAEC,OAAO,EAAAA;IAAA;EAC5C;EAEA,SAASE,iBAAiBA,CAAA;IAExB,IAAMJ,YAAY,GAAGR,mBAAmB,CAAC,MAAM,CAAC;IAChD,IAAMa,GAAG,GAAGjB,eAAe,CAAC,UAAU,CAAC;IACvC,IAAMkB,YAAY,GAAGhB,kBAAkB,CAAC,mBAAmB,CAAC;IAC5D,IAAMiB,KAAK,GAAGnB,eAAe,CAAC,YAAY,CAAC;IAC3C,IAAMoB,KAAK,GAAGpB,eAAe,CAAC,YAAY,CAAC;IAE3C,OAAAe,QAAA,CAAAA,QAAA,KAAYH,YAAY;MAAEK,GAAG,EAAAA,GAAA;MAAEC,YAAY,EAAAA,YAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA;EAC3D;EAEA,SAASC,iBAAiBA,CAAA;IAExB,IAAMT,YAAY,GAAGR,mBAAmB,CAAC,MAAM,CAAC;IAChD,IAAMkB,KAAK,GAAG1B,iBAAiB,CAAC,YAAY,CAAC;IAC7C,IAAMsB,YAAY,GAAGhB,kBAAkB,CAAC,mBAAmB,CAAC;IAC5D,IAAMe,GAAG,GAAGjB,eAAe,CAAC,UAAU,CAAC;IACvC,IAAMuB,YAAY,GAAGrB,kBAAkB,CAAC,mBAAmB,CAAC;IAC5D,IAAMiB,KAAK,GAAGnB,eAAe,CAAC,YAAY,CAAC;IAC3C,IAAMoB,KAAK,GAAGpB,eAAe,CAAC,YAAY,CAAC;IAC3C,IAAMwB,KAAK,GAAGxB,eAAe,CAAC,YAAY,CAAC;IAE3C,OAAAe,QAAA,CAAAA,QAAA,KAAYH,YAAY;MAAEU,KAAK,EAAAA,KAAA;MAAEJ,YAAY,EAAAA,YAAA;MAAED,GAAG,EAAAA,GAAA;MAAEM,YAAY,EAAAA,YAAA;MAAEJ,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEI,KAAK,EAAAA;IAAA;EACvF;EAEA,OAAO;IACLb,iBAAiB,EAAAA,iBAAA;IACjBK,iBAAiB,EAAAA,iBAAA;IACjBK,iBAAiB,EAAAA;GAClB;AAEH;AAEA,OAAM,SAAUI,0BAA0BA,CACxChC,SAA4B;EAG5B,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAAgC,EAAA,GAAAlC,iBAAA,CAAAC,SAAA,EAAAC,aAAA,CAIyC;IAH7CiB,iBAAA,GAAAe,EAAA,CAAAf,iBAAiB;IACjBK,iBAAA,GAAAU,EAAA,CAAAV,iBAAiB;IACjBK,iBAAA,GAAAK,EAAA,CAAAL,iBAC6C;EAE/C,IAAMM,IAAI,GAAGhB,iBAAiB,EAAE;EAChC,IAAMiB,IAAI,GAAGZ,iBAAiB,EAAE;EAChC,IAAMa,IAAI,GAAGR,iBAAiB,EAAE;EAEhC/B,0BAA0B,CAACG,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAEoC,MAAM,EAAE;MAAEH,IAAI,EAAAA,IAAA;MAAEC,IAAI,EAAAA,IAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;IAAEnC,aAAa,EAAAA;EAAA,CAAE;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}