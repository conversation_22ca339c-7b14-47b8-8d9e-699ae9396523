{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\nmodule.exports = hashHas;", "map": {"version": 3, "names": ["nativeCreate", "require", "objectProto", "Object", "prototype", "hasOwnProperty", "hashHas", "key", "data", "__data__", "undefined", "call", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_hashHas.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\nmodule.exports = hashHas;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;EACxB,OAAOT,YAAY,GAAIQ,IAAI,CAACD,GAAG,CAAC,KAAKG,SAAS,GAAIL,cAAc,CAACM,IAAI,CAACH,IAAI,EAAED,GAAG,CAAC;AAClF;AAEAK,MAAM,CAACC,OAAO,GAAGP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}