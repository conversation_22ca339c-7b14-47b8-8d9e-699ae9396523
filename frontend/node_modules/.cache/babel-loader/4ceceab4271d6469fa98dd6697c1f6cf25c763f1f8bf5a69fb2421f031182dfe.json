{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { depthwiseSeparableConv } from '../common';\nimport { toNetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { normalize } from '../ops';\nimport { range } from '../utils';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nfunction conv(x, params, stride) {\n  return tf.add(tf.conv2d(x, params.filters, stride, 'same'), params.bias);\n}\nfunction reductionBlock(x, params, isActivateInput) {\n  if (isActivateInput === void 0) {\n    isActivateInput = true;\n  }\n  var out = isActivateInput ? tf.relu(x) : x;\n  out = depthwiseSeparableConv(out, params.separable_conv0, [1, 1]);\n  out = depthwiseSeparableConv(tf.relu(out), params.separable_conv1, [1, 1]);\n  out = tf.maxPool(out, [3, 3], [2, 2], 'same');\n  out = tf.add(out, conv(x, params.expansion_conv, [2, 2]));\n  return out;\n}\nfunction mainBlock(x, params) {\n  var out = depthwiseSeparableConv(tf.relu(x), params.separable_conv0, [1, 1]);\n  out = depthwiseSeparableConv(tf.relu(out), params.separable_conv1, [1, 1]);\n  out = depthwiseSeparableConv(tf.relu(out), params.separable_conv2, [1, 1]);\n  out = tf.add(out, x);\n  return out;\n}\nvar TinyXception = /** @class */function (_super) {\n  __extends(TinyXception, _super);\n  function TinyXception(numMainBlocks) {\n    var _this = _super.call(this, 'TinyXception') || this;\n    _this._numMainBlocks = numMainBlocks;\n    return _this;\n  }\n  TinyXception.prototype.forwardInput = function (input) {\n    var _this = this;\n    var params = this.params;\n    if (!params) {\n      throw new Error('TinyXception - load model before inference');\n    }\n    return tf.tidy(function () {\n      var batchTensor = input.toBatchTensor(112, true);\n      var meanRgb = [122.782, 117.001, 104.298];\n      var normalized = normalize(batchTensor, meanRgb).div(tf.scalar(256));\n      var out = tf.relu(conv(normalized, params.entry_flow.conv_in, [2, 2]));\n      out = reductionBlock(out, params.entry_flow.reduction_block_0, false);\n      out = reductionBlock(out, params.entry_flow.reduction_block_1);\n      range(_this._numMainBlocks, 0, 1).forEach(function (idx) {\n        out = mainBlock(out, params.middle_flow[\"main_block_\" + idx]);\n      });\n      out = reductionBlock(out, params.exit_flow.reduction_block);\n      out = tf.relu(depthwiseSeparableConv(out, params.exit_flow.separable_conv, [1, 1]));\n      return out;\n    });\n  };\n  TinyXception.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  TinyXception.prototype.getDefaultModelName = function () {\n    return 'tiny_xception_model';\n  };\n  TinyXception.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMap(weightMap, this._numMainBlocks);\n  };\n  TinyXception.prototype.extractParams = function (weights) {\n    return extractParams(weights, this._numMainBlocks);\n  };\n  return TinyXception;\n}(NeuralNetwork);\nexport { TinyXception };", "map": {"version": 3, "names": ["tf", "depthwiseSeparableConv", "toNetInput", "NeuralNetwork", "normalize", "range", "extractParams", "extractParamsFromWeigthMap", "conv", "x", "params", "stride", "add", "conv2d", "filters", "bias", "reductionBlock", "isActivateInput", "out", "relu", "separable_conv0", "separable_conv1", "maxPool", "expansion_conv", "mainBlock", "separable_conv2", "TinyXception", "_super", "__extends", "numMainBlocks", "_this", "call", "_numMainBlocks", "prototype", "forwardInput", "input", "Error", "tidy", "batchTensor", "toBatchTensor", "meanRgb", "normalized", "div", "scalar", "entry_flow", "conv_in", "reduction_block_0", "reduction_block_1", "for<PERSON>ach", "idx", "middle_flow", "exit_flow", "reduction_block", "separable_conv", "forward", "_a", "apply", "_b", "sent", "getDefaultModelName", "weightMap", "weights"], "sources": ["../../../src/xception/TinyXception.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAAqBC,sBAAsB,QAAQ,WAAW;AAC9D,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AAGzE,SAASC,IAAIA,CAACC,CAAc,EAAEC,MAAkB,EAAEC,MAAwB;EACxE,OAAOX,EAAE,CAACY,GAAG,CAACZ,EAAE,CAACa,MAAM,CAACJ,CAAC,EAAEC,MAAM,CAACI,OAAO,EAAEH,MAAM,EAAE,MAAM,CAAC,EAAED,MAAM,CAACK,IAAI,CAAC;AAC1E;AAEA,SAASC,cAAcA,CAACP,CAAc,EAAEC,MAA4B,EAAEO,eAA+B;EAA/B,IAAAA,eAAA;IAAAA,eAAA,OAA+B;EAAA;EACnG,IAAIC,GAAG,GAAGD,eAAe,GAAGjB,EAAE,CAACmB,IAAI,CAACV,CAAC,CAAC,GAAGA,CAAC;EAC1CS,GAAG,GAAGjB,sBAAsB,CAACiB,GAAG,EAAER,MAAM,CAACU,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjEF,GAAG,GAAGjB,sBAAsB,CAACD,EAAE,CAACmB,IAAI,CAACD,GAAG,CAAC,EAAGR,MAAM,CAACW,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3EH,GAAG,GAAGlB,EAAE,CAACsB,OAAO,CAACJ,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EAC7CA,GAAG,GAAGlB,EAAE,CAACY,GAAG,CAACM,GAAG,EAAEV,IAAI,CAACC,CAAC,EAAGC,MAAM,CAACa,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1D,OAAOL,GAAG;AACZ;AAEA,SAASM,SAASA,CAACf,CAAc,EAAEC,MAAuB;EACxD,IAAIQ,GAAG,GAAGjB,sBAAsB,CAACD,EAAE,CAACmB,IAAI,CAACV,CAAC,CAAC,EAAEC,MAAM,CAACU,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5EF,GAAG,GAAGjB,sBAAsB,CAACD,EAAE,CAACmB,IAAI,CAACD,GAAG,CAAC,EAAER,MAAM,CAACW,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1EH,GAAG,GAAGjB,sBAAsB,CAACD,EAAE,CAACmB,IAAI,CAACD,GAAG,CAAC,EAAER,MAAM,CAACe,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1EP,GAAG,GAAGlB,EAAE,CAACY,GAAG,CAACM,GAAG,EAAET,CAAC,CAAC;EACpB,OAAOS,GAAG;AACZ;AAEA,IAAAQ,YAAA,0BAAAC,MAAA;EAAkCC,SAAA,CAAAF,YAAA,EAAAC,MAAA;EAIhC,SAAAD,aAAYG,aAAqB;IAAjC,IAAAC,KAAA,GACEH,MAAA,CAAAI,IAAA,OAAM,cAAc,CAAC;IACrBD,KAAI,CAACE,cAAc,GAAGH,aAAa;;EACrC;EAEOH,YAAA,CAAAO,SAAA,CAAAC,YAAY,GAAnB,UAAoBC,KAAe;IAAnC,IAAAL,KAAA;IAEU,IAAApB,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAI0B,KAAK,CAAC,4CAA4C,CAAC;;IAG/D,OAAOpC,EAAE,CAACqC,IAAI,CAAC;MACb,IAAMC,WAAW,GAAGH,KAAK,CAACI,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC;MAClD,IAAMC,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC3C,IAAMC,UAAU,GAAGrC,SAAS,CAACkC,WAAW,EAAEE,OAAO,CAAC,CAACE,GAAG,CAAC1C,EAAE,CAAC2C,MAAM,CAAC,GAAG,CAAC,CAAgB;MAErF,IAAIzB,GAAG,GAAGlB,EAAE,CAACmB,IAAI,CAACX,IAAI,CAACiC,UAAU,EAAE/B,MAAM,CAACkC,UAAU,CAACC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtE3B,GAAG,GAAGF,cAAc,CAACE,GAAG,EAAER,MAAM,CAACkC,UAAU,CAACE,iBAAiB,EAAE,KAAK,CAAC;MACrE5B,GAAG,GAAGF,cAAc,CAACE,GAAG,EAAER,MAAM,CAACkC,UAAU,CAACG,iBAAiB,CAAC;MAE9D1C,KAAK,CAACyB,KAAI,CAACE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC,CAACgB,OAAO,CAAC,UAACC,GAAG;QAC3C/B,GAAG,GAAGM,SAAS,CAACN,GAAG,EAAER,MAAM,CAACwC,WAAW,CAAC,gBAAcD,GAAK,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEF/B,GAAG,GAAGF,cAAc,CAACE,GAAG,EAAER,MAAM,CAACyC,SAAS,CAACC,eAAe,CAAC;MAC3DlC,GAAG,GAAGlB,EAAE,CAACmB,IAAI,CAAClB,sBAAsB,CAACiB,GAAG,EAAER,MAAM,CAACyC,SAAS,CAACE,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnF,OAAOnC,GAAG;IACZ,CAAC,CAAC;EACJ,CAAC;EAEYQ,YAAA,CAAAO,SAAA,CAAAqB,OAAO,GAApB,UAAqBnB,KAAgB;;;;;;YAC5BoB,EAAA,OAAI,CAACrB,YAAY;YAAC,qBAAMhC,UAAU,CAACiC,KAAK,CAAC;;YAAhD,sBAAOoB,EAAA,CAAAC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAEShC,YAAA,CAAAO,SAAA,CAAA0B,mBAAmB,GAA7B;IACE,OAAO,qBAAqB;EAC9B,CAAC;EAESjC,YAAA,CAAAO,SAAA,CAAA1B,0BAA0B,GAApC,UAAqCqD,SAA4B;IAC/D,OAAOrD,0BAA0B,CAACqD,SAAS,EAAE,IAAI,CAAC5B,cAAc,CAAC;EACnE,CAAC;EAESN,YAAA,CAAAO,SAAA,CAAA3B,aAAa,GAAvB,UAAwBuD,OAAqB;IAC3C,OAAOvD,aAAa,CAACuD,OAAO,EAAE,IAAI,CAAC7B,cAAc,CAAC;EACpD,CAAC;EACH,OAAAN,YAAC;AAAD,CAAC,CAnDiCvB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}