{"ast": null, "code": "import { __spreadArrays } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { conv, convDown, convNoRelu } from './convLayer';\nexport function residual(x, params) {\n  var out = conv(x, params.conv1);\n  out = convNoRelu(out, params.conv2);\n  out = tf.add(out, x);\n  out = tf.relu(out);\n  return out;\n}\nexport function residualDown(x, params) {\n  var out = convDown(x, params.conv1);\n  out = convNoRelu(out, params.conv2);\n  var pooled = tf.avgPool(x, 2, 2, 'valid');\n  var zeros = tf.zeros(pooled.shape);\n  var isPad = pooled.shape[3] !== out.shape[3];\n  var isAdjustShape = pooled.shape[1] !== out.shape[1] || pooled.shape[2] !== out.shape[2];\n  if (isAdjustShape) {\n    var padShapeX = __spreadArrays(out.shape);\n    padShapeX[1] = 1;\n    var zerosW = tf.zeros(padShapeX);\n    out = tf.concat([out, zerosW], 1);\n    var padShapeY = __spreadArrays(out.shape);\n    padShapeY[2] = 1;\n    var zerosH = tf.zeros(padShapeY);\n    out = tf.concat([out, zerosH], 2);\n  }\n  pooled = isPad ? tf.concat([pooled, zeros], 3) : pooled;\n  out = tf.add(pooled, out);\n  out = tf.relu(out);\n  return out;\n}", "map": {"version": 3, "names": ["tf", "conv", "convDown", "convNoRelu", "residual", "x", "params", "out", "conv1", "conv2", "add", "relu", "residualDown", "pooled", "avgPool", "zeros", "shape", "isPad", "isAdjustShape", "padShapeX", "__spreadA<PERSON>ys", "zerosW", "concat", "padShapeY", "zerosH"], "sources": ["../../../src/faceRecognitionNet/residualLayer.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,aAAa;AAGxD,OAAM,SAAUC,QAAQA,CAACC,CAAc,EAAEC,MAA2B;EAClE,IAAIC,GAAG,GAAGN,IAAI,CAACI,CAAC,EAAEC,MAAM,CAACE,KAAK,CAAC;EAC/BD,GAAG,GAAGJ,UAAU,CAACI,GAAG,EAAED,MAAM,CAACG,KAAK,CAAC;EACnCF,GAAG,GAAGP,EAAE,CAACU,GAAG,CAACH,GAAG,EAAEF,CAAC,CAAC;EACpBE,GAAG,GAAGP,EAAE,CAACW,IAAI,CAACJ,GAAG,CAAC;EAClB,OAAOA,GAAG;AACZ;AAEA,OAAM,SAAUK,YAAYA,CAACP,CAAc,EAAEC,MAA2B;EACtE,IAAIC,GAAG,GAAGL,QAAQ,CAACG,CAAC,EAAEC,MAAM,CAACE,KAAK,CAAC;EACnCD,GAAG,GAAGJ,UAAU,CAACI,GAAG,EAAED,MAAM,CAACG,KAAK,CAAC;EAEnC,IAAII,MAAM,GAAGb,EAAE,CAACc,OAAO,CAACT,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAgB;EACxD,IAAMU,KAAK,GAAGf,EAAE,CAACe,KAAK,CAAaF,MAAM,CAACG,KAAK,CAAC;EAChD,IAAMC,KAAK,GAAGJ,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,KAAKT,GAAG,CAACS,KAAK,CAAC,CAAC,CAAC;EAC9C,IAAME,aAAa,GAAGL,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,KAAKT,GAAG,CAACS,KAAK,CAAC,CAAC,CAAC,IAAIH,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,KAAKT,GAAG,CAACS,KAAK,CAAC,CAAC,CAAC;EAE1F,IAAIE,aAAa,EAAE;IACjB,IAAMC,SAAS,GAAGC,cAAA,CAAIb,GAAG,CAACS,KAAK,CAAqC;IACpEG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAChB,IAAME,MAAM,GAAGrB,EAAE,CAACe,KAAK,CAAaI,SAAS,CAAC;IAC9CZ,GAAG,GAAGP,EAAE,CAACsB,MAAM,CAAC,CAACf,GAAG,EAAEc,MAAM,CAAC,EAAE,CAAC,CAAC;IAEjC,IAAME,SAAS,GAAGH,cAAA,CAAIb,GAAG,CAACS,KAAK,CAAqC;IACpEO,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAChB,IAAMC,MAAM,GAAGxB,EAAE,CAACe,KAAK,CAAaQ,SAAS,CAAC;IAC9ChB,GAAG,GAAGP,EAAE,CAACsB,MAAM,CAAC,CAACf,GAAG,EAAEiB,MAAM,CAAC,EAAE,CAAC,CAAC;;EAGnCX,MAAM,GAAGI,KAAK,GAAGjB,EAAE,CAACsB,MAAM,CAAC,CAACT,MAAM,EAAEE,KAAK,CAAC,EAAE,CAAC,CAAC,GAAGF,MAAM;EACvDN,GAAG,GAAGP,EAAE,CAACU,GAAG,CAACG,MAAM,EAAEN,GAAG,CAAgB;EAExCA,GAAG,GAAGP,EAAE,CAACW,IAAI,CAACJ,GAAG,CAAC;EAClB,OAAOA,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}