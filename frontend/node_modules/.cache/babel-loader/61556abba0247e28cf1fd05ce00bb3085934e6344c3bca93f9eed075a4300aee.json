{"ast": null, "code": "var constant = require('./constant'),\n  defineProperty = require('./_defineProperty'),\n  identity = require('./identity');\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function (func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\nmodule.exports = baseSetToString;", "map": {"version": 3, "names": ["constant", "require", "defineProperty", "identity", "baseSetToString", "func", "string", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_baseSetToString.js"], "sourcesContent": ["var constant = require('./constant'),\n    defineProperty = require('./_defineProperty'),\n    identity = require('./identity');\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function(func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\n\nmodule.exports = baseSetToString;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;EAChCC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;EAC7CE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,eAAe,GAAG,CAACF,cAAc,GAAGC,QAAQ,GAAG,UAASE,IAAI,EAAEC,MAAM,EAAE;EACxE,OAAOJ,cAAc,CAACG,IAAI,EAAE,UAAU,EAAE;IACtC,cAAc,EAAE,IAAI;IACpB,YAAY,EAAE,KAAK;IACnB,OAAO,EAAEL,QAAQ,CAACM,MAAM,CAAC;IACzB,UAAU,EAAE;EACd,CAAC,CAAC;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}