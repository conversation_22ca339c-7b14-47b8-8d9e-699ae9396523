{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function prelu(x, alpha) {\n  return tf.tidy(function () {\n    return tf.add(tf.relu(x), tf.mul(alpha, tf.neg(tf.relu(tf.neg(x)))));\n  });\n}", "map": {"version": 3, "names": ["tf", "prelu", "x", "alpha", "tidy", "add", "relu", "mul", "neg"], "sources": ["../../../src/mtcnn/prelu.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,OAAM,SAAUC,KAAKA,CAAsBC,CAAI,EAAEC,KAAkB;EACjE,OAAOH,EAAE,CAACI,IAAI,CAAC;IACb,OAAAJ,EAAE,CAACK,GAAG,CACJL,EAAE,CAACM,IAAI,CAACJ,CAAC,CAAC,EACVF,EAAE,CAACO,GAAG,CAACJ,KAAK,EAAEH,EAAE,CAACQ,GAAG,CAACR,EAAE,CAACM,IAAI,CAACN,EAAE,CAACQ,GAAG,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1C;EAHD,CAGC,CACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}