{"ast": null, "code": "import { SsdMobilenetv1Options } from '../ssdMobilenetv1/SsdMobilenetv1Options';\nimport { DetectAllFacesTask, DetectSingleFaceTask } from './DetectFacesTasks';\nexport function detectSingleFace(input, options) {\n  if (options === void 0) {\n    options = new SsdMobilenetv1Options();\n  }\n  return new DetectSingleFaceTask(input, options);\n}\nexport function detectAllFaces(input, options) {\n  if (options === void 0) {\n    options = new SsdMobilenetv1Options();\n  }\n  return new DetectAllFacesTask(input, options);\n}", "map": {"version": 3, "names": ["SsdMobilenetv1Options", "DetectAllFacesTask", "DetectSingleFaceTask", "detectSingleFace", "input", "options", "detectAllFaces"], "sources": ["../../../src/globalApi/detectFaces.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,kBAAkB,EAAEC,oBAAoB,QAAQ,oBAAoB;AAG7E,OAAM,SAAUC,gBAAgBA,CAC9BC,KAAgB,EAChBC,OAA2D;EAA3D,IAAAA,OAAA;IAAAA,OAAA,OAAoCL,qBAAqB,EAAE;EAAA;EAE3D,OAAO,IAAIE,oBAAoB,CAACE,KAAK,EAAEC,OAAO,CAAC;AACjD;AAEA,OAAM,SAAUC,cAAcA,CAC5BF,KAAgB,EAChBC,OAA2D;EAA3D,IAAAA,OAAA;IAAAA,OAAA,OAAoCL,qBAAqB,EAAE;EAAA;EAE3D,OAAO,IAAIC,kBAAkB,CAACG,KAAK,EAAEC,OAAO,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}