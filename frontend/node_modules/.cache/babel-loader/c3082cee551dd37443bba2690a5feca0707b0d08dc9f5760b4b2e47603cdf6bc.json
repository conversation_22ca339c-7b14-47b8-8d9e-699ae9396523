{"ast": null, "code": "export function isWithAge(obj) {\n  return typeof obj['age'] === 'number';\n}\nexport function extendWithAge(sourceObj, age) {\n  var extension = {\n    age: age\n  };\n  return Object.assign({}, sourceObj, extension);\n}", "map": {"version": 3, "names": ["isWithAge", "obj", "extendWithAge", "sourceObj", "age", "extension", "Object", "assign"], "sources": ["../../../src/factories/WithAge.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAM,SAAUA,SAASA,CAACC,GAAQ;EAChC,OAAO,OAAOA,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ;AACvC;AAEA,OAAM,SAAUC,aAAaA,CAG3BC,SAAkB,EAClBC,GAAW;EAGX,IAAMC,SAAS,GAAG;IAAED,GAAG,EAAAA;EAAA,CAAE;EACzB,OAAOE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEJ,SAAS,EAAEE,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}