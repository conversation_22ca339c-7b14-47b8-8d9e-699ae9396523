{"ast": null, "code": "var baseSlice = require('./_baseSlice');\n\n/**\n * Casts `array` to a slice if it's needed.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {number} start The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the cast slice.\n */\nfunction castSlice(array, start, end) {\n  var length = array.length;\n  end = end === undefined ? length : end;\n  return !start && end >= length ? array : baseSlice(array, start, end);\n}\nmodule.exports = castSlice;", "map": {"version": 3, "names": ["baseSlice", "require", "castSlice", "array", "start", "end", "length", "undefined", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_castSlice.js"], "sourcesContent": ["var baseSlice = require('./_baseSlice');\n\n/**\n * Casts `array` to a slice if it's needed.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {number} start The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the cast slice.\n */\nfunction castSlice(array, start, end) {\n  var length = array.length;\n  end = end === undefined ? length : end;\n  return (!start && end >= length) ? array : baseSlice(array, start, end);\n}\n\nmodule.exports = castSlice;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;EACzBD,GAAG,GAAGA,GAAG,KAAKE,SAAS,GAAGD,MAAM,GAAGD,GAAG;EACtC,OAAQ,CAACD,KAAK,IAAIC,GAAG,IAAIC,MAAM,GAAIH,KAAK,GAAGH,SAAS,CAACG,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;AACzE;AAEAG,MAAM,CAACC,OAAO,GAAGP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}