{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { extendWithFaceDetection } from '../factories/WithFaceDetection';\nimport { MtcnnOptions } from '../mtcnn/MtcnnOptions';\nimport { SsdMobilenetv1Options } from '../ssdMobilenetv1/SsdMobilenetv1Options';\nimport { TinyFaceDetectorOptions } from '../tinyFaceDetector/TinyFaceDetectorOptions';\nimport { TinyYolov2Options } from '../tinyYolov2';\nimport { ComposableTask } from './ComposableTask';\nimport { DetectAllFaceLandmarksTask, DetectSingleFaceLandmarksTask } from './DetectFaceLandmarksTasks';\nimport { nets } from './nets';\nimport { PredictAllAgeAndGenderTask, PredictSingleAgeAndGenderTask } from './PredictAgeAndGenderTask';\nimport { PredictAllFaceExpressionsTask, PredictSingleFaceExpressionsTask } from './PredictFaceExpressionsTask';\nvar DetectFacesTaskBase = /** @class */function (_super) {\n  __extends(DetectFacesTaskBase, _super);\n  function DetectFacesTaskBase(input, options) {\n    if (options === void 0) {\n      options = new SsdMobilenetv1Options();\n    }\n    var _this = _super.call(this) || this;\n    _this.input = input;\n    _this.options = options;\n    return _this;\n  }\n  return DetectFacesTaskBase;\n}(ComposableTask);\nexport { DetectFacesTaskBase };\nvar DetectAllFacesTask = /** @class */function (_super) {\n  __extends(DetectAllFacesTask, _super);\n  function DetectAllFacesTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DetectAllFacesTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a, input, options, faceDetectionFunction;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this, input = _a.input, options = _a.options;\n            if (!(options instanceof MtcnnOptions)) return [3 /*break*/, 2];\n            return [4 /*yield*/, nets.mtcnn.forward(input, options)];\n          case 1:\n            return [2 /*return*/, _b.sent().map(function (result) {\n              return result.detection;\n            })];\n          case 2:\n            faceDetectionFunction = options instanceof TinyFaceDetectorOptions ? function (input) {\n              return nets.tinyFaceDetector.locateFaces(input, options);\n            } : options instanceof SsdMobilenetv1Options ? function (input) {\n              return nets.ssdMobilenetv1.locateFaces(input, options);\n            } : options instanceof TinyYolov2Options ? function (input) {\n              return nets.tinyYolov2.locateFaces(input, options);\n            } : null;\n            if (!faceDetectionFunction) {\n              throw new Error('detectFaces - expected options to be instance of TinyFaceDetectorOptions | SsdMobilenetv1Options | MtcnnOptions | TinyYolov2Options');\n            }\n            return [2 /*return*/, faceDetectionFunction(input)];\n        }\n      });\n    });\n  };\n  DetectAllFacesTask.prototype.runAndExtendWithFaceDetections = function () {\n    var _this = this;\n    return new Promise(function (res) {\n      return __awaiter(_this, void 0, void 0, function () {\n        var detections;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4 /*yield*/, this.run()];\n            case 1:\n              detections = _a.sent();\n              return [2 /*return*/, res(detections.map(function (detection) {\n                return extendWithFaceDetection({}, detection);\n              }))];\n          }\n        });\n      });\n    });\n  };\n  DetectAllFacesTask.prototype.withFaceLandmarks = function (useTinyLandmarkNet) {\n    if (useTinyLandmarkNet === void 0) {\n      useTinyLandmarkNet = false;\n    }\n    return new DetectAllFaceLandmarksTask(this.runAndExtendWithFaceDetections(), this.input, useTinyLandmarkNet);\n  };\n  DetectAllFacesTask.prototype.withFaceExpressions = function () {\n    return new PredictAllFaceExpressionsTask(this.runAndExtendWithFaceDetections(), this.input);\n  };\n  DetectAllFacesTask.prototype.withAgeAndGender = function () {\n    return new PredictAllAgeAndGenderTask(this.runAndExtendWithFaceDetections(), this.input);\n  };\n  return DetectAllFacesTask;\n}(DetectFacesTaskBase);\nexport { DetectAllFacesTask };\nvar DetectSingleFaceTask = /** @class */function (_super) {\n  __extends(DetectSingleFaceTask, _super);\n  function DetectSingleFaceTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DetectSingleFaceTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var faceDetections, faceDetectionWithHighestScore;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, new DetectAllFacesTask(this.input, this.options)];\n          case 1:\n            faceDetections = _a.sent();\n            faceDetectionWithHighestScore = faceDetections[0];\n            faceDetections.forEach(function (faceDetection) {\n              if (faceDetection.score > faceDetectionWithHighestScore.score) {\n                faceDetectionWithHighestScore = faceDetection;\n              }\n            });\n            return [2 /*return*/, faceDetectionWithHighestScore];\n        }\n      });\n    });\n  };\n  DetectSingleFaceTask.prototype.runAndExtendWithFaceDetection = function () {\n    var _this = this;\n    return new Promise(function (res) {\n      return __awaiter(_this, void 0, void 0, function () {\n        var detection;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4 /*yield*/, this.run()];\n            case 1:\n              detection = _a.sent();\n              return [2 /*return*/, res(detection ? extendWithFaceDetection({}, detection) : undefined)];\n          }\n        });\n      });\n    });\n  };\n  DetectSingleFaceTask.prototype.withFaceLandmarks = function (useTinyLandmarkNet) {\n    if (useTinyLandmarkNet === void 0) {\n      useTinyLandmarkNet = false;\n    }\n    return new DetectSingleFaceLandmarksTask(this.runAndExtendWithFaceDetection(), this.input, useTinyLandmarkNet);\n  };\n  DetectSingleFaceTask.prototype.withFaceExpressions = function () {\n    return new PredictSingleFaceExpressionsTask(this.runAndExtendWithFaceDetection(), this.input);\n  };\n  DetectSingleFaceTask.prototype.withAgeAndGender = function () {\n    return new PredictSingleAgeAndGenderTask(this.runAndExtendWithFaceDetection(), this.input);\n  };\n  return DetectSingleFaceTask;\n}(DetectFacesTaskBase);\nexport { DetectSingleFaceTask };", "map": {"version": 3, "names": ["extendWithFaceDetection", "MtcnnOptions", "SsdMobilenetv1Options", "TinyFaceDetectorOptions", "TinyYolov2Options", "ComposableTask", "DetectAllFaceLandmarksTask", "DetectSingleFaceLandmarksTask", "nets", "PredictAllAgeAndGenderTask", "PredictSingleAgeAndGenderTask", "PredictAllFaceExpressionsTask", "PredictSingleFaceExpressionsTask", "DetectFacesTaskBase", "_super", "__extends", "input", "options", "_this", "call", "DetectAllFacesTask", "prototype", "run", "_a", "mtcnn", "forward", "_b", "sent", "map", "result", "detection", "faceDetectionFunction", "tinyFaceDetector", "locateFaces", "ssdMobilenetv1", "tinyYolov2", "Error", "runAndExtendWithFaceDetections", "Promise", "res", "__awaiter", "detections", "withFaceLandmarks", "useTinyLandmarkNet", "withFaceExpressions", "withAgeAndGender", "DetectSingleFaceTask", "faceDetections", "faceDetectionWithHighestScore", "for<PERSON>ach", "faceDetection", "score", "runAndExtendWithFaceDetection", "undefined"], "sources": ["../../../src/globalApi/DetectFacesTasks.ts"], "sourcesContent": [null], "mappings": ";AAEA,SAASA,uBAAuB,QAA2B,gCAAgC;AAC3F,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,0BAA0B,EAAEC,6BAA6B,QAAQ,4BAA4B;AACtG,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,0BAA0B,EAAEC,6BAA6B,QAAQ,2BAA2B;AACrG,SAASC,6BAA6B,EAAEC,gCAAgC,QAAQ,8BAA8B;AAG9G,IAAAC,mBAAA,0BAAAC,MAAA;EAAkDC,SAAA,CAAAF,mBAAA,EAAAC,MAAA;EAChD,SAAAD,oBACYG,KAAgB,EAChBC,OAA2D;IAA3D,IAAAA,OAAA;MAAAA,OAAA,OAAoCf,qBAAqB,EAAE;IAAA;IAFvE,IAAAgB,KAAA,GAIEJ,MAAA,CAAAK,IAAA,MAAO;IAHGD,KAAA,CAAAF,KAAK,GAALA,KAAK;IACLE,KAAA,CAAAD,OAAO,GAAPA,OAAO;;EAGnB;EACF,OAAAJ,mBAAC;AAAD,CAAC,CAPiDR,cAAc;;AAShE,IAAAe,kBAAA,0BAAAN,MAAA;EAAwCC,SAAA,CAAAK,kBAAA,EAAAN,MAAA;EAAxC,SAAAM,mBAAA;;EA0DA;EAxDeA,kBAAA,CAAAC,SAAA,CAAAC,GAAG,GAAhB;;;;;;YAEQC,EAAA,GAAqB,IAAI,EAAvBP,KAAK,GAAAO,EAAA,CAAAP,KAAA,EAAEC,OAAO,GAAAM,EAAA,CAAAN,OAAA;kBAElBA,OAAO,YAAYhB,YAAY,GAA/B;YACM,qBAAMO,IAAI,CAACgB,KAAK,CAACC,OAAO,CAACT,KAAK,EAAEC,OAAO,CAAC;;YAAhD,sBAAQS,EAAA,CAAAC,IAAA,EAAwC,CAC7CC,GAAG,CAAC,UAAAC,MAAM;cAAI,OAAAA,MAAM,CAACC,SAAS;YAAhB,CAAgB,CAAC;;YAG9BC,qBAAqB,GAAGd,OAAO,YAAYd,uBAAuB,GACpE,UAACa,KAAgB;cAAK,OAAAR,IAAI,CAACwB,gBAAgB,CAACC,WAAW,CAACjB,KAAK,EAAEC,OAAO,CAAC;YAAjD,CAAiD,GAEvEA,OAAO,YAAYf,qBAAqB,GACpC,UAACc,KAAgB;cAAK,OAAAR,IAAI,CAAC0B,cAAc,CAACD,WAAW,CAACjB,KAAK,EAAEC,OAAO,CAAC;YAA/C,CAA+C,GAErEA,OAAO,YAAYb,iBAAiB,GAChC,UAACY,KAAgB;cAAK,OAAAR,IAAI,CAAC2B,UAAU,CAACF,WAAW,CAACjB,KAAK,EAAEC,OAAO,CAAC;YAA3C,CAA2C,GACjE,IAET;YAEH,IAAI,CAACc,qBAAqB,EAAE;cAC1B,MAAM,IAAIK,KAAK,CAAC,qIAAqI,CAAC;;YAGxJ,sBAAOL,qBAAqB,CAACf,KAAK,CAAC;;;;GACpC;EAEOI,kBAAA,CAAAC,SAAA,CAAAgB,8BAA8B,GAAtC;IAAA,IAAAnB,KAAA;IACE,OAAO,IAAIoB,OAAO,CAA0B,UAAMC,GAAG;MAAA,OAAAC,SAAA,CAAAtB,KAAA;;;;;cAChC,qBAAM,IAAI,CAACI,GAAG,EAAE;;cAA7BmB,UAAU,GAAGlB,EAAA,CAAAI,IAAA,EAAgB;cACnC,sBAAOY,GAAG,CAACE,UAAU,CAACb,GAAG,CAAC,UAAAE,SAAS;gBAAI,OAAA9B,uBAAuB,CAAC,EAAE,EAAE8B,SAAS,CAAC;cAAtC,CAAsC,CAAC,CAAC;;;;KAChF,CAAC;EACJ,CAAC;EAEDV,kBAAA,CAAAC,SAAA,CAAAqB,iBAAiB,GAAjB,UAAkBC,kBAAmC;IAAnC,IAAAA,kBAAA;MAAAA,kBAAA,QAAmC;IAAA;IACnD,OAAO,IAAIrC,0BAA0B,CACnC,IAAI,CAAC+B,8BAA8B,EAAE,EACrC,IAAI,CAACrB,KAAK,EACV2B,kBAAkB,CACnB;EACH,CAAC;EAEDvB,kBAAA,CAAAC,SAAA,CAAAuB,mBAAmB,GAAnB;IACE,OAAO,IAAIjC,6BAA6B,CACtC,IAAI,CAAC0B,8BAA8B,EAAE,EACrC,IAAI,CAACrB,KAAK,CACX;EACH,CAAC;EAEDI,kBAAA,CAAAC,SAAA,CAAAwB,gBAAgB,GAAhB;IACE,OAAO,IAAIpC,0BAA0B,CACnC,IAAI,CAAC4B,8BAA8B,EAAE,EACrC,IAAI,CAACrB,KAAK,CACX;EACH,CAAC;EACH,OAAAI,kBAAC;AAAD,CAAC,CA1DuCP,mBAAmB;;AA4D3D,IAAAiC,oBAAA,0BAAAhC,MAAA;EAA0CC,SAAA,CAAA+B,oBAAA,EAAAhC,MAAA;EAA1C,SAAAgC,qBAAA;;EAyCA;EAvCeA,oBAAA,CAAAzB,SAAA,CAAAC,GAAG,GAAhB;;;;;;YACyB,qBAAM,IAAIF,kBAAkB,CAAC,IAAI,CAACJ,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;;YAAvE8B,cAAc,GAAGxB,EAAA,CAAAI,IAAA,EAAsD;YACzEqB,6BAA6B,GAAGD,cAAc,CAAC,CAAC,CAAC;YACrDA,cAAc,CAACE,OAAO,CAAC,UAAAC,aAAa;cAClC,IAAIA,aAAa,CAACC,KAAK,GAAGH,6BAA6B,CAACG,KAAK,EAAE;gBAC7DH,6BAA6B,GAAGE,aAAa;;YAEjD,CAAC,CAAC;YACF,sBAAOF,6BAA6B;;;;GACrC;EAEOF,oBAAA,CAAAzB,SAAA,CAAA+B,6BAA6B,GAArC;IAAA,IAAAlC,KAAA;IACE,OAAO,IAAIoB,OAAO,CAAwB,UAAMC,GAAG;MAAA,OAAAC,SAAA,CAAAtB,KAAA;;;;;cAC/B,qBAAM,IAAI,CAACI,GAAG,EAAE;;cAA5BQ,SAAS,GAAGP,EAAA,CAAAI,IAAA,EAAgB;cAClC,sBAAOY,GAAG,CAACT,SAAS,GAAG9B,uBAAuB,CAAK,EAAE,EAAE8B,SAAS,CAAC,GAAGuB,SAAS,CAAC;;;;KAC/E,CAAC;EACJ,CAAC;EAEDP,oBAAA,CAAAzB,SAAA,CAAAqB,iBAAiB,GAAjB,UAAkBC,kBAAmC;IAAnC,IAAAA,kBAAA;MAAAA,kBAAA,QAAmC;IAAA;IACnD,OAAO,IAAIpC,6BAA6B,CACtC,IAAI,CAAC6C,6BAA6B,EAAE,EACpC,IAAI,CAACpC,KAAK,EACV2B,kBAAkB,CACnB;EACH,CAAC;EAEDG,oBAAA,CAAAzB,SAAA,CAAAuB,mBAAmB,GAAnB;IACE,OAAO,IAAIhC,gCAAgC,CACzC,IAAI,CAACwC,6BAA6B,EAAE,EACpC,IAAI,CAACpC,KAAK,CACX;EACH,CAAC;EAED8B,oBAAA,CAAAzB,SAAA,CAAAwB,gBAAgB,GAAhB;IACE,OAAO,IAAInC,6BAA6B,CACtC,IAAI,CAAC0C,6BAA6B,EAAE,EACpC,IAAI,CAACpC,KAAK,CACX;EACH,CAAC;EACH,OAAA8B,oBAAC;AAAD,CAAC,CAzCyCjC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}