{"ast": null, "code": "import { isValidNumber } from '../utils';\nvar Dimensions = /** @class */function () {\n  function Dimensions(width, height) {\n    if (!isValidNumber(width) || !isValidNumber(height)) {\n      throw new Error(\"Dimensions.constructor - expected width and height to be valid numbers, instead have \" + JSON.stringify({\n        width: width,\n        height: height\n      }));\n    }\n    this._width = width;\n    this._height = height;\n  }\n  Object.defineProperty(Dimensions.prototype, \"width\", {\n    get: function () {\n      return this._width;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Dimensions.prototype, \"height\", {\n    get: function () {\n      return this._height;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Dimensions.prototype.reverse = function () {\n    return new Dimensions(1 / this.width, 1 / this.height);\n  };\n  return Dimensions;\n}();\nexport { Dimensions };", "map": {"version": 3, "names": ["isValidNumber", "Dimensions", "width", "height", "Error", "JSON", "stringify", "_width", "_height", "Object", "defineProperty", "prototype", "get", "reverse"], "sources": ["../../../src/classes/Dimensions.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,aAAa,QAAQ,UAAU;AAOxC,IAAAC,UAAA;EAKE,SAAAA,WAAYC,KAAa,EAAEC,MAAc;IACvC,IAAI,CAACH,aAAa,CAACE,KAAK,CAAC,IAAI,CAACF,aAAa,CAACG,MAAM,CAAC,EAAE;MACnD,MAAM,IAAIC,KAAK,CAAC,0FAAwFC,IAAI,CAACC,SAAS,CAAC;QAAEJ,KAAK,EAAAA,KAAA;QAAEC,MAAM,EAAAA;MAAA,CAAE,CAAG,CAAC;;IAG9I,IAAI,CAACI,MAAM,GAAGL,KAAK;IACnB,IAAI,CAACM,OAAO,GAAGL,MAAM;EACvB;EAEAM,MAAA,CAAAC,cAAA,CAAWT,UAAA,CAAAU,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACL,MAAM;IAAC,CAAC;;;;EACjDE,MAAA,CAAAC,cAAA,CAAWT,UAAA,CAAAU,SAAA,UAAM;SAAjB,SAAAC,CAAA;MAA8B,OAAO,IAAI,CAACJ,OAAO;IAAC,CAAC;;;;EAE5CP,UAAA,CAAAU,SAAA,CAAAE,OAAO,GAAd;IACE,OAAO,IAAIZ,UAAU,CAAC,CAAC,GAAG,IAAI,CAACC,KAAK,EAAE,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC;EACxD,CAAC;EACH,OAAAF,UAAC;AAAD,CAAC,CApBD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}