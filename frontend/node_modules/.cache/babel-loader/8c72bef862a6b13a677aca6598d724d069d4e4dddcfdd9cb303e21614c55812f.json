{"ast": null, "code": "var baseIteratee = require('./_baseIteratee'),\n  baseSum = require('./_baseSum');\n\n/**\n * This method is like `_.sum` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be summed.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the sum.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.sumBy(objects, function(o) { return o.n; });\n * // => 20\n *\n * // The `_.property` iteratee shorthand.\n * _.sumBy(objects, 'n');\n * // => 20\n */\nfunction sumBy(array, iteratee) {\n  return array && array.length ? baseSum(array, baseIteratee(iteratee, 2)) : 0;\n}\nmodule.exports = sumBy;", "map": {"version": 3, "names": ["baseIteratee", "require", "baseSum", "sumBy", "array", "iteratee", "length", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/sumBy.js"], "sourcesContent": ["var baseIteratee = require('./_baseIteratee'),\n    baseSum = require('./_baseSum');\n\n/**\n * This method is like `_.sum` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be summed.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the sum.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.sumBy(objects, function(o) { return o.n; });\n * // => 20\n *\n * // The `_.property` iteratee shorthand.\n * _.sumBy(objects, 'n');\n * // => 20\n */\nfunction sumBy(array, iteratee) {\n  return (array && array.length)\n    ? baseSum(array, baseIteratee(iteratee, 2))\n    : 0;\n}\n\nmodule.exports = sumBy;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC9B,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBJ,OAAO,CAACE,KAAK,EAAEJ,YAAY,CAACK,QAAQ,EAAE,CAAC,CAAC,CAAC,GACzC,CAAC;AACP;AAEAE,MAAM,CAACC,OAAO,GAAGL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}