{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { extractFaces, extractFaceTensors } from '../dom';\nimport { extendWithFaceLandmarks } from '../factories/WithFaceLandmarks';\nimport { ComposableTask } from './ComposableTask';\nimport { ComputeAllFaceDescriptorsTask, ComputeSingleFaceDescriptorTask } from './ComputeFaceDescriptorsTasks';\nimport { nets } from './nets';\nimport { PredictAllAgeAndGenderWithFaceAlignmentTask, PredictSingleAgeAndGenderWithFaceAlignmentTask } from './PredictAgeAndGenderTask';\nimport { PredictAllFaceExpressionsWithFaceAlignmentTask, PredictSingleFaceExpressionsWithFaceAlignmentTask } from './PredictFaceExpressionsTask';\nvar DetectFaceLandmarksTaskBase = /** @class */function (_super) {\n  __extends(DetectFaceLandmarksTaskBase, _super);\n  function DetectFaceLandmarksTaskBase(parentTask, input, useTinyLandmarkNet) {\n    var _this = _super.call(this) || this;\n    _this.parentTask = parentTask;\n    _this.input = input;\n    _this.useTinyLandmarkNet = useTinyLandmarkNet;\n    return _this;\n  }\n  Object.defineProperty(DetectFaceLandmarksTaskBase.prototype, \"landmarkNet\", {\n    get: function () {\n      return this.useTinyLandmarkNet ? nets.faceLandmark68TinyNet : nets.faceLandmark68Net;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  return DetectFaceLandmarksTaskBase;\n}(ComposableTask);\nexport { DetectFaceLandmarksTaskBase };\nvar DetectAllFaceLandmarksTask = /** @class */function (_super) {\n  __extends(DetectAllFaceLandmarksTask, _super);\n  function DetectAllFaceLandmarksTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DetectAllFaceLandmarksTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResults, detections, faces, _a, faceLandmarksByFace;\n      var _this = this;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResults = _b.sent();\n            detections = parentResults.map(function (res) {\n              return res.detection;\n            });\n            if (!(this.input instanceof tf.Tensor)) return [3 /*break*/, 3];\n            return [4 /*yield*/, extractFaceTensors(this.input, detections)];\n          case 2:\n            _a = _b.sent();\n            return [3 /*break*/, 5];\n          case 3:\n            return [4 /*yield*/, extractFaces(this.input, detections)];\n          case 4:\n            _a = _b.sent();\n            _b.label = 5;\n          case 5:\n            faces = _a;\n            return [4 /*yield*/, Promise.all(faces.map(function (face) {\n              return _this.landmarkNet.detectLandmarks(face);\n            }))];\n          case 6:\n            faceLandmarksByFace = _b.sent();\n            faces.forEach(function (f) {\n              return f instanceof tf.Tensor && f.dispose();\n            });\n            return [2 /*return*/, parentResults.map(function (parentResult, i) {\n              return extendWithFaceLandmarks(parentResult, faceLandmarksByFace[i]);\n            })];\n        }\n      });\n    });\n  };\n  DetectAllFaceLandmarksTask.prototype.withFaceExpressions = function () {\n    return new PredictAllFaceExpressionsWithFaceAlignmentTask(this, this.input);\n  };\n  DetectAllFaceLandmarksTask.prototype.withAgeAndGender = function () {\n    return new PredictAllAgeAndGenderWithFaceAlignmentTask(this, this.input);\n  };\n  DetectAllFaceLandmarksTask.prototype.withFaceDescriptors = function () {\n    return new ComputeAllFaceDescriptorsTask(this, this.input);\n  };\n  return DetectAllFaceLandmarksTask;\n}(DetectFaceLandmarksTaskBase);\nexport { DetectAllFaceLandmarksTask };\nvar DetectSingleFaceLandmarksTask = /** @class */function (_super) {\n  __extends(DetectSingleFaceLandmarksTask, _super);\n  function DetectSingleFaceLandmarksTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DetectSingleFaceLandmarksTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResult, detection, faces, _a, landmarks;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResult = _b.sent();\n            if (!parentResult) {\n              return [2 /*return*/];\n            }\n            detection = parentResult.detection;\n            if (!(this.input instanceof tf.Tensor)) return [3 /*break*/, 3];\n            return [4 /*yield*/, extractFaceTensors(this.input, [detection])];\n          case 2:\n            _a = _b.sent();\n            return [3 /*break*/, 5];\n          case 3:\n            return [4 /*yield*/, extractFaces(this.input, [detection])];\n          case 4:\n            _a = _b.sent();\n            _b.label = 5;\n          case 5:\n            faces = _a;\n            return [4 /*yield*/, this.landmarkNet.detectLandmarks(faces[0])];\n          case 6:\n            landmarks = _b.sent();\n            faces.forEach(function (f) {\n              return f instanceof tf.Tensor && f.dispose();\n            });\n            return [2 /*return*/, extendWithFaceLandmarks(parentResult, landmarks)];\n        }\n      });\n    });\n  };\n  DetectSingleFaceLandmarksTask.prototype.withFaceExpressions = function () {\n    return new PredictSingleFaceExpressionsWithFaceAlignmentTask(this, this.input);\n  };\n  DetectSingleFaceLandmarksTask.prototype.withAgeAndGender = function () {\n    return new PredictSingleAgeAndGenderWithFaceAlignmentTask(this, this.input);\n  };\n  DetectSingleFaceLandmarksTask.prototype.withFaceDescriptor = function () {\n    return new ComputeSingleFaceDescriptorTask(this, this.input);\n  };\n  return DetectSingleFaceLandmarksTask;\n}(DetectFaceLandmarksTaskBase);\nexport { DetectSingleFaceLandmarksTask };", "map": {"version": 3, "names": ["tf", "extractFaces", "extractFaceTensors", "extendWithFaceLandmarks", "ComposableTask", "ComputeAllFaceDescriptorsTask", "ComputeSingleFaceDescriptorTask", "nets", "PredictAllAgeAndGenderWithFaceAlignmentTask", "PredictSingleAgeAndGenderWithFaceAlignmentTask", "PredictAllFaceExpressionsWithFaceAlignmentTask", "PredictSingleFaceExpressionsWithFaceAlignmentTask", "DetectFaceLandmarksTaskBase", "_super", "__extends", "parentTask", "input", "useTinyLandmarkNet", "_this", "call", "Object", "defineProperty", "prototype", "get", "faceLandmark68TinyNet", "faceLandmark68Net", "DetectAllFaceLandmarksTask", "run", "parentResults", "_b", "sent", "detections", "map", "res", "detection", "Tensor", "_a", "faces", "Promise", "all", "face", "landmarkNet", "detectLandmarks", "faceLandmarksByFace", "for<PERSON>ach", "f", "dispose", "parentResult", "i", "withFaceExpressions", "withAgeAndGender", "withFaceDescriptors", "DetectSingleFaceLandmarksTask", "landmarks", "withFaceDescriptor"], "sources": ["../../../src/globalApi/DetectFaceLandmarksTasks.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,YAAY,EAAEC,kBAAkB,QAAmB,QAAQ;AAIpE,SAASC,uBAAuB,QAA2B,gCAAgC;AAC3F,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,6BAA6B,EAAEC,+BAA+B,QAAQ,+BAA+B;AAC9G,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SACEC,2CAA2C,EAC3CC,8CAA8C,QACzC,2BAA2B;AAClC,SACEC,8CAA8C,EAC9CC,iDAAiD,QAC5C,8BAA8B;AAErC,IAAAC,2BAAA,0BAAAC,MAAA;EAAyEC,SAAA,CAAAF,2BAAA,EAAAC,MAAA;EACvE,SAAAD,4BACYG,UAAkE,EAClEC,KAAgB,EAChBC,kBAA2B;IAHvC,IAAAC,KAAA,GAKEL,MAAA,CAAAM,IAAA,MAAO;IAJGD,KAAA,CAAAH,UAAU,GAAVA,UAAU;IACVG,KAAA,CAAAF,KAAK,GAALA,KAAK;IACLE,KAAA,CAAAD,kBAAkB,GAAlBA,kBAAkB;;EAG9B;EAEAG,MAAA,CAAAC,cAAA,CAAcT,2BAAA,CAAAU,SAAA,eAAW;SAAzB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACN,kBAAkB,GAC1BV,IAAI,CAACiB,qBAAqB,GAC1BjB,IAAI,CAACkB,iBAAiB;IAC5B,CAAC;;;;EACH,OAAAb,2BAAC;AAAD,CAAC,CAdwER,cAAc;;AAgBvF,IAAAsB,0BAAA,0BAAAb,MAAA;EAEUC,SAAA,CAAAY,0BAAA,EAAAb,MAAA;EAFV,SAAAa,2BAAA;;EAmCA;EA/BeA,0BAAA,CAAAJ,SAAA,CAAAK,GAAG,GAAhB;;;;;;;YAEwB,qBAAM,IAAI,CAACZ,UAAU;;YAArCa,aAAa,GAAGC,EAAA,CAAAC,IAAA,EAAqB;YACrCC,UAAU,GAAGH,aAAa,CAACI,GAAG,CAAC,UAAAC,GAAG;cAAI,OAAAA,GAAG,CAACC,SAAS;YAAb,CAAa,CAAC;kBAEJ,IAAI,CAAClB,KAAK,YAAYhB,EAAE,CAACmC,MAAM,GAA/B;YAClD,qBAAMjC,kBAAkB,CAAC,IAAI,CAACc,KAAK,EAAEe,UAAU,CAAC;;YAAhDK,EAAA,GAAAP,EAAA,CAAAC,IAAA,EAAgD;;;YAChD,qBAAM7B,YAAY,CAAC,IAAI,CAACe,KAAK,EAAEe,UAAU,CAAC;;YAA1CK,EAAA,GAAAP,EAAA,CAAAC,IAAA,EAA0C;;;YAFxCO,KAAK,GAAAD,EAEmC;YAElB,qBAAME,OAAO,CAACC,GAAG,CAACF,KAAK,CAACL,GAAG,CACrD,UAAAQ,IAAI;cAAI,OAAAtB,KAAI,CAACuB,WAAW,CAACC,eAAe,CAACF,IAAI,CAAC;YAAtC,CAAsC,CAC/C,CAAC;;YAFIG,mBAAmB,GAAGd,EAAA,CAAAC,IAAA,EAEL;YAEvBO,KAAK,CAACO,OAAO,CAAC,UAAAC,CAAC;cAAI,OAAAA,CAAC,YAAY7C,EAAE,CAACmC,MAAM,IAAIU,CAAC,CAACC,OAAO,EAAE;YAArC,CAAqC,CAAC;YAEzD,sBAAOlB,aAAa,CAACI,GAAG,CAAC,UAACe,YAAY,EAAEC,CAAC;cACvC,OAAA7C,uBAAuB,CAAU4C,YAAY,EAAEJ,mBAAmB,CAACK,CAAC,CAAC,CAAC;YAAtE,CAAsE,CACvE;;;;GACF;EAEDtB,0BAAA,CAAAJ,SAAA,CAAA2B,mBAAmB,GAAnB;IACE,OAAO,IAAIvC,8CAA8C,CAAC,IAAI,EAAE,IAAI,CAACM,KAAK,CAAC;EAC7E,CAAC;EAEDU,0BAAA,CAAAJ,SAAA,CAAA4B,gBAAgB,GAAhB;IACE,OAAO,IAAI1C,2CAA2C,CAAC,IAAI,EAAE,IAAI,CAACQ,KAAK,CAAC;EAC1E,CAAC;EAEDU,0BAAA,CAAAJ,SAAA,CAAA6B,mBAAmB,GAAnB;IACE,OAAO,IAAI9C,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAACW,KAAK,CAAC;EAC5D,CAAC;EACH,OAAAU,0BAAC;AAAD,CAAC,CAjCSd,2BAA2B;;AAmCrC,IAAAwC,6BAAA,0BAAAvC,MAAA;EAEWC,SAAA,CAAAsC,6BAAA,EAAAvC,MAAA;EAFX,SAAAuC,8BAAA;;EAkCA;EA9BeA,6BAAA,CAAA9B,SAAA,CAAAK,GAAG,GAAhB;;;;;;YAEuB,qBAAM,IAAI,CAACZ,UAAU;;YAApCgC,YAAY,GAAGlB,EAAA,CAAAC,IAAA,EAAqB;YAC1C,IAAI,CAACiB,YAAY,EAAE;cACjB;;YAGMb,SAAS,GAAKa,YAAY,CAAAb,SAAjB;kBACqC,IAAI,CAAClB,KAAK,YAAYhB,EAAE,CAACmC,MAAM,GAA/B;YAClD,qBAAMjC,kBAAkB,CAAC,IAAI,CAACc,KAAK,EAAE,CAACkB,SAAS,CAAC,CAAC;;YAAjDE,EAAA,GAAAP,EAAA,CAAAC,IAAA,EAAiD;;;YACjD,qBAAM7B,YAAY,CAAC,IAAI,CAACe,KAAK,EAAE,CAACkB,SAAS,CAAC,CAAC;;YAA3CE,EAAA,GAAAP,EAAA,CAAAC,IAAA,EAA2C;;;YAFzCO,KAAK,GAAAD,EAEoC;YAE7B,qBAAM,IAAI,CAACK,WAAW,CAACC,eAAe,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;;YAA5DgB,SAAS,GAAGxB,EAAA,CAAAC,IAAA,EAAmE;YAErFO,KAAK,CAACO,OAAO,CAAC,UAAAC,CAAC;cAAI,OAAAA,CAAC,YAAY7C,EAAE,CAACmC,MAAM,IAAIU,CAAC,CAACC,OAAO,EAAE;YAArC,CAAqC,CAAC;YAEzD,sBAAO3C,uBAAuB,CAAU4C,YAAY,EAAEM,SAAS,CAAC;;;;GACjE;EAEDD,6BAAA,CAAA9B,SAAA,CAAA2B,mBAAmB,GAAnB;IACE,OAAO,IAAItC,iDAAiD,CAAC,IAAI,EAAE,IAAI,CAACK,KAAK,CAAC;EAChF,CAAC;EAEDoC,6BAAA,CAAA9B,SAAA,CAAA4B,gBAAgB,GAAhB;IACE,OAAO,IAAIzC,8CAA8C,CAAC,IAAI,EAAE,IAAI,CAACO,KAAK,CAAC;EAC7E,CAAC;EAEDoC,6BAAA,CAAA9B,SAAA,CAAAgC,kBAAkB,GAAlB;IACE,OAAO,IAAIhD,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAACU,KAAK,CAAC;EAC9D,CAAC;EACH,OAAAoC,6BAAC;AAAD,CAAC,CAhCUxC,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}