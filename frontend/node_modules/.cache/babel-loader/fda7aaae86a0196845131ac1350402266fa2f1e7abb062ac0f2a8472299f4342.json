{"ast": null, "code": "import WebSocket from './WebSocket';\nimport { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_HEADERS, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => {};\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.logLevel Sets the log level for Realtime\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint, options) {\n    var _a;\n    this.accessTokenValue = null;\n    this.apiKey = null;\n    this.channels = new Array();\n    this.endPoint = '';\n    this.httpEndpoint = '';\n    this.headers = DEFAULT_HEADERS;\n    this.params = {};\n    this.timeout = DEFAULT_TIMEOUT;\n    this.heartbeatIntervalMs = 25000;\n    this.heartbeatTimer = undefined;\n    this.pendingHeartbeatRef = null;\n    this.heartbeatCallback = noop;\n    this.ref = 0;\n    this.logger = noop;\n    this.conn = null;\n    this.sendBuffer = [];\n    this.serializer = new Serializer();\n    this.stateChangeCallbacks = {\n      open: [],\n      close: [],\n      error: [],\n      message: []\n    };\n    this.accessToken = null;\n    /**\n     * Use either custom fetch, if provided, or default fetch to make HTTP requests\n     *\n     * @internal\n     */\n    this._resolveFetch = customFetch => {\n      let _fetch;\n      if (customFetch) {\n        _fetch = customFetch;\n      } else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({\n          default: fetch\n        }) => fetch(...args));\n      } else {\n        _fetch = fetch;\n      }\n      return (...args) => _fetch(...args);\n    };\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n    this.httpEndpoint = httpEndpointURL(endPoint);\n    if (options === null || options === void 0 ? void 0 : options.transport) {\n      this.transport = options.transport;\n    } else {\n      this.transport = null;\n    }\n    if (options === null || options === void 0 ? void 0 : options.params) this.params = options.params;\n    if (options === null || options === void 0 ? void 0 : options.headers) this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n    if (options === null || options === void 0 ? void 0 : options.timeout) this.timeout = options.timeout;\n    if (options === null || options === void 0 ? void 0 : options.logger) this.logger = options.logger;\n    if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n      this.logLevel = options.logLevel || options.log_level;\n      this.params = Object.assign(Object.assign({}, this.params), {\n        log_level: this.logLevel\n      });\n    }\n    if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n    const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue;\n      this.apiKey = accessTokenValue;\n    }\n    this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs) ? options.reconnectAfterMs : tries => {\n      return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n    };\n    this.encode = (options === null || options === void 0 ? void 0 : options.encode) ? options.encode : (payload, callback) => {\n      return callback(JSON.stringify(payload));\n    };\n    this.decode = (options === null || options === void 0 ? void 0 : options.decode) ? options.decode : this.serializer.decode.bind(this.serializer);\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect();\n      this.connect();\n    }, this.reconnectAfterMs);\n    this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n    if (options === null || options === void 0 ? void 0 : options.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported');\n      }\n      this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n      this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n    }\n    this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n  }\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect() {\n    if (this.conn) {\n      return;\n    }\n    if (!this.transport) {\n      this.transport = WebSocket;\n    }\n    if (this.transport) {\n      // Detect if using the native browser WebSocket\n      const isBrowser = typeof window !== 'undefined' && this.transport === window.WebSocket;\n      if (isBrowser) {\n        this.conn = new this.transport(this.endpointURL());\n      } else {\n        this.conn = new this.transport(this.endpointURL(), undefined, {\n          headers: this.headers\n        });\n      }\n      this.setupConnection();\n      return;\n    }\n    this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n      close: () => {\n        this.conn = null;\n      }\n    });\n  }\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL() {\n    return this._appendParams(this.endPoint, Object.assign({}, this.params, {\n      vsn: VSN\n    }));\n  }\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code, reason) {\n    if (this.conn) {\n      this.conn.onclose = function () {}; // noop\n      if (code) {\n        this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n      } else {\n        this.conn.close();\n      }\n      this.conn = null;\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.reconnectTimer.reset();\n      this.channels.forEach(channel => channel.teardown());\n    }\n  }\n  /**\n   * Returns all created channels\n   */\n  getChannels() {\n    return this.channels;\n  }\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(channel) {\n    const status = await channel.unsubscribe();\n    this.channels = this.channels.filter(c => c._joinRef !== channel._joinRef);\n    if (this.channels.length === 0) {\n      this.disconnect();\n    }\n    return status;\n  }\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels() {\n    const values_1 = await Promise.all(this.channels.map(channel => channel.unsubscribe()));\n    this.channels = [];\n    this.disconnect();\n    return values_1;\n  }\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind, msg, data) {\n    this.logger(kind, msg, data);\n  }\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState() {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting;\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open;\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing;\n      default:\n        return CONNECTION_STATE.Closed;\n    }\n  }\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected() {\n    return this.connectionState() === CONNECTION_STATE.Open;\n  }\n  channel(topic, params = {\n    config: {}\n  }) {\n    const realtimeTopic = `realtime:${topic}`;\n    const exists = this.getChannels().find(c => c.topic === realtimeTopic);\n    if (!exists) {\n      const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n      this.channels.push(chan);\n      return chan;\n    } else {\n      return exists;\n    }\n  }\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data) {\n    const {\n      topic,\n      event,\n      payload,\n      ref\n    } = data;\n    const callback = () => {\n      this.encode(data, result => {\n        var _a;\n        (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n      });\n    };\n    this.log('push', `${topic} ${event} (${ref})`, payload);\n    if (this.isConnected()) {\n      callback();\n    } else {\n      this.sendBuffer.push(callback);\n    }\n  }\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token = null) {\n    let tokenToSend = token || this.accessToken && (await this.accessToken()) || this.accessTokenValue;\n    if (this.accessTokenValue != tokenToSend) {\n      this.accessTokenValue = tokenToSend;\n      this.channels.forEach(channel => {\n        tokenToSend && channel.updateJoinPayload({\n          access_token: tokenToSend,\n          version: this.headers && this.headers['X-Client-Info']\n        });\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend\n          });\n        }\n      });\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    var _a;\n    if (!this.isConnected()) {\n      this.heartbeatCallback('disconnected');\n      return;\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null;\n      this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n      this.heartbeatCallback('timeout');\n      (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n      return;\n    }\n    this.pendingHeartbeatRef = this._makeRef();\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef\n    });\n    this.heartbeatCallback('sent');\n    await this.setAuth();\n  }\n  onHeartbeat(callback) {\n    this.heartbeatCallback = callback;\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach(callback => callback());\n      this.sendBuffer = [];\n    }\n  }\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef() {\n    let newRef = this.ref + 1;\n    if (newRef === this.ref) {\n      this.ref = 0;\n    } else {\n      this.ref = newRef;\n    }\n    return this.ref.toString();\n  }\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic) {\n    let dupChannel = this.channels.find(c => c.topic === topic && (c._isJoined() || c._isJoining()));\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`);\n      dupChannel.unsubscribe();\n    }\n  }\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel) {\n    this.channels = this.channels.filter(c => c.topic !== channel.topic);\n  }\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  setupConnection() {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer';\n      this.conn.onopen = () => this._onConnOpen();\n      this.conn.onerror = error => this._onConnError(error);\n      this.conn.onmessage = event => this._onConnMessage(event);\n      this.conn.onclose = event => this._onConnClose(event);\n    }\n  }\n  /** @internal */\n  _onConnMessage(rawMessage) {\n    this.decode(rawMessage.data, msg => {\n      let {\n        topic,\n        event,\n        payload,\n        ref\n      } = msg;\n      if (topic === 'phoenix' && event === 'phx_reply') {\n        this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error');\n      }\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null;\n      }\n      this.log('receive', `${payload.status || ''} ${topic} ${event} ${ref && '(' + ref + ')' || ''}`, payload);\n      Array.from(this.channels).filter(channel => channel._isMember(topic)).forEach(channel => channel._trigger(event, payload, ref));\n      this.stateChangeCallbacks.message.forEach(callback => callback(msg));\n    });\n  }\n  /** @internal */\n  _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`);\n    this.flushSendBuffer();\n    this.reconnectTimer.reset();\n    if (!this.worker) {\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n    } else {\n      if (this.workerUrl) {\n        this.log('worker', `starting worker for from ${this.workerUrl}`);\n      } else {\n        this.log('worker', `starting default worker`);\n      }\n      const objectUrl = this._workerObjectUrl(this.workerUrl);\n      this.workerRef = new Worker(objectUrl);\n      this.workerRef.onerror = error => {\n        this.log('worker', 'worker error', error.message);\n        this.workerRef.terminate();\n      };\n      this.workerRef.onmessage = event => {\n        if (event.data.event === 'keepAlive') {\n          this.sendHeartbeat();\n        }\n      };\n      this.workerRef.postMessage({\n        event: 'start',\n        interval: this.heartbeatIntervalMs\n      });\n    }\n    this.stateChangeCallbacks.open.forEach(callback => callback());\n  }\n  /** @internal */\n  _onConnClose(event) {\n    this.log('transport', 'close', event);\n    this._triggerChanError();\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n    this.reconnectTimer.scheduleTimeout();\n    this.stateChangeCallbacks.close.forEach(callback => callback(event));\n  }\n  /** @internal */\n  _onConnError(error) {\n    this.log('transport', error.message);\n    this._triggerChanError();\n    this.stateChangeCallbacks.error.forEach(callback => callback(error));\n  }\n  /** @internal */\n  _triggerChanError() {\n    this.channels.forEach(channel => channel._trigger(CHANNEL_EVENTS.error));\n  }\n  /** @internal */\n  _appendParams(url, params) {\n    if (Object.keys(params).length === 0) {\n      return url;\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?';\n    const query = new URLSearchParams(params);\n    return `${url}${prefix}${query}`;\n  }\n  _workerObjectUrl(url) {\n    let result_url;\n    if (url) {\n      result_url = url;\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], {\n        type: 'application/javascript'\n      });\n      result_url = URL.createObjectURL(blob);\n    }\n    return result_url;\n  }\n}\nclass WSWebSocketDummy {\n  constructor(address, _protocols, options) {\n    this.binaryType = 'arraybuffer';\n    this.onclose = () => {};\n    this.onerror = () => {};\n    this.onmessage = () => {};\n    this.onopen = () => {};\n    this.readyState = SOCKET_STATES.connecting;\n    this.send = () => {};\n    this.url = null;\n    this.url = address;\n    this.close = options.close;\n  }\n}", "map": {"version": 3, "names": ["WebSocket", "CHANNEL_EVENTS", "CONNECTION_STATE", "DEFAULT_HEADERS", "DEFAULT_TIMEOUT", "SOCKET_STATES", "TRANSPORTS", "VSN", "WS_CLOSE_NORMAL", "Serializer", "Timer", "httpEndpointURL", "RealtimeChannel", "noop", "WORKER_SCRIPT", "RealtimeClient", "constructor", "endPoint", "options", "accessTokenValue", "<PERSON><PERSON><PERSON><PERSON>", "channels", "Array", "httpEndpoint", "headers", "params", "timeout", "heartbeatIntervalMs", "heartbeatTimer", "undefined", "pendingHeartbeatRef", "heartbeat<PERSON><PERSON><PERSON>", "ref", "logger", "conn", "send<PERSON><PERSON><PERSON>", "serializer", "stateChangeCallbacks", "open", "close", "error", "message", "accessToken", "_resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "websocket", "transport", "Object", "assign", "logLevel", "log_level", "_a", "apikey", "reconnectAfterMs", "tries", "encode", "payload", "callback", "JSON", "stringify", "decode", "bind", "reconnectTimer", "disconnect", "connect", "worker", "window", "Worker", "Error", "workerUrl", "<PERSON><PERSON><PERSON><PERSON>", "endpointURL", "setupConnection", "WSWebSocketDummy", "_appendParams", "vsn", "code", "reason", "onclose", "clearInterval", "reset", "for<PERSON>ach", "channel", "teardown", "getChannels", "removeChannel", "status", "unsubscribe", "filter", "c", "_joinRef", "length", "removeAllChannels", "values_1", "Promise", "all", "map", "log", "kind", "msg", "data", "connectionState", "readyState", "connecting", "Connecting", "Open", "closing", "Closing", "Closed", "isConnected", "topic", "config", "realtimeTopic", "exists", "find", "chan", "push", "event", "result", "send", "setAuth", "token", "tokenToSend", "updateJoinPayload", "access_token", "version", "joinedOnce", "_isJoined", "_push", "sendHeartbeat", "_makeRef", "onHeartbeat", "flushSendBuffer", "newRef", "toString", "_leaveOpenTopic", "dup<PERSON><PERSON><PERSON>", "_isJoining", "_remove", "binaryType", "onopen", "_onConnOpen", "onerror", "_onConnError", "onmessage", "_onConnMessage", "_onConnClose", "rawMessage", "from", "_isMember", "_trigger", "setInterval", "objectUrl", "_workerObjectUrl", "workerRef", "terminate", "postMessage", "interval", "_trigger<PERSON>hanError", "scheduleTimeout", "url", "keys", "prefix", "match", "query", "URLSearchParams", "result_url", "blob", "Blob", "type", "URL", "createObjectURL", "address", "_protocols"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@supabase/realtime-js/src/RealtimeClient.ts"], "sourcesContent": ["import WebSocket from './WebSocket'\n\nimport {\n  CHANNEL_EVENTS,\n  CONNECTION_STATE,\n  DEFAULT_HEADERS,\n  DEFAULT_TIMEOUT,\n  SOCKET_STATES,\n  TRANSPORTS,\n  VSN,\n  WS_CLOSE_NORMAL,\n} from './lib/constants'\n\nimport Serializer from './lib/serializer'\nimport Timer from './lib/timer'\n\nimport { httpEndpointURL } from './lib/transformers'\nimport RealtimeChannel from './RealtimeChannel'\nimport type { RealtimeChannelOptions } from './RealtimeChannel'\n\ntype Fetch = typeof fetch\n\nexport type Channel = {\n  name: string\n  inserted_at: string\n  updated_at: string\n  id: number\n}\nexport type LogLevel = 'info' | 'warn' | 'error'\n\nexport type RealtimeMessage = {\n  topic: string\n  event: string\n  payload: any\n  ref: string\n  join_ref?: string\n}\n\nexport type RealtimeRemoveChannelResponse = 'ok' | 'timed out' | 'error'\nexport type HeartbeatStatus =\n  | 'sent'\n  | 'ok'\n  | 'error'\n  | 'timeout'\n  | 'disconnected'\n\nconst noop = () => {}\n\nexport interface WebSocketLikeConstructor {\n  new (\n    address: string | URL,\n    _ignored?: any,\n    options?: { headers: Object | undefined }\n  ): WebSocketLike\n}\n\nexport type WebSocketLike = WebSocket | WSWebSocketDummy\n\nexport interface WebSocketLikeError {\n  error: any\n  message: string\n  type: string\n}\n\nexport type RealtimeClientOptions = {\n  transport?: WebSocketLikeConstructor\n  timeout?: number\n  heartbeatIntervalMs?: number\n  logger?: Function\n  encode?: Function\n  decode?: Function\n  reconnectAfterMs?: Function\n  headers?: { [key: string]: string }\n  params?: { [key: string]: any }\n  //Deprecated: Use it in favour of correct casing `logLevel`\n  log_level?: LogLevel\n  logLevel?: LogLevel\n  fetch?: Fetch\n  worker?: boolean\n  workerUrl?: string\n  accessToken?: () => Promise<string | null>\n}\n\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`\n\nexport default class RealtimeClient {\n  accessTokenValue: string | null = null\n  apiKey: string | null = null\n  channels: RealtimeChannel[] = new Array()\n  endPoint: string = ''\n  httpEndpoint: string = ''\n  headers?: { [key: string]: string } = DEFAULT_HEADERS\n  params?: { [key: string]: string } = {}\n  timeout: number = DEFAULT_TIMEOUT\n  transport: WebSocketLikeConstructor | null\n  heartbeatIntervalMs: number = 25000\n  heartbeatTimer: ReturnType<typeof setInterval> | undefined = undefined\n  pendingHeartbeatRef: string | null = null\n  heartbeatCallback: (status: HeartbeatStatus) => void = noop\n  ref: number = 0\n  reconnectTimer: Timer\n  logger: Function = noop\n  logLevel?: LogLevel\n  encode: Function\n  decode: Function\n  reconnectAfterMs: Function\n  conn: WebSocketLike | null = null\n  sendBuffer: Function[] = []\n  serializer: Serializer = new Serializer()\n  stateChangeCallbacks: {\n    open: Function[]\n    close: Function[]\n    error: Function[]\n    message: Function[]\n  } = {\n    open: [],\n    close: [],\n    error: [],\n    message: [],\n  }\n  fetch: Fetch\n  accessToken: (() => Promise<string | null>) | null = null\n  worker?: boolean\n  workerUrl?: string\n  workerRef?: Worker\n\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.logLevel Sets the log level for Realtime\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint: string, options?: RealtimeClientOptions) {\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`\n    this.httpEndpoint = httpEndpointURL(endPoint)\n    if (options?.transport) {\n      this.transport = options.transport\n    } else {\n      this.transport = null\n    }\n    if (options?.params) this.params = options.params\n    if (options?.headers) this.headers = { ...this.headers, ...options.headers }\n    if (options?.timeout) this.timeout = options.timeout\n    if (options?.logger) this.logger = options.logger\n    if (options?.logLevel || options?.log_level) {\n      this.logLevel = options.logLevel || options.log_level\n      this.params = { ...this.params, log_level: this.logLevel as string }\n    }\n\n    if (options?.heartbeatIntervalMs)\n      this.heartbeatIntervalMs = options.heartbeatIntervalMs\n\n    const accessTokenValue = options?.params?.apikey\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue\n      this.apiKey = accessTokenValue\n    }\n\n    this.reconnectAfterMs = options?.reconnectAfterMs\n      ? options.reconnectAfterMs\n      : (tries: number) => {\n          return [1000, 2000, 5000, 10000][tries - 1] || 10000\n        }\n    this.encode = options?.encode\n      ? options.encode\n      : (payload: JSON, callback: Function) => {\n          return callback(JSON.stringify(payload))\n        }\n    this.decode = options?.decode\n      ? options.decode\n      : this.serializer.decode.bind(this.serializer)\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect()\n      this.connect()\n    }, this.reconnectAfterMs)\n\n    this.fetch = this._resolveFetch(options?.fetch)\n    if (options?.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported')\n      }\n      this.worker = options?.worker || false\n      this.workerUrl = options?.workerUrl\n    }\n    this.accessToken = options?.accessToken || null\n  }\n\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect(): void {\n    if (this.conn) {\n      return\n    }\n    if (!this.transport) {\n      this.transport = WebSocket\n    }\n    if (this.transport) {\n      // Detect if using the native browser WebSocket\n      const isBrowser =\n        typeof window !== 'undefined' && this.transport === window.WebSocket\n      if (isBrowser) {\n        this.conn = new this.transport(this.endpointURL())\n      } else {\n        this.conn = new this.transport(this.endpointURL(), undefined, {\n          headers: this.headers,\n        })\n      }\n      this.setupConnection()\n      return\n    }\n    this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n      close: () => {\n        this.conn = null\n      },\n    })\n  }\n\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL(): string {\n    return this._appendParams(\n      this.endPoint,\n      Object.assign({}, this.params, { vsn: VSN })\n    )\n  }\n\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code?: number, reason?: string): void {\n    if (this.conn) {\n      this.conn.onclose = function () {} // noop\n      if (code) {\n        this.conn.close(code, reason ?? '')\n      } else {\n        this.conn.close()\n      }\n      this.conn = null\n\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.reconnectTimer.reset()\n      this.channels.forEach((channel) => channel.teardown())\n    }\n  }\n\n  /**\n   * Returns all created channels\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.channels\n  }\n\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(\n    channel: RealtimeChannel\n  ): Promise<RealtimeRemoveChannelResponse> {\n    const status = await channel.unsubscribe()\n    this.channels = this.channels.filter((c) => c._joinRef !== channel._joinRef)\n\n    if (this.channels.length === 0) {\n      this.disconnect()\n    }\n\n    return status\n  }\n\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels(): Promise<RealtimeRemoveChannelResponse[]> {\n    const values_1 = await Promise.all(\n      this.channels.map((channel) => channel.unsubscribe())\n    )\n    this.channels = []\n    this.disconnect()\n    return values_1\n  }\n\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind: string, msg: string, data?: any) {\n    this.logger(kind, msg, data)\n  }\n\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState(): CONNECTION_STATE {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing\n      default:\n        return CONNECTION_STATE.Closed\n    }\n  }\n\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected(): boolean {\n    return this.connectionState() === CONNECTION_STATE.Open\n  }\n\n  channel(\n    topic: string,\n    params: RealtimeChannelOptions = { config: {} }\n  ): RealtimeChannel {\n    const realtimeTopic = `realtime:${topic}`\n    const exists = this.getChannels().find(\n      (c: RealtimeChannel) => c.topic === realtimeTopic\n    )\n\n    if (!exists) {\n      const chan = new RealtimeChannel(`realtime:${topic}`, params, this)\n      this.channels.push(chan)\n\n      return chan\n    } else {\n      return exists\n    }\n  }\n\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data: RealtimeMessage): void {\n    const { topic, event, payload, ref } = data\n    const callback = () => {\n      this.encode(data, (result: any) => {\n        this.conn?.send(result)\n      })\n    }\n    this.log('push', `${topic} ${event} (${ref})`, payload)\n    if (this.isConnected()) {\n      callback()\n    } else {\n      this.sendBuffer.push(callback)\n    }\n  }\n\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token: string | null = null): Promise<void> {\n    let tokenToSend =\n      token ||\n      (this.accessToken && (await this.accessToken())) ||\n      this.accessTokenValue\n\n    if (this.accessTokenValue != tokenToSend) {\n      this.accessTokenValue = tokenToSend\n      this.channels.forEach((channel) => {\n        tokenToSend &&\n          channel.updateJoinPayload({\n            access_token: tokenToSend,\n            version: this.headers && this.headers['X-Client-Info'],\n          })\n\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend,\n          })\n        }\n      })\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    if (!this.isConnected()) {\n      this.heartbeatCallback('disconnected')\n      return\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null\n      this.log(\n        'transport',\n        'heartbeat timeout. Attempting to re-establish connection'\n      )\n      this.heartbeatCallback('timeout')\n      this.conn?.close(WS_CLOSE_NORMAL, 'hearbeat timeout')\n      return\n    }\n    this.pendingHeartbeatRef = this._makeRef()\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef,\n    })\n    this.heartbeatCallback('sent')\n    await this.setAuth()\n  }\n\n  onHeartbeat(callback: (status: HeartbeatStatus) => void): void {\n    this.heartbeatCallback = callback\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach((callback) => callback())\n      this.sendBuffer = []\n    }\n  }\n\n  /**\n   * Use either custom fetch, if provided, or default fetch to make HTTP requests\n   *\n   * @internal\n   */\n  _resolveFetch = (customFetch?: Fetch): Fetch => {\n    let _fetch: Fetch\n    if (customFetch) {\n      _fetch = customFetch\n    } else if (typeof fetch === 'undefined') {\n      _fetch = (...args) =>\n        import('@supabase/node-fetch' as any).then(({ default: fetch }) =>\n          fetch(...args)\n        )\n    } else {\n      _fetch = fetch\n    }\n    return (...args) => _fetch(...args)\n  }\n\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef(): string {\n    let newRef = this.ref + 1\n    if (newRef === this.ref) {\n      this.ref = 0\n    } else {\n      this.ref = newRef\n    }\n\n    return this.ref.toString()\n  }\n\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic: string): void {\n    let dupChannel = this.channels.find(\n      (c) => c.topic === topic && (c._isJoined() || c._isJoining())\n    )\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`)\n      dupChannel.unsubscribe()\n    }\n  }\n\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel: RealtimeChannel) {\n    this.channels = this.channels.filter((c) => c.topic !== channel.topic)\n  }\n\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  private setupConnection(): void {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer'\n      this.conn.onopen = () => this._onConnOpen()\n      this.conn.onerror = (error: WebSocketLikeError) =>\n        this._onConnError(error as WebSocketLikeError)\n      this.conn.onmessage = (event: any) => this._onConnMessage(event)\n      this.conn.onclose = (event: any) => this._onConnClose(event)\n    }\n  }\n\n  /** @internal */\n  private _onConnMessage(rawMessage: { data: any }) {\n    this.decode(rawMessage.data, (msg: RealtimeMessage) => {\n      let { topic, event, payload, ref } = msg\n\n      if (topic === 'phoenix' && event === 'phx_reply') {\n        this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error')\n      }\n\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null\n      }\n\n      this.log(\n        'receive',\n        `${payload.status || ''} ${topic} ${event} ${\n          (ref && '(' + ref + ')') || ''\n        }`,\n        payload\n      )\n\n      Array.from(this.channels)\n        .filter((channel: RealtimeChannel) => channel._isMember(topic))\n        .forEach((channel: RealtimeChannel) =>\n          channel._trigger(event, payload, ref)\n        )\n\n      this.stateChangeCallbacks.message.forEach((callback) => callback(msg))\n    })\n  }\n\n  /** @internal */\n  private _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`)\n    this.flushSendBuffer()\n    this.reconnectTimer.reset()\n    if (!this.worker) {\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.heartbeatTimer = setInterval(\n        () => this.sendHeartbeat(),\n        this.heartbeatIntervalMs\n      )\n    } else {\n      if (this.workerUrl) {\n        this.log('worker', `starting worker for from ${this.workerUrl}`)\n      } else {\n        this.log('worker', `starting default worker`)\n      }\n      const objectUrl = this._workerObjectUrl(this.workerUrl!)\n      this.workerRef = new Worker(objectUrl)\n      this.workerRef.onerror = (error) => {\n        this.log('worker', 'worker error', (error as ErrorEvent).message)\n        this.workerRef!.terminate()\n      }\n      this.workerRef.onmessage = (event) => {\n        if (event.data.event === 'keepAlive') {\n          this.sendHeartbeat()\n        }\n      }\n      this.workerRef.postMessage({\n        event: 'start',\n        interval: this.heartbeatIntervalMs,\n      })\n    }\n    this.stateChangeCallbacks.open.forEach((callback) => callback())\n  }\n\n  /** @internal */\n  private _onConnClose(event: any) {\n    this.log('transport', 'close', event)\n    this._triggerChanError()\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.reconnectTimer.scheduleTimeout()\n    this.stateChangeCallbacks.close.forEach((callback) => callback(event))\n  }\n\n  /** @internal */\n  private _onConnError(error: WebSocketLikeError) {\n    this.log('transport', error.message)\n    this._triggerChanError()\n    this.stateChangeCallbacks.error.forEach((callback) => callback(error))\n  }\n\n  /** @internal */\n  private _triggerChanError() {\n    this.channels.forEach((channel: RealtimeChannel) =>\n      channel._trigger(CHANNEL_EVENTS.error)\n    )\n  }\n\n  /** @internal */\n  private _appendParams(\n    url: string,\n    params: { [key: string]: string }\n  ): string {\n    if (Object.keys(params).length === 0) {\n      return url\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?'\n    const query = new URLSearchParams(params)\n    return `${url}${prefix}${query}`\n  }\n\n  private _workerObjectUrl(url: string | undefined): string {\n    let result_url: string\n    if (url) {\n      result_url = url\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' })\n      result_url = URL.createObjectURL(blob)\n    }\n    return result_url\n  }\n}\n\nclass WSWebSocketDummy {\n  binaryType: string = 'arraybuffer'\n  close: Function\n  onclose: Function = () => {}\n  onerror: Function = () => {}\n  onmessage: Function = () => {}\n  onopen: Function = () => {}\n  readyState: number = SOCKET_STATES.connecting\n  send: Function = () => {}\n  url: string | URL | null = null\n\n  constructor(\n    address: string,\n    _protocols: undefined,\n    options: { close: Function }\n  ) {\n    this.url = address\n    this.close = options.close\n  }\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AAEnC,SACEC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,UAAU,EACVC,GAAG,EACHC,eAAe,QACV,iBAAiB;AAExB,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,KAAK,MAAM,aAAa;AAE/B,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AA6B/C,MAAMC,IAAI,GAAGA,CAAA,KAAK,CAAE,CAAC;AAqCrB,MAAMC,aAAa,GAAG;;;;;MAKhB;AAEN,eAAc,MAAOC,cAAc;EAyCjC;;;;;;;;;;;;;;;;;;EAkBAC,YAAYC,QAAgB,EAAEC,OAA+B;;IA1D7D,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAAC,QAAQ,GAAsB,IAAIC,KAAK,EAAE;IACzC,KAAAL,QAAQ,GAAW,EAAE;IACrB,KAAAM,YAAY,GAAW,EAAE;IACzB,KAAAC,OAAO,GAA+BrB,eAAe;IACrD,KAAAsB,MAAM,GAA+B,EAAE;IACvC,KAAAC,OAAO,GAAWtB,eAAe;IAEjC,KAAAuB,mBAAmB,GAAW,KAAK;IACnC,KAAAC,cAAc,GAA+CC,SAAS;IACtE,KAAAC,mBAAmB,GAAkB,IAAI;IACzC,KAAAC,iBAAiB,GAAsClB,IAAI;IAC3D,KAAAmB,GAAG,GAAW,CAAC;IAEf,KAAAC,MAAM,GAAapB,IAAI;IAKvB,KAAAqB,IAAI,GAAyB,IAAI;IACjC,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,UAAU,GAAe,IAAI3B,UAAU,EAAE;IACzC,KAAA4B,oBAAoB,GAKhB;MACFC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;KACV;IAED,KAAAC,WAAW,GAA0C,IAAI;IAoUzD;;;;;IAKA,KAAAC,aAAa,GAAIC,WAAmB,IAAW;MAC7C,IAAIC,MAAa;MACjB,IAAID,WAAW,EAAE;QACfC,MAAM,GAAGD,WAAW;MACtB,CAAC,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;QACvCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC,OAAO,EAAEH;QAAK,CAAE,KAC5DA,KAAK,CAAC,GAAGC,IAAI,CAAC,CACf;MACL,CAAC,MAAM;QACLF,MAAM,GAAGC,KAAK;MAChB;MACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;IACrC,CAAC;IA9TC,IAAI,CAAC9B,QAAQ,GAAG,GAAGA,QAAQ,IAAIX,UAAU,CAAC4C,SAAS,EAAE;IACrD,IAAI,CAAC3B,YAAY,GAAGZ,eAAe,CAACM,QAAQ,CAAC;IAC7C,IAAIC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAGjC,OAAO,CAACiC,SAAS;IACpC,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI;IACvB;IACA,IAAIjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGP,OAAO,CAACO,MAAM;IACjD,IAAIP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,OAAO,EAAE,IAAI,CAACA,OAAO,GAAA4B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC7B,OAAO,GAAKN,OAAO,CAACM,OAAO,CAAE;IAC5E,IAAIN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,OAAO,EAAE,IAAI,CAACA,OAAO,GAAGR,OAAO,CAACQ,OAAO;IACpD,IAAIR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGf,OAAO,CAACe,MAAM;IACjD,IAAI,CAAAf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,QAAQ,MAAIpC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,SAAS,GAAE;MAC3C,IAAI,CAACD,QAAQ,GAAGpC,OAAO,CAACoC,QAAQ,IAAIpC,OAAO,CAACqC,SAAS;MACrD,IAAI,CAAC9B,MAAM,GAAA2B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC5B,MAAM;QAAE8B,SAAS,EAAE,IAAI,CAACD;MAAkB,EAAE;IACtE;IAEA,IAAIpC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,mBAAmB,EAC9B,IAAI,CAACA,mBAAmB,GAAGT,OAAO,CAACS,mBAAmB;IAExD,MAAMR,gBAAgB,GAAG,CAAAqC,EAAA,GAAAtC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,MAAM,cAAA+B,EAAA,uBAAAA,EAAA,CAAEC,MAAM;IAChD,IAAItC,gBAAgB,EAAE;MACpB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,MAAM,GAAGD,gBAAgB;IAChC;IAEA,IAAI,CAACuC,gBAAgB,GAAG,CAAAxC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,gBAAgB,IAC7CxC,OAAO,CAACwC,gBAAgB,GACvBC,KAAa,IAAI;MAChB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACA,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK;IACtD,CAAC;IACL,IAAI,CAACC,MAAM,GAAG,CAAA1C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0C,MAAM,IACzB1C,OAAO,CAAC0C,MAAM,GACd,CAACC,OAAa,EAAEC,QAAkB,KAAI;MACpC,OAAOA,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC1C,CAAC;IACL,IAAI,CAACI,MAAM,GAAG,CAAA/C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+C,MAAM,IACzB/C,OAAO,CAAC+C,MAAM,GACd,IAAI,CAAC7B,UAAU,CAAC6B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC;IAChD,IAAI,CAAC+B,cAAc,GAAG,IAAIzD,KAAK,CAAC,YAAW;MACzC,IAAI,CAAC0D,UAAU,EAAE;MACjB,IAAI,CAACC,OAAO,EAAE;IAChB,CAAC,EAAE,IAAI,CAACX,gBAAgB,CAAC;IAEzB,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACH,aAAa,CAACzB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,KAAK,CAAC;IAC/C,IAAI5B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoD,MAAM,EAAE;MACnB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAChD;MACA,IAAI,CAACH,MAAM,GAAG,CAAApD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoD,MAAM,KAAI,KAAK;MACtC,IAAI,CAACI,SAAS,GAAGxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwD,SAAS;IACrC;IACA,IAAI,CAAChC,WAAW,GAAG,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,WAAW,KAAI,IAAI;EACjD;EAEA;;;EAGA2B,OAAOA,CAAA;IACL,IAAI,IAAI,CAACnC,IAAI,EAAE;MACb;IACF;IACA,IAAI,CAAC,IAAI,CAACiB,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAGnD,SAAS;IAC5B;IACA,IAAI,IAAI,CAACmD,SAAS,EAAE;MAClB;MACA,MAAMwB,SAAS,GACb,OAAOJ,MAAM,KAAK,WAAW,IAAI,IAAI,CAACpB,SAAS,KAAKoB,MAAM,CAACvE,SAAS;MACtE,IAAI2E,SAAS,EAAE;QACb,IAAI,CAACzC,IAAI,GAAG,IAAI,IAAI,CAACiB,SAAS,CAAC,IAAI,CAACyB,WAAW,EAAE,CAAC;MACpD,CAAC,MAAM;QACL,IAAI,CAAC1C,IAAI,GAAG,IAAI,IAAI,CAACiB,SAAS,CAAC,IAAI,CAACyB,WAAW,EAAE,EAAE/C,SAAS,EAAE;UAC5DL,OAAO,EAAE,IAAI,CAACA;SACf,CAAC;MACJ;MACA,IAAI,CAACqD,eAAe,EAAE;MACtB;IACF;IACA,IAAI,CAAC3C,IAAI,GAAG,IAAI4C,gBAAgB,CAAC,IAAI,CAACF,WAAW,EAAE,EAAE/C,SAAS,EAAE;MAC9DU,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACL,IAAI,GAAG,IAAI;MAClB;KACD,CAAC;EACJ;EAEA;;;;EAIA0C,WAAWA,CAAA;IACT,OAAO,IAAI,CAACG,aAAa,CACvB,IAAI,CAAC9D,QAAQ,EACbmC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC5B,MAAM,EAAE;MAAEuD,GAAG,EAAEzE;IAAG,CAAE,CAAC,CAC7C;EACH;EAEA;;;;;;EAMA6D,UAAUA,CAACa,IAAa,EAAEC,MAAe;IACvC,IAAI,IAAI,CAAChD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACiD,OAAO,GAAG,aAAa,CAAC,EAAC;MACnC,IAAIF,IAAI,EAAE;QACR,IAAI,CAAC/C,IAAI,CAACK,KAAK,CAAC0C,IAAI,EAAEC,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QACL,IAAI,CAAChD,IAAI,CAACK,KAAK,EAAE;MACnB;MACA,IAAI,CAACL,IAAI,GAAG,IAAI;MAEhB;MACA,IAAI,CAACN,cAAc,IAAIwD,aAAa,CAAC,IAAI,CAACxD,cAAc,CAAC;MACzD,IAAI,CAACuC,cAAc,CAACkB,KAAK,EAAE;MAC3B,IAAI,CAAChE,QAAQ,CAACiE,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAACC,QAAQ,EAAE,CAAC;IACxD;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACpE,QAAQ;EACtB;EAEA;;;;EAIA,MAAMqE,aAAaA,CACjBH,OAAwB;IAExB,MAAMI,MAAM,GAAG,MAAMJ,OAAO,CAACK,WAAW,EAAE;IAC1C,IAAI,CAACvE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACwE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,KAAKR,OAAO,CAACQ,QAAQ,CAAC;IAE5E,IAAI,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAAC5B,UAAU,EAAE;IACnB;IAEA,OAAOuB,MAAM;EACf;EAEA;;;EAGA,MAAMM,iBAAiBA,CAAA;IACrB,MAAMC,QAAQ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAChC,IAAI,CAAC/E,QAAQ,CAACgF,GAAG,CAAEd,OAAO,IAAKA,OAAO,CAACK,WAAW,EAAE,CAAC,CACtD;IACD,IAAI,CAACvE,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC+C,UAAU,EAAE;IACjB,OAAO8B,QAAQ;EACjB;EAEA;;;;;EAKAI,GAAGA,CAACC,IAAY,EAAEC,GAAW,EAAEC,IAAU;IACvC,IAAI,CAACxE,MAAM,CAACsE,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAC9B;EAEA;;;EAGAC,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACxE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACyE,UAAU;MACvC,KAAKtG,aAAa,CAACuG,UAAU;QAC3B,OAAO1G,gBAAgB,CAAC2G,UAAU;MACpC,KAAKxG,aAAa,CAACiC,IAAI;QACrB,OAAOpC,gBAAgB,CAAC4G,IAAI;MAC9B,KAAKzG,aAAa,CAAC0G,OAAO;QACxB,OAAO7G,gBAAgB,CAAC8G,OAAO;MACjC;QACE,OAAO9G,gBAAgB,CAAC+G,MAAM;IAClC;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACR,eAAe,EAAE,KAAKxG,gBAAgB,CAAC4G,IAAI;EACzD;EAEAvB,OAAOA,CACL4B,KAAa,EACb1F,MAAA,GAAiC;IAAE2F,MAAM,EAAE;EAAE,CAAE;IAE/C,MAAMC,aAAa,GAAG,YAAYF,KAAK,EAAE;IACzC,MAAMG,MAAM,GAAG,IAAI,CAAC7B,WAAW,EAAE,CAAC8B,IAAI,CACnCzB,CAAkB,IAAKA,CAAC,CAACqB,KAAK,KAAKE,aAAa,CAClD;IAED,IAAI,CAACC,MAAM,EAAE;MACX,MAAME,IAAI,GAAG,IAAI5G,eAAe,CAAC,YAAYuG,KAAK,EAAE,EAAE1F,MAAM,EAAE,IAAI,CAAC;MACnE,IAAI,CAACJ,QAAQ,CAACoG,IAAI,CAACD,IAAI,CAAC;MAExB,OAAOA,IAAI;IACb,CAAC,MAAM;MACL,OAAOF,MAAM;IACf;EACF;EAEA;;;;;EAKAG,IAAIA,CAAChB,IAAqB;IACxB,MAAM;MAAEU,KAAK;MAAEO,KAAK;MAAE7D,OAAO;MAAE7B;IAAG,CAAE,GAAGyE,IAAI;IAC3C,MAAM3C,QAAQ,GAAGA,CAAA,KAAK;MACpB,IAAI,CAACF,MAAM,CAAC6C,IAAI,EAAGkB,MAAW,IAAI;;QAChC,CAAAnE,EAAA,OAAI,CAACtB,IAAI,cAAAsB,EAAA,uBAAAA,EAAA,CAAEoE,IAAI,CAACD,MAAM,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACrB,GAAG,CAAC,MAAM,EAAE,GAAGa,KAAK,IAAIO,KAAK,KAAK1F,GAAG,GAAG,EAAE6B,OAAO,CAAC;IACvD,IAAI,IAAI,CAACqD,WAAW,EAAE,EAAE;MACtBpD,QAAQ,EAAE;IACZ,CAAC,MAAM;MACL,IAAI,CAAC3B,UAAU,CAACsF,IAAI,CAAC3D,QAAQ,CAAC;IAChC;EACF;EAEA;;;;;;;;;EASA,MAAM+D,OAAOA,CAACC,KAAA,GAAuB,IAAI;IACvC,IAAIC,WAAW,GACbD,KAAK,IACJ,IAAI,CAACpF,WAAW,KAAK,MAAM,IAAI,CAACA,WAAW,EAAE,CAAE,IAChD,IAAI,CAACvB,gBAAgB;IAEvB,IAAI,IAAI,CAACA,gBAAgB,IAAI4G,WAAW,EAAE;MACxC,IAAI,CAAC5G,gBAAgB,GAAG4G,WAAW;MACnC,IAAI,CAAC1G,QAAQ,CAACiE,OAAO,CAAEC,OAAO,IAAI;QAChCwC,WAAW,IACTxC,OAAO,CAACyC,iBAAiB,CAAC;UACxBC,YAAY,EAAEF,WAAW;UACzBG,OAAO,EAAE,IAAI,CAAC1G,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,eAAe;SACtD,CAAC;QAEJ,IAAI+D,OAAO,CAAC4C,UAAU,IAAI5C,OAAO,CAAC6C,SAAS,EAAE,EAAE;UAC7C7C,OAAO,CAAC8C,KAAK,CAACpI,cAAc,CAACgI,YAAY,EAAE;YACzCA,YAAY,EAAEF;WACf,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;EACA;;;EAGA,MAAMO,aAAaA,CAAA;;IACjB,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE,EAAE;MACvB,IAAI,CAACnF,iBAAiB,CAAC,cAAc,CAAC;MACtC;IACF;IACA,IAAI,IAAI,CAACD,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACwE,GAAG,CACN,WAAW,EACX,0DAA0D,CAC3D;MACD,IAAI,CAACvE,iBAAiB,CAAC,SAAS,CAAC;MACjC,CAAAyB,EAAA,OAAI,CAACtB,IAAI,cAAAsB,EAAA,uBAAAA,EAAA,CAAEjB,KAAK,CAAC/B,eAAe,EAAE,kBAAkB,CAAC;MACrD;IACF;IACA,IAAI,CAACsB,mBAAmB,GAAG,IAAI,CAACyG,QAAQ,EAAE;IAC1C,IAAI,CAACd,IAAI,CAAC;MACRN,KAAK,EAAE,SAAS;MAChBO,KAAK,EAAE,WAAW;MAClB7D,OAAO,EAAE,EAAE;MACX7B,GAAG,EAAE,IAAI,CAACF;KACX,CAAC;IACF,IAAI,CAACC,iBAAiB,CAAC,MAAM,CAAC;IAC9B,MAAM,IAAI,CAAC8F,OAAO,EAAE;EACtB;EAEAW,WAAWA,CAAC1E,QAA2C;IACrD,IAAI,CAAC/B,iBAAiB,GAAG+B,QAAQ;EACnC;EACA;;;EAGA2E,eAAeA,CAAA;IACb,IAAI,IAAI,CAACvB,WAAW,EAAE,IAAI,IAAI,CAAC/E,UAAU,CAAC6D,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAAC7D,UAAU,CAACmD,OAAO,CAAExB,QAAQ,IAAKA,QAAQ,EAAE,CAAC;MACjD,IAAI,CAAC3B,UAAU,GAAG,EAAE;IACtB;EACF;EAsBA;;;;;EAKAoG,QAAQA,CAAA;IACN,IAAIG,MAAM,GAAG,IAAI,CAAC1G,GAAG,GAAG,CAAC;IACzB,IAAI0G,MAAM,KAAK,IAAI,CAAC1G,GAAG,EAAE;MACvB,IAAI,CAACA,GAAG,GAAG,CAAC;IACd,CAAC,MAAM;MACL,IAAI,CAACA,GAAG,GAAG0G,MAAM;IACnB;IAEA,OAAO,IAAI,CAAC1G,GAAG,CAAC2G,QAAQ,EAAE;EAC5B;EAEA;;;;;EAKAC,eAAeA,CAACzB,KAAa;IAC3B,IAAI0B,UAAU,GAAG,IAAI,CAACxH,QAAQ,CAACkG,IAAI,CAChCzB,CAAC,IAAKA,CAAC,CAACqB,KAAK,KAAKA,KAAK,KAAKrB,CAAC,CAACsC,SAAS,EAAE,IAAItC,CAAC,CAACgD,UAAU,EAAE,CAAC,CAC9D;IACD,IAAID,UAAU,EAAE;MACd,IAAI,CAACvC,GAAG,CAAC,WAAW,EAAE,4BAA4Ba,KAAK,GAAG,CAAC;MAC3D0B,UAAU,CAACjD,WAAW,EAAE;IAC1B;EACF;EAEA;;;;;;;EAOAmD,OAAOA,CAACxD,OAAwB;IAC9B,IAAI,CAAClE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACwE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACqB,KAAK,KAAK5B,OAAO,CAAC4B,KAAK,CAAC;EACxE;EAEA;;;;;EAKQtC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAAC3C,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC8G,UAAU,GAAG,aAAa;MACpC,IAAI,CAAC9G,IAAI,CAAC+G,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,EAAE;MAC3C,IAAI,CAAChH,IAAI,CAACiH,OAAO,GAAI3G,KAAyB,IAC5C,IAAI,CAAC4G,YAAY,CAAC5G,KAA2B,CAAC;MAChD,IAAI,CAACN,IAAI,CAACmH,SAAS,GAAI3B,KAAU,IAAK,IAAI,CAAC4B,cAAc,CAAC5B,KAAK,CAAC;MAChE,IAAI,CAACxF,IAAI,CAACiD,OAAO,GAAIuC,KAAU,IAAK,IAAI,CAAC6B,YAAY,CAAC7B,KAAK,CAAC;IAC9D;EACF;EAEA;EACQ4B,cAAcA,CAACE,UAAyB;IAC9C,IAAI,CAACvF,MAAM,CAACuF,UAAU,CAAC/C,IAAI,EAAGD,GAAoB,IAAI;MACpD,IAAI;QAAEW,KAAK;QAAEO,KAAK;QAAE7D,OAAO;QAAE7B;MAAG,CAAE,GAAGwE,GAAG;MAExC,IAAIW,KAAK,KAAK,SAAS,IAAIO,KAAK,KAAK,WAAW,EAAE;QAChD,IAAI,CAAC3F,iBAAiB,CAACyE,GAAG,CAAC3C,OAAO,CAAC8B,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC;MACrE;MAEA,IAAI3D,GAAG,IAAIA,GAAG,KAAK,IAAI,CAACF,mBAAmB,EAAE;QAC3C,IAAI,CAACA,mBAAmB,GAAG,IAAI;MACjC;MAEA,IAAI,CAACwE,GAAG,CACN,SAAS,EACT,GAAGzC,OAAO,CAAC8B,MAAM,IAAI,EAAE,IAAIwB,KAAK,IAAIO,KAAK,IACtC1F,GAAG,IAAI,GAAG,GAAGA,GAAG,GAAG,GAAG,IAAK,EAC9B,EAAE,EACF6B,OAAO,CACR;MAEDvC,KAAK,CAACmI,IAAI,CAAC,IAAI,CAACpI,QAAQ,CAAC,CACtBwE,MAAM,CAAEN,OAAwB,IAAKA,OAAO,CAACmE,SAAS,CAACvC,KAAK,CAAC,CAAC,CAC9D7B,OAAO,CAAEC,OAAwB,IAChCA,OAAO,CAACoE,QAAQ,CAACjC,KAAK,EAAE7D,OAAO,EAAE7B,GAAG,CAAC,CACtC;MAEH,IAAI,CAACK,oBAAoB,CAACI,OAAO,CAAC6C,OAAO,CAAExB,QAAQ,IAAKA,QAAQ,CAAC0C,GAAG,CAAC,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;EACQ0C,WAAWA,CAAA;IACjB,IAAI,CAAC5C,GAAG,CAAC,WAAW,EAAE,gBAAgB,IAAI,CAAC1B,WAAW,EAAE,EAAE,CAAC;IAC3D,IAAI,CAAC6D,eAAe,EAAE;IACtB,IAAI,CAACtE,cAAc,CAACkB,KAAK,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;MAChB,IAAI,CAAC1C,cAAc,IAAIwD,aAAa,CAAC,IAAI,CAACxD,cAAc,CAAC;MACzD,IAAI,CAACA,cAAc,GAAGgI,WAAW,CAC/B,MAAM,IAAI,CAACtB,aAAa,EAAE,EAC1B,IAAI,CAAC3G,mBAAmB,CACzB;IACH,CAAC,MAAM;MACL,IAAI,IAAI,CAAC+C,SAAS,EAAE;QAClB,IAAI,CAAC4B,GAAG,CAAC,QAAQ,EAAE,4BAA4B,IAAI,CAAC5B,SAAS,EAAE,CAAC;MAClE,CAAC,MAAM;QACL,IAAI,CAAC4B,GAAG,CAAC,QAAQ,EAAE,yBAAyB,CAAC;MAC/C;MACA,MAAMuD,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACpF,SAAU,CAAC;MACxD,IAAI,CAACqF,SAAS,GAAG,IAAIvF,MAAM,CAACqF,SAAS,CAAC;MACtC,IAAI,CAACE,SAAS,CAACZ,OAAO,GAAI3G,KAAK,IAAI;QACjC,IAAI,CAAC8D,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAG9D,KAAoB,CAACC,OAAO,CAAC;QACjE,IAAI,CAACsH,SAAU,CAACC,SAAS,EAAE;MAC7B,CAAC;MACD,IAAI,CAACD,SAAS,CAACV,SAAS,GAAI3B,KAAK,IAAI;QACnC,IAAIA,KAAK,CAACjB,IAAI,CAACiB,KAAK,KAAK,WAAW,EAAE;UACpC,IAAI,CAACY,aAAa,EAAE;QACtB;MACF,CAAC;MACD,IAAI,CAACyB,SAAS,CAACE,WAAW,CAAC;QACzBvC,KAAK,EAAE,OAAO;QACdwC,QAAQ,EAAE,IAAI,CAACvI;OAChB,CAAC;IACJ;IACA,IAAI,CAACU,oBAAoB,CAACC,IAAI,CAACgD,OAAO,CAAExB,QAAQ,IAAKA,QAAQ,EAAE,CAAC;EAClE;EAEA;EACQyF,YAAYA,CAAC7B,KAAU;IAC7B,IAAI,CAACpB,GAAG,CAAC,WAAW,EAAE,OAAO,EAAEoB,KAAK,CAAC;IACrC,IAAI,CAACyC,iBAAiB,EAAE;IACxB,IAAI,CAACvI,cAAc,IAAIwD,aAAa,CAAC,IAAI,CAACxD,cAAc,CAAC;IACzD,IAAI,CAACuC,cAAc,CAACiG,eAAe,EAAE;IACrC,IAAI,CAAC/H,oBAAoB,CAACE,KAAK,CAAC+C,OAAO,CAAExB,QAAQ,IAAKA,QAAQ,CAAC4D,KAAK,CAAC,CAAC;EACxE;EAEA;EACQ0B,YAAYA,CAAC5G,KAAyB;IAC5C,IAAI,CAAC8D,GAAG,CAAC,WAAW,EAAE9D,KAAK,CAACC,OAAO,CAAC;IACpC,IAAI,CAAC0H,iBAAiB,EAAE;IACxB,IAAI,CAAC9H,oBAAoB,CAACG,KAAK,CAAC8C,OAAO,CAAExB,QAAQ,IAAKA,QAAQ,CAACtB,KAAK,CAAC,CAAC;EACxE;EAEA;EACQ2H,iBAAiBA,CAAA;IACvB,IAAI,CAAC9I,QAAQ,CAACiE,OAAO,CAAEC,OAAwB,IAC7CA,OAAO,CAACoE,QAAQ,CAAC1J,cAAc,CAACuC,KAAK,CAAC,CACvC;EACH;EAEA;EACQuC,aAAaA,CACnBsF,GAAW,EACX5I,MAAiC;IAEjC,IAAI2B,MAAM,CAACkH,IAAI,CAAC7I,MAAM,CAAC,CAACuE,MAAM,KAAK,CAAC,EAAE;MACpC,OAAOqE,GAAG;IACZ;IACA,MAAME,MAAM,GAAGF,GAAG,CAACG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IAC1C,MAAMC,KAAK,GAAG,IAAIC,eAAe,CAACjJ,MAAM,CAAC;IACzC,OAAO,GAAG4I,GAAG,GAAGE,MAAM,GAAGE,KAAK,EAAE;EAClC;EAEQX,gBAAgBA,CAACO,GAAuB;IAC9C,IAAIM,UAAkB;IACtB,IAAIN,GAAG,EAAE;MACPM,UAAU,GAAGN,GAAG;IAClB,CAAC,MAAM;MACL,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC/J,aAAa,CAAC,EAAE;QAAEgK,IAAI,EAAE;MAAwB,CAAE,CAAC;MAC1EH,UAAU,GAAGI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACxC;IACA,OAAOD,UAAU;EACnB;;AAGF,MAAM7F,gBAAgB;EAWpB9D,YACEiK,OAAe,EACfC,UAAqB,EACrBhK,OAA4B;IAb9B,KAAA8H,UAAU,GAAW,aAAa;IAElC,KAAA7D,OAAO,GAAa,MAAK,CAAE,CAAC;IAC5B,KAAAgE,OAAO,GAAa,MAAK,CAAE,CAAC;IAC5B,KAAAE,SAAS,GAAa,MAAK,CAAE,CAAC;IAC9B,KAAAJ,MAAM,GAAa,MAAK,CAAE,CAAC;IAC3B,KAAAtC,UAAU,GAAWtG,aAAa,CAACuG,UAAU;IAC7C,KAAAgB,IAAI,GAAa,MAAK,CAAE,CAAC;IACzB,KAAAyC,GAAG,GAAwB,IAAI;IAO7B,IAAI,CAACA,GAAG,GAAGY,OAAO;IAClB,IAAI,CAAC1I,KAAK,GAAGrB,OAAO,CAACqB,KAAK;EAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}