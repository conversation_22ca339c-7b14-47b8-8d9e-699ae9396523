{"ast": null, "code": "import { Point } from '../classes';\nexport var IOU_THRESHOLD = 0.4;\nexport var BOX_ANCHORS = [new Point(1.603231, 2.094468), new Point(6.041143, 7.080126), new Point(2.882459, 3.518061), new Point(4.266906, 5.178857), new Point(9.041765, 10.66308)];\nexport var MEAN_RGB = [117.001, 114.697, 97.404];", "map": {"version": 3, "names": ["Point", "IOU_THRESHOLD", "BOX_ANCHORS", "MEAN_RGB"], "sources": ["../../../src/tinyFaceDetector/const.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAElC,OAAO,IAAMC,aAAa,GAAG,GAAG;AAEhC,OAAO,IAAMC,WAAW,GAAG,CACzB,IAAIF,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAC9B;AAED,OAAO,IAAMG,QAAQ,GAA6B,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}