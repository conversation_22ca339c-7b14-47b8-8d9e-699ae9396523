{"ast": null, "code": "export function getSizesForScale(scale, _a) {\n  var height = _a[0],\n    width = _a[1];\n  return {\n    height: Math.floor(height * scale),\n    width: Math.floor(width * scale)\n  };\n}", "map": {"version": 3, "names": ["getSizesForScale", "scale", "_a", "height", "width", "Math", "floor"], "sources": ["../../../src/mtcnn/getSizesForScale.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,gBAAgBA,CAACC,KAAa,EAAEC,EAAyB;MAAxBC,MAAA,GAAAD,EAAA,GAAM;IAAEE,KAAA,GAAAF,EAAA,GAAK;EAC5D,OAAO;IACLC,MAAM,EAAEE,IAAI,CAACC,KAAK,CAACH,MAAM,GAAGF,KAAK,CAAC;IAClCG,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGH,KAAK;GAChC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}