{"ast": null, "code": "export function shallowEqual(a, b) {\n  /* eslint-disable no-restricted-syntax */\n  for (var key in a) {\n    if ({}.hasOwnProperty.call(a, key) && (!{}.hasOwnProperty.call(b, key) || a[key] !== b[key])) {\n      return false;\n    }\n  }\n  for (var _key in b) {\n    if ({}.hasOwnProperty.call(b, _key) && !{}.hasOwnProperty.call(a, _key)) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["shallowEqual", "a", "b", "key", "hasOwnProperty", "call", "_key"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/recharts/es6/util/ShallowEqual.js"], "sourcesContent": ["export function shallowEqual(a, b) {\n  /* eslint-disable no-restricted-syntax */\n  for (var key in a) {\n    if ({}.hasOwnProperty.call(a, key) && (!{}.hasOwnProperty.call(b, key) || a[key] !== b[key])) {\n      return false;\n    }\n  }\n  for (var _key in b) {\n    if ({}.hasOwnProperty.call(b, _key) && !{}.hasOwnProperty.call(a, _key)) {\n      return false;\n    }\n  }\n  return true;\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjC;EACA,KAAK,IAAIC,GAAG,IAAIF,CAAC,EAAE;IACjB,IAAI,CAAC,CAAC,CAACG,cAAc,CAACC,IAAI,CAACJ,CAAC,EAAEE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,GAAG,CAAC,IAAIF,CAAC,CAACE,GAAG,CAAC,KAAKD,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE;MAC5F,OAAO,KAAK;IACd;EACF;EACA,KAAK,IAAIG,IAAI,IAAIJ,CAAC,EAAE;IAClB,IAAI,CAAC,CAAC,CAACE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAACF,cAAc,CAACC,IAAI,CAACJ,CAAC,EAAEK,IAAI,CAAC,EAAE;MACvE,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}