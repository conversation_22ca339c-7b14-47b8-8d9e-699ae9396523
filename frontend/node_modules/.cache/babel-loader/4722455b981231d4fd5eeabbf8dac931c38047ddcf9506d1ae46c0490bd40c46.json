{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, TextField, Button, Typography, Alert, CircularProgress, Tabs, Tab } from '@mui/material';\nimport { Login as LoginIcon, PersonAdd as PersonAddIcon, Logout as LogoutIcon } from '@mui/icons-material';\nimport { supabase } from '../../config/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `auth-tabpanel-${index}`,\n    \"aria-labelledby\": `auth-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst SupabaseAuth = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState(null);\n  const [tabValue, setTabValue] = useState(0);\n\n  // Formulaires\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const [signupForm, setSignupForm] = useState({\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  useEffect(() => {\n    // Vérifier la session actuelle\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      var _session$user;\n      setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n    });\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      var _session$user2;\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const handleLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage(null);\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email: loginForm.email,\n        password: loginForm.password\n      });\n      if (error) throw error;\n      setMessage({\n        type: 'success',\n        text: 'Connexion réussie !'\n      });\n      setLoginForm({\n        email: '',\n        password: ''\n      });\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      setMessage({\n        type: 'error',\n        text: error instanceof Error ? error.message : 'Erreur de connexion'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSignup = async e => {\n    e.preventDefault();\n    if (signupForm.password !== signupForm.confirmPassword) {\n      setMessage({\n        type: 'error',\n        text: 'Les mots de passe ne correspondent pas'\n      });\n      return;\n    }\n    setLoading(true);\n    setMessage(null);\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email: signupForm.email,\n        password: signupForm.password\n      });\n      if (error) throw error;\n      setMessage({\n        type: 'success',\n        text: 'Compte créé ! Vérifiez votre email pour confirmer votre compte.'\n      });\n      setSignupForm({\n        email: '',\n        password: '',\n        confirmPassword: ''\n      });\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setMessage({\n        type: 'error',\n        text: error instanceof Error ? error.message : 'Erreur d\\'inscription'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = async () => {\n    setLoading(true);\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) throw error;\n      setMessage({\n        type: 'success',\n        text: 'Déconnexion réussie !'\n      });\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n      setMessage({\n        type: 'error',\n        text: error instanceof Error ? error.message : 'Erreur de déconnexion'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (user) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Utilisateur Connect\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: message.type,\n            sx: {\n              mb: 2\n            },\n            onClose: () => setMessage(null),\n            children: message.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), \" \", user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), \" \", user.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Confirm\\xE9:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), \" \", user.email_confirmed_at ? 'Oui' : 'Non']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Derni\\xE8re connexion:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), \" \", user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 26\n            }, this),\n            onClick: handleLogout,\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 26\n            }, this) : 'Se Déconnecter'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Authentification Supabase\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: message.type,\n      sx: {\n        mb: 3\n      },\n      onClose: () => setMessage(null),\n      children: message.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: tabValue,\n          onChange: (_, newValue) => setTabValue(newValue),\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Connexion\",\n            icon: /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Inscription\",\n            icon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 0,\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleLogin,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: loginForm.email,\n            onChange: e => setLoginForm({\n              ...loginForm,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Mot de passe\",\n            type: \"password\",\n            value: loginForm.password,\n            onChange: e => setLoginForm({\n              ...loginForm,\n              password: e.target.value\n            }),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            sx: {\n              mt: 3\n            },\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 69\n            }, this),\n            children: \"Se Connecter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 1,\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSignup,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: signupForm.email,\n            onChange: e => setSignupForm({\n              ...signupForm,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Mot de passe\",\n            type: \"password\",\n            value: signupForm.password,\n            onChange: e => setSignupForm({\n              ...signupForm,\n              password: e.target.value\n            }),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Confirmer le mot de passe\",\n            type: \"password\",\n            value: signupForm.confirmPassword,\n            onChange: e => setSignupForm({\n              ...signupForm,\n              confirmPassword: e.target.value\n            }),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            sx: {\n              mt: 3\n            },\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 69\n            }, this),\n            children: \"S'Inscrire\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n};\n_s(SupabaseAuth, \"Quyw9FLX2YAXMyYjBntDqO15FaM=\");\n_c2 = SupabaseAuth;\nexport default SupabaseAuth;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"SupabaseAuth\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "Tabs", "Tab", "<PERSON><PERSON>", "LoginIcon", "PersonAdd", "PersonAddIcon", "Logout", "LogoutIcon", "supabase", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "SupabaseAuth", "_s", "user", "setUser", "loading", "setLoading", "message", "setMessage", "tabValue", "setTabValue", "loginForm", "setLoginForm", "email", "password", "signupForm", "setSignupForm", "confirmPassword", "auth", "getSession", "then", "data", "session", "_session$user", "subscription", "onAuthStateChange", "_event", "_session$user2", "unsubscribe", "handleLogin", "e", "preventDefault", "error", "signInWithPassword", "type", "text", "console", "Error", "handleSignup", "signUp", "handleLogout", "signOut", "variant", "gutterBottom", "severity", "mb", "onClose", "email_confirmed_at", "last_sign_in_at", "Date", "toLocaleString", "color", "startIcon", "onClick", "disabled", "size", "borderBottom", "borderColor", "onChange", "_", "newValue", "label", "icon", "onSubmit", "fullWidth", "target", "margin", "required", "mt", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/Supabase/SupabaseAuth.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Alert,\n  CircularProgress,\n  Tabs,\n  Tab,\n  Divider\n} from '@mui/material';\nimport {\n  Login as LoginIcon,\n  PersonAdd as PersonAddIcon,\n  Logout as LogoutIcon\n} from '@mui/icons-material';\nimport { supabase } from '../../config/supabase';\nimport type { User } from '@supabase/supabase-js';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`auth-tabpanel-${index}`}\n      aria-labelledby={`auth-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst SupabaseAuth: React.FC = () => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n  const [tabValue, setTabValue] = useState(0);\n  \n  // Formulaires\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n  const [signupForm, setSignupForm] = useState({ email: '', password: '', confirmPassword: '' });\n\n  useEffect(() => {\n    // Vérifier la session actuelle\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null);\n    });\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user ?? null);\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage(null);\n\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email: loginForm.email,\n        password: loginForm.password,\n      });\n\n      if (error) throw error;\n\n      setMessage({ type: 'success', text: 'Connexion réussie !' });\n      setLoginForm({ email: '', password: '' });\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      setMessage({ \n        type: 'error', \n        text: error instanceof Error ? error.message : 'Erreur de connexion' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSignup = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (signupForm.password !== signupForm.confirmPassword) {\n      setMessage({ type: 'error', text: 'Les mots de passe ne correspondent pas' });\n      return;\n    }\n\n    setLoading(true);\n    setMessage(null);\n\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email: signupForm.email,\n        password: signupForm.password,\n      });\n\n      if (error) throw error;\n\n      setMessage({ \n        type: 'success', \n        text: 'Compte créé ! Vérifiez votre email pour confirmer votre compte.' \n      });\n      setSignupForm({ email: '', password: '', confirmPassword: '' });\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setMessage({ \n        type: 'error', \n        text: error instanceof Error ? error.message : 'Erreur d\\'inscription' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    setLoading(true);\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) throw error;\n      setMessage({ type: 'success', text: 'Déconnexion réussie !' });\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n      setMessage({ \n        type: 'error', \n        text: error instanceof Error ? error.message : 'Erreur de déconnexion' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (user) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h5\" gutterBottom>\n              Utilisateur Connecté\n            </Typography>\n            \n            {message && (\n              <Alert severity={message.type} sx={{ mb: 2 }} onClose={() => setMessage(null)}>\n                {message.text}\n              </Alert>\n            )}\n\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"body1\">\n                <strong>Email:</strong> {user.email}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>ID:</strong> {user.id}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Confirmé:</strong> {user.email_confirmed_at ? 'Oui' : 'Non'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Dernière connexion:</strong> {user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'N/A'}\n              </Typography>\n            </Box>\n\n            <Button\n              variant=\"contained\"\n              color=\"error\"\n              startIcon={<LogoutIcon />}\n              onClick={handleLogout}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={20} /> : 'Se Déconnecter'}\n            </Button>\n          </CardContent>\n        </Card>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Authentification Supabase\n      </Typography>\n\n      {message && (\n        <Alert severity={message.type} sx={{ mb: 3 }} onClose={() => setMessage(null)}>\n          {message.text}\n        </Alert>\n      )}\n\n      <Card>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>\n            <Tab label=\"Connexion\" icon={<LoginIcon />} />\n            <Tab label=\"Inscription\" icon={<PersonAddIcon />} />\n          </Tabs>\n        </Box>\n\n        <TabPanel value={tabValue} index={0}>\n          <form onSubmit={handleLogin}>\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={loginForm.email}\n              onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}\n              margin=\"normal\"\n              required\n            />\n            <TextField\n              fullWidth\n              label=\"Mot de passe\"\n              type=\"password\"\n              value={loginForm.password}\n              onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}\n              margin=\"normal\"\n              required\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3 }}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}\n            >\n              Se Connecter\n            </Button>\n          </form>\n        </TabPanel>\n\n        <TabPanel value={tabValue} index={1}>\n          <form onSubmit={handleSignup}>\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={signupForm.email}\n              onChange={(e) => setSignupForm({ ...signupForm, email: e.target.value })}\n              margin=\"normal\"\n              required\n            />\n            <TextField\n              fullWidth\n              label=\"Mot de passe\"\n              type=\"password\"\n              value={signupForm.password}\n              onChange={(e) => setSignupForm({ ...signupForm, password: e.target.value })}\n              margin=\"normal\"\n              required\n            />\n            <TextField\n              fullWidth\n              label=\"Confirmer le mot de passe\"\n              type=\"password\"\n              value={signupForm.confirmPassword}\n              onChange={(e) => setSignupForm({ ...signupForm, confirmPassword: e.target.value })}\n              margin=\"normal\"\n              required\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3 }}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <PersonAddIcon />}\n            >\n              S'Inscrire\n            </Button>\n          </form>\n        </TabPanel>\n      </Card>\n    </Box>\n  );\n};\n\nexport default SupabaseAuth;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,GAAG,QAEE,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASjD,SAASC,QAAQA,CAACC,KAAoB,EAAE;EACtC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAClD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,iBAAiBJ,KAAK,EAAG;IAC7B,mBAAiB,YAAYA,KAAK,EAAG;IAAA,GACjCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAAClB,GAAG;MAAC4B,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAbQf,QAAQ;AAejB,MAAMgB,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAqD,IAAI,CAAC;EAChG,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC;IAAEiD,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC;IAAEiD,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEG,eAAe,EAAE;EAAG,CAAC,CAAC;EAE9FpD,SAAS,CAAC,MAAM;IACd;IACAiB,QAAQ,CAACoC,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE;QAAEC;MAAQ;IAAE,CAAC,KAAK;MAAA,IAAAC,aAAA;MACzDnB,OAAO,EAAAmB,aAAA,GAACD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnB,IAAI,cAAAoB,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;IAChC,CAAC,CAAC;;IAEF;IACA,MAAM;MAAEF,IAAI,EAAE;QAAEG;MAAa;IAAE,CAAC,GAAG1C,QAAQ,CAACoC,IAAI,CAACO,iBAAiB,CAAC,CAACC,MAAM,EAAEJ,OAAO,KAAK;MAAA,IAAAK,cAAA;MACtFvB,OAAO,EAAAuB,cAAA,GAACL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnB,IAAI,cAAAwB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,MAAMH,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,WAAW,GAAG,MAAOC,CAAkB,IAAK;IAChDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBzB,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM;QAAEa,IAAI;QAAEW;MAAM,CAAC,GAAG,MAAMlD,QAAQ,CAACoC,IAAI,CAACe,kBAAkB,CAAC;QAC7DpB,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtBC,QAAQ,EAAEH,SAAS,CAACG;MACtB,CAAC,CAAC;MAEF,IAAIkB,KAAK,EAAE,MAAMA,KAAK;MAEtBxB,UAAU,CAAC;QAAE0B,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAsB,CAAC,CAAC;MAC5DvB,YAAY,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CxB,UAAU,CAAC;QACT0B,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACzB,OAAO,GAAG;MACjD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,YAAY,GAAG,MAAOR,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAIhB,UAAU,CAACD,QAAQ,KAAKC,UAAU,CAACE,eAAe,EAAE;MACtDT,UAAU,CAAC;QAAE0B,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAyC,CAAC,CAAC;MAC7E;IACF;IAEA7B,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM;QAAEa,IAAI;QAAEW;MAAM,CAAC,GAAG,MAAMlD,QAAQ,CAACoC,IAAI,CAACqB,MAAM,CAAC;QACjD1B,KAAK,EAAEE,UAAU,CAACF,KAAK;QACvBC,QAAQ,EAAEC,UAAU,CAACD;MACvB,CAAC,CAAC;MAEF,IAAIkB,KAAK,EAAE,MAAMA,KAAK;MAEtBxB,UAAU,CAAC;QACT0B,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC,CAAC;MACFnB,aAAa,CAAC;QAAEH,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAC,CAAC;IACjE,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxB,UAAU,CAAC;QACT0B,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACzB,OAAO,GAAG;MACjD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BlC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM;QAAE0B;MAAM,CAAC,GAAG,MAAMlD,QAAQ,CAACoC,IAAI,CAACuB,OAAO,CAAC,CAAC;MAC/C,IAAIT,KAAK,EAAE,MAAMA,KAAK;MACtBxB,UAAU,CAAC;QAAE0B,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAwB,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxB,UAAU,CAAC;QACT0B,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACzB,OAAO,GAAG;MACjD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIH,IAAI,EAAE;IACR,oBACEnB,OAAA,CAAClB,GAAG;MAAC4B,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,eAChBH,OAAA,CAACjB,IAAI;QAAAoB,QAAA,eACHH,OAAA,CAAChB,WAAW;UAAAmB,QAAA,gBACVH,OAAA,CAACb,UAAU;YAACuE,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAxD,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZQ,OAAO,iBACNvB,OAAA,CAACZ,KAAK;YAACwE,QAAQ,EAAErC,OAAO,CAAC2B,IAAK;YAACxC,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YAACC,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAAC,IAAI,CAAE;YAAArB,QAAA,EAC3EoB,OAAO,CAAC4B;UAAI;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,eAEDf,OAAA,CAAClB,GAAG;YAAC4B,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YAAA1D,QAAA,gBACjBH,OAAA,CAACb,UAAU;cAACuE,OAAO,EAAC,OAAO;cAAAvD,QAAA,gBACzBH,OAAA;gBAAAG,QAAA,EAAQ;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAACU,KAAK;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACbf,OAAA,CAACb,UAAU;cAACuE,OAAO,EAAC,OAAO;cAAAvD,QAAA,gBACzBH,OAAA;gBAAAG,QAAA,EAAQ;cAAG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAACV,EAAE;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACbf,OAAA,CAACb,UAAU;cAACuE,OAAO,EAAC,OAAO;cAAAvD,QAAA,gBACzBH,OAAA;gBAAAG,QAAA,EAAQ;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAAC4C,kBAAkB,GAAG,KAAK,GAAG,KAAK;YAAA;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACbf,OAAA,CAACb,UAAU;cAACuE,OAAO,EAAC,OAAO;cAAAvD,QAAA,gBACzBH,OAAA;gBAAAG,QAAA,EAAQ;cAAmB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAAC6C,eAAe,GAAG,IAAIC,IAAI,CAAC9C,IAAI,CAAC6C,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENf,OAAA,CAACd,MAAM;YACLwE,OAAO,EAAC,WAAW;YACnBS,KAAK,EAAC,OAAO;YACbC,SAAS,eAAEpE,OAAA,CAACH,UAAU;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BsD,OAAO,EAAEb,YAAa;YACtBc,QAAQ,EAAEjD,OAAQ;YAAAlB,QAAA,EAEjBkB,OAAO,gBAAGrB,OAAA,CAACX,gBAAgB;cAACkF,IAAI,EAAE;YAAG;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAgB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEf,OAAA,CAAClB,GAAG;IAAC4B,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAR,QAAA,gBAChBH,OAAA,CAACb,UAAU;MAACuE,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAxD,QAAA,EAAC;IAEtC;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZQ,OAAO,iBACNvB,OAAA,CAACZ,KAAK;MAACwE,QAAQ,EAAErC,OAAO,CAAC2B,IAAK;MAACxC,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAACC,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAAC,IAAI,CAAE;MAAArB,QAAA,EAC3EoB,OAAO,CAAC4B;IAAI;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,eAEDf,OAAA,CAACjB,IAAI;MAAAoB,QAAA,gBACHH,OAAA,CAAClB,GAAG;QAAC4B,EAAE,EAAE;UAAE8D,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAtE,QAAA,eACnDH,OAAA,CAACV,IAAI;UAACc,KAAK,EAAEqB,QAAS;UAACiD,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAKlD,WAAW,CAACkD,QAAQ,CAAE;UAAAzE,QAAA,gBACtEH,OAAA,CAACT,GAAG;YAACsF,KAAK,EAAC,WAAW;YAACC,IAAI,eAAE9E,OAAA,CAACP,SAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9Cf,OAAA,CAACT,GAAG;YAACsF,KAAK,EAAC,aAAa;YAACC,IAAI,eAAE9E,OAAA,CAACL,aAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEqB,QAAS;QAACpB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA;UAAM+E,QAAQ,EAAElC,WAAY;UAAA1C,QAAA,gBAC1BH,OAAA,CAACf,SAAS;YACR+F,SAAS;YACTH,KAAK,EAAC,OAAO;YACb3B,IAAI,EAAC,OAAO;YACZ9C,KAAK,EAAEuB,SAAS,CAACE,KAAM;YACvB6C,QAAQ,EAAG5B,CAAC,IAAKlB,YAAY,CAAC;cAAE,GAAGD,SAAS;cAAEE,KAAK,EAAEiB,CAAC,CAACmC,MAAM,CAAC7E;YAAM,CAAC,CAAE;YACvE8E,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFf,OAAA,CAACf,SAAS;YACR+F,SAAS;YACTH,KAAK,EAAC,cAAc;YACpB3B,IAAI,EAAC,UAAU;YACf9C,KAAK,EAAEuB,SAAS,CAACG,QAAS;YAC1B4C,QAAQ,EAAG5B,CAAC,IAAKlB,YAAY,CAAC;cAAE,GAAGD,SAAS;cAAEG,QAAQ,EAAEgB,CAAC,CAACmC,MAAM,CAAC7E;YAAM,CAAC,CAAE;YAC1E8E,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFf,OAAA,CAACd,MAAM;YACLgE,IAAI,EAAC,QAAQ;YACb8B,SAAS;YACTtB,OAAO,EAAC,WAAW;YACnBhD,EAAE,EAAE;cAAE0E,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEjD,OAAQ;YAClB+C,SAAS,EAAE/C,OAAO,gBAAGrB,OAAA,CAACX,gBAAgB;cAACkF,IAAI,EAAE;YAAG;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGf,OAAA,CAACP,SAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAZ,QAAA,EACrE;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEqB,QAAS;QAACpB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA;UAAM+E,QAAQ,EAAEzB,YAAa;UAAAnD,QAAA,gBAC3BH,OAAA,CAACf,SAAS;YACR+F,SAAS;YACTH,KAAK,EAAC,OAAO;YACb3B,IAAI,EAAC,OAAO;YACZ9C,KAAK,EAAE2B,UAAU,CAACF,KAAM;YACxB6C,QAAQ,EAAG5B,CAAC,IAAKd,aAAa,CAAC;cAAE,GAAGD,UAAU;cAAEF,KAAK,EAAEiB,CAAC,CAACmC,MAAM,CAAC7E;YAAM,CAAC,CAAE;YACzE8E,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFf,OAAA,CAACf,SAAS;YACR+F,SAAS;YACTH,KAAK,EAAC,cAAc;YACpB3B,IAAI,EAAC,UAAU;YACf9C,KAAK,EAAE2B,UAAU,CAACD,QAAS;YAC3B4C,QAAQ,EAAG5B,CAAC,IAAKd,aAAa,CAAC;cAAE,GAAGD,UAAU;cAAED,QAAQ,EAAEgB,CAAC,CAACmC,MAAM,CAAC7E;YAAM,CAAC,CAAE;YAC5E8E,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFf,OAAA,CAACf,SAAS;YACR+F,SAAS;YACTH,KAAK,EAAC,2BAA2B;YACjC3B,IAAI,EAAC,UAAU;YACf9C,KAAK,EAAE2B,UAAU,CAACE,eAAgB;YAClCyC,QAAQ,EAAG5B,CAAC,IAAKd,aAAa,CAAC;cAAE,GAAGD,UAAU;cAAEE,eAAe,EAAEa,CAAC,CAACmC,MAAM,CAAC7E;YAAM,CAAC,CAAE;YACnF8E,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFf,OAAA,CAACd,MAAM;YACLgE,IAAI,EAAC,QAAQ;YACb8B,SAAS;YACTtB,OAAO,EAAC,WAAW;YACnBhD,EAAE,EAAE;cAAE0E,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEjD,OAAQ;YAClB+C,SAAS,EAAE/C,OAAO,gBAAGrB,OAAA,CAACX,gBAAgB;cAACkF,IAAI,EAAE;YAAG;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGf,OAAA,CAACL,aAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAZ,QAAA,EACzE;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACG,EAAA,CApPID,YAAsB;AAAAoE,GAAA,GAAtBpE,YAAsB;AAsP5B,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAqE,GAAA;AAAAC,YAAA,CAAAtE,EAAA;AAAAsE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}