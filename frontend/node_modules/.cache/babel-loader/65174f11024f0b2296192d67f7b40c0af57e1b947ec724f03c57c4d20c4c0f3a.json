{"ast": null, "code": "import { extractWeightsFactory } from '../common';\nimport { extractorsFactory } from './extractorsFactory';\nexport function extractParamsTiny(weights) {\n  var paramMappings = [];\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var extractDenseBlock3Params = extractorsFactory(extractWeights, paramMappings).extractDenseBlock3Params;\n  var dense0 = extractDenseBlock3Params(3, 32, 'dense0', true);\n  var dense1 = extractDenseBlock3Params(32, 64, 'dense1');\n  var dense2 = extractDenseBlock3Params(64, 128, 'dense2');\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    paramMappings: paramMappings,\n    params: {\n      dense0: dense0,\n      dense1: dense1,\n      dense2: dense2\n    }\n  };\n}", "map": {"version": 3, "names": ["extractWeightsFactory", "extractorsFactory", "extractParamsTiny", "weights", "paramMappings", "_a", "extractWeights", "getRemainingWeights", "extractDenseBlock3Params", "dense0", "dense1", "dense2", "length", "Error", "params"], "sources": ["../../../src/faceFeatureExtractor/extractParamsTiny.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,qBAAqB,QAAsB,WAAW;AAC/D,SAASC,iBAAiB,QAAQ,qBAAqB;AAKvD,OAAM,SAAUC,iBAAiBA,CAACC,OAAqB;EAErD,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAAC,EAAA,GAAAL,qBAAA,CAAAG,OAAA,CAG4B;IAFhCG,cAAA,GAAAD,EAAA,CAAAC,cAAc;IACdC,mBAAA,GAAAF,EAAA,CAAAE,mBACgC;EAGhC,IAAAC,wBAAA,GAAAP,iBAAA,CAAAK,cAAA,EAAAF,aAAA,EAAAI,wBAAwB;EAG1B,IAAMC,MAAM,GAAGD,wBAAwB,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC9D,IAAME,MAAM,GAAGF,wBAAwB,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC;EACzD,IAAMG,MAAM,GAAGH,wBAAwB,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC;EAE1D,IAAID,mBAAmB,EAAE,CAACK,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCN,mBAAmB,EAAE,CAACK,MAAQ,CAAC;;EAGnF,OAAO;IACLR,aAAa,EAAAA,aAAA;IACbU,MAAM,EAAE;MAAEL,MAAM,EAAAA,MAAA;MAAEC,MAAM,EAAAA,MAAA;MAAEC,MAAM,EAAAA;IAAA;GACjC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}