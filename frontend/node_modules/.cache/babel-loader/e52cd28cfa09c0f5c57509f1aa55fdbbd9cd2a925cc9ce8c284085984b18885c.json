{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nclass PostgrestQueryBuilder {\n  constructor(url, {\n    headers = {},\n    schema,\n    fetch\n  }) {\n    this.url = url;\n    this.headers = headers;\n    this.schema = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select(columns, {\n    head = false,\n    count\n  } = {}) {\n    const method = head ? 'HEAD' : 'GET';\n    // Remove whitespaces except when quoted\n    let quoted = false;\n    const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*').split('').map(c => {\n      if (/\\s/.test(c) && !quoted) {\n        return '';\n      }\n      if (c === '\"') {\n        quoted = !quoted;\n      }\n      return c;\n    }).join('');\n    this.url.searchParams.set('select', cleanedColumns);\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert(values, {\n    count,\n    defaultToNull = true\n  } = {}) {\n    const method = 'POST';\n    const prefersHeaders = [];\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default');\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map(column => `\"${column}\"`);\n        this.url.searchParams.set('columns', uniqueColumns.join(','));\n      }\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert(values, {\n    onConflict,\n    ignoreDuplicates = false,\n    count,\n    defaultToNull = true\n  } = {}) {\n    const method = 'POST';\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict);\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default');\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map(column => `\"${column}\"`);\n        this.url.searchParams.set('columns', uniqueColumns.join(','));\n      }\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update(values, {\n    count\n  } = {}) {\n    const method = 'PATCH';\n    const prefersHeaders = [];\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete({\n    count\n  } = {}) {\n    const method = 'DELETE';\n    const prefersHeaders = [];\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer']);\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestQueryBuilder;", "map": {"version": 3, "names": ["PostgrestFilterBuilder_1", "__importDefault", "require", "PostgrestQueryBuilder", "constructor", "url", "headers", "schema", "fetch", "select", "columns", "head", "count", "method", "quoted", "cleanedColumns", "split", "map", "c", "test", "join", "searchParams", "set", "default", "allowEmpty", "insert", "values", "defaultToNull", "prefersHeaders", "push", "Array", "isArray", "reduce", "acc", "x", "concat", "Object", "keys", "length", "uniqueColumns", "Set", "column", "body", "upsert", "onConflict", "ignoreDuplicates", "undefined", "update", "delete", "unshift", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@supabase/postgrest-js/src/PostgrestQueryBuilder.ts"], "sourcesContent": ["import PostgrestBuilder from './PostgrestBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport { GetResult } from './select-query-parser/result'\nimport { Fetch, GenericSchema, GenericTable, GenericView } from './types'\n\nexport default class PostgrestQueryBuilder<\n  Schema extends GenericSchema,\n  Relation extends GenericTable | GenericView,\n  RelationName = unknown,\n  Relationships = Relation extends { Relationships: infer R } ? R : unknown\n> {\n  url: URL\n  headers: Record<string, string>\n  schema?: string\n  signal?: AbortSignal\n  fetch?: Fetch\n\n  constructor(\n    url: URL,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: string\n      fetch?: Fetch\n    }\n  ) {\n    this.url = url\n    this.headers = headers\n    this.schema = schema\n    this.fetch = fetch\n  }\n\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select<\n    Query extends string = '*',\n    ResultOne = GetResult<Schema, Relation['Row'], RelationName, Relationships, Query>\n  >(\n    columns?: Query,\n    {\n      head = false,\n      count,\n    }: {\n      head?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], ResultOne[], RelationName, Relationships> {\n    const method = head ? 'HEAD' : 'GET'\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<ResultOne[]>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk inserts.\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      count,\n      defaultToNull = true,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk upserts.\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      onConflict,\n      ignoreDuplicates = false,\n      count,\n      defaultToNull = true,\n    }: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`]\n\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict)\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update<Row extends Relation extends { Update: unknown } ? Relation['Update'] : never>(\n    values: Row,\n    {\n      count,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'PATCH'\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete({\n    count,\n  }: {\n    count?: 'exact' | 'planned' | 'estimated'\n  } = {}): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'DELETE'\n    const prefersHeaders = []\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer'])\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n}\n"], "mappings": ";;;;;;;;;;AACA,MAAAA,wBAAA,GAAAC,eAAA,CAAAC,OAAA;AAIA,MAAqBC,qBAAqB;EAYxCC,YACEC,GAAQ,EACR;IACEC,OAAO,GAAG,EAAE;IACZC,MAAM;IACNC;EAAK,CAKN;IAED,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EAEA;;;;;;;;;;;;;;;;;;;;;EAqBAC,MAAMA,CAIJC,OAAe,EACf;IACEC,IAAI,GAAG,KAAK;IACZC;EAAK,IAIH,EAAE;IAEN,MAAMC,MAAM,GAAGF,IAAI,GAAG,MAAM,GAAG,KAAK;IACpC;IACA,IAAIG,MAAM,GAAG,KAAK;IAClB,MAAMC,cAAc,GAAG,CAACL,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,GAAG,EACnCM,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAEC,CAAC,IAAI;MACT,IAAI,IAAI,CAACC,IAAI,CAACD,CAAC,CAAC,IAAI,CAACJ,MAAM,EAAE;QAC3B,OAAO,EAAE;;MAEX,IAAII,CAAC,KAAK,GAAG,EAAE;QACbJ,MAAM,GAAG,CAACA,MAAM;;MAElB,OAAOI,CAAC;IACV,CAAC,CAAC,CACDE,IAAI,CAAC,EAAE,CAAC;IACX,IAAI,CAACf,GAAG,CAACgB,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAEP,cAAc,CAAC;IACnD,IAAIH,KAAK,EAAE;MACT,IAAI,CAACN,OAAO,CAAC,QAAQ,CAAC,GAAG,SAASM,KAAK,EAAE;;IAG3C,OAAO,IAAIZ,wBAAA,CAAAuB,OAAsB,CAAC;MAChCV,MAAM;MACNR,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBgB,UAAU,EAAE;KAC+B,CAAC;EAChD;EAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BAC,MAAMA,CACJC,MAAmB,EACnB;IACEd,KAAK;IACLe,aAAa,GAAG;EAAI,IAIlB,EAAE;IAEN,MAAMd,MAAM,GAAG,MAAM;IAErB,MAAMe,cAAc,GAAG,EAAE;IACzB,IAAI,IAAI,CAACtB,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1BsB,cAAc,CAACC,IAAI,CAAC,IAAI,CAACvB,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAE7C,IAAIM,KAAK,EAAE;MACTgB,cAAc,CAACC,IAAI,CAAC,SAASjB,KAAK,EAAE,CAAC;;IAEvC,IAAI,CAACe,aAAa,EAAE;MAClBC,cAAc,CAACC,IAAI,CAAC,iBAAiB,CAAC;;IAExC,IAAI,CAACvB,OAAO,CAAC,QAAQ,CAAC,GAAGsB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,IAAIU,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MACzB,MAAMhB,OAAO,GAAGgB,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,CAACE,MAAM,CAACC,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC;MACrF,IAAIxB,OAAO,CAAC4B,MAAM,GAAG,CAAC,EAAE;QACtB,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC9B,OAAO,CAAC,CAAC,CAACO,GAAG,CAAEwB,MAAM,IAAK,IAAIA,MAAM,GAAG,CAAC;QAC1E,IAAI,CAACpC,GAAG,CAACgB,YAAY,CAACC,GAAG,CAAC,SAAS,EAAEiB,aAAa,CAACnB,IAAI,CAAC,GAAG,CAAC,CAAC;;;IAIjE,OAAO,IAAIpB,wBAAA,CAAAuB,OAAsB,CAAC;MAChCV,MAAM;MACNR,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBmC,IAAI,EAAEhB,MAAM;MACZlB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBgB,UAAU,EAAE;KACwB,CAAC;EACzC;EAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCAmB,MAAMA,CACJjB,MAAmB,EACnB;IACEkB,UAAU;IACVC,gBAAgB,GAAG,KAAK;IACxBjC,KAAK;IACLe,aAAa,GAAG;EAAI,IAMlB,EAAE;IAEN,MAAMd,MAAM,GAAG,MAAM;IAErB,MAAMe,cAAc,GAAG,CAAC,cAAciB,gBAAgB,GAAG,QAAQ,GAAG,OAAO,aAAa,CAAC;IAEzF,IAAID,UAAU,KAAKE,SAAS,EAAE,IAAI,CAACzC,GAAG,CAACgB,YAAY,CAACC,GAAG,CAAC,aAAa,EAAEsB,UAAU,CAAC;IAClF,IAAI,IAAI,CAACtC,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1BsB,cAAc,CAACC,IAAI,CAAC,IAAI,CAACvB,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAE7C,IAAIM,KAAK,EAAE;MACTgB,cAAc,CAACC,IAAI,CAAC,SAASjB,KAAK,EAAE,CAAC;;IAEvC,IAAI,CAACe,aAAa,EAAE;MAClBC,cAAc,CAACC,IAAI,CAAC,iBAAiB,CAAC;;IAExC,IAAI,CAACvB,OAAO,CAAC,QAAQ,CAAC,GAAGsB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,IAAIU,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MACzB,MAAMhB,OAAO,GAAGgB,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,CAACE,MAAM,CAACC,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC;MACrF,IAAIxB,OAAO,CAAC4B,MAAM,GAAG,CAAC,EAAE;QACtB,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC9B,OAAO,CAAC,CAAC,CAACO,GAAG,CAAEwB,MAAM,IAAK,IAAIA,MAAM,GAAG,CAAC;QAC1E,IAAI,CAACpC,GAAG,CAACgB,YAAY,CAACC,GAAG,CAAC,SAAS,EAAEiB,aAAa,CAACnB,IAAI,CAAC,GAAG,CAAC,CAAC;;;IAIjE,OAAO,IAAIpB,wBAAA,CAAAuB,OAAsB,CAAC;MAChCV,MAAM;MACNR,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBmC,IAAI,EAAEhB,MAAM;MACZlB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBgB,UAAU,EAAE;KACwB,CAAC;EACzC;EAEA;;;;;;;;;;;;;;;;;;;;;EAqBAuB,MAAMA,CACJrB,MAAW,EACX;IACEd;EAAK,IAGH,EAAE;IAEN,MAAMC,MAAM,GAAG,OAAO;IACtB,MAAMe,cAAc,GAAG,EAAE;IACzB,IAAI,IAAI,CAACtB,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1BsB,cAAc,CAACC,IAAI,CAAC,IAAI,CAACvB,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAE7C,IAAIM,KAAK,EAAE;MACTgB,cAAc,CAACC,IAAI,CAAC,SAASjB,KAAK,EAAE,CAAC;;IAEvC,IAAI,CAACN,OAAO,CAAC,QAAQ,CAAC,GAAGsB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,OAAO,IAAIpB,wBAAA,CAAAuB,OAAsB,CAAC;MAChCV,MAAM;MACNR,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBmC,IAAI,EAAEhB,MAAM;MACZlB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBgB,UAAU,EAAE;KACwB,CAAC;EACzC;EAEA;;;;;;;;;;;;;;;;;;;EAmBAwB,MAAMA,CAAC;IACLpC;EAAK,IAGH,EAAE;IACJ,MAAMC,MAAM,GAAG,QAAQ;IACvB,MAAMe,cAAc,GAAG,EAAE;IACzB,IAAIhB,KAAK,EAAE;MACTgB,cAAc,CAACC,IAAI,CAAC,SAASjB,KAAK,EAAE,CAAC;;IAEvC,IAAI,IAAI,CAACN,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1BsB,cAAc,CAACqB,OAAO,CAAC,IAAI,CAAC3C,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAEhD,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAGsB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,OAAO,IAAIpB,wBAAA,CAAAuB,OAAsB,CAAC;MAChCV,MAAM;MACNR,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBgB,UAAU,EAAE;KACwB,CAAC;EACzC;;AAtXF0B,OAAA,CAAA3B,OAAA,GAAApB,qBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}