{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Card, CardContent, Stack, Chip, Button, Alert, Tabs, Tab, IconButton, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, LinearProgress } from '@mui/material';\nimport { Dashboard as DashboardIcon, People as PeopleIcon, School as SchoolIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon, PersonAdd as PersonAddIcon, Login as LoginIcon, MoreVert as MoreVertIcon, Face as FaceIcon, PhotoCamera as PhotoCameraIcon, Videocam as VideocamIcon } from '@mui/icons-material';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';\nimport { useAuth } from '../contexts/AuthContext';\nimport { supabaseService } from '../services/supabaseService';\nimport { UserRole } from '../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `admin-tabpanel-${index}`,\n    \"aria-labelledby\": `admin-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user: currentUser,\n    signOut\n  } = useAuth();\n  const [tabValue, setTabValue] = useState(0);\n  const [stats, setStats] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [attendanceRecords, setAttendanceRecords] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // États pour la gestion multi-utilisateurs\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [userMenuAnchor, setUserMenuAnchor] = useState(null);\n  const [loginAsDialogOpen, setLoginAsDialogOpen] = useState(false);\n  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);\n  const [liveRecognitionOpen, setLiveRecognitionOpen] = useState(false);\n  const [studentPhotoManagementOpen, setStudentPhotoManagementOpen] = useState(false);\n\n  // Données pour les graphiques\n  const [attendanceChartData, setAttendanceChartData] = useState([]);\n  const [courseStatsData, setCourseStatsData] = useState([]);\n\n  /**\n   * Charge toutes les données du dashboard\n   */\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les données en parallèle\n      const [usersData, coursesData, attendanceData] = await Promise.all([supabaseService.getUsers(), supabaseService.getCourses(), supabaseService.getAttendanceRecords()]);\n      setUsers(usersData);\n      setCourses(coursesData);\n      setAttendanceRecords(attendanceData);\n\n      // Calculer les statistiques\n      const dashboardStats = {\n        totalStudents: usersData.filter(u => u.role === UserRole.STUDENT).length,\n        totalTeachers: usersData.filter(u => u.role === UserRole.TEACHER).length,\n        totalCourses: coursesData.length,\n        todaySessions: 0,\n        // À calculer selon les sessions du jour\n        averageAttendanceRate: calculateAverageAttendanceRate(attendanceData),\n        recentActivity: [] // À implémenter\n      };\n      setStats(dashboardStats);\n\n      // Préparer les données pour les graphiques\n      prepareChartData(attendanceData, coursesData);\n    } catch (err) {\n      console.error('Erreur chargement dashboard:', err);\n      setError('Impossible de charger les données du dashboard');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Calcule le taux de présence moyen\n   */\n  const calculateAverageAttendanceRate = records => {\n    if (records.length === 0) return 0;\n    const presentRecords = records.filter(r => r.status === 'present' || r.status === 'late');\n    return Math.round(presentRecords.length / records.length * 100);\n  };\n\n  /**\n   * Prépare les données pour les graphiques\n   */\n  const prepareChartData = (attendance, courses) => {\n    // Données de présence par jour (7 derniers jours)\n    const last7Days = Array.from({\n      length: 7\n    }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      return date.toISOString().split('T')[0];\n    }).reverse();\n    const attendanceByDay = last7Days.map(date => {\n      const dayRecords = attendance.filter(r => r.date === date);\n      const present = dayRecords.filter(r => r.status === 'present').length;\n      const late = dayRecords.filter(r => r.status === 'late').length;\n      const absent = dayRecords.filter(r => r.status === 'absent').length;\n      return {\n        date: new Date(date).toLocaleDateString('fr-FR', {\n          weekday: 'short',\n          day: 'numeric'\n        }),\n        present,\n        late,\n        absent,\n        total: dayRecords.length,\n        rate: dayRecords.length > 0 ? Math.round((present + late) / dayRecords.length * 100) : 0\n      };\n    });\n    setAttendanceChartData(attendanceByDay);\n\n    // Statistiques par cours\n    const courseStats = courses.map(course => {\n      const courseRecords = attendance.filter(r => r.course.id === course.id);\n      const present = courseRecords.filter(r => r.status === 'present').length;\n      const late = courseRecords.filter(r => r.status === 'late').length;\n      const absent = courseRecords.filter(r => r.status === 'absent').length;\n      return {\n        name: course.name.length > 15 ? course.name.substring(0, 15) + '...' : course.name,\n        present,\n        late,\n        absent,\n        total: courseRecords.length,\n        rate: courseRecords.length > 0 ? Math.round((present + late) / courseRecords.length * 100) : 0\n      };\n    });\n    setCourseStatsData(courseStats);\n  };\n\n  /**\n   * Se connecter en tant qu'autre utilisateur\n   */\n  const loginAsUser = async user => {\n    try {\n      // Ici, on implémenterait la logique de connexion en tant qu'autre utilisateur\n      // Pour l'instant, on simule juste\n      console.log(`Connexion en tant que ${user.firstName} ${user.lastName}`);\n      setLoginAsDialogOpen(false);\n      setSelectedUser(null);\n\n      // Rediriger vers le dashboard approprié selon le rôle\n      // window.location.href = user.role === 'teacher' ? '/teacher-dashboard' : '/student-dashboard';\n    } catch (err) {\n      console.error('Erreur connexion utilisateur:', err);\n      setError('Impossible de se connecter en tant que cet utilisateur');\n    }\n  };\n\n  /**\n   * Ouvre le menu utilisateur\n   */\n  const handleUserMenuClick = (event, user) => {\n    setUserMenuAnchor(event.currentTarget);\n    setSelectedUser(user);\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          gutterBottom: true,\n          children: \"Dashboard Administrateur\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: [\"Bienvenue, \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName, \" \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(PhotoCameraIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 24\n          }, this),\n          onClick: () => setStudentPhotoManagementOpen(true),\n          color: \"primary\",\n          children: \"G\\xE9rer Photos \\xC9tudiants\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(VideocamIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 24\n          }, this),\n          onClick: () => setLiveRecognitionOpen(true),\n          color: \"secondary\",\n          children: \"Reconnaissance Live\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"error\",\n          onClick: signOut,\n          children: \"D\\xE9connexion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 19\n    }, this), stats && /*#__PURE__*/_jsxDEV(Stack, {\n      direction: {\n        xs: 'column',\n        md: 'row'\n      },\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(PeopleIcon, {\n              color: \"primary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalStudents\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"\\xC9tudiants\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(SchoolIcon, {\n              color: \"secondary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalTeachers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Professeurs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(DashboardIcon, {\n              color: \"success\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalCourses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n              color: \"warning\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: [stats.averageAttendanceRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Taux Pr\\xE9sence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: (_, newValue) => setTabValue(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Vue d'ensemble\",\n          icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Utilisateurs\",\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Cours\",\n          icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Reconnaissance Faciale\",\n          icon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 4,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"\\xC9volution des Pr\\xE9sences (7 derniers jours)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(LineChart, {\n                data: attendanceChartData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Line, {\n                  type: \"monotone\",\n                  dataKey: \"rate\",\n                  stroke: \"#8884d8\",\n                  name: \"Taux %\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Pr\\xE9sences par Cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: courseStatsData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"present\",\n                  stackId: \"a\",\n                  fill: \"#4caf50\",\n                  name: \"Pr\\xE9sent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"late\",\n                  stackId: \"a\",\n                  fill: \"#ff9800\",\n                  name: \"Retard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"absent\",\n                  stackId: \"a\",\n                  fill: \"#f44336\",\n                  name: \"Absent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Gestion des Utilisateurs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 26\n            }, this),\n            onClick: () => {/* Ouvrir dialog création utilisateur */},\n            children: \"Nouvel Utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: users.map(user => /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 2,\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      children: [user.firstName, \" \", user.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      color: \"text.secondary\",\n                      children: [user.email, \" \\u2022 \", user.roleDisplay]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: user.role,\n                    color: user.role === 'admin' ? 'error' : user.role === 'teacher' ? 'primary' : 'secondary',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: e => handleUserMenuClick(e, user),\n                  children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)\n          }, user.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Gestion des Cours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: courses.map(course => /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: course.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              children: course.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              sx: {\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: `${course.credits} crédits`,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: course.semester,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Syst\\xE8me de Reconnaissance Faciale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Statistiques du syst\\xE8me de reconnaissance faciale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: () => setFaceRegistrationOpen(true),\n              children: \"G\\xE9rer les Encodages Faciaux\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: userMenuAnchor,\n      open: Boolean(userMenuAnchor),\n      onClose: () => setUserMenuAnchor(null),\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => setLoginAsDialogOpen(true),\n        children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this), \"Se connecter en tant que\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {/* Éditer utilisateur */},\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), \"Modifier\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: loginAsDialogOpen,\n      onClose: () => setLoginAsDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Se connecter en tant qu'utilisateur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Voulez-vous vous connecter en tant que\", ' ', /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [selectedUser.firstName, \" \", selectedUser.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this), \" ?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setLoginAsDialogOpen(false),\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => selectedUser && loginAsUser(selectedUser),\n          variant: \"contained\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"VD5opCbmouG9pYVihUQbbRkXe9o=\", false, function () {\n  return [useAuth];\n});\n_c2 = AdminDashboard;\nexport default AdminDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tabs", "Tab", "IconButton", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "LinearProgress", "Dashboard", "DashboardIcon", "People", "PeopleIcon", "School", "SchoolIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "PersonAdd", "PersonAddIcon", "<PERSON><PERSON>", "LoginIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Face", "FaceIcon", "PhotoCamera", "PhotoCameraIcon", "Videocam", "VideocamIcon", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "useAuth", "supabaseService", "UserRole", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminDashboard", "_s", "user", "currentUser", "signOut", "tabValue", "setTabValue", "stats", "setStats", "users", "setUsers", "courses", "setCourses", "attendanceRecords", "setAttendanceRecords", "loading", "setLoading", "error", "setError", "selected<PERSON>ser", "setSelectedUser", "userMenuAnchor", "setUserMenuAnchor", "loginAsDialogOpen", "setLoginAsDialogOpen", "faceRegistrationOpen", "setFaceRegistrationOpen", "liveRecognitionOpen", "setLiveRecognitionOpen", "studentPhotoManagementOpen", "setStudentPhotoManagementOpen", "attendanceChartData", "setAttendanceChartData", "courseStatsData", "setCourseStatsData", "loadDashboardData", "usersData", "coursesData", "attendanceData", "Promise", "all", "getUsers", "getCourses", "getAttendanceRecords", "dashboardStats", "totalStudents", "filter", "u", "STUDENT", "length", "totalTeachers", "TEACHER", "totalCourses", "todaySessions", "averageAttendanceRate", "calculateAverageAttendanceRate", "recentActivity", "prepareChartData", "err", "console", "records", "presentRecords", "r", "status", "Math", "round", "attendance", "last7Days", "Array", "from", "_", "i", "date", "Date", "setDate", "getDate", "toISOString", "split", "reverse", "attendanceByDay", "map", "dayRecords", "present", "late", "absent", "toLocaleDateString", "weekday", "day", "total", "rate", "courseStats", "course", "courseRecords", "name", "substring", "loginAsUser", "log", "firstName", "lastName", "handleUserMenuClick", "event", "currentTarget", "COLORS", "max<PERSON><PERSON><PERSON>", "py", "display", "justifyContent", "alignItems", "mb", "variant", "gutterBottom", "color", "direction", "spacing", "startIcon", "onClick", "severity", "onClose", "xs", "md", "flex", "fontSize", "borderBottom", "borderColor", "onChange", "newValue", "label", "icon", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "stackId", "fill", "email", "roleDisplay", "size", "e", "description", "mt", "credits", "semester", "anchorEl", "open", "Boolean", "mr", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Card,\n  CardContent,\n  Stack,\n  Chip,\n  <PERSON>ton,\n  Alert,\n  Tabs,\n  Tab,\n  IconButton,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Avatar,\n  LinearProgress\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  People as PeopleIcon,\n  School as SchoolIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon,\n  PersonAdd as PersonAddIcon,\n  Login as LoginIcon,\n  MoreVert as MoreVertIcon,\n  Face as FaceIcon,\n  PhotoCamera as PhotoCameraIcon,\n  Videocam as VideocamIcon,\n  Upload as UploadIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  Bar,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>\n} from 'recharts';\nimport { useAuth } from '../contexts/AuthContext';\nimport { supabaseService } from '../services/supabaseService';\nimport { faceRecognitionService } from '../services/faceRecognitionService';\nimport FaceRegistration from '../components/FaceRecognition/FaceRegistration';\nimport FaceDetectionCamera from '../components/FaceRecognition/FaceDetectionCamera';\nimport { User, Course, AttendanceRecord, DashboardStats, UserRole } from '../types';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`admin-tabpanel-${index}`}\n      aria-labelledby={`admin-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst AdminDashboard: React.FC = () => {\n  const { user: currentUser, signOut } = useAuth();\n  const [tabValue, setTabValue] = useState(0);\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // États pour la gestion multi-utilisateurs\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);\n  const [loginAsDialogOpen, setLoginAsDialogOpen] = useState(false);\n  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);\n  const [liveRecognitionOpen, setLiveRecognitionOpen] = useState(false);\n  const [studentPhotoManagementOpen, setStudentPhotoManagementOpen] = useState(false);\n\n  // Données pour les graphiques\n  const [attendanceChartData, setAttendanceChartData] = useState<any[]>([]);\n  const [courseStatsData, setCourseStatsData] = useState<any[]>([]);\n\n  /**\n   * Charge toutes les données du dashboard\n   */\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les données en parallèle\n      const [usersData, coursesData, attendanceData] = await Promise.all([\n        supabaseService.getUsers(),\n        supabaseService.getCourses(),\n        supabaseService.getAttendanceRecords()\n      ]);\n\n      setUsers(usersData);\n      setCourses(coursesData);\n      setAttendanceRecords(attendanceData);\n\n      // Calculer les statistiques\n      const dashboardStats: DashboardStats = {\n        totalStudents: usersData.filter(u => u.role === UserRole.STUDENT).length,\n        totalTeachers: usersData.filter(u => u.role === UserRole.TEACHER).length,\n        totalCourses: coursesData.length,\n        todaySessions: 0, // À calculer selon les sessions du jour\n        averageAttendanceRate: calculateAverageAttendanceRate(attendanceData),\n        recentActivity: [] // À implémenter\n      };\n\n      setStats(dashboardStats);\n\n      // Préparer les données pour les graphiques\n      prepareChartData(attendanceData, coursesData);\n\n    } catch (err) {\n      console.error('Erreur chargement dashboard:', err);\n      setError('Impossible de charger les données du dashboard');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Calcule le taux de présence moyen\n   */\n  const calculateAverageAttendanceRate = (records: AttendanceRecord[]): number => {\n    if (records.length === 0) return 0;\n    \n    const presentRecords = records.filter(r => r.status === 'present' || r.status === 'late');\n    return Math.round((presentRecords.length / records.length) * 100);\n  };\n\n  /**\n   * Prépare les données pour les graphiques\n   */\n  const prepareChartData = (attendance: AttendanceRecord[], courses: Course[]) => {\n    // Données de présence par jour (7 derniers jours)\n    const last7Days = Array.from({ length: 7 }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      return date.toISOString().split('T')[0];\n    }).reverse();\n\n    const attendanceByDay = last7Days.map(date => {\n      const dayRecords = attendance.filter(r => r.date === date);\n      const present = dayRecords.filter(r => r.status === 'present').length;\n      const late = dayRecords.filter(r => r.status === 'late').length;\n      const absent = dayRecords.filter(r => r.status === 'absent').length;\n      \n      return {\n        date: new Date(date).toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' }),\n        present,\n        late,\n        absent,\n        total: dayRecords.length,\n        rate: dayRecords.length > 0 ? Math.round(((present + late) / dayRecords.length) * 100) : 0\n      };\n    });\n\n    setAttendanceChartData(attendanceByDay);\n\n    // Statistiques par cours\n    const courseStats = courses.map(course => {\n      const courseRecords = attendance.filter(r => r.course.id === course.id);\n      const present = courseRecords.filter(r => r.status === 'present').length;\n      const late = courseRecords.filter(r => r.status === 'late').length;\n      const absent = courseRecords.filter(r => r.status === 'absent').length;\n      \n      return {\n        name: course.name.length > 15 ? course.name.substring(0, 15) + '...' : course.name,\n        present,\n        late,\n        absent,\n        total: courseRecords.length,\n        rate: courseRecords.length > 0 ? Math.round(((present + late) / courseRecords.length) * 100) : 0\n      };\n    });\n\n    setCourseStatsData(courseStats);\n  };\n\n  /**\n   * Se connecter en tant qu'autre utilisateur\n   */\n  const loginAsUser = async (user: User) => {\n    try {\n      // Ici, on implémenterait la logique de connexion en tant qu'autre utilisateur\n      // Pour l'instant, on simule juste\n      console.log(`Connexion en tant que ${user.firstName} ${user.lastName}`);\n      setLoginAsDialogOpen(false);\n      setSelectedUser(null);\n      \n      // Rediriger vers le dashboard approprié selon le rôle\n      // window.location.href = user.role === 'teacher' ? '/teacher-dashboard' : '/student-dashboard';\n      \n    } catch (err) {\n      console.error('Erreur connexion utilisateur:', err);\n      setError('Impossible de se connecter en tant que cet utilisateur');\n    }\n  };\n\n  /**\n   * Ouvre le menu utilisateur\n   */\n  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>, user: User) => {\n    setUserMenuAnchor(event.currentTarget);\n    setSelectedUser(user);\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 4 }}>\n      {/* En-tête avec actions de reconnaissance faciale */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={4}>\n        <Box>\n          <Typography variant=\"h3\" gutterBottom>\n            Dashboard Administrateur\n          </Typography>\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            Bienvenue, {currentUser?.firstName} {currentUser?.lastName}\n          </Typography>\n        </Box>\n\n        <Stack direction=\"row\" spacing={2}>\n          <Button\n            variant=\"contained\"\n            startIcon={<PhotoCameraIcon />}\n            onClick={() => setStudentPhotoManagementOpen(true)}\n            color=\"primary\"\n          >\n            Gérer Photos Étudiants\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<VideocamIcon />}\n            onClick={() => setLiveRecognitionOpen(true)}\n            color=\"secondary\"\n          >\n            Reconnaissance Live\n          </Button>\n          <Button\n            variant=\"outlined\"\n            color=\"error\"\n            onClick={signOut}\n          >\n            Déconnexion\n          </Button>\n        </Stack>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {loading && <LinearProgress sx={{ mb: 3 }} />}\n\n      {/* Statistiques générales */}\n      {stats && (\n        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <PeopleIcon color=\"primary\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.totalStudents}</Typography>\n                  <Typography color=\"text.secondary\">Étudiants</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <SchoolIcon color=\"secondary\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.totalTeachers}</Typography>\n                  <Typography color=\"text.secondary\">Professeurs</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <DashboardIcon color=\"success\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.totalCourses}</Typography>\n                  <Typography color=\"text.secondary\">Cours</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <AnalyticsIcon color=\"warning\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.averageAttendanceRate}%</Typography>\n                  <Typography color=\"text.secondary\">Taux Présence</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Stack>\n      )}\n\n      {/* Onglets */}\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>\n        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>\n          <Tab label=\"Vue d'ensemble\" icon={<DashboardIcon />} />\n          <Tab label=\"Utilisateurs\" icon={<PeopleIcon />} />\n          <Tab label=\"Cours\" icon={<SchoolIcon />} />\n          <Tab label=\"Reconnaissance Faciale\" icon={<FaceIcon />} />\n        </Tabs>\n      </Box>\n\n      {/* Contenu des onglets */}\n      <TabPanel value={tabValue} index={0}>\n        {/* Vue d'ensemble avec graphiques */}\n        <Stack spacing={4}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Évolution des Présences (7 derniers jours)\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <LineChart data={attendanceChartData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"date\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Line type=\"monotone\" dataKey=\"rate\" stroke=\"#8884d8\" name=\"Taux %\" />\n                </LineChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Présences par Cours\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={courseStatsData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"name\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"present\" stackId=\"a\" fill=\"#4caf50\" name=\"Présent\" />\n                  <Bar dataKey=\"late\" stackId=\"a\" fill=\"#ff9800\" name=\"Retard\" />\n                  <Bar dataKey=\"absent\" stackId=\"a\" fill=\"#f44336\" name=\"Absent\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={1}>\n        {/* Gestion des utilisateurs */}\n        <Stack spacing={3}>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n            <Typography variant=\"h6\">Gestion des Utilisateurs</Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<PersonAddIcon />}\n              onClick={() => {/* Ouvrir dialog création utilisateur */}}\n            >\n              Nouvel Utilisateur\n            </Button>\n          </Box>\n\n          <Stack spacing={2}>\n            {users.map((user) => (\n              <Card key={user.id}>\n                <CardContent>\n                  <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n                      <Box>\n                        <Typography variant=\"h6\">\n                          {user.firstName} {user.lastName}\n                        </Typography>\n                        <Typography color=\"text.secondary\">\n                          {user.email} • {user.roleDisplay}\n                        </Typography>\n                      </Box>\n                      <Chip \n                        label={user.role} \n                        color={user.role === 'admin' ? 'error' : user.role === 'teacher' ? 'primary' : 'secondary'}\n                        size=\"small\"\n                      />\n                    </Stack>\n                    \n                    <IconButton onClick={(e) => handleUserMenuClick(e, user)}>\n                      <MoreVertIcon />\n                    </IconButton>\n                  </Box>\n                </CardContent>\n              </Card>\n            ))}\n          </Stack>\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={2}>\n        {/* Gestion des cours */}\n        <Typography variant=\"h6\" gutterBottom>Gestion des Cours</Typography>\n        <Stack spacing={2}>\n          {courses.map((course) => (\n            <Card key={course.id}>\n              <CardContent>\n                <Typography variant=\"h6\">{course.name}</Typography>\n                <Typography color=\"text.secondary\">\n                  {course.description}\n                </Typography>\n                <Stack direction=\"row\" spacing={1} sx={{ mt: 1 }}>\n                  <Chip label={`${course.credits} crédits`} size=\"small\" />\n                  <Chip label={course.semester} size=\"small\" />\n                </Stack>\n              </CardContent>\n            </Card>\n          ))}\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={3}>\n        {/* Reconnaissance faciale */}\n        <Typography variant=\"h6\" gutterBottom>Système de Reconnaissance Faciale</Typography>\n        <Card>\n          <CardContent>\n            <Stack spacing={2}>\n              <Typography>\n                Statistiques du système de reconnaissance faciale\n              </Typography>\n              {/* Ici on afficherait les stats du service de reconnaissance */}\n              <Button\n                variant=\"outlined\"\n                onClick={() => setFaceRegistrationOpen(true)}\n              >\n                Gérer les Encodages Faciaux\n              </Button>\n            </Stack>\n          </CardContent>\n        </Card>\n      </TabPanel>\n\n      {/* Menu utilisateur */}\n      <Menu\n        anchorEl={userMenuAnchor}\n        open={Boolean(userMenuAnchor)}\n        onClose={() => setUserMenuAnchor(null)}\n      >\n        <MenuItem onClick={() => setLoginAsDialogOpen(true)}>\n          <LoginIcon sx={{ mr: 1 }} />\n          Se connecter en tant que\n        </MenuItem>\n        <MenuItem onClick={() => {/* Éditer utilisateur */}}>\n          <SettingsIcon sx={{ mr: 1 }} />\n          Modifier\n        </MenuItem>\n      </Menu>\n\n      {/* Dialog connexion en tant qu'utilisateur */}\n      <Dialog open={loginAsDialogOpen} onClose={() => setLoginAsDialogOpen(false)}>\n        <DialogTitle>Se connecter en tant qu'utilisateur</DialogTitle>\n        <DialogContent>\n          {selectedUser && (\n            <Typography>\n              Voulez-vous vous connecter en tant que{' '}\n              <strong>{selectedUser.firstName} {selectedUser.lastName}</strong> ?\n            </Typography>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setLoginAsDialogOpen(false)}>Annuler</Button>\n          <Button \n            onClick={() => selectedUser && loginAsUser(selectedUser)}\n            variant=\"contained\"\n          >\n            Se connecter\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EAabC,cAAc,QACT,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,QAGnB,qBAAqB;AAC5B,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,QAIE,UAAU;AACjB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,QAAQ,6BAA6B;AAI7D,SAAyDC,QAAQ,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQpF,SAASC,QAAQA,CAACC,KAAoB,EAAE;EACtC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAClD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,kBAAkBJ,KAAK,EAAG;IAC9B,mBAAiB,aAAaA,KAAK,EAAG;IAAA,GAClCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAACtD,GAAG;MAACgE,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAbQf,QAAQ;AAejB,MAAMgB,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC,IAAI,EAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAChD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACoF,OAAO,EAAEC,UAAU,CAAC,GAAGrF,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAqB,EAAE,CAAC;EAClF,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0F,KAAK,EAAEC,QAAQ,CAAC,GAAG3F,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC8F,cAAc,EAAEC,iBAAiB,CAAC,GAAG/F,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAM,CAACgG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACoG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;;EAEnF;EACA,MAAM,CAACwG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzG,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAAC0G,eAAe,EAAEC,kBAAkB,CAAC,GAAG3G,QAAQ,CAAQ,EAAE,CAAC;;EAEjE;AACF;AACA;EACE,MAAM4G,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM,CAACkB,SAAS,EAAEC,WAAW,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjE5D,eAAe,CAAC6D,QAAQ,CAAC,CAAC,EAC1B7D,eAAe,CAAC8D,UAAU,CAAC,CAAC,EAC5B9D,eAAe,CAAC+D,oBAAoB,CAAC,CAAC,CACvC,CAAC;MAEFjC,QAAQ,CAAC0B,SAAS,CAAC;MACnBxB,UAAU,CAACyB,WAAW,CAAC;MACvBvB,oBAAoB,CAACwB,cAAc,CAAC;;MAEpC;MACA,MAAMM,cAA8B,GAAG;QACrCC,aAAa,EAAET,SAAS,CAACU,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzD,IAAI,KAAKT,QAAQ,CAACmE,OAAO,CAAC,CAACC,MAAM;QACxEC,aAAa,EAAEd,SAAS,CAACU,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzD,IAAI,KAAKT,QAAQ,CAACsE,OAAO,CAAC,CAACF,MAAM;QACxEG,YAAY,EAAEf,WAAW,CAACY,MAAM;QAChCI,aAAa,EAAE,CAAC;QAAE;QAClBC,qBAAqB,EAAEC,8BAA8B,CAACjB,cAAc,CAAC;QACrEkB,cAAc,EAAE,EAAE,CAAC;MACrB,CAAC;MAEDhD,QAAQ,CAACoC,cAAc,CAAC;;MAExB;MACAa,gBAAgB,CAACnB,cAAc,EAAED,WAAW,CAAC;IAE/C,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAAC1C,KAAK,CAAC,8BAA8B,EAAEyC,GAAG,CAAC;MAClDxC,QAAQ,CAAC,gDAAgD,CAAC;IAC5D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMuC,8BAA8B,GAAIK,OAA2B,IAAa;IAC9E,IAAIA,OAAO,CAACX,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAElC,MAAMY,cAAc,GAAGD,OAAO,CAACd,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,IAAID,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC;IACzF,OAAOC,IAAI,CAACC,KAAK,CAAEJ,cAAc,CAACZ,MAAM,GAAGW,OAAO,CAACX,MAAM,GAAI,GAAG,CAAC;EACnE,CAAC;;EAED;AACF;AACA;EACE,MAAMQ,gBAAgB,GAAGA,CAACS,UAA8B,EAAEvD,OAAiB,KAAK;IAC9E;IACA,MAAMwD,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEpB,MAAM,EAAE;IAAE,CAAC,EAAE,CAACqB,CAAC,EAAEC,CAAC,KAAK;MACpD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC;MACvBD,IAAI,CAACE,OAAO,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,GAAGJ,CAAC,CAAC;MAChC,OAAOC,IAAI,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEZ,MAAMC,eAAe,GAAGZ,SAAS,CAACa,GAAG,CAACR,IAAI,IAAI;MAC5C,MAAMS,UAAU,GAAGf,UAAU,CAACpB,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACU,IAAI,KAAKA,IAAI,CAAC;MAC1D,MAAMU,OAAO,GAAGD,UAAU,CAACnC,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACd,MAAM;MACrE,MAAMkC,IAAI,GAAGF,UAAU,CAACnC,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC,CAACd,MAAM;MAC/D,MAAMmC,MAAM,GAAGH,UAAU,CAACnC,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACd,MAAM;MAEnE,OAAO;QACLuB,IAAI,EAAE,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACa,kBAAkB,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAU,CAAC,CAAC;QACtFL,OAAO;QACPC,IAAI;QACJC,MAAM;QACNI,KAAK,EAAEP,UAAU,CAAChC,MAAM;QACxBwC,IAAI,EAAER,UAAU,CAAChC,MAAM,GAAG,CAAC,GAAGe,IAAI,CAACC,KAAK,CAAE,CAACiB,OAAO,GAAGC,IAAI,IAAIF,UAAU,CAAChC,MAAM,GAAI,GAAG,CAAC,GAAG;MAC3F,CAAC;IACH,CAAC,CAAC;IAEFjB,sBAAsB,CAAC+C,eAAe,CAAC;;IAEvC;IACA,MAAMW,WAAW,GAAG/E,OAAO,CAACqE,GAAG,CAACW,MAAM,IAAI;MACxC,MAAMC,aAAa,GAAG1B,UAAU,CAACpB,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAAC6B,MAAM,CAACnG,EAAE,KAAKmG,MAAM,CAACnG,EAAE,CAAC;MACvE,MAAM0F,OAAO,GAAGU,aAAa,CAAC9C,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACd,MAAM;MACxE,MAAMkC,IAAI,GAAGS,aAAa,CAAC9C,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC,CAACd,MAAM;MAClE,MAAMmC,MAAM,GAAGQ,aAAa,CAAC9C,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACd,MAAM;MAEtE,OAAO;QACL4C,IAAI,EAAEF,MAAM,CAACE,IAAI,CAAC5C,MAAM,GAAG,EAAE,GAAG0C,MAAM,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGH,MAAM,CAACE,IAAI;QAClFX,OAAO;QACPC,IAAI;QACJC,MAAM;QACNI,KAAK,EAAEI,aAAa,CAAC3C,MAAM;QAC3BwC,IAAI,EAAEG,aAAa,CAAC3C,MAAM,GAAG,CAAC,GAAGe,IAAI,CAACC,KAAK,CAAE,CAACiB,OAAO,GAAGC,IAAI,IAAIS,aAAa,CAAC3C,MAAM,GAAI,GAAG,CAAC,GAAG;MACjG,CAAC;IACH,CAAC,CAAC;IAEFf,kBAAkB,CAACwD,WAAW,CAAC;EACjC,CAAC;;EAED;AACF;AACA;EACE,MAAMK,WAAW,GAAG,MAAO7F,IAAU,IAAK;IACxC,IAAI;MACF;MACA;MACAyD,OAAO,CAACqC,GAAG,CAAC,yBAAyB9F,IAAI,CAAC+F,SAAS,IAAI/F,IAAI,CAACgG,QAAQ,EAAE,CAAC;MACvE1E,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA;IAEF,CAAC,CAAC,OAAOsC,GAAG,EAAE;MACZC,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,EAAEyC,GAAG,CAAC;MACnDxC,QAAQ,CAAC,wDAAwD,CAAC;IACpE;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMiF,mBAAmB,GAAGA,CAACC,KAAoC,EAAElG,IAAU,KAAK;IAChFoB,iBAAiB,CAAC8E,KAAK,CAACC,aAAa,CAAC;IACtCjF,eAAe,CAAClB,IAAI,CAAC;EACvB,CAAC;;EAED;EACA1E,SAAS,CAAC,MAAM;IACd2G,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmE,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAE3D,oBACEvH,OAAA,CAACrD,SAAS;IAAC6K,QAAQ,EAAC,IAAI;IAAC9G,EAAE,EAAE;MAAE+G,EAAE,EAAE;IAAE,CAAE;IAAAtH,QAAA,gBAErCH,OAAA,CAACtD,GAAG;MAACgL,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAA1H,QAAA,gBAC3EH,OAAA,CAACtD,GAAG;QAAAyD,QAAA,gBACFH,OAAA,CAACpD,UAAU;UAACkL,OAAO,EAAC,IAAI;UAACC,YAAY;UAAA5H,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACpD,UAAU;UAACkL,OAAO,EAAC,IAAI;UAACE,KAAK,EAAC,gBAAgB;UAAA7H,QAAA,GAAC,aACnC,EAACiB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8F,SAAS,EAAC,GAAC,EAAC9F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+F,QAAQ;QAAA;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENf,OAAA,CAACjD,KAAK;QAACkL,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAA/H,QAAA,gBAChCH,OAAA,CAAC/C,MAAM;UACL6K,OAAO,EAAC,WAAW;UACnBK,SAAS,eAAEnI,OAAA,CAAChB,eAAe;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC/BqH,OAAO,EAAEA,CAAA,KAAMrF,6BAA6B,CAAC,IAAI,CAAE;UACnDiF,KAAK,EAAC,SAAS;UAAA7H,QAAA,EAChB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA,CAAC/C,MAAM;UACL6K,OAAO,EAAC,WAAW;UACnBK,SAAS,eAAEnI,OAAA,CAACd,YAAY;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BqH,OAAO,EAAEA,CAAA,KAAMvF,sBAAsB,CAAC,IAAI,CAAE;UAC5CmF,KAAK,EAAC,WAAW;UAAA7H,QAAA,EAClB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA,CAAC/C,MAAM;UACL6K,OAAO,EAAC,UAAU;UAClBE,KAAK,EAAC,OAAO;UACbI,OAAO,EAAE/G,OAAQ;UAAAlB,QAAA,EAClB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELmB,KAAK,iBACJlC,OAAA,CAAC9C,KAAK;MAACmL,QAAQ,EAAC,OAAO;MAAC3H,EAAE,EAAE;QAAEmH,EAAE,EAAE;MAAE,CAAE;MAACS,OAAO,EAAEA,CAAA,KAAMnG,QAAQ,CAAC,IAAI,CAAE;MAAAhC,QAAA,EAClE+B;IAAK;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAiB,OAAO,iBAAIhC,OAAA,CAACpC,cAAc;MAAC8C,EAAE,EAAE;QAAEmH,EAAE,EAAE;MAAE;IAAE;MAAAjH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG5CS,KAAK,iBACJxB,OAAA,CAACjD,KAAK;MAACkL,SAAS,EAAE;QAAEM,EAAE,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAM,CAAE;MAACN,OAAO,EAAE,CAAE;MAACxH,EAAE,EAAE;QAAEmH,EAAE,EAAE;MAAE,CAAE;MAAA1H,QAAA,gBACvEH,OAAA,CAACnD,IAAI;QAAC6D,EAAE,EAAE;UAAE+H,IAAI,EAAE;QAAE,CAAE;QAAAtI,QAAA,eACpBH,OAAA,CAAClD,WAAW;UAAAqD,QAAA,eACVH,OAAA,CAACjD,KAAK;YAACkL,SAAS,EAAC,KAAK;YAACL,UAAU,EAAC,QAAQ;YAACM,OAAO,EAAE,CAAE;YAAA/H,QAAA,gBACpDH,OAAA,CAAChC,UAAU;cAACgK,KAAK,EAAC,SAAS;cAACU,QAAQ,EAAC;YAAO;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/Cf,OAAA,CAACtD,GAAG;cAAAyD,QAAA,gBACFH,OAAA,CAACpD,UAAU;gBAACkL,OAAO,EAAC,IAAI;gBAAA3H,QAAA,EAAEqB,KAAK,CAACsC;cAAa;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3Df,OAAA,CAACpD,UAAU;gBAACoL,KAAK,EAAC,gBAAgB;gBAAA7H,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAACnD,IAAI;QAAC6D,EAAE,EAAE;UAAE+H,IAAI,EAAE;QAAE,CAAE;QAAAtI,QAAA,eACpBH,OAAA,CAAClD,WAAW;UAAAqD,QAAA,eACVH,OAAA,CAACjD,KAAK;YAACkL,SAAS,EAAC,KAAK;YAACL,UAAU,EAAC,QAAQ;YAACM,OAAO,EAAE,CAAE;YAAA/H,QAAA,gBACpDH,OAAA,CAAC9B,UAAU;cAAC8J,KAAK,EAAC,WAAW;cAACU,QAAQ,EAAC;YAAO;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDf,OAAA,CAACtD,GAAG;cAAAyD,QAAA,gBACFH,OAAA,CAACpD,UAAU;gBAACkL,OAAO,EAAC,IAAI;gBAAA3H,QAAA,EAAEqB,KAAK,CAAC2C;cAAa;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3Df,OAAA,CAACpD,UAAU;gBAACoL,KAAK,EAAC,gBAAgB;gBAAA7H,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAACnD,IAAI;QAAC6D,EAAE,EAAE;UAAE+H,IAAI,EAAE;QAAE,CAAE;QAAAtI,QAAA,eACpBH,OAAA,CAAClD,WAAW;UAAAqD,QAAA,eACVH,OAAA,CAACjD,KAAK;YAACkL,SAAS,EAAC,KAAK;YAACL,UAAU,EAAC,QAAQ;YAACM,OAAO,EAAE,CAAE;YAAA/H,QAAA,gBACpDH,OAAA,CAAClC,aAAa;cAACkK,KAAK,EAAC,SAAS;cAACU,QAAQ,EAAC;YAAO;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDf,OAAA,CAACtD,GAAG;cAAAyD,QAAA,gBACFH,OAAA,CAACpD,UAAU;gBAACkL,OAAO,EAAC,IAAI;gBAAA3H,QAAA,EAAEqB,KAAK,CAAC6C;cAAY;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1Df,OAAA,CAACpD,UAAU;gBAACoL,KAAK,EAAC,gBAAgB;gBAAA7H,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAACnD,IAAI;QAAC6D,EAAE,EAAE;UAAE+H,IAAI,EAAE;QAAE,CAAE;QAAAtI,QAAA,eACpBH,OAAA,CAAClD,WAAW;UAAAqD,QAAA,eACVH,OAAA,CAACjD,KAAK;YAACkL,SAAS,EAAC,KAAK;YAACL,UAAU,EAAC,QAAQ;YAACM,OAAO,EAAE,CAAE;YAAA/H,QAAA,gBACpDH,OAAA,CAAC5B,aAAa;cAAC4J,KAAK,EAAC,SAAS;cAACU,QAAQ,EAAC;YAAO;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDf,OAAA,CAACtD,GAAG;cAAAyD,QAAA,gBACFH,OAAA,CAACpD,UAAU;gBAACkL,OAAO,EAAC,IAAI;gBAAA3H,QAAA,GAAEqB,KAAK,CAAC+C,qBAAqB,EAAC,GAAC;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpEf,OAAA,CAACpD,UAAU;gBAACoL,KAAK,EAAC,gBAAgB;gBAAA7H,QAAA,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACR,eAGDf,OAAA,CAACtD,GAAG;MAACgE,EAAE,EAAE;QAAEiI,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEf,EAAE,EAAE;MAAE,CAAE;MAAA1H,QAAA,eAC1DH,OAAA,CAAC7C,IAAI;QAACiD,KAAK,EAAEkB,QAAS;QAACuH,QAAQ,EAAEA,CAACtD,CAAC,EAAEuD,QAAQ,KAAKvH,WAAW,CAACuH,QAAQ,CAAE;QAAA3I,QAAA,gBACtEH,OAAA,CAAC5C,GAAG;UAAC2L,KAAK,EAAC,gBAAgB;UAACC,IAAI,eAAEhJ,OAAA,CAAClC,aAAa;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDf,OAAA,CAAC5C,GAAG;UAAC2L,KAAK,EAAC,cAAc;UAACC,IAAI,eAAEhJ,OAAA,CAAChC,UAAU;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDf,OAAA,CAAC5C,GAAG;UAAC2L,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEhJ,OAAA,CAAC9B,UAAU;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3Cf,OAAA,CAAC5C,GAAG;UAAC2L,KAAK,EAAC,wBAAwB;UAACC,IAAI,eAAEhJ,OAAA,CAAClB,QAAQ;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEkB,QAAS;MAACjB,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAACjD,KAAK;QAACmL,OAAO,EAAE,CAAE;QAAA/H,QAAA,gBAChBH,OAAA,CAACnD,IAAI;UAAAsD,QAAA,eACHH,OAAA,CAAClD,WAAW;YAAAqD,QAAA,gBACVH,OAAA,CAACpD,UAAU;cAACkL,OAAO,EAAC,IAAI;cAACC,YAAY;cAAA5H,QAAA,EAAC;YAEtC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbf,OAAA,CAACP,mBAAmB;cAACwJ,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAA/I,QAAA,eAC5CH,OAAA,CAACb,SAAS;gBAACgK,IAAI,EAAEnG,mBAAoB;gBAAA7C,QAAA,gBACnCH,OAAA,CAACT,aAAa;kBAAC6J,eAAe,EAAC;gBAAK;kBAAAxI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCf,OAAA,CAACX,KAAK;kBAACgK,OAAO,EAAC;gBAAM;kBAAAzI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBf,OAAA,CAACV,KAAK;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTf,OAAA,CAACR,OAAO;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXf,OAAA,CAACZ,IAAI;kBAACkK,IAAI,EAAC,UAAU;kBAACD,OAAO,EAAC,MAAM;kBAACE,MAAM,EAAC,SAAS;kBAACzC,IAAI,EAAC;gBAAQ;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPf,OAAA,CAACnD,IAAI;UAAAsD,QAAA,eACHH,OAAA,CAAClD,WAAW;YAAAqD,QAAA,gBACVH,OAAA,CAACpD,UAAU;cAACkL,OAAO,EAAC,IAAI;cAACC,YAAY;cAAA5H,QAAA,EAAC;YAEtC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbf,OAAA,CAACP,mBAAmB;cAACwJ,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAA/I,QAAA,eAC5CH,OAAA,CAACN,QAAQ;gBAACyJ,IAAI,EAAEjG,eAAgB;gBAAA/C,QAAA,gBAC9BH,OAAA,CAACT,aAAa;kBAAC6J,eAAe,EAAC;gBAAK;kBAAAxI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCf,OAAA,CAACX,KAAK;kBAACgK,OAAO,EAAC;gBAAM;kBAAAzI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBf,OAAA,CAACV,KAAK;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTf,OAAA,CAACR,OAAO;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXf,OAAA,CAACL,GAAG;kBAAC0J,OAAO,EAAC,SAAS;kBAACG,OAAO,EAAC,GAAG;kBAACC,IAAI,EAAC,SAAS;kBAAC3C,IAAI,EAAC;gBAAS;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnEf,OAAA,CAACL,GAAG;kBAAC0J,OAAO,EAAC,MAAM;kBAACG,OAAO,EAAC,GAAG;kBAACC,IAAI,EAAC,SAAS;kBAAC3C,IAAI,EAAC;gBAAQ;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/Df,OAAA,CAACL,GAAG;kBAAC0J,OAAO,EAAC,QAAQ;kBAACG,OAAO,EAAC,GAAG;kBAACC,IAAI,EAAC,SAAS;kBAAC3C,IAAI,EAAC;gBAAQ;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEkB,QAAS;MAACjB,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAACjD,KAAK;QAACmL,OAAO,EAAE,CAAE;QAAA/H,QAAA,gBAChBH,OAAA,CAACtD,GAAG;UAACgL,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAAAzH,QAAA,gBACpEH,OAAA,CAACpD,UAAU;YAACkL,OAAO,EAAC,IAAI;YAAA3H,QAAA,EAAC;UAAwB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9Df,OAAA,CAAC/C,MAAM;YACL6K,OAAO,EAAC,WAAW;YACnBK,SAAS,eAAEnI,OAAA,CAACxB,aAAa;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BqH,OAAO,EAAEA,CAAA,KAAM,CAAC,yCAA0C;YAAAjI,QAAA,EAC3D;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENf,OAAA,CAACjD,KAAK;UAACmL,OAAO,EAAE,CAAE;UAAA/H,QAAA,EACfuB,KAAK,CAACuE,GAAG,CAAE9E,IAAI,iBACdnB,OAAA,CAACnD,IAAI;YAAAsD,QAAA,eACHH,OAAA,CAAClD,WAAW;cAAAqD,QAAA,eACVH,OAAA,CAACtD,GAAG;gBAACgL,OAAO,EAAC,MAAM;gBAACC,cAAc,EAAC,eAAe;gBAACC,UAAU,EAAC,QAAQ;gBAAAzH,QAAA,gBACpEH,OAAA,CAACjD,KAAK;kBAACkL,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,CAAE;kBAACN,UAAU,EAAC,QAAQ;kBAAAzH,QAAA,gBACpDH,OAAA,CAACtD,GAAG;oBAAAyD,QAAA,gBACFH,OAAA,CAACpD,UAAU;sBAACkL,OAAO,EAAC,IAAI;sBAAA3H,QAAA,GACrBgB,IAAI,CAAC+F,SAAS,EAAC,GAAC,EAAC/F,IAAI,CAACgG,QAAQ;oBAAA;sBAAAvG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACbf,OAAA,CAACpD,UAAU;sBAACoL,KAAK,EAAC,gBAAgB;sBAAA7H,QAAA,GAC/BgB,IAAI,CAACuI,KAAK,EAAC,UAAG,EAACvI,IAAI,CAACwI,WAAW;oBAAA;sBAAA/I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNf,OAAA,CAAChD,IAAI;oBACH+L,KAAK,EAAE5H,IAAI,CAACZ,IAAK;oBACjByH,KAAK,EAAE7G,IAAI,CAACZ,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGY,IAAI,CAACZ,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,WAAY;oBAC3FqJ,IAAI,EAAC;kBAAO;oBAAAhJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eAERf,OAAA,CAAC3C,UAAU;kBAAC+K,OAAO,EAAGyB,CAAC,IAAKzC,mBAAmB,CAACyC,CAAC,EAAE1I,IAAI,CAAE;kBAAAhB,QAAA,eACvDH,OAAA,CAACpB,YAAY;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC,GAvBLI,IAAI,CAACV,EAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBZ,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEkB,QAAS;MAACjB,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAACpD,UAAU;QAACkL,OAAO,EAAC,IAAI;QAACC,YAAY;QAAA5H,QAAA,EAAC;MAAiB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpEf,OAAA,CAACjD,KAAK;QAACmL,OAAO,EAAE,CAAE;QAAA/H,QAAA,EACfyB,OAAO,CAACqE,GAAG,CAAEW,MAAM,iBAClB5G,OAAA,CAACnD,IAAI;UAAAsD,QAAA,eACHH,OAAA,CAAClD,WAAW;YAAAqD,QAAA,gBACVH,OAAA,CAACpD,UAAU;cAACkL,OAAO,EAAC,IAAI;cAAA3H,QAAA,EAAEyG,MAAM,CAACE;YAAI;cAAAlG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnDf,OAAA,CAACpD,UAAU;cAACoL,KAAK,EAAC,gBAAgB;cAAA7H,QAAA,EAC/ByG,MAAM,CAACkD;YAAW;cAAAlJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACbf,OAAA,CAACjD,KAAK;cAACkL,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAACxH,EAAE,EAAE;gBAAEqJ,EAAE,EAAE;cAAE,CAAE;cAAA5J,QAAA,gBAC/CH,OAAA,CAAChD,IAAI;gBAAC+L,KAAK,EAAE,GAAGnC,MAAM,CAACoD,OAAO,UAAW;gBAACJ,IAAI,EAAC;cAAO;gBAAAhJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDf,OAAA,CAAChD,IAAI;gBAAC+L,KAAK,EAAEnC,MAAM,CAACqD,QAAS;gBAACL,IAAI,EAAC;cAAO;gBAAAhJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC,GAVL6F,MAAM,CAACnG,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWd,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEkB,QAAS;MAACjB,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAACpD,UAAU;QAACkL,OAAO,EAAC,IAAI;QAACC,YAAY;QAAA5H,QAAA,EAAC;MAAiC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpFf,OAAA,CAACnD,IAAI;QAAAsD,QAAA,eACHH,OAAA,CAAClD,WAAW;UAAAqD,QAAA,eACVH,OAAA,CAACjD,KAAK;YAACmL,OAAO,EAAE,CAAE;YAAA/H,QAAA,gBAChBH,OAAA,CAACpD,UAAU;cAAAuD,QAAA,EAAC;YAEZ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbf,OAAA,CAAC/C,MAAM;cACL6K,OAAO,EAAC,UAAU;cAClBM,OAAO,EAAEA,CAAA,KAAMzF,uBAAuB,CAAC,IAAI,CAAE;cAAAxC,QAAA,EAC9C;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGXf,OAAA,CAAC1C,IAAI;MACH4M,QAAQ,EAAE5H,cAAe;MACzB6H,IAAI,EAAEC,OAAO,CAAC9H,cAAc,CAAE;MAC9BgG,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAAC,IAAI,CAAE;MAAApC,QAAA,gBAEvCH,OAAA,CAACzC,QAAQ;QAAC6K,OAAO,EAAEA,CAAA,KAAM3F,oBAAoB,CAAC,IAAI,CAAE;QAAAtC,QAAA,gBAClDH,OAAA,CAACtB,SAAS;UAACgC,EAAE,EAAE;YAAE2J,EAAE,EAAE;UAAE;QAAE;UAAAzJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXf,OAAA,CAACzC,QAAQ;QAAC6K,OAAO,EAAEA,CAAA,KAAM,CAAC,yBAA0B;QAAAjI,QAAA,gBAClDH,OAAA,CAAC1B,YAAY;UAACoC,EAAE,EAAE;YAAE2J,EAAE,EAAE;UAAE;QAAE;UAAAzJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,YAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPf,OAAA,CAACxC,MAAM;MAAC2M,IAAI,EAAE3H,iBAAkB;MAAC8F,OAAO,EAAEA,CAAA,KAAM7F,oBAAoB,CAAC,KAAK,CAAE;MAAAtC,QAAA,gBAC1EH,OAAA,CAACvC,WAAW;QAAA0C,QAAA,EAAC;MAAmC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9Df,OAAA,CAACtC,aAAa;QAAAyC,QAAA,EACXiC,YAAY,iBACXpC,OAAA,CAACpD,UAAU;UAAAuD,QAAA,GAAC,wCAC4B,EAAC,GAAG,eAC1CH,OAAA;YAAAG,QAAA,GAASiC,YAAY,CAAC8E,SAAS,EAAC,GAAC,EAAC9E,YAAY,CAAC+E,QAAQ;UAAA;YAAAvG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,MACnE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBf,OAAA,CAACrC,aAAa;QAAAwC,QAAA,gBACZH,OAAA,CAAC/C,MAAM;UAACmL,OAAO,EAAEA,CAAA,KAAM3F,oBAAoB,CAAC,KAAK,CAAE;UAAAtC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpEf,OAAA,CAAC/C,MAAM;UACLmL,OAAO,EAAEA,CAAA,KAAMhG,YAAY,IAAI4E,WAAW,CAAC5E,YAAY,CAAE;UACzD0F,OAAO,EAAC,WAAW;UAAA3H,QAAA,EACpB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACG,EAAA,CAtbID,cAAwB;EAAA,QACWrB,OAAO;AAAA;AAAA0K,GAAA,GAD1CrJ,cAAwB;AAwb9B,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAsJ,GAAA;AAAAC,YAAA,CAAAvJ,EAAA;AAAAuJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}