{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { convLayer } from '../common';\nimport { prelu } from './prelu';\nexport function sharedLayer(x, params, isPnet) {\n  if (isPnet === void 0) {\n    isPnet = false;\n  }\n  return tf.tidy(function () {\n    var out = convLayer(x, params.conv1, 'valid');\n    out = prelu(out, params.prelu1_alpha);\n    out = tf.maxPool(out, isPnet ? [2, 2] : [3, 3], [2, 2], 'same');\n    out = convLayer(out, params.conv2, 'valid');\n    out = prelu(out, params.prelu2_alpha);\n    out = isPnet ? out : tf.maxPool(out, [3, 3], [2, 2], 'valid');\n    out = convLayer(out, params.conv3, 'valid');\n    out = prelu(out, params.prelu3_alpha);\n    return out;\n  });\n}", "map": {"version": 3, "names": ["tf", "convLayer", "prelu", "<PERSON><PERSON><PERSON><PERSON>", "x", "params", "isPnet", "tidy", "out", "conv1", "prelu1_alpha", "maxPool", "conv2", "prelu2_alpha", "conv3", "prelu3_alpha"], "sources": ["../../../src/mtcnn/sharedLayers.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,KAAK,QAAQ,SAAS;AAG/B,OAAM,SAAUC,WAAWA,CAACC,CAAc,EAAEC,MAAoB,EAAEC,MAAuB;EAAvB,IAAAA,MAAA;IAAAA,MAAA,QAAuB;EAAA;EACvF,OAAON,EAAE,CAACO,IAAI,CAAC;IAEb,IAAIC,GAAG,GAAGP,SAAS,CAACG,CAAC,EAAEC,MAAM,CAACI,KAAK,EAAE,OAAO,CAAC;IAC7CD,GAAG,GAAGN,KAAK,CAAcM,GAAG,EAAEH,MAAM,CAACK,YAAY,CAAC;IAClDF,GAAG,GAAGR,EAAE,CAACW,OAAO,CAACH,GAAG,EAAEF,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC9DE,GAAG,GAAGP,SAAS,CAACO,GAAG,EAAEH,MAAM,CAACO,KAAK,EAAE,OAAO,CAAC;IAC3CJ,GAAG,GAAGN,KAAK,CAAcM,GAAG,EAAEH,MAAM,CAACQ,YAAY,CAAC;IAClDL,GAAG,GAAGF,MAAM,GAAGE,GAAG,GAAGR,EAAE,CAACW,OAAO,CAACH,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;IAC7DA,GAAG,GAAGP,SAAS,CAACO,GAAG,EAAEH,MAAM,CAACS,KAAK,EAAE,OAAO,CAAC;IAC3CN,GAAG,GAAGN,KAAK,CAAcM,GAAG,EAAEH,MAAM,CAACU,YAAY,CAAC;IAElD,OAAOP,GAAG;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}