{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { env } from '../env';\nexport function fetchOrThrow(url, init) {\n  return __awaiter(this, void 0, void 0, function () {\n    var fetch, res;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          fetch = env.getEnv().fetch;\n          return [4 /*yield*/, fetch(url, init)];\n        case 1:\n          res = _a.sent();\n          if (!(res.status < 400)) {\n            throw new Error(\"failed to fetch: (\" + res.status + \") \" + res.statusText + \", from url: \" + res.url);\n          }\n          return [2 /*return*/, res];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["env", "fetchOrThrow", "url", "init", "fetch", "getEnv", "res", "_a", "sent", "status", "Error", "statusText"], "sources": ["../../../src/dom/fetchOrThrow.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,GAAG,QAAQ,QAAQ;AAE5B,OAAM,SAAgBC,YAAYA,CAChCC,GAAW,EACXC,IAAkB;;;;;;UAGZC,KAAK,GAAGJ,GAAG,CAACK,MAAM,EAAE,CAACD,KAAK;UACpB,qBAAMA,KAAK,CAACF,GAAG,EAAEC,IAAI,CAAC;;UAA5BG,GAAG,GAAGC,EAAA,CAAAC,IAAA,EAAsB;UAClC,IAAI,EAAEF,GAAG,CAACG,MAAM,GAAG,GAAG,CAAC,EAAE;YACvB,MAAM,IAAIC,KAAK,CAAC,uBAAqBJ,GAAG,CAACG,MAAM,UAAKH,GAAG,CAACK,UAAU,oBAAeL,GAAG,CAACJ,GAAK,CAAC;;UAE7F,sBAAOI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}