{"ast": null, "code": "import { disposeUnusedWeightTensors, extractWeightEntryFactory, loadSeparableConvParamsFactory } from '../common';\nimport { loadConvParamsFactory } from '../common/loadConvParamsFactory';\nimport { range } from '../utils';\nfunction loadParamsFactory(weightMap, paramMappings) {\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  var extractConvParams = loadConvParamsFactory(extractWeightEntry);\n  var extractSeparableConvParams = loadSeparableConvParamsFactory(extractWeightEntry);\n  function extractReductionBlockParams(mappedPrefix) {\n    var separable_conv0 = extractSeparableConvParams(mappedPrefix + \"/separable_conv0\");\n    var separable_conv1 = extractSeparableConvParams(mappedPrefix + \"/separable_conv1\");\n    var expansion_conv = extractConvParams(mappedPrefix + \"/expansion_conv\");\n    return {\n      separable_conv0: separable_conv0,\n      separable_conv1: separable_conv1,\n      expansion_conv: expansion_conv\n    };\n  }\n  function extractMainBlockParams(mappedPrefix) {\n    var separable_conv0 = extractSeparableConvParams(mappedPrefix + \"/separable_conv0\");\n    var separable_conv1 = extractSeparableConvParams(mappedPrefix + \"/separable_conv1\");\n    var separable_conv2 = extractSeparableConvParams(mappedPrefix + \"/separable_conv2\");\n    return {\n      separable_conv0: separable_conv0,\n      separable_conv1: separable_conv1,\n      separable_conv2: separable_conv2\n    };\n  }\n  return {\n    extractConvParams: extractConvParams,\n    extractSeparableConvParams: extractSeparableConvParams,\n    extractReductionBlockParams: extractReductionBlockParams,\n    extractMainBlockParams: extractMainBlockParams\n  };\n}\nexport function extractParamsFromWeigthMap(weightMap, numMainBlocks) {\n  var paramMappings = [];\n  var _a = loadParamsFactory(weightMap, paramMappings),\n    extractConvParams = _a.extractConvParams,\n    extractSeparableConvParams = _a.extractSeparableConvParams,\n    extractReductionBlockParams = _a.extractReductionBlockParams,\n    extractMainBlockParams = _a.extractMainBlockParams;\n  var entry_flow_conv_in = extractConvParams('entry_flow/conv_in');\n  var entry_flow_reduction_block_0 = extractReductionBlockParams('entry_flow/reduction_block_0');\n  var entry_flow_reduction_block_1 = extractReductionBlockParams('entry_flow/reduction_block_1');\n  var entry_flow = {\n    conv_in: entry_flow_conv_in,\n    reduction_block_0: entry_flow_reduction_block_0,\n    reduction_block_1: entry_flow_reduction_block_1\n  };\n  var middle_flow = {};\n  range(numMainBlocks, 0, 1).forEach(function (idx) {\n    middle_flow[\"main_block_\" + idx] = extractMainBlockParams(\"middle_flow/main_block_\" + idx);\n  });\n  var exit_flow_reduction_block = extractReductionBlockParams('exit_flow/reduction_block');\n  var exit_flow_separable_conv = extractSeparableConvParams('exit_flow/separable_conv');\n  var exit_flow = {\n    reduction_block: exit_flow_reduction_block,\n    separable_conv: exit_flow_separable_conv\n  };\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: {\n      entry_flow: entry_flow,\n      middle_flow: middle_flow,\n      exit_flow: exit_flow\n    },\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "extractWeightEntryFactory", "loadSeparableConvParamsFactory", "loadConvParamsFactory", "range", "loadParamsFactory", "weightMap", "paramMappings", "extractWeightEntry", "extractConvParams", "extractSeparableConvParams", "extractReductionBlockParams", "mappedPrefix", "separable_conv0", "separable_conv1", "expansion_conv", "extractMainBlockParams", "separable_conv2", "extractParamsFromWeigthMap", "numMainBlocks", "_a", "entry_flow_conv_in", "entry_flow_reduction_block_0", "entry_flow_reduction_block_1", "entry_flow", "conv_in", "reduction_block_0", "reduction_block_1", "middle_flow", "for<PERSON>ach", "idx", "exit_flow_reduction_block", "exit_flow_separable_conv", "exit_flow", "reduction_block", "separable_conv", "params"], "sources": ["../../../src/xception/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": "AAEA,SACEA,0BAA0B,EAC1BC,yBAAyB,EACzBC,8BAA8B,QAEzB,WAAW;AAClB,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,KAAK,QAAQ,UAAU;AAGhC,SAASC,iBAAiBA,CAACC,SAAc,EAAEC,aAA6B;EAEtE,IAAMC,kBAAkB,GAAGP,yBAAyB,CAACK,SAAS,EAAEC,aAAa,CAAC;EAE9E,IAAME,iBAAiB,GAAGN,qBAAqB,CAACK,kBAAkB,CAAC;EACnE,IAAME,0BAA0B,GAAGR,8BAA8B,CAACM,kBAAkB,CAAC;EAErF,SAASG,2BAA2BA,CAACC,YAAoB;IAEvD,IAAMC,eAAe,GAAGH,0BAA0B,CAAIE,YAAY,qBAAkB,CAAC;IACrF,IAAME,eAAe,GAAGJ,0BAA0B,CAAIE,YAAY,qBAAkB,CAAC;IACrF,IAAMG,cAAc,GAAGN,iBAAiB,CAAIG,YAAY,oBAAiB,CAAC;IAE1E,OAAO;MAAEC,eAAe,EAAAA,eAAA;MAAEC,eAAe,EAAAA,eAAA;MAAEC,cAAc,EAAAA;IAAA,CAAE;EAC7D;EAEA,SAASC,sBAAsBA,CAACJ,YAAoB;IAElD,IAAMC,eAAe,GAAGH,0BAA0B,CAAIE,YAAY,qBAAkB,CAAC;IACrF,IAAME,eAAe,GAAGJ,0BAA0B,CAAIE,YAAY,qBAAkB,CAAC;IACrF,IAAMK,eAAe,GAAGP,0BAA0B,CAAIE,YAAY,qBAAkB,CAAC;IAErF,OAAO;MAAEC,eAAe,EAAAA,eAAA;MAAEC,eAAe,EAAAA,eAAA;MAAEG,eAAe,EAAAA;IAAA,CAAE;EAC9D;EAEA,OAAO;IACLR,iBAAiB,EAAAA,iBAAA;IACjBC,0BAA0B,EAAAA,0BAAA;IAC1BC,2BAA2B,EAAAA,2BAAA;IAC3BK,sBAAsB,EAAAA;GACvB;AACH;AAEA,OAAM,SAAUE,0BAA0BA,CACxCZ,SAA4B,EAC5Ba,aAAqB;EAGrB,IAAMZ,aAAa,GAAmB,EAAE;EAElC,IAAAa,EAAA,GAAAf,iBAAA,CAAAC,SAAA,EAAAC,aAAA,CAKyC;IAJ7CE,iBAAA,GAAAW,EAAA,CAAAX,iBAAiB;IACjBC,0BAAA,GAAAU,EAAA,CAAAV,0BAA0B;IAC1BC,2BAAA,GAAAS,EAAA,CAAAT,2BAA2B;IAC3BK,sBAAA,GAAAI,EAAA,CAAAJ,sBAC6C;EAE/C,IAAMK,kBAAkB,GAAGZ,iBAAiB,CAAC,oBAAoB,CAAC;EAClE,IAAMa,4BAA4B,GAAGX,2BAA2B,CAAC,8BAA8B,CAAC;EAChG,IAAMY,4BAA4B,GAAGZ,2BAA2B,CAAC,8BAA8B,CAAC;EAEhG,IAAMa,UAAU,GAAG;IACjBC,OAAO,EAAEJ,kBAAkB;IAC3BK,iBAAiB,EAAEJ,4BAA4B;IAC/CK,iBAAiB,EAAEJ;GACpB;EAED,IAAMK,WAAW,GAAG,EAAE;EACtBxB,KAAK,CAACe,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,OAAO,CAAC,UAACC,GAAG;IACrCF,WAAW,CAAC,gBAAcE,GAAK,CAAC,GAAGd,sBAAsB,CAAC,4BAA0Bc,GAAK,CAAC;EAC5F,CAAC,CAAC;EAEF,IAAMC,yBAAyB,GAAGpB,2BAA2B,CAAC,2BAA2B,CAAC;EAC1F,IAAMqB,wBAAwB,GAAGtB,0BAA0B,CAAC,0BAA0B,CAAC;EAEvF,IAAMuB,SAAS,GAAG;IAChBC,eAAe,EAAEH,yBAAyB;IAC1CI,cAAc,EAAEH;GACjB;EAEDhC,0BAA0B,CAACM,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAE6B,MAAM,EAAE;MAAEZ,UAAU,EAAAA,UAAA;MAAEI,WAAW,EAAAA,WAAA;MAAEK,SAAS,EAAAA;IAAA,CAAE;IAAE1B,aAAa,EAAAA;EAAA,CAAE;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}