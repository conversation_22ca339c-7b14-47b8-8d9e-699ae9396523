{"ast": null, "code": "var Point = /** @class */function () {\n  function Point(x, y) {\n    this._x = x;\n    this._y = y;\n  }\n  Object.defineProperty(Point.prototype, \"x\", {\n    get: function () {\n      return this._x;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Point.prototype, \"y\", {\n    get: function () {\n      return this._y;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Point.prototype.add = function (pt) {\n    return new Point(this.x + pt.x, this.y + pt.y);\n  };\n  Point.prototype.sub = function (pt) {\n    return new Point(this.x - pt.x, this.y - pt.y);\n  };\n  Point.prototype.mul = function (pt) {\n    return new Point(this.x * pt.x, this.y * pt.y);\n  };\n  Point.prototype.div = function (pt) {\n    return new Point(this.x / pt.x, this.y / pt.y);\n  };\n  Point.prototype.abs = function () {\n    return new Point(Math.abs(this.x), Math.abs(this.y));\n  };\n  Point.prototype.magnitude = function () {\n    return Math.sqrt(Math.pow(this.x, 2) + Math.pow(this.y, 2));\n  };\n  Point.prototype.floor = function () {\n    return new Point(Math.floor(this.x), Math.floor(this.y));\n  };\n  return Point;\n}();\nexport { Point };", "map": {"version": 3, "names": ["Point", "x", "y", "_x", "_y", "Object", "defineProperty", "prototype", "get", "add", "pt", "sub", "mul", "div", "abs", "Math", "magnitude", "sqrt", "pow", "floor"], "sources": ["../../../src/classes/Point.ts"], "sourcesContent": [null], "mappings": "AAKA,IAAAA,KAAA;EAIE,SAAAA,MAAYC,CAAS,EAAEC,CAAS;IAC9B,IAAI,CAACC,EAAE,GAAGF,CAAC;IACX,IAAI,CAACG,EAAE,GAAGF,CAAC;EACb;EAEAG,MAAA,CAAAC,cAAA,CAAIN,KAAA,CAAAO,SAAA,KAAC;SAAL,SAAAC,CAAA;MAAkB,OAAO,IAAI,CAACL,EAAE;IAAC,CAAC;;;;EAClCE,MAAA,CAAAC,cAAA,CAAIN,KAAA,CAAAO,SAAA,KAAC;SAAL,SAAAC,CAAA;MAAkB,OAAO,IAAI,CAACJ,EAAE;IAAC,CAAC;;;;EAE3BJ,KAAA,CAAAO,SAAA,CAAAE,GAAG,GAAV,UAAWC,EAAU;IACnB,OAAO,IAAIV,KAAK,CAAC,IAAI,CAACC,CAAC,GAAGS,EAAE,CAACT,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGQ,EAAE,CAACR,CAAC,CAAC;EAChD,CAAC;EAEMF,KAAA,CAAAO,SAAA,CAAAI,GAAG,GAAV,UAAWD,EAAU;IACnB,OAAO,IAAIV,KAAK,CAAC,IAAI,CAACC,CAAC,GAAGS,EAAE,CAACT,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGQ,EAAE,CAACR,CAAC,CAAC;EAChD,CAAC;EAEMF,KAAA,CAAAO,SAAA,CAAAK,GAAG,GAAV,UAAWF,EAAU;IACnB,OAAO,IAAIV,KAAK,CAAC,IAAI,CAACC,CAAC,GAAGS,EAAE,CAACT,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGQ,EAAE,CAACR,CAAC,CAAC;EAChD,CAAC;EAEMF,KAAA,CAAAO,SAAA,CAAAM,GAAG,GAAV,UAAWH,EAAU;IACnB,OAAO,IAAIV,KAAK,CAAC,IAAI,CAACC,CAAC,GAAGS,EAAE,CAACT,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGQ,EAAE,CAACR,CAAC,CAAC;EAChD,CAAC;EAEMF,KAAA,CAAAO,SAAA,CAAAO,GAAG,GAAV;IACE,OAAO,IAAId,KAAK,CAACe,IAAI,CAACD,GAAG,CAAC,IAAI,CAACb,CAAC,CAAC,EAAEc,IAAI,CAACD,GAAG,CAAC,IAAI,CAACZ,CAAC,CAAC,CAAC;EACtD,CAAC;EAEMF,KAAA,CAAAO,SAAA,CAAAS,SAAS,GAAhB;IACE,OAAOD,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAAC,IAAI,CAACjB,CAAC,EAAE,CAAC,CAAC,GAAGc,IAAI,CAACG,GAAG,CAAC,IAAI,CAAChB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7D,CAAC;EAEMF,KAAA,CAAAO,SAAA,CAAAY,KAAK,GAAZ;IACE,OAAO,IAAInB,KAAK,CAACe,IAAI,CAACI,KAAK,CAAC,IAAI,CAAClB,CAAC,CAAC,EAAEc,IAAI,CAACI,KAAK,CAAC,IAAI,CAACjB,CAAC,CAAC,CAAC;EAC1D,CAAC;EACH,OAAAF,KAAC;AAAD,CAAC,CAvCD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}