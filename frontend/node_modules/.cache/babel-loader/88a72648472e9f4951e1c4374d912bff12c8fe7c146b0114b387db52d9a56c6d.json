{"ast": null, "code": "export function isBrowser() {\n  return typeof window === 'object' && typeof document !== 'undefined' && typeof HTMLImageElement !== 'undefined' && typeof HTMLCanvasElement !== 'undefined' && typeof HTMLVideoElement !== 'undefined' && typeof ImageData !== 'undefined' && typeof CanvasRenderingContext2D !== 'undefined';\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "HTMLImageElement", "HTMLCanvasElement", "HTMLVideoElement", "ImageData", "CanvasRenderingContext2D"], "sources": ["../../../src/env/isBrowser.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,SAASA,CAAA;EACvB,OAAO,OAAOC,MAAM,KAAK,QAAQ,IAC5B,OAAOC,QAAQ,KAAK,WAAW,IAC/B,OAAOC,gBAAgB,KAAK,WAAW,IACvC,OAAOC,iBAAiB,KAAK,WAAW,IACxC,OAAOC,gBAAgB,KAAK,WAAW,IACvC,OAAOC,SAAS,KAAK,WAAW,IAChC,OAAOC,wBAAwB,KAAK,WAAW;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}