{"ast": null, "code": "import { iou } from './iou';\nexport function nonMaxSuppression(boxes, scores, iouThreshold, isIOU) {\n  if (isIOU === void 0) {\n    isIOU = true;\n  }\n  var indicesSortedByScore = scores.map(function (score, boxIndex) {\n    return {\n      score: score,\n      boxIndex: boxIndex\n    };\n  }).sort(function (c1, c2) {\n    return c1.score - c2.score;\n  }).map(function (c) {\n    return c.boxIndex;\n  });\n  var pick = [];\n  var _loop_1 = function () {\n    var curr = indicesSortedByScore.pop();\n    pick.push(curr);\n    var indices = indicesSortedByScore;\n    var outputs = [];\n    for (var i = 0; i < indices.length; i++) {\n      var idx = indices[i];\n      var currBox = boxes[curr];\n      var idxBox = boxes[idx];\n      outputs.push(iou(currBox, idxBox, isIOU));\n    }\n    indicesSortedByScore = indicesSortedByScore.filter(function (_, j) {\n      return outputs[j] <= iouThreshold;\n    });\n  };\n  while (indicesSortedByScore.length > 0) {\n    _loop_1();\n  }\n  return pick;\n}", "map": {"version": 3, "names": ["iou", "nonMaxSuppression", "boxes", "scores", "iouThreshold", "isIOU", "indicesSortedByScore", "map", "score", "boxIndex", "sort", "c1", "c2", "c", "pick", "curr", "pop", "push", "indices", "outputs", "i", "length", "idx", "currBox", "idxBox", "filter", "_", "j"], "sources": ["../../../src/ops/nonMaxSuppression.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,GAAG,QAAQ,OAAO;AAE3B,OAAM,SAAUC,iBAAiBA,CAC/BC,KAAY,EACZC,MAAgB,EAChBC,YAAoB,EACpBC,KAAqB;EAArB,IAAAA,KAAA;IAAAA,KAAA,OAAqB;EAAA;EAGrB,IAAIC,oBAAoB,GAAGH,MAAM,CAC9BI,GAAG,CAAC,UAACC,KAAK,EAAEC,QAAQ;IAAK,OAAC;MAAED,KAAK,EAAAA,KAAA;MAAEC,QAAQ,EAAAA;IAAA,CAAE;EAApB,CAAqB,CAAC,CAC/CC,IAAI,CAAC,UAACC,EAAE,EAAEC,EAAE;IAAK,OAAAD,EAAE,CAACH,KAAK,GAAGI,EAAE,CAACJ,KAAK;EAAnB,CAAmB,CAAC,CACrCD,GAAG,CAAC,UAAAM,CAAC;IAAI,OAAAA,CAAC,CAACJ,QAAQ;EAAV,CAAU,CAAC;EAEvB,IAAMK,IAAI,GAAa,EAAE;;IAGvB,IAAMC,IAAI,GAAGT,oBAAoB,CAACU,GAAG,EAAY;IACjDF,IAAI,CAACG,IAAI,CAACF,IAAI,CAAC;IAEf,IAAMG,OAAO,GAAGZ,oBAAoB;IAEpC,IAAMa,OAAO,GAAa,EAAE;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAME,GAAG,GAAGJ,OAAO,CAACE,CAAC,CAAC;MAEtB,IAAMG,OAAO,GAAGrB,KAAK,CAACa,IAAI,CAAC;MAC3B,IAAMS,MAAM,GAAGtB,KAAK,CAACoB,GAAG,CAAC;MAEzBH,OAAO,CAACF,IAAI,CAACjB,GAAG,CAACuB,OAAO,EAAEC,MAAM,EAAEnB,KAAK,CAAC,CAAC;;IAG3CC,oBAAoB,GAAGA,oBAAoB,CAACmB,MAAM,CAChD,UAACC,CAAC,EAAEC,CAAC;MAAK,OAAAR,OAAO,CAACQ,CAAC,CAAC,IAAIvB,YAAY;IAA1B,CAA0B,CACrC;;EAlBH,OAAME,oBAAoB,CAACe,MAAM,GAAG,CAAC;;;EAqBrC,OAAOP,IAAI;AAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}