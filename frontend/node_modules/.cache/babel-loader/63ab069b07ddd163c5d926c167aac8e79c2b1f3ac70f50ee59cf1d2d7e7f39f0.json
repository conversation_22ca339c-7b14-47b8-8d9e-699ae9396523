{"ast": null, "code": "import { Dimensions } from './classes';\nimport { FaceDetection } from './classes/FaceDetection';\nimport { FaceLandmarks } from './classes/FaceLandmarks';\nimport { extendWithFaceDetection, isWithFaceDetection } from './factories/WithFaceDetection';\nimport { extendWithFaceLandmarks, isWithFaceLandmarks } from './factories/WithFaceLandmarks';\nexport function resizeResults(results, dimensions) {\n  var _a = new Dimensions(dimensions.width, dimensions.height),\n    width = _a.width,\n    height = _a.height;\n  if (width <= 0 || height <= 0) {\n    throw new Error(\"resizeResults - invalid dimensions: \" + JSON.stringify({\n      width: width,\n      height: height\n    }));\n  }\n  if (Array.isArray(results)) {\n    return results.map(function (obj) {\n      return resizeResults(obj, {\n        width: width,\n        height: height\n      });\n    });\n  }\n  if (isWithFaceLandmarks(results)) {\n    var resizedDetection = results.detection.forSize(width, height);\n    var resizedLandmarks = results.unshiftedLandmarks.forSize(resizedDetection.box.width, resizedDetection.box.height);\n    return extendWithFaceLandmarks(extendWithFaceDetection(results, resizedDetection), resizedLandmarks);\n  }\n  if (isWithFaceDetection(results)) {\n    return extendWithFaceDetection(results, results.detection.forSize(width, height));\n  }\n  if (results instanceof FaceLandmarks || results instanceof FaceDetection) {\n    return results.forSize(width, height);\n  }\n  return results;\n}", "map": {"version": 3, "names": ["Dimensions", "FaceDetection", "FaceLandmarks", "extendWithFaceDetection", "isWithFaceDetection", "extendWithFaceLandmarks", "isWithFaceLandmarks", "resizeResults", "results", "dimensions", "_a", "width", "height", "Error", "JSON", "stringify", "Array", "isArray", "map", "obj", "resizedDetection", "detection", "forSize", "resizedLandmarks", "unshiftedLandmarks", "box"], "sources": ["../../src/resizeResults.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,UAAU,QAAqB,WAAW;AACnD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,uBAAuB,EAAEC,mBAAmB,QAAQ,+BAA+B;AAC5F,SAASC,uBAAuB,EAAEC,mBAAmB,QAAQ,+BAA+B;AAE5F,OAAM,SAAUC,aAAaA,CAAIC,OAAU,EAAEC,UAAuB;EAE5D,IAAAC,EAAA,OAAAV,UAAA,CAAAS,UAAA,CAAAE,KAAA,EAAAF,UAAA,CAAAG,MAAA,CAAuE;IAArED,KAAA,GAAAD,EAAA,CAAAC,KAAK;IAAEC,MAAA,GAAAF,EAAA,CAAAE,MAA8D;EAE7E,IAAID,KAAK,IAAI,CAAC,IAAIC,MAAM,IAAI,CAAC,EAAE;IAC7B,MAAM,IAAIC,KAAK,CAAC,yCAAuCC,IAAI,CAACC,SAAS,CAAC;MAAEJ,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAAG,CAAC;;EAG7F,IAAII,KAAK,CAACC,OAAO,CAACT,OAAO,CAAC,EAAE;IAC1B,OAAOA,OAAO,CAACU,GAAG,CAAC,UAAAC,GAAG;MAAI,OAAAZ,aAAa,CAACY,GAAG,EAAE;QAAER,KAAK,EAAAA,KAAA;QAAEC,MAAM,EAAAA;MAAA,CAAE,CAAC;IAArC,CAAqC,CAAa;;EAG9E,IAAIN,mBAAmB,CAACE,OAAO,CAAC,EAAE;IAChC,IAAMY,gBAAgB,GAAGZ,OAAO,CAACa,SAAS,CAACC,OAAO,CAACX,KAAK,EAAEC,MAAM,CAAC;IACjE,IAAMW,gBAAgB,GAAGf,OAAO,CAACgB,kBAAkB,CAACF,OAAO,CAACF,gBAAgB,CAACK,GAAG,CAACd,KAAK,EAAES,gBAAgB,CAACK,GAAG,CAACb,MAAM,CAAC;IAEpH,OAAOP,uBAAuB,CAACF,uBAAuB,CAACK,OAAO,EAAEY,gBAAgB,CAAC,EAAEG,gBAAgB,CAAC;;EAGtG,IAAInB,mBAAmB,CAACI,OAAO,CAAC,EAAE;IAChC,OAAOL,uBAAuB,CAACK,OAAO,EAAEA,OAAO,CAACa,SAAS,CAACC,OAAO,CAACX,KAAK,EAAEC,MAAM,CAAC,CAAC;;EAGnF,IAAIJ,OAAO,YAAYN,aAAa,IAAIM,OAAO,YAAYP,aAAa,EAAE;IACxE,OAAQO,OAAe,CAACc,OAAO,CAACX,KAAK,EAAEC,MAAM,CAAC;;EAGhD,OAAOJ,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}