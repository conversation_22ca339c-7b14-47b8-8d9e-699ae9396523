{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { convLayer } from '../common';\nexport function boxPredictionLayer(x, params) {\n  return tf.tidy(function () {\n    var batchSize = x.shape[0];\n    var boxPredictionEncoding = tf.reshape(convLayer(x, params.box_encoding_predictor), [batchSize, -1, 1, 4]);\n    var classPrediction = tf.reshape(convLayer(x, params.class_predictor), [batchSize, -1, 3]);\n    return {\n      boxPredictionEncoding: boxPredictionEncoding,\n      classPrediction: classPrediction\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "convLayer", "boxPredictionLayer", "x", "params", "tidy", "batchSize", "shape", "boxPredictionEncoding", "reshape", "box_encoding_predictor", "classPrediction", "class_predictor"], "sources": ["../../../src/ssdMobilenetv1/boxPredictionLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,SAAS,QAAQ,WAAW;AAIrC,OAAM,SAAUC,kBAAkBA,CAChCC,CAAc,EACdC,MAA2B;EAE3B,OAAOJ,EAAE,CAACK,IAAI,CAAC;IAEb,IAAMC,SAAS,GAAGH,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;IAE5B,IAAMC,qBAAqB,GAAGR,EAAE,CAACS,OAAO,CACtCR,SAAS,CAACE,CAAC,EAAEC,MAAM,CAACM,sBAAsB,CAAC,EAC3C,CAACJ,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACtB;IACD,IAAMK,eAAe,GAAGX,EAAE,CAACS,OAAO,CAChCR,SAAS,CAACE,CAAC,EAAEC,MAAM,CAACQ,eAAe,CAAC,EACpC,CAACN,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CACnB;IAED,OAAO;MACLE,qBAAqB,EAAAA,qBAAA;MACrBG,eAAe,EAAAA;KAChB;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}