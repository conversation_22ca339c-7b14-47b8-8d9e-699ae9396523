{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { Point } from '../classes';\nimport { Dimensions } from '../classes/Dimensions';\nexport function isTensor(tensor, dim) {\n  return tensor instanceof tf.Tensor && tensor.shape.length === dim;\n}\nexport function isTensor1D(tensor) {\n  return isTensor(tensor, 1);\n}\nexport function isTensor2D(tensor) {\n  return isTensor(tensor, 2);\n}\nexport function isTensor3D(tensor) {\n  return isTensor(tensor, 3);\n}\nexport function isTensor4D(tensor) {\n  return isTensor(tensor, 4);\n}\nexport function isFloat(num) {\n  return num % 1 !== 0;\n}\nexport function isEven(num) {\n  return num % 2 === 0;\n}\nexport function round(num, prec) {\n  if (prec === void 0) {\n    prec = 2;\n  }\n  var f = Math.pow(10, prec);\n  return Math.floor(num * f) / f;\n}\nexport function isDimensions(obj) {\n  return obj && obj.width && obj.height;\n}\nexport function computeReshapedDimensions(_a, inputSize) {\n  var width = _a.width,\n    height = _a.height;\n  var scale = inputSize / Math.max(height, width);\n  return new Dimensions(Math.round(width * scale), Math.round(height * scale));\n}\nexport function getCenterPoint(pts) {\n  return pts.reduce(function (sum, pt) {\n    return sum.add(pt);\n  }, new Point(0, 0)).div(new Point(pts.length, pts.length));\n}\nexport function range(num, start, step) {\n  return Array(num).fill(0).map(function (_, i) {\n    return start + i * step;\n  });\n}\nexport function isValidNumber(num) {\n  return !!num && num !== Infinity && num !== -Infinity && !isNaN(num) || num === 0;\n}\nexport function isValidProbablitiy(num) {\n  return isValidNumber(num) && 0 <= num && num <= 1.0;\n}", "map": {"version": 3, "names": ["tf", "Point", "Dimensions", "isTensor", "tensor", "dim", "Tensor", "shape", "length", "isTensor1D", "isTensor2D", "isTensor3D", "isTensor4D", "isFloat", "num", "isEven", "round", "prec", "f", "Math", "pow", "floor", "isDimensions", "obj", "width", "height", "computeReshapedDimensions", "_a", "inputSize", "scale", "max", "getCenterPoint", "pts", "reduce", "sum", "pt", "add", "div", "range", "start", "step", "Array", "fill", "map", "_", "i", "isValidNumber", "Infinity", "isNaN", "isValidProbablitiy"], "sources": ["../../../src/utils/index.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,UAAU,QAAqB,uBAAuB;AAE/D,OAAM,SAAUC,QAAQA,CAACC,MAAW,EAAEC,GAAW;EAC/C,OAAOD,MAAM,YAAYJ,EAAE,CAACM,MAAM,IAAIF,MAAM,CAACG,KAAK,CAACC,MAAM,KAAKH,GAAG;AACnE;AAEA,OAAM,SAAUI,UAAUA,CAACL,MAAW;EACpC,OAAOD,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;AAC5B;AAEA,OAAM,SAAUM,UAAUA,CAACN,MAAW;EACpC,OAAOD,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;AAC5B;AAEA,OAAM,SAAUO,UAAUA,CAACP,MAAW;EACpC,OAAOD,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;AAC5B;AAEA,OAAM,SAAUQ,UAAUA,CAACR,MAAW;EACpC,OAAOD,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;AAC5B;AAEA,OAAM,SAAUS,OAAOA,CAACC,GAAW;EACjC,OAAOA,GAAG,GAAG,CAAC,KAAK,CAAC;AACtB;AAEA,OAAM,SAAUC,MAAMA,CAACD,GAAW;EAChC,OAAOA,GAAG,GAAG,CAAC,KAAK,CAAC;AACtB;AAEA,OAAM,SAAUE,KAAKA,CAACF,GAAW,EAAEG,IAAgB;EAAhB,IAAAA,IAAA;IAAAA,IAAA,IAAgB;EAAA;EACjD,IAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEH,IAAI,CAAC;EAC5B,OAAOE,IAAI,CAACE,KAAK,CAACP,GAAG,GAAGI,CAAC,CAAC,GAAGA,CAAC;AAChC;AAEA,OAAM,SAAUI,YAAYA,CAACC,GAAQ;EACnC,OAAOA,GAAG,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,MAAM;AACvC;AAEA,OAAM,SAAUC,yBAAyBA,CAACC,EAA8B,EAAEC,SAAiB;MAA/CJ,KAAA,GAAAG,EAAA,CAAAH,KAAK;IAAEC,MAAA,GAAAE,EAAA,CAAAF,MAAM;EACvD,IAAMI,KAAK,GAAGD,SAAS,GAAGT,IAAI,CAACW,GAAG,CAACL,MAAM,EAAED,KAAK,CAAC;EACjD,OAAO,IAAItB,UAAU,CAACiB,IAAI,CAACH,KAAK,CAACQ,KAAK,GAAGK,KAAK,CAAC,EAAEV,IAAI,CAACH,KAAK,CAACS,MAAM,GAAGI,KAAK,CAAC,CAAC;AAC9E;AAEA,OAAM,SAAUE,cAAcA,CAACC,GAAY;EACzC,OAAOA,GAAG,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,EAAE;IAAK,OAAAD,GAAG,CAACE,GAAG,CAACD,EAAE,CAAC;EAAX,CAAW,EAAE,IAAIlC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACzDoC,GAAG,CAAC,IAAIpC,KAAK,CAAC+B,GAAG,CAACxB,MAAM,EAAEwB,GAAG,CAACxB,MAAM,CAAC,CAAC;AAC3C;AAEA,OAAM,SAAU8B,KAAKA,CAACxB,GAAW,EAAEyB,KAAa,EAAEC,IAAY;EAC5D,OAAOC,KAAK,CAAC3B,GAAG,CAAC,CAAC4B,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;IAAK,OAAAN,KAAK,GAAIM,CAAC,GAAGL,IAAK;EAAlB,CAAkB,CAAC;AAC7D;AAEA,OAAM,SAAUM,aAAaA,CAAChC,GAAQ;EACpC,OAAO,CAAC,CAACA,GAAG,IAAIA,GAAG,KAAKiC,QAAQ,IAAIjC,GAAG,KAAK,CAACiC,QAAQ,IAAI,CAACC,KAAK,CAAClC,GAAG,CAAC,IAAIA,GAAG,KAAK,CAAC;AACnF;AAEA,OAAM,SAAUmC,kBAAkBA,CAACnC,GAAQ;EACzC,OAAOgC,aAAa,CAAChC,GAAG,CAAC,IAAI,CAAC,IAAIA,GAAG,IAAIA,GAAG,IAAI,GAAG;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}