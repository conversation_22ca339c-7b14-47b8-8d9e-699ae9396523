{"ast": null, "code": "import { env } from '../env';\nimport { resolveInput } from './resolveInput';\nexport function getContext2dOrThrow(canvasArg) {\n  var _a = env.getEnv(),\n    Canvas = _a.<PERSON>,\n    CanvasRenderingContext2D = _a.CanvasRenderingContext2D;\n  if (canvasArg instanceof CanvasRenderingContext2D) {\n    return canvasArg;\n  }\n  var canvas = resolveInput(canvasArg);\n  if (!(canvas instanceof Canvas)) {\n    throw new Error('resolveContext2d - expected canvas to be of instance of Canvas');\n  }\n  var ctx = canvas.getContext('2d');\n  if (!ctx) {\n    throw new Error('resolveContext2d - canvas 2d context is null');\n  }\n  return ctx;\n}", "map": {"version": 3, "names": ["env", "resolveInput", "getContext2dOrThrow", "canvasArg", "_a", "getEnv", "<PERSON><PERSON>", "CanvasRenderingContext2D", "canvas", "Error", "ctx", "getContext"], "sources": ["../../../src/dom/getContext2dOrThrow.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAC5B,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,OAAM,SAAUC,mBAAmBA,CAACC,SAAgE;EAE5F,IAAAC,EAAA,GAAAJ,GAAA,CAAAK,MAAA,EAAmD;IAAjDC,MAAA,GAAAF,EAAA,CAAAE,MAAM;IAAEC,wBAAA,GAAAH,EAAA,CAAAG,wBAAyC;EAEzD,IAAIJ,SAAS,YAAYI,wBAAwB,EAAE;IACjD,OAAOJ,SAAS;;EAGlB,IAAMK,MAAM,GAAGP,YAAY,CAACE,SAAS,CAAC;EAEtC,IAAI,EAAEK,MAAM,YAAYF,MAAM,CAAC,EAAE;IAC/B,MAAM,IAAIG,KAAK,CAAC,gEAAgE,CAAC;;EAGnF,IAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EACnC,IAAI,CAACD,GAAG,EAAE;IACR,MAAM,IAAID,KAAK,CAAC,8CAA8C,CAAC;;EAGjE,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}