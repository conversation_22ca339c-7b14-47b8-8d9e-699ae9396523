{"ast": null, "code": "import { Dimensions } from '../classes/Dimensions';\nimport { env } from '../env';\nexport function getMediaDimensions(input) {\n  var _a = env.getEnv(),\n    Image = _a.Image,\n    Video = _a.Video;\n  if (input instanceof Image) {\n    return new Dimensions(input.naturalWidth, input.naturalHeight);\n  }\n  if (input instanceof Video) {\n    return new Dimensions(input.videoWidth, input.videoHeight);\n  }\n  return new Dimensions(input.width, input.height);\n}", "map": {"version": 3, "names": ["Dimensions", "env", "getMediaDimensions", "input", "_a", "getEnv", "Image", "Video", "naturalWidth", "naturalHeight", "videoWidth", "videoHeight", "width", "height"], "sources": ["../../../src/dom/getMediaDimensions.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,UAAU,QAAqB,uBAAuB;AAC/D,SAASC,GAAG,QAAQ,QAAQ;AAE5B,OAAM,SAAUC,kBAAkBA,CAACC,KAA4E;EAEvG,IAAAC,EAAA,GAAAH,GAAA,CAAAI,MAAA,EAA+B;IAA7BC,KAAA,GAAAF,EAAA,CAAAE,KAAK;IAAEC,KAAA,GAAAH,EAAA,CAAAG,KAAsB;EAErC,IAAIJ,KAAK,YAAYG,KAAK,EAAE;IAC1B,OAAO,IAAIN,UAAU,CAACG,KAAK,CAACK,YAAY,EAAEL,KAAK,CAACM,aAAa,CAAC;;EAEhE,IAAIN,KAAK,YAAYI,KAAK,EAAE;IAC1B,OAAO,IAAIP,UAAU,CAACG,KAAK,CAACO,UAAU,EAAEP,KAAK,CAACQ,WAAW,CAAC;;EAE5D,OAAO,IAAIX,UAAU,CAACG,KAAK,CAACS,KAAK,EAAET,KAAK,CAACU,MAAM,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}