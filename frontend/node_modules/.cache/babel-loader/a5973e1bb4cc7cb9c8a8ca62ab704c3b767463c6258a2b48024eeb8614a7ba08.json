{"ast": null, "code": "import { Box } from './Box';\nimport { Dimensions } from './Dimensions';\nvar ObjectDetection = /** @class */function () {\n  function ObjectDetection(score, classScore, className, relativeBox, imageDims) {\n    this._imageDims = new Dimensions(imageDims.width, imageDims.height);\n    this._score = score;\n    this._classScore = classScore;\n    this._className = className;\n    this._box = new Box(relativeBox).rescale(this._imageDims);\n  }\n  Object.defineProperty(ObjectDetection.prototype, \"score\", {\n    get: function () {\n      return this._score;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"classScore\", {\n    get: function () {\n      return this._classScore;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"className\", {\n    get: function () {\n      return this._className;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"box\", {\n    get: function () {\n      return this._box;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"imageDims\", {\n    get: function () {\n      return this._imageDims;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"imageWidth\", {\n    get: function () {\n      return this.imageDims.width;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"imageHeight\", {\n    get: function () {\n      return this.imageDims.height;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(ObjectDetection.prototype, \"relativeBox\", {\n    get: function () {\n      return new Box(this._box).rescale(this.imageDims.reverse());\n    },\n    enumerable: true,\n    configurable: true\n  });\n  ObjectDetection.prototype.forSize = function (width, height) {\n    return new ObjectDetection(this.score, this.classScore, this.className, this.relativeBox, {\n      width: width,\n      height: height\n    });\n  };\n  return ObjectDetection;\n}();\nexport { ObjectDetection };", "map": {"version": 3, "names": ["Box", "Dimensions", "ObjectDetection", "score", "classScore", "className", "relativeBox", "imageDims", "_imageDims", "width", "height", "_score", "_classScore", "_className", "_box", "rescale", "Object", "defineProperty", "prototype", "get", "reverse", "forSize"], "sources": ["../../../src/classes/ObjectDetection.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,SAASC,UAAU,QAAqB,cAAc;AAGtD,IAAAC,eAAA;EAOE,SAAAA,gBACEC,KAAa,EACbC,UAAkB,EAClBC,SAAiB,EACjBC,WAAkB,EAClBC,SAAsB;IAEtB,IAAI,CAACC,UAAU,GAAG,IAAIP,UAAU,CAACM,SAAS,CAACE,KAAK,EAAEF,SAAS,CAACG,MAAM,CAAC;IACnE,IAAI,CAACC,MAAM,GAAGR,KAAK;IACnB,IAAI,CAACS,WAAW,GAAGR,UAAU;IAC7B,IAAI,CAACS,UAAU,GAAGR,SAAS;IAC3B,IAAI,CAACS,IAAI,GAAG,IAAId,GAAG,CAACM,WAAW,CAAC,CAACS,OAAO,CAAC,IAAI,CAACP,UAAU,CAAC;EAC3D;EAEAQ,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACR,MAAM;IAAC,CAAC;;;;EACjDK,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,cAAU;SAArB,SAAAC,CAAA;MAAkC,OAAO,IAAI,CAACP,WAAW;IAAC,CAAC;;;;EAC3DI,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,aAAS;SAApB,SAAAC,CAAA;MAAiC,OAAO,IAAI,CAACN,UAAU;IAAC,CAAC;;;;EACzDG,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,OAAG;SAAd,SAAAC,CAAA;MAAwB,OAAO,IAAI,CAACL,IAAI;IAAC,CAAC;;;;EAC1CE,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,aAAS;SAApB,SAAAC,CAAA;MAAqC,OAAO,IAAI,CAACX,UAAU;IAAC,CAAC;;;;EAC7DQ,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,cAAU;SAArB,SAAAC,CAAA;MAAkC,OAAO,IAAI,CAACZ,SAAS,CAACE,KAAK;IAAC,CAAC;;;;EAC/DO,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,eAAW;SAAtB,SAAAC,CAAA;MAAmC,OAAO,IAAI,CAACZ,SAAS,CAACG,MAAM;IAAC,CAAC;;;;EACjEM,MAAA,CAAAC,cAAA,CAAWf,eAAA,CAAAgB,SAAA,eAAW;SAAtB,SAAAC,CAAA;MAAgC,OAAO,IAAInB,GAAG,CAAC,IAAI,CAACc,IAAI,CAAC,CAACC,OAAO,CAAC,IAAI,CAACR,SAAS,CAACa,OAAO,EAAE,CAAC;IAAC,CAAC;;;;EAEtFlB,eAAA,CAAAgB,SAAA,CAAAG,OAAO,GAAd,UAAeZ,KAAa,EAAEC,MAAc;IAC1C,OAAO,IAAIR,eAAe,CACxB,IAAI,CAACC,KAAK,EACV,IAAI,CAACC,UAAU,EACf,IAAI,CAACC,SAAS,EACd,IAAI,CAACC,WAAW,EAChB;MAAEG,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAC,CACjB;EACH,CAAC;EACH,OAAAR,eAAC;AAAD,CAAC,CAvCD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}