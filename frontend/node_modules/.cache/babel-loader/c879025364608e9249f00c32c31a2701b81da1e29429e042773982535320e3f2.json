{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M15.5 14.5c0-2.8 2.2-5 5-5 .36 0 .71.04 1.05.11L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7L12 21.5l3.5-4.36V14.5z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    d: \"M23 16v-1.34c0-1.47-1.2-2.75-2.66-2.66-1.33.09-2.34 1.16-2.34 2.5V16h-1v6h7v-6h-1zm-1 0h-3v-1.5c0-.8.7-1.5 1.5-1.5s1.5.7 1.5 1.5V16zm-6.5-1.5c0-2.19 1.35-3.99 3.27-4.68C17.29 8.98 14.94 8 12 8c-4.81 0-8.04 2.62-8.47 2.95L12 21.5l3.5-4.36V14.5z\"\n  })]\n}), 'SignalWifi3BarLockSharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "children", "fillOpacity", "d"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@mui/icons-material/esm/SignalWifi3BarLockSharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M15.5 14.5c0-2.8 2.2-5 5-5 .36 0 .71.04 1.05.11L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7L12 21.5l3.5-4.36V14.5z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    d: \"M23 16v-1.34c0-1.47-1.2-2.75-2.66-2.66-1.33.09-2.34 1.16-2.34 2.5V16h-1v6h7v-6h-1zm-1 0h-3v-1.5c0-.8.7-1.5 1.5-1.5s1.5.7 1.5 1.5V16zm-6.5-1.5c0-2.19 1.35-3.99 3.27-4.68C17.29 8.98 14.94 8 12 8c-4.81 0-8.04 2.62-8.47 2.95L12 21.5l3.5-4.36V14.5z\"\n  })]\n}), 'SignalWifi3BarLockSharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,eAAeJ,aAAa,CAAC,aAAaI,KAAK,CAACL,KAAK,CAACM,QAAQ,EAAE;EAC9DC,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAAC,MAAM,EAAE;IACnCK,WAAW,EAAE,IAAI;IACjBC,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAaN,IAAI,CAAC,MAAM,EAAE;IAC5BM,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC,EAAE,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}