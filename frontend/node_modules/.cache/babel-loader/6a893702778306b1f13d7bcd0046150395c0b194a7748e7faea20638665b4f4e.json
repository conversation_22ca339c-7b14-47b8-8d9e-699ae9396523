{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function bgrToRgbTensor(tensor) {\n  return tf.tidy(function () {\n    return tf.stack(tf.unstack(tensor, 3).reverse(), 3);\n  });\n}", "map": {"version": 3, "names": ["tf", "bgrToRgbTensor", "tensor", "tidy", "stack", "unstack", "reverse"], "sources": ["../../../src/mtcnn/bgrToRgbTensor.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,OAAM,SAAUC,cAAcA,CAACC,MAAmB;EAChD,OAAOF,EAAE,CAACG,IAAI,CACZ;IAAM,OAAAH,EAAE,CAACI,KAAK,CAACJ,EAAE,CAACK,OAAO,CAACH,MAAM,EAAE,CAAC,CAAC,CAACI,OAAO,EAAE,EAAE,CAAC,CAAC;EAA5C,CAA4C,CACpC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}