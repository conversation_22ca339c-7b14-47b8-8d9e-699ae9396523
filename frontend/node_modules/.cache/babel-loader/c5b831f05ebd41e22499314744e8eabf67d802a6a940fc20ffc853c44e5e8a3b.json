{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function convLayer(x, params, padding, withRelu) {\n  if (padding === void 0) {\n    padding = 'same';\n  }\n  if (withRelu === void 0) {\n    withRelu = false;\n  }\n  return tf.tidy(function () {\n    var out = tf.add(tf.conv2d(x, params.filters, [1, 1], padding), params.bias);\n    return withRelu ? tf.relu(out) : out;\n  });\n}", "map": {"version": 3, "names": ["tf", "convLayer", "x", "params", "padding", "withRelu", "tidy", "out", "add", "conv2d", "filters", "bias", "relu"], "sources": ["../../../src/common/convLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAI3C,OAAM,SAAUC,SAASA,CACvBC,CAAc,EACdC,MAAkB,EAClBC,OAAkC,EAClCC,QAAyB;EADzB,IAAAD,OAAA;IAAAA,OAAA,SAAkC;EAAA;EAClC,IAAAC,QAAA;IAAAA,QAAA,QAAyB;EAAA;EAEzB,OAAOL,EAAE,CAACM,IAAI,CAAC;IACb,IAAMC,GAAG,GAAGP,EAAE,CAACQ,GAAG,CAChBR,EAAE,CAACS,MAAM,CAACP,CAAC,EAAEC,MAAM,CAACO,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEN,OAAO,CAAC,EAC7CD,MAAM,CAACQ,IAAI,CACG;IAEhB,OAAON,QAAQ,GAAGL,EAAE,CAACY,IAAI,CAACL,GAAG,CAAC,GAAGA,GAAG;EACtC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}