{"ast": null, "code": "import value from \"./value.js\";\nimport numberArray, { isNumberArray } from \"./numberArray.js\";\nexport default function (a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n    na = a ? Math.min(nb, a.length) : 0,\n    x = new Array(na),\n    c = new Array(nb),\n    i;\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n  return function (t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}", "map": {"version": 3, "names": ["value", "numberArray", "isNumberArray", "a", "b", "genericArray", "nb", "length", "na", "Math", "min", "x", "Array", "c", "i", "t"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/d3-interpolate/src/array.js"], "sourcesContent": ["import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,OAAOC,WAAW,IAAGC,aAAa,QAAO,kBAAkB;AAE3D,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAO,CAACF,aAAa,CAACE,CAAC,CAAC,GAAGH,WAAW,GAAGI,YAAY,EAAEF,CAAC,EAAEC,CAAC,CAAC;AAC9D;AAEA,OAAO,SAASC,YAAYA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAIE,EAAE,GAAGF,CAAC,GAAGA,CAAC,CAACG,MAAM,GAAG,CAAC;IACrBC,EAAE,GAAGL,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACJ,EAAE,EAAEH,CAAC,CAACI,MAAM,CAAC,GAAG,CAAC;IACnCI,CAAC,GAAG,IAAIC,KAAK,CAACJ,EAAE,CAAC;IACjBK,CAAC,GAAG,IAAID,KAAK,CAACN,EAAE,CAAC;IACjBQ,CAAC;EAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,EAAE,EAAE,EAAEM,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,GAAGd,KAAK,CAACG,CAAC,CAACW,CAAC,CAAC,EAAEV,CAAC,CAACU,CAAC,CAAC,CAAC;EACjD,OAAOA,CAAC,GAAGR,EAAE,EAAE,EAAEQ,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGV,CAAC,CAACU,CAAC,CAAC;EAE/B,OAAO,UAASC,CAAC,EAAE;IACjB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,EAAE,EAAE,EAAEM,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,CAACC,CAAC,CAAC;IACvC,OAAOF,CAAC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}