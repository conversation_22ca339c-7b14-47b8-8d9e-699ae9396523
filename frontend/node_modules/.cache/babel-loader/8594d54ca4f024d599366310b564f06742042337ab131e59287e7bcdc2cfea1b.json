{"ast": null, "code": "export * from './BoundingBox';\nexport * from './Box';\nexport * from './Dimensions';\nexport * from './FaceDetection';\nexport * from './FaceLandmarks';\nexport * from './FaceLandmarks5';\nexport * from './FaceLandmarks68';\nexport * from './FaceMatch';\nexport * from './LabeledBox';\nexport * from './LabeledFaceDescriptors';\nexport * from './ObjectDetection';\nexport * from './Point';\nexport * from './PredictedBox';\nexport * from './Rect';", "map": {"version": 3, "names": [], "sources": ["../../../src/classes/index.ts"], "sourcesContent": [null], "mappings": "AAAA,cAAc,eAAe;AAC7B,cAAc,OAAO;AACrB,cAAc,cAAc;AAC5B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,kBAAkB;AAChC,cAAc,mBAAmB;AACjC,cAAc,aAAa;AAC3B,cAAc,cAAc;AAC5B,cAAc,0BAA0B;AACxC,cAAc,mBAAmB;AACjC,cAAc,SAAS;AACvB,cAAc,gBAAgB;AAC9B,cAAc,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}