{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { toNetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { normalize } from '../ops';\nimport { convDown } from './convLayer';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nimport { residual, residualDown } from './residualLayer';\nvar FaceRecognitionNet = /** @class */function (_super) {\n  __extends(FaceRecognitionNet, _super);\n  function FaceRecognitionNet() {\n    return _super.call(this, 'FaceRecognitionNet') || this;\n  }\n  FaceRecognitionNet.prototype.forwardInput = function (input) {\n    var params = this.params;\n    if (!params) {\n      throw new Error('FaceRecognitionNet - load model before inference');\n    }\n    return tf.tidy(function () {\n      var batchTensor = input.toBatchTensor(150, true).toFloat();\n      var meanRgb = [122.782, 117.001, 104.298];\n      var normalized = normalize(batchTensor, meanRgb).div(tf.scalar(256));\n      var out = convDown(normalized, params.conv32_down);\n      out = tf.maxPool(out, 3, 2, 'valid');\n      out = residual(out, params.conv32_1);\n      out = residual(out, params.conv32_2);\n      out = residual(out, params.conv32_3);\n      out = residualDown(out, params.conv64_down);\n      out = residual(out, params.conv64_1);\n      out = residual(out, params.conv64_2);\n      out = residual(out, params.conv64_3);\n      out = residualDown(out, params.conv128_down);\n      out = residual(out, params.conv128_1);\n      out = residual(out, params.conv128_2);\n      out = residualDown(out, params.conv256_down);\n      out = residual(out, params.conv256_1);\n      out = residual(out, params.conv256_2);\n      out = residualDown(out, params.conv256_down_out);\n      var globalAvg = out.mean([1, 2]);\n      var fullyConnected = tf.matMul(globalAvg, params.fc);\n      return fullyConnected;\n    });\n  };\n  FaceRecognitionNet.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  FaceRecognitionNet.prototype.computeFaceDescriptor = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var netInput, faceDescriptorTensors, faceDescriptorsForBatch;\n      var _this = this;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            netInput = _a.sent();\n            faceDescriptorTensors = tf.tidy(function () {\n              return tf.unstack(_this.forwardInput(netInput));\n            });\n            return [4 /*yield*/, Promise.all(faceDescriptorTensors.map(function (t) {\n              return t.data();\n            }))];\n          case 2:\n            faceDescriptorsForBatch = _a.sent();\n            faceDescriptorTensors.forEach(function (t) {\n              return t.dispose();\n            });\n            return [2 /*return*/, netInput.isBatchInput ? faceDescriptorsForBatch : faceDescriptorsForBatch[0]];\n        }\n      });\n    });\n  };\n  FaceRecognitionNet.prototype.getDefaultModelName = function () {\n    return 'face_recognition_model';\n  };\n  FaceRecognitionNet.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMap(weightMap);\n  };\n  FaceRecognitionNet.prototype.extractParams = function (weights) {\n    return extractParams(weights);\n  };\n  return FaceRecognitionNet;\n}(NeuralNetwork);\nexport { FaceRecognitionNet };", "map": {"version": 3, "names": ["tf", "toNetInput", "NeuralNetwork", "normalize", "convDown", "extractParams", "extractParamsFromWeigthMap", "residual", "residualDown", "FaceRecognitionNet", "_super", "__extends", "call", "prototype", "forwardInput", "input", "params", "Error", "tidy", "batchTensor", "toBatchTensor", "toFloat", "meanRgb", "normalized", "div", "scalar", "out", "conv32_down", "maxPool", "conv32_1", "conv32_2", "conv32_3", "conv64_down", "conv64_1", "conv64_2", "conv64_3", "conv128_down", "conv128_1", "conv128_2", "conv256_down", "conv256_1", "conv256_2", "conv256_down_out", "globalAvg", "mean", "fullyConnected", "<PERSON><PERSON><PERSON>", "fc", "forward", "_a", "apply", "_b", "sent", "computeFaceDescriptor", "netInput", "faceDescriptorTensors", "unstack", "_this", "Promise", "all", "map", "t", "data", "faceDescriptorsForBatch", "for<PERSON>ach", "dispose", "isBatchInput", "getDefaultModelName", "weightMap", "weights"], "sources": ["../../../src/faceRecognitionNet/FaceRecognitionNet.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAIxD,IAAAC,kBAAA,0BAAAC,MAAA;EAAwCC,SAAA,CAAAF,kBAAA,EAAAC,MAAA;EAEtC,SAAAD,mBAAA;WACEC,MAAA,CAAAE,IAAA,OAAM,oBAAoB,CAAC;EAC7B;EAEOH,kBAAA,CAAAI,SAAA,CAAAC,YAAY,GAAnB,UAAoBC,KAAe;IAEzB,IAAAC,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;;IAGrE,OAAOjB,EAAE,CAACkB,IAAI,CAAC;MACb,IAAMC,WAAW,GAAGJ,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,CAACC,OAAO,EAAE;MAE5D,IAAMC,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC3C,IAAMC,UAAU,GAAGpB,SAAS,CAACgB,WAAW,EAAEG,OAAO,CAAC,CAACE,GAAG,CAACxB,EAAE,CAACyB,MAAM,CAAC,GAAG,CAAC,CAAgB;MAErF,IAAIC,GAAG,GAAGtB,QAAQ,CAACmB,UAAU,EAAEP,MAAM,CAACW,WAAW,CAAC;MAClDD,GAAG,GAAG1B,EAAE,CAAC4B,OAAO,CAACF,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC;MAEpCA,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACa,QAAQ,CAAC;MACpCH,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACc,QAAQ,CAAC;MACpCJ,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACe,QAAQ,CAAC;MAEpCL,GAAG,GAAGlB,YAAY,CAACkB,GAAG,EAAEV,MAAM,CAACgB,WAAW,CAAC;MAC3CN,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACiB,QAAQ,CAAC;MACpCP,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACkB,QAAQ,CAAC;MACpCR,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACmB,QAAQ,CAAC;MAEpCT,GAAG,GAAGlB,YAAY,CAACkB,GAAG,EAAEV,MAAM,CAACoB,YAAY,CAAC;MAC5CV,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACqB,SAAS,CAAC;MACrCX,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACsB,SAAS,CAAC;MAErCZ,GAAG,GAAGlB,YAAY,CAACkB,GAAG,EAAEV,MAAM,CAACuB,YAAY,CAAC;MAC5Cb,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACwB,SAAS,CAAC;MACrCd,GAAG,GAAGnB,QAAQ,CAACmB,GAAG,EAAEV,MAAM,CAACyB,SAAS,CAAC;MACrCf,GAAG,GAAGlB,YAAY,CAACkB,GAAG,EAAEV,MAAM,CAAC0B,gBAAgB,CAAC;MAEhD,IAAMC,SAAS,GAAGjB,GAAG,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAgB;MACjD,IAAMC,cAAc,GAAG7C,EAAE,CAAC8C,MAAM,CAACH,SAAS,EAAE3B,MAAM,CAAC+B,EAAE,CAAC;MAEtD,OAAOF,cAAc;IACvB,CAAC,CAAC;EACJ,CAAC;EAEYpC,kBAAA,CAAAI,SAAA,CAAAmC,OAAO,GAApB,UAAqBjC,KAAgB;;;;;;YAC5BkC,EAAA,OAAI,CAACnC,YAAY;YAAC,qBAAMb,UAAU,CAACc,KAAK,CAAC;;YAAhD,sBAAOkC,EAAA,CAAAC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAEY3C,kBAAA,CAAAI,SAAA,CAAAwC,qBAAqB,GAAlC,UAAmCtC,KAAgB;;;;;;;YAChC,qBAAMd,UAAU,CAACc,KAAK,CAAC;;YAAlCuC,QAAQ,GAAGL,EAAA,CAAAG,IAAA,EAAuB;YAElCG,qBAAqB,GAAGvD,EAAE,CAACkB,IAAI,CACnC;cAAM,OAAAlB,EAAE,CAACwD,OAAO,CAACC,KAAI,CAAC3C,YAAY,CAACwC,QAAQ,CAAC,CAAC;YAAvC,CAAuC,CAC9C;YAE+B,qBAAMI,OAAO,CAACC,GAAG,CAACJ,qBAAqB,CAACK,GAAG,CACzE,UAAAC,CAAC;cAAI,OAAAA,CAAC,CAACC,IAAI,EAAE;YAAR,CAAQ,CACd,CAAC;;YAFIC,uBAAuB,GAAGd,EAAA,CAAAG,IAAA,EAEZ;YAEpBG,qBAAqB,CAACS,OAAO,CAAC,UAAAH,CAAC;cAAI,OAAAA,CAAC,CAACI,OAAO,EAAE;YAAX,CAAW,CAAC;YAE/C,sBAAOX,QAAQ,CAACY,YAAY,GACxBH,uBAAuB,GACvBA,uBAAuB,CAAC,CAAC,CAAC;;;;GAC/B;EAEStD,kBAAA,CAAAI,SAAA,CAAAsD,mBAAmB,GAA7B;IACE,OAAO,wBAAwB;EACjC,CAAC;EAES1D,kBAAA,CAAAI,SAAA,CAAAP,0BAA0B,GAApC,UAAqC8D,SAA4B;IAC/D,OAAO9D,0BAA0B,CAAC8D,SAAS,CAAC;EAC9C,CAAC;EAES3D,kBAAA,CAAAI,SAAA,CAAAR,aAAa,GAAvB,UAAwBgE,OAAqB;IAC3C,OAAOhE,aAAa,CAACgE,OAAO,CAAC;EAC/B,CAAC;EACH,OAAA5D,kBAAC;AAAD,CAAC,CAjFuCP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}