{"ast": null, "code": "import { extractConvParamsFactory, extractSeparableConvParamsFactory, extractWeightsFactory } from '../common';\nimport { range } from '../utils';\nfunction extractorsFactory(extractWeights, paramMappings) {\n  var extractConvParams = extractConvParamsFactory(extractWeights, paramMappings);\n  var extractSeparableConvParams = extractSeparableConvParamsFactory(extractWeights, paramMappings);\n  function extractReductionBlockParams(channelsIn, channelsOut, mappedPrefix) {\n    var separable_conv0 = extractSeparableConvParams(channelsIn, channelsOut, mappedPrefix + \"/separable_conv0\");\n    var separable_conv1 = extractSeparableConvParams(channelsOut, channelsOut, mappedPrefix + \"/separable_conv1\");\n    var expansion_conv = extractConvParams(channelsIn, channelsOut, 1, mappedPrefix + \"/expansion_conv\");\n    return {\n      separable_conv0: separable_conv0,\n      separable_conv1: separable_conv1,\n      expansion_conv: expansion_conv\n    };\n  }\n  function extractMainBlockParams(channels, mappedPrefix) {\n    var separable_conv0 = extractSeparableConvParams(channels, channels, mappedPrefix + \"/separable_conv0\");\n    var separable_conv1 = extractSeparableConvParams(channels, channels, mappedPrefix + \"/separable_conv1\");\n    var separable_conv2 = extractSeparableConvParams(channels, channels, mappedPrefix + \"/separable_conv2\");\n    return {\n      separable_conv0: separable_conv0,\n      separable_conv1: separable_conv1,\n      separable_conv2: separable_conv2\n    };\n  }\n  return {\n    extractConvParams: extractConvParams,\n    extractSeparableConvParams: extractSeparableConvParams,\n    extractReductionBlockParams: extractReductionBlockParams,\n    extractMainBlockParams: extractMainBlockParams\n  };\n}\nexport function extractParams(weights, numMainBlocks) {\n  var paramMappings = [];\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var _b = extractorsFactory(extractWeights, paramMappings),\n    extractConvParams = _b.extractConvParams,\n    extractSeparableConvParams = _b.extractSeparableConvParams,\n    extractReductionBlockParams = _b.extractReductionBlockParams,\n    extractMainBlockParams = _b.extractMainBlockParams;\n  var entry_flow_conv_in = extractConvParams(3, 32, 3, 'entry_flow/conv_in');\n  var entry_flow_reduction_block_0 = extractReductionBlockParams(32, 64, 'entry_flow/reduction_block_0');\n  var entry_flow_reduction_block_1 = extractReductionBlockParams(64, 128, 'entry_flow/reduction_block_1');\n  var entry_flow = {\n    conv_in: entry_flow_conv_in,\n    reduction_block_0: entry_flow_reduction_block_0,\n    reduction_block_1: entry_flow_reduction_block_1\n  };\n  var middle_flow = {};\n  range(numMainBlocks, 0, 1).forEach(function (idx) {\n    middle_flow[\"main_block_\" + idx] = extractMainBlockParams(128, \"middle_flow/main_block_\" + idx);\n  });\n  var exit_flow_reduction_block = extractReductionBlockParams(128, 256, 'exit_flow/reduction_block');\n  var exit_flow_separable_conv = extractSeparableConvParams(256, 512, 'exit_flow/separable_conv');\n  var exit_flow = {\n    reduction_block: exit_flow_reduction_block,\n    separable_conv: exit_flow_separable_conv\n  };\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    paramMappings: paramMappings,\n    params: {\n      entry_flow: entry_flow,\n      middle_flow: middle_flow,\n      exit_flow: exit_flow\n    }\n  };\n}", "map": {"version": 3, "names": ["extractConvParamsFactory", "extractSeparableConvParamsFactory", "extractWeightsFactory", "range", "extractorsFactory", "extractWeights", "paramMappings", "extractConvParams", "extractSeparableConvParams", "extractReductionBlockParams", "channelsIn", "channelsOut", "mappedPrefix", "separable_conv0", "separable_conv1", "expansion_conv", "extractMainBlockParams", "channels", "separable_conv2", "extractParams", "weights", "numMainBlocks", "_a", "getRemainingWeights", "_b", "entry_flow_conv_in", "entry_flow_reduction_block_0", "entry_flow_reduction_block_1", "entry_flow", "conv_in", "reduction_block_0", "reduction_block_1", "middle_flow", "for<PERSON>ach", "idx", "exit_flow_reduction_block", "exit_flow_separable_conv", "exit_flow", "reduction_block", "separable_conv", "length", "Error", "params"], "sources": ["../../../src/xception/extractParams.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,wBAAwB,EAAEC,iCAAiC,EAAEC,qBAAqB,QAAQ,WAAW;AAE9G,SAASC,KAAK,QAAQ,UAAU;AAGhC,SAASC,iBAAiBA,CAACC,cAAsC,EAAEC,aAA6B;EAE9F,IAAMC,iBAAiB,GAAGP,wBAAwB,CAACK,cAAc,EAAEC,aAAa,CAAC;EACjF,IAAME,0BAA0B,GAAGP,iCAAiC,CAACI,cAAc,EAAEC,aAAa,CAAC;EAEnG,SAASG,2BAA2BA,CAACC,UAAkB,EAAEC,WAAmB,EAAEC,YAAoB;IAEhG,IAAMC,eAAe,GAAGL,0BAA0B,CAACE,UAAU,EAAEC,WAAW,EAAKC,YAAY,qBAAkB,CAAC;IAC9G,IAAME,eAAe,GAAGN,0BAA0B,CAACG,WAAW,EAAEA,WAAW,EAAKC,YAAY,qBAAkB,CAAC;IAC/G,IAAMG,cAAc,GAAGR,iBAAiB,CAACG,UAAU,EAAEC,WAAW,EAAE,CAAC,EAAKC,YAAY,oBAAiB,CAAC;IAEtG,OAAO;MAAEC,eAAe,EAAAA,eAAA;MAAEC,eAAe,EAAAA,eAAA;MAAEC,cAAc,EAAAA;IAAA,CAAE;EAC7D;EAEA,SAASC,sBAAsBA,CAACC,QAAgB,EAAEL,YAAoB;IAEpE,IAAMC,eAAe,GAAGL,0BAA0B,CAACS,QAAQ,EAAEA,QAAQ,EAAKL,YAAY,qBAAkB,CAAC;IACzG,IAAME,eAAe,GAAGN,0BAA0B,CAACS,QAAQ,EAAEA,QAAQ,EAAKL,YAAY,qBAAkB,CAAC;IACzG,IAAMM,eAAe,GAAGV,0BAA0B,CAACS,QAAQ,EAAEA,QAAQ,EAAKL,YAAY,qBAAkB,CAAC;IAEzG,OAAO;MAAEC,eAAe,EAAAA,eAAA;MAAEC,eAAe,EAAAA,eAAA;MAAEI,eAAe,EAAAA;IAAA,CAAE;EAC9D;EAEA,OAAO;IACLX,iBAAiB,EAAAA,iBAAA;IACjBC,0BAA0B,EAAAA,0BAAA;IAC1BC,2BAA2B,EAAAA,2BAAA;IAC3BO,sBAAsB,EAAAA;GACvB;AAEH;AAEA,OAAM,SAAUG,aAAaA,CAACC,OAAqB,EAAEC,aAAqB;EAExE,IAAMf,aAAa,GAAmB,EAAE;EAElC,IAAAgB,EAAA,GAAApB,qBAAA,CAAAkB,OAAA,CAG4B;IAFhCf,cAAA,GAAAiB,EAAA,CAAAjB,cAAc;IACdkB,mBAAA,GAAAD,EAAA,CAAAC,mBACgC;EAE5B,IAAAC,EAAA,GAAApB,iBAAA,CAAAC,cAAA,EAAAC,aAAA,CAK8C;IAJlDC,iBAAA,GAAAiB,EAAA,CAAAjB,iBAAiB;IACjBC,0BAAA,GAAAgB,EAAA,CAAAhB,0BAA0B;IAC1BC,2BAAA,GAAAe,EAAA,CAAAf,2BAA2B;IAC3BO,sBAAA,GAAAQ,EAAA,CAAAR,sBACkD;EAEpD,IAAMS,kBAAkB,GAAGlB,iBAAiB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,oBAAoB,CAAC;EAC5E,IAAMmB,4BAA4B,GAAGjB,2BAA2B,CAAC,EAAE,EAAE,EAAE,EAAE,8BAA8B,CAAC;EACxG,IAAMkB,4BAA4B,GAAGlB,2BAA2B,CAAC,EAAE,EAAE,GAAG,EAAE,8BAA8B,CAAC;EAEzG,IAAMmB,UAAU,GAAG;IACjBC,OAAO,EAAEJ,kBAAkB;IAC3BK,iBAAiB,EAAEJ,4BAA4B;IAC/CK,iBAAiB,EAAEJ;GACpB;EAED,IAAMK,WAAW,GAAG,EAAE;EACtB7B,KAAK,CAACkB,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAACY,OAAO,CAAC,UAACC,GAAG;IACrCF,WAAW,CAAC,gBAAcE,GAAK,CAAC,GAAGlB,sBAAsB,CAAC,GAAG,EAAE,4BAA0BkB,GAAK,CAAC;EACjG,CAAC,CAAC;EAEF,IAAMC,yBAAyB,GAAG1B,2BAA2B,CAAC,GAAG,EAAE,GAAG,EAAE,2BAA2B,CAAC;EACpG,IAAM2B,wBAAwB,GAAG5B,0BAA0B,CAAC,GAAG,EAAE,GAAG,EAAE,0BAA0B,CAAC;EAEjG,IAAM6B,SAAS,GAAG;IAChBC,eAAe,EAAEH,yBAAyB;IAC1CI,cAAc,EAAEH;GACjB;EAED,IAAIb,mBAAmB,EAAE,CAACiB,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkClB,mBAAmB,EAAE,CAACiB,MAAQ,CAAC;;EAGnF,OAAO;IACLlC,aAAa,EAAAA,aAAA;IACboC,MAAM,EAAE;MAAEd,UAAU,EAAAA,UAAA;MAAEI,WAAW,EAAAA,WAAA;MAAEK,SAAS,EAAAA;IAAA;GAC7C;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}