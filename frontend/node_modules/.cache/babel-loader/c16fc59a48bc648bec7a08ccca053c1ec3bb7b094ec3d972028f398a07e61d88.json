{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { getModelUris } from './common/getModelUris';\nimport { loadWeightMap } from './dom';\nimport { env } from './env';\nvar NeuralNetwork = /** @class */function () {\n  function NeuralNetwork(_name) {\n    this._name = _name;\n    this._params = undefined;\n    this._paramMappings = [];\n  }\n  Object.defineProperty(NeuralNetwork.prototype, \"params\", {\n    get: function () {\n      return this._params;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NeuralNetwork.prototype, \"paramMappings\", {\n    get: function () {\n      return this._paramMappings;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NeuralNetwork.prototype, \"isLoaded\", {\n    get: function () {\n      return !!this.params;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  NeuralNetwork.prototype.getParamFromPath = function (paramPath) {\n    var _a = this.traversePropertyPath(paramPath),\n      obj = _a.obj,\n      objProp = _a.objProp;\n    return obj[objProp];\n  };\n  NeuralNetwork.prototype.reassignParamFromPath = function (paramPath, tensor) {\n    var _a = this.traversePropertyPath(paramPath),\n      obj = _a.obj,\n      objProp = _a.objProp;\n    obj[objProp].dispose();\n    obj[objProp] = tensor;\n  };\n  NeuralNetwork.prototype.getParamList = function () {\n    var _this = this;\n    return this._paramMappings.map(function (_a) {\n      var paramPath = _a.paramPath;\n      return {\n        path: paramPath,\n        tensor: _this.getParamFromPath(paramPath)\n      };\n    });\n  };\n  NeuralNetwork.prototype.getTrainableParams = function () {\n    return this.getParamList().filter(function (param) {\n      return param.tensor instanceof tf.Variable;\n    });\n  };\n  NeuralNetwork.prototype.getFrozenParams = function () {\n    return this.getParamList().filter(function (param) {\n      return !(param.tensor instanceof tf.Variable);\n    });\n  };\n  NeuralNetwork.prototype.variable = function () {\n    var _this = this;\n    this.getFrozenParams().forEach(function (_a) {\n      var path = _a.path,\n        tensor = _a.tensor;\n      _this.reassignParamFromPath(path, tensor.variable());\n    });\n  };\n  NeuralNetwork.prototype.freeze = function () {\n    var _this = this;\n    this.getTrainableParams().forEach(function (_a) {\n      var path = _a.path,\n        variable = _a.tensor;\n      var tensor = tf.tensor(variable.dataSync());\n      variable.dispose();\n      _this.reassignParamFromPath(path, tensor);\n    });\n  };\n  NeuralNetwork.prototype.dispose = function (throwOnRedispose) {\n    if (throwOnRedispose === void 0) {\n      throwOnRedispose = true;\n    }\n    this.getParamList().forEach(function (param) {\n      if (throwOnRedispose && param.tensor.isDisposed) {\n        throw new Error(\"param tensor has already been disposed for path \" + param.path);\n      }\n      param.tensor.dispose();\n    });\n    this._params = undefined;\n  };\n  NeuralNetwork.prototype.serializeParams = function () {\n    return new Float32Array(this.getParamList().map(function (_a) {\n      var tensor = _a.tensor;\n      return Array.from(tensor.dataSync());\n    }).reduce(function (flat, arr) {\n      return flat.concat(arr);\n    }));\n  };\n  NeuralNetwork.prototype.load = function (weightsOrUrl) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (weightsOrUrl instanceof Float32Array) {\n              this.extractWeights(weightsOrUrl);\n              return [2 /*return*/];\n            }\n            return [4 /*yield*/, this.loadFromUri(weightsOrUrl)];\n          case 1:\n            _a.sent();\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  NeuralNetwork.prototype.loadFromUri = function (uri) {\n    return __awaiter(this, void 0, void 0, function () {\n      var weightMap;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (uri && typeof uri !== 'string') {\n              throw new Error(this._name + \".loadFromUri - expected model uri\");\n            }\n            return [4 /*yield*/, loadWeightMap(uri, this.getDefaultModelName())];\n          case 1:\n            weightMap = _a.sent();\n            this.loadFromWeightMap(weightMap);\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  NeuralNetwork.prototype.loadFromDisk = function (filePath) {\n    return __awaiter(this, void 0, void 0, function () {\n      var readFile, _a, manifestUri, modelBaseUri, fetchWeightsFromDisk, loadWeights, manifest, _b, _c, weightMap;\n      return __generator(this, function (_d) {\n        switch (_d.label) {\n          case 0:\n            if (filePath && typeof filePath !== 'string') {\n              throw new Error(this._name + \".loadFromDisk - expected model file path\");\n            }\n            readFile = env.getEnv().readFile;\n            _a = getModelUris(filePath, this.getDefaultModelName()), manifestUri = _a.manifestUri, modelBaseUri = _a.modelBaseUri;\n            fetchWeightsFromDisk = function (filePaths) {\n              return Promise.all(filePaths.map(function (filePath) {\n                return readFile(filePath).then(function (buf) {\n                  return buf.buffer;\n                });\n              }));\n            };\n            loadWeights = tf.io.weightsLoaderFactory(fetchWeightsFromDisk);\n            _c = (_b = JSON).parse;\n            return [4 /*yield*/, readFile(manifestUri)];\n          case 1:\n            manifest = _c.apply(_b, [_d.sent().toString()]);\n            return [4 /*yield*/, loadWeights(manifest, modelBaseUri)];\n          case 2:\n            weightMap = _d.sent();\n            this.loadFromWeightMap(weightMap);\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  NeuralNetwork.prototype.loadFromWeightMap = function (weightMap) {\n    var _a = this.extractParamsFromWeigthMap(weightMap),\n      paramMappings = _a.paramMappings,\n      params = _a.params;\n    this._paramMappings = paramMappings;\n    this._params = params;\n  };\n  NeuralNetwork.prototype.extractWeights = function (weights) {\n    var _a = this.extractParams(weights),\n      paramMappings = _a.paramMappings,\n      params = _a.params;\n    this._paramMappings = paramMappings;\n    this._params = params;\n  };\n  NeuralNetwork.prototype.traversePropertyPath = function (paramPath) {\n    if (!this.params) {\n      throw new Error(\"traversePropertyPath - model has no loaded params\");\n    }\n    var result = paramPath.split('/').reduce(function (res, objProp) {\n      if (!res.nextObj.hasOwnProperty(objProp)) {\n        throw new Error(\"traversePropertyPath - object does not have property \" + objProp + \", for path \" + paramPath);\n      }\n      return {\n        obj: res.nextObj,\n        objProp: objProp,\n        nextObj: res.nextObj[objProp]\n      };\n    }, {\n      nextObj: this.params\n    });\n    var obj = result.obj,\n      objProp = result.objProp;\n    if (!obj || !objProp || !(obj[objProp] instanceof tf.Tensor)) {\n      throw new Error(\"traversePropertyPath - parameter is not a tensor, for path \" + paramPath);\n    }\n    return {\n      obj: obj,\n      objProp: objProp\n    };\n  };\n  return NeuralNetwork;\n}();\nexport { NeuralNetwork };", "map": {"version": 3, "names": ["tf", "getModelUris", "loadWeightMap", "env", "NeuralNetwork", "_name", "_params", "undefined", "_paramMappings", "Object", "defineProperty", "prototype", "get", "params", "getParamFromPath", "<PERSON><PERSON><PERSON><PERSON>", "_a", "traversePropertyPath", "obj", "objProp", "reassignParamFromPath", "tensor", "dispose", "getParamList", "_this", "map", "path", "getTrainableParams", "filter", "param", "Variable", "getFrozenParams", "variable", "for<PERSON>ach", "freeze", "dataSync", "throwOnRedispose", "isDisposed", "Error", "serializeParams", "Float32Array", "Array", "from", "reduce", "flat", "arr", "concat", "load", "weightsOrUrl", "extractWeights", "loadFromUri", "sent", "uri", "getDefaultModelName", "weightMap", "loadFromWeightMap", "loadFromDisk", "filePath", "readFile", "getEnv", "manifestUri", "modelBaseUri", "fetchWeightsFromDisk", "filePaths", "Promise", "all", "then", "buf", "buffer", "loadWeights", "io", "weightsLoaderFactory", "_c", "_b", "JSON", "parse", "manifest", "apply", "_d", "toString", "extractParamsFromWeigthMap", "paramMappings", "weights", "extractParams", "result", "split", "res", "nextObj", "hasOwnProperty", "Tensor"], "sources": ["../../src/NeuralNetwork.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,aAAa,QAAQ,OAAO;AACrC,SAASC,GAAG,QAAQ,OAAO;AAE3B,IAAAC,aAAA;EAKE,SAAAA,cAAsBC,KAAa;IAAb,KAAAA,KAAK,GAALA,KAAK;IAHjB,KAAAC,OAAO,GAA2BC,SAAS;IAC3C,KAAAC,cAAc,GAAmB,EAAE;EAEP;EAEtCC,MAAA,CAAAC,cAAA,CAAWN,aAAA,CAAAO,SAAA,UAAM;SAAjB,SAAAC,CAAA;MAA8C,OAAO,IAAI,CAACN,OAAO;IAAC,CAAC;;;;EACnEG,MAAA,CAAAC,cAAA,CAAWN,aAAA,CAAAO,SAAA,iBAAa;SAAxB,SAAAC,CAAA;MAA6C,OAAO,IAAI,CAACJ,cAAc;IAAC,CAAC;;;;EACzEC,MAAA,CAAAC,cAAA,CAAWN,aAAA,CAAAO,SAAA,YAAQ;SAAnB,SAAAC,CAAA;MAAiC,OAAO,CAAC,CAAC,IAAI,CAACC,MAAM;IAAC,CAAC;;;;EAEhDT,aAAA,CAAAO,SAAA,CAAAG,gBAAgB,GAAvB,UAAwBC,SAAiB;IACjC,IAAAC,EAAA,QAAAC,oBAAA,CAAAF,SAAA,CAAuD;MAArDG,GAAA,GAAAF,EAAA,CAAAE,GAAG;MAAEC,OAAA,GAAAH,EAAA,CAAAG,OAAgD;IAC7D,OAAOD,GAAG,CAACC,OAAO,CAAC;EACrB,CAAC;EAEMf,aAAA,CAAAO,SAAA,CAAAS,qBAAqB,GAA5B,UAA6BL,SAAiB,EAAEM,MAAiB;IACzD,IAAAL,EAAA,QAAAC,oBAAA,CAAAF,SAAA,CAAuD;MAArDG,GAAA,GAAAF,EAAA,CAAAE,GAAG;MAAEC,OAAA,GAAAH,EAAA,CAAAG,OAAgD;IAC7DD,GAAG,CAACC,OAAO,CAAC,CAACG,OAAO,EAAE;IACtBJ,GAAG,CAACC,OAAO,CAAC,GAAGE,MAAM;EACvB,CAAC;EAEMjB,aAAA,CAAAO,SAAA,CAAAY,YAAY,GAAnB;IAAA,IAAAC,KAAA;IACE,OAAO,IAAI,CAAChB,cAAc,CAACiB,GAAG,CAAC,UAACT,EAAa;UAAXD,SAAA,GAAAC,EAAA,CAAAD,SAAS;MAAO,OAAC;QACjDW,IAAI,EAAEX,SAAS;QACfM,MAAM,EAAEG,KAAI,CAACV,gBAAgB,CAACC,SAAS;OACxC;IAHiD,CAGhD,CAAC;EACL,CAAC;EAEMX,aAAA,CAAAO,SAAA,CAAAgB,kBAAkB,GAAzB;IACE,OAAO,IAAI,CAACJ,YAAY,EAAE,CAACK,MAAM,CAAC,UAAAC,KAAK;MAAI,OAAAA,KAAK,CAACR,MAAM,YAAYrB,EAAE,CAAC8B,QAAQ;IAAnC,CAAmC,CAAC;EACjF,CAAC;EAEM1B,aAAA,CAAAO,SAAA,CAAAoB,eAAe,GAAtB;IACE,OAAO,IAAI,CAACR,YAAY,EAAE,CAACK,MAAM,CAAC,UAAAC,KAAK;MAAI,SAAEA,KAAK,CAACR,MAAM,YAAYrB,EAAE,CAAC8B,QAAQ,CAAC;IAAtC,CAAsC,CAAC;EACpF,CAAC;EAEM1B,aAAA,CAAAO,SAAA,CAAAqB,QAAQ,GAAf;IAAA,IAAAR,KAAA;IACE,IAAI,CAACO,eAAe,EAAE,CAACE,OAAO,CAAC,UAACjB,EAAgB;UAAdU,IAAA,GAAAV,EAAA,CAAAU,IAAI;QAAEL,MAAA,GAAAL,EAAA,CAAAK,MAAM;MAC5CG,KAAI,CAACJ,qBAAqB,CAACM,IAAI,EAAEL,MAAM,CAACW,QAAQ,EAAE,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;EAEM5B,aAAA,CAAAO,SAAA,CAAAuB,MAAM,GAAb;IAAA,IAAAV,KAAA;IACE,IAAI,CAACG,kBAAkB,EAAE,CAACM,OAAO,CAAC,UAACjB,EAA0B;UAAxBU,IAAA,GAAAV,EAAA,CAAAU,IAAI;QAAEM,QAAA,GAAAhB,EAAA,CAAAK,MAAgB;MACzD,IAAMA,MAAM,GAAGrB,EAAE,CAACqB,MAAM,CAACW,QAAQ,CAACG,QAAQ,EAAE,CAAC;MAC7CH,QAAQ,CAACV,OAAO,EAAE;MAClBE,KAAI,CAACJ,qBAAqB,CAACM,IAAI,EAAEL,MAAM,CAAC;IAC1C,CAAC,CAAC;EACJ,CAAC;EAEMjB,aAAA,CAAAO,SAAA,CAAAW,OAAO,GAAd,UAAec,gBAAgC;IAAhC,IAAAA,gBAAA;MAAAA,gBAAA,OAAgC;IAAA;IAC7C,IAAI,CAACb,YAAY,EAAE,CAACU,OAAO,CAAC,UAAAJ,KAAK;MAC/B,IAAIO,gBAAgB,IAAIP,KAAK,CAACR,MAAM,CAACgB,UAAU,EAAE;QAC/C,MAAM,IAAIC,KAAK,CAAC,qDAAmDT,KAAK,CAACH,IAAM,CAAC;;MAElFG,KAAK,CAACR,MAAM,CAACC,OAAO,EAAE;IACxB,CAAC,CAAC;IACF,IAAI,CAAChB,OAAO,GAAGC,SAAS;EAC1B,CAAC;EAEMH,aAAA,CAAAO,SAAA,CAAA4B,eAAe,GAAtB;IACE,OAAO,IAAIC,YAAY,CACrB,IAAI,CAACjB,YAAY,EAAE,CAChBE,GAAG,CAAC,UAACT,EAAU;UAARK,MAAA,GAAAL,EAAA,CAAAK,MAAM;MAAO,OAAAoB,KAAK,CAACC,IAAI,CAACrB,MAAM,CAACc,QAAQ,EAAE,CAAa;IAAzC,CAAyC,CAAC,CAC9DQ,MAAM,CAAC,UAACC,IAAI,EAAEC,GAAG;MAAK,OAAAD,IAAI,CAACE,MAAM,CAACD,GAAG,CAAC;IAAhB,CAAgB,CAAC,CAC3C;EACH,CAAC;EAEYzC,aAAA,CAAAO,SAAA,CAAAoC,IAAI,GAAjB,UAAkBC,YAA+C;;;;;YAC/D,IAAIA,YAAY,YAAYR,YAAY,EAAE;cACxC,IAAI,CAACS,cAAc,CAACD,YAAY,CAAC;cACjC;;YAGF,qBAAM,IAAI,CAACE,WAAW,CAACF,YAAY,CAAC;;YAApChC,EAAA,CAAAmC,IAAA,EAAoC;;;;;GACrC;EAEY/C,aAAA,CAAAO,SAAA,CAAAuC,WAAW,GAAxB,UAAyBE,GAAuB;;;;;;YAC9C,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cAClC,MAAM,IAAId,KAAK,CAAI,IAAI,CAACjC,KAAK,sCAAmC,CAAC;;YAGjD,qBAAMH,aAAa,CAACkD,GAAG,EAAE,IAAI,CAACC,mBAAmB,EAAE,CAAC;;YAAhEC,SAAS,GAAGtC,EAAA,CAAAmC,IAAA,EAAoD;YACtE,IAAI,CAACI,iBAAiB,CAACD,SAAS,CAAC;;;;;GAClC;EAEYlD,aAAA,CAAAO,SAAA,CAAA6C,YAAY,GAAzB,UAA0BC,QAA4B;;;;;;YACpD,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAC5C,MAAM,IAAInB,KAAK,CAAI,IAAI,CAACjC,KAAK,6CAA0C,CAAC;;YAGlEqD,QAAQ,GAAKvD,GAAG,CAACwD,MAAM,EAAE,CAAAD,QAAjB;YAEV1C,EAAA,GAAgCf,YAAY,CAACwD,QAAQ,EAAE,IAAI,CAACJ,mBAAmB,EAAE,CAAC,EAAhFO,WAAW,GAAA5C,EAAA,CAAA4C,WAAA,EAAEC,YAAY,GAAA7C,EAAA,CAAA6C,YAAA;YAE3BC,oBAAoB,GAAG,SAAAA,CAACC,SAAmB;cAAK,OAAAC,OAAO,CAACC,GAAG,CAC/DF,SAAS,CAACtC,GAAG,CAAC,UAAAgC,QAAQ;gBAAI,OAAAC,QAAQ,CAACD,QAAQ,CAAC,CAACS,IAAI,CAAC,UAAAC,GAAG;kBAAI,OAAAA,GAAG,CAACC,MAAM;gBAAV,CAAU,CAAC;cAA1C,CAA0C,CAAC,CACtE;YAFqD,CAErD;YACKC,WAAW,GAAGrE,EAAE,CAACsE,EAAE,CAACC,oBAAoB,CAACT,oBAAoB,CAAC;YAEnDU,EAAA,IAAAC,EAAA,GAAAC,IAAI,EAACC,KAAK;YAAE,qBAAMjB,QAAQ,CAACE,WAAW,CAAC;;YAAlDgB,QAAQ,GAAGJ,EAAA,CAAAK,KAAA,CAAAJ,EAAA,GAAYK,EAAA,CAAA3B,IAAA,EAA2B,CAAE4B,QAAQ,EAAE,EAAC;YACnD,qBAAMV,WAAW,CAACO,QAAQ,EAAEf,YAAY,CAAC;;YAArDP,SAAS,GAAGwB,EAAA,CAAA3B,IAAA,EAAyC;YAE3D,IAAI,CAACI,iBAAiB,CAACD,SAAS,CAAC;;;;;GAClC;EAEMlD,aAAA,CAAAO,SAAA,CAAA4C,iBAAiB,GAAxB,UAAyBD,SAA4B;IAC7C,IAAAtC,EAAA,QAAAgE,0BAAA,CAAA1B,SAAA,CAGwC;MAF5C2B,aAAA,GAAAjE,EAAA,CAAAiE,aAAa;MACbpE,MAAA,GAAAG,EAAA,CAAAH,MAC4C;IAE9C,IAAI,CAACL,cAAc,GAAGyE,aAAa;IACnC,IAAI,CAAC3E,OAAO,GAAGO,MAAM;EACvB,CAAC;EAEMT,aAAA,CAAAO,SAAA,CAAAsC,cAAc,GAArB,UAAsBiC,OAAqB;IACnC,IAAAlE,EAAA,QAAAmE,aAAA,CAAAD,OAAA,CAGyB;MAF7BD,aAAA,GAAAjE,EAAA,CAAAiE,aAAa;MACbpE,MAAA,GAAAG,EAAA,CAAAH,MAC6B;IAE/B,IAAI,CAACL,cAAc,GAAGyE,aAAa;IACnC,IAAI,CAAC3E,OAAO,GAAGO,MAAM;EACvB,CAAC;EAEOT,aAAA,CAAAO,SAAA,CAAAM,oBAAoB,GAA5B,UAA6BF,SAAiB;IAC5C,IAAI,CAAC,IAAI,CAACF,MAAM,EAAE;MAChB,MAAM,IAAIyB,KAAK,CAAC,mDAAmD,CAAC;;IAGtE,IAAM8C,MAAM,GAAGrE,SAAS,CAACsE,KAAK,CAAC,GAAG,CAAC,CAAC1C,MAAM,CAAC,UAAC2C,GAAkD,EAAEnE,OAAO;MACrG,IAAI,CAACmE,GAAG,CAACC,OAAO,CAACC,cAAc,CAACrE,OAAO,CAAC,EAAE;QACxC,MAAM,IAAImB,KAAK,CAAC,0DAAwDnB,OAAO,mBAAcJ,SAAW,CAAC;;MAG3G,OAAO;QAAEG,GAAG,EAAEoE,GAAG,CAACC,OAAO;QAAEpE,OAAO,EAAAA,OAAA;QAAEoE,OAAO,EAAED,GAAG,CAACC,OAAO,CAACpE,OAAO;MAAC,CAAE;IACrE,CAAC,EAAE;MAAEoE,OAAO,EAAE,IAAI,CAAC1E;IAAM,CAAE,CAAC;IAEpB,IAAAK,GAAA,GAAAkE,MAAA,CAAAlE,GAAG;MAAEC,OAAA,GAAAiE,MAAA,CAAAjE,OAAO;IACpB,IAAI,CAACD,GAAG,IAAI,CAACC,OAAO,IAAI,EAAED,GAAG,CAACC,OAAO,CAAC,YAAYnB,EAAE,CAACyF,MAAM,CAAC,EAAE;MAC5D,MAAM,IAAInD,KAAK,CAAC,gEAA8DvB,SAAW,CAAC;;IAG5F,OAAO;MAAEG,GAAG,EAAAA,GAAA;MAAEC,OAAO,EAAAA;IAAA,CAAE;EACzB,CAAC;EAKH,OAAAf,aAAC;AAAD,CAAC,CAvJD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}