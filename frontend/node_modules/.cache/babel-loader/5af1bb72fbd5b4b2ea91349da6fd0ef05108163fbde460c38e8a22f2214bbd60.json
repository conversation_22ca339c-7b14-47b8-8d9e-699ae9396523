{"ast": null, "code": "export function drawContour(ctx, points, isClosed) {\n  if (isClosed === void 0) {\n    isClosed = false;\n  }\n  ctx.beginPath();\n  points.slice(1).forEach(function (_a, prevIdx) {\n    var x = _a.x,\n      y = _a.y;\n    var from = points[prevIdx];\n    ctx.moveTo(from.x, from.y);\n    ctx.lineTo(x, y);\n  });\n  if (isClosed) {\n    var from = points[points.length - 1];\n    var to = points[0];\n    if (!from || !to) {\n      return;\n    }\n    ctx.moveTo(from.x, from.y);\n    ctx.lineTo(to.x, to.y);\n  }\n  ctx.stroke();\n}", "map": {"version": 3, "names": ["drawContour", "ctx", "points", "isClosed", "beginPath", "slice", "for<PERSON>ach", "_a", "prevIdx", "x", "y", "from", "moveTo", "lineTo", "length", "to", "stroke"], "sources": ["../../../src/draw/drawContour.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,WAAWA,CACzBC,GAA6B,EAC7BC,MAAe,EACfC,QAAyB;EAAzB,IAAAA,QAAA;IAAAA,QAAA,QAAyB;EAAA;EAEzBF,GAAG,CAACG,SAAS,EAAE;EAEfF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAACC,EAAQ,EAAEC,OAAO;QAAfC,CAAA,GAAAF,EAAA,CAAAE,CAAC;MAAEC,CAAA,GAAAH,EAAA,CAAAG,CAAC;IAC7B,IAAMC,IAAI,GAAGT,MAAM,CAACM,OAAO,CAAC;IAC5BP,GAAG,CAACW,MAAM,CAACD,IAAI,CAACF,CAAC,EAAEE,IAAI,CAACD,CAAC,CAAC;IAC1BT,GAAG,CAACY,MAAM,CAACJ,CAAC,EAAEC,CAAC,CAAC;EAClB,CAAC,CAAC;EAEF,IAAIP,QAAQ,EAAE;IACZ,IAAMQ,IAAI,GAAGT,MAAM,CAACA,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC;IACtC,IAAMC,EAAE,GAAGb,MAAM,CAAC,CAAC,CAAC;IACpB,IAAI,CAACS,IAAI,IAAI,CAACI,EAAE,EAAE;MAChB;;IAGFd,GAAG,CAACW,MAAM,CAACD,IAAI,CAACF,CAAC,EAAEE,IAAI,CAACD,CAAC,CAAC;IAC1BT,GAAG,CAACY,MAAM,CAACE,EAAE,CAACN,CAAC,EAAEM,EAAE,CAACL,CAAC,CAAC;;EAGxBT,GAAG,CAACe,MAAM,EAAE;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}