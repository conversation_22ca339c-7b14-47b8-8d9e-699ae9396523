{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { BoundingBox, Point } from '../classes';\nimport { nonMaxSuppression } from '../ops';\nimport { CELL_SIZE, CELL_STRIDE } from './config';\nimport { getSizesForScale } from './getSizesForScale';\nimport { MtcnnBox } from './MtcnnBox';\nimport { normalize } from './normalize';\nimport { PNet } from './PNet';\nfunction rescaleAndNormalize(x, scale) {\n  return tf.tidy(function () {\n    var _a = getSizesForScale(scale, x.shape.slice(1)),\n      height = _a.height,\n      width = _a.width;\n    var resized = tf.image.resizeBilinear(x, [height, width]);\n    var normalized = normalize(resized);\n    return tf.transpose(normalized, [0, 2, 1, 3]);\n  });\n}\nfunction extractBoundingBoxes(scoresTensor, regionsTensor, scale, scoreThreshold) {\n  // TODO: fix this!, maybe better to use tf.gather here\n  var indices = [];\n  var scoresData = scoresTensor.arraySync();\n  for (var y = 0; y < scoresTensor.shape[0]; y++) {\n    for (var x = 0; x < scoresTensor.shape[1]; x++) {\n      if (scoresData[y][x] >= scoreThreshold) {\n        indices.push(new Point(x, y));\n      }\n    }\n  }\n  var boundingBoxes = indices.map(function (idx) {\n    var cell = new BoundingBox(Math.round((idx.y * CELL_STRIDE + 1) / scale), Math.round((idx.x * CELL_STRIDE + 1) / scale), Math.round((idx.y * CELL_STRIDE + CELL_SIZE) / scale), Math.round((idx.x * CELL_STRIDE + CELL_SIZE) / scale));\n    var score = scoresData[idx.y][idx.x];\n    var regionsData = regionsTensor.arraySync();\n    var region = new MtcnnBox(regionsData[idx.y][idx.x][0], regionsData[idx.y][idx.x][1], regionsData[idx.y][idx.x][2], regionsData[idx.y][idx.x][3]);\n    return {\n      cell: cell,\n      score: score,\n      region: region\n    };\n  });\n  return boundingBoxes;\n}\nexport function stage1(imgTensor, scales, scoreThreshold, params, stats) {\n  stats.stage1 = [];\n  var pnetOutputs = scales.map(function (scale) {\n    return tf.tidy(function () {\n      var statsForScale = {\n        scale: scale\n      };\n      var resized = rescaleAndNormalize(imgTensor, scale);\n      var ts = Date.now();\n      var _a = PNet(resized, params),\n        prob = _a.prob,\n        regions = _a.regions;\n      statsForScale.pnet = Date.now() - ts;\n      var scoresTensor = tf.unstack(tf.unstack(prob, 3)[1])[0];\n      var regionsTensor = tf.unstack(regions)[0];\n      return {\n        scoresTensor: scoresTensor,\n        regionsTensor: regionsTensor,\n        scale: scale,\n        statsForScale: statsForScale\n      };\n    });\n  });\n  var boxesForScale = pnetOutputs.map(function (_a) {\n    var scoresTensor = _a.scoresTensor,\n      regionsTensor = _a.regionsTensor,\n      scale = _a.scale,\n      statsForScale = _a.statsForScale;\n    var boundingBoxes = extractBoundingBoxes(scoresTensor, regionsTensor, scale, scoreThreshold);\n    scoresTensor.dispose();\n    regionsTensor.dispose();\n    if (!boundingBoxes.length) {\n      stats.stage1.push(statsForScale);\n      return [];\n    }\n    var ts = Date.now();\n    var indices = nonMaxSuppression(boundingBoxes.map(function (bbox) {\n      return bbox.cell;\n    }), boundingBoxes.map(function (bbox) {\n      return bbox.score;\n    }), 0.5);\n    statsForScale.nms = Date.now() - ts;\n    statsForScale.numBoxes = indices.length;\n    stats.stage1.push(statsForScale);\n    return indices.map(function (boxIdx) {\n      return boundingBoxes[boxIdx];\n    });\n  });\n  var allBoxes = boxesForScale.reduce(function (all, boxes) {\n    return all.concat(boxes);\n  }, []);\n  var finalBoxes = [];\n  var finalScores = [];\n  if (allBoxes.length > 0) {\n    var ts = Date.now();\n    var indices = nonMaxSuppression(allBoxes.map(function (bbox) {\n      return bbox.cell;\n    }), allBoxes.map(function (bbox) {\n      return bbox.score;\n    }), 0.7);\n    stats.stage1_nms = Date.now() - ts;\n    finalScores = indices.map(function (idx) {\n      return allBoxes[idx].score;\n    });\n    finalBoxes = indices.map(function (idx) {\n      return allBoxes[idx];\n    }).map(function (_a) {\n      var cell = _a.cell,\n        region = _a.region;\n      return new BoundingBox(cell.left + region.left * cell.width, cell.top + region.top * cell.height, cell.right + region.right * cell.width, cell.bottom + region.bottom * cell.height).toSquare().round();\n    });\n  }\n  return {\n    boxes: finalBoxes,\n    scores: finalScores\n  };\n}", "map": {"version": 3, "names": ["tf", "BoundingBox", "Point", "nonMaxSuppression", "CELL_SIZE", "CELL_STRIDE", "getSizesForScale", "MtcnnBox", "normalize", "PNet", "rescaleAndNormalize", "x", "scale", "tidy", "_a", "shape", "slice", "height", "width", "resized", "image", "resizeBilinear", "normalized", "transpose", "extractBoundingBoxes", "scoresTensor", "regionsTensor", "scoreThreshold", "indices", "scoresData", "arraySync", "y", "push", "boundingBoxes", "map", "idx", "cell", "Math", "round", "score", "regionsData", "region", "stage1", "imgTensor", "scales", "params", "stats", "pnetOutputs", "statsForScale", "ts", "Date", "now", "prob", "regions", "pnet", "unstack", "boxesForScale", "dispose", "length", "bbox", "nms", "numBoxes", "boxIdx", "allBoxes", "reduce", "all", "boxes", "concat", "finalBoxes", "finalScores", "stage1_nms", "left", "top", "right", "bottom", "toSquare", "scores"], "sources": ["../../../src/mtcnn/stage1.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,WAAW,EAAEC,KAAK,QAAQ,YAAY;AAC/C,SAASC,iBAAiB,QAAQ,QAAQ;AAC1C,SAASC,SAAS,EAAEC,WAAW,QAAQ,UAAU;AACjD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,IAAI,QAAQ,QAAQ;AAG7B,SAASC,mBAAmBA,CAACC,CAAc,EAAEC,KAAa;EACxD,OAAOZ,EAAE,CAACa,IAAI,CAAC;IAEP,IAAAC,EAAA,GAAAR,gBAAA,CAAAM,KAAA,EAAAD,CAAA,CAAAI,KAAA,CAAAC,KAAA,IAA6D;MAA3DC,MAAA,GAAAH,EAAA,CAAAG,MAAM;MAAEC,KAAA,GAAAJ,EAAA,CAAAI,KAAmD;IACnE,IAAMC,OAAO,GAAGnB,EAAE,CAACoB,KAAK,CAACC,cAAc,CAACV,CAAC,EAAE,CAACM,MAAM,EAAEC,KAAK,CAAC,CAAC;IAC3D,IAAMI,UAAU,GAAGd,SAAS,CAACW,OAAO,CAAC;IAErC,OAAQnB,EAAE,CAACuB,SAAS,CAACD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAiB;EAChE,CAAC,CAAC;AACJ;AAEA,SAASE,oBAAoBA,CAC3BC,YAAyB,EACzBC,aAA0B,EAC1Bd,KAAa,EACbe,cAAsB;EAGtB;EACA,IAAMC,OAAO,GAAY,EAAE;EAC3B,IAAMC,UAAU,GAAGJ,YAAY,CAACK,SAAS,EAAE;EAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,CAACV,KAAK,CAAC,CAAC,CAAC,EAAEgB,CAAC,EAAE,EAAE;IAC9C,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,YAAY,CAACV,KAAK,CAAC,CAAC,CAAC,EAAEJ,CAAC,EAAE,EAAE;MAC9C,IAAIkB,UAAU,CAACE,CAAC,CAAC,CAACpB,CAAC,CAAC,IAAIgB,cAAc,EAAE;QACtCC,OAAO,CAACI,IAAI,CAAC,IAAI9B,KAAK,CAACS,CAAC,EAAEoB,CAAC,CAAC,CAAC;;;;EAKnC,IAAME,aAAa,GAAGL,OAAO,CAACM,GAAG,CAAC,UAAAC,GAAG;IACnC,IAAMC,IAAI,GAAG,IAAInC,WAAW,CAC1BoC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACJ,CAAC,GAAG1B,WAAW,GAAG,CAAC,IAAIO,KAAK,CAAC,EAC7CyB,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACxB,CAAC,GAAGN,WAAW,GAAG,CAAC,IAAIO,KAAK,CAAC,EAC7CyB,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACJ,CAAC,GAAG1B,WAAW,GAAGD,SAAS,IAAIQ,KAAK,CAAC,EACrDyB,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACxB,CAAC,GAAGN,WAAW,GAAGD,SAAS,IAAIQ,KAAK,CAAC,CACtD;IAED,IAAM2B,KAAK,GAAGV,UAAU,CAACM,GAAG,CAACJ,CAAC,CAAC,CAACI,GAAG,CAACxB,CAAC,CAAC;IAEtC,IAAM6B,WAAW,GAAGd,aAAa,CAACI,SAAS,EAAE;IAC7C,IAAMW,MAAM,GAAG,IAAIlC,QAAQ,CACzBiC,WAAW,CAACL,GAAG,CAACJ,CAAC,CAAC,CAACI,GAAG,CAACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B6B,WAAW,CAACL,GAAG,CAACJ,CAAC,CAAC,CAACI,GAAG,CAACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B6B,WAAW,CAACL,GAAG,CAACJ,CAAC,CAAC,CAACI,GAAG,CAACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B6B,WAAW,CAACL,GAAG,CAACJ,CAAC,CAAC,CAACI,GAAG,CAACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7B;IAED,OAAO;MACLyB,IAAI,EAAAA,IAAA;MACJG,KAAK,EAAAA,KAAA;MACLE,MAAM,EAAAA;KACP;EACH,CAAC,CAAC;EAEF,OAAOR,aAAa;AACtB;AAEA,OAAM,SAAUS,MAAMA,CACpBC,SAAsB,EACtBC,MAAgB,EAChBjB,cAAsB,EACtBkB,MAAkB,EAClBC,KAAU;EAEVA,KAAK,CAACJ,MAAM,GAAG,EAAE;EAEjB,IAAMK,WAAW,GAAGH,MAAM,CAACV,GAAG,CAAC,UAACtB,KAAK;IAAK,OAAAZ,EAAE,CAACa,IAAI,CAAC;MAChD,IAAMmC,aAAa,GAAQ;QAAEpC,KAAK,EAAAA;MAAA,CAAE;MACpC,IAAMO,OAAO,GAAGT,mBAAmB,CAACiC,SAAS,EAAE/B,KAAK,CAAC;MAErD,IAAIqC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;MACb,IAAArC,EAAA,GAAAL,IAAA,CAAAU,OAAA,EAAA0B,MAAA,CAAyC;QAAvCO,IAAA,GAAAtC,EAAA,CAAAsC,IAAI;QAAEC,OAAA,GAAAvC,EAAA,CAAAuC,OAAiC;MAC/CL,aAAa,CAACM,IAAI,GAAGJ,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;MAEpC,IAAMxB,YAAY,GAAGzB,EAAE,CAACuD,OAAO,CAACvD,EAAE,CAACuD,OAAO,CAACH,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAgB;MACzE,IAAM1B,aAAa,GAAG1B,EAAE,CAACuD,OAAO,CAACF,OAAO,CAAC,CAAC,CAAC,CAAgB;MAE3D,OAAO;QACL5B,YAAY,EAAAA,YAAA;QACZC,aAAa,EAAAA,aAAA;QACbd,KAAK,EAAAA,KAAA;QACLoC,aAAa,EAAAA;OACd;IACH,CAAC,CAAC;EAjBwC,CAiBxC,CAAC;EAEH,IAAMQ,aAAa,GAAGT,WAAW,CAACb,GAAG,CAAC,UAACpB,EAAqD;QAAnDW,YAAA,GAAAX,EAAA,CAAAW,YAAY;MAAEC,aAAA,GAAAZ,EAAA,CAAAY,aAAa;MAAEd,KAAA,GAAAE,EAAA,CAAAF,KAAK;MAAEoC,aAAA,GAAAlC,EAAA,CAAAkC,aAAa;IACxF,IAAMf,aAAa,GAAGT,oBAAoB,CACxCC,YAAY,EACZC,aAAa,EACbd,KAAK,EACLe,cAAc,CACf;IAEDF,YAAY,CAACgC,OAAO,EAAE;IACtB/B,aAAa,CAAC+B,OAAO,EAAE;IAEvB,IAAI,CAACxB,aAAa,CAACyB,MAAM,EAAE;MACzBZ,KAAK,CAACJ,MAAM,CAACV,IAAI,CAACgB,aAAa,CAAC;MAChC,OAAO,EAAE;;IAGX,IAAIC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;IACnB,IAAMvB,OAAO,GAAGzB,iBAAiB,CAC/B8B,aAAa,CAACC,GAAG,CAAC,UAAAyB,IAAI;MAAI,OAAAA,IAAI,CAACvB,IAAI;IAAT,CAAS,CAAC,EACpCH,aAAa,CAACC,GAAG,CAAC,UAAAyB,IAAI;MAAI,OAAAA,IAAI,CAACpB,KAAK;IAAV,CAAU,CAAC,EACrC,GAAG,CACJ;IACDS,aAAa,CAACY,GAAG,GAAGV,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;IACnCD,aAAa,CAACa,QAAQ,GAAGjC,OAAO,CAAC8B,MAAM;IAEvCZ,KAAK,CAACJ,MAAM,CAACV,IAAI,CAACgB,aAAa,CAAC;IAChC,OAAOpB,OAAO,CAACM,GAAG,CAAC,UAAA4B,MAAM;MAAI,OAAA7B,aAAa,CAAC6B,MAAM,CAAC;IAArB,CAAqB,CAAC;EACrD,CAAC,CAAC;EAEF,IAAMC,QAAQ,GAAGP,aAAa,CAACQ,MAAM,CACnC,UAACC,GAAG,EAAEC,KAAK;IAAK,OAAAD,GAAG,CAACE,MAAM,CAACD,KAAK,CAAC;EAAjB,CAAiB,EAAE,EAAE,CACtC;EAED,IAAIE,UAAU,GAAkB,EAAE;EAClC,IAAIC,WAAW,GAAa,EAAE;EAE9B,IAAIN,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;IACvB,IAAIT,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;IACnB,IAAMvB,OAAO,GAAGzB,iBAAiB,CAC/B4D,QAAQ,CAAC7B,GAAG,CAAC,UAAAyB,IAAI;MAAI,OAAAA,IAAI,CAACvB,IAAI;IAAT,CAAS,CAAC,EAC/B2B,QAAQ,CAAC7B,GAAG,CAAC,UAAAyB,IAAI;MAAI,OAAAA,IAAI,CAACpB,KAAK;IAAV,CAAU,CAAC,EAChC,GAAG,CACJ;IACDO,KAAK,CAACwB,UAAU,GAAGpB,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;IAElCoB,WAAW,GAAGzC,OAAO,CAACM,GAAG,CAAC,UAAAC,GAAG;MAAI,OAAA4B,QAAQ,CAAC5B,GAAG,CAAC,CAACI,KAAK;IAAnB,CAAmB,CAAC;IACrD6B,UAAU,GAAGxC,OAAO,CACjBM,GAAG,CAAC,UAAAC,GAAG;MAAI,OAAA4B,QAAQ,CAAC5B,GAAG,CAAC;IAAb,CAAa,CAAC,CACzBD,GAAG,CAAC,UAACpB,EAAgB;UAAdsB,IAAA,GAAAtB,EAAA,CAAAsB,IAAI;QAAEK,MAAA,GAAA3B,EAAA,CAAA2B,MAAM;MAClB,WAAIxC,WAAW,CACbmC,IAAI,CAACmC,IAAI,GAAI9B,MAAM,CAAC8B,IAAI,GAAGnC,IAAI,CAAClB,KAAM,EACtCkB,IAAI,CAACoC,GAAG,GAAI/B,MAAM,CAAC+B,GAAG,GAAGpC,IAAI,CAACnB,MAAO,EACrCmB,IAAI,CAACqC,KAAK,GAAIhC,MAAM,CAACgC,KAAK,GAAGrC,IAAI,CAAClB,KAAM,EACxCkB,IAAI,CAACsC,MAAM,GAAIjC,MAAM,CAACiC,MAAM,GAAGtC,IAAI,CAACnB,MAAO,CAC5C,CAAC0D,QAAQ,EAAE,CAACrC,KAAK,EAAE;IALpB,CAKoB,CACrB;;EAIL,OAAO;IACL4B,KAAK,EAAEE,UAAU;IACjBQ,MAAM,EAAEP;GACT;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}