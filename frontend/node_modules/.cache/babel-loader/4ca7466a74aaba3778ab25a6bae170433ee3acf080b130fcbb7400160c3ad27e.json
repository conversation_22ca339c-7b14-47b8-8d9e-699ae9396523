{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { extractFaces, extractFaceTensors } from '../dom';\nimport { isWithFaceLandmarks } from '../factories/WithFaceLandmarks';\nexport function extractAllFacesAndComputeResults(parentResults, input, computeResults, extractedFaces, getRectForAlignment) {\n  if (getRectForAlignment === void 0) {\n    getRectForAlignment = function (_a) {\n      var alignedRect = _a.alignedRect;\n      return alignedRect;\n    };\n  }\n  return __awaiter(this, void 0, void 0, function () {\n    var faceBoxes, faces, _a, _b, results;\n    return __generator(this, function (_c) {\n      switch (_c.label) {\n        case 0:\n          faceBoxes = parentResults.map(function (parentResult) {\n            return isWithFaceLandmarks(parentResult) ? getRectForAlignment(parentResult) : parentResult.detection;\n          });\n          _a = extractedFaces;\n          if (_a) return [3 /*break*/, 5];\n          if (!(input instanceof tf.Tensor)) return [3 /*break*/, 2];\n          return [4 /*yield*/, extractFaceTensors(input, faceBoxes)];\n        case 1:\n          _b = _c.sent();\n          return [3 /*break*/, 4];\n        case 2:\n          return [4 /*yield*/, extractFaces(input, faceBoxes)];\n        case 3:\n          _b = _c.sent();\n          _c.label = 4;\n        case 4:\n          _a = _b;\n          _c.label = 5;\n        case 5:\n          faces = _a;\n          return [4 /*yield*/, computeResults(faces)];\n        case 6:\n          results = _c.sent();\n          faces.forEach(function (f) {\n            return f instanceof tf.Tensor && f.dispose();\n          });\n          return [2 /*return*/, results];\n      }\n    });\n  });\n}\nexport function extractSingleFaceAndComputeResult(parentResult, input, computeResult, extractedFaces, getRectForAlignment) {\n  return __awaiter(this, void 0, void 0, function () {\n    var _this = this;\n    return __generator(this, function (_a) {\n      return [2 /*return*/, extractAllFacesAndComputeResults([parentResult], input, function (faces) {\n        return __awaiter(_this, void 0, void 0, function () {\n          return __generator(this, function (_a) {\n            return [2 /*return*/, computeResult(faces[0])];\n          });\n        });\n      }, extractedFaces, getRectForAlignment)];\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "extractFaces", "extractFaceTensors", "isWithFaceLandmarks", "extractAllFacesAndComputeResults", "parentResults", "input", "computeResults", "extractedFaces", "getRectForAlignment", "_a", "alignedRect", "faceBoxes", "map", "parentResult", "detection", "Tensor", "_b", "_c", "sent", "faces", "results", "for<PERSON>ach", "f", "dispose", "extractSingleFaceAndComputeResult", "computeResult", "__awaiter", "_this", "__generator"], "sources": ["../../../src/globalApi/extractFacesAndComputeResults.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,YAAY,EAAEC,kBAAkB,QAAmB,QAAQ;AAEpE,SAASC,mBAAmB,QAA2B,gCAAgC;AAEvF,OAAM,SAAgBC,gCAAgCA,CACpDC,aAAwB,EACxBC,KAAgB,EAChBC,cAAmF,EACnFC,cAA8D,EAC9DC,mBAAwH;EAAxH,IAAAA,mBAAA;IAAAA,mBAAA,YAAAA,CAAyFC,EAAe;UAAbC,WAAA,GAAAD,EAAA,CAAAC,WAAW;MAAO,OAAAA,WAAW;IAAX,CAAW;EAAA;;;;;;UAElHC,SAAS,GAAGP,aAAa,CAACQ,GAAG,CAAC,UAAAC,YAAY;YAC9C,OAAAX,mBAAmB,CAACW,YAAY,CAAC,GAC7BL,mBAAmB,CAACK,YAAY,CAAC,GACjCA,YAAY,CAACC,SAAS;UAF1B,CAE0B,CAC3B;UACqDL,EAAA,GAAAF,cAAc;kBAAd;gBACpDF,KAAK,YAAYN,EAAE,CAACgB,MAAM,GAA1B;UACI,qBAAMd,kBAAkB,CAACI,KAAK,EAAEM,SAAS,CAAC;;UAA1CK,EAAA,GAAAC,EAAA,CAAAC,IAAA,EAA0C;;;UAC1C,qBAAMlB,YAAY,CAACK,KAAK,EAAEM,SAAS,CAAC;;UAApCK,EAAA,GAAAC,EAAA,CAAAC,IAAA,EAAoC;;;UAH8BT,EAAA,GAAAO,EAIvE;;;UAJKG,KAAK,GAAAV,EAIV;UAEe,qBAAMH,cAAc,CAACa,KAAK,CAAC;;UAArCC,OAAO,GAAGH,EAAA,CAAAC,IAAA,EAA2B;UAE3CC,KAAK,CAACE,OAAO,CAAC,UAAAC,CAAC;YAAI,OAAAA,CAAC,YAAYvB,EAAE,CAACgB,MAAM,IAAIO,CAAC,CAACC,OAAO,EAAE;UAArC,CAAqC,CAAC;UAEzD,sBAAOH,OAAO;;;;;AAGhB,OAAM,SAAgBI,iCAAiCA,CACrDX,YAAqB,EACrBR,KAAgB,EAChBoB,aAA0E,EAC1ElB,cAA8D,EAC9DC,mBAAsF;;;;MAEtF,sBAAOL,gCAAgC,CACrC,CAACU,YAAY,CAAC,EACdR,KAAK,EACL,UAAMc,KAAK;QAAA,OAAAO,SAAA,CAAAC,KAAA;UAAA,OAAAC,WAAA,iBAAAnB,EAAA;YAAI,sBAAAgB,aAAa,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;;;OAAA,EACtCZ,cAAc,EACdC,mBAAmB,CACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}