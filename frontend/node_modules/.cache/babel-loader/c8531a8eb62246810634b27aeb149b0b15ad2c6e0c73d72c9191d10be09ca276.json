{"ast": null, "code": "var baseSetToString = require('./_baseSetToString'),\n  shortOut = require('./_shortOut');\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\nmodule.exports = setToString;", "map": {"version": 3, "names": ["baseSetToString", "require", "shortOut", "setToString", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_setToString.js"], "sourcesContent": ["var baseSetToString = require('./_baseSetToString'),\n    shortOut = require('./_shortOut');\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\n\nmodule.exports = setToString;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EAC/CC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAGD,QAAQ,CAACF,eAAe,CAAC;AAE3CI,MAAM,CAACC,OAAO,GAAGF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}