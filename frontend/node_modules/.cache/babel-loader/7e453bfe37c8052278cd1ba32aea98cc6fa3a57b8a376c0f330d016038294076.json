{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid2 as Grid, Card, CardContent, Chip, Alert, Divider } from '@mui/material';\nimport { CheckCircle as CheckCircleIcon, Error as ErrorIcon, Storage as StorageIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport SupabaseAuth from '../components/Supabase/SupabaseAuth';\nimport SupabaseImageUpload from '../components/Supabase/SupabaseImageUpload';\nimport { supabase } from '../config/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SupabaseTestPage = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [connectionStatus, setConnectionStatus] = useState('checking');\n  useEffect(() => {\n    // Vérifier la connexion Supabase\n    const checkConnection = async () => {\n      try {\n        var _data$session$user, _data$session;\n        const {\n          data,\n          error\n        } = await supabase.auth.getSession();\n        if (error) throw error;\n        setConnectionStatus('connected');\n        setUser((_data$session$user = (_data$session = data.session) === null || _data$session === void 0 ? void 0 : _data$session.user) !== null && _data$session$user !== void 0 ? _data$session$user : null);\n      } catch (error) {\n        console.error('Erreur de connexion Supabase:', error);\n        setConnectionStatus('error');\n      }\n    };\n    checkConnection();\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      var _session$user;\n      setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const StatusCard = ({\n    title,\n    status,\n    description,\n    icon\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        mb: 2,\n        children: [icon, /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: status === 'success' ? 'OK' : status === 'error' ? 'Erreur' : 'Attention',\n          color: status,\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      align: \"center\",\n      children: \"Test Supabase - PresencePro\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      align: \"center\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Page de test pour v\\xE9rifier l'int\\xE9gration Supabase : authentification et upload d'images\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StatusCard, {\n          title: \"Connexion Supabase\",\n          status: connectionStatus === 'connected' ? 'success' : connectionStatus === 'error' ? 'error' : 'warning',\n          description: connectionStatus === 'connected' ? 'Connexion établie avec succès' : connectionStatus === 'error' ? 'Erreur de connexion à Supabase' : 'Vérification en cours...',\n          icon: connectionStatus === 'connected' ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 54\n          }, this) : /*#__PURE__*/_jsxDEV(ErrorIcon, {\n            color: \"error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 92\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StatusCard, {\n          title: \"Authentification\",\n          status: user ? 'success' : 'warning',\n          description: user ? `Connecté en tant que ${user.email}` : 'Aucun utilisateur connecté',\n          icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            color: user ? 'success' : 'warning'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Configuration Supabase\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Project URL:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 46\n              }, this), process.env.REACT_APP_SUPABASE_URL || 'Non configuré']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Anon Key:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 43\n              }, this), process.env.REACT_APP_SUPABASE_ANON_KEY ? `${process.env.REACT_APP_SUPABASE_ANON_KEY.substring(0, 20)}...` : 'Non configuré']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), connectionStatus === 'error' && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Erreur de connexion Supabase\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"V\\xE9rifiez que les variables d'environnement REACT_APP_SUPABASE_URL et REACT_APP_SUPABASE_ANON_KEY sont correctement configur\\xE9es dans le fichier .env\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: \"Authentification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SupabaseAuth, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(StorageIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: \"Upload d'Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), !user ? /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              children: \"Connectez-vous pour tester l'upload d'images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(SupabaseImageUpload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Instructions de Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          component: \"div\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"1. Authentification :\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 51\n          }, this), \"\\u2022 Cr\\xE9ez un compte ou connectez-vous avec un compte existant\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 72\n          }, this), \"\\u2022 V\\xE9rifiez que l'authentification fonctionne correctement\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 70\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 76\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"2. Upload d'Images :\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 50\n          }, this), \"\\u2022 Une fois connect\\xE9, testez l'upload d'images de profil et faciales\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 80\n          }, this), \"\\u2022 V\\xE9rifiez que les images sont bien stock\\xE9es dans le bucket Supabase\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 81\n          }, this), \"\\u2022 Testez la suppression d'images\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 45\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 51\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"3. Politiques de S\\xE9curit\\xE9 :\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 57\n          }, this), \"\\u2022 Les uploads n\\xE9cessitent une authentification (politique INSERT)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 78\n          }, this), \"\\u2022 La lecture des images est publique (politique SELECT)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 68\n          }, this), \"\\u2022 Les images sont stock\\xE9es dans le bucket \\\"images\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(SupabaseTestPage, \"fHx0tZP+moGPZxVYtfHbs3ERaYg=\");\n_c = SupabaseTestPage;\nexport default SupabaseTestPage;\nvar _c;\n$RefreshReg$(_c, \"SupabaseTestPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid2", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "Divider", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Storage", "StorageIcon", "Security", "SecurityIcon", "SupabaseAuth", "SupabaseImageUpload", "supabase", "jsxDEV", "_jsxDEV", "SupabaseTestPage", "_s", "user", "setUser", "connectionStatus", "setConnectionStatus", "checkConnection", "_data$session$user", "_data$session", "data", "error", "auth", "getSession", "session", "console", "subscription", "onAuthStateChange", "_event", "_session$user", "unsubscribe", "StatusCard", "title", "status", "description", "icon", "children", "display", "alignItems", "gap", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "color", "size", "max<PERSON><PERSON><PERSON>", "sx", "py", "gutterBottom", "align", "container", "spacing", "xs", "md", "email", "process", "env", "REACT_APP_SUPABASE_URL", "item", "REACT_APP_SUPABASE_ANON_KEY", "substring", "severity", "lg", "mt", "component", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid2 as Grid,\n  Card,\n  CardContent,\n  Chip,\n  Alert,\n  Divider\n} from '@mui/material';\nimport {\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n  Storage as StorageIcon,\n  Security as SecurityIcon\n} from '@mui/icons-material';\nimport SupabaseAuth from '../components/Supabase/SupabaseAuth';\nimport SupabaseImageUpload from '../components/Supabase/SupabaseImageUpload';\nimport { supabase } from '../config/supabase';\nimport type { User } from '@supabase/supabase-js';\n\nconst SupabaseTestPage: React.FC = () => {\n  const [user, setUser] = useState<User | null>(null);\n  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');\n\n  useEffect(() => {\n    // Vérifier la connexion Supabase\n    const checkConnection = async () => {\n      try {\n        const { data, error } = await supabase.auth.getSession();\n        if (error) throw error;\n        setConnectionStatus('connected');\n        setUser(data.session?.user ?? null);\n      } catch (error) {\n        console.error('Erreur de connexion Supabase:', error);\n        setConnectionStatus('error');\n      }\n    };\n\n    checkConnection();\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user ?? null);\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const StatusCard: React.FC<{ \n    title: string; \n    status: 'success' | 'error' | 'warning'; \n    description: string;\n    icon: React.ReactNode;\n  }> = ({ title, status, description, icon }) => (\n    <Card>\n      <CardContent>\n        <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n          {icon}\n          <Typography variant=\"h6\">{title}</Typography>\n          <Chip\n            label={status === 'success' ? 'OK' : status === 'error' ? 'Erreur' : 'Attention'}\n            color={status}\n            size=\"small\"\n          />\n        </Box>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {description}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      <Typography variant=\"h3\" gutterBottom align=\"center\">\n        Test Supabase - PresencePro\n      </Typography>\n      \n      <Typography variant=\"body1\" align=\"center\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Page de test pour vérifier l'intégration Supabase : authentification et upload d'images\n      </Typography>\n\n      {/* Status de connexion */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid xs={12} md={6}>\n          <StatusCard\n            title=\"Connexion Supabase\"\n            status={connectionStatus === 'connected' ? 'success' : connectionStatus === 'error' ? 'error' : 'warning'}\n            description={\n              connectionStatus === 'connected' \n                ? 'Connexion établie avec succès'\n                : connectionStatus === 'error'\n                ? 'Erreur de connexion à Supabase'\n                : 'Vérification en cours...'\n            }\n            icon={connectionStatus === 'connected' ? <CheckCircleIcon color=\"success\" /> : <ErrorIcon color=\"error\" />}\n          />\n        </Grid>\n\n        <Grid xs={12} md={6}>\n          <StatusCard\n            title=\"Authentification\"\n            status={user ? 'success' : 'warning'}\n            description={\n              user \n                ? `Connecté en tant que ${user.email}`\n                : 'Aucun utilisateur connecté'\n            }\n            icon={<SecurityIcon color={user ? 'success' : 'warning'} />}\n          />\n        </Grid>\n      </Grid>\n\n      {/* Configuration actuelle */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Configuration Supabase\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <Grid container spacing={2}>\n            <Grid xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Project URL:</strong><br />\n                {process.env.REACT_APP_SUPABASE_URL || 'Non configuré'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Anon Key:</strong><br />\n                {process.env.REACT_APP_SUPABASE_ANON_KEY ? \n                  `${process.env.REACT_APP_SUPABASE_ANON_KEY.substring(0, 20)}...` : \n                  'Non configuré'\n                }\n              </Typography>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {connectionStatus === 'error' && (\n        <Alert severity=\"error\" sx={{ mb: 4 }}>\n          <Typography variant=\"h6\">Erreur de connexion Supabase</Typography>\n          <Typography>\n            Vérifiez que les variables d'environnement REACT_APP_SUPABASE_URL et REACT_APP_SUPABASE_ANON_KEY \n            sont correctement configurées dans le fichier .env\n          </Typography>\n        </Alert>\n      )}\n\n      {/* Section Authentification */}\n      <Grid container spacing={4}>\n        <Grid item xs={12} lg={6}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                <SecurityIcon />\n                <Typography variant=\"h5\">Authentification</Typography>\n              </Box>\n              <SupabaseAuth />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Section Upload d'Images */}\n        <Grid item xs={12} lg={6}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                <StorageIcon />\n                <Typography variant=\"h5\">Upload d'Images</Typography>\n              </Box>\n              \n              {!user ? (\n                <Alert severity=\"info\">\n                  Connectez-vous pour tester l'upload d'images\n                </Alert>\n              ) : (\n                <SupabaseImageUpload />\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Instructions */}\n      <Card sx={{ mt: 4 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Instructions de Test\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <Typography variant=\"body2\" component=\"div\">\n            <strong>1. Authentification :</strong><br />\n            • Créez un compte ou connectez-vous avec un compte existant<br />\n            • Vérifiez que l'authentification fonctionne correctement<br /><br />\n            \n            <strong>2. Upload d'Images :</strong><br />\n            • Une fois connecté, testez l'upload d'images de profil et faciales<br />\n            • Vérifiez que les images sont bien stockées dans le bucket Supabase<br />\n            • Testez la suppression d'images<br /><br />\n            \n            <strong>3. Politiques de Sécurité :</strong><br />\n            • Les uploads nécessitent une authentification (politique INSERT)<br />\n            • La lecture des images est publique (politique SELECT)<br />\n            • Les images sont stockées dans le bucket \"images\"\n          </Typography>\n        </CardContent>\n      </Card>\n    </Container>\n  );\n};\n\nexport default SupabaseTestPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,KAAK,IAAIC,IAAI,EACbC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,SAASC,QAAQ,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9C,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAqC,UAAU,CAAC;EAExGC,SAAS,CAAC,MAAM;IACd;IACA,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QAAA,IAAAC,kBAAA,EAAAC,aAAA;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAACC,UAAU,CAAC,CAAC;QACxD,IAAIF,KAAK,EAAE,MAAMA,KAAK;QACtBL,mBAAmB,CAAC,WAAW,CAAC;QAChCF,OAAO,EAAAI,kBAAA,IAAAC,aAAA,GAACC,IAAI,CAACI,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcN,IAAI,cAAAK,kBAAA,cAAAA,kBAAA,GAAI,IAAI,CAAC;MACrC,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdI,OAAO,CAACJ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDL,mBAAmB,CAAC,OAAO,CAAC;MAC9B;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAM;MAAEG,IAAI,EAAE;QAAEM;MAAa;IAAE,CAAC,GAAGlB,QAAQ,CAACc,IAAI,CAACK,iBAAiB,CAAC,CAACC,MAAM,EAAEJ,OAAO,KAAK;MAAA,IAAAK,aAAA;MACtFf,OAAO,EAAAe,aAAA,GAACL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEX,IAAI,cAAAgB,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,MAAMH,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAKJ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,MAAM;IAAEC,WAAW;IAAEC;EAAK,CAAC,kBACxCzB,OAAA,CAACjB,IAAI;IAAA2C,QAAA,eACH1B,OAAA,CAAChB,WAAW;MAAA0C,QAAA,gBACV1B,OAAA,CAACtB,GAAG;QAACiD,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAJ,QAAA,GACnDD,IAAI,eACLzB,OAAA,CAACpB,UAAU;UAACmD,OAAO,EAAC,IAAI;UAAAL,QAAA,EAAEJ;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7CnC,OAAA,CAACf,IAAI;UACHmD,KAAK,EAAEb,MAAM,KAAK,SAAS,GAAG,IAAI,GAAGA,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,WAAY;UACjFc,KAAK,EAAEd,MAAO;UACde,IAAI,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnC,OAAA,CAACpB,UAAU;QAACmD,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,gBAAgB;QAAAX,QAAA,EAC/CF;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,oBACEnC,OAAA,CAACrB,SAAS;IAAC4D,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAf,QAAA,gBACrC1B,OAAA,CAACpB,UAAU;MAACmD,OAAO,EAAC,IAAI;MAACW,YAAY;MAACC,KAAK,EAAC,QAAQ;MAAAjB,QAAA,EAAC;IAErD;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnC,OAAA,CAACpB,UAAU;MAACmD,OAAO,EAAC,OAAO;MAACY,KAAK,EAAC,QAAQ;MAACN,KAAK,EAAC,gBAAgB;MAACG,EAAE,EAAE;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAEjF;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbnC,OAAA,CAAClB,IAAI;MAAC8D,SAAS;MAACC,OAAO,EAAE,CAAE;MAACL,EAAE,EAAE;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC1B,OAAA,CAAClB,IAAI;QAACgE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAClB1B,OAAA,CAACqB,UAAU;UACTC,KAAK,EAAC,oBAAoB;UAC1BC,MAAM,EAAElB,gBAAgB,KAAK,WAAW,GAAG,SAAS,GAAGA,gBAAgB,KAAK,OAAO,GAAG,OAAO,GAAG,SAAU;UAC1GmB,WAAW,EACTnB,gBAAgB,KAAK,WAAW,GAC5B,+BAA+B,GAC/BA,gBAAgB,KAAK,OAAO,GAC5B,gCAAgC,GAChC,0BACL;UACDoB,IAAI,EAAEpB,gBAAgB,KAAK,WAAW,gBAAGL,OAAA,CAACX,eAAe;YAACgD,KAAK,EAAC;UAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnC,OAAA,CAACT,SAAS;YAAC8C,KAAK,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPnC,OAAA,CAAClB,IAAI;QAACgE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eAClB1B,OAAA,CAACqB,UAAU;UACTC,KAAK,EAAC,kBAAkB;UACxBC,MAAM,EAAEpB,IAAI,GAAG,SAAS,GAAG,SAAU;UACrCqB,WAAW,EACTrB,IAAI,GACA,wBAAwBA,IAAI,CAAC6C,KAAK,EAAE,GACpC,4BACL;UACDvB,IAAI,eAAEzB,OAAA,CAACL,YAAY;YAAC0C,KAAK,EAAElC,IAAI,GAAG,SAAS,GAAG;UAAU;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnC,OAAA,CAACjB,IAAI;MAACyD,EAAE,EAAE;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClB1B,OAAA,CAAChB,WAAW;QAAA0C,QAAA,gBACV1B,OAAA,CAACpB,UAAU;UAACmD,OAAO,EAAC,IAAI;UAACW,YAAY;UAAAhB,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACb,OAAO;UAACqD,EAAE,EAAE;YAAEV,EAAE,EAAE;UAAE;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BnC,OAAA,CAAClB,IAAI;UAAC8D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBACzB1B,OAAA,CAAClB,IAAI;YAACgE,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eAClB1B,OAAA,CAACpB,UAAU;cAACmD,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB1B,OAAA;gBAAA0B,QAAA,EAAQ;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAnC,OAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAClCc,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,eAAe;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnC,OAAA,CAAClB,IAAI;YAACsE,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACvB1B,OAAA,CAACpB,UAAU;cAACmD,OAAO,EAAC,OAAO;cAAAL,QAAA,gBACzB1B,OAAA;gBAAA0B,QAAA,EAAQ;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAnC,OAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC/Bc,OAAO,CAACC,GAAG,CAACG,2BAA2B,GACtC,GAAGJ,OAAO,CAACC,GAAG,CAACG,2BAA2B,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAChE,eAAe;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEN9B,gBAAgB,KAAK,OAAO,iBAC3BL,OAAA,CAACd,KAAK;MAACqE,QAAQ,EAAC,OAAO;MAACf,EAAE,EAAE;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACpC1B,OAAA,CAACpB,UAAU;QAACmD,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAA4B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClEnC,OAAA,CAACpB,UAAU;QAAA8C,QAAA,EAAC;MAGZ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,eAGDnC,OAAA,CAAClB,IAAI;MAAC8D,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzB1B,OAAA,CAAClB,IAAI;QAACsE,IAAI;QAACN,EAAE,EAAE,EAAG;QAACU,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB1B,OAAA,CAACjB,IAAI;UAAA2C,QAAA,eACH1B,OAAA,CAAChB,WAAW;YAAA0C,QAAA,gBACV1B,OAAA,CAACtB,GAAG;cAACiD,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAAJ,QAAA,gBACpD1B,OAAA,CAACL,YAAY;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChBnC,OAAA,CAACpB,UAAU;gBAACmD,OAAO,EAAC,IAAI;gBAAAL,QAAA,EAAC;cAAgB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNnC,OAAA,CAACJ,YAAY;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPnC,OAAA,CAAClB,IAAI;QAACsE,IAAI;QAACN,EAAE,EAAE,EAAG;QAACU,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB1B,OAAA,CAACjB,IAAI;UAAA2C,QAAA,eACH1B,OAAA,CAAChB,WAAW;YAAA0C,QAAA,gBACV1B,OAAA,CAACtB,GAAG;cAACiD,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAAJ,QAAA,gBACpD1B,OAAA,CAACP,WAAW;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACfnC,OAAA,CAACpB,UAAU;gBAACmD,OAAO,EAAC,IAAI;gBAAAL,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,EAEL,CAAChC,IAAI,gBACJH,OAAA,CAACd,KAAK;cAACqE,QAAQ,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAEvB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAERnC,OAAA,CAACH,mBAAmB;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACvB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnC,OAAA,CAACjB,IAAI;MAACyD,EAAE,EAAE;QAAEiB,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,eAClB1B,OAAA,CAAChB,WAAW;QAAA0C,QAAA,gBACV1B,OAAA,CAACpB,UAAU;UAACmD,OAAO,EAAC,IAAI;UAACW,YAAY;UAAAhB,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACb,OAAO;UAACqD,EAAE,EAAE;YAAEV,EAAE,EAAE;UAAE;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BnC,OAAA,CAACpB,UAAU;UAACmD,OAAO,EAAC,OAAO;UAAC2B,SAAS,EAAC,KAAK;UAAAhC,QAAA,gBACzC1B,OAAA;YAAA0B,QAAA,EAAQ;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,uEACe,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,qEACR,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAErEnC,OAAA;YAAA0B,QAAA,EAAQ;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,+EACwB,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,mFACL,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yCAC1C,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAE5CnC,OAAA;YAAA0B,QAAA,EAAQ;UAA2B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,6EACe,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gEAChB,eAAAnC,OAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gEAE/D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACjC,EAAA,CA/LID,gBAA0B;AAAA0D,EAAA,GAA1B1D,gBAA0B;AAiMhC,eAAeA,gBAAgB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}