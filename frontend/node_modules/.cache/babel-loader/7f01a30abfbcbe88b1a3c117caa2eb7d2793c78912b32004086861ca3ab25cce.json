{"ast": null, "code": "import { getContext2dOrThrow } from '../dom/getContext2dOrThrow';\nimport { resolveInput } from '../dom/resolveInput';\nexport var AnchorPosition;\n(function (AnchorPosition) {\n  AnchorPosition[\"TOP_LEFT\"] = \"TOP_LEFT\";\n  AnchorPosition[\"TOP_RIGHT\"] = \"TOP_RIGHT\";\n  AnchorPosition[\"BOTTOM_LEFT\"] = \"BOTTOM_LEFT\";\n  AnchorPosition[\"BOTTOM_RIGHT\"] = \"BOTTOM_RIGHT\";\n})(AnchorPosition || (AnchorPosition = {}));\nvar DrawTextFieldOptions = /** @class */function () {\n  function DrawTextFieldOptions(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var anchorPosition = options.anchorPosition,\n      backgroundColor = options.backgroundColor,\n      fontColor = options.fontColor,\n      fontSize = options.fontSize,\n      fontStyle = options.fontStyle,\n      padding = options.padding;\n    this.anchorPosition = anchorPosition || AnchorPosition.TOP_LEFT;\n    this.backgroundColor = backgroundColor || 'rgba(0, 0, 0, 0.5)';\n    this.fontColor = fontColor || 'rgba(255, 255, 255, 1)';\n    this.fontSize = fontSize || 14;\n    this.fontStyle = fontStyle || 'Georgia';\n    this.padding = padding || 4;\n  }\n  return DrawTextFieldOptions;\n}();\nexport { DrawTextFieldOptions };\nvar DrawTextField = /** @class */function () {\n  function DrawTextField(text, anchor, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.text = typeof text === 'string' ? [text] : text instanceof DrawTextField ? text.text : text;\n    this.anchor = anchor;\n    this.options = new DrawTextFieldOptions(options);\n  }\n  DrawTextField.prototype.measureWidth = function (ctx) {\n    var padding = this.options.padding;\n    return this.text.map(function (l) {\n      return ctx.measureText(l).width;\n    }).reduce(function (w0, w1) {\n      return w0 < w1 ? w1 : w0;\n    }, 0) + 2 * padding;\n  };\n  DrawTextField.prototype.measureHeight = function () {\n    var _a = this.options,\n      fontSize = _a.fontSize,\n      padding = _a.padding;\n    return this.text.length * fontSize + 2 * padding;\n  };\n  DrawTextField.prototype.getUpperLeft = function (ctx, canvasDims) {\n    var anchorPosition = this.options.anchorPosition;\n    var isShiftLeft = anchorPosition === AnchorPosition.BOTTOM_RIGHT || anchorPosition === AnchorPosition.TOP_RIGHT;\n    var isShiftTop = anchorPosition === AnchorPosition.BOTTOM_LEFT || anchorPosition === AnchorPosition.BOTTOM_RIGHT;\n    var textFieldWidth = this.measureWidth(ctx);\n    var textFieldHeight = this.measureHeight();\n    var x = isShiftLeft ? this.anchor.x - textFieldWidth : this.anchor.x;\n    var y = isShiftTop ? this.anchor.y - textFieldHeight : this.anchor.y;\n    // adjust anchor if text box exceeds canvas borders\n    if (canvasDims) {\n      var width = canvasDims.width,\n        height = canvasDims.height;\n      var newX = Math.max(Math.min(x, width - textFieldWidth), 0);\n      var newY = Math.max(Math.min(y, height - textFieldHeight), 0);\n      return {\n        x: newX,\n        y: newY\n      };\n    }\n    return {\n      x: x,\n      y: y\n    };\n  };\n  DrawTextField.prototype.draw = function (canvasArg) {\n    var canvas = resolveInput(canvasArg);\n    var ctx = getContext2dOrThrow(canvas);\n    var _a = this.options,\n      backgroundColor = _a.backgroundColor,\n      fontColor = _a.fontColor,\n      fontSize = _a.fontSize,\n      fontStyle = _a.fontStyle,\n      padding = _a.padding;\n    ctx.font = fontSize + \"px \" + fontStyle;\n    var maxTextWidth = this.measureWidth(ctx);\n    var textHeight = this.measureHeight();\n    ctx.fillStyle = backgroundColor;\n    var upperLeft = this.getUpperLeft(ctx, canvas);\n    ctx.fillRect(upperLeft.x, upperLeft.y, maxTextWidth, textHeight);\n    ctx.fillStyle = fontColor;\n    this.text.forEach(function (textLine, i) {\n      var x = padding + upperLeft.x;\n      var y = padding + upperLeft.y + (i + 1) * fontSize;\n      ctx.fillText(textLine, x, y);\n    });\n  };\n  return DrawTextField;\n}();\nexport { DrawTextField };", "map": {"version": 3, "names": ["getContext2dOrThrow", "resolveInput", "AnchorPosition", "DrawTextFieldOptions", "options", "anchorPosition", "backgroundColor", "fontColor", "fontSize", "fontStyle", "padding", "TOP_LEFT", "DrawTextField", "text", "anchor", "prototype", "measureWidth", "ctx", "map", "l", "measureText", "width", "reduce", "w0", "w1", "measureHeight", "_a", "length", "getUpperLeft", "canvasDims", "isShiftLeft", "BOTTOM_RIGHT", "TOP_RIGHT", "isShiftTop", "BOTTOM_LEFT", "textFieldWidth", "textFieldHeight", "x", "y", "height", "newX", "Math", "max", "min", "newY", "draw", "canvasArg", "canvas", "font", "maxTextWidth", "textHeight", "fillStyle", "upperLeft", "fillRect", "for<PERSON>ach", "textLine", "i", "fillText"], "sources": ["../../../src/draw/DrawTextField.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAElD,WAAYC,cAKX;AALD,WAAYA,cAAc;EACxBA,cAAA,yBAAqB;EACrBA,cAAA,2BAAuB;EACvBA,cAAA,+BAA2B;EAC3BA,cAAA,iCAA6B;AAC/B,CAAC,EALWA,cAAc,KAAdA,cAAc;AAgB1B,IAAAC,oBAAA;EAQE,SAAAA,qBAAYC,OAAmC;IAAnC,IAAAA,OAAA;MAAAA,OAAA,KAAmC;IAAA;IACrC,IAAAC,cAAA,GAAAD,OAAA,CAAAC,cAAc;MAAEC,eAAA,GAAAF,OAAA,CAAAE,eAAe;MAAEC,SAAA,GAAAH,OAAA,CAAAG,SAAS;MAAEC,QAAA,GAAAJ,OAAA,CAAAI,QAAQ;MAAEC,SAAA,GAAAL,OAAA,CAAAK,SAAS;MAAEC,OAAA,GAAAN,OAAA,CAAAM,OAAO;IAChF,IAAI,CAACL,cAAc,GAAGA,cAAc,IAAIH,cAAc,CAACS,QAAQ;IAC/D,IAAI,CAACL,eAAe,GAAGA,eAAe,IAAI,oBAAoB;IAC9D,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,wBAAwB;IACtD,IAAI,CAACC,QAAQ,GAAGA,QAAQ,IAAI,EAAE;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,SAAS;IACvC,IAAI,CAACC,OAAO,GAAGA,OAAO,IAAI,CAAC;EAC7B;EACF,OAAAP,oBAAC;AAAD,CAAC,CAjBD;;AAmBA,IAAAS,aAAA;EAKE,SAAAA,cACEC,IAAuC,EACvCC,MAAc,EACdV,OAAmC;IAAnC,IAAAA,OAAA;MAAAA,OAAA,KAAmC;IAAA;IAEnC,IAAI,CAACS,IAAI,GAAG,OAAOA,IAAI,KAAK,QAAQ,GAChC,CAACA,IAAI,CAAC,GACLA,IAAI,YAAYD,aAAa,GAAGC,IAAI,CAACA,IAAI,GAAGA,IAAK;IACtD,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACV,OAAO,GAAG,IAAID,oBAAoB,CAACC,OAAO,CAAC;EAClD;EAEAQ,aAAA,CAAAG,SAAA,CAAAC,YAAY,GAAZ,UAAaC,GAA6B;IAChC,IAAAP,OAAA,QAAAN,OAAA,CAAAM,OAAO;IACf,OAAO,IAAI,CAACG,IAAI,CAACK,GAAG,CAAC,UAAAC,CAAC;MAAI,OAAAF,GAAG,CAACG,WAAW,CAACD,CAAC,CAAC,CAACE,KAAK;IAAxB,CAAwB,CAAC,CAACC,MAAM,CAAC,UAACC,EAAE,EAAEC,EAAE;MAAK,OAAAD,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGD,EAAE;IAAjB,CAAiB,EAAE,CAAC,CAAC,GAAI,CAAC,GAAGb,OAAQ;EAC9G,CAAC;EAEDE,aAAA,CAAAG,SAAA,CAAAU,aAAa,GAAb;IACQ,IAAAC,EAAA,QAAAtB,OAAoC;MAAlCI,QAAA,GAAAkB,EAAA,CAAAlB,QAAQ;MAAEE,OAAA,GAAAgB,EAAA,CAAAhB,OAAwB;IAC1C,OAAO,IAAI,CAACG,IAAI,CAACc,MAAM,GAAGnB,QAAQ,GAAI,CAAC,GAAGE,OAAQ;EACpD,CAAC;EAEDE,aAAA,CAAAG,SAAA,CAAAa,YAAY,GAAZ,UAAaX,GAA6B,EAAEY,UAAwB;IAC1D,IAAAxB,cAAA,QAAAD,OAAA,CAAAC,cAAc;IACtB,IAAMyB,WAAW,GAAGzB,cAAc,KAAKH,cAAc,CAAC6B,YAAY,IAAI1B,cAAc,KAAKH,cAAc,CAAC8B,SAAS;IACjH,IAAMC,UAAU,GAAG5B,cAAc,KAAKH,cAAc,CAACgC,WAAW,IAAI7B,cAAc,KAAKH,cAAc,CAAC6B,YAAY;IAElH,IAAMI,cAAc,GAAG,IAAI,CAACnB,YAAY,CAACC,GAAG,CAAC;IAC7C,IAAMmB,eAAe,GAAG,IAAI,CAACX,aAAa,EAAE;IAC5C,IAAMY,CAAC,GAAIP,WAAW,GAAG,IAAI,CAAChB,MAAM,CAACuB,CAAC,GAAGF,cAAc,GAAG,IAAI,CAACrB,MAAM,CAACuB,CAAE;IACxE,IAAMC,CAAC,GAAGL,UAAU,GAAG,IAAI,CAACnB,MAAM,CAACwB,CAAC,GAAGF,eAAe,GAAG,IAAI,CAACtB,MAAM,CAACwB,CAAC;IAEtE;IACA,IAAIT,UAAU,EAAE;MACN,IAAAR,KAAA,GAAAQ,UAAA,CAAAR,KAAK;QAAEkB,MAAA,GAAAV,UAAA,CAAAU,MAAM;MACrB,IAAMC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,CAAC,EAAEhB,KAAK,GAAGc,cAAc,CAAC,EAAE,CAAC,CAAC;MAC7D,IAAMS,IAAI,GAAGH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,CAAC,EAAEC,MAAM,GAAGH,eAAe,CAAC,EAAE,CAAC,CAAC;MAC/D,OAAO;QAAEC,CAAC,EAAEG,IAAI;QAAEF,CAAC,EAAEM;MAAI,CAAE;;IAE7B,OAAO;MAAEP,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA;IAAA,CAAE;EACjB,CAAC;EAED1B,aAAA,CAAAG,SAAA,CAAA8B,IAAI,GAAJ,UAAKC,SAAgE;IACnE,IAAMC,MAAM,GAAG9C,YAAY,CAAC6C,SAAS,CAAC;IACtC,IAAM7B,GAAG,GAAGjB,mBAAmB,CAAC+C,MAAM,CAAC;IAEjC,IAAArB,EAAA,QAAAtB,OAA2E;MAAzEE,eAAA,GAAAoB,EAAA,CAAApB,eAAe;MAAEC,SAAA,GAAAmB,EAAA,CAAAnB,SAAS;MAAEC,QAAA,GAAAkB,EAAA,CAAAlB,QAAQ;MAAEC,SAAA,GAAAiB,EAAA,CAAAjB,SAAS;MAAEC,OAAA,GAAAgB,EAAA,CAAAhB,OAAwB;IAEjFO,GAAG,CAAC+B,IAAI,GAAMxC,QAAQ,WAAMC,SAAW;IACvC,IAAMwC,YAAY,GAAG,IAAI,CAACjC,YAAY,CAACC,GAAG,CAAC;IAC3C,IAAMiC,UAAU,GAAG,IAAI,CAACzB,aAAa,EAAE;IAEvCR,GAAG,CAACkC,SAAS,GAAG7C,eAAe;IAC/B,IAAM8C,SAAS,GAAG,IAAI,CAACxB,YAAY,CAACX,GAAG,EAAE8B,MAAM,CAAC;IAChD9B,GAAG,CAACoC,QAAQ,CAACD,SAAS,CAACf,CAAC,EAAEe,SAAS,CAACd,CAAC,EAAEW,YAAY,EAAEC,UAAU,CAAC;IAEhEjC,GAAG,CAACkC,SAAS,GAAG5C,SAAS;IACzB,IAAI,CAACM,IAAI,CAACyC,OAAO,CAAC,UAACC,QAAQ,EAAEC,CAAC;MAC5B,IAAMnB,CAAC,GAAG3B,OAAO,GAAG0C,SAAS,CAACf,CAAC;MAC/B,IAAMC,CAAC,GAAG5B,OAAO,GAAG0C,SAAS,CAACd,CAAC,GAAI,CAACkB,CAAC,GAAG,CAAC,IAAIhD,QAAS;MACtDS,GAAG,CAACwC,QAAQ,CAACF,QAAQ,EAAElB,CAAC,EAAEC,CAAC,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EACH,OAAA1B,aAAC;AAAD,CAAC,CApED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}