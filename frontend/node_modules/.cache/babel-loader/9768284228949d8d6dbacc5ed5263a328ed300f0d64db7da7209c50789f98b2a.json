{"ast": null, "code": "export default function (series, order) {\n  if (!((n = series.length) > 1)) return;\n  for (var i = 1, j, s0, s1 = series[order[0]], n, m = s1.length; i < n; ++i) {\n    s0 = s1, s1 = series[order[i]];\n    for (j = 0; j < m; ++j) {\n      s1[j][1] += s1[j][0] = isNaN(s0[j][1]) ? s0[j][0] : s0[j][1];\n    }\n  }\n}", "map": {"version": 3, "names": ["series", "order", "n", "length", "i", "j", "s0", "s1", "m", "isNaN"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/d3-shape/src/offset/none.js"], "sourcesContent": ["export default function(series, order) {\n  if (!((n = series.length) > 1)) return;\n  for (var i = 1, j, s0, s1 = series[order[0]], n, m = s1.length; i < n; ++i) {\n    s0 = s1, s1 = series[order[i]];\n    for (j = 0; j < m; ++j) {\n      s1[j][1] += s1[j][0] = isNaN(s0[j][1]) ? s0[j][0] : s0[j][1];\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAEC,KAAK,EAAE;EACrC,IAAI,EAAE,CAACC,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,GAAGP,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,EAAEM,CAAC,GAAGD,EAAE,CAACJ,MAAM,EAAEC,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IAC1EE,EAAE,GAAGC,EAAE,EAAEA,EAAE,GAAGP,MAAM,CAACC,KAAK,CAACG,CAAC,CAAC,CAAC;IAC9B,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,CAAC,EAAE,EAAEH,CAAC,EAAE;MACtBE,EAAE,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIE,EAAE,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGI,KAAK,CAACH,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}