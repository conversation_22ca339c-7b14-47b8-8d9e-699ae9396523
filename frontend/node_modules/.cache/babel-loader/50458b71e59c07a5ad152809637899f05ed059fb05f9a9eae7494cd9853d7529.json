{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { convLayer } from '../common';\nimport { sharedLayer } from './sharedLayers';\nexport function PNet(x, params) {\n  return tf.tidy(function () {\n    var out = sharedLayer(x, params, true);\n    var conv = convLayer(out, params.conv4_1, 'valid');\n    var max = tf.expandDims(tf.max(conv, 3), 3);\n    var prob = tf.softmax(tf.sub(conv, max), 3);\n    var regions = convLayer(out, params.conv4_2, 'valid');\n    return {\n      prob: prob,\n      regions: regions\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "convLayer", "<PERSON><PERSON><PERSON><PERSON>", "PNet", "x", "params", "tidy", "out", "conv", "conv4_1", "max", "expandDims", "prob", "softmax", "sub", "regions", "conv4_2"], "sources": ["../../../src/mtcnn/PNet.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,OAAM,SAAUC,IAAIA,CAACC,CAAc,EAAEC,MAAkB;EACrD,OAAOL,EAAE,CAACM,IAAI,CAAC;IAEb,IAAIC,GAAG,GAAGL,WAAW,CAACE,CAAC,EAAEC,MAAM,EAAE,IAAI,CAAC;IACtC,IAAMG,IAAI,GAAGP,SAAS,CAACM,GAAG,EAAEF,MAAM,CAACI,OAAO,EAAE,OAAO,CAAC;IACpD,IAAMC,GAAG,GAAGV,EAAE,CAACW,UAAU,CAACX,EAAE,CAACU,GAAG,CAACF,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,IAAMI,IAAI,GAAGZ,EAAE,CAACa,OAAO,CAACb,EAAE,CAACc,GAAG,CAACN,IAAI,EAAEE,GAAG,CAAC,EAAE,CAAC,CAAgB;IAC5D,IAAMK,OAAO,GAAGd,SAAS,CAACM,GAAG,EAAEF,MAAM,CAACW,OAAO,EAAE,OAAO,CAAC;IAEvD,OAAO;MAAEJ,IAAI,EAAAA,IAAA;MAAEG,OAAO,EAAAA;IAAA,CAAE;EAC1B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}