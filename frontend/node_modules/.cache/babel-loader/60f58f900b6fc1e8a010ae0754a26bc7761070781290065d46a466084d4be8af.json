{"ast": null, "code": "export function createFileSystem(fs) {\n  var requireFsError = '';\n  if (!fs) {\n    try {\n      fs = require('fs');\n    } catch (err) {\n      requireFsError = err.toString();\n    }\n  }\n  var readFile = fs ? function (filePath) {\n    return new Promise(function (res, rej) {\n      fs.readFile(filePath, function (err, buffer) {\n        return err ? rej(err) : res(buffer);\n      });\n    });\n  } : function () {\n    throw new Error(\"readFile - failed to require fs in nodejs environment with error: \" + requireFsError);\n  };\n  return {\n    readFile: readFile\n  };\n}", "map": {"version": 3, "names": ["createFileSystem", "fs", "requireFsError", "require", "err", "toString", "readFile", "filePath", "Promise", "res", "rej", "buffer", "Error"], "sources": ["../../../src/env/createFileSystem.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,gBAAgBA,CAACC,EAAQ;EAEvC,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAI,CAACD,EAAE,EAAE;IACP,IAAI;MACFA,EAAE,GAAGE,OAAO,CAAC,IAAI,CAAC;KACnB,CAAC,OAAOC,GAAG,EAAE;MACZF,cAAc,GAAGE,GAAG,CAACC,QAAQ,EAAE;;;EAInC,IAAMC,QAAQ,GAAGL,EAAE,GACf,UAASM,QAAgB;IACzB,OAAO,IAAIC,OAAO,CAAS,UAACC,GAAG,EAAEC,GAAG;MAClCT,EAAE,CAACK,QAAQ,CAACC,QAAQ,EAAE,UAASH,GAAQ,EAAEO,MAAc;QACrD,OAAOP,GAAG,GAAGM,GAAG,CAACN,GAAG,CAAC,GAAGK,GAAG,CAACE,MAAM,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,GACC;IACA,MAAM,IAAIC,KAAK,CAAC,uEAAqEV,cAAgB,CAAC;EACxG,CAAC;EAEH,OAAO;IACLI,QAAQ,EAAAA;GACT;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}