{"ast": null, "code": "var SeparableConvParams = /** @class */function () {\n  function SeparableConvParams(depthwise_filter, pointwise_filter, bias) {\n    this.depthwise_filter = depthwise_filter;\n    this.pointwise_filter = pointwise_filter;\n    this.bias = bias;\n  }\n  return SeparableConvParams;\n}();\nexport { SeparableConvParams };", "map": {"version": 3, "names": ["SeparableConvParams", "depthwise_filter", "pointwise_filter", "bias"], "sources": ["../../../src/common/types.ts"], "sourcesContent": [null], "mappings": "AAmBA,IAAAA,mBAAA;EACE,SAAAA,oBACSC,gBAA6B,EAC7BC,gBAA6B,EAC7BC,IAAiB;IAFjB,KAAAF,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,IAAI,GAAJA,IAAI;EACV;EACL,OAAAH,mBAAC;AAAD,CAAC,CAND", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}