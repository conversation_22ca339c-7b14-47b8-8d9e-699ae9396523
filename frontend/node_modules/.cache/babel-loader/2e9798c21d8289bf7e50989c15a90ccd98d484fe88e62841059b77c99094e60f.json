{"ast": null, "code": "export function seperateWeightMaps(weightMap) {\n  var featureExtractorMap = {};\n  var classifierMap = {};\n  Object.keys(weightMap).forEach(function (key) {\n    var map = key.startsWith('fc') ? classifierMap : featureExtractorMap;\n    map[key] = weightMap[key];\n  });\n  return {\n    featureExtractorMap: featureExtractorMap,\n    classifierMap: classifierMap\n  };\n}", "map": {"version": 3, "names": ["seperateWeightMaps", "weightMap", "featureExtractorMap", "classifierMap", "Object", "keys", "for<PERSON>ach", "key", "map", "startsWith"], "sources": ["../../../src/faceProcessor/util.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,kBAAkBA,CAACC,SAA4B;EAE7D,IAAMC,mBAAmB,GAAsB,EAAE;EACjD,IAAMC,aAAa,GAAsB,EAAE;EAE3CC,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,OAAO,CAAC,UAAAC,GAAG;IAChC,IAAMC,GAAG,GAAGD,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC,GAAGN,aAAa,GAAGD,mBAAmB;IACtEM,GAAG,CAACD,GAAG,CAAC,GAAGN,SAAS,CAACM,GAAG,CAAC;EAC3B,CAAC,CAAC;EAEF,OAAO;IAAEL,mBAAmB,EAAAA,mBAAA;IAAEC,aAAa,EAAAA;EAAA,CAAE;AAE/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}