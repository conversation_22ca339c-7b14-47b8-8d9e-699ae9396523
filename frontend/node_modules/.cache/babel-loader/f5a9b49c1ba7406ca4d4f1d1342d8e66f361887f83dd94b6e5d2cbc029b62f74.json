{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport mapValues from 'lodash/mapValues';\nimport every from 'lodash/every';\nimport { getTicksOfScale, parseScale, checkDomainOfScale, getBandSizeOfAxis } from './ChartUtils';\nimport { findChildByType } from './ReactUtils';\nimport { compareValues, getPercentValue } from './DataUtils';\nimport { Bar } from '../cartesian/Bar';\n\n/**\n * Calculate the scale function, position, width, height of axes\n * @param  {Object} props     Latest props\n * @param  {Object} axisMap   The configuration of axes\n * @param  {Object} offset    The offset of main part in the svg element\n * @param  {String} axisType  The type of axes, x-axis or y-axis\n * @param  {String} chartName The name of chart\n * @return {Object} Configuration\n */\nexport var formatAxisMap = function formatAxisMap(props, axisMap, offset, axisType, chartName) {\n  var width = props.width,\n    height = props.height,\n    layout = props.layout,\n    children = props.children;\n  var ids = Object.keys(axisMap);\n  var steps = {\n    left: offset.left,\n    leftMirror: offset.left,\n    right: width - offset.right,\n    rightMirror: width - offset.right,\n    top: offset.top,\n    topMirror: offset.top,\n    bottom: height - offset.bottom,\n    bottomMirror: height - offset.bottom\n  };\n  var hasBar = !!findChildByType(children, Bar);\n  return ids.reduce(function (result, id) {\n    var axis = axisMap[id];\n    var orientation = axis.orientation,\n      domain = axis.domain,\n      _axis$padding = axis.padding,\n      padding = _axis$padding === void 0 ? {} : _axis$padding,\n      mirror = axis.mirror,\n      reversed = axis.reversed;\n    var offsetKey = \"\".concat(orientation).concat(mirror ? 'Mirror' : '');\n    var calculatedPadding, range, x, y, needSpace;\n    if (axis.type === 'number' && (axis.padding === 'gap' || axis.padding === 'no-gap')) {\n      var diff = domain[1] - domain[0];\n      var smallestDistanceBetweenValues = Infinity;\n      var sortedValues = axis.categoricalDomain.sort(compareValues);\n      sortedValues.forEach(function (value, index) {\n        if (index > 0) {\n          smallestDistanceBetweenValues = Math.min((value || 0) - (sortedValues[index - 1] || 0), smallestDistanceBetweenValues);\n        }\n      });\n      if (Number.isFinite(smallestDistanceBetweenValues)) {\n        var smallestDistanceInPercent = smallestDistanceBetweenValues / diff;\n        var rangeWidth = axis.layout === 'vertical' ? offset.height : offset.width;\n        if (axis.padding === 'gap') {\n          calculatedPadding = smallestDistanceInPercent * rangeWidth / 2;\n        }\n        if (axis.padding === 'no-gap') {\n          var gap = getPercentValue(props.barCategoryGap, smallestDistanceInPercent * rangeWidth);\n          var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n          calculatedPadding = halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n        }\n      }\n    }\n    if (axisType === 'xAxis') {\n      range = [offset.left + (padding.left || 0) + (calculatedPadding || 0), offset.left + offset.width - (padding.right || 0) - (calculatedPadding || 0)];\n    } else if (axisType === 'yAxis') {\n      range = layout === 'horizontal' ? [offset.top + offset.height - (padding.bottom || 0), offset.top + (padding.top || 0)] : [offset.top + (padding.top || 0) + (calculatedPadding || 0), offset.top + offset.height - (padding.bottom || 0) - (calculatedPadding || 0)];\n    } else {\n      range = axis.range;\n    }\n    if (reversed) {\n      range = [range[1], range[0]];\n    }\n    var _parseScale = parseScale(axis, chartName, hasBar),\n      scale = _parseScale.scale,\n      realScaleType = _parseScale.realScaleType;\n    scale.domain(domain).range(range);\n    checkDomainOfScale(scale);\n    var ticks = getTicksOfScale(scale, _objectSpread(_objectSpread({}, axis), {}, {\n      realScaleType: realScaleType\n    }));\n    if (axisType === 'xAxis') {\n      needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n      x = offset.left;\n      y = steps[offsetKey] - needSpace * axis.height;\n    } else if (axisType === 'yAxis') {\n      needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n      x = steps[offsetKey] - needSpace * axis.width;\n      y = offset.top;\n    }\n    var finalAxis = _objectSpread(_objectSpread(_objectSpread({}, axis), ticks), {}, {\n      realScaleType: realScaleType,\n      x: x,\n      y: y,\n      scale: scale,\n      width: axisType === 'xAxis' ? offset.width : axis.width,\n      height: axisType === 'yAxis' ? offset.height : axis.height\n    });\n    finalAxis.bandSize = getBandSizeOfAxis(finalAxis, ticks);\n    if (!axis.hide && axisType === 'xAxis') {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.height;\n    } else if (!axis.hide) {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.width;\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, id, finalAxis));\n  }, {});\n};\nexport var rectWithPoints = function rectWithPoints(_ref, _ref2) {\n  var x1 = _ref.x,\n    y1 = _ref.y;\n  var x2 = _ref2.x,\n    y2 = _ref2.y;\n  return {\n    x: Math.min(x1, x2),\n    y: Math.min(y1, y2),\n    width: Math.abs(x2 - x1),\n    height: Math.abs(y2 - y1)\n  };\n};\n\n/**\n * Compute the x, y, width, and height of a box from two reference points.\n * @param  {Object} coords     x1, x2, y1, and y2\n * @return {Object} object\n */\nexport var rectWithCoords = function rectWithCoords(_ref3) {\n  var x1 = _ref3.x1,\n    y1 = _ref3.y1,\n    x2 = _ref3.x2,\n    y2 = _ref3.y2;\n  return rectWithPoints({\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  });\n};\nexport var ScaleHelper = /*#__PURE__*/function () {\n  function ScaleHelper(scale) {\n    _classCallCheck(this, ScaleHelper);\n    this.scale = scale;\n  }\n  return _createClass(ScaleHelper, [{\n    key: \"domain\",\n    get: function get() {\n      return this.scale.domain;\n    }\n  }, {\n    key: \"range\",\n    get: function get() {\n      return this.scale.range;\n    }\n  }, {\n    key: \"rangeMin\",\n    get: function get() {\n      return this.range()[0];\n    }\n  }, {\n    key: \"rangeMax\",\n    get: function get() {\n      return this.range()[1];\n    }\n  }, {\n    key: \"bandwidth\",\n    get: function get() {\n      return this.scale.bandwidth;\n    }\n  }, {\n    key: \"apply\",\n    value: function apply(value) {\n      var _ref4 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref4.bandAware,\n        position = _ref4.position;\n      if (value === undefined) {\n        return undefined;\n      }\n      if (position) {\n        switch (position) {\n          case 'start':\n            {\n              return this.scale(value);\n            }\n          case 'middle':\n            {\n              var offset = this.bandwidth ? this.bandwidth() / 2 : 0;\n              return this.scale(value) + offset;\n            }\n          case 'end':\n            {\n              var _offset = this.bandwidth ? this.bandwidth() : 0;\n              return this.scale(value) + _offset;\n            }\n          default:\n            {\n              return this.scale(value);\n            }\n        }\n      }\n      if (bandAware) {\n        var _offset2 = this.bandwidth ? this.bandwidth() / 2 : 0;\n        return this.scale(value) + _offset2;\n      }\n      return this.scale(value);\n    }\n  }, {\n    key: \"isInRange\",\n    value: function isInRange(value) {\n      var range = this.range();\n      var first = range[0];\n      var last = range[range.length - 1];\n      return first <= last ? value >= first && value <= last : value >= last && value <= first;\n    }\n  }], [{\n    key: \"create\",\n    value: function create(obj) {\n      return new ScaleHelper(obj);\n    }\n  }]);\n}();\n_defineProperty(ScaleHelper, \"EPS\", 1e-4);\nexport var createLabeledScales = function createLabeledScales(options) {\n  var scales = Object.keys(options).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, ScaleHelper.create(options[key])));\n  }, {});\n  return _objectSpread(_objectSpread({}, scales), {}, {\n    apply: function apply(coord) {\n      var _ref5 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref5.bandAware,\n        position = _ref5.position;\n      return mapValues(coord, function (value, label) {\n        return scales[label].apply(value, {\n          bandAware: bandAware,\n          position: position\n        });\n      });\n    },\n    isInRange: function isInRange(coord) {\n      return every(coord, function (value, label) {\n        return scales[label].isInRange(value);\n      });\n    }\n  });\n};\n\n/** Normalizes the angle so that 0 <= angle < 180.\n * @param {number} angle Angle in degrees.\n * @return {number} the normalized angle with a value of at least 0 and never greater or equal to 180. */\nexport function normalizeAngle(angle) {\n  return (angle % 180 + 180) % 180;\n}\n\n/** Calculates the width of the largest horizontal line that fits inside a rectangle that is displayed at an angle.\n * @param {Object} size Width and height of the text in a horizontal position.\n * @param {number} angle Angle in degrees in which the text is displayed.\n * @return {number} The width of the largest horizontal line that fits inside a rectangle that is displayed at an angle.\n */\nexport var getAngledRectangleWidth = function getAngledRectangleWidth(_ref6) {\n  var width = _ref6.width,\n    height = _ref6.height;\n  var angle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Ensure angle is >= 0 && < 180\n  var normalizedAngle = normalizeAngle(angle);\n  var angleRadians = normalizedAngle * Math.PI / 180;\n\n  /* Depending on the height and width of the rectangle, we may need to use different formulas to calculate the angled\n   * width. This threshold defines when each formula should kick in. */\n  var angleThreshold = Math.atan(height / width);\n  var angledWidth = angleRadians > angleThreshold && angleRadians < Math.PI - angleThreshold ? height / Math.sin(angleRadians) : width / Math.cos(angleRadians);\n  return Math.abs(angledWidth);\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "obj", "value", "_toPrimitive", "toPrimitive", "call", "String", "Number", "mapValues", "every", "getTicksOfScale", "parseScale", "checkDomainOfScale", "getBandSizeOfAxis", "findChildByType", "compareValues", "getPercentValue", "Bar", "formatAxisMap", "axisMap", "offset", "axisType", "chartName", "width", "height", "layout", "children", "ids", "steps", "left", "leftMirror", "right", "rightMirror", "top", "topMirror", "bottom", "bottomMirror", "<PERSON><PERSON><PERSON>", "reduce", "result", "id", "axis", "orientation", "domain", "_axis$padding", "padding", "mirror", "reversed", "offsetKey", "concat", "calculatedPadding", "range", "x", "y", "needSpace", "type", "diff", "smallestDistanceBetweenValues", "Infinity", "sortedValues", "categoricalDomain", "sort", "index", "Math", "min", "isFinite", "smallestDistanceInPercent", "rangeWidth", "gap", "barCategoryGap", "halfBand", "_parseScale", "scale", "realScaleType", "ticks", "finalAxis", "bandSize", "hide", "rectWithPoints", "_ref", "_ref2", "x1", "y1", "x2", "y2", "abs", "rectWithCoords", "_ref3", "ScaleHelper", "get", "bandwidth", "_ref4", "undefined", "bandAware", "position", "_offset", "_offset2", "isInRange", "first", "last", "create", "createLabeledScales", "options", "scales", "res", "coord", "_ref5", "label", "normalizeAngle", "angle", "getAngledRectangleWidth", "_ref6", "normalizedAngle", "angleRadians", "PI", "angleThreshold", "atan", "angled<PERSON>id<PERSON>", "sin", "cos"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/recharts/es6/util/CartesianUtils.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport mapValues from 'lodash/mapValues';\nimport every from 'lodash/every';\nimport { getTicksOfScale, parseScale, checkDomainOfScale, getBandSizeOfAxis } from './ChartUtils';\nimport { findChildByType } from './ReactUtils';\nimport { compareValues, getPercentValue } from './DataUtils';\nimport { Bar } from '../cartesian/Bar';\n\n/**\n * Calculate the scale function, position, width, height of axes\n * @param  {Object} props     Latest props\n * @param  {Object} axisMap   The configuration of axes\n * @param  {Object} offset    The offset of main part in the svg element\n * @param  {String} axisType  The type of axes, x-axis or y-axis\n * @param  {String} chartName The name of chart\n * @return {Object} Configuration\n */\nexport var formatAxisMap = function formatAxisMap(props, axisMap, offset, axisType, chartName) {\n  var width = props.width,\n    height = props.height,\n    layout = props.layout,\n    children = props.children;\n  var ids = Object.keys(axisMap);\n  var steps = {\n    left: offset.left,\n    leftMirror: offset.left,\n    right: width - offset.right,\n    rightMirror: width - offset.right,\n    top: offset.top,\n    topMirror: offset.top,\n    bottom: height - offset.bottom,\n    bottomMirror: height - offset.bottom\n  };\n  var hasBar = !!findChildByType(children, Bar);\n  return ids.reduce(function (result, id) {\n    var axis = axisMap[id];\n    var orientation = axis.orientation,\n      domain = axis.domain,\n      _axis$padding = axis.padding,\n      padding = _axis$padding === void 0 ? {} : _axis$padding,\n      mirror = axis.mirror,\n      reversed = axis.reversed;\n    var offsetKey = \"\".concat(orientation).concat(mirror ? 'Mirror' : '');\n    var calculatedPadding, range, x, y, needSpace;\n    if (axis.type === 'number' && (axis.padding === 'gap' || axis.padding === 'no-gap')) {\n      var diff = domain[1] - domain[0];\n      var smallestDistanceBetweenValues = Infinity;\n      var sortedValues = axis.categoricalDomain.sort(compareValues);\n      sortedValues.forEach(function (value, index) {\n        if (index > 0) {\n          smallestDistanceBetweenValues = Math.min((value || 0) - (sortedValues[index - 1] || 0), smallestDistanceBetweenValues);\n        }\n      });\n      if (Number.isFinite(smallestDistanceBetweenValues)) {\n        var smallestDistanceInPercent = smallestDistanceBetweenValues / diff;\n        var rangeWidth = axis.layout === 'vertical' ? offset.height : offset.width;\n        if (axis.padding === 'gap') {\n          calculatedPadding = smallestDistanceInPercent * rangeWidth / 2;\n        }\n        if (axis.padding === 'no-gap') {\n          var gap = getPercentValue(props.barCategoryGap, smallestDistanceInPercent * rangeWidth);\n          var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n          calculatedPadding = halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n        }\n      }\n    }\n    if (axisType === 'xAxis') {\n      range = [offset.left + (padding.left || 0) + (calculatedPadding || 0), offset.left + offset.width - (padding.right || 0) - (calculatedPadding || 0)];\n    } else if (axisType === 'yAxis') {\n      range = layout === 'horizontal' ? [offset.top + offset.height - (padding.bottom || 0), offset.top + (padding.top || 0)] : [offset.top + (padding.top || 0) + (calculatedPadding || 0), offset.top + offset.height - (padding.bottom || 0) - (calculatedPadding || 0)];\n    } else {\n      range = axis.range;\n    }\n    if (reversed) {\n      range = [range[1], range[0]];\n    }\n    var _parseScale = parseScale(axis, chartName, hasBar),\n      scale = _parseScale.scale,\n      realScaleType = _parseScale.realScaleType;\n    scale.domain(domain).range(range);\n    checkDomainOfScale(scale);\n    var ticks = getTicksOfScale(scale, _objectSpread(_objectSpread({}, axis), {}, {\n      realScaleType: realScaleType\n    }));\n    if (axisType === 'xAxis') {\n      needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n      x = offset.left;\n      y = steps[offsetKey] - needSpace * axis.height;\n    } else if (axisType === 'yAxis') {\n      needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n      x = steps[offsetKey] - needSpace * axis.width;\n      y = offset.top;\n    }\n    var finalAxis = _objectSpread(_objectSpread(_objectSpread({}, axis), ticks), {}, {\n      realScaleType: realScaleType,\n      x: x,\n      y: y,\n      scale: scale,\n      width: axisType === 'xAxis' ? offset.width : axis.width,\n      height: axisType === 'yAxis' ? offset.height : axis.height\n    });\n    finalAxis.bandSize = getBandSizeOfAxis(finalAxis, ticks);\n    if (!axis.hide && axisType === 'xAxis') {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.height;\n    } else if (!axis.hide) {\n      steps[offsetKey] += (needSpace ? -1 : 1) * finalAxis.width;\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, id, finalAxis));\n  }, {});\n};\nexport var rectWithPoints = function rectWithPoints(_ref, _ref2) {\n  var x1 = _ref.x,\n    y1 = _ref.y;\n  var x2 = _ref2.x,\n    y2 = _ref2.y;\n  return {\n    x: Math.min(x1, x2),\n    y: Math.min(y1, y2),\n    width: Math.abs(x2 - x1),\n    height: Math.abs(y2 - y1)\n  };\n};\n\n/**\n * Compute the x, y, width, and height of a box from two reference points.\n * @param  {Object} coords     x1, x2, y1, and y2\n * @return {Object} object\n */\nexport var rectWithCoords = function rectWithCoords(_ref3) {\n  var x1 = _ref3.x1,\n    y1 = _ref3.y1,\n    x2 = _ref3.x2,\n    y2 = _ref3.y2;\n  return rectWithPoints({\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  });\n};\nexport var ScaleHelper = /*#__PURE__*/function () {\n  function ScaleHelper(scale) {\n    _classCallCheck(this, ScaleHelper);\n    this.scale = scale;\n  }\n  return _createClass(ScaleHelper, [{\n    key: \"domain\",\n    get: function get() {\n      return this.scale.domain;\n    }\n  }, {\n    key: \"range\",\n    get: function get() {\n      return this.scale.range;\n    }\n  }, {\n    key: \"rangeMin\",\n    get: function get() {\n      return this.range()[0];\n    }\n  }, {\n    key: \"rangeMax\",\n    get: function get() {\n      return this.range()[1];\n    }\n  }, {\n    key: \"bandwidth\",\n    get: function get() {\n      return this.scale.bandwidth;\n    }\n  }, {\n    key: \"apply\",\n    value: function apply(value) {\n      var _ref4 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref4.bandAware,\n        position = _ref4.position;\n      if (value === undefined) {\n        return undefined;\n      }\n      if (position) {\n        switch (position) {\n          case 'start':\n            {\n              return this.scale(value);\n            }\n          case 'middle':\n            {\n              var offset = this.bandwidth ? this.bandwidth() / 2 : 0;\n              return this.scale(value) + offset;\n            }\n          case 'end':\n            {\n              var _offset = this.bandwidth ? this.bandwidth() : 0;\n              return this.scale(value) + _offset;\n            }\n          default:\n            {\n              return this.scale(value);\n            }\n        }\n      }\n      if (bandAware) {\n        var _offset2 = this.bandwidth ? this.bandwidth() / 2 : 0;\n        return this.scale(value) + _offset2;\n      }\n      return this.scale(value);\n    }\n  }, {\n    key: \"isInRange\",\n    value: function isInRange(value) {\n      var range = this.range();\n      var first = range[0];\n      var last = range[range.length - 1];\n      return first <= last ? value >= first && value <= last : value >= last && value <= first;\n    }\n  }], [{\n    key: \"create\",\n    value: function create(obj) {\n      return new ScaleHelper(obj);\n    }\n  }]);\n}();\n_defineProperty(ScaleHelper, \"EPS\", 1e-4);\nexport var createLabeledScales = function createLabeledScales(options) {\n  var scales = Object.keys(options).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, ScaleHelper.create(options[key])));\n  }, {});\n  return _objectSpread(_objectSpread({}, scales), {}, {\n    apply: function apply(coord) {\n      var _ref5 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        bandAware = _ref5.bandAware,\n        position = _ref5.position;\n      return mapValues(coord, function (value, label) {\n        return scales[label].apply(value, {\n          bandAware: bandAware,\n          position: position\n        });\n      });\n    },\n    isInRange: function isInRange(coord) {\n      return every(coord, function (value, label) {\n        return scales[label].isInRange(value);\n      });\n    }\n  });\n};\n\n/** Normalizes the angle so that 0 <= angle < 180.\n * @param {number} angle Angle in degrees.\n * @return {number} the normalized angle with a value of at least 0 and never greater or equal to 180. */\nexport function normalizeAngle(angle) {\n  return (angle % 180 + 180) % 180;\n}\n\n/** Calculates the width of the largest horizontal line that fits inside a rectangle that is displayed at an angle.\n * @param {Object} size Width and height of the text in a horizontal position.\n * @param {number} angle Angle in degrees in which the text is displayed.\n * @return {number} The width of the largest horizontal line that fits inside a rectangle that is displayed at an angle.\n */\nexport var getAngledRectangleWidth = function getAngledRectangleWidth(_ref6) {\n  var width = _ref6.width,\n    height = _ref6.height;\n  var angle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Ensure angle is >= 0 && < 180\n  var normalizedAngle = normalizeAngle(angle);\n  var angleRadians = normalizedAngle * Math.PI / 180;\n\n  /* Depending on the height and width of the rectangle, we may need to use different formulas to calculate the angled\n   * width. This threshold defines when each formula should kick in. */\n  var angleThreshold = Math.atan(height / width);\n  var angledWidth = angleRadians > angleThreshold && angleRadians < Math.PI - angleThreshold ? height / Math.sin(angleRadians) : width / Math.cos(angleRadians);\n  return Math.abs(angledWidth);\n};"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,cAAc,CAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASQ,YAAYA,CAACf,WAAW,EAAEgB,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACF,WAAW,CAACH,SAAS,EAAEmB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACF,WAAW,EAAEiB,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAC5R,SAASkB,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGV,MAAM,CAACW,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIR,MAAM,CAACY,qBAAqB,EAAE;IAAE,IAAI9B,CAAC,GAAGkB,MAAM,CAACY,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAK3B,CAAC,GAAGA,CAAC,CAAC+B,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOT,MAAM,CAACc,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACZ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEa,CAAC,CAACK,IAAI,CAACC,KAAK,CAACN,CAAC,EAAE5B,CAAC,CAAC;EAAE;EAAE,OAAO4B,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,SAAS,CAACvB,MAAM,EAAEc,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIQ,SAAS,CAACT,CAAC,CAAC,GAAGS,SAAS,CAACT,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACP,MAAM,CAACU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACS,OAAO,CAAC,UAAUV,CAAC,EAAE;MAAEW,eAAe,CAACZ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGT,MAAM,CAACqB,yBAAyB,GAAGrB,MAAM,CAACsB,gBAAgB,CAACd,CAAC,EAAER,MAAM,CAACqB,yBAAyB,CAACX,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACP,MAAM,CAACU,CAAC,CAAC,CAAC,CAACS,OAAO,CAAC,UAAUV,CAAC,EAAE;MAAET,MAAM,CAACC,cAAc,CAACO,CAAC,EAAEC,CAAC,EAAET,MAAM,CAACc,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASY,eAAeA,CAACG,GAAG,EAAEpB,GAAG,EAAEqB,KAAK,EAAE;EAAErB,GAAG,GAAGD,cAAc,CAACC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIoB,GAAG,EAAE;IAAEvB,MAAM,CAACC,cAAc,CAACsB,GAAG,EAAEpB,GAAG,EAAE;MAAEqB,KAAK,EAAEA,KAAK;MAAE3B,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEwB,GAAG,CAACpB,GAAG,CAAC,GAAGqB,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASrB,cAAcA,CAACQ,CAAC,EAAE;EAAE,IAAIhB,CAAC,GAAG+B,YAAY,CAACf,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI7B,OAAO,CAACa,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAAS+B,YAAYA,CAACf,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI5B,OAAO,CAAC6B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAAC3B,MAAM,CAAC2C,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlB,CAAC,EAAE;IAAE,IAAId,CAAC,GAAGc,CAAC,CAACmB,IAAI,CAACjB,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI5B,OAAO,CAACa,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIJ,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKmB,CAAC,GAAGmB,MAAM,GAAGC,MAAM,EAAEnB,CAAC,CAAC;AAAE;AAC3T,OAAOoB,SAAS,MAAM,kBAAkB;AACxC,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,eAAe,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,cAAc;AACjG,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,aAAa,EAAEC,eAAe,QAAQ,aAAa;AAC5D,SAASC,GAAG,QAAQ,kBAAkB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAC/C,KAAK,EAAEgD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EAC7F,IAAIC,KAAK,GAAGpD,KAAK,CAACoD,KAAK;IACrBC,MAAM,GAAGrD,KAAK,CAACqD,MAAM;IACrBC,MAAM,GAAGtD,KAAK,CAACsD,MAAM;IACrBC,QAAQ,GAAGvD,KAAK,CAACuD,QAAQ;EAC3B,IAAIC,GAAG,GAAGjD,MAAM,CAACW,IAAI,CAAC8B,OAAO,CAAC;EAC9B,IAAIS,KAAK,GAAG;IACVC,IAAI,EAAET,MAAM,CAACS,IAAI;IACjBC,UAAU,EAAEV,MAAM,CAACS,IAAI;IACvBE,KAAK,EAAER,KAAK,GAAGH,MAAM,CAACW,KAAK;IAC3BC,WAAW,EAAET,KAAK,GAAGH,MAAM,CAACW,KAAK;IACjCE,GAAG,EAAEb,MAAM,CAACa,GAAG;IACfC,SAAS,EAAEd,MAAM,CAACa,GAAG;IACrBE,MAAM,EAAEX,MAAM,GAAGJ,MAAM,CAACe,MAAM;IAC9BC,YAAY,EAAEZ,MAAM,GAAGJ,MAAM,CAACe;EAChC,CAAC;EACD,IAAIE,MAAM,GAAG,CAAC,CAACvB,eAAe,CAACY,QAAQ,EAAET,GAAG,CAAC;EAC7C,OAAOU,GAAG,CAACW,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;IACtC,IAAIC,IAAI,GAAGtB,OAAO,CAACqB,EAAE,CAAC;IACtB,IAAIE,WAAW,GAAGD,IAAI,CAACC,WAAW;MAChCC,MAAM,GAAGF,IAAI,CAACE,MAAM;MACpBC,aAAa,GAAGH,IAAI,CAACI,OAAO;MAC5BA,OAAO,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,aAAa;MACvDE,MAAM,GAAGL,IAAI,CAACK,MAAM;MACpBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IAC1B,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACP,WAAW,CAAC,CAACO,MAAM,CAACH,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC;IACrE,IAAII,iBAAiB,EAAEC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,SAAS;IAC7C,IAAIb,IAAI,CAACc,IAAI,KAAK,QAAQ,KAAKd,IAAI,CAACI,OAAO,KAAK,KAAK,IAAIJ,IAAI,CAACI,OAAO,KAAK,QAAQ,CAAC,EAAE;MACnF,IAAIW,IAAI,GAAGb,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;MAChC,IAAIc,6BAA6B,GAAGC,QAAQ;MAC5C,IAAIC,YAAY,GAAGlB,IAAI,CAACmB,iBAAiB,CAACC,IAAI,CAAC9C,aAAa,CAAC;MAC7D4C,YAAY,CAAC9D,OAAO,CAAC,UAAUK,KAAK,EAAE4D,KAAK,EAAE;QAC3C,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbL,6BAA6B,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC9D,KAAK,IAAI,CAAC,KAAKyD,YAAY,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEL,6BAA6B,CAAC;QACxH;MACF,CAAC,CAAC;MACF,IAAIlD,MAAM,CAAC0D,QAAQ,CAACR,6BAA6B,CAAC,EAAE;QAClD,IAAIS,yBAAyB,GAAGT,6BAA6B,GAAGD,IAAI;QACpE,IAAIW,UAAU,GAAG1B,IAAI,CAAChB,MAAM,KAAK,UAAU,GAAGL,MAAM,CAACI,MAAM,GAAGJ,MAAM,CAACG,KAAK;QAC1E,IAAIkB,IAAI,CAACI,OAAO,KAAK,KAAK,EAAE;UAC1BK,iBAAiB,GAAGgB,yBAAyB,GAAGC,UAAU,GAAG,CAAC;QAChE;QACA,IAAI1B,IAAI,CAACI,OAAO,KAAK,QAAQ,EAAE;UAC7B,IAAIuB,GAAG,GAAGpD,eAAe,CAAC7C,KAAK,CAACkG,cAAc,EAAEH,yBAAyB,GAAGC,UAAU,CAAC;UACvF,IAAIG,QAAQ,GAAGJ,yBAAyB,GAAGC,UAAU,GAAG,CAAC;UACzDjB,iBAAiB,GAAGoB,QAAQ,GAAGF,GAAG,GAAG,CAACE,QAAQ,GAAGF,GAAG,IAAID,UAAU,GAAGC,GAAG;QAC1E;MACF;IACF;IACA,IAAI/C,QAAQ,KAAK,OAAO,EAAE;MACxB8B,KAAK,GAAG,CAAC/B,MAAM,CAACS,IAAI,IAAIgB,OAAO,CAAChB,IAAI,IAAI,CAAC,CAAC,IAAIqB,iBAAiB,IAAI,CAAC,CAAC,EAAE9B,MAAM,CAACS,IAAI,GAAGT,MAAM,CAACG,KAAK,IAAIsB,OAAO,CAACd,KAAK,IAAI,CAAC,CAAC,IAAImB,iBAAiB,IAAI,CAAC,CAAC,CAAC;IACtJ,CAAC,MAAM,IAAI7B,QAAQ,KAAK,OAAO,EAAE;MAC/B8B,KAAK,GAAG1B,MAAM,KAAK,YAAY,GAAG,CAACL,MAAM,CAACa,GAAG,GAAGb,MAAM,CAACI,MAAM,IAAIqB,OAAO,CAACV,MAAM,IAAI,CAAC,CAAC,EAAEf,MAAM,CAACa,GAAG,IAAIY,OAAO,CAACZ,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAACb,MAAM,CAACa,GAAG,IAAIY,OAAO,CAACZ,GAAG,IAAI,CAAC,CAAC,IAAIiB,iBAAiB,IAAI,CAAC,CAAC,EAAE9B,MAAM,CAACa,GAAG,GAAGb,MAAM,CAACI,MAAM,IAAIqB,OAAO,CAACV,MAAM,IAAI,CAAC,CAAC,IAAIe,iBAAiB,IAAI,CAAC,CAAC,CAAC;IACvQ,CAAC,MAAM;MACLC,KAAK,GAAGV,IAAI,CAACU,KAAK;IACpB;IACA,IAAIJ,QAAQ,EAAE;MACZI,KAAK,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,IAAIoB,WAAW,GAAG5D,UAAU,CAAC8B,IAAI,EAAEnB,SAAS,EAAEe,MAAM,CAAC;MACnDmC,KAAK,GAAGD,WAAW,CAACC,KAAK;MACzBC,aAAa,GAAGF,WAAW,CAACE,aAAa;IAC3CD,KAAK,CAAC7B,MAAM,CAACA,MAAM,CAAC,CAACQ,KAAK,CAACA,KAAK,CAAC;IACjCvC,kBAAkB,CAAC4D,KAAK,CAAC;IACzB,IAAIE,KAAK,GAAGhE,eAAe,CAAC8D,KAAK,EAAE7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8C,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5EgC,aAAa,EAAEA;IACjB,CAAC,CAAC,CAAC;IACH,IAAIpD,QAAQ,KAAK,OAAO,EAAE;MACxBiC,SAAS,GAAGZ,WAAW,KAAK,KAAK,IAAI,CAACI,MAAM,IAAIJ,WAAW,KAAK,QAAQ,IAAII,MAAM;MAClFM,CAAC,GAAGhC,MAAM,CAACS,IAAI;MACfwB,CAAC,GAAGzB,KAAK,CAACoB,SAAS,CAAC,GAAGM,SAAS,GAAGb,IAAI,CAACjB,MAAM;IAChD,CAAC,MAAM,IAAIH,QAAQ,KAAK,OAAO,EAAE;MAC/BiC,SAAS,GAAGZ,WAAW,KAAK,MAAM,IAAI,CAACI,MAAM,IAAIJ,WAAW,KAAK,OAAO,IAAII,MAAM;MAClFM,CAAC,GAAGxB,KAAK,CAACoB,SAAS,CAAC,GAAGM,SAAS,GAAGb,IAAI,CAAClB,KAAK;MAC7C8B,CAAC,GAAGjC,MAAM,CAACa,GAAG;IAChB;IACA,IAAI0C,SAAS,GAAGhF,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8C,IAAI,CAAC,EAAEiC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/ED,aAAa,EAAEA,aAAa;MAC5BrB,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJmB,KAAK,EAAEA,KAAK;MACZjD,KAAK,EAAEF,QAAQ,KAAK,OAAO,GAAGD,MAAM,CAACG,KAAK,GAAGkB,IAAI,CAAClB,KAAK;MACvDC,MAAM,EAAEH,QAAQ,KAAK,OAAO,GAAGD,MAAM,CAACI,MAAM,GAAGiB,IAAI,CAACjB;IACtD,CAAC,CAAC;IACFmD,SAAS,CAACC,QAAQ,GAAG/D,iBAAiB,CAAC8D,SAAS,EAAED,KAAK,CAAC;IACxD,IAAI,CAACjC,IAAI,CAACoC,IAAI,IAAIxD,QAAQ,KAAK,OAAO,EAAE;MACtCO,KAAK,CAACoB,SAAS,CAAC,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIqB,SAAS,CAACnD,MAAM;IAC7D,CAAC,MAAM,IAAI,CAACiB,IAAI,CAACoC,IAAI,EAAE;MACrBjD,KAAK,CAACoB,SAAS,CAAC,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIqB,SAAS,CAACpD,KAAK;IAC5D;IACA,OAAO5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEzC,eAAe,CAAC,CAAC,CAAC,EAAE0C,EAAE,EAAEmC,SAAS,CAAC,CAAC;EACzF,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AACD,OAAO,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC/D,IAAIC,EAAE,GAAGF,IAAI,CAAC3B,CAAC;IACb8B,EAAE,GAAGH,IAAI,CAAC1B,CAAC;EACb,IAAI8B,EAAE,GAAGH,KAAK,CAAC5B,CAAC;IACdgC,EAAE,GAAGJ,KAAK,CAAC3B,CAAC;EACd,OAAO;IACLD,CAAC,EAAEW,IAAI,CAACC,GAAG,CAACiB,EAAE,EAAEE,EAAE,CAAC;IACnB9B,CAAC,EAAEU,IAAI,CAACC,GAAG,CAACkB,EAAE,EAAEE,EAAE,CAAC;IACnB7D,KAAK,EAAEwC,IAAI,CAACsB,GAAG,CAACF,EAAE,GAAGF,EAAE,CAAC;IACxBzD,MAAM,EAAEuC,IAAI,CAACsB,GAAG,CAACD,EAAE,GAAGF,EAAE;EAC1B,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIN,EAAE,GAAGM,KAAK,CAACN,EAAE;IACfC,EAAE,GAAGK,KAAK,CAACL,EAAE;IACbC,EAAE,GAAGI,KAAK,CAACJ,EAAE;IACbC,EAAE,GAAGG,KAAK,CAACH,EAAE;EACf,OAAON,cAAc,CAAC;IACpB1B,CAAC,EAAE6B,EAAE;IACL5B,CAAC,EAAE6B;EACL,CAAC,EAAE;IACD9B,CAAC,EAAE+B,EAAE;IACL9B,CAAC,EAAE+B;EACL,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAII,WAAW,GAAG,aAAa,YAAY;EAChD,SAASA,WAAWA,CAAChB,KAAK,EAAE;IAC1B3G,eAAe,CAAC,IAAI,EAAE2H,WAAW,CAAC;IAClC,IAAI,CAAChB,KAAK,GAAGA,KAAK;EACpB;EACA,OAAO1F,YAAY,CAAC0G,WAAW,EAAE,CAAC;IAChC3G,GAAG,EAAE,QAAQ;IACb4G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAAC7B,MAAM;IAC1B;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,OAAO;IACZ4G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAACrB,KAAK;IACzB;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,UAAU;IACf4G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,UAAU;IACf4G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,WAAW;IAChB4G,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAACkB,SAAS;IAC7B;EACF,CAAC,EAAE;IACD7G,GAAG,EAAE,OAAO;IACZqB,KAAK,EAAE,SAASR,KAAKA,CAACQ,KAAK,EAAE;MAC3B,IAAIyF,KAAK,GAAG/F,SAAS,CAACvB,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAAC,CAAC,CAAC,KAAKgG,SAAS,GAAGhG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChFiG,SAAS,GAAGF,KAAK,CAACE,SAAS;QAC3BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MAC3B,IAAI5F,KAAK,KAAK0F,SAAS,EAAE;QACvB,OAAOA,SAAS;MAClB;MACA,IAAIE,QAAQ,EAAE;QACZ,QAAQA,QAAQ;UACd,KAAK,OAAO;YACV;cACE,OAAO,IAAI,CAACtB,KAAK,CAACtE,KAAK,CAAC;YAC1B;UACF,KAAK,QAAQ;YACX;cACE,IAAIkB,MAAM,GAAG,IAAI,CAACsE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;cACtD,OAAO,IAAI,CAAClB,KAAK,CAACtE,KAAK,CAAC,GAAGkB,MAAM;YACnC;UACF,KAAK,KAAK;YACR;cACE,IAAI2E,OAAO,GAAG,IAAI,CAACL,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,CAAC;cACnD,OAAO,IAAI,CAAClB,KAAK,CAACtE,KAAK,CAAC,GAAG6F,OAAO;YACpC;UACF;YACE;cACE,OAAO,IAAI,CAACvB,KAAK,CAACtE,KAAK,CAAC;YAC1B;QACJ;MACF;MACA,IAAI2F,SAAS,EAAE;QACb,IAAIG,QAAQ,GAAG,IAAI,CAACN,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACxD,OAAO,IAAI,CAAClB,KAAK,CAACtE,KAAK,CAAC,GAAG8F,QAAQ;MACrC;MACA,OAAO,IAAI,CAACxB,KAAK,CAACtE,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE;IACDrB,GAAG,EAAE,WAAW;IAChBqB,KAAK,EAAE,SAAS+F,SAASA,CAAC/F,KAAK,EAAE;MAC/B,IAAIiD,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;MACxB,IAAI+C,KAAK,GAAG/C,KAAK,CAAC,CAAC,CAAC;MACpB,IAAIgD,IAAI,GAAGhD,KAAK,CAACA,KAAK,CAAC9E,MAAM,GAAG,CAAC,CAAC;MAClC,OAAO6H,KAAK,IAAIC,IAAI,GAAGjG,KAAK,IAAIgG,KAAK,IAAIhG,KAAK,IAAIiG,IAAI,GAAGjG,KAAK,IAAIiG,IAAI,IAAIjG,KAAK,IAAIgG,KAAK;IAC1F;EACF,CAAC,CAAC,EAAE,CAAC;IACHrH,GAAG,EAAE,QAAQ;IACbqB,KAAK,EAAE,SAASkG,MAAMA,CAACnG,GAAG,EAAE;MAC1B,OAAO,IAAIuF,WAAW,CAACvF,GAAG,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHH,eAAe,CAAC0F,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;AACzC,OAAO,IAAIa,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,OAAO,EAAE;EACrE,IAAIC,MAAM,GAAG7H,MAAM,CAACW,IAAI,CAACiH,OAAO,CAAC,CAAChE,MAAM,CAAC,UAAUkE,GAAG,EAAE3H,GAAG,EAAE;IAC3D,OAAOc,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE1G,eAAe,CAAC,CAAC,CAAC,EAAEjB,GAAG,EAAE2G,WAAW,CAACY,MAAM,CAACE,OAAO,CAACzH,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9G,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOc,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4G,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;IAClD7G,KAAK,EAAE,SAASA,KAAKA,CAAC+G,KAAK,EAAE;MAC3B,IAAIC,KAAK,GAAG9G,SAAS,CAACvB,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAAC,CAAC,CAAC,KAAKgG,SAAS,GAAGhG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChFiG,SAAS,GAAGa,KAAK,CAACb,SAAS;QAC3BC,QAAQ,GAAGY,KAAK,CAACZ,QAAQ;MAC3B,OAAOtF,SAAS,CAACiG,KAAK,EAAE,UAAUvG,KAAK,EAAEyG,KAAK,EAAE;QAC9C,OAAOJ,MAAM,CAACI,KAAK,CAAC,CAACjH,KAAK,CAACQ,KAAK,EAAE;UAChC2F,SAAS,EAAEA,SAAS;UACpBC,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDG,SAAS,EAAE,SAASA,SAASA,CAACQ,KAAK,EAAE;MACnC,OAAOhG,KAAK,CAACgG,KAAK,EAAE,UAAUvG,KAAK,EAAEyG,KAAK,EAAE;QAC1C,OAAOJ,MAAM,CAACI,KAAK,CAAC,CAACV,SAAS,CAAC/F,KAAK,CAAC;MACvC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,SAAS0G,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,CAACA,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,KAAK,EAAE;EAC3E,IAAIxF,KAAK,GAAGwF,KAAK,CAACxF,KAAK;IACrBC,MAAM,GAAGuF,KAAK,CAACvF,MAAM;EACvB,IAAIqF,KAAK,GAAGjH,SAAS,CAACvB,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAAC,CAAC,CAAC,KAAKgG,SAAS,GAAGhG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjF;EACA,IAAIoH,eAAe,GAAGJ,cAAc,CAACC,KAAK,CAAC;EAC3C,IAAII,YAAY,GAAGD,eAAe,GAAGjD,IAAI,CAACmD,EAAE,GAAG,GAAG;;EAElD;AACF;EACE,IAAIC,cAAc,GAAGpD,IAAI,CAACqD,IAAI,CAAC5F,MAAM,GAAGD,KAAK,CAAC;EAC9C,IAAI8F,WAAW,GAAGJ,YAAY,GAAGE,cAAc,IAAIF,YAAY,GAAGlD,IAAI,CAACmD,EAAE,GAAGC,cAAc,GAAG3F,MAAM,GAAGuC,IAAI,CAACuD,GAAG,CAACL,YAAY,CAAC,GAAG1F,KAAK,GAAGwC,IAAI,CAACwD,GAAG,CAACN,YAAY,CAAC;EAC7J,OAAOlD,IAAI,CAACsB,GAAG,CAACgC,WAAW,CAAC;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}