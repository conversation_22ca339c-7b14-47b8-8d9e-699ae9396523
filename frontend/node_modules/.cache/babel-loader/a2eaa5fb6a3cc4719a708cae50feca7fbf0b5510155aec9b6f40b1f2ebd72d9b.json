{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nvar ComposableTask = /** @class */function () {\n  function ComposableTask() {}\n  ComposableTask.prototype.then = function (onfulfilled) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = onfulfilled;\n            return [4 /*yield*/, this.run()];\n          case 1:\n            return [2 /*return*/, _a.apply(void 0, [_b.sent()])];\n        }\n      });\n    });\n  };\n  ComposableTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        throw new Error('ComposableTask - run is not implemented');\n      });\n    });\n  };\n  return ComposableTask;\n}();\nexport { ComposableTask };", "map": {"version": 3, "names": ["ComposableTask", "prototype", "then", "onfulfilled", "_a", "run", "apply", "_b", "sent", "Error"], "sources": ["../../../src/globalApi/ComposableTask.ts"], "sourcesContent": [null], "mappings": ";AAAA,IAAAA,cAAA;EAAA,SAAAA,eAAA,GAWA;EATeA,cAAA,CAAAC,SAAA,CAAAC,IAAI,GAAjB,UACEC,WAA6C;;;;;;YAEtCC,EAAA,GAAAD,WAAW;YAAC,qBAAM,IAAI,CAACE,GAAG,EAAE;;YAAnC,sBAAOD,EAAA,CAAAE,KAAA,UAAYC,EAAA,CAAAC,IAAA,EAAgB,EAAC;;;;GACrC;EAEYR,cAAA,CAAAC,SAAA,CAAAI,GAAG,GAAhB;;;QACE,MAAM,IAAII,KAAK,CAAC,yCAAyC,CAAC;;;GAC3D;EACH,OAAAT,cAAC;AAAD,CAAC,CAXD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}