{"ast": null, "code": "import { TinyFaceDetector } from './TinyFaceDetector';\nexport * from './TinyFaceDetector';\nexport * from './TinyFaceDetectorOptions';\nexport function createTinyFaceDetector(weights) {\n  var net = new TinyFaceDetector();\n  net.extractWeights(weights);\n  return net;\n}", "map": {"version": 3, "names": ["TinyFaceDetector", "createTinyFaceDetector", "weights", "net", "extractWeights"], "sources": ["../../../src/tinyFaceDetector/index.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAErD,cAAc,oBAAoB;AAClC,cAAc,2BAA2B;AAEzC,OAAM,SAAUC,sBAAsBA,CAACC,OAAqB;EAC1D,IAAMC,GAAG,GAAG,IAAIH,gBAAgB,EAAE;EAClCG,GAAG,CAACC,cAAc,CAACF,OAAO,CAAC;EAC3B,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}