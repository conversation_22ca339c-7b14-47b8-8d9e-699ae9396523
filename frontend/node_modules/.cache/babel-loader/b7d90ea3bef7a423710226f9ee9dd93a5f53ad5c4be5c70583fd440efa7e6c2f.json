{"ast": null, "code": "import { Point } from '../classes';\nimport { FaceExpressions } from '../faceExpressionNet';\nimport { isWithFaceDetection } from '../factories/WithFaceDetection';\nimport { isWithFaceExpressions } from '../factories/WithFaceExpressions';\nimport { round } from '../utils';\nimport { DrawTextField } from './DrawTextField';\nexport function drawFaceExpressions(canvasArg, faceExpressions, minConfidence, textFieldAnchor) {\n  if (minConfidence === void 0) {\n    minConfidence = 0.1;\n  }\n  var faceExpressionsArray = Array.isArray(faceExpressions) ? faceExpressions : [faceExpressions];\n  faceExpressionsArray.forEach(function (e) {\n    var expr = e instanceof FaceExpressions ? e : isWithFaceExpressions(e) ? e.expressions : undefined;\n    if (!expr) {\n      throw new Error('drawFaceExpressions - expected faceExpressions to be FaceExpressions | WithFaceExpressions<{}> or array thereof');\n    }\n    var sorted = expr.asSortedArray();\n    var resultsToDisplay = sorted.filter(function (expr) {\n      return expr.probability > minConfidence;\n    });\n    var anchor = isWithFaceDetection(e) ? e.detection.box.bottomLeft : textFieldAnchor || new Point(0, 0);\n    var drawTextField = new DrawTextField(resultsToDisplay.map(function (expr) {\n      return expr.expression + \" (\" + round(expr.probability) + \")\";\n    }), anchor);\n    drawTextField.draw(canvasArg);\n  });\n}", "map": {"version": 3, "names": ["Point", "FaceExpressions", "isWithFaceDetection", "isWithFaceExpressions", "round", "DrawTextField", "drawFaceExpressions", "canvasArg", "faceExpressions", "minConfidence", "textFieldAnchor", "faceExpressionsArray", "Array", "isArray", "for<PERSON>ach", "e", "expr", "expressions", "undefined", "Error", "sorted", "asSortedA<PERSON>y", "resultsToDisplay", "filter", "probability", "anchor", "detection", "box", "bottomLeft", "drawTextField", "map", "expression", "draw"], "sources": ["../../../src/draw/drawFaceExpressions.ts"], "sourcesContent": [null], "mappings": "AAAA,SAAiBA,KAAK,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,qBAAqB,QAA6B,kCAAkC;AAC7F,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,aAAa,QAAQ,iBAAiB;AAI/C,OAAM,SAAUC,mBAAmBA,CACjCC,SAAqC,EACrCC,eAA2E,EAC3EC,aAAmB,EACnBC,eAAwB;EADxB,IAAAD,aAAA;IAAAA,aAAA,MAAmB;EAAA;EAGnB,IAAME,oBAAoB,GAAGC,KAAK,CAACC,OAAO,CAACL,eAAe,CAAC,GAAGA,eAAe,GAAG,CAACA,eAAe,CAAC;EAEjGG,oBAAoB,CAACG,OAAO,CAAC,UAAAC,CAAC;IAC5B,IAAMC,IAAI,GAAGD,CAAC,YAAYd,eAAe,GACrCc,CAAC,GACAZ,qBAAqB,CAACY,CAAC,CAAC,GAAGA,CAAC,CAACE,WAAW,GAAGC,SAAU;IAC1D,IAAI,CAACF,IAAI,EAAE;MACT,MAAM,IAAIG,KAAK,CAAC,iHAAiH,CAAC;;IAGpI,IAAMC,MAAM,GAAGJ,IAAI,CAACK,aAAa,EAAE;IACnC,IAAMC,gBAAgB,GAAGF,MAAM,CAACG,MAAM,CAAC,UAAAP,IAAI;MAAI,OAAAA,IAAI,CAACQ,WAAW,GAAGf,aAAa;IAAhC,CAAgC,CAAC;IAEhF,IAAMgB,MAAM,GAAGvB,mBAAmB,CAACa,CAAC,CAAC,GACjCA,CAAC,CAACW,SAAS,CAACC,GAAG,CAACC,UAAU,GACzBlB,eAAe,IAAI,IAAIV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;IAExC,IAAM6B,aAAa,GAAG,IAAIxB,aAAa,CACrCiB,gBAAgB,CAACQ,GAAG,CAAC,UAAAd,IAAI;MAAI,OAAGA,IAAI,CAACe,UAAU,UAAK3B,KAAK,CAACY,IAAI,CAACQ,WAAW,CAAC,MAAG;IAAjD,CAAiD,CAAC,EAC/EC,MAAM,CACP;IACDI,aAAa,CAACG,IAAI,CAACzB,SAAS,CAAC;EAC/B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}