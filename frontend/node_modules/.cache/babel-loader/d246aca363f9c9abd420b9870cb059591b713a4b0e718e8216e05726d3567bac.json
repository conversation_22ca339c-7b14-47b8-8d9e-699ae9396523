{"ast": null, "code": "import { __assign, __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { BoundingBox } from '../classes/BoundingBox';\nimport { ObjectDetection } from '../classes/ObjectDetection';\nimport { convLayer } from '../common';\nimport { toNetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { sigmoid } from '../ops';\nimport { nonMaxSuppression } from '../ops/nonMaxSuppression';\nimport { normalize } from '../ops/normalize';\nimport { validateConfig } from './config';\nimport { convWithBatchNorm } from './convWithBatchNorm';\nimport { depthwiseSeparableConv } from './depthwiseSeparableConv';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nimport { leaky } from './leaky';\nimport { TinyYolov2Options } from './TinyYolov2Options';\nvar TinyYolov2Base = /** @class */function (_super) {\n  __extends(TinyYolov2Base, _super);\n  function TinyYolov2Base(config) {\n    var _this = _super.call(this, 'TinyYolov2') || this;\n    validateConfig(config);\n    _this._config = config;\n    return _this;\n  }\n  Object.defineProperty(TinyYolov2Base.prototype, \"config\", {\n    get: function () {\n      return this._config;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(TinyYolov2Base.prototype, \"withClassScores\", {\n    get: function () {\n      return this.config.withClassScores || this.config.classes.length > 1;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(TinyYolov2Base.prototype, \"boxEncodingSize\", {\n    get: function () {\n      return 5 + (this.withClassScores ? this.config.classes.length : 0);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  TinyYolov2Base.prototype.runTinyYolov2 = function (x, params) {\n    var out = convWithBatchNorm(x, params.conv0);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = convWithBatchNorm(out, params.conv1);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = convWithBatchNorm(out, params.conv2);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = convWithBatchNorm(out, params.conv3);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = convWithBatchNorm(out, params.conv4);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = convWithBatchNorm(out, params.conv5);\n    out = tf.maxPool(out, [2, 2], [1, 1], 'same');\n    out = convWithBatchNorm(out, params.conv6);\n    out = convWithBatchNorm(out, params.conv7);\n    return convLayer(out, params.conv8, 'valid', false);\n  };\n  TinyYolov2Base.prototype.runMobilenet = function (x, params) {\n    var out = this.config.isFirstLayerConv2d ? leaky(convLayer(x, params.conv0, 'valid', false)) : depthwiseSeparableConv(x, params.conv0);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = depthwiseSeparableConv(out, params.conv1);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = depthwiseSeparableConv(out, params.conv2);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = depthwiseSeparableConv(out, params.conv3);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = depthwiseSeparableConv(out, params.conv4);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = depthwiseSeparableConv(out, params.conv5);\n    out = tf.maxPool(out, [2, 2], [1, 1], 'same');\n    out = params.conv6 ? depthwiseSeparableConv(out, params.conv6) : out;\n    out = params.conv7 ? depthwiseSeparableConv(out, params.conv7) : out;\n    return convLayer(out, params.conv8, 'valid', false);\n  };\n  TinyYolov2Base.prototype.forwardInput = function (input, inputSize) {\n    var _this = this;\n    var params = this.params;\n    if (!params) {\n      throw new Error('TinyYolov2 - load model before inference');\n    }\n    return tf.tidy(function () {\n      var batchTensor = input.toBatchTensor(inputSize, false).toFloat();\n      batchTensor = _this.config.meanRgb ? normalize(batchTensor, _this.config.meanRgb) : batchTensor;\n      batchTensor = batchTensor.div(tf.scalar(256));\n      return _this.config.withSeparableConvs ? _this.runMobilenet(batchTensor, params) : _this.runTinyYolov2(batchTensor, params);\n    });\n  };\n  TinyYolov2Base.prototype.forward = function (input, inputSize) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [4 /*yield*/, _a.apply(this, [_b.sent(), inputSize])];\n          case 2:\n            return [2 /*return*/, _b.sent()];\n        }\n      });\n    });\n  };\n  TinyYolov2Base.prototype.detect = function (input, forwardParams) {\n    if (forwardParams === void 0) {\n      forwardParams = {};\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var _a, inputSize, scoreThreshold, netInput, out, out0, inputDimensions, results, boxes, scores, classScores, classNames, indices, detections;\n      var _this = this;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = new TinyYolov2Options(forwardParams), inputSize = _a.inputSize, scoreThreshold = _a.scoreThreshold;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            netInput = _b.sent();\n            return [4 /*yield*/, this.forwardInput(netInput, inputSize)];\n          case 2:\n            out = _b.sent();\n            out0 = tf.tidy(function () {\n              return tf.unstack(out)[0].expandDims();\n            });\n            inputDimensions = {\n              width: netInput.getInputWidth(0),\n              height: netInput.getInputHeight(0)\n            };\n            return [4 /*yield*/, this.extractBoxes(out0, netInput.getReshapedInputDimensions(0), scoreThreshold)];\n          case 3:\n            results = _b.sent();\n            out.dispose();\n            out0.dispose();\n            boxes = results.map(function (res) {\n              return res.box;\n            });\n            scores = results.map(function (res) {\n              return res.score;\n            });\n            classScores = results.map(function (res) {\n              return res.classScore;\n            });\n            classNames = results.map(function (res) {\n              return _this.config.classes[res.label];\n            });\n            indices = nonMaxSuppression(boxes.map(function (box) {\n              return box.rescale(inputSize);\n            }), scores, this.config.iouThreshold, true);\n            detections = indices.map(function (idx) {\n              return new ObjectDetection(scores[idx], classScores[idx], classNames[idx], boxes[idx], inputDimensions);\n            });\n            return [2 /*return*/, detections];\n        }\n      });\n    });\n  };\n  TinyYolov2Base.prototype.getDefaultModelName = function () {\n    return '';\n  };\n  TinyYolov2Base.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMap(weightMap, this.config);\n  };\n  TinyYolov2Base.prototype.extractParams = function (weights) {\n    var filterSizes = this.config.filterSizes || TinyYolov2Base.DEFAULT_FILTER_SIZES;\n    var numFilters = filterSizes ? filterSizes.length : undefined;\n    if (numFilters !== 7 && numFilters !== 8 && numFilters !== 9) {\n      throw new Error(\"TinyYolov2 - expected 7 | 8 | 9 convolutional filters, but found \" + numFilters + \" filterSizes in config\");\n    }\n    return extractParams(weights, this.config, this.boxEncodingSize, filterSizes);\n  };\n  TinyYolov2Base.prototype.extractBoxes = function (outputTensor, inputBlobDimensions, scoreThreshold) {\n    return __awaiter(this, void 0, void 0, function () {\n      var width, height, inputSize, correctionFactorX, correctionFactorY, numCells, numBoxes, _a, boxesTensor, scoresTensor, classScoresTensor, results, scoresData, boxesData, row, col, anchor, score, ctX, ctY, width_1, height_1, x, y, pos, _b, classScore, label, _c;\n      var _this = this;\n      return __generator(this, function (_d) {\n        switch (_d.label) {\n          case 0:\n            width = inputBlobDimensions.width, height = inputBlobDimensions.height;\n            inputSize = Math.max(width, height);\n            correctionFactorX = inputSize / width;\n            correctionFactorY = inputSize / height;\n            numCells = outputTensor.shape[1];\n            numBoxes = this.config.anchors.length;\n            _a = tf.tidy(function () {\n              var reshaped = outputTensor.reshape([numCells, numCells, numBoxes, _this.boxEncodingSize]);\n              var boxes = reshaped.slice([0, 0, 0, 0], [numCells, numCells, numBoxes, 4]);\n              var scores = reshaped.slice([0, 0, 0, 4], [numCells, numCells, numBoxes, 1]);\n              var classScores = _this.withClassScores ? tf.softmax(reshaped.slice([0, 0, 0, 5], [numCells, numCells, numBoxes, _this.config.classes.length]), 3) : tf.scalar(0);\n              return [boxes, scores, classScores];\n            }), boxesTensor = _a[0], scoresTensor = _a[1], classScoresTensor = _a[2];\n            results = [];\n            return [4 /*yield*/, scoresTensor.array()];\n          case 1:\n            scoresData = _d.sent();\n            return [4 /*yield*/, boxesTensor.array()];\n          case 2:\n            boxesData = _d.sent();\n            row = 0;\n            _d.label = 3;\n          case 3:\n            if (!(row < numCells)) return [3 /*break*/, 12];\n            col = 0;\n            _d.label = 4;\n          case 4:\n            if (!(col < numCells)) return [3 /*break*/, 11];\n            anchor = 0;\n            _d.label = 5;\n          case 5:\n            if (!(anchor < numBoxes)) return [3 /*break*/, 10];\n            score = sigmoid(scoresData[row][col][anchor][0]);\n            if (!(!scoreThreshold || score > scoreThreshold)) return [3 /*break*/, 9];\n            ctX = (col + sigmoid(boxesData[row][col][anchor][0])) / numCells * correctionFactorX;\n            ctY = (row + sigmoid(boxesData[row][col][anchor][1])) / numCells * correctionFactorY;\n            width_1 = Math.exp(boxesData[row][col][anchor][2]) * this.config.anchors[anchor].x / numCells * correctionFactorX;\n            height_1 = Math.exp(boxesData[row][col][anchor][3]) * this.config.anchors[anchor].y / numCells * correctionFactorY;\n            x = ctX - width_1 / 2;\n            y = ctY - height_1 / 2;\n            pos = {\n              row: row,\n              col: col,\n              anchor: anchor\n            };\n            if (!this.withClassScores) return [3 /*break*/, 7];\n            return [4 /*yield*/, this.extractPredictedClass(classScoresTensor, pos)];\n          case 6:\n            _c = _d.sent();\n            return [3 /*break*/, 8];\n          case 7:\n            _c = {\n              classScore: 1,\n              label: 0\n            };\n            _d.label = 8;\n          case 8:\n            _b = _c, classScore = _b.classScore, label = _b.label;\n            results.push(__assign({\n              box: new BoundingBox(x, y, x + width_1, y + height_1),\n              score: score,\n              classScore: score * classScore,\n              label: label\n            }, pos));\n            _d.label = 9;\n          case 9:\n            anchor++;\n            return [3 /*break*/, 5];\n          case 10:\n            col++;\n            return [3 /*break*/, 4];\n          case 11:\n            row++;\n            return [3 /*break*/, 3];\n          case 12:\n            boxesTensor.dispose();\n            scoresTensor.dispose();\n            classScoresTensor.dispose();\n            return [2 /*return*/, results];\n        }\n      });\n    });\n  };\n  TinyYolov2Base.prototype.extractPredictedClass = function (classesTensor, pos) {\n    return __awaiter(this, void 0, void 0, function () {\n      var row, col, anchor, classesData;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            row = pos.row, col = pos.col, anchor = pos.anchor;\n            return [4 /*yield*/, classesTensor.array()];\n          case 1:\n            classesData = _a.sent();\n            return [2 /*return*/, Array(this.config.classes.length).fill(0).map(function (_, i) {\n              return classesData[row][col][anchor][i];\n            }).map(function (classScore, label) {\n              return {\n                classScore: classScore,\n                label: label\n              };\n            }).reduce(function (max, curr) {\n              return max.classScore > curr.classScore ? max : curr;\n            })];\n        }\n      });\n    });\n  };\n  TinyYolov2Base.DEFAULT_FILTER_SIZES = [3, 16, 32, 64, 128, 256, 512, 1024, 1024];\n  return TinyYolov2Base;\n}(NeuralNetwork);\nexport { TinyYolov2Base };", "map": {"version": 3, "names": ["tf", "BoundingBox", "ObjectDetection", "convLayer", "toNetInput", "NeuralNetwork", "sigmoid", "nonMaxSuppression", "normalize", "validateConfig", "convWithBatchNorm", "depthwiseSeparableConv", "extractParams", "extractParamsFromWeigthMap", "leaky", "TinyYolov2Options", "TinyYolov2Base", "_super", "__extends", "config", "_this", "call", "_config", "Object", "defineProperty", "prototype", "get", "withClassScores", "classes", "length", "runTinyYolov2", "x", "params", "out", "conv0", "maxPool", "conv1", "conv2", "conv3", "conv4", "conv5", "conv6", "conv7", "conv8", "runMobilenet", "isFirstLayerConv2d", "forwardInput", "input", "inputSize", "Error", "tidy", "batchTensor", "toBatchTensor", "toFloat", "meanRgb", "div", "scalar", "withSeparableConvs", "forward", "_a", "apply", "_b", "sent", "detect", "forwardParams", "scoreThreshold", "netInput", "out0", "unstack", "expandDims", "inputDimensions", "width", "getInputWidth", "height", "getInputHeight", "extractBoxes", "getReshapedInputDimensions", "results", "dispose", "boxes", "map", "res", "box", "scores", "score", "classScores", "classScore", "classNames", "label", "indices", "rescale", "iouThreshold", "detections", "idx", "getDefaultModelName", "weightMap", "weights", "filterSizes", "DEFAULT_FILTER_SIZES", "numFilters", "undefined", "boxEncodingSize", "outputTensor", "inputBlobDimensions", "Math", "max", "correctionFactorX", "correctionFactorY", "num<PERSON>ells", "shape", "numBoxes", "anchors", "reshaped", "reshape", "slice", "softmax", "boxesTensor", "scoresTensor", "classScoresTensor", "array", "scoresData", "_d", "boxesData", "row", "col", "anchor", "ctX", "ctY", "width_1", "exp", "height_1", "y", "pos", "extractPredictedClass", "_c", "push", "__assign", "classesTensor", "classesData", "Array", "fill", "_", "i", "reduce", "curr"], "sources": ["../../../src/tinyYolov2/TinyYolov2Base.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,WAAW,QAAQ,wBAAwB;AAEpD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,SAAS,QAAQ,WAAW;AAErC,SAASC,UAAU,QAAQ,QAAQ;AAGnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,OAAO,QAAQ,QAAQ;AAChC,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAA2BC,cAAc,QAAQ,UAAU;AAC3D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAA6BC,iBAAiB,QAAQ,qBAAqB;AAG3E,IAAAC,cAAA,0BAAAC,MAAA;EAAoCC,SAAA,CAAAF,cAAA,EAAAC,MAAA;EAQlC,SAAAD,eAAYG,MAAwB;IAApC,IAAAC,KAAA,GACEH,MAAA,CAAAI,IAAA,OAAM,YAAY,CAAC;IACnBZ,cAAc,CAACU,MAAM,CAAC;IACtBC,KAAI,CAACE,OAAO,GAAGH,MAAM;;EACvB;EAEAI,MAAA,CAAAC,cAAA,CAAWR,cAAA,CAAAS,SAAA,UAAM;SAAjB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACJ,OAAO;IACrB,CAAC;;;;EAEDC,MAAA,CAAAC,cAAA,CAAWR,cAAA,CAAAS,SAAA,mBAAe;SAA1B,SAAAC,CAAA;MACE,OAAO,IAAI,CAACP,MAAM,CAACQ,eAAe,IAAI,IAAI,CAACR,MAAM,CAACS,OAAO,CAACC,MAAM,GAAG,CAAC;IACtE,CAAC;;;;EAEDN,MAAA,CAAAC,cAAA,CAAWR,cAAA,CAAAS,SAAA,mBAAe;SAA1B,SAAAC,CAAA;MACE,OAAO,CAAC,IAAI,IAAI,CAACC,eAAe,GAAG,IAAI,CAACR,MAAM,CAACS,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC;IACpE,CAAC;;;;EAEMb,cAAA,CAAAS,SAAA,CAAAK,aAAa,GAApB,UAAqBC,CAAc,EAAEC,MAAkC;IAErE,IAAIC,GAAG,GAAGvB,iBAAiB,CAACqB,CAAC,EAAEC,MAAM,CAACE,KAAK,CAAC;IAC5CD,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACI,KAAK,CAAC;IAC1CH,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACK,KAAK,CAAC;IAC1CJ,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACM,KAAK,CAAC;IAC1CL,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACO,KAAK,CAAC;IAC1CN,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACQ,KAAK,CAAC;IAC1CP,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACS,KAAK,CAAC;IAC1CR,GAAG,GAAGvB,iBAAiB,CAACuB,GAAG,EAAED,MAAM,CAACU,KAAK,CAAC;IAE1C,OAAOvC,SAAS,CAAC8B,GAAG,EAAED,MAAM,CAACW,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;EACrD,CAAC;EAEM3B,cAAA,CAAAS,SAAA,CAAAmB,YAAY,GAAnB,UAAoBb,CAAc,EAAEC,MAAuB;IAEzD,IAAIC,GAAG,GAAG,IAAI,CAACd,MAAM,CAAC0B,kBAAkB,GACpC/B,KAAK,CAACX,SAAS,CAAC4B,CAAC,EAAEC,MAAM,CAACE,KAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,GAC/DvB,sBAAsB,CAACoB,CAAC,EAAEC,MAAM,CAACE,KAA4B,CAAC;IAClED,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGtB,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACI,KAAK,CAAC;IAC/CH,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGtB,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACK,KAAK,CAAC;IAC/CJ,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGtB,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACM,KAAK,CAAC;IAC/CL,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGtB,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACO,KAAK,CAAC;IAC/CN,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGtB,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACQ,KAAK,CAAC;IAC/CP,GAAG,GAAGjC,EAAE,CAACmC,OAAO,CAACF,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGD,MAAM,CAACS,KAAK,GAAG9B,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACS,KAAK,CAAC,GAAGR,GAAG;IACpEA,GAAG,GAAGD,MAAM,CAACU,KAAK,GAAG/B,sBAAsB,CAACsB,GAAG,EAAED,MAAM,CAACU,KAAK,CAAC,GAAGT,GAAG;IAEpE,OAAO9B,SAAS,CAAC8B,GAAG,EAAED,MAAM,CAACW,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;EACrD,CAAC;EAEM3B,cAAA,CAAAS,SAAA,CAAAqB,YAAY,GAAnB,UAAoBC,KAAe,EAAEC,SAAiB;IAAtD,IAAA5B,KAAA;IAEU,IAAAY,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIiB,KAAK,CAAC,0CAA0C,CAAC;;IAG7D,OAAOjD,EAAE,CAACkD,IAAI,CAAC;MAEb,IAAIC,WAAW,GAAGJ,KAAK,CAACK,aAAa,CAACJ,SAAS,EAAE,KAAK,CAAC,CAACK,OAAO,EAAE;MACjEF,WAAW,GAAG/B,KAAI,CAACD,MAAM,CAACmC,OAAO,GAC7B9C,SAAS,CAAC2C,WAAW,EAAE/B,KAAI,CAACD,MAAM,CAACmC,OAAO,CAAC,GAC3CH,WAAW;MACfA,WAAW,GAAGA,WAAW,CAACI,GAAG,CAACvD,EAAE,CAACwD,MAAM,CAAC,GAAG,CAAC,CAAgB;MAE5D,OAAOpC,KAAI,CAACD,MAAM,CAACsC,kBAAkB,GACjCrC,KAAI,CAACwB,YAAY,CAACO,WAAW,EAAEnB,MAAyB,CAAC,GACzDZ,KAAI,CAACU,aAAa,CAACqB,WAAW,EAAEnB,MAAoC,CAAC;IAC3E,CAAC,CAAC;EACJ,CAAC;EAEYhB,cAAA,CAAAS,SAAA,CAAAiC,OAAO,GAApB,UAAqBX,KAAgB,EAAEC,SAAiB;;;;;;YACzCW,EAAA,OAAI,CAACb,YAAY;YAAC,qBAAM1C,UAAU,CAAC2C,KAAK,CAAC;;YAA/C,qBAAMY,EAAA,CAAAC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAEd,SAAS,EAAC;;YAAlE,sBAAOa,EAAA,CAAAC,IAAA,EAA2D;;;;GACnE;EAEY9C,cAAA,CAAAS,SAAA,CAAAsC,MAAM,GAAnB,UAAoBhB,KAAgB,EAAEiB,aAAsC;IAAtC,IAAAA,aAAA;MAAAA,aAAA,KAAsC;IAAA;;;;;;;YAEpEL,EAAA,GAAgC,IAAI5C,iBAAiB,CAACiD,aAAa,CAAC,EAAlEhB,SAAS,GAAAW,EAAA,CAAAX,SAAA,EAAEiB,cAAc,GAAAN,EAAA,CAAAM,cAAA;YAEhB,qBAAM7D,UAAU,CAAC2C,KAAK,CAAC;;YAAlCmB,QAAQ,GAAGL,EAAA,CAAAC,IAAA,EAAuB;YAC5B,qBAAM,IAAI,CAAChB,YAAY,CAACoB,QAAQ,EAAElB,SAAS,CAAC;;YAAlDf,GAAG,GAAG4B,EAAA,CAAAC,IAAA,EAA4C;YAClDK,IAAI,GAAGnE,EAAE,CAACkD,IAAI,CAAC;cAAM,OAAAlD,EAAE,CAACoE,OAAO,CAACnC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACoC,UAAU,EAAE;YAA/B,CAA+B,CAAgB;YAEpEC,eAAe,GAAG;cACtBC,KAAK,EAAEL,QAAQ,CAACM,aAAa,CAAC,CAAC,CAAC;cAChCC,MAAM,EAAEP,QAAQ,CAACQ,cAAc,CAAC,CAAC;aAClC;YAEe,qBAAM,IAAI,CAACC,YAAY,CAACR,IAAI,EAAED,QAAQ,CAACU,0BAA0B,CAAC,CAAC,CAAC,EAAEX,cAAc,CAAC;;YAA/FY,OAAO,GAAGhB,EAAA,CAAAC,IAAA,EAAqF;YACrG7B,GAAG,CAAC6C,OAAO,EAAE;YACbX,IAAI,CAACW,OAAO,EAAE;YAERC,KAAK,GAAGF,OAAO,CAACG,GAAG,CAAC,UAAAC,GAAG;cAAI,OAAAA,GAAG,CAACC,GAAG;YAAP,CAAO,CAAC;YACnCC,MAAM,GAAGN,OAAO,CAACG,GAAG,CAAC,UAAAC,GAAG;cAAI,OAAAA,GAAG,CAACG,KAAK;YAAT,CAAS,CAAC;YACtCC,WAAW,GAAGR,OAAO,CAACG,GAAG,CAAC,UAAAC,GAAG;cAAI,OAAAA,GAAG,CAACK,UAAU;YAAd,CAAc,CAAC;YAChDC,UAAU,GAAGV,OAAO,CAACG,GAAG,CAAC,UAAAC,GAAG;cAAI,OAAA7D,KAAI,CAACD,MAAM,CAACS,OAAO,CAACqD,GAAG,CAACO,KAAK,CAAC;YAA9B,CAA8B,CAAC;YAE/DC,OAAO,GAAGlF,iBAAiB,CAC/BwE,KAAK,CAACC,GAAG,CAAC,UAAAE,GAAG;cAAI,OAAAA,GAAG,CAACQ,OAAO,CAAC1C,SAAS,CAAC;YAAtB,CAAsB,CAAC,EACxCmC,MAAM,EACN,IAAI,CAAChE,MAAM,CAACwE,YAAY,EACxB,IAAI,CACL;YAEKC,UAAU,GAAGH,OAAO,CAACT,GAAG,CAAC,UAAAa,GAAG;cAChC,WAAI3F,eAAe,CACjBiF,MAAM,CAACU,GAAG,CAAC,EACXR,WAAW,CAACQ,GAAG,CAAC,EAChBN,UAAU,CAACM,GAAG,CAAC,EACfd,KAAK,CAACc,GAAG,CAAC,EACVvB,eAAe,CAChB;YAND,CAMC,CACF;YAED,sBAAOsB,UAAU;;;;GAClB;EAES5E,cAAA,CAAAS,SAAA,CAAAqE,mBAAmB,GAA7B;IACE,OAAO,EAAE;EACX,CAAC;EAES9E,cAAA,CAAAS,SAAA,CAAAZ,0BAA0B,GAApC,UAAqCkF,SAA4B;IAC/D,OAAOlF,0BAA0B,CAACkF,SAAS,EAAE,IAAI,CAAC5E,MAAM,CAAC;EAC3D,CAAC;EAESH,cAAA,CAAAS,SAAA,CAAAb,aAAa,GAAvB,UAAwBoF,OAAqB;IAC3C,IAAMC,WAAW,GAAG,IAAI,CAAC9E,MAAM,CAAC8E,WAAW,IAAIjF,cAAc,CAACkF,oBAAoB;IAElF,IAAMC,UAAU,GAAGF,WAAW,GAAGA,WAAW,CAACpE,MAAM,GAAGuE,SAAS;IAC/D,IAAID,UAAU,KAAK,CAAC,IAAIA,UAAU,KAAK,CAAC,IAAIA,UAAU,KAAK,CAAC,EAAE;MAC5D,MAAM,IAAIlD,KAAK,CAAC,sEAAoEkD,UAAU,2BAAwB,CAAC;;IAEzH,OAAOvF,aAAa,CAACoF,OAAO,EAAE,IAAI,CAAC7E,MAAM,EAAE,IAAI,CAACkF,eAAe,EAAEJ,WAAW,CAAC;EAC/E,CAAC;EAEejF,cAAA,CAAAS,SAAA,CAAAkD,YAAY,GAA5B,UACE2B,YAAyB,EACzBC,mBAA+B,EAC/BtC,cAAuB;;;;;;;YAGfM,KAAK,GAAagC,mBAAmB,CAAAhC,KAAhC,EAAEE,MAAM,GAAK8B,mBAAmB,CAAA9B,MAAxB;YACfzB,SAAS,GAAGwD,IAAI,CAACC,GAAG,CAAClC,KAAK,EAAEE,MAAM,CAAC;YACnCiC,iBAAiB,GAAG1D,SAAS,GAAGuB,KAAK;YACrCoC,iBAAiB,GAAG3D,SAAS,GAAGyB,MAAM;YAEtCmC,QAAQ,GAAGN,YAAY,CAACO,KAAK,CAAC,CAAC,CAAC;YAChCC,QAAQ,GAAG,IAAI,CAAC3F,MAAM,CAAC4F,OAAO,CAAClF,MAAM;YAErC8B,EAAA,GAAiD3D,EAAE,CAACkD,IAAI,CAAC;cAC7D,IAAM8D,QAAQ,GAAGV,YAAY,CAACW,OAAO,CAAC,CAACL,QAAQ,EAAEA,QAAQ,EAAEE,QAAQ,EAAE1F,KAAI,CAACiF,eAAe,CAAC,CAAC;cAE3F,IAAMtB,KAAK,GAAGiC,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAACN,QAAQ,EAAEA,QAAQ,EAAEE,QAAQ,EAAE,CAAC,CAAC,CAAC;cAC7E,IAAM3B,MAAM,GAAG6B,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAACN,QAAQ,EAAEA,QAAQ,EAAEE,QAAQ,EAAE,CAAC,CAAC,CAAC;cAC9E,IAAMzB,WAAW,GAAGjE,KAAI,CAACO,eAAe,GACpC3B,EAAE,CAACmH,OAAO,CAACH,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAACN,QAAQ,EAAEA,QAAQ,EAAEE,QAAQ,EAAE1F,KAAI,CAACD,MAAM,CAACS,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GACvG7B,EAAE,CAACwD,MAAM,CAAC,CAAC,CAAC;cAChB,OAAO,CAACuB,KAAK,EAAEI,MAAM,EAAEE,WAAW,CAAC;YACrC,CAAC,CAAC,EATK+B,WAAW,GAAAzD,EAAA,KAAE0D,YAAY,GAAA1D,EAAA,KAAE2D,iBAAiB,GAAA3D,EAAA;YAW7CkB,OAAO,GAAG,EAAE;YAEC,qBAAMwC,YAAY,CAACE,KAAK,EAAE;;YAAvCC,UAAU,GAAGC,EAAA,CAAA3D,IAAA,EAA0B;YAC3B,qBAAMsD,WAAW,CAACG,KAAK,EAAE;;YAArCG,SAAS,GAAGD,EAAA,CAAA3D,IAAA,EAAyB;YAClC6D,GAAG,GAAG,CAAC;;;kBAAEA,GAAG,GAAGf,QAAQ;YACrBgB,GAAG,GAAG,CAAC;;;kBAAEA,GAAG,GAAGhB,QAAQ;YACrBiB,MAAM,GAAG,CAAC;;;kBAAEA,MAAM,GAAGf,QAAQ;YAE9B1B,KAAK,GAAG9E,OAAO,CAACkH,UAAU,CAACG,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClD,CAAC5D,cAAc,IAAImB,KAAK,GAAGnB,cAAc,GAAzC;YACI6D,GAAG,GAAI,CAACF,GAAG,GAAGtH,OAAO,CAACoH,SAAS,CAACC,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIjB,QAAQ,GAAIF,iBAAiB;YACtFqB,GAAG,GAAI,CAACJ,GAAG,GAAGrH,OAAO,CAACoH,SAAS,CAACC,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIjB,QAAQ,GAAID,iBAAiB;YACtFqB,OAAA,GAAUxB,IAAI,CAACyB,GAAG,CAACP,SAAS,CAACC,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC1G,MAAM,CAAC4F,OAAO,CAACc,MAAM,CAAC,CAAC9F,CAAC,GAAI6E,QAAQ,GAAIF,iBAAiB;YACnHwB,QAAA,GAAW1B,IAAI,CAACyB,GAAG,CAACP,SAAS,CAACC,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC1G,MAAM,CAAC4F,OAAO,CAACc,MAAM,CAAC,CAACM,CAAC,GAAIvB,QAAQ,GAAID,iBAAiB;YAEpH5E,CAAC,GAAI+F,GAAG,GAAIE,OAAK,GAAG,CAAG;YACvBG,CAAC,GAAIJ,GAAG,GAAIG,QAAM,GAAG,CAAG;YAExBE,GAAG,GAAG;cAAET,GAAG,EAAAA,GAAA;cAAEC,GAAG,EAAAA,GAAA;cAAEC,MAAM,EAAAA;YAAA,CAAE;iBACF,IAAI,CAAClG,eAAe,EAApB;YAC1B,qBAAM,IAAI,CAAC0G,qBAAqB,CAACf,iBAAgC,EAAEc,GAAG,CAAC;;YAAvEE,EAAA,GAAAb,EAAA,CAAA3D,IAAA,EAAuE;;;YACvEwE,EAAA;cAAEhD,UAAU,EAAE,CAAC;cAAEE,KAAK,EAAE;YAAC,CAAE;;;YAFzB3B,EAAA,GAAAyE,EAEyB,EAFvBhD,UAAU,GAAAzB,EAAA,CAAAyB,UAAA,EAAEE,KAAK,GAAA3B,EAAA,CAAA2B,KAAA;YAIzBX,OAAO,CAAC0D,IAAI,CAAAC,QAAA;cACVtD,GAAG,EAAE,IAAIjF,WAAW,CAAC8B,CAAC,EAAEoG,CAAC,EAAEpG,CAAC,GAAGiG,OAAK,EAAEG,CAAC,GAAGD,QAAM,CAAC;cACjD9C,KAAK,EAAEA,KAAK;cACZE,UAAU,EAAEF,KAAK,GAAGE,UAAU;cAC9BE,KAAK,EAAAA;YAAA,GACF4C,GAAG,EACN;;;YAvBkCP,MAAM,EAAG;;;YADjBD,GAAG,EAAG;;;YADRD,GAAG,EAAG;;;YA+BxCP,WAAW,CAACtC,OAAO,EAAE;YACrBuC,YAAY,CAACvC,OAAO,EAAE;YACtBwC,iBAAiB,CAACxC,OAAO,EAAE;YAE3B,sBAAOD,OAAO;;;;GACf;EAEa7D,cAAA,CAAAS,SAAA,CAAA4G,qBAAqB,GAAnC,UAAoCI,aAA0B,EAAEL,GAAiD;;;;;;YACvGT,GAAG,GAAkBS,GAAG,CAAAT,GAArB,EAAEC,GAAG,GAAaQ,GAAG,CAAAR,GAAhB,EAAEC,MAAM,GAAKO,GAAG,CAAAP,MAAR;YACJ,qBAAMY,aAAa,CAAClB,KAAK,EAAE;;YAAzCmB,WAAW,GAAG/E,EAAA,CAAAG,IAAA,EAA2B;YAC/C,sBAAO6E,KAAK,CAAC,IAAI,CAACxH,MAAM,CAACS,OAAO,CAACC,MAAM,CAAC,CAAC+G,IAAI,CAAC,CAAC,CAAC,CAC7C5D,GAAG,CAAC,UAAC6D,CAAC,EAAEC,CAAC;cAAK,OAAAJ,WAAW,CAACf,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACiB,CAAC,CAAC;YAAhC,CAAgC,CAAC,CAC/C9D,GAAG,CAAC,UAACM,UAAU,EAAEE,KAAK;cAAK,OAAC;gBAC3BF,UAAU,EAAAA,UAAA;gBACVE,KAAK,EAAAA;eACN;YAH2B,CAG1B,CAAC,CACFuD,MAAM,CAAC,UAACtC,GAAG,EAAEuC,IAAI;cAAK,OAAAvC,GAAG,CAACnB,UAAU,GAAG0D,IAAI,CAAC1D,UAAU,GAAGmB,GAAG,GAAGuC,IAAI;YAA7C,CAA6C,CAAC;;;;GACxE;EArOahI,cAAA,CAAAkF,oBAAoB,GAAG,CACnC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CACzC;EAoOH,OAAAlF,cAAC;CAAA,CAxOmCX,aAAa;SAApCW,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}