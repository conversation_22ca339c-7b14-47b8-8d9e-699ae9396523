{"ast": null, "code": "export function shuffleArray(inputArray) {\n  var array = inputArray.slice();\n  for (var i = array.length - 1; i > 0; i--) {\n    var j = Math.floor(Math.random() * (i + 1));\n    var x = array[i];\n    array[i] = array[j];\n    array[j] = x;\n  }\n  return array;\n}", "map": {"version": 3, "names": ["shuffle<PERSON><PERSON><PERSON>", "inputArray", "array", "slice", "i", "length", "j", "Math", "floor", "random", "x"], "sources": ["../../../src/ops/shuffleArray.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,YAAYA,CAACC,UAAiB;EAC5C,IAAMC,KAAK,GAAGD,UAAU,CAACE,KAAK,EAAE;EAChC,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAME,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,IAAIL,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,IAAMM,CAAC,GAAGR,KAAK,CAACE,CAAC,CAAC;IAClBF,KAAK,CAACE,CAAC,CAAC,GAAGF,KAAK,CAACI,CAAC,CAAC;IACnBJ,KAAK,CAACI,CAAC,CAAC,GAAGI,CAAC;;EAEhB,OAAOR,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}