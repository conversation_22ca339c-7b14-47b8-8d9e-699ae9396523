{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Card, CardContent, Button, Alert, TextField, CircularProgress, Avatar, Divider } from '@mui/material';\nimport { Login as LoginIcon, PersonAdd as PersonAddIcon, Logout as LogoutIcon, CloudUpload as CloudUploadIcon } from '@mui/icons-material';\nimport { supabase } from '../config/supabase';\nimport { supabaseService } from '../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SupabaseTestSimple = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState(null);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [uploadedImages, setUploadedImages] = useState([]);\n  useEffect(() => {\n    // Vérifier la session actuelle\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      var _session$user;\n      setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n    });\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      var _session$user2;\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const handleLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage(null);\n    try {\n      const {\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) throw error;\n      setMessage({\n        type: 'success',\n        text: 'Connexion réussie !'\n      });\n      setEmail('');\n      setPassword('');\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      setMessage({\n        type: 'error',\n        text: error instanceof Error ? error.message : 'Erreur de connexion'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSignup = async () => {\n    setLoading(true);\n    setMessage(null);\n    try {\n      const {\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password\n      });\n      if (error) throw error;\n      setMessage({\n        type: 'success',\n        text: 'Compte créé ! Vérifiez votre email pour confirmer votre compte.'\n      });\n      setEmail('');\n      setPassword('');\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setMessage({\n        type: 'error',\n        text: error instanceof Error ? error.message : 'Erreur d\\'inscription'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = async () => {\n    setLoading(true);\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) throw error;\n      setMessage({\n        type: 'success',\n        text: 'Déconnexion réussie !'\n      });\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n      setMessage({\n        type: 'error',\n        text: error instanceof Error ? error.message : 'Erreur de déconnexion'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFileUpload = async (file, type) => {\n    if (!file || !user) return;\n    setLoading(true);\n    setMessage(null);\n    try {\n      let imageUrl;\n      if (type === 'profile') {\n        imageUrl = await supabaseService.uploadProfileImage(user.id, file);\n      } else {\n        imageUrl = await supabaseService.uploadFaceImage(user.id, file);\n      }\n      setUploadedImages(prev => [...prev, imageUrl]);\n      setMessage({\n        type: 'success',\n        text: `Image ${type === 'profile' ? 'de profil' : 'faciale'} uploadée avec succès !`\n      });\n    } catch (error) {\n      console.error('Erreur upload:', error);\n      setMessage({\n        type: 'error',\n        text: `Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      align: \"center\",\n      children: \"Test Supabase Simple\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: message.type,\n      sx: {\n        mb: 3\n      },\n      onClose: () => setMessage(null),\n      children: message.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Configuration Supabase\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Project URL:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), \" \", process.env.REACT_APP_SUPABASE_URL || 'Non configuré', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 98\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Anon Key:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), \" \", process.env.REACT_APP_SUPABASE_ANON_KEY ? 'Configuré' : 'Non configuré']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), user ?\n    /*#__PURE__*/\n    // Utilisateur connecté\n    _jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Utilisateur Connect\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), \" \", user.email, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 53\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), \" \", user.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"error\",\n            startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 28\n            }, this),\n            onClick: handleLogout,\n            disabled: loading,\n            sx: {\n              mt: 2\n            },\n            children: \"Se D\\xE9connecter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Upload d'Images\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              component: \"label\",\n              startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 30\n              }, this),\n              disabled: loading,\n              sx: {\n                mr: 2\n              },\n              children: [\"Upload Image de Profil\", /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                hidden: true,\n                accept: \"image/*\",\n                onChange: e => {\n                  var _e$target$files;\n                  const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n                  if (file) handleFileUpload(file, 'profile');\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              component: \"label\",\n              startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 30\n              }, this),\n              disabled: loading,\n              children: [\"Upload Image Faciale\", /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                hidden: true,\n                accept: \"image/*\",\n                onChange: e => {\n                  var _e$target$files2;\n                  const file = (_e$target$files2 = e.target.files) === null || _e$target$files2 === void 0 ? void 0 : _e$target$files2[0];\n                  if (file) handleFileUpload(file, 'face');\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), loading && /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 27\n          }, this), uploadedImages.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Images upload\\xE9es (\", uploadedImages.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                flexWrap: 'wrap'\n              },\n              children: uploadedImages.map((url, index) => /*#__PURE__*/_jsxDEV(Avatar, {\n                src: url,\n                sx: {\n                  width: 80,\n                  height: 80\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    // Formulaire de connexion\n    _jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Authentification\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleLogin,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Mot de passe\",\n            type: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              display: 'flex',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              variant: \"contained\",\n              disabled: loading,\n              startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 40\n              }, this) : /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 73\n              }, this),\n              children: \"Se Connecter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: handleSignup,\n              disabled: loading,\n              startIcon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 30\n              }, this),\n              children: \"S'Inscrire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Instructions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"1. Cr\\xE9ez un compte ou connectez-vous\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 49\n          }, this), \"2. Une fois connect\\xE9, testez l'upload d'images\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 59\n          }, this), \"3. V\\xE9rifiez que les images apparaissent dans le bucket Supabase\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n};\n_s(SupabaseTestSimple, \"DoW12uwtssMelyJL3EfFTdUCwFE=\");\n_c = SupabaseTestSimple;\nexport default SupabaseTestSimple;\nvar _c;\n$RefreshReg$(_c, \"SupabaseTestSimple\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "CircularProgress", "Avatar", "Divider", "<PERSON><PERSON>", "LoginIcon", "PersonAdd", "PersonAddIcon", "Logout", "LogoutIcon", "CloudUpload", "CloudUploadIcon", "supabase", "supabaseService", "jsxDEV", "_jsxDEV", "SupabaseTestSimple", "_s", "user", "setUser", "loading", "setLoading", "message", "setMessage", "email", "setEmail", "password", "setPassword", "uploadedImages", "setUploadedImages", "auth", "getSession", "then", "data", "session", "_session$user", "subscription", "onAuthStateChange", "_event", "_session$user2", "unsubscribe", "handleLogin", "e", "preventDefault", "error", "signInWithPassword", "type", "text", "console", "Error", "handleSignup", "signUp", "handleLogout", "signOut", "handleFileUpload", "file", "imageUrl", "uploadProfileImage", "id", "uploadFaceImage", "prev", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "variant", "gutterBottom", "align", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "onClose", "process", "env", "REACT_APP_SUPABASE_URL", "REACT_APP_SUPABASE_ANON_KEY", "color", "startIcon", "onClick", "disabled", "mt", "component", "mr", "hidden", "accept", "onChange", "_e$target$files", "target", "files", "_e$target$files2", "size", "length", "display", "gap", "flexWrap", "map", "url", "index", "src", "width", "height", "onSubmit", "fullWidth", "label", "value", "margin", "required", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/SupabaseTestSimple.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Container,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Alert,\n  TextField,\n  CircularProgress,\n  Avatar,\n  Divider\n} from '@mui/material';\nimport {\n  Login as LoginIcon,\n  PersonAdd as PersonAddIcon,\n  Logout as LogoutIcon,\n  CloudUpload as CloudUploadIcon\n} from '@mui/icons-material';\nimport { supabase } from '../config/supabase';\nimport { supabaseService } from '../services/supabaseService';\nimport type { User } from '@supabase/supabase-js';\n\nconst SupabaseTestSimple: React.FC = () => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [uploadedImages, setUploadedImages] = useState<string[]>([]);\n\n  useEffect(() => {\n    // Vérifier la session actuelle\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null);\n    });\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user ?? null);\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage(null);\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) throw error;\n\n      setMessage({ type: 'success', text: 'Connexion réussie !' });\n      setEmail('');\n      setPassword('');\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      setMessage({ \n        type: 'error', \n        text: error instanceof Error ? error.message : 'Erreur de connexion' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSignup = async () => {\n    setLoading(true);\n    setMessage(null);\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n      });\n\n      if (error) throw error;\n\n      setMessage({ \n        type: 'success', \n        text: 'Compte créé ! Vérifiez votre email pour confirmer votre compte.' \n      });\n      setEmail('');\n      setPassword('');\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setMessage({ \n        type: 'error', \n        text: error instanceof Error ? error.message : 'Erreur d\\'inscription' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    setLoading(true);\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) throw error;\n      setMessage({ type: 'success', text: 'Déconnexion réussie !' });\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n      setMessage({ \n        type: 'error', \n        text: error instanceof Error ? error.message : 'Erreur de déconnexion' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFileUpload = async (file: File, type: 'profile' | 'face') => {\n    if (!file || !user) return;\n\n    setLoading(true);\n    setMessage(null);\n\n    try {\n      let imageUrl: string;\n      \n      if (type === 'profile') {\n        imageUrl = await supabaseService.uploadProfileImage(user.id, file);\n      } else {\n        imageUrl = await supabaseService.uploadFaceImage(user.id, file);\n      }\n\n      setUploadedImages(prev => [...prev, imageUrl]);\n      setMessage({ \n        type: 'success', \n        text: `Image ${type === 'profile' ? 'de profil' : 'faciale'} uploadée avec succès !` \n      });\n\n    } catch (error) {\n      console.error('Erreur upload:', error);\n      setMessage({ \n        type: 'error', \n        text: `Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}` \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container maxWidth=\"md\" sx={{ py: 4 }}>\n      <Typography variant=\"h3\" gutterBottom align=\"center\">\n        Test Supabase Simple\n      </Typography>\n\n      {message && (\n        <Alert severity={message.type} sx={{ mb: 3 }} onClose={() => setMessage(null)}>\n          {message.text}\n        </Alert>\n      )}\n\n      {/* Configuration */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Configuration Supabase\n          </Typography>\n          <Typography variant=\"body2\">\n            <strong>Project URL:</strong> {process.env.REACT_APP_SUPABASE_URL || 'Non configuré'}<br />\n            <strong>Anon Key:</strong> {process.env.REACT_APP_SUPABASE_ANON_KEY ? 'Configuré' : 'Non configuré'}\n          </Typography>\n        </CardContent>\n      </Card>\n\n      {user ? (\n        // Utilisateur connecté\n        <Box>\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Utilisateur Connecté\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Email:</strong> {user.email}<br />\n                <strong>ID:</strong> {user.id}\n              </Typography>\n              <Button\n                variant=\"contained\"\n                color=\"error\"\n                startIcon={<LogoutIcon />}\n                onClick={handleLogout}\n                disabled={loading}\n                sx={{ mt: 2 }}\n              >\n                Se Déconnecter\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Upload d'images */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Upload d'Images\n              </Typography>\n              \n              <Box sx={{ mb: 2 }}>\n                <Button\n                  variant=\"contained\"\n                  component=\"label\"\n                  startIcon={<CloudUploadIcon />}\n                  disabled={loading}\n                  sx={{ mr: 2 }}\n                >\n                  Upload Image de Profil\n                  <input\n                    type=\"file\"\n                    hidden\n                    accept=\"image/*\"\n                    onChange={(e) => {\n                      const file = e.target.files?.[0];\n                      if (file) handleFileUpload(file, 'profile');\n                    }}\n                  />\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  component=\"label\"\n                  startIcon={<CloudUploadIcon />}\n                  disabled={loading}\n                >\n                  Upload Image Faciale\n                  <input\n                    type=\"file\"\n                    hidden\n                    accept=\"image/*\"\n                    onChange={(e) => {\n                      const file = e.target.files?.[0];\n                      if (file) handleFileUpload(file, 'face');\n                    }}\n                  />\n                </Button>\n              </Box>\n\n              {loading && <CircularProgress size={20} />}\n\n              {uploadedImages.length > 0 && (\n                <Box>\n                  <Typography variant=\"subtitle1\" gutterBottom>\n                    Images uploadées ({uploadedImages.length})\n                  </Typography>\n                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                    {uploadedImages.map((url, index) => (\n                      <Avatar\n                        key={index}\n                        src={url}\n                        sx={{ width: 80, height: 80 }}\n                      />\n                    ))}\n                  </Box>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Box>\n      ) : (\n        // Formulaire de connexion\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Authentification\n            </Typography>\n            \n            <form onSubmit={handleLogin}>\n              <TextField\n                fullWidth\n                label=\"Email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                margin=\"normal\"\n                required\n              />\n              <TextField\n                fullWidth\n                label=\"Mot de passe\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                margin=\"normal\"\n                required\n              />\n              \n              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  disabled={loading}\n                  startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}\n                >\n                  Se Connecter\n                </Button>\n                \n                <Button\n                  variant=\"outlined\"\n                  onClick={handleSignup}\n                  disabled={loading}\n                  startIcon={<PersonAddIcon />}\n                >\n                  S'Inscrire\n                </Button>\n              </Box>\n            </form>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Instructions */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Instructions\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <Typography variant=\"body2\">\n            1. Créez un compte ou connectez-vous<br />\n            2. Une fois connecté, testez l'upload d'images<br />\n            3. Vérifiez que les images apparaissent dans le bucket Supabase\n          </Typography>\n        </CardContent>\n      </Card>\n    </Container>\n  );\n};\n\nexport default SupabaseTestSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9D,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAqD,IAAI,CAAC;EAChG,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAW,EAAE,CAAC;EAElEC,SAAS,CAAC,MAAM;IACd;IACAoB,QAAQ,CAACkB,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE;QAAEC;MAAQ;IAAE,CAAC,KAAK;MAAA,IAAAC,aAAA;MACzDhB,OAAO,EAAAgB,aAAA,GAACD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,IAAI,cAAAiB,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;IAChC,CAAC,CAAC;;IAEF;IACA,MAAM;MAAEF,IAAI,EAAE;QAAEG;MAAa;IAAE,CAAC,GAAGxB,QAAQ,CAACkB,IAAI,CAACO,iBAAiB,CAAC,CAACC,MAAM,EAAEJ,OAAO,KAAK;MAAA,IAAAK,cAAA;MACtFpB,OAAO,EAAAoB,cAAA,GAACL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,IAAI,cAAAqB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,MAAMH,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,WAAW,GAAG,MAAOC,CAAkB,IAAK;IAChDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBtB,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM;QAAEqB;MAAM,CAAC,GAAG,MAAMhC,QAAQ,CAACkB,IAAI,CAACe,kBAAkB,CAAC;QACvDrB,KAAK;QACLE;MACF,CAAC,CAAC;MAEF,IAAIkB,KAAK,EAAE,MAAMA,KAAK;MAEtBrB,UAAU,CAAC;QAAEuB,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAsB,CAAC,CAAC;MAC5DtB,QAAQ,CAAC,EAAE,CAAC;MACZE,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CrB,UAAU,CAAC;QACTuB,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACtB,OAAO,GAAG;MACjD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B7B,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM;QAAEqB;MAAM,CAAC,GAAG,MAAMhC,QAAQ,CAACkB,IAAI,CAACqB,MAAM,CAAC;QAC3C3B,KAAK;QACLE;MACF,CAAC,CAAC;MAEF,IAAIkB,KAAK,EAAE,MAAMA,KAAK;MAEtBrB,UAAU,CAAC;QACTuB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC,CAAC;MACFtB,QAAQ,CAAC,EAAE,CAAC;MACZE,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CrB,UAAU,CAAC;QACTuB,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACtB,OAAO,GAAG;MACjD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B/B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM;QAAEuB;MAAM,CAAC,GAAG,MAAMhC,QAAQ,CAACkB,IAAI,CAACuB,OAAO,CAAC,CAAC;MAC/C,IAAIT,KAAK,EAAE,MAAMA,KAAK;MACtBrB,UAAU,CAAC;QAAEuB,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAwB,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CrB,UAAU,CAAC;QACTuB,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACtB,OAAO,GAAG;MACjD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,gBAAgB,GAAG,MAAAA,CAAOC,IAAU,EAAET,IAAwB,KAAK;IACvE,IAAI,CAACS,IAAI,IAAI,CAACrC,IAAI,EAAE;IAEpBG,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAIiC,QAAgB;MAEpB,IAAIV,IAAI,KAAK,SAAS,EAAE;QACtBU,QAAQ,GAAG,MAAM3C,eAAe,CAAC4C,kBAAkB,CAACvC,IAAI,CAACwC,EAAE,EAAEH,IAAI,CAAC;MACpE,CAAC,MAAM;QACLC,QAAQ,GAAG,MAAM3C,eAAe,CAAC8C,eAAe,CAACzC,IAAI,CAACwC,EAAE,EAAEH,IAAI,CAAC;MACjE;MAEA1B,iBAAiB,CAAC+B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,QAAQ,CAAC,CAAC;MAC9CjC,UAAU,CAAC;QACTuB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,SAASD,IAAI,KAAK,SAAS,GAAG,WAAW,GAAG,SAAS;MAC7D,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCrB,UAAU,CAAC;QACTuB,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,4BAA4BH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACtB,OAAO,GAAG,iBAAiB;MAC9F,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA,CAACrB,SAAS;IAACmE,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACrCjD,OAAA,CAACpB,UAAU;MAACsE,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,KAAK,EAAC,QAAQ;MAAAH,QAAA,EAAC;IAErD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZjD,OAAO,iBACNP,OAAA,CAAChB,KAAK;MAACyE,QAAQ,EAAElD,OAAO,CAACwB,IAAK;MAACgB,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAACC,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC,IAAI,CAAE;MAAAyC,QAAA,EAC3E1C,OAAO,CAACyB;IAAI;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,eAGDxD,OAAA,CAACnB,IAAI;MAACkE,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,eAClBjD,OAAA,CAAClB,WAAW;QAAAmE,QAAA,gBACVjD,OAAA,CAACpB,UAAU;UAACsE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxD,OAAA,CAACpB,UAAU;UAACsE,OAAO,EAAC,OAAO;UAAAD,QAAA,gBACzBjD,OAAA;YAAAiD,QAAA,EAAQ;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACI,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,eAAe,eAAC9D,OAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3FxD,OAAA;YAAAiD,QAAA,EAAQ;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACI,OAAO,CAACC,GAAG,CAACE,2BAA2B,GAAG,WAAW,GAAG,eAAe;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAENrD,IAAI;IAAA;IACH;IACAH,OAAA,CAACtB,GAAG;MAAAuE,QAAA,gBACFjD,OAAA,CAACnB,IAAI;QAACkE,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,eAClBjD,OAAA,CAAClB,WAAW;UAAAmE,QAAA,gBACVjD,OAAA,CAACpB,UAAU;YAACsE,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxD,OAAA,CAACpB,UAAU;YAACsE,OAAO,EAAC,OAAO;YAAAD,QAAA,gBACzBjD,OAAA;cAAAiD,QAAA,EAAQ;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrD,IAAI,CAACM,KAAK,eAACT,OAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1CxD,OAAA;cAAAiD,QAAA,EAAQ;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrD,IAAI,CAACwC,EAAE;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACbxD,OAAA,CAACjB,MAAM;YACLmE,OAAO,EAAC,WAAW;YACnBc,KAAK,EAAC,OAAO;YACbC,SAAS,eAAEjE,OAAA,CAACN,UAAU;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BU,OAAO,EAAE7B,YAAa;YACtB8B,QAAQ,EAAE9D,OAAQ;YAClB0C,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAAnB,QAAA,EACf;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGPxD,OAAA,CAACnB,IAAI;QAACkE,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,eAClBjD,OAAA,CAAClB,WAAW;UAAAmE,QAAA,gBACVjD,OAAA,CAACpB,UAAU;YAACsE,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbxD,OAAA,CAACtB,GAAG;YAACqE,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACjBjD,OAAA,CAACjB,MAAM;cACLmE,OAAO,EAAC,WAAW;cACnBmB,SAAS,EAAC,OAAO;cACjBJ,SAAS,eAAEjE,OAAA,CAACJ,eAAe;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BW,QAAQ,EAAE9D,OAAQ;cAClB0C,EAAE,EAAE;gBAAEuB,EAAE,EAAE;cAAE,CAAE;cAAArB,QAAA,GACf,wBAEC,eAAAjD,OAAA;gBACE+B,IAAI,EAAC,MAAM;gBACXwC,MAAM;gBACNC,MAAM,EAAC,SAAS;gBAChBC,QAAQ,EAAG9C,CAAC,IAAK;kBAAA,IAAA+C,eAAA;kBACf,MAAMlC,IAAI,IAAAkC,eAAA,GAAG/C,CAAC,CAACgD,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;kBAChC,IAAIlC,IAAI,EAAED,gBAAgB,CAACC,IAAI,EAAE,SAAS,CAAC;gBAC7C;cAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAETxD,OAAA,CAACjB,MAAM;cACLmE,OAAO,EAAC,UAAU;cAClBmB,SAAS,EAAC,OAAO;cACjBJ,SAAS,eAAEjE,OAAA,CAACJ,eAAe;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BW,QAAQ,EAAE9D,OAAQ;cAAA4C,QAAA,GACnB,sBAEC,eAAAjD,OAAA;gBACE+B,IAAI,EAAC,MAAM;gBACXwC,MAAM;gBACNC,MAAM,EAAC,SAAS;gBAChBC,QAAQ,EAAG9C,CAAC,IAAK;kBAAA,IAAAkD,gBAAA;kBACf,MAAMrC,IAAI,IAAAqC,gBAAA,GAAGlD,CAAC,CAACgD,MAAM,CAACC,KAAK,cAAAC,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC;kBAChC,IAAIrC,IAAI,EAAED,gBAAgB,CAACC,IAAI,EAAE,MAAM,CAAC;gBAC1C;cAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELnD,OAAO,iBAAIL,OAAA,CAACd,gBAAgB;YAAC4F,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAEzC3C,cAAc,CAACkE,MAAM,GAAG,CAAC,iBACxB/E,OAAA,CAACtB,GAAG;YAAAuE,QAAA,gBACFjD,OAAA,CAACpB,UAAU;cAACsE,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,GAAC,uBACzB,EAACpC,cAAc,CAACkE,MAAM,EAAC,GAC3C;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxD,OAAA,CAACtB,GAAG;cAACqE,EAAE,EAAE;gBAAEiC,OAAO,EAAE,MAAM;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAjC,QAAA,EACpDpC,cAAc,CAACsE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC7BrF,OAAA,CAACb,MAAM;gBAELmG,GAAG,EAAEF,GAAI;gBACTrC,EAAE,EAAE;kBAAEwC,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG;cAAE,GAFzBH,KAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;IAAA;IAEN;IACAxD,OAAA,CAACnB,IAAI;MAAAoE,QAAA,eACHjD,OAAA,CAAClB,WAAW;QAAAmE,QAAA,gBACVjD,OAAA,CAACpB,UAAU;UAACsE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbxD,OAAA;UAAMyF,QAAQ,EAAE/D,WAAY;UAAAuB,QAAA,gBAC1BjD,OAAA,CAACf,SAAS;YACRyG,SAAS;YACTC,KAAK,EAAC,OAAO;YACb5D,IAAI,EAAC,OAAO;YACZ6D,KAAK,EAAEnF,KAAM;YACbgE,QAAQ,EAAG9C,CAAC,IAAKjB,QAAQ,CAACiB,CAAC,CAACgD,MAAM,CAACiB,KAAK,CAAE;YAC1CC,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFxD,OAAA,CAACf,SAAS;YACRyG,SAAS;YACTC,KAAK,EAAC,cAAc;YACpB5D,IAAI,EAAC,UAAU;YACf6D,KAAK,EAAEjF,QAAS;YAChB8D,QAAQ,EAAG9C,CAAC,IAAKf,WAAW,CAACe,CAAC,CAACgD,MAAM,CAACiB,KAAK,CAAE;YAC7CC,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFxD,OAAA,CAACtB,GAAG;YAACqE,EAAE,EAAE;cAAEqB,EAAE,EAAE,CAAC;cAAEY,OAAO,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAhC,QAAA,gBAC1CjD,OAAA,CAACjB,MAAM;cACLgD,IAAI,EAAC,QAAQ;cACbmB,OAAO,EAAC,WAAW;cACnBiB,QAAQ,EAAE9D,OAAQ;cAClB4D,SAAS,EAAE5D,OAAO,gBAAGL,OAAA,CAACd,gBAAgB;gBAAC4F,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxD,OAAA,CAACV,SAAS;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,EACrE;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETxD,OAAA,CAACjB,MAAM;cACLmE,OAAO,EAAC,UAAU;cAClBgB,OAAO,EAAE/B,YAAa;cACtBgC,QAAQ,EAAE9D,OAAQ;cAClB4D,SAAS,eAAEjE,OAAA,CAACR,aAAa;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,EAC9B;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGDxD,OAAA,CAACnB,IAAI;MAACkE,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAnB,QAAA,eAClBjD,OAAA,CAAClB,WAAW;QAAAmE,QAAA,gBACVjD,OAAA,CAACpB,UAAU;UAACsE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxD,OAAA,CAACZ,OAAO;UAAC2D,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BxD,OAAA,CAACpB,UAAU;UAACsE,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,yCACU,eAAAjD,OAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,qDACI,eAAAxD,OAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,sEAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACtD,EAAA,CAxTID,kBAA4B;AAAA8F,EAAA,GAA5B9F,kBAA4B;AA0TlC,eAAeA,kBAAkB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}