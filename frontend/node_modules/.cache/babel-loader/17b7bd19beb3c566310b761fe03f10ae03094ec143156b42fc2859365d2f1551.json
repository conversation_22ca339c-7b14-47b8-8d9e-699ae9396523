{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { SsdMobilenetv1 } from './SsdMobilenetv1';\nexport * from './SsdMobilenetv1';\nexport * from './SsdMobilenetv1Options';\nexport function createSsdMobilenetv1(weights) {\n  var net = new SsdMobilenetv1();\n  net.extractWeights(weights);\n  return net;\n}\nexport function createFaceDetectionNet(weights) {\n  return createSsdMobilenetv1(weights);\n}\n// alias for backward compatibily\nvar FaceDetectionNet = /** @class */function (_super) {\n  __extends(FaceDetectionNet, _super);\n  function FaceDetectionNet() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return FaceDetectionNet;\n}(SsdMobilenetv1);\nexport { FaceDetectionNet };", "map": {"version": 3, "names": ["SsdMobilenetv1", "createSsdMobilenetv1", "weights", "net", "extractWeights", "createFaceDetectionNet", "FaceDetectionNet", "_super", "__extends"], "sources": ["../../../src/ssdMobilenetv1/index.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,cAAc,QAAQ,kBAAkB;AAEjD,cAAc,kBAAkB;AAChC,cAAc,yBAAyB;AAEvC,OAAM,SAAUC,oBAAoBA,CAACC,OAAqB;EACxD,IAAMC,GAAG,GAAG,IAAIH,cAAc,EAAE;EAChCG,GAAG,CAACC,cAAc,CAACF,OAAO,CAAC;EAC3B,OAAOC,GAAG;AACZ;AAEA,OAAM,SAAUE,sBAAsBA,CAACH,OAAqB;EAC1D,OAAOD,oBAAoB,CAACC,OAAO,CAAC;AACtC;AAEA;AACA,IAAAI,gBAAA,0BAAAC,MAAA;EAAsCC,SAAA,CAAAF,gBAAA,EAAAC,MAAA;EAAtC,SAAAD,iBAAA;;EAAsD;EAAA,OAAAA,gBAAC;AAAD,CAAC,CAAjBN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}