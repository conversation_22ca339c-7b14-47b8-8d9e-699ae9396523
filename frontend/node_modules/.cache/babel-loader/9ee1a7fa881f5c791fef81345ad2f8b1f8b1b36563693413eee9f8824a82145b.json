{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Alert, Stack, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem } from '@mui/material';\nimport { PlayArrow as PlayIcon, Stop as StopIcon, Schedule as ScheduleIcon, CheckCircle as CheckCircleIcon, Cancel as CancelIcon, Warning as WarningIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport FaceDetectionCamera from '../FaceRecognition/FaceDetectionCamera';\nimport { supabaseService } from '../../services/supabaseService';\nimport { AttendanceStatus, AttendanceMethod } from '../../types';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AutoAttendanceSystem = ({\n  course,\n  onAttendanceUpdate\n}) => {\n  _s();\n  const [session, setSession] = useState(null);\n  const [students, setStudents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [settingsOpen, setSettingsOpen] = useState(false);\n  const [lateThresholdMinutes, setLateThresholdMinutes] = useState(15);\n  const [detectionHistory, setDetectionHistory] = useState([]);\n\n  /**\n   * Charge la liste des étudiants du cours\n   */\n  const loadStudents = useCallback(async () => {\n    try {\n      setLoading(true);\n      // Ici, on devrait charger les étudiants inscrits au cours\n      // Pour l'instant, on charge tous les étudiants\n      const allUsers = await supabaseService.getUsers();\n      const courseStudents = allUsers.filter(user => user.role === 'student');\n      setStudents(courseStudents);\n    } catch (err) {\n      console.error('Erreur chargement étudiants:', err);\n      setError('Impossible de charger la liste des étudiants');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * Démarre une session de présence automatique\n   */\n  const startSession = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const newSession = {\n        id: `session_${Date.now()}`,\n        course,\n        startTime: new Date(),\n        status: 'active',\n        detectedStudents: new Set(),\n        attendanceRecords: []\n      };\n      setSession(newSession);\n      setDetectionHistory([]);\n    } catch (err) {\n      console.error('Erreur démarrage session:', err);\n      setError('Impossible de démarrer la session');\n    } finally {\n      setLoading(false);\n    }\n  }, [course]);\n\n  /**\n   * Arrête la session de présence\n   */\n  const stopSession = useCallback(async () => {\n    if (!session) return;\n    try {\n      setLoading(true);\n\n      // Marquer les étudiants non détectés comme absents\n      const absentStudents = students.filter(student => !session.detectedStudents.has(student.id));\n      const absentRecords = await Promise.all(absentStudents.map(async student => {\n        const attendanceData = {\n          student,\n          course,\n          date: new Date().toISOString().split('T')[0],\n          time: new Date().toISOString(),\n          status: AttendanceStatus.ABSENT,\n          method: AttendanceMethod.FACE_RECOGNITION,\n          notes: 'Marqué automatiquement comme absent',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n        const recordId = await supabaseService.recordAttendance(attendanceData);\n        return {\n          ...attendanceData,\n          id: recordId\n        };\n      }));\n      const updatedSession = {\n        ...session,\n        endTime: new Date(),\n        status: 'completed',\n        attendanceRecords: [...session.attendanceRecords, ...absentRecords]\n      };\n      setSession(updatedSession);\n      if (onAttendanceUpdate) {\n        onAttendanceUpdate(updatedSession.attendanceRecords);\n      }\n    } catch (err) {\n      console.error('Erreur arrêt session:', err);\n      setError('Erreur lors de l\\'arrêt de la session');\n    } finally {\n      setLoading(false);\n    }\n  }, [session, students, course, onAttendanceUpdate]);\n\n  /**\n   * Traite une détection de visage\n   */\n  const handleFaceDetection = useCallback(async result => {\n    if (!session || session.status !== 'active') return;\n    for (const detection of result.detectedUsers) {\n      const {\n        user,\n        confidence\n      } = detection;\n\n      // Vérifier si c'est un étudiant du cours\n      const student = students.find(s => s.username === user.username);\n      if (!student) continue;\n\n      // Vérifier si déjà détecté dans cette session\n      if (session.detectedStudents.has(student.id)) continue;\n\n      // Ajouter à l'historique de détection\n      setDetectionHistory(prev => [...prev, {\n        userId: student.id,\n        timestamp: new Date(),\n        confidence\n      }]);\n\n      // Déterminer le statut (présent ou en retard)\n      const now = new Date();\n      const sessionStart = session.startTime;\n      const minutesLate = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));\n      const status = minutesLate <= lateThresholdMinutes ? AttendanceStatus.PRESENT : AttendanceStatus.LATE;\n      try {\n        // Enregistrer la présence\n        const attendanceData = {\n          student,\n          course,\n          date: now.toISOString().split('T')[0],\n          time: now.toISOString(),\n          status,\n          method: AttendanceMethod.FACE_RECOGNITION,\n          confidence,\n          notes: `Détecté automatiquement (confiance: ${Math.round(confidence * 100)}%)`,\n          createdAt: now.toISOString(),\n          updatedAt: now.toISOString()\n        };\n        const recordId = await supabaseService.recordAttendance(attendanceData);\n        const newRecord = {\n          ...attendanceData,\n          id: recordId\n        };\n\n        // Mettre à jour la session\n        setSession(prev => {\n          if (!prev) return prev;\n          const updatedSession = {\n            ...prev,\n            detectedStudents: new Set(Array.from(prev.detectedStudents).concat(student.id)),\n            attendanceRecords: [...prev.attendanceRecords, newRecord]\n          };\n          if (onAttendanceUpdate) {\n            onAttendanceUpdate(updatedSession.attendanceRecords);\n          }\n          return updatedSession;\n        });\n      } catch (err) {\n        console.error('Erreur enregistrement présence:', err);\n      }\n    }\n  }, [session, students, course, lateThresholdMinutes, onAttendanceUpdate]);\n\n  // Charger les étudiants au montage\n  useEffect(() => {\n    loadStudents();\n  }, [loadStudents]);\n  const getStatusIcon = status => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 16\n        }, this);\n      case AttendanceStatus.LATE:\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 16\n        }, this);\n      case AttendanceStatus.ABSENT:\n        return /*#__PURE__*/_jsxDEV(CancelIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ScheduleIcon, {\n          color: \"disabled\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return 'success';\n      case AttendanceStatus.LATE:\n        return 'warning';\n      case AttendanceStatus.ABSENT:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Stack, {\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Syst\\xE8me de Pr\\xE9sence Automatique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setSettingsOpen(true),\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), !session ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 30\n              }, this),\n              onClick: startSession,\n              disabled: loading,\n              children: \"D\\xE9marrer Session\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this) : session.status === 'active' ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 30\n              }, this),\n              onClick: stopSession,\n              disabled: loading,\n              children: \"Arr\\xEAter Session\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Session Termin\\xE9e\",\n              color: \"success\",\n              icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: `Cours: ${course.name}`,\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `Étudiants: ${students.length}`,\n            color: \"secondary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), session && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: `Détectés: ${session.detectedStudents.size}`,\n              color: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Début: ${session.startTime.toLocaleTimeString()}`,\n              color: \"info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mt: 2\n          },\n          onClose: () => setError(null),\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), (session === null || session === void 0 ? void 0 : session.status) === 'active' && /*#__PURE__*/_jsxDEV(FaceDetectionCamera, {\n      onDetection: handleFaceDetection,\n      autoDetect: true,\n      showControls: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 9\n    }, this), session && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\xC9tat des Pr\\xE9sences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"\\xC9tudiant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Statut\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Heure\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Confiance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"M\\xE9thode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: students.map(student => {\n                const record = session.attendanceRecords.find(r => {\n                  var _r$student;\n                  return ((_r$student = r.student) === null || _r$student === void 0 ? void 0 : _r$student.id) === student.id;\n                });\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: [student.firstName, \" \", student.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      spacing: 1,\n                      alignItems: \"center\",\n                      children: record ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [getStatusIcon(record.status), /*#__PURE__*/_jsxDEV(Chip, {\n                          label: record.status,\n                          color: getStatusColor(record.status),\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 398,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true) : session.status === 'active' ? /*#__PURE__*/_jsxDEV(Chip, {\n                        label: \"En attente\",\n                        color: \"default\",\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [getStatusIcon(AttendanceStatus.ABSENT), /*#__PURE__*/_jsxDEV(Chip, {\n                          label: \"Absent\",\n                          color: \"error\",\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 409,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record ? new Date(record.time).toLocaleTimeString() : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record !== null && record !== void 0 && record.confidence ? `${Math.round(record.confidence * 100)}%` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record ? record.method : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 25\n                  }, this)]\n                }, student.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: settingsOpen,\n      onClose: () => setSettingsOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Param\\xE8tres de Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 3,\n          sx: {\n            pt: 1,\n            minWidth: 300\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Seuil de Retard (minutes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: lateThresholdMinutes,\n              onChange: e => setLateThresholdMinutes(Number(e.target.value)),\n              label: \"Seuil de Retard (minutes)\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 5,\n                children: \"5 minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 10,\n                children: \"10 minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 15,\n                children: \"15 minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 20,\n                children: \"20 minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 30,\n                children: \"30 minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Les \\xE9tudiants arrivant apr\\xE8s ce d\\xE9lai seront marqu\\xE9s comme \\\"En retard\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setSettingsOpen(false),\n          children: \"Fermer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 285,\n    columnNumber: 5\n  }, this);\n};\n_s(AutoAttendanceSystem, \"5gXbLYYiSRhNO/AKnWG9sAwuUrI=\");\n_c = AutoAttendanceSystem;\nexport default AutoAttendanceSystem;\nvar _c;\n$RefreshReg$(_c, \"AutoAttendanceSystem\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "PlayArrow", "PlayIcon", "Stop", "StopIcon", "Schedule", "ScheduleIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Settings", "SettingsIcon", "FaceDetectionCamera", "supabaseService", "AttendanceStatus", "AttendanceMethod", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutoAttendanceSystem", "course", "onAttendanceUpdate", "_s", "session", "setSession", "students", "setStudents", "loading", "setLoading", "error", "setError", "settingsOpen", "setSettingsOpen", "lateThresholdMinutes", "setLateThresholdMinutes", "detectionHistory", "setDetectionHistory", "loadStudents", "allUsers", "getUsers", "courseStudents", "filter", "user", "role", "err", "console", "startSession", "newSession", "id", "Date", "now", "startTime", "status", "detectedStudents", "Set", "attendanceRecords", "stopSession", "absentStudents", "student", "has", "absentRecords", "Promise", "all", "map", "attendanceData", "date", "toISOString", "split", "time", "ABSENT", "method", "FACE_RECOGNITION", "notes", "createdAt", "updatedAt", "recordId", "recordAttendance", "updatedSession", "endTime", "handleFaceDetection", "result", "detection", "detectedUsers", "confidence", "find", "s", "username", "prev", "userId", "timestamp", "sessionStart", "minutesLate", "Math", "floor", "getTime", "PRESENT", "LATE", "round", "newRecord", "Array", "from", "concat", "getStatusIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "spacing", "children", "display", "justifyContent", "alignItems", "mb", "variant", "direction", "onClick", "startIcon", "disabled", "label", "icon", "flexWrap", "name", "length", "size", "toLocaleTimeString", "severity", "sx", "mt", "onClose", "onDetection", "autoDetect", "showControls", "gutterBottom", "component", "record", "r", "_r$student", "firstName", "lastName", "open", "pt", "min<PERSON><PERSON><PERSON>", "fullWidth", "value", "onChange", "e", "Number", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/Attendance/AutoAttendanceSystem.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  <PERSON><PERSON><PERSON>,\n  Button,\n  Alert,\n  Stack,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem\n} from '@mui/material';\nimport {\n  PlayArrow as PlayIcon,\n  Stop as StopIcon,\n  Schedule as ScheduleIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Settings as SettingsIcon\n} from '@mui/icons-material';\nimport FaceDetectionCamera from '../FaceRecognition/FaceDetectionCamera';\nimport { supabaseService } from '../../services/supabaseService';\nimport { \n  Course, \n  User, \n  AttendanceRecord, \n  AttendanceStatus, \n  AttendanceMethod,\n  FaceDetectionResult \n} from '../../types';\n\ninterface AutoAttendanceSystemProps {\n  course: Course;\n  onAttendanceUpdate?: (records: AttendanceRecord[]) => void;\n}\n\ninterface AttendanceSession {\n  id: string;\n  course: Course;\n  startTime: Date;\n  endTime?: Date;\n  status: 'active' | 'completed' | 'cancelled';\n  detectedStudents: Set<string>;\n  attendanceRecords: AttendanceRecord[];\n}\n\nconst AutoAttendanceSystem: React.FC<AutoAttendanceSystemProps> = ({\n  course,\n  onAttendanceUpdate\n}) => {\n  const [session, setSession] = useState<AttendanceSession | null>(null);\n  const [students, setStudents] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [settingsOpen, setSettingsOpen] = useState(false);\n  const [lateThresholdMinutes, setLateThresholdMinutes] = useState(15);\n  const [detectionHistory, setDetectionHistory] = useState<{\n    userId: string;\n    timestamp: Date;\n    confidence: number;\n  }[]>([]);\n\n  /**\n   * Charge la liste des étudiants du cours\n   */\n  const loadStudents = useCallback(async () => {\n    try {\n      setLoading(true);\n      // Ici, on devrait charger les étudiants inscrits au cours\n      // Pour l'instant, on charge tous les étudiants\n      const allUsers = await supabaseService.getUsers();\n      const courseStudents = allUsers.filter(user => user.role === 'student');\n      setStudents(courseStudents);\n    } catch (err) {\n      console.error('Erreur chargement étudiants:', err);\n      setError('Impossible de charger la liste des étudiants');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * Démarre une session de présence automatique\n   */\n  const startSession = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const newSession: AttendanceSession = {\n        id: `session_${Date.now()}`,\n        course,\n        startTime: new Date(),\n        status: 'active',\n        detectedStudents: new Set(),\n        attendanceRecords: []\n      };\n\n      setSession(newSession);\n      setDetectionHistory([]);\n\n    } catch (err) {\n      console.error('Erreur démarrage session:', err);\n      setError('Impossible de démarrer la session');\n    } finally {\n      setLoading(false);\n    }\n  }, [course]);\n\n  /**\n   * Arrête la session de présence\n   */\n  const stopSession = useCallback(async () => {\n    if (!session) return;\n\n    try {\n      setLoading(true);\n\n      // Marquer les étudiants non détectés comme absents\n      const absentStudents = students.filter(\n        student => !session.detectedStudents.has(student.id)\n      );\n\n      const absentRecords = await Promise.all(\n        absentStudents.map(async (student) => {\n          const attendanceData: Omit<AttendanceRecord, 'id'> = {\n            student,\n            course,\n            date: new Date().toISOString().split('T')[0],\n            time: new Date().toISOString(),\n            status: AttendanceStatus.ABSENT,\n            method: AttendanceMethod.FACE_RECOGNITION,\n            notes: 'Marqué automatiquement comme absent',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          };\n\n          const recordId = await supabaseService.recordAttendance(attendanceData);\n          return { ...attendanceData, id: recordId };\n        })\n      );\n\n      const updatedSession = {\n        ...session,\n        endTime: new Date(),\n        status: 'completed' as const,\n        attendanceRecords: [...session.attendanceRecords, ...absentRecords]\n      };\n\n      setSession(updatedSession);\n\n      if (onAttendanceUpdate) {\n        onAttendanceUpdate(updatedSession.attendanceRecords);\n      }\n\n    } catch (err) {\n      console.error('Erreur arrêt session:', err);\n      setError('Erreur lors de l\\'arrêt de la session');\n    } finally {\n      setLoading(false);\n    }\n  }, [session, students, course, onAttendanceUpdate]);\n\n  /**\n   * Traite une détection de visage\n   */\n  const handleFaceDetection = useCallback(async (result: FaceDetectionResult) => {\n    if (!session || session.status !== 'active') return;\n\n    for (const detection of result.detectedUsers) {\n      const { user, confidence } = detection;\n      \n      // Vérifier si c'est un étudiant du cours\n      const student = students.find(s => s.username === user.username);\n      if (!student) continue;\n\n      // Vérifier si déjà détecté dans cette session\n      if (session.detectedStudents.has(student.id)) continue;\n\n      // Ajouter à l'historique de détection\n      setDetectionHistory(prev => [...prev, {\n        userId: student.id,\n        timestamp: new Date(),\n        confidence\n      }]);\n\n      // Déterminer le statut (présent ou en retard)\n      const now = new Date();\n      const sessionStart = session.startTime;\n      const minutesLate = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));\n      \n      const status = minutesLate <= lateThresholdMinutes \n        ? AttendanceStatus.PRESENT \n        : AttendanceStatus.LATE;\n\n      try {\n        // Enregistrer la présence\n        const attendanceData: Omit<AttendanceRecord, 'id'> = {\n          student,\n          course,\n          date: now.toISOString().split('T')[0],\n          time: now.toISOString(),\n          status,\n          method: AttendanceMethod.FACE_RECOGNITION,\n          confidence,\n          notes: `Détecté automatiquement (confiance: ${Math.round(confidence * 100)}%)`,\n          createdAt: now.toISOString(),\n          updatedAt: now.toISOString()\n        };\n\n        const recordId = await supabaseService.recordAttendance(attendanceData);\n        const newRecord = { ...attendanceData, id: recordId };\n\n        // Mettre à jour la session\n        setSession(prev => {\n          if (!prev) return prev;\n          \n          const updatedSession = {\n            ...prev,\n            detectedStudents: new Set(Array.from(prev.detectedStudents).concat(student.id)),\n            attendanceRecords: [...prev.attendanceRecords, newRecord]\n          };\n\n          if (onAttendanceUpdate) {\n            onAttendanceUpdate(updatedSession.attendanceRecords);\n          }\n\n          return updatedSession;\n        });\n\n      } catch (err) {\n        console.error('Erreur enregistrement présence:', err);\n      }\n    }\n  }, [session, students, course, lateThresholdMinutes, onAttendanceUpdate]);\n\n  // Charger les étudiants au montage\n  useEffect(() => {\n    loadStudents();\n  }, [loadStudents]);\n\n  const getStatusIcon = (status: AttendanceStatus) => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return <CheckCircleIcon color=\"success\" />;\n      case AttendanceStatus.LATE:\n        return <WarningIcon color=\"warning\" />;\n      case AttendanceStatus.ABSENT:\n        return <CancelIcon color=\"error\" />;\n      default:\n        return <ScheduleIcon color=\"disabled\" />;\n    }\n  };\n\n  const getStatusColor = (status: AttendanceStatus) => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return 'success';\n      case AttendanceStatus.LATE:\n        return 'warning';\n      case AttendanceStatus.ABSENT:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Stack spacing={3}>\n      {/* En-tête de contrôle */}\n      <Card>\n        <CardContent>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n            <Typography variant=\"h6\">\n              Système de Présence Automatique\n            </Typography>\n            \n            <Stack direction=\"row\" spacing={2}>\n              <IconButton onClick={() => setSettingsOpen(true)}>\n                <SettingsIcon />\n              </IconButton>\n              \n              {!session ? (\n                <Button\n                  variant=\"contained\"\n                  startIcon={<PlayIcon />}\n                  onClick={startSession}\n                  disabled={loading}\n                >\n                  Démarrer Session\n                </Button>\n              ) : session.status === 'active' ? (\n                <Button\n                  variant=\"contained\"\n                  color=\"error\"\n                  startIcon={<StopIcon />}\n                  onClick={stopSession}\n                  disabled={loading}\n                >\n                  Arrêter Session\n                </Button>\n              ) : (\n                <Chip \n                  label=\"Session Terminée\" \n                  color=\"success\" \n                  icon={<CheckCircleIcon />}\n                />\n              )}\n            </Stack>\n          </Box>\n\n          {/* Informations du cours */}\n          <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\">\n            <Chip label={`Cours: ${course.name}`} color=\"primary\" />\n            <Chip label={`Étudiants: ${students.length}`} color=\"secondary\" />\n            {session && (\n              <>\n                <Chip \n                  label={`Détectés: ${session.detectedStudents.size}`} \n                  color=\"success\" \n                />\n                <Chip \n                  label={`Début: ${session.startTime.toLocaleTimeString()}`} \n                  color=\"info\" \n                />\n              </>\n            )}\n          </Stack>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mt: 2 }} onClose={() => setError(null)}>\n              {error}\n            </Alert>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Caméra de reconnaissance */}\n      {session?.status === 'active' && (\n        <FaceDetectionCamera\n          onDetection={handleFaceDetection}\n          autoDetect={true}\n          showControls={false}\n        />\n      )}\n\n      {/* Tableau des présences */}\n      {session && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              État des Présences\n            </Typography>\n            \n            <TableContainer component={Paper}>\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Étudiant</TableCell>\n                    <TableCell>Statut</TableCell>\n                    <TableCell>Heure</TableCell>\n                    <TableCell>Confiance</TableCell>\n                    <TableCell>Méthode</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {students.map((student) => {\n                    const record = session.attendanceRecords.find(\n                      r => r.student?.id === student.id\n                    );\n                    \n                    return (\n                      <TableRow key={student.id}>\n                        <TableCell>\n                          {student.firstName} {student.lastName}\n                        </TableCell>\n                        <TableCell>\n                          <Stack direction=\"row\" spacing={1} alignItems=\"center\">\n                            {record ? (\n                              <>\n                                {getStatusIcon(record.status)}\n                                <Chip\n                                  label={record.status}\n                                  color={getStatusColor(record.status) as any}\n                                  size=\"small\"\n                                />\n                              </>\n                            ) : session.status === 'active' ? (\n                              <Chip label=\"En attente\" color=\"default\" size=\"small\" />\n                            ) : (\n                              <>\n                                {getStatusIcon(AttendanceStatus.ABSENT)}\n                                <Chip label=\"Absent\" color=\"error\" size=\"small\" />\n                              </>\n                            )}\n                          </Stack>\n                        </TableCell>\n                        <TableCell>\n                          {record ? new Date(record.time).toLocaleTimeString() : '-'}\n                        </TableCell>\n                        <TableCell>\n                          {record?.confidence ? `${Math.round(record.confidence * 100)}%` : '-'}\n                        </TableCell>\n                        <TableCell>\n                          {record ? record.method : '-'}\n                        </TableCell>\n                      </TableRow>\n                    );\n                  })}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Dialog des paramètres */}\n      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)}>\n        <DialogTitle>Paramètres de Session</DialogTitle>\n        <DialogContent>\n          <Stack spacing={3} sx={{ pt: 1, minWidth: 300 }}>\n            <FormControl fullWidth>\n              <InputLabel>Seuil de Retard (minutes)</InputLabel>\n              <Select\n                value={lateThresholdMinutes}\n                onChange={(e) => setLateThresholdMinutes(Number(e.target.value))}\n                label=\"Seuil de Retard (minutes)\"\n              >\n                <MenuItem value={5}>5 minutes</MenuItem>\n                <MenuItem value={10}>10 minutes</MenuItem>\n                <MenuItem value={15}>15 minutes</MenuItem>\n                <MenuItem value={20}>20 minutes</MenuItem>\n                <MenuItem value={30}>30 minutes</MenuItem>\n              </Select>\n            </FormControl>\n            \n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Les étudiants arrivant après ce délai seront marqués comme \"En retard\"\n            </Typography>\n          </Stack>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setSettingsOpen(false)}>Fermer</Button>\n        </DialogActions>\n      </Dialog>\n    </Stack>\n  );\n};\n\nexport default AutoAttendanceSystem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,IAAIC,QAAQ,EACrBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAIEC,gBAAgB,EAChBC,gBAAgB,QAEX,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAiBrB,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAA2B,IAAI,CAAC;EACtE,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAInD,EAAE,CAAC;;EAER;AACF;AACA;EACE,MAAMmE,YAAY,GAAGjE,WAAW,CAAC,YAAY;IAC3C,IAAI;MACFwD,UAAU,CAAC,IAAI,CAAC;MAChB;MACA;MACA,MAAMU,QAAQ,GAAG,MAAM1B,eAAe,CAAC2B,QAAQ,CAAC,CAAC;MACjD,MAAMC,cAAc,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,CAAC;MACvEjB,WAAW,CAACc,cAAc,CAAC;IAC7B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,8BAA8B,EAAEe,GAAG,CAAC;MAClDd,QAAQ,CAAC,8CAA8C,CAAC;IAC1D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMkB,YAAY,GAAG1E,WAAW,CAAC,YAAY;IAC3C,IAAI;MACFwD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMiB,UAA6B,GAAG;QACpCC,EAAE,EAAE,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC3B9B,MAAM;QACN+B,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC;QACrBG,MAAM,EAAE,QAAQ;QAChBC,gBAAgB,EAAE,IAAIC,GAAG,CAAC,CAAC;QAC3BC,iBAAiB,EAAE;MACrB,CAAC;MAED/B,UAAU,CAACuB,UAAU,CAAC;MACtBX,mBAAmB,CAAC,EAAE,CAAC;IAEzB,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,2BAA2B,EAAEe,GAAG,CAAC;MAC/Cd,QAAQ,CAAC,mCAAmC,CAAC;IAC/C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMoC,WAAW,GAAGpF,WAAW,CAAC,YAAY;IAC1C,IAAI,CAACmD,OAAO,EAAE;IAEd,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM6B,cAAc,GAAGhC,QAAQ,CAACgB,MAAM,CACpCiB,OAAO,IAAI,CAACnC,OAAO,CAAC8B,gBAAgB,CAACM,GAAG,CAACD,OAAO,CAACV,EAAE,CACrD,CAAC;MAED,MAAMY,aAAa,GAAG,MAAMC,OAAO,CAACC,GAAG,CACrCL,cAAc,CAACM,GAAG,CAAC,MAAOL,OAAO,IAAK;QACpC,MAAMM,cAA4C,GAAG;UACnDN,OAAO;UACPtC,MAAM;UACN6C,IAAI,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC5CC,IAAI,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;UAC9Bd,MAAM,EAAEvC,gBAAgB,CAACwD,MAAM;UAC/BC,MAAM,EAAExD,gBAAgB,CAACyD,gBAAgB;UACzCC,KAAK,EAAE,qCAAqC;UAC5CC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;UACnCQ,SAAS,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC;QACpC,CAAC;QAED,MAAMS,QAAQ,GAAG,MAAM/D,eAAe,CAACgE,gBAAgB,CAACZ,cAAc,CAAC;QACvE,OAAO;UAAE,GAAGA,cAAc;UAAEhB,EAAE,EAAE2B;QAAS,CAAC;MAC5C,CAAC,CACH,CAAC;MAED,MAAME,cAAc,GAAG;QACrB,GAAGtD,OAAO;QACVuD,OAAO,EAAE,IAAI7B,IAAI,CAAC,CAAC;QACnBG,MAAM,EAAE,WAAoB;QAC5BG,iBAAiB,EAAE,CAAC,GAAGhC,OAAO,CAACgC,iBAAiB,EAAE,GAAGK,aAAa;MACpE,CAAC;MAEDpC,UAAU,CAACqD,cAAc,CAAC;MAE1B,IAAIxD,kBAAkB,EAAE;QACtBA,kBAAkB,CAACwD,cAAc,CAACtB,iBAAiB,CAAC;MACtD;IAEF,CAAC,CAAC,OAAOX,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,GAAG,CAAC;MAC3Cd,QAAQ,CAAC,uCAAuC,CAAC;IACnD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,OAAO,EAAEE,QAAQ,EAAEL,MAAM,EAAEC,kBAAkB,CAAC,CAAC;;EAEnD;AACF;AACA;EACE,MAAM0D,mBAAmB,GAAG3G,WAAW,CAAC,MAAO4G,MAA2B,IAAK;IAC7E,IAAI,CAACzD,OAAO,IAAIA,OAAO,CAAC6B,MAAM,KAAK,QAAQ,EAAE;IAE7C,KAAK,MAAM6B,SAAS,IAAID,MAAM,CAACE,aAAa,EAAE;MAC5C,MAAM;QAAExC,IAAI;QAAEyC;MAAW,CAAC,GAAGF,SAAS;;MAEtC;MACA,MAAMvB,OAAO,GAAGjC,QAAQ,CAAC2D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK5C,IAAI,CAAC4C,QAAQ,CAAC;MAChE,IAAI,CAAC5B,OAAO,EAAE;;MAEd;MACA,IAAInC,OAAO,CAAC8B,gBAAgB,CAACM,GAAG,CAACD,OAAO,CAACV,EAAE,CAAC,EAAE;;MAE9C;MACAZ,mBAAmB,CAACmD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QACpCC,MAAM,EAAE9B,OAAO,CAACV,EAAE;QAClByC,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC;QACrBkC;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMjC,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAMyC,YAAY,GAAGnE,OAAO,CAAC4B,SAAS;MACtC,MAAMwC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC3C,GAAG,CAAC4C,OAAO,CAAC,CAAC,GAAGJ,YAAY,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MAEtF,MAAM1C,MAAM,GAAGuC,WAAW,IAAI1D,oBAAoB,GAC9CpB,gBAAgB,CAACkF,OAAO,GACxBlF,gBAAgB,CAACmF,IAAI;MAEzB,IAAI;QACF;QACA,MAAMhC,cAA4C,GAAG;UACnDN,OAAO;UACPtC,MAAM;UACN6C,IAAI,EAAEf,GAAG,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACrCC,IAAI,EAAElB,GAAG,CAACgB,WAAW,CAAC,CAAC;UACvBd,MAAM;UACNkB,MAAM,EAAExD,gBAAgB,CAACyD,gBAAgB;UACzCY,UAAU;UACVX,KAAK,EAAE,uCAAuCoB,IAAI,CAACK,KAAK,CAACd,UAAU,GAAG,GAAG,CAAC,IAAI;UAC9EV,SAAS,EAAEvB,GAAG,CAACgB,WAAW,CAAC,CAAC;UAC5BQ,SAAS,EAAExB,GAAG,CAACgB,WAAW,CAAC;QAC7B,CAAC;QAED,MAAMS,QAAQ,GAAG,MAAM/D,eAAe,CAACgE,gBAAgB,CAACZ,cAAc,CAAC;QACvE,MAAMkC,SAAS,GAAG;UAAE,GAAGlC,cAAc;UAAEhB,EAAE,EAAE2B;QAAS,CAAC;;QAErD;QACAnD,UAAU,CAAC+D,IAAI,IAAI;UACjB,IAAI,CAACA,IAAI,EAAE,OAAOA,IAAI;UAEtB,MAAMV,cAAc,GAAG;YACrB,GAAGU,IAAI;YACPlC,gBAAgB,EAAE,IAAIC,GAAG,CAAC6C,KAAK,CAACC,IAAI,CAACb,IAAI,CAAClC,gBAAgB,CAAC,CAACgD,MAAM,CAAC3C,OAAO,CAACV,EAAE,CAAC,CAAC;YAC/EO,iBAAiB,EAAE,CAAC,GAAGgC,IAAI,CAAChC,iBAAiB,EAAE2C,SAAS;UAC1D,CAAC;UAED,IAAI7E,kBAAkB,EAAE;YACtBA,kBAAkB,CAACwD,cAAc,CAACtB,iBAAiB,CAAC;UACtD;UAEA,OAAOsB,cAAc;QACvB,CAAC,CAAC;MAEJ,CAAC,CAAC,OAAOjC,GAAG,EAAE;QACZC,OAAO,CAAChB,KAAK,CAAC,iCAAiC,EAAEe,GAAG,CAAC;MACvD;IACF;EACF,CAAC,EAAE,CAACrB,OAAO,EAAEE,QAAQ,EAAEL,MAAM,EAAEa,oBAAoB,EAAEZ,kBAAkB,CAAC,CAAC;;EAEzE;EACAlD,SAAS,CAAC,MAAM;IACdkE,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMiE,aAAa,GAAIlD,MAAwB,IAAK;IAClD,QAAQA,MAAM;MACZ,KAAKvC,gBAAgB,CAACkF,OAAO;QAC3B,oBAAO/E,OAAA,CAACZ,eAAe;UAACmG,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5C,KAAK9F,gBAAgB,CAACmF,IAAI;QACxB,oBAAOhF,OAAA,CAACR,WAAW;UAAC+F,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAK9F,gBAAgB,CAACwD,MAAM;QAC1B,oBAAOrD,OAAA,CAACV,UAAU;UAACiG,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC;QACE,oBAAO3F,OAAA,CAACd,YAAY;UAACqG,KAAK,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIxD,MAAwB,IAAK;IACnD,QAAQA,MAAM;MACZ,KAAKvC,gBAAgB,CAACkF,OAAO;QAC3B,OAAO,SAAS;MAClB,KAAKlF,gBAAgB,CAACmF,IAAI;QACxB,OAAO,SAAS;MAClB,KAAKnF,gBAAgB,CAACwD,MAAM;QAC1B,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACErD,OAAA,CAACrC,KAAK;IAACkI,OAAO,EAAE,CAAE;IAAAC,QAAA,gBAEhB9F,OAAA,CAAC1C,IAAI;MAAAwI,QAAA,eACH9F,OAAA,CAACzC,WAAW;QAAAuI,QAAA,gBACV9F,OAAA,CAAC3C,GAAG;UAAC0I,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACC,EAAE,EAAE,CAAE;UAAAJ,QAAA,gBAC3E9F,OAAA,CAACxC,UAAU;YAAC2I,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAEzB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb3F,OAAA,CAACrC,KAAK;YAACyI,SAAS,EAAC,KAAK;YAACP,OAAO,EAAE,CAAE;YAAAC,QAAA,gBAChC9F,OAAA,CAAC5B,UAAU;cAACiI,OAAO,EAAEA,CAAA,KAAMrF,eAAe,CAAC,IAAI,CAAE;cAAA8E,QAAA,eAC/C9F,OAAA,CAACN,YAAY;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEZ,CAACpF,OAAO,gBACPP,OAAA,CAACvC,MAAM;cACL0I,OAAO,EAAC,WAAW;cACnBG,SAAS,eAAEtG,OAAA,CAAClB,QAAQ;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBU,OAAO,EAAEvE,YAAa;cACtByE,QAAQ,EAAE5F,OAAQ;cAAAmF,QAAA,EACnB;YAED;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GACPpF,OAAO,CAAC6B,MAAM,KAAK,QAAQ,gBAC7BpC,OAAA,CAACvC,MAAM;cACL0I,OAAO,EAAC,WAAW;cACnBZ,KAAK,EAAC,OAAO;cACbe,SAAS,eAAEtG,OAAA,CAAChB,QAAQ;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBU,OAAO,EAAE7D,WAAY;cACrB+D,QAAQ,EAAE5F,OAAQ;cAAAmF,QAAA,EACnB;YAED;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAET3F,OAAA,CAACpC,IAAI;cACH4I,KAAK,EAAC,qBAAkB;cACxBjB,KAAK,EAAC,SAAS;cACfkB,IAAI,eAAEzG,OAAA,CAACZ,eAAe;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN3F,OAAA,CAACrC,KAAK;UAACyI,SAAS,EAAC,KAAK;UAACP,OAAO,EAAE,CAAE;UAACa,QAAQ,EAAC,MAAM;UAAAZ,QAAA,gBAChD9F,OAAA,CAACpC,IAAI;YAAC4I,KAAK,EAAE,UAAUpG,MAAM,CAACuG,IAAI,EAAG;YAACpB,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxD3F,OAAA,CAACpC,IAAI;YAAC4I,KAAK,EAAE,cAAc/F,QAAQ,CAACmG,MAAM,EAAG;YAACrB,KAAK,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjEpF,OAAO,iBACNP,OAAA,CAAAE,SAAA;YAAA4F,QAAA,gBACE9F,OAAA,CAACpC,IAAI;cACH4I,KAAK,EAAE,aAAajG,OAAO,CAAC8B,gBAAgB,CAACwE,IAAI,EAAG;cACpDtB,KAAK,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACF3F,OAAA,CAACpC,IAAI;cACH4I,KAAK,EAAE,UAAUjG,OAAO,CAAC4B,SAAS,CAAC2E,kBAAkB,CAAC,CAAC,EAAG;cAC1DvB,KAAK,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,eACF,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAEP9E,KAAK,iBACJb,OAAA,CAACtC,KAAK;UAACqJ,QAAQ,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAACC,OAAO,EAAEA,CAAA,KAAMpG,QAAQ,CAAC,IAAI,CAAE;UAAAgF,QAAA,EAClEjF;QAAK;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGN,CAAApF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6B,MAAM,MAAK,QAAQ,iBAC3BpC,OAAA,CAACL,mBAAmB;MAClBwH,WAAW,EAAEpD,mBAAoB;MACjCqD,UAAU,EAAE,IAAK;MACjBC,YAAY,EAAE;IAAM;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACF,EAGApF,OAAO,iBACNP,OAAA,CAAC1C,IAAI;MAAAwI,QAAA,eACH9F,OAAA,CAACzC,WAAW;QAAAuI,QAAA,gBACV9F,OAAA,CAACxC,UAAU;UAAC2I,OAAO,EAAC,IAAI;UAACmB,YAAY;UAAAxB,QAAA,EAAC;QAEtC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3F,OAAA,CAAChC,cAAc;UAACuJ,SAAS,EAAEpJ,KAAM;UAAA2H,QAAA,eAC/B9F,OAAA,CAACnC,KAAK;YAAAiI,QAAA,gBACJ9F,OAAA,CAAC/B,SAAS;cAAA6H,QAAA,eACR9F,OAAA,CAAC9B,QAAQ;gBAAA4H,QAAA,gBACP9F,OAAA,CAACjC,SAAS;kBAAA+H,QAAA,EAAC;gBAAQ;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/B3F,OAAA,CAACjC,SAAS;kBAAA+H,QAAA,EAAC;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7B3F,OAAA,CAACjC,SAAS;kBAAA+H,QAAA,EAAC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B3F,OAAA,CAACjC,SAAS;kBAAA+H,QAAA,EAAC;gBAAS;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC3F,OAAA,CAACjC,SAAS;kBAAA+H,QAAA,EAAC;gBAAO;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ3F,OAAA,CAAClC,SAAS;cAAAgI,QAAA,EACPrF,QAAQ,CAACsC,GAAG,CAAEL,OAAO,IAAK;gBACzB,MAAM8E,MAAM,GAAGjH,OAAO,CAACgC,iBAAiB,CAAC6B,IAAI,CAC3CqD,CAAC;kBAAA,IAAAC,UAAA;kBAAA,OAAI,EAAAA,UAAA,GAAAD,CAAC,CAAC/E,OAAO,cAAAgF,UAAA,uBAATA,UAAA,CAAW1F,EAAE,MAAKU,OAAO,CAACV,EAAE;gBAAA,CACnC,CAAC;gBAED,oBACEhC,OAAA,CAAC9B,QAAQ;kBAAA4H,QAAA,gBACP9F,OAAA,CAACjC,SAAS;oBAAA+H,QAAA,GACPpD,OAAO,CAACiF,SAAS,EAAC,GAAC,EAACjF,OAAO,CAACkF,QAAQ;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACZ3F,OAAA,CAACjC,SAAS;oBAAA+H,QAAA,eACR9F,OAAA,CAACrC,KAAK;sBAACyI,SAAS,EAAC,KAAK;sBAACP,OAAO,EAAE,CAAE;sBAACI,UAAU,EAAC,QAAQ;sBAAAH,QAAA,EACnD0B,MAAM,gBACLxH,OAAA,CAAAE,SAAA;wBAAA4F,QAAA,GACGR,aAAa,CAACkC,MAAM,CAACpF,MAAM,CAAC,eAC7BpC,OAAA,CAACpC,IAAI;0BACH4I,KAAK,EAAEgB,MAAM,CAACpF,MAAO;0BACrBmD,KAAK,EAAEK,cAAc,CAAC4B,MAAM,CAACpF,MAAM,CAAS;0BAC5CyE,IAAI,EAAC;wBAAO;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CAAC;sBAAA,eACF,CAAC,GACDpF,OAAO,CAAC6B,MAAM,KAAK,QAAQ,gBAC7BpC,OAAA,CAACpC,IAAI;wBAAC4I,KAAK,EAAC,YAAY;wBAACjB,KAAK,EAAC,SAAS;wBAACsB,IAAI,EAAC;sBAAO;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAExD3F,OAAA,CAAAE,SAAA;wBAAA4F,QAAA,GACGR,aAAa,CAACzF,gBAAgB,CAACwD,MAAM,CAAC,eACvCrD,OAAA,CAACpC,IAAI;0BAAC4I,KAAK,EAAC,QAAQ;0BAACjB,KAAK,EAAC,OAAO;0BAACsB,IAAI,EAAC;wBAAO;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA,eAClD;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZ3F,OAAA,CAACjC,SAAS;oBAAA+H,QAAA,EACP0B,MAAM,GAAG,IAAIvF,IAAI,CAACuF,MAAM,CAACpE,IAAI,CAAC,CAAC0D,kBAAkB,CAAC,CAAC,GAAG;kBAAG;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACZ3F,OAAA,CAACjC,SAAS;oBAAA+H,QAAA,EACP0B,MAAM,aAANA,MAAM,eAANA,MAAM,CAAErD,UAAU,GAAG,GAAGS,IAAI,CAACK,KAAK,CAACuC,MAAM,CAACrD,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;kBAAG;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACZ3F,OAAA,CAACjC,SAAS;oBAAA+H,QAAA,EACP0B,MAAM,GAAGA,MAAM,CAAClE,MAAM,GAAG;kBAAG;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA,GAjCCjD,OAAO,CAACV,EAAE;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkCf,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGD3F,OAAA,CAAC3B,MAAM;MAACwJ,IAAI,EAAE9G,YAAa;MAACmG,OAAO,EAAEA,CAAA,KAAMlG,eAAe,CAAC,KAAK,CAAE;MAAA8E,QAAA,gBAChE9F,OAAA,CAAC1B,WAAW;QAAAwH,QAAA,EAAC;MAAqB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChD3F,OAAA,CAACzB,aAAa;QAAAuH,QAAA,eACZ9F,OAAA,CAACrC,KAAK;UAACkI,OAAO,EAAE,CAAE;UAACmB,EAAE,EAAE;YAAEc,EAAE,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAjC,QAAA,gBAC9C9F,OAAA,CAACvB,WAAW;YAACuJ,SAAS;YAAAlC,QAAA,gBACpB9F,OAAA,CAACtB,UAAU;cAAAoH,QAAA,EAAC;YAAyB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClD3F,OAAA,CAACrB,MAAM;cACLsJ,KAAK,EAAEhH,oBAAqB;cAC5BiH,QAAQ,EAAGC,CAAC,IAAKjH,uBAAuB,CAACkH,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;cACjEzB,KAAK,EAAC,2BAA2B;cAAAV,QAAA,gBAEjC9F,OAAA,CAACpB,QAAQ;gBAACqJ,KAAK,EAAE,CAAE;gBAAAnC,QAAA,EAAC;cAAS;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC3F,OAAA,CAACpB,QAAQ;gBAACqJ,KAAK,EAAE,EAAG;gBAAAnC,QAAA,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1C3F,OAAA,CAACpB,QAAQ;gBAACqJ,KAAK,EAAE,EAAG;gBAAAnC,QAAA,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1C3F,OAAA,CAACpB,QAAQ;gBAACqJ,KAAK,EAAE,EAAG;gBAAAnC,QAAA,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1C3F,OAAA,CAACpB,QAAQ;gBAACqJ,KAAK,EAAE,EAAG;gBAAAnC,QAAA,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEd3F,OAAA,CAACxC,UAAU;YAAC2I,OAAO,EAAC,OAAO;YAACZ,KAAK,EAAC,gBAAgB;YAAAO,QAAA,EAAC;UAEnD;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChB3F,OAAA,CAACxB,aAAa;QAAAsH,QAAA,eACZ9F,OAAA,CAACvC,MAAM;UAAC4I,OAAO,EAAEA,CAAA,KAAMrF,eAAe,CAAC,KAAK,CAAE;UAAA8E,QAAA,EAAC;QAAM;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ,CAAC;AAACrF,EAAA,CAjZIH,oBAAyD;AAAAmI,EAAA,GAAzDnI,oBAAyD;AAmZ/D,eAAeA,oBAAoB;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}