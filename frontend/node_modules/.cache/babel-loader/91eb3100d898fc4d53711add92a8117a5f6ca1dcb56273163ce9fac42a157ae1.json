{"ast": null, "code": "import { FaceMatch } from '../classes/FaceMatch';\nimport { LabeledFaceDescriptors } from '../classes/LabeledFaceDescriptors';\nimport { euclideanDistance } from '../euclideanDistance';\nvar FaceMatcher = /** @class */function () {\n  function FaceMatcher(inputs, distanceThreshold) {\n    if (distanceThreshold === void 0) {\n      distanceThreshold = 0.6;\n    }\n    this._distanceThreshold = distanceThreshold;\n    var inputArray = Array.isArray(inputs) ? inputs : [inputs];\n    if (!inputArray.length) {\n      throw new Error(\"FaceRecognizer.constructor - expected atleast one input\");\n    }\n    var count = 1;\n    var createUniqueLabel = function () {\n      return \"person \" + count++;\n    };\n    this._labeledDescriptors = inputArray.map(function (desc) {\n      if (desc instanceof LabeledFaceDescriptors) {\n        return desc;\n      }\n      if (desc instanceof Float32Array) {\n        return new LabeledFaceDescriptors(createUniqueLabel(), [desc]);\n      }\n      if (desc.descriptor && desc.descriptor instanceof Float32Array) {\n        return new LabeledFaceDescriptors(createUniqueLabel(), [desc.descriptor]);\n      }\n      throw new Error(\"FaceRecognizer.constructor - expected inputs to be of type LabeledFaceDescriptors | WithFaceDescriptor<any> | Float32Array | Array<LabeledFaceDescriptors | WithFaceDescriptor<any> | Float32Array>\");\n    });\n  }\n  Object.defineProperty(FaceMatcher.prototype, \"labeledDescriptors\", {\n    get: function () {\n      return this._labeledDescriptors;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(FaceMatcher.prototype, \"distanceThreshold\", {\n    get: function () {\n      return this._distanceThreshold;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  FaceMatcher.prototype.computeMeanDistance = function (queryDescriptor, descriptors) {\n    return descriptors.map(function (d) {\n      return euclideanDistance(d, queryDescriptor);\n    }).reduce(function (d1, d2) {\n      return d1 + d2;\n    }, 0) / (descriptors.length || 1);\n  };\n  FaceMatcher.prototype.matchDescriptor = function (queryDescriptor) {\n    var _this = this;\n    return this.labeledDescriptors.map(function (_a) {\n      var descriptors = _a.descriptors,\n        label = _a.label;\n      return new FaceMatch(label, _this.computeMeanDistance(queryDescriptor, descriptors));\n    }).reduce(function (best, curr) {\n      return best.distance < curr.distance ? best : curr;\n    });\n  };\n  FaceMatcher.prototype.findBestMatch = function (queryDescriptor) {\n    var bestMatch = this.matchDescriptor(queryDescriptor);\n    return bestMatch.distance < this.distanceThreshold ? bestMatch : new FaceMatch('unknown', bestMatch.distance);\n  };\n  FaceMatcher.prototype.toJSON = function () {\n    return {\n      distanceThreshold: this.distanceThreshold,\n      labeledDescriptors: this.labeledDescriptors.map(function (ld) {\n        return ld.toJSON();\n      })\n    };\n  };\n  FaceMatcher.fromJSON = function (json) {\n    var labeledDescriptors = json.labeledDescriptors.map(function (ld) {\n      return LabeledFaceDescriptors.fromJSON(ld);\n    });\n    return new FaceMatcher(labeledDescriptors, json.distanceThreshold);\n  };\n  return FaceMatcher;\n}();\nexport { FaceMatcher };", "map": {"version": 3, "names": ["FaceMatch", "LabeledFaceDescriptors", "euclideanDistance", "FaceMatcher", "inputs", "distanceThreshold", "_distanceThreshold", "inputArray", "Array", "isArray", "length", "Error", "count", "createUniqueLabel", "_labeledDescriptors", "map", "desc", "Float32Array", "descriptor", "Object", "defineProperty", "prototype", "get", "computeMeanDistance", "queryDescriptor", "descriptors", "d", "reduce", "d1", "d2", "matchDescriptor", "_this", "labeledDescriptors", "_a", "label", "best", "curr", "distance", "findBestMatch", "bestMatch", "toJSON", "ld", "fromJSON", "json"], "sources": ["../../../src/globalApi/FaceMatcher.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAChD,SAASC,sBAAsB,QAAQ,mCAAmC;AAC1E,SAASC,iBAAiB,QAAQ,sBAAsB;AAGxD,IAAAC,WAAA;EAKE,SAAAA,YACEC,MAAgJ,EAChJC,iBAA+B;IAA/B,IAAAA,iBAAA;MAAAA,iBAAA,MAA+B;IAAA;IAG/B,IAAI,CAACC,kBAAkB,GAAGD,iBAAiB;IAE3C,IAAME,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IAE5D,IAAI,CAACG,UAAU,CAACG,MAAM,EAAE;MACtB,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;;IAG5E,IAAIC,KAAK,GAAG,CAAC;IACb,IAAMC,iBAAiB,GAAG,SAAAA,CAAA;MAAM,mBAAUD,KAAK,EAAI;IAAnB,CAAmB;IAEnD,IAAI,CAACE,mBAAmB,GAAGP,UAAU,CAACQ,GAAG,CAAC,UAACC,IAAI;MAC7C,IAAIA,IAAI,YAAYf,sBAAsB,EAAE;QAC1C,OAAOe,IAAI;;MAGb,IAAIA,IAAI,YAAYC,YAAY,EAAE;QAChC,OAAO,IAAIhB,sBAAsB,CAACY,iBAAiB,EAAE,EAAE,CAACG,IAAI,CAAC,CAAC;;MAGhE,IAAIA,IAAI,CAACE,UAAU,IAAIF,IAAI,CAACE,UAAU,YAAYD,YAAY,EAAE;QAC9D,OAAO,IAAIhB,sBAAsB,CAACY,iBAAiB,EAAE,EAAE,CAACG,IAAI,CAACE,UAAU,CAAC,CAAC;;MAG3E,MAAM,IAAIP,KAAK,CAAC,qMAAqM,CAAC;IACxN,CAAC,CAAC;EACJ;EAEAQ,MAAA,CAAAC,cAAA,CAAWjB,WAAA,CAAAkB,SAAA,sBAAkB;SAA7B,SAAAC,CAAA;MAA4D,OAAO,IAAI,CAACR,mBAAmB;IAAC,CAAC;;;;EAC7FK,MAAA,CAAAC,cAAA,CAAWjB,WAAA,CAAAkB,SAAA,qBAAiB;SAA5B,SAAAC,CAAA;MAAyC,OAAO,IAAI,CAAChB,kBAAkB;IAAC,CAAC;;;;EAElEH,WAAA,CAAAkB,SAAA,CAAAE,mBAAmB,GAA1B,UAA2BC,eAA6B,EAAEC,WAA2B;IACnF,OAAOA,WAAW,CACfV,GAAG,CAAC,UAAAW,CAAC;MAAI,OAAAxB,iBAAiB,CAACwB,CAAC,EAAEF,eAAe,CAAC;IAArC,CAAqC,CAAC,CAC/CG,MAAM,CAAC,UAACC,EAAE,EAAEC,EAAE;MAAK,OAAAD,EAAE,GAAGC,EAAE;IAAP,CAAO,EAAE,CAAC,CAAC,IAC1BJ,WAAW,CAACf,MAAM,IAAI,CAAC,CAAC;EACjC,CAAC;EAEMP,WAAA,CAAAkB,SAAA,CAAAS,eAAe,GAAtB,UAAuBN,eAA6B;IAApD,IAAAO,KAAA;IACE,OAAO,IAAI,CAACC,kBAAkB,CAC3BjB,GAAG,CAAC,UAACkB,EAAsB;UAApBR,WAAA,GAAAQ,EAAA,CAAAR,WAAW;QAAES,KAAA,GAAAD,EAAA,CAAAC,KAAK;MAAO,WAAIlC,SAAS,CAC1CkC,KAAK,EACLH,KAAI,CAACR,mBAAmB,CAACC,eAAe,EAAEC,WAAW,CAAC,CACzD;IAHgC,CAGhC,CAAC,CACDE,MAAM,CAAC,UAACQ,IAAI,EAAEC,IAAI;MAAK,OAAAD,IAAI,CAACE,QAAQ,GAAGD,IAAI,CAACC,QAAQ,GAAGF,IAAI,GAAGC,IAAI;IAA3C,CAA2C,CAAC;EACxE,CAAC;EAEMjC,WAAA,CAAAkB,SAAA,CAAAiB,aAAa,GAApB,UAAqBd,eAA6B;IAChD,IAAMe,SAAS,GAAG,IAAI,CAACT,eAAe,CAACN,eAAe,CAAC;IACvD,OAAOe,SAAS,CAACF,QAAQ,GAAG,IAAI,CAAChC,iBAAiB,GAC9CkC,SAAS,GACT,IAAIvC,SAAS,CAAC,SAAS,EAAEuC,SAAS,CAACF,QAAQ,CAAC;EAClD,CAAC;EAEMlC,WAAA,CAAAkB,SAAA,CAAAmB,MAAM,GAAb;IACE,OAAO;MACLnC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzC2B,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACjB,GAAG,CAAC,UAAC0B,EAAE;QAAK,OAAAA,EAAE,CAACD,MAAM,EAAE;MAAX,CAAW;KACpE;EACH,CAAC;EAEarC,WAAA,CAAAuC,QAAQ,GAAtB,UAAuBC,IAAS;IAC9B,IAAMX,kBAAkB,GAAGW,IAAI,CAACX,kBAAkB,CAC/CjB,GAAG,CAAC,UAAC0B,EAAO;MAAK,OAAAxC,sBAAsB,CAACyC,QAAQ,CAACD,EAAE,CAAC;IAAnC,CAAmC,CAAC;IACxD,OAAO,IAAItC,WAAW,CAAC6B,kBAAkB,EAAEW,IAAI,CAACtC,iBAAiB,CAAC;EACpE,CAAC;EAEH,OAAAF,WAAC;AAAD,CAAC,CA7ED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}