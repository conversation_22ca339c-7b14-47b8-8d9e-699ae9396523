{"ast": null, "code": "/**\n * Service Supabase pour PresencePro (anciennement Firebase)\n * Maintient la compatibilité avec l'interface Firebase\n */\n\nimport { supabaseService } from './supabaseService';\nclass FirebaseService {\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers() {\n    return supabaseService.getUsers();\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId) {\n    return supabaseService.getUserById(userId);\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData) {\n    const user = await supabaseService.createUser(userData);\n    return user.id;\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId, userData) {\n    await supabaseService.updateUser(userId, userData);\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId) {\n    return supabaseService.deleteUser(userId);\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours\n   */\n  async getCourses() {\n    return supabaseService.getCourses();\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId) {\n    return supabaseService.getCoursesByTeacher(teacherId);\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData) {\n    return supabaseService.createCourse(courseData);\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData) {\n    return supabaseService.recordAttendance(attendanceData);\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId, date) {\n    return supabaseService.getAttendanceByCourse(courseId, date);\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId) {\n    return supabaseService.getAttendanceByStudent(studentId);\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId, file) {\n    return supabaseService.uploadProfileImage(userId, file);\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId, file) {\n    return supabaseService.uploadFaceImage(userId, file);\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl) {\n    return supabaseService.deleteImage(imageUrl);\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats() {\n    return supabaseService.getGlobalStats();\n  }\n}\n\n// Instance singleton du service Firebase (maintenant Supabase)\nexport const firebaseService = new FirebaseService();\nexport default firebaseService;", "map": {"version": 3, "names": ["supabaseService", "FirebaseService", "getUsers", "getUserById", "userId", "createUser", "userData", "user", "id", "updateUser", "deleteUser", "getCourses", "getCoursesByTeacher", "teacherId", "createCourse", "courseData", "recordAttendance", "attendanceData", "getAttendanceByCourse", "courseId", "date", "getAttendanceByStudent", "studentId", "uploadProfileImage", "file", "uploadFaceImage", "deleteImage", "imageUrl", "getGlobalStats", "firebaseService"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts"], "sourcesContent": ["/**\n * Service Supabase pour PresencePro (anciennement Firebase)\n * Maintient la compatibilité avec l'interface Firebase\n */\n\nimport { supabaseService } from './supabaseService';\nimport { User, Course, AttendanceRecord } from '../types';\n\nclass FirebaseService {\n\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers(): Promise<User[]> {\n    return supabaseService.getUsers();\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId: string): Promise<User | null> {\n    return supabaseService.getUserById(userId);\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData: Omit<User, 'id'>): Promise<string> {\n    const user = await supabaseService.createUser(userData);\n    return user.id;\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId: string, userData: Partial<User>): Promise<void> {\n    await supabaseService.updateUser(userId, userData);\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId: string): Promise<void> {\n    return supabaseService.deleteUser(userId);\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours\n   */\n  async getCourses(): Promise<Course[]> {\n    return supabaseService.getCourses();\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {\n    return supabaseService.getCoursesByTeacher(teacherId);\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {\n    return supabaseService.createCourse(courseData);\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {\n    return supabaseService.recordAttendance(attendanceData);\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {\n    return supabaseService.getAttendanceByCourse(courseId, date);\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {\n    return supabaseService.getAttendanceByStudent(studentId);\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId: string, file: File): Promise<string> {\n    return supabaseService.uploadProfileImage(userId, file);\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId: string, file: File): Promise<string> {\n    return supabaseService.uploadFaceImage(userId, file);\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl: string): Promise<void> {\n    return supabaseService.deleteImage(imageUrl);\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats(): Promise<any> {\n    return supabaseService.getGlobalStats();\n  }\n}\n\n// Instance singleton du service Firebase (maintenant Supabase)\nexport const firebaseService = new FirebaseService();\nexport default firebaseService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,eAAe,QAAQ,mBAAmB;AAGnD,MAAMC,eAAe,CAAC;EAEpB;;EAEA;AACF;AACA;EACE,MAAMC,QAAQA,CAAA,EAAoB;IAChC,OAAOF,eAAe,CAACE,QAAQ,CAAC,CAAC;EACnC;;EAEA;AACF;AACA;EACE,MAAMC,WAAWA,CAACC,MAAc,EAAwB;IACtD,OAAOJ,eAAe,CAACG,WAAW,CAACC,MAAM,CAAC;EAC5C;;EAEA;AACF;AACA;EACE,MAAMC,UAAUA,CAACC,QAA0B,EAAmB;IAC5D,MAAMC,IAAI,GAAG,MAAMP,eAAe,CAACK,UAAU,CAACC,QAAQ,CAAC;IACvD,OAAOC,IAAI,CAACC,EAAE;EAChB;;EAEA;AACF;AACA;EACE,MAAMC,UAAUA,CAACL,MAAc,EAAEE,QAAuB,EAAiB;IACvE,MAAMN,eAAe,CAACS,UAAU,CAACL,MAAM,EAAEE,QAAQ,CAAC;EACpD;;EAEA;AACF;AACA;EACE,MAAMI,UAAUA,CAACN,MAAc,EAAiB;IAC9C,OAAOJ,eAAe,CAACU,UAAU,CAACN,MAAM,CAAC;EAC3C;;EAEA;;EAEA;AACF;AACA;EACE,MAAMO,UAAUA,CAAA,EAAsB;IACpC,OAAOX,eAAe,CAACW,UAAU,CAAC,CAAC;EACrC;;EAEA;AACF;AACA;EACE,MAAMC,mBAAmBA,CAACC,SAAiB,EAAqB;IAC9D,OAAOb,eAAe,CAACY,mBAAmB,CAACC,SAAS,CAAC;EACvD;;EAEA;AACF;AACA;EACE,MAAMC,YAAYA,CAACC,UAA8B,EAAmB;IAClE,OAAOf,eAAe,CAACc,YAAY,CAACC,UAAU,CAAC;EACjD;;EAEA;;EAEA;AACF;AACA;EACE,MAAMC,gBAAgBA,CAACC,cAA4C,EAAmB;IACpF,OAAOjB,eAAe,CAACgB,gBAAgB,CAACC,cAAc,CAAC;EACzD;;EAEA;AACF;AACA;EACE,MAAMC,qBAAqBA,CAACC,QAAgB,EAAEC,IAAa,EAA+B;IACxF,OAAOpB,eAAe,CAACkB,qBAAqB,CAACC,QAAQ,EAAEC,IAAI,CAAC;EAC9D;;EAEA;AACF;AACA;EACE,MAAMC,sBAAsBA,CAACC,SAAiB,EAA+B;IAC3E,OAAOtB,eAAe,CAACqB,sBAAsB,CAACC,SAAS,CAAC;EAC1D;;EAEA;;EAEA;AACF;AACA;EACE,MAAMC,kBAAkBA,CAACnB,MAAc,EAAEoB,IAAU,EAAmB;IACpE,OAAOxB,eAAe,CAACuB,kBAAkB,CAACnB,MAAM,EAAEoB,IAAI,CAAC;EACzD;;EAEA;AACF;AACA;EACE,MAAMC,eAAeA,CAACrB,MAAc,EAAEoB,IAAU,EAAmB;IACjE,OAAOxB,eAAe,CAACyB,eAAe,CAACrB,MAAM,EAAEoB,IAAI,CAAC;EACtD;;EAEA;AACF;AACA;EACE,MAAME,WAAWA,CAACC,QAAgB,EAAiB;IACjD,OAAO3B,eAAe,CAAC0B,WAAW,CAACC,QAAQ,CAAC;EAC9C;;EAEA;;EAEA;AACF;AACA;EACE,MAAMC,cAAcA,CAAA,EAAiB;IACnC,OAAO5B,eAAe,CAAC4B,cAAc,CAAC,CAAC;EACzC;AACF;;AAEA;AACA,OAAO,MAAMC,eAAe,GAAG,IAAI5B,eAAe,CAAC,CAAC;AACpD,eAAe4B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}