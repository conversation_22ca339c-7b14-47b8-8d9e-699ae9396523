{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { createCanvas, createCanvasFromMedia, getContext2dOrThrow } from '../dom';\nimport { env } from '../env';\nimport { normalize } from './normalize';\nexport function extractImagePatches(img, boxes, _a) {\n  var width = _a.width,\n    height = _a.height;\n  return __awaiter(this, void 0, void 0, function () {\n    var imgCtx, bitmaps, imagePatchesDatas;\n    var _this = this;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          imgCtx = getContext2dOrThrow(img);\n          return [4 /*yield*/, Promise.all(boxes.map(function (box) {\n            return __awaiter(_this, void 0, void 0, function () {\n              var _a, y, ey, x, ex, fromX, fromY, imgData;\n              return __generator(this, function (_b) {\n                _a = box.padAtBorders(img.height, img.width), y = _a.y, ey = _a.ey, x = _a.x, ex = _a.ex;\n                fromX = x - 1;\n                fromY = y - 1;\n                imgData = imgCtx.getImageData(fromX, fromY, ex - fromX, ey - fromY);\n                return [2 /*return*/, env.isNodejs() ? createCanvasFromMedia(imgData) : createImageBitmap(imgData)];\n              });\n            });\n          }))];\n        case 1:\n          bitmaps = _b.sent();\n          imagePatchesDatas = [];\n          bitmaps.forEach(function (bmp) {\n            var patch = createCanvas({\n              width: width,\n              height: height\n            });\n            var patchCtx = getContext2dOrThrow(patch);\n            patchCtx.drawImage(bmp, 0, 0, width, height);\n            var data = patchCtx.getImageData(0, 0, width, height).data;\n            var currData = [];\n            // RGBA -> BGR\n            for (var i = 0; i < data.length; i += 4) {\n              currData.push(data[i + 2]);\n              currData.push(data[i + 1]);\n              currData.push(data[i]);\n            }\n            imagePatchesDatas.push(currData);\n          });\n          return [2 /*return*/, imagePatchesDatas.map(function (data) {\n            var t = tf.tidy(function () {\n              var imagePatchTensor = tf.transpose(tf.tensor4d(data, [1, width, height, 3]), [0, 2, 1, 3]).toFloat();\n              return normalize(imagePatchTensor);\n            });\n            return t;\n          })];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "createCanvas", "createCanvasFromMedia", "getContext2dOrThrow", "env", "normalize", "extractImagePatches", "img", "boxes", "_a", "width", "height", "imgCtx", "Promise", "all", "map", "box", "__awaiter", "_this", "padAtBorders", "y", "ey", "x", "ex", "fromX", "fromY", "imgData", "getImageData", "isNodejs", "createImageBitmap", "bitmaps", "_b", "sent", "imagePatchesDatas", "for<PERSON>ach", "bmp", "patch", "patchCtx", "drawImage", "data", "currData", "i", "length", "push", "t", "tidy", "imagePatchTensor", "transpose", "tensor4d", "toFloat"], "sources": ["../../../src/mtcnn/extractImagePatches.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,YAAY,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,QAAQ;AACjF,SAASC,GAAG,QAAQ,QAAQ;AAC5B,SAASC,SAAS,QAAQ,aAAa;AAEvC,OAAM,SAAgBC,mBAAmBA,CACvCC,GAAsB,EACtBC,KAAY,EACZC,EAA8B;MAA5BC,KAAA,GAAAD,EAAA,CAAAC,KAAK;IAAEC,MAAA,GAAAF,EAAA,CAAAE,MAAM;;;;;;;UAITC,MAAM,GAAGT,mBAAmB,CAACI,GAAG,CAAC;UAEvB,qBAAMM,OAAO,CAACC,GAAG,CAACN,KAAK,CAACO,GAAG,CAAC,UAAMC,GAAG;YAAA,OAAAC,SAAA,CAAAC,KAAA;;;gBAE7CT,EAAA,GAAmBO,GAAG,CAACG,YAAY,CAACZ,GAAG,CAACI,MAAM,EAAEJ,GAAG,CAACG,KAAK,CAAC,EAAxDU,CAAC,GAAAX,EAAA,CAAAW,CAAA,EAAEC,EAAE,GAAAZ,EAAA,CAAAY,EAAA,EAAEC,CAAC,GAAAb,EAAA,CAAAa,CAAA,EAAEC,EAAE,GAAAd,EAAA,CAAAc,EAAA;gBAEdC,KAAK,GAAGF,CAAC,GAAG,CAAC;gBACbG,KAAK,GAAGL,CAAC,GAAG,CAAC;gBACbM,OAAO,GAAGd,MAAM,CAACe,YAAY,CAACH,KAAK,EAAEC,KAAK,EAAGF,EAAE,GAAGC,KAAK,EAAIH,EAAE,GAAGI,KAAM,CAAC;gBAE7E,sBAAOrB,GAAG,CAACwB,QAAQ,EAAE,GAAG1B,qBAAqB,CAACwB,OAAO,CAAC,GAAGG,iBAAiB,CAACH,OAAO,CAAC;;;WACpF,CAAC,CAAC;;UATGI,OAAO,GAAGC,EAAA,CAAAC,IAAA,EASb;UAEGC,iBAAiB,GAAe,EAAE;UAExCH,OAAO,CAACI,OAAO,CAAC,UAAAC,GAAG;YACjB,IAAMC,KAAK,GAAGnC,YAAY,CAAC;cAAES,KAAK,EAAAA,KAAA;cAAEC,MAAM,EAAAA;YAAA,CAAE,CAAC;YAC7C,IAAM0B,QAAQ,GAAGlC,mBAAmB,CAACiC,KAAK,CAAC;YAC3CC,QAAQ,CAACC,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEzB,KAAK,EAAEC,MAAM,CAAC;YACpC,IAAA4B,IAAA,GAAAF,QAAA,CAAAV,YAAA,OAAAjB,KAAA,EAAAC,MAAA,EAAA4B,IAAI;YAEZ,IAAMC,QAAQ,GAAG,EAAE;YACnB;YACA,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,IAAE,CAAC,EAAE;cACpCD,QAAQ,CAACG,IAAI,CAACJ,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1BD,QAAQ,CAACG,IAAI,CAACJ,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1BD,QAAQ,CAACG,IAAI,CAACJ,IAAI,CAACE,CAAC,CAAC,CAAC;;YAExBR,iBAAiB,CAACU,IAAI,CAACH,QAAQ,CAAC;UAClC,CAAC,CAAC;UAGF,sBAAOP,iBAAiB,CAAClB,GAAG,CAAC,UAAAwB,IAAI;YAC/B,IAAMK,CAAC,GAAG5C,EAAE,CAAC6C,IAAI,CAAC;cAChB,IAAMC,gBAAgB,GAAG9C,EAAE,CAAC+C,SAAS,CACnC/C,EAAE,CAACgD,QAAQ,CAACT,IAAI,EAAE,CAAC,CAAC,EAAE7B,KAAK,EAAEC,MAAM,EAAE,CAAC,CAAC,CAAC,EACxC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb,CAACsC,OAAO,EAAiB;cAE1B,OAAO5C,SAAS,CAACyC,gBAAgB,CAAC;YACpC,CAAC,CAAC;YACF,OAAOF,CAAC;UACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}