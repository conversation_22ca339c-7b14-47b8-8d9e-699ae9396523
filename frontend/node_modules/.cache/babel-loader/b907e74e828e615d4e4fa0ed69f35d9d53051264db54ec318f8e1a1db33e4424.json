{"ast": null, "code": "import { env } from '../env';\nimport { getContext2dOrThrow } from './getContext2dOrThrow';\nimport { getMediaDimensions } from './getMediaDimensions';\nimport { isMediaLoaded } from './isMediaLoaded';\nexport function createCanvas(_a) {\n  var width = _a.width,\n    height = _a.height;\n  var createCanvasElement = env.getEnv().createCanvasElement;\n  var canvas = createCanvasElement();\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\nexport function createCanvasFromMedia(media, dims) {\n  var ImageData = env.getEnv().ImageData;\n  if (!(media instanceof ImageData) && !isMediaLoaded(media)) {\n    throw new Error('createCanvasFromMedia - media has not finished loading yet');\n  }\n  var _a = dims || getMediaDimensions(media),\n    width = _a.width,\n    height = _a.height;\n  var canvas = createCanvas({\n    width: width,\n    height: height\n  });\n  if (media instanceof ImageData) {\n    getContext2dOrThrow(canvas).putImageData(media, 0, 0);\n  } else {\n    getContext2dOrThrow(canvas).drawImage(media, 0, 0, width, height);\n  }\n  return canvas;\n}", "map": {"version": 3, "names": ["env", "getContext2dOrThrow", "getMediaDimensions", "isMediaLoaded", "createCanvas", "_a", "width", "height", "createCanvasElement", "getEnv", "canvas", "createCanvasFromMedia", "media", "dims", "ImageData", "Error", "putImageData", "drawImage"], "sources": ["../../../src/dom/createCanvas.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,GAAG,QAAQ,QAAQ;AAC5B,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,OAAM,SAAUC,YAAYA,CAACC,EAA8B;MAA5BC,KAAA,GAAAD,EAAA,CAAAC,KAAK;IAAEC,MAAA,GAAAF,EAAA,CAAAE,MAAM;EAElC,IAAAC,mBAAA,GAAAR,GAAA,CAAAS,MAAA,GAAAD,mBAAmB;EAC3B,IAAME,MAAM,GAAGF,mBAAmB,EAAE;EACpCE,MAAM,CAACJ,KAAK,GAAGA,KAAK;EACpBI,MAAM,CAACH,MAAM,GAAGA,MAAM;EACtB,OAAOG,MAAM;AACf;AAEA,OAAM,SAAUC,qBAAqBA,CAACC,KAAsD,EAAEC,IAAkB;EAEtG,IAAAC,SAAA,GAAAd,GAAA,CAAAS,MAAA,GAAAK,SAAS;EAEjB,IAAI,EAAEF,KAAK,YAAYE,SAAS,CAAC,IAAI,CAACX,aAAa,CAACS,KAAK,CAAC,EAAE;IAC1D,MAAM,IAAIG,KAAK,CAAC,4DAA4D,CAAC;;EAGzE,IAAAV,EAAA,GAAAQ,IAAA,IAAAX,kBAAA,CAAAU,KAAA,CAAqD;IAAnDN,KAAA,GAAAD,EAAA,CAAAC,KAAK;IAAEC,MAAA,GAAAF,EAAA,CAAAE,MAA4C;EAC3D,IAAMG,MAAM,GAAGN,YAAY,CAAC;IAAEE,KAAK,EAAAA,KAAA;IAAEC,MAAM,EAAAA;EAAA,CAAE,CAAC;EAE9C,IAAIK,KAAK,YAAYE,SAAS,EAAE;IAC9Bb,mBAAmB,CAACS,MAAM,CAAC,CAACM,YAAY,CAACJ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;GACtD,MAAM;IACLX,mBAAmB,CAACS,MAAM,CAAC,CAACO,SAAS,CAACL,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEN,KAAK,EAAEC,MAAM,CAAC;;EAEnE,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}