{"ast": null, "code": "import noop from \"../noop.js\";\nimport { point } from \"./cardinal.js\";\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 1:\n        {\n          this._context.moveTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 2:\n        {\n          this._context.lineTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 3:\n        {\n          this.point(this._x3, this._y3);\n          this.point(this._x4, this._y4);\n          this.point(this._x5, this._y5);\n          break;\n        }\n    }\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._x3 = x, this._y3 = y;\n        break;\n      case 1:\n        this._point = 2;\n        this._context.moveTo(this._x4 = x, this._y4 = y);\n        break;\n      case 2:\n        this._point = 3;\n        this._x5 = x, this._y5 = y;\n        break;\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(tension) {\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n  cardinal.tension = function (tension) {\n    return custom(+tension);\n  };\n  return cardinal;\n})(0);", "map": {"version": 3, "names": ["noop", "point", "CardinalClosed", "context", "tension", "_context", "_k", "prototype", "areaStart", "areaEnd", "lineStart", "_x0", "_x1", "_x2", "_x3", "_x4", "_x5", "_y0", "_y1", "_y2", "_y3", "_y4", "_y5", "NaN", "_point", "lineEnd", "moveTo", "closePath", "lineTo", "x", "y", "custom", "cardinal"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/d3-shape/src/curve/cardinalClosed.js"], "sourcesContent": ["import noop from \"../noop.js\";\nimport {point} from \"./cardinal.js\";\n\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,SAAQC,KAAK,QAAO,eAAe;AAEnC,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC/C,IAAI,CAACC,QAAQ,GAAGF,OAAO;EACvB,IAAI,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGF,OAAO,IAAI,CAAC;AAC7B;AAEAF,cAAc,CAACK,SAAS,GAAG;EACzBC,SAAS,EAAER,IAAI;EACfS,OAAO,EAAET,IAAI;EACbU,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAC/D,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGC,GAAG;IACrE,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,QAAQ,IAAI,CAACD,MAAM;MACjB,KAAK,CAAC;QAAE;UACN,IAAI,CAACnB,QAAQ,CAACqB,MAAM,CAAC,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UACxC,IAAI,CAACf,QAAQ,CAACsB,SAAS,CAAC,CAAC;UACzB;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAACtB,QAAQ,CAACuB,MAAM,CAAC,IAAI,CAACd,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UACxC,IAAI,CAACf,QAAQ,CAACsB,SAAS,CAAC,CAAC;UACzB;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAAC1B,KAAK,CAAC,IAAI,CAACa,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UAC9B,IAAI,CAACnB,KAAK,CAAC,IAAI,CAACc,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UAC9B,IAAI,CAACpB,KAAK,CAAC,IAAI,CAACe,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UAC9B;QACF;IACF;EACF,CAAC;EACDrB,KAAK,EAAE,SAAAA,CAAS4B,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACN,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACV,GAAG,GAAGe,CAAC,EAAE,IAAI,CAACT,GAAG,GAAGU,CAAC;QAAE;MACrD,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;QAAE,IAAI,CAACnB,QAAQ,CAACqB,MAAM,CAAC,IAAI,CAACX,GAAG,GAAGc,CAAC,EAAE,IAAI,CAACR,GAAG,GAAGS,CAAC,CAAC;QAAE;MAC3E,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;QAAE,IAAI,CAACR,GAAG,GAAGa,CAAC,EAAE,IAAI,CAACP,GAAG,GAAGQ,CAAC;QAAE;MACrD;QAAS7B,KAAK,CAAC,IAAI,EAAE4B,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IACA,IAAI,CAACnB,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGgB,CAAC;IACtD,IAAI,CAACZ,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGW,CAAC;EACxD;AACF,CAAC;AAED,eAAe,CAAC,SAASC,MAAMA,CAAC3B,OAAO,EAAE;EAEvC,SAAS4B,QAAQA,CAAC7B,OAAO,EAAE;IACzB,OAAO,IAAID,cAAc,CAACC,OAAO,EAAEC,OAAO,CAAC;EAC7C;EAEA4B,QAAQ,CAAC5B,OAAO,GAAG,UAASA,OAAO,EAAE;IACnC,OAAO2B,MAAM,CAAC,CAAC3B,OAAO,CAAC;EACzB,CAAC;EAED,OAAO4B,QAAQ;AACjB,CAAC,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}