{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Contexte d'authentification pour PresencePro avec Supabase\n */\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../config/supabase';\nimport { UserRole } from '../types';\nimport { supabaseService } from '../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [supabaseUser, setSupabaseUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Fonction pour obtenir la route basée sur le rôle\n  const getRoleBasedRoute = () => {\n    if (!user) return '/login';\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return '/admin-dashboard';\n      case UserRole.TEACHER:\n        return '/teacher-dashboard';\n      case UserRole.STUDENT:\n        return '/student-dashboard';\n      default:\n        return '/login';\n    }\n  };\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        // Obtenir la session actuelle\n        const {\n          data: {\n            session\n          }\n        } = await supabase.auth.getSession();\n        if (session !== null && session !== void 0 && session.user) {\n          setSupabaseUser(session.user);\n\n          // Récupérer les données utilisateur depuis notre base\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email);\n            setUser(userData);\n          } catch (error) {\n            console.error('Erreur lors de la récupération des données utilisateur:', error);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur lors de l\\'initialisation de l\\'authentification:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    initializeAuth();\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      if (event === 'SIGNED_IN' && session !== null && session !== void 0 && session.user) {\n        setSupabaseUser(session.user);\n        try {\n          const userData = await supabaseService.getUserByEmail(session.user.email);\n          setUser(userData);\n        } catch (error) {\n          console.error('Erreur lors de la récupération des données utilisateur:', error);\n        }\n      } else if (event === 'SIGNED_OUT') {\n        setSupabaseUser(null);\n        setUser(null);\n      }\n      setLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // Fonction de connexion\n  const signIn = async (email, password) => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) {\n        return {\n          user: null,\n          error: error.message\n        };\n      }\n      if (data.user) {\n        setSupabaseUser(data.user);\n        try {\n          const userData = await supabaseService.getUserByEmail(data.user.email);\n          setUser(userData);\n          return {\n            user: userData,\n            error: null\n          };\n        } catch (userError) {\n          return {\n            user: null,\n            error: 'Utilisateur non trouvé dans la base de données'\n          };\n        }\n      }\n      return {\n        user: null,\n        error: 'Erreur de connexion'\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur inconnue'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction d'inscription\n  const signUp = async (email, password, userData) => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password\n      });\n      if (error) {\n        return {\n          user: null,\n          error: error.message\n        };\n      }\n      if (data.user) {\n        // Créer l'utilisateur dans notre base de données\n        try {\n          const newUser = {\n            username: userData.username || email.split('@')[0],\n            email,\n            firstName: userData.firstName || '',\n            lastName: userData.lastName || '',\n            role: userData.role || UserRole.STUDENT,\n            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' : userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n            isActive: true,\n            dateJoined: new Date().toISOString()\n          };\n          const createdUser = await supabaseService.createUser(newUser);\n          setUser(createdUser);\n          return {\n            user: createdUser,\n            error: null\n          };\n        } catch (userError) {\n          return {\n            user: null,\n            error: 'Erreur lors de la création du profil utilisateur'\n          };\n        }\n      }\n      return {\n        user: null,\n        error: 'Erreur lors de l\\'inscription'\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur inconnue'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction de déconnexion\n  const signOut = async () => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSupabaseUser(null);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  };\n\n  // Fonction de mise à jour du profil\n  const updateProfile = async userData => {\n    if (!user) {\n      return {\n        user: null,\n        error: 'Aucun utilisateur connecté'\n      };\n    }\n    try {\n      const updatedUser = await supabaseService.updateUser(user.id, userData);\n      setUser(updatedUser);\n      return {\n        user: updatedUser,\n        error: null\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur de mise à jour'\n      };\n    }\n  };\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n    getRoleBasedRoute\n  };\n\n  // Afficher un loader pendant l'initialisation avec timeout\n  if (loading) {\n    // Timeout de sécurité pour éviter le chargement infini\n    setTimeout(() => {\n      if (loading) {\n        console.warn('⚠️ Timeout de chargement - forçage de l\\'arrêt du loading');\n        setLoading(false);\n      }\n    }, 5000);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Chargement...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          marginTop: '10px',\n          color: '#666'\n        },\n        children: \"Si le chargement persiste, v\\xE9rifiez la console pour les erreurs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\n_s(AuthProvider, \"D2vDCBuOQTUoDpzCc/919H0GjjI=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "supabase", "UserRole", "supabaseService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "supabaseUser", "setSupabaseUser", "loading", "setLoading", "getRoleBasedRoute", "role", "ADMIN", "TEACHER", "STUDENT", "initializeAuth", "data", "session", "auth", "getSession", "userData", "getUserByEmail", "email", "error", "console", "subscription", "onAuthStateChange", "event", "unsubscribe", "signIn", "password", "signInWithPassword", "message", "userError", "Error", "signUp", "newUser", "username", "split", "firstName", "lastName", "fullName", "trim", "roleDisplay", "isActive", "dateJoined", "Date", "toISOString", "created<PERSON>ser", "createUser", "signOut", "updateProfile", "updatedUser", "updateUser", "id", "value", "isAuthenticated", "setTimeout", "warn", "style", "display", "flexDirection", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "color", "Provider", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["/**\n * Contexte d'authentification pour PresencePro avec Supabase\n */\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { supabase } from '../config/supabase';\nimport { User as SupabaseUser } from '@supabase/supabase-js';\nimport { User, UserRole } from '../types';\nimport { supabaseService } from '../services/supabaseService';\n\ninterface AuthContextType {\n  user: User | null;\n  supabaseUser: SupabaseUser | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;\n  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;\n  signOut: () => Promise<void>;\n  updateProfile: (userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;\n  getRoleBasedRoute: () => string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Provider\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Fonction pour obtenir la route basée sur le rôle\n  const getRoleBasedRoute = (): string => {\n    if (!user) return '/login';\n\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return '/admin-dashboard';\n      case UserRole.TEACHER:\n        return '/teacher-dashboard';\n      case UserRole.STUDENT:\n        return '/student-dashboard';\n      default:\n        return '/login';\n    }\n  };\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        // Obtenir la session actuelle\n        const { data: { session } } = await supabase.auth.getSession();\n\n        if (session?.user) {\n          setSupabaseUser(session.user);\n\n          // Récupérer les données utilisateur depuis notre base\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email!);\n            setUser(userData);\n          } catch (error) {\n            console.error('Erreur lors de la récupération des données utilisateur:', error);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur lors de l\\'initialisation de l\\'authentification:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initializeAuth();\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          setSupabaseUser(session.user);\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email!);\n            setUser(userData);\n          } catch (error) {\n            console.error('Erreur lors de la récupération des données utilisateur:', error);\n          }\n        } else if (event === 'SIGNED_OUT') {\n          setSupabaseUser(null);\n          setUser(null);\n        }\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // Fonction de connexion\n  const signIn = async (email: string, password: string): Promise<{ user: User | null; error: string | null }> => {\n    try {\n      setLoading(true);\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { user: null, error: error.message };\n      }\n\n      if (data.user) {\n        setSupabaseUser(data.user);\n\n        try {\n          const userData = await supabaseService.getUserByEmail(data.user.email!);\n          setUser(userData);\n          return { user: userData, error: null };\n        } catch (userError) {\n          return { user: null, error: 'Utilisateur non trouvé dans la base de données' };\n        }\n      }\n\n      return { user: null, error: 'Erreur de connexion' };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction d'inscription\n  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {\n    try {\n      setLoading(true);\n\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { user: null, error: error.message };\n      }\n\n      if (data.user) {\n        // Créer l'utilisateur dans notre base de données\n        try {\n          const newUser: Omit<User, 'id'> = {\n            username: userData.username || email.split('@')[0],\n            email,\n            firstName: userData.firstName || '',\n            lastName: userData.lastName || '',\n            role: userData.role || UserRole.STUDENT,\n            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' :\n                        userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n            isActive: true,\n            dateJoined: new Date().toISOString(),\n          };\n\n          const createdUser = await supabaseService.createUser(newUser);\n          setUser(createdUser);\n          return { user: createdUser, error: null };\n        } catch (userError) {\n          return { user: null, error: 'Erreur lors de la création du profil utilisateur' };\n        }\n      }\n\n      return { user: null, error: 'Erreur lors de l\\'inscription' };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction de déconnexion\n  const signOut = async (): Promise<void> => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSupabaseUser(null);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  };\n\n  // Fonction de mise à jour du profil\n  const updateProfile = async (userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {\n    if (!user) {\n      return { user: null, error: 'Aucun utilisateur connecté' };\n    }\n\n    try {\n      const updatedUser = await supabaseService.updateUser(user.id, userData);\n      setUser(updatedUser);\n      return { user: updatedUser, error: null };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur de mise à jour' };\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    supabaseUser,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n    getRoleBasedRoute,\n  };\n\n  // Afficher un loader pendant l'initialisation avec timeout\n  if (loading) {\n    // Timeout de sécurité pour éviter le chargement infini\n    setTimeout(() => {\n      if (loading) {\n        console.warn('⚠️ Timeout de chargement - forçage de l\\'arrêt du loading');\n        setLoading(false);\n      }\n    }, 5000);\n\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      }}>\n        <div>Chargement...</div>\n        <div style={{ fontSize: '14px', marginTop: '10px', color: '#666' }}>\n          Si le chargement persiste, vérifiez la console pour les erreurs\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAAeC,QAAQ,QAAQ,UAAU;AACzC,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc9D,MAAMC,WAAW,gBAAGT,aAAa,CAA8BU,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMkB,iBAAiB,GAAGA,CAAA,KAAc;IACtC,IAAI,CAACN,IAAI,EAAE,OAAO,QAAQ;IAE1B,QAAQA,IAAI,CAACO,IAAI;MACf,KAAKhB,QAAQ,CAACiB,KAAK;QACjB,OAAO,kBAAkB;MAC3B,KAAKjB,QAAQ,CAACkB,OAAO;QACnB,OAAO,oBAAoB;MAC7B,KAAKlB,QAAQ,CAACmB,OAAO;QACnB,OAAO,oBAAoB;MAC7B;QACE,OAAO,QAAQ;IACnB;EACF,CAAC;;EAED;EACArB,SAAS,CAAC,MAAM;IACd,MAAMsB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF;QACA,MAAM;UAAEC,IAAI,EAAE;YAAEC;UAAQ;QAAE,CAAC,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAACC,UAAU,CAAC,CAAC;QAE9D,IAAIF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEb,IAAI,EAAE;UACjBG,eAAe,CAACU,OAAO,CAACb,IAAI,CAAC;;UAE7B;UACA,IAAI;YACF,MAAMgB,QAAQ,GAAG,MAAMxB,eAAe,CAACyB,cAAc,CAACJ,OAAO,CAACb,IAAI,CAACkB,KAAM,CAAC;YAC1EjB,OAAO,CAACe,QAAQ,CAAC;UACnB,CAAC,CAAC,OAAOG,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;UACjF;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0DAA0D,EAAEA,KAAK,CAAC;MAClF,CAAC,SAAS;QACRd,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDM,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAM;MAAEC,IAAI,EAAE;QAAES;MAAa;IAAE,CAAC,GAAG/B,QAAQ,CAACwB,IAAI,CAACQ,iBAAiB,CAChE,OAAOC,KAAK,EAAEV,OAAO,KAAK;MACxB,IAAIU,KAAK,KAAK,WAAW,IAAIV,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEb,IAAI,EAAE;QAC1CG,eAAe,CAACU,OAAO,CAACb,IAAI,CAAC;QAC7B,IAAI;UACF,MAAMgB,QAAQ,GAAG,MAAMxB,eAAe,CAACyB,cAAc,CAACJ,OAAO,CAACb,IAAI,CAACkB,KAAM,CAAC;UAC1EjB,OAAO,CAACe,QAAQ,CAAC;QACnB,CAAC,CAAC,OAAOG,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;QACjF;MACF,CAAC,MAAM,IAAII,KAAK,KAAK,YAAY,EAAE;QACjCpB,eAAe,CAAC,IAAI,CAAC;QACrBF,OAAO,CAAC,IAAI,CAAC;MACf;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMgB,YAAY,CAACG,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,MAAM,GAAG,MAAAA,CAAOP,KAAa,EAAEQ,QAAgB,KAA2D;IAC9G,IAAI;MACFrB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM;QAAEO,IAAI;QAAEO;MAAM,CAAC,GAAG,MAAM7B,QAAQ,CAACwB,IAAI,CAACa,kBAAkB,CAAC;QAC7DT,KAAK;QACLQ;MACF,CAAC,CAAC;MAEF,IAAIP,KAAK,EAAE;QACT,OAAO;UAAEnB,IAAI,EAAE,IAAI;UAAEmB,KAAK,EAAEA,KAAK,CAACS;QAAQ,CAAC;MAC7C;MAEA,IAAIhB,IAAI,CAACZ,IAAI,EAAE;QACbG,eAAe,CAACS,IAAI,CAACZ,IAAI,CAAC;QAE1B,IAAI;UACF,MAAMgB,QAAQ,GAAG,MAAMxB,eAAe,CAACyB,cAAc,CAACL,IAAI,CAACZ,IAAI,CAACkB,KAAM,CAAC;UACvEjB,OAAO,CAACe,QAAQ,CAAC;UACjB,OAAO;YAAEhB,IAAI,EAAEgB,QAAQ;YAAEG,KAAK,EAAE;UAAK,CAAC;QACxC,CAAC,CAAC,OAAOU,SAAS,EAAE;UAClB,OAAO;YAAE7B,IAAI,EAAE,IAAI;YAAEmB,KAAK,EAAE;UAAiD,CAAC;QAChF;MACF;MAEA,OAAO;QAAEnB,IAAI,EAAE,IAAI;QAAEmB,KAAK,EAAE;MAAsB,CAAC;IACrD,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEnB,IAAI,EAAE,IAAI;QAAEmB,KAAK,EAAEA,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACS,OAAO,GAAG;MAAkB,CAAC;IAC1F,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,MAAM,GAAG,MAAAA,CAAOb,KAAa,EAAEQ,QAAgB,EAAEV,QAAuB,KAA2D;IACvI,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM;QAAEO,IAAI;QAAEO;MAAM,CAAC,GAAG,MAAM7B,QAAQ,CAACwB,IAAI,CAACiB,MAAM,CAAC;QACjDb,KAAK;QACLQ;MACF,CAAC,CAAC;MAEF,IAAIP,KAAK,EAAE;QACT,OAAO;UAAEnB,IAAI,EAAE,IAAI;UAAEmB,KAAK,EAAEA,KAAK,CAACS;QAAQ,CAAC;MAC7C;MAEA,IAAIhB,IAAI,CAACZ,IAAI,EAAE;QACb;QACA,IAAI;UACF,MAAMgC,OAAyB,GAAG;YAChCC,QAAQ,EAAEjB,QAAQ,CAACiB,QAAQ,IAAIf,KAAK,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClDhB,KAAK;YACLiB,SAAS,EAAEnB,QAAQ,CAACmB,SAAS,IAAI,EAAE;YACnCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,EAAE;YACjC7B,IAAI,EAAES,QAAQ,CAACT,IAAI,IAAIhB,QAAQ,CAACmB,OAAO;YACvC2B,QAAQ,EAAE,GAAGrB,QAAQ,CAACmB,SAAS,IAAI,EAAE,IAAInB,QAAQ,CAACoB,QAAQ,IAAI,EAAE,EAAE,CAACE,IAAI,CAAC,CAAC;YACzEC,WAAW,EAAEvB,QAAQ,CAACT,IAAI,KAAKhB,QAAQ,CAACiB,KAAK,GAAG,gBAAgB,GACpDQ,QAAQ,CAACT,IAAI,KAAKhB,QAAQ,CAACkB,OAAO,GAAG,YAAY,GAAG,UAAU;YAC1E+B,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACrC,CAAC;UAED,MAAMC,WAAW,GAAG,MAAMpD,eAAe,CAACqD,UAAU,CAACb,OAAO,CAAC;UAC7D/B,OAAO,CAAC2C,WAAW,CAAC;UACpB,OAAO;YAAE5C,IAAI,EAAE4C,WAAW;YAAEzB,KAAK,EAAE;UAAK,CAAC;QAC3C,CAAC,CAAC,OAAOU,SAAS,EAAE;UAClB,OAAO;YAAE7B,IAAI,EAAE,IAAI;YAAEmB,KAAK,EAAE;UAAmD,CAAC;QAClF;MACF;MAEA,OAAO;QAAEnB,IAAI,EAAE,IAAI;QAAEmB,KAAK,EAAE;MAAgC,CAAC;IAC/D,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEnB,IAAI,EAAE,IAAI;QAAEmB,KAAK,EAAEA,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACS,OAAO,GAAG;MAAkB,CAAC;IAC1F,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,OAAO,GAAG,MAAAA,CAAA,KAA2B;IACzC,IAAI;MACF,MAAMxD,QAAQ,CAACwB,IAAI,CAACgC,OAAO,CAAC,CAAC;MAC7B7C,OAAO,CAAC,IAAI,CAAC;MACbE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM4B,aAAa,GAAG,MAAO/B,QAAuB,IAA2D;IAC7G,IAAI,CAAChB,IAAI,EAAE;MACT,OAAO;QAAEA,IAAI,EAAE,IAAI;QAAEmB,KAAK,EAAE;MAA6B,CAAC;IAC5D;IAEA,IAAI;MACF,MAAM6B,WAAW,GAAG,MAAMxD,eAAe,CAACyD,UAAU,CAACjD,IAAI,CAACkD,EAAE,EAAElC,QAAQ,CAAC;MACvEf,OAAO,CAAC+C,WAAW,CAAC;MACpB,OAAO;QAAEhD,IAAI,EAAEgD,WAAW;QAAE7B,KAAK,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEnB,IAAI,EAAE,IAAI;QAAEmB,KAAK,EAAEA,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACS,OAAO,GAAG;MAAwB,CAAC;IAChG;EACF,CAAC;EAED,MAAMuB,KAAsB,GAAG;IAC7BnD,IAAI;IACJE,YAAY;IACZE,OAAO;IACPgD,eAAe,EAAE,CAAC,CAACpD,IAAI;IACvByB,MAAM;IACNM,MAAM;IACNe,OAAO;IACPC,aAAa;IACbzC;EACF,CAAC;;EAED;EACA,IAAIF,OAAO,EAAE;IACX;IACAiD,UAAU,CAAC,MAAM;MACf,IAAIjD,OAAO,EAAE;QACXgB,OAAO,CAACkC,IAAI,CAAC,2DAA2D,CAAC;QACzEjD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,EAAE,IAAI,CAAC;IAER,oBACEX,OAAA;MAAK6D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAA/D,QAAA,gBACAJ,OAAA;QAAAI,QAAA,EAAK;MAAa;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxBvE,OAAA;QAAK6D,KAAK,EAAE;UAAEM,QAAQ,EAAE,MAAM;UAAEK,SAAS,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAArE,QAAA,EAAC;MAEpE;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvE,OAAA,CAACC,WAAW,CAACyE,QAAQ;IAACjB,KAAK,EAAEA,KAAM;IAAArD,QAAA,EAChCA;EAAQ;IAAAgE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAlE,EAAA,CA9NaF,YAAyC;AAAAwE,EAAA,GAAzCxE,YAAyC;AA+NtD,OAAO,MAAMyE,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGrF,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI6E,OAAO,KAAK5E,SAAS,EAAE;IACzB,MAAM,IAAIkC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAO0C,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe3E,WAAW;AAAC,IAAA0E,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}