{"ast": null, "code": "/**\n * Service Firebase pour PresencePro\n */\n\nimport { collection, doc, getDocs, getDoc, addDoc, updateDoc, deleteDoc, query, where, orderBy, Timestamp } from 'firebase/firestore';\nimport { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';\nimport { db, storage } from '../config/firebase';\nclass FirebaseService {\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers() {\n    try {\n      const usersCollection = collection(db, 'users');\n      const usersSnapshot = await getDocs(usersCollection);\n      return usersSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      }));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des utilisateurs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId) {\n    try {\n      const userDoc = doc(db, 'users', userId);\n      const userSnapshot = await getDoc(userDoc);\n      if (userSnapshot.exists()) {\n        return {\n          id: userSnapshot.id,\n          ...userSnapshot.data()\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData) {\n    try {\n      const usersCollection = collection(db, 'users');\n      const docRef = await addDoc(usersCollection, {\n        ...userData,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId, userData) {\n    try {\n      const userDoc = doc(db, 'users', userId);\n      await updateDoc(userDoc, {\n        ...userData,\n        updatedAt: Timestamp.now()\n      });\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId) {\n    try {\n      const userDoc = doc(db, 'users', userId);\n      await deleteDoc(userDoc);\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours\n   */\n  async getCourses() {\n    try {\n      const coursesCollection = collection(db, 'courses');\n      const coursesSnapshot = await getDocs(coursesCollection);\n      return coursesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      }));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId) {\n    try {\n      const coursesCollection = collection(db, 'courses');\n      const q = query(coursesCollection, where('teacherId', '==', teacherId));\n      const coursesSnapshot = await getDocs(q);\n      return coursesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      }));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours de l\\'enseignant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData) {\n    try {\n      const coursesCollection = collection(db, 'courses');\n      const docRef = await addDoc(coursesCollection, {\n        ...courseData,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Erreur lors de la création du cours:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData) {\n    try {\n      const attendanceCollection = collection(db, 'attendance');\n      const docRef = await addDoc(attendanceCollection, {\n        ...attendanceData,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Erreur lors de l\\'enregistrement de la présence:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId, date) {\n    try {\n      const attendanceCollection = collection(db, 'attendance');\n      let q = query(attendanceCollection, where('courseId', '==', courseId));\n      if (date) {\n        q = query(q, where('date', '==', date));\n      }\n      q = query(q, orderBy('createdAt', 'desc'));\n      const attendanceSnapshot = await getDocs(q);\n      return attendanceSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      }));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId) {\n    try {\n      const attendanceCollection = collection(db, 'attendance');\n      const q = query(attendanceCollection, where('studentId', '==', studentId), orderBy('date', 'desc'));\n      const attendanceSnapshot = await getDocs(q);\n      return attendanceSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      }));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences de l\\'étudiant:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId, file) {\n    try {\n      const imageRef = ref(storage, `profile-images/${userId}/${file.name}`);\n      const snapshot = await uploadBytes(imageRef, file);\n      const downloadURL = await getDownloadURL(snapshot.ref);\n      return downloadURL;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId, file) {\n    try {\n      const imageRef = ref(storage, `face-images/${userId}/${Date.now()}_${file.name}`);\n      const snapshot = await uploadBytes(imageRef, file);\n      const downloadURL = await getDownloadURL(snapshot.ref);\n      return downloadURL;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image faciale:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl) {\n    try {\n      const imageRef = ref(storage, imageUrl);\n      await deleteObject(imageRef);\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats() {\n    try {\n      // Compter les utilisateurs par rôle\n      const usersCollection = collection(db, 'users');\n      const usersSnapshot = await getDocs(usersCollection);\n      const users = usersSnapshot.docs.map(doc => doc.data());\n      const stats = {\n        totalUsers: users.length,\n        totalStudents: users.filter(user => user.role === 'student').length,\n        totalTeachers: users.filter(user => user.role === 'teacher').length,\n        totalAdmins: users.filter(user => user.role === 'admin').length\n      };\n\n      // Compter les cours\n      const coursesCollection = collection(db, 'courses');\n      const coursesSnapshot = await getDocs(coursesCollection);\n      stats.totalCourses = coursesSnapshot.size;\n      return stats;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  }\n}\n\n// Instance singleton du service Firebase\nexport const firebaseService = new FirebaseService();\nexport default firebaseService;", "map": {"version": 3, "names": ["collection", "doc", "getDocs", "getDoc", "addDoc", "updateDoc", "deleteDoc", "query", "where", "orderBy", "Timestamp", "ref", "uploadBytes", "getDownloadURL", "deleteObject", "db", "storage", "FirebaseService", "getUsers", "usersCollection", "usersSnapshot", "docs", "map", "id", "data", "error", "console", "getUserById", "userId", "userDoc", "userSnapshot", "exists", "createUser", "userData", "doc<PERSON>ef", "createdAt", "now", "updatedAt", "updateUser", "deleteUser", "getCourses", "coursesCollection", "coursesSnapshot", "getCoursesByTeacher", "teacherId", "q", "createCourse", "courseData", "recordAttendance", "attendanceData", "attendanceCollection", "getAttendanceByCourse", "courseId", "date", "attendanceSnapshot", "getAttendanceByStudent", "studentId", "uploadProfileImage", "file", "imageRef", "name", "snapshot", "downloadURL", "uploadFaceImage", "Date", "deleteImage", "imageUrl", "getGlobalStats", "users", "stats", "totalUsers", "length", "totalStudents", "filter", "user", "role", "totalTeachers", "totalAdmins", "totalCourses", "size", "firebaseService"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/services/firebaseService.ts"], "sourcesContent": ["/**\n * Service Firebase pour PresencePro\n */\n\nimport { \n  collection, \n  doc, \n  getDocs, \n  getDoc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  query, \n  where, \n  orderBy, \n  limit,\n  Timestamp \n} from 'firebase/firestore';\nimport { \n  ref, \n  uploadBytes, \n  getDownloadURL, \n  deleteObject \n} from 'firebase/storage';\nimport { db, storage } from '../config/firebase';\nimport { User, Course, AttendanceRecord, StudentGroup } from '../types';\n\nclass FirebaseService {\n  \n  // ==================== GESTION DES UTILISATEURS ====================\n  \n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers(): Promise<User[]> {\n    try {\n      const usersCollection = collection(db, 'users');\n      const usersSnapshot = await getDocs(usersCollection);\n      return usersSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as User));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des utilisateurs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId: string): Promise<User | null> {\n    try {\n      const userDoc = doc(db, 'users', userId);\n      const userSnapshot = await getDoc(userDoc);\n      \n      if (userSnapshot.exists()) {\n        return {\n          id: userSnapshot.id,\n          ...userSnapshot.data()\n        } as User;\n      }\n      return null;\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData: Omit<User, 'id'>): Promise<string> {\n    try {\n      const usersCollection = collection(db, 'users');\n      const docRef = await addDoc(usersCollection, {\n        ...userData,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId: string, userData: Partial<User>): Promise<void> {\n    try {\n      const userDoc = doc(db, 'users', userId);\n      await updateDoc(userDoc, {\n        ...userData,\n        updatedAt: Timestamp.now()\n      });\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId: string): Promise<void> {\n    try {\n      const userDoc = doc(db, 'users', userId);\n      await deleteDoc(userDoc);\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours\n   */\n  async getCourses(): Promise<Course[]> {\n    try {\n      const coursesCollection = collection(db, 'courses');\n      const coursesSnapshot = await getDocs(coursesCollection);\n      return coursesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as Course));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {\n    try {\n      const coursesCollection = collection(db, 'courses');\n      const q = query(coursesCollection, where('teacherId', '==', teacherId));\n      const coursesSnapshot = await getDocs(q);\n      return coursesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as Course));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours de l\\'enseignant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const coursesCollection = collection(db, 'courses');\n      const docRef = await addDoc(coursesCollection, {\n        ...courseData,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Erreur lors de la création du cours:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {\n    try {\n      const attendanceCollection = collection(db, 'attendance');\n      const docRef = await addDoc(attendanceCollection, {\n        ...attendanceData,\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Erreur lors de l\\'enregistrement de la présence:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {\n    try {\n      const attendanceCollection = collection(db, 'attendance');\n      let q = query(attendanceCollection, where('courseId', '==', courseId));\n      \n      if (date) {\n        q = query(q, where('date', '==', date));\n      }\n      \n      q = query(q, orderBy('createdAt', 'desc'));\n      \n      const attendanceSnapshot = await getDocs(q);\n      return attendanceSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as AttendanceRecord));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {\n    try {\n      const attendanceCollection = collection(db, 'attendance');\n      const q = query(\n        attendanceCollection, \n        where('studentId', '==', studentId),\n        orderBy('date', 'desc')\n      );\n      \n      const attendanceSnapshot = await getDocs(q);\n      return attendanceSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as AttendanceRecord));\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences de l\\'étudiant:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId: string, file: File): Promise<string> {\n    try {\n      const imageRef = ref(storage, `profile-images/${userId}/${file.name}`);\n      const snapshot = await uploadBytes(imageRef, file);\n      const downloadURL = await getDownloadURL(snapshot.ref);\n      return downloadURL;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId: string, file: File): Promise<string> {\n    try {\n      const imageRef = ref(storage, `face-images/${userId}/${Date.now()}_${file.name}`);\n      const snapshot = await uploadBytes(imageRef, file);\n      const downloadURL = await getDownloadURL(snapshot.ref);\n      return downloadURL;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image faciale:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl: string): Promise<void> {\n    try {\n      const imageRef = ref(storage, imageUrl);\n      await deleteObject(imageRef);\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats(): Promise<any> {\n    try {\n      // Compter les utilisateurs par rôle\n      const usersCollection = collection(db, 'users');\n      const usersSnapshot = await getDocs(usersCollection);\n      const users = usersSnapshot.docs.map(doc => doc.data());\n      \n      const stats = {\n        totalUsers: users.length,\n        totalStudents: users.filter(user => user.role === 'student').length,\n        totalTeachers: users.filter(user => user.role === 'teacher').length,\n        totalAdmins: users.filter(user => user.role === 'admin').length,\n      };\n\n      // Compter les cours\n      const coursesCollection = collection(db, 'courses');\n      const coursesSnapshot = await getDocs(coursesCollection);\n      stats.totalCourses = coursesSnapshot.size;\n\n      return stats;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  }\n}\n\n// Instance singleton du service Firebase\nexport const firebaseService = new FirebaseService();\nexport default firebaseService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SACEA,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,OAAO,EAEPC,SAAS,QACJ,oBAAoB;AAC3B,SACEC,GAAG,EACHC,WAAW,EACXC,cAAc,EACdC,YAAY,QACP,kBAAkB;AACzB,SAASC,EAAE,EAAEC,OAAO,QAAQ,oBAAoB;AAGhD,MAAMC,eAAe,CAAC;EAEpB;;EAEA;AACF;AACA;EACE,MAAMC,QAAQA,CAAA,EAAoB;IAChC,IAAI;MACF,MAAMC,eAAe,GAAGnB,UAAU,CAACe,EAAE,EAAE,OAAO,CAAC;MAC/C,MAAMK,aAAa,GAAG,MAAMlB,OAAO,CAACiB,eAAe,CAAC;MACpD,OAAOC,aAAa,CAACC,IAAI,CAACC,GAAG,CAACrB,GAAG,KAAK;QACpCsB,EAAE,EAAEtB,GAAG,CAACsB,EAAE;QACV,GAAGtB,GAAG,CAACuB,IAAI,CAAC;MACd,CAAC,CAAS,CAAC;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAME,WAAWA,CAACC,MAAc,EAAwB;IACtD,IAAI;MACF,MAAMC,OAAO,GAAG5B,GAAG,CAACc,EAAE,EAAE,OAAO,EAAEa,MAAM,CAAC;MACxC,MAAME,YAAY,GAAG,MAAM3B,MAAM,CAAC0B,OAAO,CAAC;MAE1C,IAAIC,YAAY,CAACC,MAAM,CAAC,CAAC,EAAE;QACzB,OAAO;UACLR,EAAE,EAAEO,YAAY,CAACP,EAAE;UACnB,GAAGO,YAAY,CAACN,IAAI,CAAC;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMO,UAAUA,CAACC,QAA0B,EAAmB;IAC5D,IAAI;MACF,MAAMd,eAAe,GAAGnB,UAAU,CAACe,EAAE,EAAE,OAAO,CAAC;MAC/C,MAAMmB,MAAM,GAAG,MAAM9B,MAAM,CAACe,eAAe,EAAE;QAC3C,GAAGc,QAAQ;QACXE,SAAS,EAAEzB,SAAS,CAAC0B,GAAG,CAAC,CAAC;QAC1BC,SAAS,EAAE3B,SAAS,CAAC0B,GAAG,CAAC;MAC3B,CAAC,CAAC;MACF,OAAOF,MAAM,CAACX,EAAE;IAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMa,UAAUA,CAACV,MAAc,EAAEK,QAAuB,EAAiB;IACvE,IAAI;MACF,MAAMJ,OAAO,GAAG5B,GAAG,CAACc,EAAE,EAAE,OAAO,EAAEa,MAAM,CAAC;MACxC,MAAMvB,SAAS,CAACwB,OAAO,EAAE;QACvB,GAAGI,QAAQ;QACXI,SAAS,EAAE3B,SAAS,CAAC0B,GAAG,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMc,UAAUA,CAACX,MAAc,EAAiB;IAC9C,IAAI;MACF,MAAMC,OAAO,GAAG5B,GAAG,CAACc,EAAE,EAAE,OAAO,EAAEa,MAAM,CAAC;MACxC,MAAMtB,SAAS,CAACuB,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMe,UAAUA,CAAA,EAAsB;IACpC,IAAI;MACF,MAAMC,iBAAiB,GAAGzC,UAAU,CAACe,EAAE,EAAE,SAAS,CAAC;MACnD,MAAM2B,eAAe,GAAG,MAAMxC,OAAO,CAACuC,iBAAiB,CAAC;MACxD,OAAOC,eAAe,CAACrB,IAAI,CAACC,GAAG,CAACrB,GAAG,KAAK;QACtCsB,EAAE,EAAEtB,GAAG,CAACsB,EAAE;QACV,GAAGtB,GAAG,CAACuB,IAAI,CAAC;MACd,CAAC,CAAW,CAAC;IACf,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMkB,mBAAmBA,CAACC,SAAiB,EAAqB;IAC9D,IAAI;MACF,MAAMH,iBAAiB,GAAGzC,UAAU,CAACe,EAAE,EAAE,SAAS,CAAC;MACnD,MAAM8B,CAAC,GAAGtC,KAAK,CAACkC,iBAAiB,EAAEjC,KAAK,CAAC,WAAW,EAAE,IAAI,EAAEoC,SAAS,CAAC,CAAC;MACvE,MAAMF,eAAe,GAAG,MAAMxC,OAAO,CAAC2C,CAAC,CAAC;MACxC,OAAOH,eAAe,CAACrB,IAAI,CAACC,GAAG,CAACrB,GAAG,KAAK;QACtCsB,EAAE,EAAEtB,GAAG,CAACsB,EAAE;QACV,GAAGtB,GAAG,CAACuB,IAAI,CAAC;MACd,CAAC,CAAW,CAAC;IACf,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;MAClF,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMqB,YAAYA,CAACC,UAA8B,EAAmB;IAClE,IAAI;MACF,MAAMN,iBAAiB,GAAGzC,UAAU,CAACe,EAAE,EAAE,SAAS,CAAC;MACnD,MAAMmB,MAAM,GAAG,MAAM9B,MAAM,CAACqC,iBAAiB,EAAE;QAC7C,GAAGM,UAAU;QACbZ,SAAS,EAAEzB,SAAS,CAAC0B,GAAG,CAAC,CAAC;QAC1BC,SAAS,EAAE3B,SAAS,CAAC0B,GAAG,CAAC;MAC3B,CAAC,CAAC;MACF,OAAOF,MAAM,CAACX,EAAE;IAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMuB,gBAAgBA,CAACC,cAA4C,EAAmB;IACpF,IAAI;MACF,MAAMC,oBAAoB,GAAGlD,UAAU,CAACe,EAAE,EAAE,YAAY,CAAC;MACzD,MAAMmB,MAAM,GAAG,MAAM9B,MAAM,CAAC8C,oBAAoB,EAAE;QAChD,GAAGD,cAAc;QACjBd,SAAS,EAAEzB,SAAS,CAAC0B,GAAG,CAAC,CAAC;QAC1BC,SAAS,EAAE3B,SAAS,CAAC0B,GAAG,CAAC;MAC3B,CAAC,CAAC;MACF,OAAOF,MAAM,CAACX,EAAE;IAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM0B,qBAAqBA,CAACC,QAAgB,EAAEC,IAAa,EAA+B;IACxF,IAAI;MACF,MAAMH,oBAAoB,GAAGlD,UAAU,CAACe,EAAE,EAAE,YAAY,CAAC;MACzD,IAAI8B,CAAC,GAAGtC,KAAK,CAAC2C,oBAAoB,EAAE1C,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE4C,QAAQ,CAAC,CAAC;MAEtE,IAAIC,IAAI,EAAE;QACRR,CAAC,GAAGtC,KAAK,CAACsC,CAAC,EAAErC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE6C,IAAI,CAAC,CAAC;MACzC;MAEAR,CAAC,GAAGtC,KAAK,CAACsC,CAAC,EAAEpC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;MAE1C,MAAM6C,kBAAkB,GAAG,MAAMpD,OAAO,CAAC2C,CAAC,CAAC;MAC3C,OAAOS,kBAAkB,CAACjC,IAAI,CAACC,GAAG,CAACrB,GAAG,KAAK;QACzCsB,EAAE,EAAEtB,GAAG,CAACsB,EAAE;QACV,GAAGtB,GAAG,CAACuB,IAAI,CAAC;MACd,CAAC,CAAqB,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8B,sBAAsBA,CAACC,SAAiB,EAA+B;IAC3E,IAAI;MACF,MAAMN,oBAAoB,GAAGlD,UAAU,CAACe,EAAE,EAAE,YAAY,CAAC;MACzD,MAAM8B,CAAC,GAAGtC,KAAK,CACb2C,oBAAoB,EACpB1C,KAAK,CAAC,WAAW,EAAE,IAAI,EAAEgD,SAAS,CAAC,EACnC/C,OAAO,CAAC,MAAM,EAAE,MAAM,CACxB,CAAC;MAED,MAAM6C,kBAAkB,GAAG,MAAMpD,OAAO,CAAC2C,CAAC,CAAC;MAC3C,OAAOS,kBAAkB,CAACjC,IAAI,CAACC,GAAG,CAACrB,GAAG,KAAK;QACzCsB,EAAE,EAAEtB,GAAG,CAACsB,EAAE;QACV,GAAGtB,GAAG,CAACuB,IAAI,CAAC;MACd,CAAC,CAAqB,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACpF,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMgC,kBAAkBA,CAAC7B,MAAc,EAAE8B,IAAU,EAAmB;IACpE,IAAI;MACF,MAAMC,QAAQ,GAAGhD,GAAG,CAACK,OAAO,EAAE,kBAAkBY,MAAM,IAAI8B,IAAI,CAACE,IAAI,EAAE,CAAC;MACtE,MAAMC,QAAQ,GAAG,MAAMjD,WAAW,CAAC+C,QAAQ,EAAED,IAAI,CAAC;MAClD,MAAMI,WAAW,GAAG,MAAMjD,cAAc,CAACgD,QAAQ,CAAClD,GAAG,CAAC;MACtD,OAAOmD,WAAW;IACpB,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsC,eAAeA,CAACnC,MAAc,EAAE8B,IAAU,EAAmB;IACjE,IAAI;MACF,MAAMC,QAAQ,GAAGhD,GAAG,CAACK,OAAO,EAAE,eAAeY,MAAM,IAAIoC,IAAI,CAAC5B,GAAG,CAAC,CAAC,IAAIsB,IAAI,CAACE,IAAI,EAAE,CAAC;MACjF,MAAMC,QAAQ,GAAG,MAAMjD,WAAW,CAAC+C,QAAQ,EAAED,IAAI,CAAC;MAClD,MAAMI,WAAW,GAAG,MAAMjD,cAAc,CAACgD,QAAQ,CAAClD,GAAG,CAAC;MACtD,OAAOmD,WAAW;IACpB,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMwC,WAAWA,CAACC,QAAgB,EAAiB;IACjD,IAAI;MACF,MAAMP,QAAQ,GAAGhD,GAAG,CAACK,OAAO,EAAEkD,QAAQ,CAAC;MACvC,MAAMpD,YAAY,CAAC6C,QAAQ,CAAC;IAC9B,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAM0C,cAAcA,CAAA,EAAiB;IACnC,IAAI;MACF;MACA,MAAMhD,eAAe,GAAGnB,UAAU,CAACe,EAAE,EAAE,OAAO,CAAC;MAC/C,MAAMK,aAAa,GAAG,MAAMlB,OAAO,CAACiB,eAAe,CAAC;MACpD,MAAMiD,KAAK,GAAGhD,aAAa,CAACC,IAAI,CAACC,GAAG,CAACrB,GAAG,IAAIA,GAAG,CAACuB,IAAI,CAAC,CAAC,CAAC;MAEvD,MAAM6C,KAAK,GAAG;QACZC,UAAU,EAAEF,KAAK,CAACG,MAAM;QACxBC,aAAa,EAAEJ,KAAK,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,CAAC,CAACJ,MAAM;QACnEK,aAAa,EAAER,KAAK,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,CAAC,CAACJ,MAAM;QACnEM,WAAW,EAAET,KAAK,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,OAAO,CAAC,CAACJ;MAC3D,CAAC;;MAED;MACA,MAAM9B,iBAAiB,GAAGzC,UAAU,CAACe,EAAE,EAAE,SAAS,CAAC;MACnD,MAAM2B,eAAe,GAAG,MAAMxC,OAAO,CAACuC,iBAAiB,CAAC;MACxD4B,KAAK,CAACS,YAAY,GAAGpC,eAAe,CAACqC,IAAI;MAEzC,OAAOV,KAAK;IACd,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,OAAO,MAAMuD,eAAe,GAAG,IAAI/D,eAAe,CAAC,CAAC;AACpD,eAAe+D,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}