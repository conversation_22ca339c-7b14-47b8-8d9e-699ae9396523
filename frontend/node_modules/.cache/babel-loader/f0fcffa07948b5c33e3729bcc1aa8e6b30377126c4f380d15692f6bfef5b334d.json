{"ast": null, "code": "import { Box } from '../classes';\nimport { FaceDetection } from '../classes/FaceDetection';\nimport { isWithFaceDetection } from '../factories/WithFaceDetection';\nimport { round } from '../utils';\nimport { DrawBox } from './DrawBox';\nexport function drawDetections(canvasArg, detections) {\n  var detectionsArray = Array.isArray(detections) ? detections : [detections];\n  detectionsArray.forEach(function (det) {\n    var score = det instanceof FaceDetection ? det.score : isWithFaceDetection(det) ? det.detection.score : undefined;\n    var box = det instanceof FaceDetection ? det.box : isWithFaceDetection(det) ? det.detection.box : new Box(det);\n    var label = score ? \"\" + round(score) : undefined;\n    new DrawBox(box, {\n      label: label\n    }).draw(canvasArg);\n  });\n}", "map": {"version": 3, "names": ["Box", "FaceDetection", "isWithFaceDetection", "round", "DrawBox", "drawDetections", "canvasArg", "detections", "detectionsArray", "Array", "isArray", "for<PERSON>ach", "det", "score", "detection", "undefined", "box", "label", "draw"], "sources": ["../../../src/draw/drawDetections.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAA6B,YAAY;AACrD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,mBAAmB,QAA2B,gCAAgC;AACvF,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,OAAO,QAAQ,WAAW;AAInC,OAAM,SAAUC,cAAcA,CAC5BC,SAAqC,EACrCC,UAA8D;EAE9D,IAAMC,eAAe,GAAGC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;EAE7EC,eAAe,CAACG,OAAO,CAAC,UAAAC,GAAG;IACzB,IAAMC,KAAK,GAAGD,GAAG,YAAYX,aAAa,GACtCW,GAAG,CAACC,KAAK,GACRX,mBAAmB,CAACU,GAAG,CAAC,GAAGA,GAAG,CAACE,SAAS,CAACD,KAAK,GAAGE,SAAU;IAEhE,IAAMC,GAAG,GAAGJ,GAAG,YAAYX,aAAa,GACpCW,GAAG,CAACI,GAAG,GACNd,mBAAmB,CAACU,GAAG,CAAC,GAAGA,GAAG,CAACE,SAAS,CAACE,GAAG,GAAG,IAAIhB,GAAG,CAACY,GAAG,CAAE;IAEjE,IAAMK,KAAK,GAAGJ,KAAK,GAAG,KAAGV,KAAK,CAACU,KAAK,CAAG,GAAGE,SAAS;IACnD,IAAIX,OAAO,CAACY,GAAG,EAAE;MAAEC,KAAK,EAAAA;IAAA,CAAE,CAAC,CAACC,IAAI,CAACZ,SAAS,CAAC;EAC7C,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}