{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Box } from './Box';\nvar Rect = /** @class */function (_super) {\n  __extends(Rect, _super);\n  function Rect(x, y, width, height, allowNegativeDimensions) {\n    if (allowNegativeDimensions === void 0) {\n      allowNegativeDimensions = false;\n    }\n    return _super.call(this, {\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }, allowNegativeDimensions) || this;\n  }\n  return Rect;\n}(Box);\nexport { Rect };", "map": {"version": 3, "names": ["Box", "Rect", "_super", "__extends", "x", "y", "width", "height", "allowNegativeDimensions", "call"], "sources": ["../../../src/classes/Rect.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,GAAG,QAAQ,OAAO;AAS3B,IAAAC,IAAA,0BAAAC,MAAA;EAA0BC,SAAA,CAAAF,IAAA,EAAAC,MAAA;EACxB,SAAAD,KAAYG,CAAS,EAAEC,CAAS,EAAEC,KAAa,EAAEC,MAAc,EAAEC,uBAAwC;IAAxC,IAAAA,uBAAA;MAAAA,uBAAA,QAAwC;IAAA;WACvGN,MAAA,CAAAO,IAAA,OAAM;MAAEL,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,EAAEC,uBAAuB,CAAC;EACzD;EACF,OAAAP,IAAC;AAAD,CAAC,CAJyBD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}