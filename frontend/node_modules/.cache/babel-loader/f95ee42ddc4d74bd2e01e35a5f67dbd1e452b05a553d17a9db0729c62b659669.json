{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { toNetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { normalize } from '../ops';\nimport { denseBlock3 } from './denseBlock';\nimport { extractParamsFromWeigthMapTiny } from './extractParamsFromWeigthMapTiny';\nimport { extractParamsTiny } from './extractParamsTiny';\nvar TinyFaceFeatureExtractor = /** @class */function (_super) {\n  __extends(TinyFaceFeatureExtractor, _super);\n  function TinyFaceFeatureExtractor() {\n    return _super.call(this, 'TinyFaceFeatureExtractor') || this;\n  }\n  TinyFaceFeatureExtractor.prototype.forwardInput = function (input) {\n    var params = this.params;\n    if (!params) {\n      throw new Error('TinyFaceFeatureExtractor - load model before inference');\n    }\n    return tf.tidy(function () {\n      var batchTensor = input.toBatchTensor(112, true);\n      var meanRgb = [122.782, 117.001, 104.298];\n      var normalized = normalize(batchTensor, meanRgb).div(tf.scalar(255));\n      var out = denseBlock3(normalized, params.dense0, true);\n      out = denseBlock3(out, params.dense1);\n      out = denseBlock3(out, params.dense2);\n      out = tf.avgPool(out, [14, 14], [2, 2], 'valid');\n      return out;\n    });\n  };\n  TinyFaceFeatureExtractor.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  TinyFaceFeatureExtractor.prototype.getDefaultModelName = function () {\n    return 'face_feature_extractor_tiny_model';\n  };\n  TinyFaceFeatureExtractor.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMapTiny(weightMap);\n  };\n  TinyFaceFeatureExtractor.prototype.extractParams = function (weights) {\n    return extractParamsTiny(weights);\n  };\n  return TinyFaceFeatureExtractor;\n}(NeuralNetwork);\nexport { TinyFaceFeatureExtractor };", "map": {"version": 3, "names": ["tf", "toNetInput", "NeuralNetwork", "normalize", "denseBlock3", "extractParamsFromWeigthMapTiny", "extractParamsTiny", "TinyFaceFeatureExtractor", "_super", "__extends", "call", "prototype", "forwardInput", "input", "params", "Error", "tidy", "batchTensor", "toBatchTensor", "meanRgb", "normalized", "div", "scalar", "out", "dense0", "dense1", "dense2", "avgPool", "forward", "_a", "apply", "_b", "sent", "getDefaultModelName", "extractParamsFromWeigthMap", "weightMap", "extractParams", "weights"], "sources": ["../../../src/faceFeatureExtractor/TinyFaceFeatureExtractor.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,8BAA8B,QAAQ,kCAAkC;AACjF,SAASC,iBAAiB,QAAQ,qBAAqB;AAGvD,IAAAC,wBAAA,0BAAAC,MAAA;EAA8CC,SAAA,CAAAF,wBAAA,EAAAC,MAAA;EAE5C,SAAAD,yBAAA;WACEC,MAAA,CAAAE,IAAA,OAAM,0BAA0B,CAAC;EACnC;EAEOH,wBAAA,CAAAI,SAAA,CAAAC,YAAY,GAAnB,UAAoBC,KAAe;IAEzB,IAAAC,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;;IAG3E,OAAOf,EAAE,CAACgB,IAAI,CAAC;MACb,IAAMC,WAAW,GAAGJ,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC;MAClD,IAAMC,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC3C,IAAMC,UAAU,GAAGjB,SAAS,CAACc,WAAW,EAAEE,OAAO,CAAC,CAACE,GAAG,CAACrB,EAAE,CAACsB,MAAM,CAAC,GAAG,CAAC,CAAgB;MAErF,IAAIC,GAAG,GAAGnB,WAAW,CAACgB,UAAU,EAAEN,MAAM,CAACU,MAAM,EAAE,IAAI,CAAC;MACtDD,GAAG,GAAGnB,WAAW,CAACmB,GAAG,EAAET,MAAM,CAACW,MAAM,CAAC;MACrCF,GAAG,GAAGnB,WAAW,CAACmB,GAAG,EAAET,MAAM,CAACY,MAAM,CAAC;MACrCH,GAAG,GAAGvB,EAAE,CAAC2B,OAAO,CAACJ,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;MAEhD,OAAOA,GAAG;IACZ,CAAC,CAAC;EACJ,CAAC;EAEYhB,wBAAA,CAAAI,SAAA,CAAAiB,OAAO,GAApB,UAAqBf,KAAgB;;;;;;YAC5BgB,EAAA,OAAI,CAACjB,YAAY;YAAC,qBAAMX,UAAU,CAACY,KAAK,CAAC;;YAAhD,sBAAOgB,EAAA,CAAAC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAESzB,wBAAA,CAAAI,SAAA,CAAAsB,mBAAmB,GAA7B;IACE,OAAO,mCAAmC;EAC5C,CAAC;EAES1B,wBAAA,CAAAI,SAAA,CAAAuB,0BAA0B,GAApC,UAAqCC,SAA4B;IAC/D,OAAO9B,8BAA8B,CAAC8B,SAAS,CAAC;EAClD,CAAC;EAES5B,wBAAA,CAAAI,SAAA,CAAAyB,aAAa,GAAvB,UAAwBC,OAAqB;IAC3C,OAAO/B,iBAAiB,CAAC+B,OAAO,CAAC;EACnC,CAAC;EACH,OAAA9B,wBAAC;AAAD,CAAC,CA3C6CL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}