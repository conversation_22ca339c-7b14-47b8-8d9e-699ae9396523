{"ast": null, "code": "/**\n * @fileOverview Composed Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Area } from '../cartesian/Area';\nimport { Bar } from '../cartesian/Bar';\nimport { Line } from '../cartesian/Line';\nimport { Scatter } from '../cartesian/Scatter';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { ZAxis } from '../cartesian/ZAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var ComposedChart = generateCategoricalChart({\n  chartName: 'ComposedChart',\n  GraphicalChild: [Line, Area, Bar, Scatter],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }, {\n    axisType: 'zAxis',\n    AxisComp: ZAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "map": {"version": 3, "names": ["generateCategoricalChart", "Area", "Bar", "Line", "<PERSON><PERSON><PERSON>", "XAxis", "YA<PERSON>s", "ZAxis", "formatAxisMap", "ComposedChart", "chartName", "GraphicalChild", "axisComponents", "axisType", "AxisComp"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/recharts/es6/chart/ComposedChart.js"], "sourcesContent": ["/**\n * @fileOverview Composed Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Area } from '../cartesian/Area';\nimport { Bar } from '../cartesian/Bar';\nimport { Line } from '../cartesian/Line';\nimport { Scatter } from '../cartesian/Scatter';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { ZAxis } from '../cartesian/ZAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var ComposedChart = generateCategoricalChart({\n  chartName: 'ComposedChart',\n  GraphicalChild: [Line, Area, Bar, Scatter],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }, {\n    axisType: 'zAxis',\n    AxisComp: ZAxis\n  }],\n  formatAxisMap: formatAxisMap\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAO,IAAIC,aAAa,GAAGT,wBAAwB,CAAC;EAClDU,SAAS,EAAE,eAAe;EAC1BC,cAAc,EAAE,CAACR,IAAI,EAAEF,IAAI,EAAEC,GAAG,EAAEE,OAAO,CAAC;EAC1CQ,cAAc,EAAE,CAAC;IACfC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAET;EACZ,CAAC,EAAE;IACDQ,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAER;EACZ,CAAC,EAAE;IACDO,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAEP;EACZ,CAAC,CAAC;EACFC,aAAa,EAAEA;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}