{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { Point } from '../classes';\nimport { nonMaxSuppression } from '../ops';\nimport { extractImagePatches } from './extractImagePatches';\nimport { MtcnnBox } from './MtcnnBox';\nimport { ONet } from './ONet';\nexport function stage3(img, inputBoxes, scoreThreshold, params, stats) {\n  return __awaiter(this, void 0, void 0, function () {\n    var ts, onetInputs, onetOuts, scoresTensor, scores, _a, _b, indices, filteredRegions, filteredBoxes, filteredScores, finalBoxes, finalScores, points, indicesNms;\n    return __generator(this, function (_c) {\n      switch (_c.label) {\n        case 0:\n          ts = Date.now();\n          return [4 /*yield*/, extractImagePatches(img, inputBoxes, {\n            width: 48,\n            height: 48\n          })];\n        case 1:\n          onetInputs = _c.sent();\n          stats.stage3_extractImagePatches = Date.now() - ts;\n          ts = Date.now();\n          onetOuts = onetInputs.map(function (onetInput) {\n            var out = ONet(onetInput, params);\n            onetInput.dispose();\n            return out;\n          });\n          stats.stage3_onet = Date.now() - ts;\n          scoresTensor = onetOuts.length > 1 ? tf.concat(onetOuts.map(function (out) {\n            return out.scores;\n          })) : onetOuts[0].scores;\n          _b = (_a = Array).from;\n          return [4 /*yield*/, scoresTensor.data()];\n        case 2:\n          scores = _b.apply(_a, [_c.sent()]);\n          scoresTensor.dispose();\n          indices = scores.map(function (score, idx) {\n            return {\n              score: score,\n              idx: idx\n            };\n          }).filter(function (c) {\n            return c.score > scoreThreshold;\n          }).map(function (_a) {\n            var idx = _a.idx;\n            return idx;\n          });\n          filteredRegions = indices.map(function (idx) {\n            var regionsData = onetOuts[idx].regions.arraySync();\n            return new MtcnnBox(regionsData[0][0], regionsData[0][1], regionsData[0][2], regionsData[0][3]);\n          });\n          filteredBoxes = indices.map(function (idx, i) {\n            return inputBoxes[idx].calibrate(filteredRegions[i]);\n          });\n          filteredScores = indices.map(function (idx) {\n            return scores[idx];\n          });\n          finalBoxes = [];\n          finalScores = [];\n          points = [];\n          if (filteredBoxes.length > 0) {\n            ts = Date.now();\n            indicesNms = nonMaxSuppression(filteredBoxes, filteredScores, 0.7, false);\n            stats.stage3_nms = Date.now() - ts;\n            finalBoxes = indicesNms.map(function (idx) {\n              return filteredBoxes[idx];\n            });\n            finalScores = indicesNms.map(function (idx) {\n              return filteredScores[idx];\n            });\n            points = indicesNms.map(function (idx, i) {\n              return Array(5).fill(0).map(function (_, ptIdx) {\n                var pointsData = onetOuts[idx].points.arraySync();\n                return new Point(pointsData[0][ptIdx] * (finalBoxes[i].width + 1) + finalBoxes[i].left, pointsData[0][ptIdx + 5] * (finalBoxes[i].height + 1) + finalBoxes[i].top);\n              });\n            });\n          }\n          onetOuts.forEach(function (t) {\n            t.regions.dispose();\n            t.scores.dispose();\n            t.points.dispose();\n          });\n          return [2 /*return*/, {\n            boxes: finalBoxes,\n            scores: finalScores,\n            points: points\n          }];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "Point", "nonMaxSuppression", "extractImagePatches", "MtcnnBox", "ONet", "stage3", "img", "inputBoxes", "scoreThreshold", "params", "stats", "ts", "Date", "now", "width", "height", "onetInputs", "_c", "sent", "stage3_extractImagePatches", "onetOuts", "map", "onetInput", "out", "dispose", "stage3_onet", "scoresTensor", "length", "concat", "scores", "_b", "_a", "Array", "from", "data", "apply", "indices", "score", "idx", "filter", "c", "filteredRegions", "regionsData", "regions", "arraySync", "filteredBoxes", "i", "calibrate", "filteredScores", "finalBoxes", "finalScores", "points", "indicesNms", "stage3_nms", "fill", "_", "ptIdx", "pointsData", "left", "top", "for<PERSON>ach", "t", "boxes"], "sources": ["../../../src/mtcnn/stage3.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAA2BC,KAAK,QAAQ,YAAY;AACpD,SAASC,iBAAiB,QAAQ,QAAQ;AAC1C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAG7B,OAAM,SAAgBC,MAAMA,CAC1BC,GAAsB,EACtBC,UAAyB,EACzBC,cAAsB,EACtBC,MAAkB,EAClBC,KAAU;;;;;;UAGNC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;UACA,qBAAMX,mBAAmB,CAACI,GAAG,EAAEC,UAAU,EAAE;YAAEO,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAE,CAAE,CAAC;;UAAlFC,UAAU,GAAGC,EAAA,CAAAC,IAAA,EAAqE;UACxFR,KAAK,CAACS,0BAA0B,GAAGP,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;UAElDA,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;UACTO,QAAQ,GAAGJ,UAAU,CAACK,GAAG,CAC7B,UAAAC,SAAS;YACP,IAAMC,GAAG,GAAGnB,IAAI,CAACkB,SAAS,EAAEb,MAAM,CAAC;YACnCa,SAAS,CAACE,OAAO,EAAE;YACnB,OAAOD,GAAG;UACZ,CAAC,CACF;UACDb,KAAK,CAACe,WAAW,GAAGb,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;UAE7Be,YAAY,GAAGN,QAAQ,CAACO,MAAM,GAAG,CAAC,GACpC5B,EAAE,CAAC6B,MAAM,CAACR,QAAQ,CAACC,GAAG,CAAC,UAAAE,GAAG;YAAI,OAAAA,GAAG,CAACM,MAAM;UAAV,CAAU,CAAC,CAAC,GAC1CT,QAAQ,CAAC,CAAC,CAAC,CAACS,MAAM;UACPC,EAAA,IAAAC,EAAA,GAAAC,KAAK,EAACC,IAAI;UAAC,qBAAMP,YAAY,CAACQ,IAAI,EAAE;;UAA7CL,MAAM,GAAGC,EAAA,CAAAK,KAAA,CAAAJ,EAAA,GAAWd,EAAA,CAAAC,IAAA,EAAyB,EAAC;UACpDQ,YAAY,CAACF,OAAO,EAAE;UAEhBY,OAAO,GAAGP,MAAM,CACnBR,GAAG,CAAC,UAACgB,KAAK,EAAEC,GAAG;YAAK,OAAC;cAAED,KAAK,EAAAA,KAAA;cAAEC,GAAG,EAAAA;YAAA,CAAE;UAAf,CAAgB,CAAC,CACrCC,MAAM,CAAC,UAAAC,CAAC;YAAI,OAAAA,CAAC,CAACH,KAAK,GAAG7B,cAAc;UAAxB,CAAwB,CAAC,CACrCa,GAAG,CAAC,UAACU,EAAO;gBAALO,GAAA,GAAAP,EAAA,CAAAO,GAAG;YAAO,OAAAA,GAAG;UAAH,CAAG,CAAC;UAElBG,eAAe,GAAGL,OAAO,CAACf,GAAG,CAAC,UAAAiB,GAAG;YACrC,IAAMI,WAAW,GAAGtB,QAAQ,CAACkB,GAAG,CAAC,CAACK,OAAO,CAACC,SAAS,EAAE;YACrD,OAAO,IAAIzC,QAAQ,CACjBuC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjBA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjBA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjBA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpB;UAAA,CAAC,CAAC;UACGG,aAAa,GAAGT,OAAO,CAC1Bf,GAAG,CAAC,UAACiB,GAAG,EAAEQ,CAAC;YAAK,OAAAvC,UAAU,CAAC+B,GAAG,CAAC,CAACS,SAAS,CAACN,eAAe,CAACK,CAAC,CAAC,CAAC;UAA7C,CAA6C,CAAC;UAC3DE,cAAc,GAAGZ,OAAO,CAACf,GAAG,CAAC,UAAAiB,GAAG;YAAI,OAAAT,MAAM,CAACS,GAAG,CAAC;UAAX,CAAW,CAAC;UAElDW,UAAU,GAAU,EAAE;UACtBC,WAAW,GAAa,EAAE;UAC1BC,MAAM,GAAc,EAAE;UAE1B,IAAIN,aAAa,CAAClB,MAAM,GAAG,CAAC,EAAE;YAE5BhB,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;YACTuC,UAAU,GAAGnD,iBAAiB,CAClC4C,aAAa,EACbG,cAAc,EACd,GAAG,EACH,KAAK,CACN;YACDtC,KAAK,CAAC2C,UAAU,GAAGzC,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;YAElCsC,UAAU,GAAGG,UAAU,CAAC/B,GAAG,CAAC,UAAAiB,GAAG;cAAI,OAAAO,aAAa,CAACP,GAAG,CAAC;YAAlB,CAAkB,CAAC;YACtDY,WAAW,GAAGE,UAAU,CAAC/B,GAAG,CAAC,UAAAiB,GAAG;cAAI,OAAAU,cAAc,CAACV,GAAG,CAAC;YAAnB,CAAmB,CAAC;YACxDa,MAAM,GAAGC,UAAU,CAAC/B,GAAG,CAAC,UAACiB,GAAG,EAAEQ,CAAC;cAC7B,OAAAd,KAAK,CAAC,CAAC,CAAC,CAACsB,IAAI,CAAC,CAAC,CAAC,CAACjC,GAAG,CAAC,UAACkC,CAAC,EAAEC,KAAK;gBAC1B,IAAMC,UAAU,GAAGrC,QAAQ,CAACkB,GAAG,CAAC,CAACa,MAAM,CAACP,SAAS,EAAE;gBACnD,OAAO,IAAI5C,KAAK,CACZyD,UAAU,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,IAAIP,UAAU,CAACH,CAAC,CAAC,CAAChC,KAAK,GAAG,CAAC,CAAC,GAAImC,UAAU,CAACH,CAAC,CAAC,CAACY,IAAI,EACtED,UAAU,CAAC,CAAC,CAAC,CAACD,KAAK,GAAC,CAAC,CAAC,IAAIP,UAAU,CAACH,CAAC,CAAC,CAAC/B,MAAM,GAAG,CAAC,CAAC,GAAIkC,UAAU,CAACH,CAAC,CAAC,CAACa,GAAI,CAC5E;cACH,CAAC,CACF;YAPD,CAOC,CACF;;UAGHvC,QAAQ,CAACwC,OAAO,CAAC,UAAAC,CAAC;YAChBA,CAAC,CAAClB,OAAO,CAACnB,OAAO,EAAE;YACnBqC,CAAC,CAAChC,MAAM,CAACL,OAAO,EAAE;YAClBqC,CAAC,CAACV,MAAM,CAAC3B,OAAO,EAAE;UACpB,CAAC,CAAC;UAEF,sBAAO;YACLsC,KAAK,EAAEb,UAAU;YACjBpB,MAAM,EAAEqB,WAAW;YACnBC,MAAM,EAAAA;WACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}