{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { ObjectDetection } from './ObjectDetection';\nvar FaceDetection = /** @class */function (_super) {\n  __extends(FaceDetection, _super);\n  function FaceDetection(score, relativeBox, imageDims) {\n    return _super.call(this, score, score, '', relativeBox, imageDims) || this;\n  }\n  FaceDetection.prototype.forSize = function (width, height) {\n    var _a = _super.prototype.forSize.call(this, width, height),\n      score = _a.score,\n      relativeBox = _a.relativeBox,\n      imageDims = _a.imageDims;\n    return new FaceDetection(score, relativeBox, imageDims);\n  };\n  return FaceDetection;\n}(ObjectDetection);\nexport { FaceDetection };", "map": {"version": 3, "names": ["ObjectDetection", "FaceDetection", "_super", "__extends", "score", "relativeBox", "imageDims", "call", "prototype", "forSize", "width", "height", "_a"], "sources": ["../../../src/classes/FaceDetection.ts"], "sourcesContent": [null], "mappings": ";AAEA,SAASA,eAAe,QAAQ,mBAAmB;AAQnD,IAAAC,aAAA,0BAAAC,MAAA;EAAmCC,SAAA,CAAAF,aAAA,EAAAC,MAAA;EACjC,SAAAD,cACEG,KAAa,EACbC,WAAiB,EACjBC,SAAsB;WAEtBJ,MAAA,CAAAK,IAAA,OAAMH,KAAK,EAAEA,KAAK,EAAE,EAAE,EAAEC,WAAW,EAAEC,SAAS,CAAC;EACjD;EAEOL,aAAA,CAAAO,SAAA,CAAAC,OAAO,GAAd,UAAeC,KAAa,EAAEC,MAAc;IACpC,IAAAC,EAAA,GAAAV,MAAA,CAAAM,SAAA,CAAAC,OAAA,CAAAF,IAAA,OAAAG,KAAA,EAAAC,MAAA,CAAgE;MAA9DP,KAAA,GAAAQ,EAAA,CAAAR,KAAK;MAAEC,WAAA,GAAAO,EAAA,CAAAP,WAAW;MAAEC,SAAA,GAAAM,EAAA,CAAAN,SAA0C;IACtE,OAAO,IAAIL,aAAa,CAACG,KAAK,EAAEC,WAAW,EAAEC,SAAS,CAAC;EACzD,CAAC;EACH,OAAAL,aAAC;AAAD,CAAC,CAbkCD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}