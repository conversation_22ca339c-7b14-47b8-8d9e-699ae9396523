{"ast": null, "code": "import { extractWeightsFactory } from '../common';\nimport { extractorsFactory } from './extractorsFactory';\nexport function extractParams(weights) {\n  var paramMappings = [];\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var extractDenseBlock4Params = extractorsFactory(extractWeights, paramMappings).extractDenseBlock4Params;\n  var dense0 = extractDenseBlock4Params(3, 32, 'dense0', true);\n  var dense1 = extractDenseBlock4Params(32, 64, 'dense1');\n  var dense2 = extractDenseBlock4Params(64, 128, 'dense2');\n  var dense3 = extractDenseBlock4Params(128, 256, 'dense3');\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    paramMappings: paramMappings,\n    params: {\n      dense0: dense0,\n      dense1: dense1,\n      dense2: dense2,\n      dense3: dense3\n    }\n  };\n}", "map": {"version": 3, "names": ["extractWeightsFactory", "extractorsFactory", "extractParams", "weights", "paramMappings", "_a", "extractWeights", "getRemainingWeights", "extractDenseBlock4Params", "dense0", "dense1", "dense2", "dense3", "length", "Error", "params"], "sources": ["../../../src/faceFeatureExtractor/extractParams.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,qBAAqB,QAAsB,WAAW;AAC/D,SAASC,iBAAiB,QAAQ,qBAAqB;AAIvD,OAAM,SAAUC,aAAaA,CAACC,OAAqB;EAEjD,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAAC,EAAA,GAAAL,qBAAA,CAAAG,OAAA,CAG4B;IAFhCG,cAAA,GAAAD,EAAA,CAAAC,cAAc;IACdC,mBAAA,GAAAF,EAAA,CAAAE,mBACgC;EAGhC,IAAAC,wBAAA,GAAAP,iBAAA,CAAAK,cAAA,EAAAF,aAAA,EAAAI,wBAAwB;EAG1B,IAAMC,MAAM,GAAGD,wBAAwB,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC9D,IAAME,MAAM,GAAGF,wBAAwB,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC;EACzD,IAAMG,MAAM,GAAGH,wBAAwB,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC;EAC1D,IAAMI,MAAM,GAAGJ,wBAAwB,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;EAE3D,IAAID,mBAAmB,EAAE,CAACM,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCP,mBAAmB,EAAE,CAACM,MAAQ,CAAC;;EAGnF,OAAO;IACLT,aAAa,EAAAA,aAAA;IACbW,MAAM,EAAE;MAAEN,MAAM,EAAAA,MAAA;MAAEC,MAAM,EAAAA,MAAA;MAAEC,MAAM,EAAAA,MAAA;MAAEC,MAAM,EAAAA;IAAA;GACzC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}