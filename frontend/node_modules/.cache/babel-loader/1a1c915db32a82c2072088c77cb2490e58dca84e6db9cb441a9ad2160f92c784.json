{"ast": null, "code": "import { env } from '../env';\nexport function isMediaElement(input) {\n  var _a = env.getEnv(),\n    Image = _a.Image,\n    Canvas = _a.Canvas,\n    Video = _a.Video;\n  return input instanceof Image || input instanceof Canvas || input instanceof Video;\n}", "map": {"version": 3, "names": ["env", "isMediaElement", "input", "_a", "getEnv", "Image", "<PERSON><PERSON>", "Video"], "sources": ["../../../src/dom/isMediaElement.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAE5B,OAAM,SAAUC,cAAcA,CAACC,KAAU;EAEjC,IAAAC,EAAA,GAAAH,GAAA,CAAAI,MAAA,EAAuC;IAArCC,KAAA,GAAAF,EAAA,CAAAE,KAAK;IAAEC,MAAA,GAAAH,EAAA,CAAAG,MAAM;IAAEC,KAAA,GAAAJ,EAAA,CAAAI,KAAsB;EAE7C,OAAOL,KAAK,YAAYG,KAAK,IACxBH,KAAK,YAAYI,MAAM,IACvBJ,KAAK,YAAYK,KAAK;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}