{"ast": null, "code": "import { extractConvParamsFactory, extractSeparableConvParamsFactory } from '../common';\nexport function extractorsFactory(extractWeights, paramMappings) {\n  var extractConvParams = extractConvParamsFactory(extractWeights, paramMappings);\n  var extractSeparableConvParams = extractSeparableConvParamsFactory(extractWeights, paramMappings);\n  function extractDenseBlock3Params(channelsIn, channelsOut, mappedPrefix, isFirstLayer) {\n    if (isFirstLayer === void 0) {\n      isFirstLayer = false;\n    }\n    var conv0 = isFirstLayer ? extractConvParams(channelsIn, channelsOut, 3, mappedPrefix + \"/conv0\") : extractSeparableConvParams(channelsIn, channelsOut, mappedPrefix + \"/conv0\");\n    var conv1 = extractSeparableConvParams(channelsOut, channelsOut, mappedPrefix + \"/conv1\");\n    var conv2 = extractSeparableConvParams(channelsOut, channelsOut, mappedPrefix + \"/conv2\");\n    return {\n      conv0: conv0,\n      conv1: conv1,\n      conv2: conv2\n    };\n  }\n  function extractDenseBlock4Params(channelsIn, channelsOut, mappedPrefix, isFirstLayer) {\n    if (isFirstLayer === void 0) {\n      isFirstLayer = false;\n    }\n    var _a = extractDenseBlock3Params(channelsIn, channelsOut, mappedPrefix, isFirstLayer),\n      conv0 = _a.conv0,\n      conv1 = _a.conv1,\n      conv2 = _a.conv2;\n    var conv3 = extractSeparableConvParams(channelsOut, channelsOut, mappedPrefix + \"/conv3\");\n    return {\n      conv0: conv0,\n      conv1: conv1,\n      conv2: conv2,\n      conv3: conv3\n    };\n  }\n  return {\n    extractDenseBlock3Params: extractDenseBlock3Params,\n    extractDenseBlock4Params: extractDenseBlock4Params\n  };\n}", "map": {"version": 3, "names": ["extractConvParamsFactory", "extractSeparableConvParamsFactory", "extractorsFactory", "extractWeights", "paramMappings", "extractConvParams", "extractSeparableConvParams", "extractDenseBlock3Params", "channelsIn", "channelsOut", "mappedPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conv0", "conv1", "conv2", "extractDenseBlock4Params", "_a", "conv3"], "sources": ["../../../src/faceFeatureExtractor/extractorsFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,SACEA,wBAAwB,EACxBC,iCAAiC,QAG5B,WAAW;AAGlB,OAAM,SAAUC,iBAAiBA,CAACC,cAAsC,EAAEC,aAA6B;EAErG,IAAMC,iBAAiB,GAAGL,wBAAwB,CAACG,cAAc,EAAEC,aAAa,CAAC;EACjF,IAAME,0BAA0B,GAAGL,iCAAiC,CAACE,cAAc,EAAEC,aAAa,CAAC;EAEnG,SAASG,wBAAwBA,CAACC,UAAkB,EAAEC,WAAmB,EAAEC,YAAoB,EAAEC,YAA6B;IAA7B,IAAAA,YAAA;MAAAA,YAAA,QAA6B;IAAA;IAE5H,IAAMC,KAAK,GAAGD,YAAY,GACtBN,iBAAiB,CAACG,UAAU,EAAEC,WAAW,EAAE,CAAC,EAAKC,YAAY,WAAQ,CAAC,GACtEJ,0BAA0B,CAACE,UAAU,EAAEC,WAAW,EAAKC,YAAY,WAAQ,CAAC;IAChF,IAAMG,KAAK,GAAGP,0BAA0B,CAACG,WAAW,EAAEA,WAAW,EAAKC,YAAY,WAAQ,CAAC;IAC3F,IAAMI,KAAK,GAAGR,0BAA0B,CAACG,WAAW,EAAEA,WAAW,EAAKC,YAAY,WAAQ,CAAC;IAE3F,OAAO;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;EAChC;EAEA,SAASC,wBAAwBA,CAACP,UAAkB,EAAEC,WAAmB,EAAEC,YAAoB,EAAEC,YAA6B;IAA7B,IAAAA,YAAA;MAAAA,YAAA,QAA6B;IAAA;IAEtH,IAAAK,EAAA,GAAAT,wBAAA,CAAAC,UAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,CAAuG;MAArGC,KAAA,GAAAI,EAAA,CAAAJ,KAAK;MAAEC,KAAA,GAAAG,EAAA,CAAAH,KAAK;MAAEC,KAAA,GAAAE,EAAA,CAAAF,KAAuF;IAC7G,IAAMG,KAAK,GAAGX,0BAA0B,CAACG,WAAW,EAAEA,WAAW,EAAKC,YAAY,WAAQ,CAAC;IAE3F,OAAO;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEG,KAAK,EAAAA;IAAA,CAAE;EACvC;EAEA,OAAO;IACLV,wBAAwB,EAAAA,wBAAA;IACxBQ,wBAAwB,EAAAA;GACzB;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}