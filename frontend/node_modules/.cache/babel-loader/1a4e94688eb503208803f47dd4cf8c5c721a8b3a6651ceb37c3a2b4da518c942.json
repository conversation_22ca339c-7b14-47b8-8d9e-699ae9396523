{"ast": null, "code": "import { __extends } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { fullyConnectedLayer } from '../common/fullyConnectedLayer';\nimport { NetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nimport { seperateWeightMaps } from './util';\nvar FaceProcessor = /** @class */function (_super) {\n  __extends(FaceProcessor, _super);\n  function FaceProcessor(_name, faceFeatureExtractor) {\n    var _this = _super.call(this, _name) || this;\n    _this._faceFeatureExtractor = faceFeatureExtractor;\n    return _this;\n  }\n  Object.defineProperty(FaceProcessor.prototype, \"faceFeatureExtractor\", {\n    get: function () {\n      return this._faceFeatureExtractor;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  FaceProcessor.prototype.runNet = function (input) {\n    var _this = this;\n    var params = this.params;\n    if (!params) {\n      throw new Error(this._name + \" - load model before inference\");\n    }\n    return tf.tidy(function () {\n      var bottleneckFeatures = input instanceof NetInput ? _this.faceFeatureExtractor.forwardInput(input) : input;\n      return fullyConnectedLayer(bottleneckFeatures.as2D(bottleneckFeatures.shape[0], -1), params.fc);\n    });\n  };\n  FaceProcessor.prototype.dispose = function (throwOnRedispose) {\n    if (throwOnRedispose === void 0) {\n      throwOnRedispose = true;\n    }\n    this.faceFeatureExtractor.dispose(throwOnRedispose);\n    _super.prototype.dispose.call(this, throwOnRedispose);\n  };\n  FaceProcessor.prototype.loadClassifierParams = function (weights) {\n    var _a = this.extractClassifierParams(weights),\n      params = _a.params,\n      paramMappings = _a.paramMappings;\n    this._params = params;\n    this._paramMappings = paramMappings;\n  };\n  FaceProcessor.prototype.extractClassifierParams = function (weights) {\n    return extractParams(weights, this.getClassifierChannelsIn(), this.getClassifierChannelsOut());\n  };\n  FaceProcessor.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    var _a = seperateWeightMaps(weightMap),\n      featureExtractorMap = _a.featureExtractorMap,\n      classifierMap = _a.classifierMap;\n    this.faceFeatureExtractor.loadFromWeightMap(featureExtractorMap);\n    return extractParamsFromWeigthMap(classifierMap);\n  };\n  FaceProcessor.prototype.extractParams = function (weights) {\n    var cIn = this.getClassifierChannelsIn();\n    var cOut = this.getClassifierChannelsOut();\n    var classifierWeightSize = cOut * cIn + cOut;\n    var featureExtractorWeights = weights.slice(0, weights.length - classifierWeightSize);\n    var classifierWeights = weights.slice(weights.length - classifierWeightSize);\n    this.faceFeatureExtractor.extractWeights(featureExtractorWeights);\n    return this.extractClassifierParams(classifierWeights);\n  };\n  return FaceProcessor;\n}(NeuralNetwork);\nexport { FaceProcessor };", "map": {"version": 3, "names": ["tf", "fullyConnectedLayer", "NetInput", "NeuralNetwork", "extractParams", "extractParamsFromWeigthMap", "seperateWeightMaps", "FaceProcessor", "_super", "__extends", "_name", "faceFeatureExtractor", "_this", "call", "_faceFeatureExtractor", "Object", "defineProperty", "prototype", "get", "runNet", "input", "params", "Error", "tidy", "bottleneckFeatures", "forwardInput", "as2D", "shape", "fc", "dispose", "throwOnRedispose", "loadClassifierParams", "weights", "_a", "extractClassifierParams", "paramMappings", "_params", "_paramMappings", "getClassifierChannelsIn", "getClassifierChannelsOut", "weightMap", "featureExtractorMap", "classifierMap", "loadFromWeightMap", "cIn", "cOut", "classifierWeightSize", "featureExtractorWeights", "slice", "length", "classifierWeights", "extractWeights"], "sources": ["../../../src/faceProcessor/FaceProcessor.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,QAAQ,QAAQ,QAAQ;AAMjC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AAEzE,SAASC,kBAAkB,QAAQ,QAAQ;AAE3C,IAAAC,aAAA,0BAAAC,MAAA;EAGUC,SAAA,CAAAF,aAAA,EAAAC,MAAA;EAIR,SAAAD,cAAYG,KAAa,EAAEC,oBAA6D;IAAxF,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,KAAK,CAAC;IACZE,KAAI,CAACE,qBAAqB,GAAGH,oBAAoB;;EACnD;EAEAI,MAAA,CAAAC,cAAA,CAAWT,aAAA,CAAAU,SAAA,wBAAoB;SAA/B,SAAAC,CAAA;MACE,OAAO,IAAI,CAACJ,qBAAqB;IACnC,CAAC;;;;EAMMP,aAAA,CAAAU,SAAA,CAAAE,MAAM,GAAb,UAAcC,KAA6B;IAA3C,IAAAR,KAAA;IAEU,IAAAS,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAI,IAAI,CAACZ,KAAK,mCAAgC,CAAC;;IAGhE,OAAOV,EAAE,CAACuB,IAAI,CAAC;MACb,IAAMC,kBAAkB,GAAGJ,KAAK,YAAYlB,QAAQ,GAChDU,KAAI,CAACD,oBAAoB,CAACc,YAAY,CAACL,KAAK,CAAC,GAC7CA,KAAK;MACT,OAAOnB,mBAAmB,CAACuB,kBAAkB,CAACE,IAAI,CAACF,kBAAkB,CAACG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEN,MAAM,CAACO,EAAE,CAAC;IACjG,CAAC,CAAC;EACJ,CAAC;EAEMrB,aAAA,CAAAU,SAAA,CAAAY,OAAO,GAAd,UAAeC,gBAAgC;IAAhC,IAAAA,gBAAA;MAAAA,gBAAA,OAAgC;IAAA;IAC7C,IAAI,CAACnB,oBAAoB,CAACkB,OAAO,CAACC,gBAAgB,CAAC;IACnDtB,MAAA,CAAAS,SAAA,CAAMY,OAAO,CAAAhB,IAAA,OAACiB,gBAAgB,CAAC;EACjC,CAAC;EAEMvB,aAAA,CAAAU,SAAA,CAAAc,oBAAoB,GAA3B,UAA4BC,OAAqB;IACzC,IAAAC,EAAA,QAAAC,uBAAA,CAAAF,OAAA,CAAiE;MAA/DX,MAAA,GAAAY,EAAA,CAAAZ,MAAM;MAAEc,aAAA,GAAAF,EAAA,CAAAE,aAAuD;IACvE,IAAI,CAACC,OAAO,GAAGf,MAAM;IACrB,IAAI,CAACgB,cAAc,GAAGF,aAAa;EACrC,CAAC;EAEM5B,aAAA,CAAAU,SAAA,CAAAiB,uBAAuB,GAA9B,UAA+BF,OAAqB;IAClD,OAAO5B,aAAa,CAAC4B,OAAO,EAAE,IAAI,CAACM,uBAAuB,EAAE,EAAE,IAAI,CAACC,wBAAwB,EAAE,CAAC;EAChG,CAAC;EAEShC,aAAA,CAAAU,SAAA,CAAAZ,0BAA0B,GAApC,UAAqCmC,SAA4B;IAEzD,IAAAP,EAAA,GAAA3B,kBAAA,CAAAkC,SAAA,CAAsE;MAApEC,mBAAA,GAAAR,EAAA,CAAAQ,mBAAmB;MAAEC,aAAA,GAAAT,EAAA,CAAAS,aAA+C;IAE5E,IAAI,CAAC/B,oBAAoB,CAACgC,iBAAiB,CAACF,mBAAmB,CAAC;IAEhE,OAAOpC,0BAA0B,CAACqC,aAAa,CAAC;EAClD,CAAC;EAESnC,aAAA,CAAAU,SAAA,CAAAb,aAAa,GAAvB,UAAwB4B,OAAqB;IAE3C,IAAMY,GAAG,GAAG,IAAI,CAACN,uBAAuB,EAAE;IAC1C,IAAMO,IAAI,GAAG,IAAI,CAACN,wBAAwB,EAAE;IAC5C,IAAMO,oBAAoB,GAAID,IAAI,GAAGD,GAAG,GAAKC,IAAI;IAEjD,IAAME,uBAAuB,GAAGf,OAAO,CAACgB,KAAK,CAAC,CAAC,EAAEhB,OAAO,CAACiB,MAAM,GAAGH,oBAAoB,CAAC;IACvF,IAAMI,iBAAiB,GAAGlB,OAAO,CAACgB,KAAK,CAAChB,OAAO,CAACiB,MAAM,GAAGH,oBAAoB,CAAC;IAE9E,IAAI,CAACnC,oBAAoB,CAACwC,cAAc,CAACJ,uBAAuB,CAAC;IACjE,OAAO,IAAI,CAACb,uBAAuB,CAACgB,iBAAiB,CAAC;EACxD,CAAC;EACH,OAAA3C,aAAC;AAAD,CAAC,CArESJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}