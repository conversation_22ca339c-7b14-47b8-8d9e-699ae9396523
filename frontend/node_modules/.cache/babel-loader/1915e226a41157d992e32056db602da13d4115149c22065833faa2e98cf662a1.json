{"ast": null, "code": "/**\n * Service Supabase pour PresencePro\n */\n\nimport { supabase } from '../config/supabase';\nimport { UserRole } from '../types';\nclass SupabaseService {\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('*').order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(user => this.mapUserFromDB(user))) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des utilisateurs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('*').eq('id', userId).single();\n      if (error) {\n        if (error.code === 'PGRST116') return null; // Not found\n        throw error;\n      }\n      return data ? this.mapUserFromDB(data) : null;\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData) {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      const {\n        data,\n        error\n      } = await supabase.from('users').insert([dbUser]).select().single();\n      if (error) throw error;\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur avec un ID spécifique (pour l'authentification)\n   */\n  async createUserWithId(userId, userData) {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      const {\n        error\n      } = await supabase.from('users').insert([{\n        ...dbUser,\n        id: userId\n      }]);\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur avec ID:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId, userData) {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      const {\n        data,\n        error\n      } = await supabase.from('users').update({\n        ...dbUser,\n        updated_at: new Date().toISOString()\n      }).eq('id', userId).select().single();\n      if (error) throw error;\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId) {\n    try {\n      const {\n        error\n      } = await supabase.from('users').delete().eq('id', userId);\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours avec leurs relations\n   */\n  async getCourses() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('courses').select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(course => this.mapCourseFromDB(course))) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('courses').select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `).eq('teacher_id', teacherId).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(course => this.mapCourseFromDB(course))) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours de l\\'enseignant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData) {\n    try {\n      const dbCourse = this.mapCourseToDB(courseData);\n      const {\n        data,\n        error\n      } = await supabase.from('courses').insert([dbCourse]).select().single();\n      if (error) throw error;\n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de la création du cours:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData) {\n    try {\n      const dbAttendance = this.mapAttendanceToDB(attendanceData);\n      const {\n        data,\n        error\n      } = await supabase.from('attendance').insert([dbAttendance]).select().single();\n      if (error) throw error;\n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de l\\'enregistrement de la présence:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère toutes les présences\n   */\n  async getAttendanceRecords() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('attendance').select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(*)\n        `).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(attendance => this.mapAttendanceFromDB(attendance))) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId, date) {\n    try {\n      let query = supabase.from('attendance').select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `).eq('course_id', courseId);\n      if (date) {\n        query = query.eq('date', date);\n      }\n      query = query.order('created_at', {\n        ascending: false\n      });\n      const {\n        data,\n        error\n      } = await query;\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(attendance => this.mapAttendanceFromDB(attendance))) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('attendance').select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `).eq('student_id', studentId).order('date', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(attendance => this.mapAttendanceFromDB(attendance))) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences de l\\'étudiant:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId, file) {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/profile.${fileExt}`;\n      const filePath = `profile-images/${fileName}`;\n      const {\n        error: uploadError\n      } = await supabase.storage.from('images').upload(filePath, file, {\n        upsert: true\n      });\n      if (uploadError) throw uploadError;\n      const {\n        data\n      } = supabase.storage.from('images').getPublicUrl(filePath);\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId, file) {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/${Date.now()}.${fileExt}`;\n      const filePath = `face-images/${fileName}`;\n      const {\n        error: uploadError\n      } = await supabase.storage.from('images').upload(filePath, file);\n      if (uploadError) throw uploadError;\n      const {\n        data\n      } = supabase.storage.from('images').getPublicUrl(filePath);\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image faciale:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl) {\n    try {\n      // Extraire le chemin du fichier depuis l'URL\n      const urlParts = imageUrl.split('/');\n      const bucketIndex = urlParts.findIndex(part => part === 'images');\n      if (bucketIndex === -1) throw new Error('URL d\\'image invalide');\n      const filePath = urlParts.slice(bucketIndex + 1).join('/');\n      const {\n        error\n      } = await supabase.storage.from('images').remove([filePath]);\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats() {\n    try {\n      // Compter les utilisateurs par rôle\n      const {\n        data: users,\n        error: usersError\n      } = await supabase.from('users').select('role');\n      if (usersError) throw usersError;\n\n      // Compter les cours\n      const {\n        count: coursesCount,\n        error: coursesError\n      } = await supabase.from('courses').select('*', {\n        count: 'exact',\n        head: true\n      });\n      if (coursesError) throw coursesError;\n\n      // Compter les présences\n      const {\n        count: attendanceCount,\n        error: attendanceError\n      } = await supabase.from('attendance').select('*', {\n        count: 'exact',\n        head: true\n      });\n      if (attendanceError) throw attendanceError;\n      const stats = {\n        totalUsers: (users === null || users === void 0 ? void 0 : users.length) || 0,\n        totalStudents: (users === null || users === void 0 ? void 0 : users.filter(user => user.role === UserRole.STUDENT).length) || 0,\n        totalTeachers: (users === null || users === void 0 ? void 0 : users.filter(user => user.role === UserRole.TEACHER).length) || 0,\n        totalAdmins: (users === null || users === void 0 ? void 0 : users.filter(user => user.role === UserRole.ADMIN).length) || 0,\n        totalCourses: coursesCount || 0,\n        totalAttendances: attendanceCount || 0\n      };\n      return stats;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  }\n\n  // ==================== MÉTHODES DE MAPPING ====================\n\n  mapUserFromDB(dbUser) {\n    const firstName = dbUser.first_name || '';\n    const lastName = dbUser.last_name || '';\n    const role = dbUser.role || 'student';\n    return {\n      id: dbUser.id,\n      username: dbUser.username || '',\n      email: dbUser.email || '',\n      firstName,\n      lastName,\n      fullName: `${firstName} ${lastName}`.trim() || 'Utilisateur',\n      role: role,\n      roleDisplay: this.getRoleDisplay(role),\n      phoneNumber: dbUser.phone_number || undefined,\n      dateOfBirth: dbUser.date_of_birth || undefined,\n      address: dbUser.address || undefined,\n      profilePicture: dbUser.profile_picture || undefined,\n      isActive: dbUser.is_active !== false,\n      dateJoined: dbUser.created_at || new Date().toISOString(),\n      lastLogin: dbUser.last_login || undefined\n    };\n  }\n  mapUserToDB(user) {\n    return {\n      username: user.username,\n      email: user.email,\n      first_name: user.firstName,\n      last_name: user.lastName,\n      role: user.role,\n      phone_number: user.phoneNumber,\n      date_of_birth: user.dateOfBirth,\n      address: user.address,\n      profile_picture: user.profilePicture,\n      is_active: user.isActive,\n      last_login: user.lastLogin\n    };\n  }\n  mapCourseFromDB(dbCourse) {\n    return {\n      id: dbCourse.id,\n      name: dbCourse.name,\n      code: dbCourse.code,\n      description: dbCourse.description,\n      teacher: this.mapUserFromDB(dbCourse.teacher),\n      studentGroup: dbCourse.student_group,\n      schedule: dbCourse.schedule || [],\n      academicYear: dbCourse.academic_year,\n      semester: dbCourse.semester,\n      credits: dbCourse.credits,\n      isActive: dbCourse.is_active,\n      createdAt: dbCourse.created_at,\n      updatedAt: dbCourse.updated_at\n    };\n  }\n  mapCourseToDB(course) {\n    var _course$teacher, _course$studentGroup;\n    return {\n      name: course.name,\n      code: course.code,\n      description: course.description,\n      teacher_id: (_course$teacher = course.teacher) === null || _course$teacher === void 0 ? void 0 : _course$teacher.id,\n      student_group_id: (_course$studentGroup = course.studentGroup) === null || _course$studentGroup === void 0 ? void 0 : _course$studentGroup.id,\n      schedule: course.schedule,\n      academic_year: course.academicYear,\n      semester: course.semester,\n      credits: course.credits,\n      is_active: course.isActive\n    };\n  }\n  mapAttendanceFromDB(dbAttendance) {\n    return {\n      id: dbAttendance.id,\n      student: this.mapUserFromDB(dbAttendance.student),\n      course: dbAttendance.course,\n      date: dbAttendance.date,\n      time: dbAttendance.time,\n      status: dbAttendance.status,\n      method: dbAttendance.method,\n      confidence: dbAttendance.confidence,\n      notes: dbAttendance.notes,\n      createdAt: dbAttendance.created_at,\n      updatedAt: dbAttendance.updated_at\n    };\n  }\n  mapAttendanceToDB(attendance) {\n    var _attendance$student, _attendance$course;\n    return {\n      student_id: (_attendance$student = attendance.student) === null || _attendance$student === void 0 ? void 0 : _attendance$student.id,\n      course_id: (_attendance$course = attendance.course) === null || _attendance$course === void 0 ? void 0 : _attendance$course.id,\n      date: attendance.date,\n      time: attendance.time,\n      status: attendance.status,\n      method: attendance.method,\n      confidence: attendance.confidence,\n      notes: attendance.notes\n    };\n  }\n\n  /**\n   * Récupère un utilisateur par email\n   */\n  async getUserByEmail(email) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('*').eq('email', email).single();\n      if (error) throw error;\n      if (!data) throw new Error('Utilisateur non trouvé');\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur par email:', error);\n      throw error;\n    }\n  }\n  getRoleDisplay(role) {\n    const roleStr = typeof role === 'string' ? role : role;\n    switch (roleStr) {\n      case UserRole.ADMIN:\n      case 'admin':\n        return 'Administrateur';\n      case UserRole.TEACHER:\n      case 'teacher':\n        return 'Professeur';\n      case UserRole.STUDENT:\n      case 'student':\n        return 'Étudiant';\n      default:\n        return 'Utilisateur';\n    }\n  }\n}\n\n// Instance singleton du service Supabase\nexport const supabaseService = new SupabaseService();\nexport default supabaseService;", "map": {"version": 3, "names": ["supabase", "UserRole", "SupabaseService", "getUsers", "data", "error", "from", "select", "order", "ascending", "map", "user", "mapUserFromDB", "console", "getUserById", "userId", "eq", "single", "code", "createUser", "userData", "dbUser", "mapUserToDB", "insert", "createUserWithId", "id", "updateUser", "update", "updated_at", "Date", "toISOString", "deleteUser", "delete", "getCourses", "course", "mapCourseFromDB", "getCoursesByTeacher", "teacherId", "createCourse", "courseData", "dbCourse", "mapCourseToDB", "recordAttendance", "attendanceData", "dbAttendance", "mapAttendanceToDB", "getAttendanceRecords", "attendance", "mapAttendanceFromDB", "getAttendanceByCourse", "courseId", "date", "query", "getAttendanceByStudent", "studentId", "uploadProfileImage", "file", "fileExt", "name", "split", "pop", "fileName", "filePath", "uploadError", "storage", "upload", "upsert", "getPublicUrl", "publicUrl", "uploadFaceImage", "now", "deleteImage", "imageUrl", "urlParts", "bucketIndex", "findIndex", "part", "Error", "slice", "join", "remove", "getGlobalStats", "users", "usersError", "count", "coursesCount", "coursesError", "head", "attendanceCount", "attendanceError", "stats", "totalUsers", "length", "totalStudents", "filter", "role", "STUDENT", "totalTeachers", "TEACHER", "totalAdmins", "ADMIN", "totalCourses", "totalAttendances", "firstName", "first_name", "lastName", "last_name", "username", "email", "fullName", "trim", "roleDisplay", "getRoleDisplay", "phoneNumber", "phone_number", "undefined", "dateOfBirth", "date_of_birth", "address", "profilePicture", "profile_picture", "isActive", "is_active", "dateJoined", "created_at", "lastLogin", "last_login", "description", "teacher", "studentGroup", "student_group", "schedule", "academicYear", "academic_year", "semester", "credits", "createdAt", "updatedAt", "_course$teacher", "_course$studentGroup", "teacher_id", "student_group_id", "student", "time", "status", "method", "confidence", "notes", "_attendance$student", "_attendance$course", "student_id", "course_id", "getUserByEmail", "roleStr", "supabaseService"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts"], "sourcesContent": ["/**\n * Service Supabase pour PresencePro\n */\n\nimport { supabase } from '../config/supabase';\nimport { User, Course, AttendanceRecord, UserRole } from '../types';\n\nclass SupabaseService {\n  \n  // ==================== GESTION DES UTILISATEURS ====================\n  \n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers(): Promise<User[]> {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(user => this.mapUserFromDB(user)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des utilisateurs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId: string): Promise<User | null> {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        if (error.code === 'PGRST116') return null; // Not found\n        throw error;\n      }\n      \n      return data ? this.mapUserFromDB(data) : null;\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData: Omit<User, 'id'>): Promise<User> {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n\n      const { data, error } = await supabase\n        .from('users')\n        .insert([dbUser])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur avec un ID spécifique (pour l'authentification)\n   */\n  async createUserWithId(userId: string, userData: Omit<User, 'id'>): Promise<void> {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      \n      const { error } = await supabase\n        .from('users')\n        .insert([{ ...dbUser, id: userId }]);\n\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur avec ID:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId: string, userData: Partial<User>): Promise<User> {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n\n      const { data, error } = await supabase\n        .from('users')\n        .update({ ...dbUser, updated_at: new Date().toISOString() })\n        .eq('id', userId)\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId: string): Promise<void> {\n    try {\n      const { error } = await supabase\n        .from('users')\n        .delete()\n        .eq('id', userId);\n\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours avec leurs relations\n   */\n  async getCourses(): Promise<Course[]> {\n    try {\n      const { data, error } = await supabase\n        .from('courses')\n        .select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(course => this.mapCourseFromDB(course)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {\n    try {\n      const { data, error } = await supabase\n        .from('courses')\n        .select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `)\n        .eq('teacher_id', teacherId)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(course => this.mapCourseFromDB(course)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours de l\\'enseignant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const dbCourse = this.mapCourseToDB(courseData);\n      \n      const { data, error } = await supabase\n        .from('courses')\n        .insert([dbCourse])\n        .select()\n        .single();\n\n      if (error) throw error;\n      \n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de la création du cours:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {\n    try {\n      const dbAttendance = this.mapAttendanceToDB(attendanceData);\n      \n      const { data, error } = await supabase\n        .from('attendance')\n        .insert([dbAttendance])\n        .select()\n        .single();\n\n      if (error) throw error;\n      \n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de l\\'enregistrement de la présence:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère toutes les présences\n   */\n  async getAttendanceRecords(): Promise<AttendanceRecord[]> {\n    try {\n      const { data, error } = await supabase\n        .from('attendance')\n        .select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(*)\n        `)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n\n      return data?.map(attendance => this.mapAttendanceFromDB(attendance)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {\n    try {\n      let query = supabase\n        .from('attendance')\n        .select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `)\n        .eq('course_id', courseId);\n      \n      if (date) {\n        query = query.eq('date', date);\n      }\n      \n      query = query.order('created_at', { ascending: false });\n      \n      const { data, error } = await query;\n\n      if (error) throw error;\n      \n      return data?.map(attendance => this.mapAttendanceFromDB(attendance)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {\n    try {\n      const { data, error } = await supabase\n        .from('attendance')\n        .select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `)\n        .eq('student_id', studentId)\n        .order('date', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(attendance => this.mapAttendanceFromDB(attendance)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences de l\\'étudiant:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId: string, file: File): Promise<string> {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/profile.${fileExt}`;\n      const filePath = `profile-images/${fileName}`;\n\n      const { error: uploadError } = await supabase.storage\n        .from('images')\n        .upload(filePath, file, { upsert: true });\n\n      if (uploadError) throw uploadError;\n\n      const { data } = supabase.storage\n        .from('images')\n        .getPublicUrl(filePath);\n\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId: string, file: File): Promise<string> {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/${Date.now()}.${fileExt}`;\n      const filePath = `face-images/${fileName}`;\n\n      const { error: uploadError } = await supabase.storage\n        .from('images')\n        .upload(filePath, file);\n\n      if (uploadError) throw uploadError;\n\n      const { data } = supabase.storage\n        .from('images')\n        .getPublicUrl(filePath);\n\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image faciale:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl: string): Promise<void> {\n    try {\n      // Extraire le chemin du fichier depuis l'URL\n      const urlParts = imageUrl.split('/');\n      const bucketIndex = urlParts.findIndex(part => part === 'images');\n      if (bucketIndex === -1) throw new Error('URL d\\'image invalide');\n      \n      const filePath = urlParts.slice(bucketIndex + 1).join('/');\n\n      const { error } = await supabase.storage\n        .from('images')\n        .remove([filePath]);\n\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats(): Promise<any> {\n    try {\n      // Compter les utilisateurs par rôle\n      const { data: users, error: usersError } = await supabase\n        .from('users')\n        .select('role');\n\n      if (usersError) throw usersError;\n\n      // Compter les cours\n      const { count: coursesCount, error: coursesError } = await supabase\n        .from('courses')\n        .select('*', { count: 'exact', head: true });\n\n      if (coursesError) throw coursesError;\n\n      // Compter les présences\n      const { count: attendanceCount, error: attendanceError } = await supabase\n        .from('attendance')\n        .select('*', { count: 'exact', head: true });\n\n      if (attendanceError) throw attendanceError;\n\n      const stats = {\n        totalUsers: users?.length || 0,\n        totalStudents: users?.filter(user => user.role === UserRole.STUDENT).length || 0,\n        totalTeachers: users?.filter(user => user.role === UserRole.TEACHER).length || 0,\n        totalAdmins: users?.filter(user => user.role === UserRole.ADMIN).length || 0,\n        totalCourses: coursesCount || 0,\n        totalAttendances: attendanceCount || 0\n      };\n\n      return stats;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  }\n\n  // ==================== MÉTHODES DE MAPPING ====================\n\n  private mapUserFromDB(dbUser: any): User {\n    const firstName = dbUser.first_name || '';\n    const lastName = dbUser.last_name || '';\n    const role = dbUser.role || 'student';\n\n    return {\n      id: dbUser.id,\n      username: dbUser.username || '',\n      email: dbUser.email || '',\n      firstName,\n      lastName,\n      fullName: `${firstName} ${lastName}`.trim() || 'Utilisateur',\n      role: role as UserRole,\n      roleDisplay: this.getRoleDisplay(role as UserRole),\n      phoneNumber: dbUser.phone_number || undefined,\n      dateOfBirth: dbUser.date_of_birth || undefined,\n      address: dbUser.address || undefined,\n      profilePicture: dbUser.profile_picture || undefined,\n      isActive: dbUser.is_active !== false,\n      dateJoined: dbUser.created_at || new Date().toISOString(),\n      lastLogin: dbUser.last_login || undefined\n    };\n  }\n\n  private mapUserToDB(user: Partial<User>): any {\n    return {\n      username: user.username,\n      email: user.email,\n      first_name: user.firstName,\n      last_name: user.lastName,\n      role: user.role,\n      phone_number: user.phoneNumber,\n      date_of_birth: user.dateOfBirth,\n      address: user.address,\n      profile_picture: user.profilePicture,\n      is_active: user.isActive,\n      last_login: user.lastLogin\n    };\n  }\n\n  private mapCourseFromDB(dbCourse: any): Course {\n    return {\n      id: dbCourse.id,\n      name: dbCourse.name,\n      code: dbCourse.code,\n      description: dbCourse.description,\n      teacher: this.mapUserFromDB(dbCourse.teacher),\n      studentGroup: dbCourse.student_group,\n      schedule: dbCourse.schedule || [],\n      academicYear: dbCourse.academic_year,\n      semester: dbCourse.semester,\n      credits: dbCourse.credits,\n      isActive: dbCourse.is_active,\n      createdAt: dbCourse.created_at,\n      updatedAt: dbCourse.updated_at\n    };\n  }\n\n  private mapCourseToDB(course: Partial<Course>): any {\n    return {\n      name: course.name,\n      code: course.code,\n      description: course.description,\n      teacher_id: course.teacher?.id,\n      student_group_id: course.studentGroup?.id,\n      schedule: course.schedule,\n      academic_year: course.academicYear,\n      semester: course.semester,\n      credits: course.credits,\n      is_active: course.isActive\n    };\n  }\n\n  private mapAttendanceFromDB(dbAttendance: any): AttendanceRecord {\n    return {\n      id: dbAttendance.id,\n      student: this.mapUserFromDB(dbAttendance.student),\n      course: dbAttendance.course,\n      date: dbAttendance.date,\n      time: dbAttendance.time,\n      status: dbAttendance.status,\n      method: dbAttendance.method,\n      confidence: dbAttendance.confidence,\n      notes: dbAttendance.notes,\n      createdAt: dbAttendance.created_at,\n      updatedAt: dbAttendance.updated_at\n    };\n  }\n\n  private mapAttendanceToDB(attendance: Partial<AttendanceRecord>): any {\n    return {\n      student_id: attendance.student?.id,\n      course_id: attendance.course?.id,\n      date: attendance.date,\n      time: attendance.time,\n      status: attendance.status,\n      method: attendance.method,\n      confidence: attendance.confidence,\n      notes: attendance.notes\n    };\n  }\n\n\n\n  /**\n   * Récupère un utilisateur par email\n   */\n  async getUserByEmail(email: string): Promise<User> {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('email', email)\n        .single();\n\n      if (error) throw error;\n      if (!data) throw new Error('Utilisateur non trouvé');\n\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur par email:', error);\n      throw error;\n    }\n  }\n\n  private getRoleDisplay(role: UserRole | string): string {\n    const roleStr = typeof role === 'string' ? role : role;\n\n    switch (roleStr) {\n      case UserRole.ADMIN:\n      case 'admin':\n        return 'Administrateur';\n      case UserRole.TEACHER:\n      case 'teacher':\n        return 'Professeur';\n      case UserRole.STUDENT:\n      case 'student':\n        return 'Étudiant';\n      default:\n        return 'Utilisateur';\n    }\n  }\n}\n\n// Instance singleton du service Supabase\nexport const supabaseService = new SupabaseService();\nexport default supabaseService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,SAAyCC,QAAQ,QAAQ,UAAU;AAEnE,MAAMC,eAAe,CAAC;EAEpB;;EAEA;AACF;AACA;EACE,MAAMC,QAAQA,CAAA,EAAoB;IAChC,IAAI;MACF,MAAM;QAAEC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAACC,IAAI,IAAI,IAAI,CAACC,aAAa,CAACD,IAAI,CAAC,CAAC,KAAI,EAAE;IAC1D,CAAC,CAAC,OAAON,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMS,WAAWA,CAACC,MAAc,EAAwB;IACtD,IAAI;MACF,MAAM;QAAEX,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXS,EAAE,CAAC,IAAI,EAAED,MAAM,CAAC,CAChBE,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE;QACT,IAAIA,KAAK,CAACa,IAAI,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC,CAAC;QAC5C,MAAMb,KAAK;MACb;MAEA,OAAOD,IAAI,GAAG,IAAI,CAACQ,aAAa,CAACR,IAAI,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMc,UAAUA,CAACC,QAA0B,EAAiB;IAC1D,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;MAEzC,MAAM;QAAEhB,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbiB,MAAM,CAAC,CAACF,MAAM,CAAC,CAAC,CAChBd,MAAM,CAAC,CAAC,CACRU,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,IAAI,CAACO,aAAa,CAACR,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMmB,gBAAgBA,CAACT,MAAc,EAAEK,QAA0B,EAAiB;IAChF,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;MAEzC,MAAM;QAAEf;MAAM,CAAC,GAAG,MAAML,QAAQ,CAC7BM,IAAI,CAAC,OAAO,CAAC,CACbiB,MAAM,CAAC,CAAC;QAAE,GAAGF,MAAM;QAAEI,EAAE,EAAEV;MAAO,CAAC,CAAC,CAAC;MAEtC,IAAIV,KAAK,EAAE,MAAMA,KAAK;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMqB,UAAUA,CAACX,MAAc,EAAEK,QAAuB,EAAiB;IACvE,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;MAEzC,MAAM;QAAEhB,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbqB,MAAM,CAAC;QAAE,GAAGN,MAAM;QAAEO,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,CAAC,CAC3Dd,EAAE,CAAC,IAAI,EAAED,MAAM,CAAC,CAChBR,MAAM,CAAC,CAAC,CACRU,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,IAAI,CAACO,aAAa,CAACR,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM0B,UAAUA,CAAChB,MAAc,EAAiB;IAC9C,IAAI;MACF,MAAM;QAAEV;MAAM,CAAC,GAAG,MAAML,QAAQ,CAC7BM,IAAI,CAAC,OAAO,CAAC,CACb0B,MAAM,CAAC,CAAC,CACRhB,EAAE,CAAC,IAAI,EAAED,MAAM,CAAC;MAEnB,IAAIV,KAAK,EAAE,MAAMA,KAAK;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAM4B,UAAUA,CAAA,EAAsB;IACpC,IAAI;MACF,MAAM;QAAE7B,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAACwB,MAAM,IAAI,IAAI,CAACC,eAAe,CAACD,MAAM,CAAC,CAAC,KAAI,EAAE;IAChE,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM+B,mBAAmBA,CAACC,SAAiB,EAAqB;IAC9D,IAAI;MACF,MAAM;QAAEjC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDS,EAAE,CAAC,YAAY,EAAEqB,SAAS,CAAC,CAC3B7B,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAACwB,MAAM,IAAI,IAAI,CAACC,eAAe,CAACD,MAAM,CAAC,CAAC,KAAI,EAAE;IAChE,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;MAClF,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiC,YAAYA,CAACC,UAA8B,EAAmB;IAClE,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACF,UAAU,CAAC;MAE/C,MAAM;QAAEnC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfiB,MAAM,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAClBjC,MAAM,CAAC,CAAC,CACRU,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAOD,IAAI,CAACqB,EAAE;IAChB,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMqC,gBAAgBA,CAACC,cAA4C,EAAmB;IACpF,IAAI;MACF,MAAMC,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAACF,cAAc,CAAC;MAE3D,MAAM;QAAEvC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBiB,MAAM,CAAC,CAACqB,YAAY,CAAC,CAAC,CACtBrC,MAAM,CAAC,CAAC,CACRU,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAOD,IAAI,CAACqB,EAAE;IAChB,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMyC,oBAAoBA,CAAA,EAAgC;IACxD,IAAI;MACF,MAAM;QAAE1C,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAACqC,UAAU,IAAI,IAAI,CAACC,mBAAmB,CAACD,UAAU,CAAC,CAAC,KAAI,EAAE;IAC5E,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4C,qBAAqBA,CAACC,QAAgB,EAAEC,IAAa,EAA+B;IACxF,IAAI;MACF,IAAIC,KAAK,GAAGpD,QAAQ,CACjBM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDS,EAAE,CAAC,WAAW,EAAEkC,QAAQ,CAAC;MAE5B,IAAIC,IAAI,EAAE;QACRC,KAAK,GAAGA,KAAK,CAACpC,EAAE,CAAC,MAAM,EAAEmC,IAAI,CAAC;MAChC;MAEAC,KAAK,GAAGA,KAAK,CAAC5C,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAEvD,MAAM;QAAEL,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM+C,KAAK;MAEnC,IAAI/C,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAACqC,UAAU,IAAI,IAAI,CAACC,mBAAmB,CAACD,UAAU,CAAC,CAAC,KAAI,EAAE;IAC5E,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgD,sBAAsBA,CAACC,SAAiB,EAA+B;IAC3E,IAAI;MACF,MAAM;QAAElD,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDS,EAAE,CAAC,YAAY,EAAEsC,SAAS,CAAC,CAC3B9C,KAAK,CAAC,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAEtC,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAACqC,UAAU,IAAI,IAAI,CAACC,mBAAmB,CAACD,UAAU,CAAC,CAAC,KAAI,EAAE;IAC5E,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACpF,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMkD,kBAAkBA,CAACxC,MAAc,EAAEyC,IAAU,EAAmB;IACpE,IAAI;MACF,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,GAAG9C,MAAM,YAAY0C,OAAO,EAAE;MAC/C,MAAMK,QAAQ,GAAG,kBAAkBD,QAAQ,EAAE;MAE7C,MAAM;QAAExD,KAAK,EAAE0D;MAAY,CAAC,GAAG,MAAM/D,QAAQ,CAACgE,OAAO,CAClD1D,IAAI,CAAC,QAAQ,CAAC,CACd2D,MAAM,CAACH,QAAQ,EAAEN,IAAI,EAAE;QAAEU,MAAM,EAAE;MAAK,CAAC,CAAC;MAE3C,IAAIH,WAAW,EAAE,MAAMA,WAAW;MAElC,MAAM;QAAE3D;MAAK,CAAC,GAAGJ,QAAQ,CAACgE,OAAO,CAC9B1D,IAAI,CAAC,QAAQ,CAAC,CACd6D,YAAY,CAACL,QAAQ,CAAC;MAEzB,OAAO1D,IAAI,CAACgE,SAAS;IACvB,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgE,eAAeA,CAACtD,MAAc,EAAEyC,IAAU,EAAmB;IACjE,IAAI;MACF,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,GAAG9C,MAAM,IAAIc,IAAI,CAACyC,GAAG,CAAC,CAAC,IAAIb,OAAO,EAAE;MACrD,MAAMK,QAAQ,GAAG,eAAeD,QAAQ,EAAE;MAE1C,MAAM;QAAExD,KAAK,EAAE0D;MAAY,CAAC,GAAG,MAAM/D,QAAQ,CAACgE,OAAO,CAClD1D,IAAI,CAAC,QAAQ,CAAC,CACd2D,MAAM,CAACH,QAAQ,EAAEN,IAAI,CAAC;MAEzB,IAAIO,WAAW,EAAE,MAAMA,WAAW;MAElC,MAAM;QAAE3D;MAAK,CAAC,GAAGJ,QAAQ,CAACgE,OAAO,CAC9B1D,IAAI,CAAC,QAAQ,CAAC,CACd6D,YAAY,CAACL,QAAQ,CAAC;MAEzB,OAAO1D,IAAI,CAACgE,SAAS;IACvB,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMkE,WAAWA,CAACC,QAAgB,EAAiB;IACjD,IAAI;MACF;MACA,MAAMC,QAAQ,GAAGD,QAAQ,CAACb,KAAK,CAAC,GAAG,CAAC;MACpC,MAAMe,WAAW,GAAGD,QAAQ,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,KAAK,QAAQ,CAAC;MACjE,IAAIF,WAAW,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,uBAAuB,CAAC;MAEhE,MAAMf,QAAQ,GAAGW,QAAQ,CAACK,KAAK,CAACJ,WAAW,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;MAE1D,MAAM;QAAE1E;MAAM,CAAC,GAAG,MAAML,QAAQ,CAACgE,OAAO,CACrC1D,IAAI,CAAC,QAAQ,CAAC,CACd0E,MAAM,CAAC,CAAClB,QAAQ,CAAC,CAAC;MAErB,IAAIzD,KAAK,EAAE,MAAMA,KAAK;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAM4E,cAAcA,CAAA,EAAiB;IACnC,IAAI;MACF;MACA,MAAM;QAAE7E,IAAI,EAAE8E,KAAK;QAAE7E,KAAK,EAAE8E;MAAW,CAAC,GAAG,MAAMnF,QAAQ,CACtDM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,MAAM,CAAC;MAEjB,IAAI4E,UAAU,EAAE,MAAMA,UAAU;;MAEhC;MACA,MAAM;QAAEC,KAAK,EAAEC,YAAY;QAAEhF,KAAK,EAAEiF;MAAa,CAAC,GAAG,MAAMtF,QAAQ,CAChEM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,EAAE;QAAE6E,KAAK,EAAE,OAAO;QAAEG,IAAI,EAAE;MAAK,CAAC,CAAC;MAE9C,IAAID,YAAY,EAAE,MAAMA,YAAY;;MAEpC;MACA,MAAM;QAAEF,KAAK,EAAEI,eAAe;QAAEnF,KAAK,EAAEoF;MAAgB,CAAC,GAAG,MAAMzF,QAAQ,CACtEM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC,GAAG,EAAE;QAAE6E,KAAK,EAAE,OAAO;QAAEG,IAAI,EAAE;MAAK,CAAC,CAAC;MAE9C,IAAIE,eAAe,EAAE,MAAMA,eAAe;MAE1C,MAAMC,KAAK,GAAG;QACZC,UAAU,EAAE,CAAAT,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,MAAM,KAAI,CAAC;QAC9BC,aAAa,EAAE,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,CAACnF,IAAI,IAAIA,IAAI,CAACoF,IAAI,KAAK9F,QAAQ,CAAC+F,OAAO,CAAC,CAACJ,MAAM,KAAI,CAAC;QAChFK,aAAa,EAAE,CAAAf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,CAACnF,IAAI,IAAIA,IAAI,CAACoF,IAAI,KAAK9F,QAAQ,CAACiG,OAAO,CAAC,CAACN,MAAM,KAAI,CAAC;QAChFO,WAAW,EAAE,CAAAjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,CAACnF,IAAI,IAAIA,IAAI,CAACoF,IAAI,KAAK9F,QAAQ,CAACmG,KAAK,CAAC,CAACR,MAAM,KAAI,CAAC;QAC5ES,YAAY,EAAEhB,YAAY,IAAI,CAAC;QAC/BiB,gBAAgB,EAAEd,eAAe,IAAI;MACvC,CAAC;MAED,OAAOE,KAAK;IACd,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEQO,aAAaA,CAACS,MAAW,EAAQ;IACvC,MAAMkF,SAAS,GAAGlF,MAAM,CAACmF,UAAU,IAAI,EAAE;IACzC,MAAMC,QAAQ,GAAGpF,MAAM,CAACqF,SAAS,IAAI,EAAE;IACvC,MAAMX,IAAI,GAAG1E,MAAM,CAAC0E,IAAI,IAAI,SAAS;IAErC,OAAO;MACLtE,EAAE,EAAEJ,MAAM,CAACI,EAAE;MACbkF,QAAQ,EAAEtF,MAAM,CAACsF,QAAQ,IAAI,EAAE;MAC/BC,KAAK,EAAEvF,MAAM,CAACuF,KAAK,IAAI,EAAE;MACzBL,SAAS;MACTE,QAAQ;MACRI,QAAQ,EAAE,GAAGN,SAAS,IAAIE,QAAQ,EAAE,CAACK,IAAI,CAAC,CAAC,IAAI,aAAa;MAC5Df,IAAI,EAAEA,IAAgB;MACtBgB,WAAW,EAAE,IAAI,CAACC,cAAc,CAACjB,IAAgB,CAAC;MAClDkB,WAAW,EAAE5F,MAAM,CAAC6F,YAAY,IAAIC,SAAS;MAC7CC,WAAW,EAAE/F,MAAM,CAACgG,aAAa,IAAIF,SAAS;MAC9CG,OAAO,EAAEjG,MAAM,CAACiG,OAAO,IAAIH,SAAS;MACpCI,cAAc,EAAElG,MAAM,CAACmG,eAAe,IAAIL,SAAS;MACnDM,QAAQ,EAAEpG,MAAM,CAACqG,SAAS,KAAK,KAAK;MACpCC,UAAU,EAAEtG,MAAM,CAACuG,UAAU,IAAI,IAAI/F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACzD+F,SAAS,EAAExG,MAAM,CAACyG,UAAU,IAAIX;IAClC,CAAC;EACH;EAEQ7F,WAAWA,CAACX,IAAmB,EAAO;IAC5C,OAAO;MACLgG,QAAQ,EAAEhG,IAAI,CAACgG,QAAQ;MACvBC,KAAK,EAAEjG,IAAI,CAACiG,KAAK;MACjBJ,UAAU,EAAE7F,IAAI,CAAC4F,SAAS;MAC1BG,SAAS,EAAE/F,IAAI,CAAC8F,QAAQ;MACxBV,IAAI,EAAEpF,IAAI,CAACoF,IAAI;MACfmB,YAAY,EAAEvG,IAAI,CAACsG,WAAW;MAC9BI,aAAa,EAAE1G,IAAI,CAACyG,WAAW;MAC/BE,OAAO,EAAE3G,IAAI,CAAC2G,OAAO;MACrBE,eAAe,EAAE7G,IAAI,CAAC4G,cAAc;MACpCG,SAAS,EAAE/G,IAAI,CAAC8G,QAAQ;MACxBK,UAAU,EAAEnH,IAAI,CAACkH;IACnB,CAAC;EACH;EAEQ1F,eAAeA,CAACK,QAAa,EAAU;IAC7C,OAAO;MACLf,EAAE,EAAEe,QAAQ,CAACf,EAAE;MACfiC,IAAI,EAAElB,QAAQ,CAACkB,IAAI;MACnBxC,IAAI,EAAEsB,QAAQ,CAACtB,IAAI;MACnB6G,WAAW,EAAEvF,QAAQ,CAACuF,WAAW;MACjCC,OAAO,EAAE,IAAI,CAACpH,aAAa,CAAC4B,QAAQ,CAACwF,OAAO,CAAC;MAC7CC,YAAY,EAAEzF,QAAQ,CAAC0F,aAAa;MACpCC,QAAQ,EAAE3F,QAAQ,CAAC2F,QAAQ,IAAI,EAAE;MACjCC,YAAY,EAAE5F,QAAQ,CAAC6F,aAAa;MACpCC,QAAQ,EAAE9F,QAAQ,CAAC8F,QAAQ;MAC3BC,OAAO,EAAE/F,QAAQ,CAAC+F,OAAO;MACzBd,QAAQ,EAAEjF,QAAQ,CAACkF,SAAS;MAC5Bc,SAAS,EAAEhG,QAAQ,CAACoF,UAAU;MAC9Ba,SAAS,EAAEjG,QAAQ,CAACZ;IACtB,CAAC;EACH;EAEQa,aAAaA,CAACP,MAAuB,EAAO;IAAA,IAAAwG,eAAA,EAAAC,oBAAA;IAClD,OAAO;MACLjF,IAAI,EAAExB,MAAM,CAACwB,IAAI;MACjBxC,IAAI,EAAEgB,MAAM,CAAChB,IAAI;MACjB6G,WAAW,EAAE7F,MAAM,CAAC6F,WAAW;MAC/Ba,UAAU,GAAAF,eAAA,GAAExG,MAAM,CAAC8F,OAAO,cAAAU,eAAA,uBAAdA,eAAA,CAAgBjH,EAAE;MAC9BoH,gBAAgB,GAAAF,oBAAA,GAAEzG,MAAM,CAAC+F,YAAY,cAAAU,oBAAA,uBAAnBA,oBAAA,CAAqBlH,EAAE;MACzC0G,QAAQ,EAAEjG,MAAM,CAACiG,QAAQ;MACzBE,aAAa,EAAEnG,MAAM,CAACkG,YAAY;MAClCE,QAAQ,EAAEpG,MAAM,CAACoG,QAAQ;MACzBC,OAAO,EAAErG,MAAM,CAACqG,OAAO;MACvBb,SAAS,EAAExF,MAAM,CAACuF;IACpB,CAAC;EACH;EAEQzE,mBAAmBA,CAACJ,YAAiB,EAAoB;IAC/D,OAAO;MACLnB,EAAE,EAAEmB,YAAY,CAACnB,EAAE;MACnBqH,OAAO,EAAE,IAAI,CAAClI,aAAa,CAACgC,YAAY,CAACkG,OAAO,CAAC;MACjD5G,MAAM,EAAEU,YAAY,CAACV,MAAM;MAC3BiB,IAAI,EAAEP,YAAY,CAACO,IAAI;MACvB4F,IAAI,EAAEnG,YAAY,CAACmG,IAAI;MACvBC,MAAM,EAAEpG,YAAY,CAACoG,MAAM;MAC3BC,MAAM,EAAErG,YAAY,CAACqG,MAAM;MAC3BC,UAAU,EAAEtG,YAAY,CAACsG,UAAU;MACnCC,KAAK,EAAEvG,YAAY,CAACuG,KAAK;MACzBX,SAAS,EAAE5F,YAAY,CAACgF,UAAU;MAClCa,SAAS,EAAE7F,YAAY,CAAChB;IAC1B,CAAC;EACH;EAEQiB,iBAAiBA,CAACE,UAAqC,EAAO;IAAA,IAAAqG,mBAAA,EAAAC,kBAAA;IACpE,OAAO;MACLC,UAAU,GAAAF,mBAAA,GAAErG,UAAU,CAAC+F,OAAO,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoB3H,EAAE;MAClC8H,SAAS,GAAAF,kBAAA,GAAEtG,UAAU,CAACb,MAAM,cAAAmH,kBAAA,uBAAjBA,kBAAA,CAAmB5H,EAAE;MAChC0B,IAAI,EAAEJ,UAAU,CAACI,IAAI;MACrB4F,IAAI,EAAEhG,UAAU,CAACgG,IAAI;MACrBC,MAAM,EAAEjG,UAAU,CAACiG,MAAM;MACzBC,MAAM,EAAElG,UAAU,CAACkG,MAAM;MACzBC,UAAU,EAAEnG,UAAU,CAACmG,UAAU;MACjCC,KAAK,EAAEpG,UAAU,CAACoG;IACpB,CAAC;EACH;;EAIA;AACF;AACA;EACE,MAAMK,cAAcA,CAAC5C,KAAa,EAAiB;IACjD,IAAI;MACF,MAAM;QAAExG,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXS,EAAE,CAAC,OAAO,EAAE4F,KAAK,CAAC,CAClB3F,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MACtB,IAAI,CAACD,IAAI,EAAE,MAAM,IAAIyE,KAAK,CAAC,wBAAwB,CAAC;MAEpD,OAAO,IAAI,CAACjE,aAAa,CAACR,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;MACnF,MAAMA,KAAK;IACb;EACF;EAEQ2G,cAAcA,CAACjB,IAAuB,EAAU;IACtD,MAAM0D,OAAO,GAAG,OAAO1D,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI;IAEtD,QAAQ0D,OAAO;MACb,KAAKxJ,QAAQ,CAACmG,KAAK;MACnB,KAAK,OAAO;QACV,OAAO,gBAAgB;MACzB,KAAKnG,QAAQ,CAACiG,OAAO;MACrB,KAAK,SAAS;QACZ,OAAO,YAAY;MACrB,KAAKjG,QAAQ,CAAC+F,OAAO;MACrB,KAAK,SAAS;QACZ,OAAO,UAAU;MACnB;QACE,OAAO,aAAa;IACxB;EACF;AACF;;AAEA;AACA,OAAO,MAAM0D,eAAe,GAAG,IAAIxJ,eAAe,CAAC,CAAC;AACpD,eAAewJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}