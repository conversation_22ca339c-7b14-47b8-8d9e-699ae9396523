{"ast": null, "code": "import { Mtcnn } from './Mtcnn';\nexport * from './Mtcnn';\nexport * from './MtcnnOptions';\nexport function createMtcnn(weights) {\n  var net = new Mtcnn();\n  net.extractWeights(weights);\n  return net;\n}", "map": {"version": 3, "names": ["Mtcnn", "createMtcnn", "weights", "net", "extractWeights"], "sources": ["../../../src/mtcnn/index.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAE/B,cAAc,SAAS;AACvB,cAAc,gBAAgB;AAE9B,OAAM,SAAUC,WAAWA,CAACC,OAAqB;EAC/C,IAAMC,GAAG,GAAG,IAAIH,KAAK,EAAE;EACvBG,GAAG,CAACC,cAAc,CAACF,OAAO,CAAC;EAC3B,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}