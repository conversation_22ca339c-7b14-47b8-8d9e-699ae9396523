{"ast": null, "code": "import { env } from '../env';\nexport function resolveInput(arg) {\n  if (!env.isNodejs() && typeof arg === 'string') {\n    return document.getElementById(arg);\n  }\n  return arg;\n}", "map": {"version": 3, "names": ["env", "resolveInput", "arg", "isNodejs", "document", "getElementById"], "sources": ["../../../src/dom/resolveInput.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAE5B,OAAM,SAAUC,YAAYA,CAACC,GAAiB;EAC5C,IAAI,CAACF,GAAG,CAACG,QAAQ,EAAE,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC9C,OAAOE,QAAQ,CAACC,cAAc,CAACH,GAAG,CAAC;;EAErC,OAAOA,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}