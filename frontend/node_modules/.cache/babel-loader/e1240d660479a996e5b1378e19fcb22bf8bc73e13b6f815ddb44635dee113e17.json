{"ast": null, "code": "var LabeledFaceDescriptors = /** @class */function () {\n  function LabeledFaceDescriptors(label, descriptors) {\n    if (!(typeof label === 'string')) {\n      throw new Error('LabeledFaceDescriptors - constructor expected label to be a string');\n    }\n    if (!Array.isArray(descriptors) || descriptors.some(function (desc) {\n      return !(desc instanceof Float32Array);\n    })) {\n      throw new Error('LabeledFaceDescriptors - constructor expected descriptors to be an array of Float32Array');\n    }\n    this._label = label;\n    this._descriptors = descriptors;\n  }\n  Object.defineProperty(LabeledFaceDescriptors.prototype, \"label\", {\n    get: function () {\n      return this._label;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(LabeledFaceDescriptors.prototype, \"descriptors\", {\n    get: function () {\n      return this._descriptors;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  LabeledFaceDescriptors.prototype.toJSON = function () {\n    return {\n      label: this.label,\n      descriptors: this.descriptors.map(function (d) {\n        return Array.from(d);\n      })\n    };\n  };\n  LabeledFaceDescriptors.fromJSON = function (json) {\n    var descriptors = json.descriptors.map(function (d) {\n      return new Float32Array(d);\n    });\n    return new LabeledFaceDescriptors(json.label, descriptors);\n  };\n  return LabeledFaceDescriptors;\n}();\nexport { LabeledFaceDescriptors };", "map": {"version": 3, "names": ["LabeledFaceDescriptors", "label", "descriptors", "Error", "Array", "isArray", "some", "desc", "Float32Array", "_label", "_descriptors", "Object", "defineProperty", "prototype", "get", "toJSON", "map", "d", "from", "fromJSON", "json"], "sources": ["../../../src/classes/LabeledFaceDescriptors.ts"], "sourcesContent": [null], "mappings": "AAAA,IAAAA,sBAAA;EAIE,SAAAA,uBAAYC,KAAa,EAAEC,WAA2B;IACpD,IAAI,EAAE,OAAOD,KAAK,KAAK,QAAQ,CAAC,EAAE;MAChC,MAAM,IAAIE,KAAK,CAAC,oEAAoE,CAAC;;IAGvF,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,IAAIA,WAAW,CAACI,IAAI,CAAC,UAAAC,IAAI;MAAI,SAAEA,IAAI,YAAYC,YAAY,CAAC;IAA/B,CAA+B,CAAC,EAAE;MAC5F,MAAM,IAAIL,KAAK,CAAC,0FAA0F,CAAC;;IAG7G,IAAI,CAACM,MAAM,GAAGR,KAAK;IACnB,IAAI,CAACS,YAAY,GAAGR,WAAW;EACjC;EAEAS,MAAA,CAAAC,cAAA,CAAWZ,sBAAA,CAAAa,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACL,MAAM;IAAC,CAAC;;;;EACjDE,MAAA,CAAAC,cAAA,CAAWZ,sBAAA,CAAAa,SAAA,eAAW;SAAtB,SAAAC,CAAA;MAA2C,OAAO,IAAI,CAACJ,YAAY;IAAC,CAAC;;;;EAE9DV,sBAAA,CAAAa,SAAA,CAAAE,MAAM,GAAb;IACE,OAAO;MACLd,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,WAAW,EAAE,IAAI,CAACA,WAAW,CAACc,GAAG,CAAC,UAACC,CAAC;QAAK,OAAAb,KAAK,CAACc,IAAI,CAACD,CAAC,CAAC;MAAb,CAAa;KACvD;EACH,CAAC;EAEajB,sBAAA,CAAAmB,QAAQ,GAAtB,UAAuBC,IAAS;IAC9B,IAAMlB,WAAW,GAAGkB,IAAI,CAAClB,WAAW,CAACc,GAAG,CAAC,UAACC,CAAM;MAC9C,OAAO,IAAIT,YAAY,CAACS,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,OAAO,IAAIjB,sBAAsB,CAACoB,IAAI,CAACnB,KAAK,EAAEC,WAAW,CAAC;EAC5D,CAAC;EAEH,OAAAF,sBAAC;AAAD,CAAC,CAlCD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}