{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function extractConvParamsFactory(extractWeights, paramMappings) {\n  return function (channelsIn, channelsOut, filterSize, mappedPrefix) {\n    var filters = tf.tensor4d(extractWeights(channelsIn * channelsOut * filterSize * filterSize), [filterSize, filterSize, channelsIn, channelsOut]);\n    var bias = tf.tensor1d(extractWeights(channelsOut));\n    paramMappings.push({\n      paramPath: mappedPrefix + \"/filters\"\n    }, {\n      paramPath: mappedPrefix + \"/bias\"\n    });\n    return {\n      filters: filters,\n      bias: bias\n    };\n  };\n}", "map": {"version": 3, "names": ["tf", "extractConvParamsFactory", "extractWeights", "paramMappings", "channelsIn", "channelsOut", "filterSize", "mappedPrefix", "filters", "tensor4d", "bias", "tensor1d", "push", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/common/extractConvParamsFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAI3C,OAAM,SAAUC,wBAAwBA,CACtCC,cAAsC,EACtCC,aAA6B;EAG7B,OAAO,UACLC,UAAkB,EAClBC,WAAmB,EACnBC,UAAkB,EAClBC,YAAoB;IAGpB,IAAMC,OAAO,GAAGR,EAAE,CAACS,QAAQ,CACzBP,cAAc,CAACE,UAAU,GAAGC,WAAW,GAAGC,UAAU,GAAGA,UAAU,CAAC,EAClE,CAACA,UAAU,EAAEA,UAAU,EAAEF,UAAU,EAAEC,WAAW,CAAC,CAClD;IACD,IAAMK,IAAI,GAAGV,EAAE,CAACW,QAAQ,CAACT,cAAc,CAACG,WAAW,CAAC,CAAC;IAErDF,aAAa,CAACS,IAAI,CAChB;MAAEC,SAAS,EAAKN,YAAY;IAAU,CAAE,EACxC;MAAEM,SAAS,EAAKN,YAAY;IAAO,CAAE,CACtC;IAED,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEE,IAAI,EAAAA;IAAA,CAAE;EAC1B,CAAC;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}