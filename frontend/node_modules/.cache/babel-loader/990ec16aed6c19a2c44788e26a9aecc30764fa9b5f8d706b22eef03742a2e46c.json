{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nfunction getCenterCoordinatesAndSizesLayer(x) {\n  var vec = tf.unstack(tf.transpose(x, [1, 0]));\n  var sizes = [tf.sub(vec[2], vec[0]), tf.sub(vec[3], vec[1])];\n  var centers = [tf.add(vec[0], tf.div(sizes[0], tf.scalar(2))), tf.add(vec[1], tf.div(sizes[1], tf.scalar(2)))];\n  return {\n    sizes: sizes,\n    centers: centers\n  };\n}\nfunction decodeBoxesLayer(x0, x1) {\n  var _a = getCenterCoordinatesAndSizesLayer(x0),\n    sizes = _a.sizes,\n    centers = _a.centers;\n  var vec = tf.unstack(tf.transpose(x1, [1, 0]));\n  var div0_out = tf.div(tf.mul(tf.exp(tf.div(vec[2], tf.scalar(5))), sizes[0]), tf.scalar(2));\n  var add0_out = tf.add(tf.mul(tf.div(vec[0], tf.scalar(10)), sizes[0]), centers[0]);\n  var div1_out = tf.div(tf.mul(tf.exp(tf.div(vec[3], tf.scalar(5))), sizes[1]), tf.scalar(2));\n  var add1_out = tf.add(tf.mul(tf.div(vec[1], tf.scalar(10)), sizes[1]), centers[1]);\n  return tf.transpose(tf.stack([tf.sub(add0_out, div0_out), tf.sub(add1_out, div1_out), tf.add(add0_out, div0_out), tf.add(add1_out, div1_out)]), [1, 0]);\n}\nexport function outputLayer(boxPredictions, classPredictions, params) {\n  return tf.tidy(function () {\n    var batchSize = boxPredictions.shape[0];\n    var boxes = decodeBoxesLayer(tf.reshape(tf.tile(params.extra_dim, [batchSize, 1, 1]), [-1, 4]), tf.reshape(boxPredictions, [-1, 4]));\n    boxes = tf.reshape(boxes, [batchSize, boxes.shape[0] / batchSize, 4]);\n    var scoresAndClasses = tf.sigmoid(tf.slice(classPredictions, [0, 0, 1], [-1, -1, -1]));\n    var scores = tf.slice(scoresAndClasses, [0, 0, 0], [-1, -1, 1]);\n    scores = tf.reshape(scores, [batchSize, scores.shape[1]]);\n    var boxesByBatch = tf.unstack(boxes);\n    var scoresByBatch = tf.unstack(scores);\n    return {\n      boxes: boxesByBatch,\n      scores: scoresByBatch\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "getCenterCoordinatesAndSizesLayer", "x", "vec", "unstack", "transpose", "sizes", "sub", "centers", "add", "div", "scalar", "decodeBoxesLayer", "x0", "x1", "_a", "div0_out", "mul", "exp", "add0_out", "div1_out", "add1_out", "stack", "outputLayer", "boxPredictions", "classPredictions", "params", "tidy", "batchSize", "shape", "boxes", "reshape", "tile", "extra_dim", "scoresAndClasses", "sigmoid", "slice", "scores", "boxesByBatch", "scoresByBatch"], "sources": ["../../../src/ssdMobilenetv1/outputLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAK3C,SAASC,iCAAiCA,CAACC,CAAc;EACvD,IAAMC,GAAG,GAAGH,EAAE,CAACI,OAAO,CAACJ,EAAE,CAACK,SAAS,CAACH,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAE/C,IAAMI,KAAK,GAAG,CACZN,EAAE,CAACO,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,EACtBH,EAAE,CAACO,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CACvB;EAED,IAAMK,OAAO,GAAG,CACdR,EAAE,CAACS,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACU,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEN,EAAE,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9CX,EAAE,CAACS,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACU,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEN,EAAE,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/C;EAED,OAAO;IACLL,KAAK,EAAAA,KAAA;IACLE,OAAO,EAAAA;GACR;AACH;AAEA,SAASI,gBAAgBA,CAACC,EAAe,EAAEC,EAAe;EAClD,IAAAC,EAAA,GAAAd,iCAAA,CAAAY,EAAA,CAGmC;IAFvCP,KAAA,GAAAS,EAAA,CAAAT,KAAK;IACLE,OAAA,GAAAO,EAAA,CAAAP,OACuC;EAEzC,IAAML,GAAG,GAAGH,EAAE,CAACI,OAAO,CAACJ,EAAE,CAACK,SAAS,CAACS,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAEhD,IAAME,QAAQ,GAAGhB,EAAE,CAACU,GAAG,CAACV,EAAE,CAACiB,GAAG,CAACjB,EAAE,CAACkB,GAAG,CAAClB,EAAE,CAACU,GAAG,CAACP,GAAG,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEN,EAAE,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7F,IAAMQ,QAAQ,GAAGnB,EAAE,CAACS,GAAG,CAACT,EAAE,CAACiB,GAAG,CAACjB,EAAE,CAACU,GAAG,CAACP,GAAG,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACW,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEE,OAAO,CAAC,CAAC,CAAC,CAAC;EAEpF,IAAMY,QAAQ,GAAGpB,EAAE,CAACU,GAAG,CAACV,EAAE,CAACiB,GAAG,CAACjB,EAAE,CAACkB,GAAG,CAAClB,EAAE,CAACU,GAAG,CAACP,GAAG,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEN,EAAE,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7F,IAAMU,QAAQ,GAAGrB,EAAE,CAACS,GAAG,CAACT,EAAE,CAACiB,GAAG,CAACjB,EAAE,CAACU,GAAG,CAACP,GAAG,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACW,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEE,OAAO,CAAC,CAAC,CAAC,CAAC;EAEpF,OAAOR,EAAE,CAACK,SAAS,CACjBL,EAAE,CAACsB,KAAK,CAAC,CACPtB,EAAE,CAACO,GAAG,CAACY,QAAQ,EAAEH,QAAQ,CAAC,EAC1BhB,EAAE,CAACO,GAAG,CAACc,QAAQ,EAAED,QAAQ,CAAC,EAC1BpB,EAAE,CAACS,GAAG,CAACU,QAAQ,EAAEH,QAAQ,CAAC,EAC1BhB,EAAE,CAACS,GAAG,CAACY,QAAQ,EAAED,QAAQ,CAAC,CAC3B,CAAC,EACF,CAAC,CAAC,EAAE,CAAC,CAAC,CACP;AACH;AAEA,OAAM,SAAUG,WAAWA,CACzBC,cAA2B,EAC3BC,gBAA6B,EAC7BC,MAAyB;EAEzB,OAAO1B,EAAE,CAAC2B,IAAI,CAAC;IAEb,IAAMC,SAAS,GAAGJ,cAAc,CAACK,KAAK,CAAC,CAAC,CAAC;IAEzC,IAAIC,KAAK,GAAGlB,gBAAgB,CAC1BZ,EAAE,CAAC+B,OAAO,CAAC/B,EAAE,CAACgC,IAAI,CAACN,MAAM,CAACO,SAAS,EAAE,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAgB,EAChF5B,EAAE,CAAC+B,OAAO,CAACP,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAgB,CACnD;IACDM,KAAK,GAAG9B,EAAE,CAAC+B,OAAO,CAChBD,KAAK,EACL,CAACF,SAAS,EAAGE,KAAK,CAACD,KAAK,CAAC,CAAC,CAAC,GAAGD,SAAS,EAAG,CAAC,CAAC,CAC7C;IAED,IAAMM,gBAAgB,GAAGlC,EAAE,CAACmC,OAAO,CAACnC,EAAE,CAACoC,KAAK,CAACX,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,IAAIY,MAAM,GAAGrC,EAAE,CAACoC,KAAK,CAACF,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAc;IAE5EG,MAAM,GAAGrC,EAAE,CAAC+B,OAAO,CACjBM,MAAM,EACN,CAACT,SAAS,EAAES,MAAM,CAACR,KAAK,CAAC,CAAC,CAAW,CAAC,CACvC;IAED,IAAMS,YAAY,GAAGtC,EAAE,CAACI,OAAO,CAAC0B,KAAK,CAAkB;IACvD,IAAMS,aAAa,GAAGvC,EAAE,CAACI,OAAO,CAACiC,MAAM,CAAkB;IAEzD,OAAO;MACLP,KAAK,EAAEQ,YAAY;MACnBD,MAAM,EAAEE;KACT;EAEH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}