{"ast": null, "code": "var getNative = require('./_getNative');\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\nmodule.exports = nativeCreate;", "map": {"version": 3, "names": ["getNative", "require", "nativeCreate", "Object", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_nativeCreate.js"], "sourcesContent": ["var getNative = require('./_getNative');\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\n\nmodule.exports = nativeCreate;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEvC;AACA,IAAIC,YAAY,GAAGF,SAAS,CAACG,MAAM,EAAE,QAAQ,CAAC;AAE9CC,MAAM,CAACC,OAAO,GAAGH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}