{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { Point } from '../classes';\nimport { FaceLandmarks68 } from '../classes/FaceLandmarks68';\nimport { toNetInput } from '../dom';\nimport { FaceProcessor } from '../faceProcessor/FaceProcessor';\nimport { isEven } from '../utils';\nvar FaceLandmark68NetBase = /** @class */function (_super) {\n  __extends(FaceLandmark68NetBase, _super);\n  function FaceLandmark68NetBase() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  FaceLandmark68NetBase.prototype.postProcess = function (output, inputSize, originalDimensions) {\n    var inputDimensions = originalDimensions.map(function (_a) {\n      var width = _a.width,\n        height = _a.height;\n      var scale = inputSize / Math.max(height, width);\n      return {\n        width: width * scale,\n        height: height * scale\n      };\n    });\n    var batchSize = inputDimensions.length;\n    return tf.tidy(function () {\n      var createInterleavedTensor = function (fillX, fillY) {\n        return tf.stack([tf.fill([68], fillX), tf.fill([68], fillY)], 1).as2D(1, 136).as1D();\n      };\n      var getPadding = function (batchIdx, cond) {\n        var _a = inputDimensions[batchIdx],\n          width = _a.width,\n          height = _a.height;\n        return cond(width, height) ? Math.abs(width - height) / 2 : 0;\n      };\n      var getPaddingX = function (batchIdx) {\n        return getPadding(batchIdx, function (w, h) {\n          return w < h;\n        });\n      };\n      var getPaddingY = function (batchIdx) {\n        return getPadding(batchIdx, function (w, h) {\n          return h < w;\n        });\n      };\n      var landmarkTensors = output.mul(tf.fill([batchSize, 136], inputSize)).sub(tf.stack(Array.from(Array(batchSize), function (_, batchIdx) {\n        return createInterleavedTensor(getPaddingX(batchIdx), getPaddingY(batchIdx));\n      }))).div(tf.stack(Array.from(Array(batchSize), function (_, batchIdx) {\n        return createInterleavedTensor(inputDimensions[batchIdx].width, inputDimensions[batchIdx].height);\n      })));\n      return landmarkTensors;\n    });\n  };\n  FaceLandmark68NetBase.prototype.forwardInput = function (input) {\n    var _this = this;\n    return tf.tidy(function () {\n      var out = _this.runNet(input);\n      return _this.postProcess(out, input.inputSize, input.inputDimensions.map(function (_a) {\n        var height = _a[0],\n          width = _a[1];\n        return {\n          height: height,\n          width: width\n        };\n      }));\n    });\n  };\n  FaceLandmark68NetBase.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  FaceLandmark68NetBase.prototype.detectLandmarks = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var netInput, landmarkTensors, landmarksForBatch;\n      var _this = this;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            netInput = _a.sent();\n            landmarkTensors = tf.tidy(function () {\n              return tf.unstack(_this.forwardInput(netInput));\n            });\n            return [4 /*yield*/, Promise.all(landmarkTensors.map(function (landmarkTensor, batchIdx) {\n              return __awaiter(_this, void 0, void 0, function () {\n                var landmarksArray, _a, _b, xCoords, yCoords;\n                return __generator(this, function (_c) {\n                  switch (_c.label) {\n                    case 0:\n                      _b = (_a = Array).from;\n                      return [4 /*yield*/, landmarkTensor.data()];\n                    case 1:\n                      landmarksArray = _b.apply(_a, [_c.sent()]);\n                      xCoords = landmarksArray.filter(function (_, i) {\n                        return isEven(i);\n                      });\n                      yCoords = landmarksArray.filter(function (_, i) {\n                        return !isEven(i);\n                      });\n                      return [2 /*return*/, new FaceLandmarks68(Array(68).fill(0).map(function (_, i) {\n                        return new Point(xCoords[i], yCoords[i]);\n                      }), {\n                        height: netInput.getInputHeight(batchIdx),\n                        width: netInput.getInputWidth(batchIdx)\n                      })];\n                  }\n                });\n              });\n            }))];\n          case 2:\n            landmarksForBatch = _a.sent();\n            landmarkTensors.forEach(function (t) {\n              return t.dispose();\n            });\n            return [2 /*return*/, netInput.isBatchInput ? landmarksForBatch : landmarksForBatch[0]];\n        }\n      });\n    });\n  };\n  FaceLandmark68NetBase.prototype.getClassifierChannelsOut = function () {\n    return 136;\n  };\n  return FaceLandmark68NetBase;\n}(FaceProcessor);\nexport { FaceLandmark68NetBase };", "map": {"version": 3, "names": ["tf", "Point", "FaceLandmarks68", "toNetInput", "FaceProcessor", "isEven", "FaceLandmark68NetBase", "_super", "__extends", "prototype", "postProcess", "output", "inputSize", "originalDimensions", "inputDimensions", "map", "_a", "width", "height", "scale", "Math", "max", "batchSize", "length", "tidy", "createInterleavedTensor", "fillX", "fillY", "stack", "fill", "as2D", "as1D", "getPadding", "batchIdx", "cond", "abs", "getPaddingX", "w", "h", "getPaddingY", "landmarkTensors", "mul", "sub", "Array", "from", "_", "div", "forwardInput", "input", "_this", "out", "runNet", "forward", "apply", "_b", "sent", "detectLandmarks", "netInput", "unstack", "Promise", "all", "landmarkTensor", "__awaiter", "data", "landmarksArray", "_c", "xCoords", "filter", "i", "yCoords", "getInputHeight", "getInputWidth", "landmarksForBatch", "for<PERSON>ach", "t", "dispose", "isBatchInput", "getClassifierChannelsOut"], "sources": ["../../../src/faceLandmarkNet/FaceLandmark68NetBase.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAAsBC,KAAK,QAAQ,YAAY;AAC/C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAA8BC,UAAU,QAAQ,QAAQ;AAExD,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,MAAM,QAAQ,UAAU;AAEjC,IAAAC,qBAAA,0BAAAC,MAAA;EAGUC,SAAA,CAAAF,qBAAA,EAAAC,MAAA;EAHV,SAAAD,sBAAA;;EAiGA;EA5FSA,qBAAA,CAAAG,SAAA,CAAAC,WAAW,GAAlB,UAAmBC,MAAmB,EAAEC,SAAiB,EAAEC,kBAAiC;IAE1F,IAAMC,eAAe,GAAGD,kBAAkB,CAACE,GAAG,CAAC,UAACC,EAAiB;UAAfC,KAAA,GAAAD,EAAA,CAAAC,KAAK;QAAEC,MAAA,GAAAF,EAAA,CAAAE,MAAM;MAC7D,IAAMC,KAAK,GAAGP,SAAS,GAAGQ,IAAI,CAACC,GAAG,CAACH,MAAM,EAAED,KAAK,CAAC;MACjD,OAAO;QACLA,KAAK,EAAEA,KAAK,GAAGE,KAAK;QACpBD,MAAM,EAAEA,MAAM,GAAGC;OAClB;IACH,CAAC,CAAC;IAEF,IAAMG,SAAS,GAAGR,eAAe,CAACS,MAAM;IAExC,OAAOvB,EAAE,CAACwB,IAAI,CAAC;MACb,IAAMC,uBAAuB,GAAG,SAAAA,CAACC,KAAa,EAAEC,KAAa;QAC3D,OAAA3B,EAAE,CAAC4B,KAAK,CAAC,CACP5B,EAAE,CAAC6B,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEH,KAAK,CAAC,EACpB1B,EAAE,CAAC6B,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEF,KAAK,CAAC,CACrB,EAAE,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,IAAI,EAAE;MAHzB,CAGyB;MAE3B,IAAMC,UAAU,GAAG,SAAAA,CAACC,QAAgB,EAAEC,IAAuC;QACrE,IAAAlB,EAAA,GAAAF,eAAA,CAAAmB,QAAA,CAA6C;UAA3ChB,KAAA,GAAAD,EAAA,CAAAC,KAAK;UAAEC,MAAA,GAAAF,EAAA,CAAAE,MAAoC;QACnD,OAAOgB,IAAI,CAACjB,KAAK,EAAEC,MAAM,CAAC,GAAGE,IAAI,CAACe,GAAG,CAAClB,KAAK,GAAGC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;MAC/D,CAAC;MACD,IAAMkB,WAAW,GAAG,SAAAA,CAACH,QAAgB;QAAK,OAAAD,UAAU,CAACC,QAAQ,EAAE,UAACI,CAAC,EAAEC,CAAC;UAAK,OAAAD,CAAC,GAAGC,CAAC;QAAL,CAAK,CAAC;MAArC,CAAqC;MAC/E,IAAMC,WAAW,GAAG,SAAAA,CAACN,QAAgB;QAAK,OAAAD,UAAU,CAACC,QAAQ,EAAE,UAACI,CAAC,EAAEC,CAAC;UAAK,OAAAA,CAAC,GAAGD,CAAC;QAAL,CAAK,CAAC;MAArC,CAAqC;MAE/E,IAAMG,eAAe,GAAG7B,MAAM,CAC3B8B,GAAG,CAACzC,EAAE,CAAC6B,IAAI,CAAC,CAACP,SAAS,EAAE,GAAG,CAAC,EAAEV,SAAS,CAAC,CAAC,CACzC8B,GAAG,CAAC1C,EAAE,CAAC4B,KAAK,CAACe,KAAK,CAACC,IAAI,CAACD,KAAK,CAACrB,SAAS,CAAC,EAAE,UAACuB,CAAC,EAAEZ,QAAQ;QACrD,OAAAR,uBAAuB,CACrBW,WAAW,CAACH,QAAQ,CAAC,EACrBM,WAAW,CAACN,QAAQ,CAAC,CACtB;MAHD,CAGC,CACF,CAAC,CAAC,CACFa,GAAG,CAAC9C,EAAE,CAAC4B,KAAK,CAACe,KAAK,CAACC,IAAI,CAACD,KAAK,CAACrB,SAAS,CAAC,EAAE,UAACuB,CAAC,EAAEZ,QAAQ;QACrD,OAAAR,uBAAuB,CACrBX,eAAe,CAACmB,QAAQ,CAAC,CAAChB,KAAK,EAC/BH,eAAe,CAACmB,QAAQ,CAAC,CAACf,MAAM,CACjC;MAHD,CAGC,CACF,CAAC,CAAC;MAEL,OAAOsB,eAA8B;IACvC,CAAC,CAAC;EACJ,CAAC;EAEMlC,qBAAA,CAAAG,SAAA,CAAAsC,YAAY,GAAnB,UAAoBC,KAAe;IAAnC,IAAAC,KAAA;IACE,OAAOjD,EAAE,CAACwB,IAAI,CAAC;MACb,IAAM0B,GAAG,GAAGD,KAAI,CAACE,MAAM,CAACH,KAAK,CAAC;MAC9B,OAAOC,KAAI,CAACvC,WAAW,CACrBwC,GAAG,EACHF,KAAK,CAACpC,SAAmB,EACzBoC,KAAK,CAAClC,eAAe,CAACC,GAAG,CAAC,UAACC,EAAe;YAAdE,MAAA,GAAAF,EAAA,GAAM;UAAEC,KAAA,GAAAD,EAAA,GAAK;QAAM,OAAC;UAAEE,MAAM,EAAAA,MAAA;UAAED,KAAK,EAAAA;QAAA,CAAE;MAAlB,CAAmB,CAAC,CACpE;IACH,CAAC,CAAC;EACJ,CAAC;EAEYX,qBAAA,CAAAG,SAAA,CAAA2C,OAAO,GAApB,UAAqBJ,KAAgB;;;;;;YAC5BhC,EAAA,OAAI,CAAC+B,YAAY;YAAC,qBAAM5C,UAAU,CAAC6C,KAAK,CAAC;;YAAhD,sBAAOhC,EAAA,CAAAqC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAEYjD,qBAAA,CAAAG,SAAA,CAAA+C,eAAe,GAA5B,UAA6BR,KAAgB;;;;;;;YAC1B,qBAAM7C,UAAU,CAAC6C,KAAK,CAAC;;YAAlCS,QAAQ,GAAGzC,EAAA,CAAAuC,IAAA,EAAuB;YAClCf,eAAe,GAAGxC,EAAE,CAACwB,IAAI,CAC7B;cAAM,OAAAxB,EAAE,CAAC0D,OAAO,CAACT,KAAI,CAACF,YAAY,CAACU,QAAQ,CAAC,CAAC;YAAvC,CAAuC,CAC9C;YAEyB,qBAAME,OAAO,CAACC,GAAG,CAACpB,eAAe,CAACzB,GAAG,CAC7D,UAAO8C,cAAc,EAAE5B,QAAQ;cAAA,OAAA6B,SAAA,CAAAb,KAAA;;;;;sBACNK,EAAA,IAAAtC,EAAA,GAAA2B,KAAK,EAACC,IAAI;sBAAC,qBAAMiB,cAAc,CAACE,IAAI,EAAE;;sBAAvDC,cAAc,GAAGV,EAAA,CAAAD,KAAA,CAAArC,EAAA,GAAWiD,EAAA,CAAAV,IAAA,EAA2B,EAAC;sBACxDW,OAAO,GAAGF,cAAc,CAACG,MAAM,CAAC,UAACtB,CAAC,EAAEuB,CAAC;wBAAK,OAAA/D,MAAM,CAAC+D,CAAC,CAAC;sBAAT,CAAS,CAAC;sBACpDC,OAAO,GAAGL,cAAc,CAACG,MAAM,CAAC,UAACtB,CAAC,EAAEuB,CAAC;wBAAK,QAAC/D,MAAM,CAAC+D,CAAC,CAAC;sBAAV,CAAU,CAAC;sBAE3D,sBAAO,IAAIlE,eAAe,CACxByC,KAAK,CAAC,EAAE,CAAC,CAACd,IAAI,CAAC,CAAC,CAAC,CAACd,GAAG,CAAC,UAAC8B,CAAC,EAAEuB,CAAC;wBAAK,WAAInE,KAAK,CAACiE,OAAO,CAACE,CAAC,CAAC,EAAEC,OAAO,CAACD,CAAC,CAAC,CAAC;sBAAjC,CAAiC,CAAC,EAClE;wBACElD,MAAM,EAAEuC,QAAQ,CAACa,cAAc,CAACrC,QAAQ,CAAC;wBACzChB,KAAK,EAAGwC,QAAQ,CAACc,aAAa,CAACtC,QAAQ;uBACxC,CACF;;;;aACF,CACF,CAAC;;YAdIuC,iBAAiB,GAAGxD,EAAA,CAAAuC,IAAA,EAcxB;YAEFf,eAAe,CAACiC,OAAO,CAAC,UAAAC,CAAC;cAAI,OAAAA,CAAC,CAACC,OAAO,EAAE;YAAX,CAAW,CAAC;YAEzC,sBAAOlB,QAAQ,CAACmB,YAAY,GACxBJ,iBAAiB,GACjBA,iBAAiB,CAAC,CAAC,CAAC;;;;GACzB;EAESlE,qBAAA,CAAAG,SAAA,CAAAoE,wBAAwB,GAAlC;IACE,OAAO,GAAG;EACZ,CAAC;EACH,OAAAvE,qBAAC;AAAD,CAAC,CA9FSF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}