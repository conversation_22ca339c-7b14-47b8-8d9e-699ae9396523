{"ast": null, "code": "/**\n * Service Firebase simulé pour le développement local\n * Utilise localStorage pour simuler Firestore\n */\n\nimport { UserRole } from '../types';\nclass MockFirebaseService {\n  getFromStorage(key) {\n    const data = localStorage.getItem(`presencepro_${key}`);\n    return data ? JSON.parse(data) : [];\n  }\n  saveToStorage(key, data) {\n    localStorage.setItem(`presencepro_${key}`, JSON.stringify(data));\n  }\n  generateId() {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  async getUsers() {\n    // Simuler un délai réseau\n    await new Promise(resolve => setTimeout(resolve, 500));\n    let users = this.getFromStorage('users');\n\n    // Créer des données de test si vide\n    if (users.length === 0) {\n      users = [{\n        id: '1',\n        username: 'admin',\n        email: '<EMAIL>',\n        firstName: 'Admin',\n        lastName: 'System',\n        fullName: 'Admin System',\n        role: UserRole.ADMIN,\n        roleDisplay: 'Administrateur',\n        isActive: true,\n        dateJoined: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      }, {\n        id: '2',\n        username: 'prof.martin',\n        email: '<EMAIL>',\n        firstName: 'Jean',\n        lastName: 'Martin',\n        fullName: 'Jean Martin',\n        role: UserRole.TEACHER,\n        roleDisplay: 'Enseignant',\n        isActive: true,\n        dateJoined: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      }, {\n        id: '3',\n        username: 'marie.dubois',\n        email: '<EMAIL>',\n        firstName: 'Marie',\n        lastName: 'Dubois',\n        fullName: 'Marie Dubois',\n        role: UserRole.STUDENT,\n        roleDisplay: 'Étudiant',\n        isActive: true,\n        dateJoined: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      }];\n      this.saveToStorage('users', users);\n    }\n    return users;\n  }\n  async getUserById(userId) {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const users = await this.getUsers();\n    return users.find(user => user.id === userId) || null;\n  }\n  async createUser(userData) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const newUser = {\n      ...userData,\n      id: this.generateId(),\n      dateJoined: new Date().toISOString(),\n      lastLogin: new Date().toISOString()\n    };\n    users.push(newUser);\n    this.saveToStorage('users', users);\n    return newUser.id;\n  }\n  async updateUser(userId, userData) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const index = users.findIndex(user => user.id === userId);\n    if (index !== -1) {\n      users[index] = {\n        ...users[index],\n        ...userData\n      };\n      this.saveToStorage('users', users);\n    }\n  }\n  async deleteUser(userId) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const filteredUsers = users.filter(user => user.id !== userId);\n    this.saveToStorage('users', filteredUsers);\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  async getCourses() {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    let courses = this.getFromStorage('courses');\n\n    // Créer des données de test si vide\n    if (courses.length === 0) {\n      const users = await this.getUsers();\n      const teacher = users.find(u => u.role === UserRole.TEACHER);\n      if (teacher) {\n        courses = [{\n          id: '1',\n          name: 'Mathématiques Avancées',\n          code: 'MATH301',\n          description: 'Cours de mathématiques niveau avancé',\n          teacher: teacher,\n          studentGroup: {\n            id: '1',\n            name: 'L3 Informatique',\n            description: 'Licence 3 Informatique',\n            academicYear: '2023-2024',\n            level: 'L3',\n            specialization: 'Informatique',\n            studentCount: 25,\n            isActive: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          },\n          schedule: [{\n            id: '1',\n            dayOfWeek: 1,\n            // Lundi\n            startTime: '09:00',\n            endTime: '11:00',\n            room: 'A101',\n            building: 'Bâtiment A'\n          }],\n          academicYear: '2023-2024',\n          semester: 'S1',\n          credits: 6,\n          isActive: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }];\n        this.saveToStorage('courses', courses);\n      }\n    }\n    return courses;\n  }\n  async getCoursesByTeacher(teacherId) {\n    const courses = await this.getCourses();\n    return courses.filter(course => {\n      var _course$teacher;\n      return ((_course$teacher = course.teacher) === null || _course$teacher === void 0 ? void 0 : _course$teacher.id) === teacherId;\n    });\n  }\n  async createCourse(courseData) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const courses = await this.getCourses();\n    const newCourse = {\n      ...courseData,\n      id: this.generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    courses.push(newCourse);\n    this.saveToStorage('courses', courses);\n    return newCourse.id;\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  async recordAttendance(attendanceData) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const attendances = this.getFromStorage('attendance');\n    const newAttendance = {\n      ...attendanceData,\n      id: this.generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    attendances.push(newAttendance);\n    this.saveToStorage('attendance', attendances);\n    return newAttendance.id;\n  }\n  async getAttendanceByCourse(courseId, date) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const attendances = this.getFromStorage('attendance');\n    return attendances.filter(att => {\n      var _att$course;\n      const matchesCourse = ((_att$course = att.course) === null || _att$course === void 0 ? void 0 : _att$course.id) === courseId;\n      const matchesDate = !date || att.date === date;\n      return matchesCourse && matchesDate;\n    });\n  }\n  async getAttendanceByStudent(studentId) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const attendances = this.getFromStorage('attendance');\n    return attendances.filter(att => att.student.id === studentId);\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  async uploadProfileImage(userId, file) {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    // Simuler un URL d'image\n    return `https://mock-storage.presencepro.com/profile-images/${userId}/${file.name}`;\n  }\n  async uploadFaceImage(userId, file) {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    // Simuler un URL d'image\n    return `https://mock-storage.presencepro.com/face-images/${userId}/${Date.now()}_${file.name}`;\n  }\n  async deleteImage(imageUrl) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    // Simuler la suppression\n    console.log(`Image supprimée : ${imageUrl}`);\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  async getGlobalStats() {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const courses = await this.getCourses();\n    const attendances = this.getFromStorage('attendance');\n    return {\n      totalUsers: users.length,\n      totalStudents: users.filter(user => user.role === UserRole.STUDENT).length,\n      totalTeachers: users.filter(user => user.role === UserRole.TEACHER).length,\n      totalAdmins: users.filter(user => user.role === UserRole.ADMIN).length,\n      totalCourses: courses.length,\n      totalAttendances: attendances.length\n    };\n  }\n\n  // ==================== MÉTHODES UTILITAIRES ====================\n\n  async clearAllData() {\n    localStorage.removeItem('presencepro_users');\n    localStorage.removeItem('presencepro_courses');\n    localStorage.removeItem('presencepro_attendance');\n    console.log('Toutes les données simulées ont été effacées');\n  }\n  async seedTestData() {\n    await this.clearAllData();\n    await this.getUsers(); // Cela va créer les données de test\n    await this.getCourses(); // Cela va créer les données de test\n    console.log('Données de test créées');\n  }\n}\n\n// Instance singleton du service Firebase simulé\nexport const mockFirebaseService = new MockFirebaseService();\nexport default mockFirebaseService;", "map": {"version": 3, "names": ["UserRole", "MockFirebaseService", "getFromStorage", "key", "data", "localStorage", "getItem", "JSON", "parse", "saveToStorage", "setItem", "stringify", "generateId", "Date", "now", "toString", "Math", "random", "substr", "getUsers", "Promise", "resolve", "setTimeout", "users", "length", "id", "username", "email", "firstName", "lastName", "fullName", "role", "ADMIN", "roleDisplay", "isActive", "dateJoined", "toISOString", "lastLogin", "TEACHER", "STUDENT", "getUserById", "userId", "find", "user", "createUser", "userData", "newUser", "push", "updateUser", "index", "findIndex", "deleteUser", "filteredUsers", "filter", "getCourses", "courses", "teacher", "u", "name", "code", "description", "studentGroup", "academicYear", "level", "specialization", "studentCount", "createdAt", "updatedAt", "schedule", "dayOfWeek", "startTime", "endTime", "room", "building", "semester", "credits", "getCoursesByTeacher", "teacherId", "course", "_course$teacher", "createCourse", "courseData", "newCourse", "recordAttendance", "attendanceData", "attendances", "newAttendance", "getAttendanceByCourse", "courseId", "date", "att", "_att$course", "matchesCourse", "matchesDate", "getAttendanceByStudent", "studentId", "student", "uploadProfileImage", "file", "uploadFaceImage", "deleteImage", "imageUrl", "console", "log", "getGlobalStats", "totalUsers", "totalStudents", "totalTeachers", "totalAdmins", "totalCourses", "totalAttendances", "clearAllData", "removeItem", "seedTestData", "mockFirebaseService"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/services/mockFirebaseService.ts"], "sourcesContent": ["/**\n * Service Firebase simulé pour le développement local\n * Utilise localStorage pour simuler Firestore\n */\n\nimport { User, Course, AttendanceRecord, UserRole } from '../types';\n\nclass MockFirebaseService {\n  private getFromStorage<T>(key: string): T[] {\n    const data = localStorage.getItem(`presencepro_${key}`);\n    return data ? JSON.parse(data) : [];\n  }\n\n  private saveToStorage<T>(key: string, data: T[]): void {\n    localStorage.setItem(`presencepro_${key}`, JSON.stringify(data));\n  }\n\n  private generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  async getUsers(): Promise<User[]> {\n    // Simuler un délai r<PERSON>eau\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    let users = this.getFromStorage<User>('users');\n    \n    // Créer des données de test si vide\n    if (users.length === 0) {\n      users = [\n        {\n          id: '1',\n          username: 'admin',\n          email: '<EMAIL>',\n          firstName: 'Admin',\n          lastName: 'System',\n          fullName: 'Admin System',\n          role: UserRole.ADMIN,\n          roleDisplay: 'Administrateur',\n          isActive: true,\n          dateJoined: new Date().toISOString(),\n          lastLogin: new Date().toISOString()\n        },\n        {\n          id: '2',\n          username: 'prof.martin',\n          email: '<EMAIL>',\n          firstName: 'Jean',\n          lastName: 'Martin',\n          fullName: 'Jean Martin',\n          role: UserRole.TEACHER,\n          roleDisplay: 'Enseignant',\n          isActive: true,\n          dateJoined: new Date().toISOString(),\n          lastLogin: new Date().toISOString()\n        },\n        {\n          id: '3',\n          username: 'marie.dubois',\n          email: '<EMAIL>',\n          firstName: 'Marie',\n          lastName: 'Dubois',\n          fullName: 'Marie Dubois',\n          role: UserRole.STUDENT,\n          roleDisplay: 'Étudiant',\n          isActive: true,\n          dateJoined: new Date().toISOString(),\n          lastLogin: new Date().toISOString()\n        }\n      ];\n      this.saveToStorage('users', users);\n    }\n    \n    return users;\n  }\n\n  async getUserById(userId: string): Promise<User | null> {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const users = await this.getUsers();\n    return users.find(user => user.id === userId) || null;\n  }\n\n  async createUser(userData: Omit<User, 'id'>): Promise<string> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const newUser: User = {\n      ...userData,\n      id: this.generateId(),\n      dateJoined: new Date().toISOString(),\n      lastLogin: new Date().toISOString()\n    };\n    users.push(newUser);\n    this.saveToStorage('users', users);\n    return newUser.id;\n  }\n\n  async updateUser(userId: string, userData: Partial<User>): Promise<void> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const index = users.findIndex(user => user.id === userId);\n    if (index !== -1) {\n      users[index] = { ...users[index], ...userData };\n      this.saveToStorage('users', users);\n    }\n  }\n\n  async deleteUser(userId: string): Promise<void> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const users = await this.getUsers();\n    const filteredUsers = users.filter(user => user.id !== userId);\n    this.saveToStorage('users', filteredUsers);\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  async getCourses(): Promise<Course[]> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    let courses = this.getFromStorage<Course>('courses');\n    \n    // Créer des données de test si vide\n    if (courses.length === 0) {\n      const users = await this.getUsers();\n      const teacher = users.find(u => u.role === UserRole.TEACHER);\n      \n      if (teacher) {\n        courses = [\n          {\n            id: '1',\n            name: 'Mathématiques Avancées',\n            code: 'MATH301',\n            description: 'Cours de mathématiques niveau avancé',\n            teacher: teacher,\n            studentGroup: {\n              id: '1',\n              name: 'L3 Informatique',\n              description: 'Licence 3 Informatique',\n              academicYear: '2023-2024',\n              level: 'L3',\n              specialization: 'Informatique',\n              studentCount: 25,\n              isActive: true,\n              createdAt: new Date().toISOString(),\n              updatedAt: new Date().toISOString()\n            },\n            schedule: [\n              {\n                id: '1',\n                dayOfWeek: 1, // Lundi\n                startTime: '09:00',\n                endTime: '11:00',\n                room: 'A101',\n                building: 'Bâtiment A'\n              }\n            ],\n            academicYear: '2023-2024',\n            semester: 'S1',\n            credits: 6,\n            isActive: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          }\n        ];\n        this.saveToStorage('courses', courses);\n      }\n    }\n    \n    return courses;\n  }\n\n  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {\n    const courses = await this.getCourses();\n    return courses.filter(course => course.teacher?.id === teacherId);\n  }\n\n  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const courses = await this.getCourses();\n    const newCourse: Course = {\n      ...courseData,\n      id: this.generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    courses.push(newCourse);\n    this.saveToStorage('courses', courses);\n    return newCourse.id;\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const attendances = this.getFromStorage<AttendanceRecord>('attendance');\n    const newAttendance: AttendanceRecord = {\n      ...attendanceData,\n      id: this.generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    attendances.push(newAttendance);\n    this.saveToStorage('attendance', attendances);\n    return newAttendance.id;\n  }\n\n  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const attendances = this.getFromStorage<AttendanceRecord>('attendance');\n    return attendances.filter(att => {\n      const matchesCourse = att.course?.id === courseId;\n      const matchesDate = !date || att.date === date;\n      return matchesCourse && matchesDate;\n    });\n  }\n\n  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const attendances = this.getFromStorage<AttendanceRecord>('attendance');\n    return attendances.filter(att => att.student.id === studentId);\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  async uploadProfileImage(userId: string, file: File): Promise<string> {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    // Simuler un URL d'image\n    return `https://mock-storage.presencepro.com/profile-images/${userId}/${file.name}`;\n  }\n\n  async uploadFaceImage(userId: string, file: File): Promise<string> {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    // Simuler un URL d'image\n    return `https://mock-storage.presencepro.com/face-images/${userId}/${Date.now()}_${file.name}`;\n  }\n\n  async deleteImage(imageUrl: string): Promise<void> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    // Simuler la suppression\n    console.log(`Image supprimée : ${imageUrl}`);\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  async getGlobalStats(): Promise<any> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    const users = await this.getUsers();\n    const courses = await this.getCourses();\n    const attendances = this.getFromStorage<AttendanceRecord>('attendance');\n    \n    return {\n      totalUsers: users.length,\n      totalStudents: users.filter(user => user.role === UserRole.STUDENT).length,\n      totalTeachers: users.filter(user => user.role === UserRole.TEACHER).length,\n      totalAdmins: users.filter(user => user.role === UserRole.ADMIN).length,\n      totalCourses: courses.length,\n      totalAttendances: attendances.length\n    };\n  }\n\n  // ==================== MÉTHODES UTILITAIRES ====================\n\n  async clearAllData(): Promise<void> {\n    localStorage.removeItem('presencepro_users');\n    localStorage.removeItem('presencepro_courses');\n    localStorage.removeItem('presencepro_attendance');\n    console.log('Toutes les données simulées ont été effacées');\n  }\n\n  async seedTestData(): Promise<void> {\n    await this.clearAllData();\n    await this.getUsers(); // Cela va créer les données de test\n    await this.getCourses(); // Cela va créer les données de test\n    console.log('Données de test créées');\n  }\n}\n\n// Instance singleton du service Firebase simulé\nexport const mockFirebaseService = new MockFirebaseService();\nexport default mockFirebaseService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAAyCA,QAAQ,QAAQ,UAAU;AAEnE,MAAMC,mBAAmB,CAAC;EAChBC,cAAcA,CAAIC,GAAW,EAAO;IAC1C,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAeH,GAAG,EAAE,CAAC;IACvD,OAAOC,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,GAAG,EAAE;EACrC;EAEQK,aAAaA,CAAIN,GAAW,EAAEC,IAAS,EAAQ;IACrDC,YAAY,CAACK,OAAO,CAAC,eAAeP,GAAG,EAAE,EAAEI,IAAI,CAACI,SAAS,CAACP,IAAI,CAAC,CAAC;EAClE;EAEQQ,UAAUA,CAAA,EAAW;IAC3B,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACxE;;EAEA;;EAEA,MAAMC,QAAQA,CAAA,EAAoB;IAChC;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIE,KAAK,GAAG,IAAI,CAACrB,cAAc,CAAO,OAAO,CAAC;;IAE9C;IACA,IAAIqB,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtBD,KAAK,GAAG,CACN;QACEE,EAAE,EAAE,GAAG;QACPC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,uBAAuB;QAC9BC,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,cAAc;QACxBC,IAAI,EAAE/B,QAAQ,CAACgC,KAAK;QACpBC,WAAW,EAAE,gBAAgB;QAC7BC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;QACpCC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;MACpC,CAAC,EACD;QACEX,EAAE,EAAE,GAAG;QACPC,QAAQ,EAAE,aAAa;QACvBC,KAAK,EAAE,wBAAwB;QAC/BC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,aAAa;QACvBC,IAAI,EAAE/B,QAAQ,CAACsC,OAAO;QACtBL,WAAW,EAAE,YAAY;QACzBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;QACpCC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;MACpC,CAAC,EACD;QACEX,EAAE,EAAE,GAAG;QACPC,QAAQ,EAAE,cAAc;QACxBC,KAAK,EAAE,sCAAsC;QAC7CC,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,cAAc;QACxBC,IAAI,EAAE/B,QAAQ,CAACuC,OAAO;QACtBN,WAAW,EAAE,UAAU;QACvBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;QACpCC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;MACpC,CAAC,CACF;MACD,IAAI,CAAC3B,aAAa,CAAC,OAAO,EAAEc,KAAK,CAAC;IACpC;IAEA,OAAOA,KAAK;EACd;EAEA,MAAMiB,WAAWA,CAACC,MAAc,EAAwB;IACtD,MAAM,IAAIrB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAME,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACnC,OAAOI,KAAK,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKgB,MAAM,CAAC,IAAI,IAAI;EACvD;EAEA,MAAMG,UAAUA,CAACC,QAA0B,EAAmB;IAC5D,MAAM,IAAIzB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAME,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACnC,MAAM2B,OAAa,GAAG;MACpB,GAAGD,QAAQ;MACXpB,EAAE,EAAE,IAAI,CAACb,UAAU,CAAC,CAAC;MACrBuB,UAAU,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;MACpCC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;IACpC,CAAC;IACDb,KAAK,CAACwB,IAAI,CAACD,OAAO,CAAC;IACnB,IAAI,CAACrC,aAAa,CAAC,OAAO,EAAEc,KAAK,CAAC;IAClC,OAAOuB,OAAO,CAACrB,EAAE;EACnB;EAEA,MAAMuB,UAAUA,CAACP,MAAc,EAAEI,QAAuB,EAAiB;IACvE,MAAM,IAAIzB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAME,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACnC,MAAM8B,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACP,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKgB,MAAM,CAAC;IACzD,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB1B,KAAK,CAAC0B,KAAK,CAAC,GAAG;QAAE,GAAG1B,KAAK,CAAC0B,KAAK,CAAC;QAAE,GAAGJ;MAAS,CAAC;MAC/C,IAAI,CAACpC,aAAa,CAAC,OAAO,EAAEc,KAAK,CAAC;IACpC;EACF;EAEA,MAAM4B,UAAUA,CAACV,MAAc,EAAiB;IAC9C,MAAM,IAAIrB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAME,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACnC,MAAMiC,aAAa,GAAG7B,KAAK,CAAC8B,MAAM,CAACV,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKgB,MAAM,CAAC;IAC9D,IAAI,CAAChC,aAAa,CAAC,OAAO,EAAE2C,aAAa,CAAC;EAC5C;;EAEA;;EAEA,MAAME,UAAUA,CAAA,EAAsB;IACpC,MAAM,IAAIlC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIkC,OAAO,GAAG,IAAI,CAACrD,cAAc,CAAS,SAAS,CAAC;;IAEpD;IACA,IAAIqD,OAAO,CAAC/B,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMD,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;MACnC,MAAMqC,OAAO,GAAGjC,KAAK,CAACmB,IAAI,CAACe,CAAC,IAAIA,CAAC,CAAC1B,IAAI,KAAK/B,QAAQ,CAACsC,OAAO,CAAC;MAE5D,IAAIkB,OAAO,EAAE;QACXD,OAAO,GAAG,CACR;UACE9B,EAAE,EAAE,GAAG;UACPiC,IAAI,EAAE,wBAAwB;UAC9BC,IAAI,EAAE,SAAS;UACfC,WAAW,EAAE,sCAAsC;UACnDJ,OAAO,EAAEA,OAAO;UAChBK,YAAY,EAAE;YACZpC,EAAE,EAAE,GAAG;YACPiC,IAAI,EAAE,iBAAiB;YACvBE,WAAW,EAAE,wBAAwB;YACrCE,YAAY,EAAE,WAAW;YACzBC,KAAK,EAAE,IAAI;YACXC,cAAc,EAAE,cAAc;YAC9BC,YAAY,EAAE,EAAE;YAChB/B,QAAQ,EAAE,IAAI;YACdgC,SAAS,EAAE,IAAIrD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;YACnC+B,SAAS,EAAE,IAAItD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;UACpC,CAAC;UACDgC,QAAQ,EAAE,CACR;YACE3C,EAAE,EAAE,GAAG;YACP4C,SAAS,EAAE,CAAC;YAAE;YACdC,SAAS,EAAE,OAAO;YAClBC,OAAO,EAAE,OAAO;YAChBC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;UACZ,CAAC,CACF;UACDX,YAAY,EAAE,WAAW;UACzBY,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,CAAC;UACVzC,QAAQ,EAAE,IAAI;UACdgC,SAAS,EAAE,IAAIrD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;UACnC+B,SAAS,EAAE,IAAItD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;QACpC,CAAC,CACF;QACD,IAAI,CAAC3B,aAAa,CAAC,SAAS,EAAE8C,OAAO,CAAC;MACxC;IACF;IAEA,OAAOA,OAAO;EAChB;EAEA,MAAMqB,mBAAmBA,CAACC,SAAiB,EAAqB;IAC9D,MAAMtB,OAAO,GAAG,MAAM,IAAI,CAACD,UAAU,CAAC,CAAC;IACvC,OAAOC,OAAO,CAACF,MAAM,CAACyB,MAAM;MAAA,IAAAC,eAAA;MAAA,OAAI,EAAAA,eAAA,GAAAD,MAAM,CAACtB,OAAO,cAAAuB,eAAA,uBAAdA,eAAA,CAAgBtD,EAAE,MAAKoD,SAAS;IAAA,EAAC;EACnE;EAEA,MAAMG,YAAYA,CAACC,UAA8B,EAAmB;IAClE,MAAM,IAAI7D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMkC,OAAO,GAAG,MAAM,IAAI,CAACD,UAAU,CAAC,CAAC;IACvC,MAAM4B,SAAiB,GAAG;MACxB,GAAGD,UAAU;MACbxD,EAAE,EAAE,IAAI,CAACb,UAAU,CAAC,CAAC;MACrBsD,SAAS,EAAE,IAAIrD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;MACnC+B,SAAS,EAAE,IAAItD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;IACpC,CAAC;IACDmB,OAAO,CAACR,IAAI,CAACmC,SAAS,CAAC;IACvB,IAAI,CAACzE,aAAa,CAAC,SAAS,EAAE8C,OAAO,CAAC;IACtC,OAAO2B,SAAS,CAACzD,EAAE;EACrB;;EAEA;;EAEA,MAAM0D,gBAAgBA,CAACC,cAA4C,EAAmB;IACpF,MAAM,IAAIhE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMgE,WAAW,GAAG,IAAI,CAACnF,cAAc,CAAmB,YAAY,CAAC;IACvE,MAAMoF,aAA+B,GAAG;MACtC,GAAGF,cAAc;MACjB3D,EAAE,EAAE,IAAI,CAACb,UAAU,CAAC,CAAC;MACrBsD,SAAS,EAAE,IAAIrD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;MACnC+B,SAAS,EAAE,IAAItD,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC;IACpC,CAAC;IACDiD,WAAW,CAACtC,IAAI,CAACuC,aAAa,CAAC;IAC/B,IAAI,CAAC7E,aAAa,CAAC,YAAY,EAAE4E,WAAW,CAAC;IAC7C,OAAOC,aAAa,CAAC7D,EAAE;EACzB;EAEA,MAAM8D,qBAAqBA,CAACC,QAAgB,EAAEC,IAAa,EAA+B;IACxF,MAAM,IAAIrE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMgE,WAAW,GAAG,IAAI,CAACnF,cAAc,CAAmB,YAAY,CAAC;IACvE,OAAOmF,WAAW,CAAChC,MAAM,CAACqC,GAAG,IAAI;MAAA,IAAAC,WAAA;MAC/B,MAAMC,aAAa,GAAG,EAAAD,WAAA,GAAAD,GAAG,CAACZ,MAAM,cAAAa,WAAA,uBAAVA,WAAA,CAAYlE,EAAE,MAAK+D,QAAQ;MACjD,MAAMK,WAAW,GAAG,CAACJ,IAAI,IAAIC,GAAG,CAACD,IAAI,KAAKA,IAAI;MAC9C,OAAOG,aAAa,IAAIC,WAAW;IACrC,CAAC,CAAC;EACJ;EAEA,MAAMC,sBAAsBA,CAACC,SAAiB,EAA+B;IAC3E,MAAM,IAAI3E,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMgE,WAAW,GAAG,IAAI,CAACnF,cAAc,CAAmB,YAAY,CAAC;IACvE,OAAOmF,WAAW,CAAChC,MAAM,CAACqC,GAAG,IAAIA,GAAG,CAACM,OAAO,CAACvE,EAAE,KAAKsE,SAAS,CAAC;EAChE;;EAEA;;EAEA,MAAME,kBAAkBA,CAACxD,MAAc,EAAEyD,IAAU,EAAmB;IACpE,MAAM,IAAI9E,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACvD;IACA,OAAO,uDAAuDoB,MAAM,IAAIyD,IAAI,CAACxC,IAAI,EAAE;EACrF;EAEA,MAAMyC,eAAeA,CAAC1D,MAAc,EAAEyD,IAAU,EAAmB;IACjE,MAAM,IAAI9E,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACvD;IACA,OAAO,oDAAoDoB,MAAM,IAAI5B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIoF,IAAI,CAACxC,IAAI,EAAE;EAChG;EAEA,MAAM0C,WAAWA,CAACC,QAAgB,EAAiB;IACjD,MAAM,IAAIjF,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD;IACAiF,OAAO,CAACC,GAAG,CAAC,qBAAqBF,QAAQ,EAAE,CAAC;EAC9C;;EAEA;;EAEA,MAAMG,cAAcA,CAAA,EAAiB;IACnC,MAAM,IAAIpF,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAME,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACnC,MAAMoC,OAAO,GAAG,MAAM,IAAI,CAACD,UAAU,CAAC,CAAC;IACvC,MAAM+B,WAAW,GAAG,IAAI,CAACnF,cAAc,CAAmB,YAAY,CAAC;IAEvE,OAAO;MACLuG,UAAU,EAAElF,KAAK,CAACC,MAAM;MACxBkF,aAAa,EAAEnF,KAAK,CAAC8B,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACZ,IAAI,KAAK/B,QAAQ,CAACuC,OAAO,CAAC,CAACf,MAAM;MAC1EmF,aAAa,EAAEpF,KAAK,CAAC8B,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACZ,IAAI,KAAK/B,QAAQ,CAACsC,OAAO,CAAC,CAACd,MAAM;MAC1EoF,WAAW,EAAErF,KAAK,CAAC8B,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACZ,IAAI,KAAK/B,QAAQ,CAACgC,KAAK,CAAC,CAACR,MAAM;MACtEqF,YAAY,EAAEtD,OAAO,CAAC/B,MAAM;MAC5BsF,gBAAgB,EAAEzB,WAAW,CAAC7D;IAChC,CAAC;EACH;;EAEA;;EAEA,MAAMuF,YAAYA,CAAA,EAAkB;IAClC1G,YAAY,CAAC2G,UAAU,CAAC,mBAAmB,CAAC;IAC5C3G,YAAY,CAAC2G,UAAU,CAAC,qBAAqB,CAAC;IAC9C3G,YAAY,CAAC2G,UAAU,CAAC,wBAAwB,CAAC;IACjDV,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEA,MAAMU,YAAYA,CAAA,EAAkB;IAClC,MAAM,IAAI,CAACF,YAAY,CAAC,CAAC;IACzB,MAAM,IAAI,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,IAAI,CAACmC,UAAU,CAAC,CAAC,CAAC,CAAC;IACzBgD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC;AACF;;AAEA;AACA,OAAO,MAAMW,mBAAmB,GAAG,IAAIjH,mBAAmB,CAAC,CAAC;AAC5D,eAAeiH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}