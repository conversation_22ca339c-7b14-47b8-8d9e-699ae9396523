{"ast": null, "code": "import { sqrt } from \"../math.js\";\nconst sqrt3 = sqrt(3);\nexport default {\n  draw(context, size) {\n    const s = sqrt(size) * 0.6824;\n    const t = s / 2;\n    const u = s * sqrt3 / 2; // cos(Math.PI / 6)\n    context.moveTo(0, -s);\n    context.lineTo(u, t);\n    context.lineTo(-u, t);\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["sqrt", "sqrt3", "draw", "context", "size", "s", "t", "u", "moveTo", "lineTo", "closePath"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/d3-shape/src/symbol/triangle2.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const s = sqrt(size) * 0.6824;\n    const t = s  / 2;\n    const u = (s * sqrt3) / 2; // cos(Math.PI / 6)\n    context.moveTo(0, -s);\n    context.lineTo(u, t);\n    context.lineTo(-u, t);\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;AAE/B,MAAMC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;AAErB,eAAe;EACbE,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGL,IAAI,CAACI,IAAI,CAAC,GAAG,MAAM;IAC7B,MAAME,CAAC,GAAGD,CAAC,GAAI,CAAC;IAChB,MAAME,CAAC,GAAIF,CAAC,GAAGJ,KAAK,GAAI,CAAC,CAAC,CAAC;IAC3BE,OAAO,CAACK,MAAM,CAAC,CAAC,EAAE,CAACH,CAAC,CAAC;IACrBF,OAAO,CAACM,MAAM,CAACF,CAAC,EAAED,CAAC,CAAC;IACpBH,OAAO,CAACM,MAAM,CAAC,CAACF,CAAC,EAAED,CAAC,CAAC;IACrBH,OAAO,CAACO,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}