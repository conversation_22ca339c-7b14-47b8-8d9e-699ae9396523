{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { isValidProbablitiy } from '../utils';\nimport { LabeledBox } from './LabeledBox';\nvar PredictedBox = /** @class */function (_super) {\n  __extends(PredictedBox, _super);\n  function PredictedBox(box, label, score, classScore) {\n    var _this = _super.call(this, box, label) || this;\n    _this._score = score;\n    _this._classScore = classScore;\n    return _this;\n  }\n  PredictedBox.assertIsValidPredictedBox = function (box, callee) {\n    LabeledBox.assertIsValidLabeledBox(box, callee);\n    if (!isValidProbablitiy(box.score) || !isValidProbablitiy(box.classScore)) {\n      throw new Error(callee + \" - expected properties score (\" + box.score + \") and (\" + box.classScore + \") to be a number between [0, 1]\");\n    }\n  };\n  Object.defineProperty(PredictedBox.prototype, \"score\", {\n    get: function () {\n      return this._score;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(PredictedBox.prototype, \"classScore\", {\n    get: function () {\n      return this._classScore;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  return PredictedBox;\n}(LabeledBox);\nexport { PredictedBox };", "map": {"version": 3, "names": ["isValidProbablitiy", "LabeledBox", "PredictedBox", "_super", "__extends", "box", "label", "score", "classScore", "_this", "call", "_score", "_classScore", "assertIsValidPredictedBox", "callee", "assertIsValidLabeledBox", "Error", "Object", "defineProperty", "prototype", "get"], "sources": ["../../../src/classes/PredictedBox.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,kBAAkB,QAAQ,UAAU;AAE7C,SAASC,UAAU,QAAQ,cAAc;AAGzC,IAAAC,YAAA,0BAAAC,MAAA;EAAkCC,SAAA,CAAAF,YAAA,EAAAC,MAAA;EAgBhC,SAAAD,aAAYG,GAA+B,EAAEC,KAAa,EAAEC,KAAa,EAAEC,UAAkB;IAA7F,IAAAC,KAAA,GACEN,MAAA,CAAAO,IAAA,OAAML,GAAG,EAAEC,KAAK,CAAC;IACjBG,KAAI,CAACE,MAAM,GAAGJ,KAAK;IACnBE,KAAI,CAACG,WAAW,GAAGJ,UAAU;;EAC/B;EAlBcN,YAAA,CAAAW,yBAAyB,GAAvC,UAAwCR,GAAQ,EAAES,MAAc;IAC9Db,UAAU,CAACc,uBAAuB,CAACV,GAAG,EAAES,MAAM,CAAC;IAE/C,IACE,CAACd,kBAAkB,CAACK,GAAG,CAACE,KAAK,CAAC,IAC3B,CAACP,kBAAkB,CAACK,GAAG,CAACG,UAAU,CAAC,EACtC;MACA,MAAM,IAAIQ,KAAK,CAAIF,MAAM,sCAAiCT,GAAG,CAACE,KAAK,eAAUF,GAAG,CAACG,UAAU,oCAAiC,CAAC;;EAEjI,CAAC;EAWDS,MAAA,CAAAC,cAAA,CAAWhB,YAAA,CAAAiB,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACT,MAAM;IAAC,CAAC;;;;EACjDM,MAAA,CAAAC,cAAA,CAAWhB,YAAA,CAAAiB,SAAA,cAAU;SAArB,SAAAC,CAAA;MAAkC,OAAO,IAAI,CAACR,WAAW;IAAC,CAAC;;;;EAE7D,OAAAV,YAAC;AAAD,CAAC,CAzBiCD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}