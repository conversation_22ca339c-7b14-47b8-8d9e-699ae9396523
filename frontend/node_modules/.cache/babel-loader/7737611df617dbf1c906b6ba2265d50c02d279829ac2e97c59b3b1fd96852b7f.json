{"ast": null, "code": "var SsdMobilenetv1Options = /** @class */function () {\n  function SsdMobilenetv1Options(_a) {\n    var _b = _a === void 0 ? {} : _a,\n      minConfidence = _b.minConfidence,\n      maxResults = _b.maxResults;\n    this._name = 'SsdMobilenetv1Options';\n    this._minConfidence = minConfidence || 0.5;\n    this._maxResults = maxResults || 100;\n    if (typeof this._minConfidence !== 'number' || this._minConfidence <= 0 || this._minConfidence >= 1) {\n      throw new Error(this._name + \" - expected minConfidence to be a number between 0 and 1\");\n    }\n    if (typeof this._maxResults !== 'number') {\n      throw new Error(this._name + \" - expected maxResults to be a number\");\n    }\n  }\n  Object.defineProperty(SsdMobilenetv1Options.prototype, \"minConfidence\", {\n    get: function () {\n      return this._minConfidence;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(SsdMobilenetv1Options.prototype, \"maxResults\", {\n    get: function () {\n      return this._maxResults;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  return SsdMobilenetv1Options;\n}();\nexport { SsdMobilenetv1Options };", "map": {"version": 3, "names": ["SsdMobilenetv1Options", "_a", "_b", "minConfidence", "maxResults", "_name", "_minConfidence", "_maxResults", "Error", "Object", "defineProperty", "prototype", "get"], "sources": ["../../../src/ssdMobilenetv1/SsdMobilenetv1Options.ts"], "sourcesContent": [null], "mappings": "AAKA,IAAAA,qBAAA;EAME,SAAAA,sBAAYC,EAA0D;QAA1DC,EAAA,GAAAD,EAAA,mBAAAA,EAA0D;MAAxDE,aAAA,GAAAD,EAAA,CAAAC,aAAa;MAAEC,UAAA,GAAAF,EAAA,CAAAE,UAAU;IAL7B,KAAAC,KAAK,GAAW,uBAAuB;IAM/C,IAAI,CAACC,cAAc,GAAGH,aAAa,IAAI,GAAG;IAC1C,IAAI,CAACI,WAAW,GAAGH,UAAU,IAAI,GAAG;IAEpC,IAAI,OAAO,IAAI,CAACE,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACA,cAAc,IAAI,CAAC,IAAI,IAAI,CAACA,cAAc,IAAI,CAAC,EAAE;MACnG,MAAM,IAAIE,KAAK,CAAI,IAAI,CAACH,KAAK,6DAA0D,CAAC;;IAG1F,IAAI,OAAO,IAAI,CAACE,WAAW,KAAK,QAAQ,EAAE;MACxC,MAAM,IAAIC,KAAK,CAAI,IAAI,CAACH,KAAK,0CAAuC,CAAC;;EAEzE;EAEAI,MAAA,CAAAC,cAAA,CAAIV,qBAAA,CAAAW,SAAA,iBAAa;SAAjB,SAAAC,CAAA;MAA8B,OAAO,IAAI,CAACN,cAAc;IAAC,CAAC;;;;EAC1DG,MAAA,CAAAC,cAAA,CAAIV,qBAAA,CAAAW,SAAA,cAAU;SAAd,SAAAC,CAAA;MAA2B,OAAO,IAAI,CAACL,WAAW;IAAC,CAAC;;;;EACtD,OAAAP,qBAAC;AAAD,CAAC,CArBD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}