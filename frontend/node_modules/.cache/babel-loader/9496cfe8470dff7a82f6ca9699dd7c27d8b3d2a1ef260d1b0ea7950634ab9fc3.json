{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { FaceLandmark68Net } from './FaceLandmark68Net';\nexport * from './FaceLandmark68Net';\nexport * from './FaceLandmark68TinyNet';\nvar FaceLandmarkNet = /** @class */function (_super) {\n  __extends(FaceLandmarkNet, _super);\n  function FaceLandmarkNet() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return FaceLandmarkNet;\n}(FaceLandmark68Net);\nexport { FaceLandmarkNet };", "map": {"version": 3, "names": ["FaceLandmark68Net", "FaceLandmarkNet", "_super", "__extends"], "sources": ["../../../src/faceLandmarkNet/index.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,iBAAiB,QAAQ,qBAAqB;AAEvD,cAAc,qBAAqB;AACnC,cAAc,yBAAyB;AAEvC,IAAAC,eAAA,0BAAAC,MAAA;EAAqCC,SAAA,CAAAF,eAAA,EAAAC,MAAA;EAArC,SAAAD,gBAAA;;EAAwD;EAAA,OAAAA,eAAC;AAAD,CAAC,CAApBD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}