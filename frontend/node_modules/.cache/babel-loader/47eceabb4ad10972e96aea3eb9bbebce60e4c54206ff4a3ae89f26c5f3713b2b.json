{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { convLayer } from '../common';\nimport { fullyConnectedLayer } from '../common/fullyConnectedLayer';\nimport { prelu } from './prelu';\nimport { sharedLayer } from './sharedLayers';\nexport function ONet(x, params) {\n  return tf.tidy(function () {\n    var out = sharedLayer(x, params);\n    out = tf.maxPool(out, [2, 2], [2, 2], 'same');\n    out = convLayer(out, params.conv4, 'valid');\n    out = prelu(out, params.prelu4_alpha);\n    var vectorized = tf.reshape(out, [out.shape[0], params.fc1.weights.shape[0]]);\n    var fc1 = fullyConnectedLayer(vectorized, params.fc1);\n    var prelu5 = prelu(fc1, params.prelu5_alpha);\n    var fc2_1 = fullyConnectedLayer(prelu5, params.fc2_1);\n    var max = tf.expandDims(tf.max(fc2_1, 1), 1);\n    var prob = tf.softmax(tf.sub(fc2_1, max), 1);\n    var regions = fullyConnectedLayer(prelu5, params.fc2_2);\n    var points = fullyConnectedLayer(prelu5, params.fc2_3);\n    var scores = tf.unstack(prob, 1)[1];\n    return {\n      scores: scores,\n      regions: regions,\n      points: points\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "convLayer", "fullyConnectedLayer", "prelu", "<PERSON><PERSON><PERSON><PERSON>", "ONet", "x", "params", "tidy", "out", "maxPool", "conv4", "prelu4_alpha", "vectorized", "reshape", "shape", "fc1", "weights", "prelu5", "prelu5_alpha", "fc2_1", "max", "expandDims", "prob", "softmax", "sub", "regions", "fc2_2", "points", "fc2_3", "scores", "unstack"], "sources": ["../../../src/mtcnn/ONet.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,OAAM,SAAUC,IAAIA,CAACC,CAAc,EAAEC,MAAkB;EACrD,OAAOP,EAAE,CAACQ,IAAI,CAAC;IAEb,IAAIC,GAAG,GAAGL,WAAW,CAACE,CAAC,EAAEC,MAAM,CAAC;IAChCE,GAAG,GAAGT,EAAE,CAACU,OAAO,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC7CA,GAAG,GAAGR,SAAS,CAACQ,GAAG,EAAEF,MAAM,CAACI,KAAK,EAAE,OAAO,CAAC;IAC3CF,GAAG,GAAGN,KAAK,CAAcM,GAAG,EAAEF,MAAM,CAACK,YAAY,CAAC;IAElD,IAAMC,UAAU,GAAGb,EAAE,CAACc,OAAO,CAACL,GAAG,EAAE,CAACA,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,EAAER,MAAM,CAACS,GAAG,CAACC,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAgB;IAC9F,IAAMC,GAAG,GAAGd,mBAAmB,CAACW,UAAU,EAAEN,MAAM,CAACS,GAAG,CAAC;IACvD,IAAME,MAAM,GAAGf,KAAK,CAAca,GAAG,EAAET,MAAM,CAACY,YAAY,CAAC;IAC3D,IAAMC,KAAK,GAAGlB,mBAAmB,CAACgB,MAAM,EAAEX,MAAM,CAACa,KAAK,CAAC;IACvD,IAAMC,GAAG,GAAGrB,EAAE,CAACsB,UAAU,CAACtB,EAAE,CAACqB,GAAG,CAACD,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAE9C,IAAMG,IAAI,GAAGvB,EAAE,CAACwB,OAAO,CAACxB,EAAE,CAACyB,GAAG,CAACL,KAAK,EAAEC,GAAG,CAAC,EAAE,CAAC,CAAgB;IAC7D,IAAMK,OAAO,GAAGxB,mBAAmB,CAACgB,MAAM,EAAEX,MAAM,CAACoB,KAAK,CAAC;IACzD,IAAMC,MAAM,GAAG1B,mBAAmB,CAACgB,MAAM,EAAEX,MAAM,CAACsB,KAAK,CAAC;IAExD,IAAMC,MAAM,GAAG9B,EAAE,CAAC+B,OAAO,CAACR,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAgB;IACpD,OAAO;MAAEO,MAAM,EAAAA,MAAA;MAAEJ,OAAO,EAAAA,OAAA;MAAEE,MAAM,EAAAA;IAAA,CAAE;EACpC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}