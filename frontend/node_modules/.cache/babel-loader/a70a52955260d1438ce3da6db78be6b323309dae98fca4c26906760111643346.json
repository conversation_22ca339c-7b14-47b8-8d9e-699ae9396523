{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Card, CardContent, Stack, Button, Alert, Tabs, Tab, Box, Chip, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Face as FaceIcon, PhotoCamera as PhotoCameraIcon, Videocam as VideocamIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport FaceDetectionCamera from '../components/FaceRecognition/FaceDetectionCamera';\nimport FaceRegistration from '../components/FaceRecognition/FaceRegistration';\nimport { faceRecognitionService } from '../services/faceRecognitionService';\nimport { supabaseService } from '../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `face-tabpanel-${index}`,\n    \"aria-labelledby\": `face-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst FaceRecognitionTestPage = () => {\n  _s();\n  const [tabValue, setTabValue] = useState(0);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [systemStats, setSystemStats] = useState(faceRecognitionService.getStats());\n  const [settingsOpen, setSettingsOpen] = useState(false);\n\n  /**\n   * Charge les utilisateurs\n   */\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const usersData = await supabaseService.getUsers();\n      setUsers(usersData);\n\n      // Simuler un utilisateur connecté (premier étudiant)\n      const student = usersData.find(u => u.role === 'student');\n      if (student) {\n        setCurrentUser(student);\n      }\n    } catch (err) {\n      console.error('Erreur chargement utilisateurs:', err);\n      setError('Impossible de charger les utilisateurs');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gère les résultats de détection\n   */\n  const handleDetection = result => {\n    setDetectionResults(prev => [result, ...prev.slice(0, 9)]); // Garder les 10 derniers\n\n    if (result.detectedUsers.length > 0) {\n      setSuccess(`${result.detectedUsers.length} utilisateur(s) détecté(s)`);\n    }\n  };\n\n  /**\n   * Initialise le système de reconnaissance\n   */\n  const initializeSystem = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      await faceRecognitionService.initialize();\n      setSystemStats(faceRecognitionService.getStats());\n      setSuccess('Système de reconnaissance initialisé avec succès');\n    } catch (err) {\n      console.error('Erreur initialisation:', err);\n      setError('Erreur lors de l\\'initialisation du système');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Recharge les encodages\n   */\n  const reloadEncodings = async () => {\n    try {\n      setLoading(true);\n      await faceRecognitionService.loadFaceEncodings();\n      setSystemStats(faceRecognitionService.getStats());\n      setSuccess('Encodages rechargés avec succès');\n    } catch (err) {\n      console.error('Erreur rechargement:', err);\n      setError('Erreur lors du rechargement des encodages');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadUsers();\n    initializeSystem();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      children: \"Test du Syst\\xE8me de Reconnaissance Faciale\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Interface de test compl\\xE8te pour le syst\\xE8me de reconnaissance faciale de PresencePro. Testez l'enregistrement, la d\\xE9tection et la reconnaissance des visages.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setSuccess(null),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"\\xC9tat du Syst\\xE8me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 26\n            }, this),\n            onClick: () => setSettingsOpen(true),\n            children: \"Param\\xE8tres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: systemStats.isInitialized ? \"Initialisé\" : \"Non initialisé\",\n            color: systemStats.isInitialized ? \"success\" : \"error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${systemStats.registeredUsers} utilisateurs enregistrés`,\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${systemStats.totalEncodings} encodages`,\n            color: \"secondary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${users.length} utilisateurs total`,\n            color: \"info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: (_, newValue) => setTabValue(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Enregistrement\",\n          icon: /*#__PURE__*/_jsxDEV(PhotoCameraIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"D\\xE9tection\",\n          icon: /*#__PURE__*/_jsxDEV(VideocamIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"R\\xE9sultats\",\n          icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Enregistrement Facial\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), currentUser ? /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Utilisateur actuel: \", currentUser.firstName, \" \", currentUser.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Enregistrez vos photos faciales pour \\xEAtre reconnu automatiquement.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FaceRegistration, {\n              user: currentUser,\n              onRegistrationComplete: success => {\n                if (success) {\n                  reloadEncodings();\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Aucun utilisateur s\\xE9lectionn\\xE9. Connectez-vous pour enregistrer vos photos faciales.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"D\\xE9tection en Temps R\\xE9el\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FaceDetectionCamera, {\n          onDetection: handleDetection,\n          autoDetect: true,\n          showControls: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 2,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Historique des D\\xE9tections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), detectionResults.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Aucune d\\xE9tection enregistr\\xE9e. Utilisez l'onglet \\\"D\\xE9tection\\\" pour commencer.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: [\"D\\xE9tection #\", detectionResults.length - index]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: new Date().toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 2\n                },\n                children: result.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 21\n              }, this), result.detectedUsers.length > 0 && /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                spacing: 1,\n                flexWrap: \"wrap\",\n                children: result.detectedUsers.map((detection, userIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`,\n                  color: detection.confidence > 0.7 ? \"success\" : \"warning\",\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 35\n                  }, this)\n                }, userIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: settingsOpen,\n      onClose: () => setSettingsOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Param\\xE8tres du Syst\\xE8me\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 3,\n          sx: {\n            pt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"\\xC9tat du Syst\\xE8me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Mod\\xE8les charg\\xE9s:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this), \" \", systemStats.isInitialized ? 'Oui' : 'Non']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utilisateurs enregistr\\xE9s:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), \" \", systemStats.registeredUsers]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Total encodages:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), \" \", systemStats.totalEncodings]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: reloadEncodings,\n                disabled: loading,\n                children: \"Recharger les Encodages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: initializeSystem,\n                disabled: loading,\n                children: \"R\\xE9initialiser le Syst\\xE8me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Instructions:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 47\n              }, this), \"1. Enregistrez d'abord vos photos faciales\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 59\n              }, this), \"2. Testez la d\\xE9tection en temps r\\xE9el\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 53\n              }, this), \"3. V\\xE9rifiez les r\\xE9sultats dans l'historique\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setSettingsOpen(false),\n          children: \"Fermer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(FaceRecognitionTestPage, \"9wkyAmVfXr0oU6VMmTnGDrxOIPE=\");\n_c2 = FaceRecognitionTestPage;\nexport default FaceRecognitionTestPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"FaceRecognitionTestPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tabs", "Tab", "Box", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Face", "FaceIcon", "PhotoCamera", "PhotoCameraIcon", "Videocam", "VideocamIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "FaceDetectionCamera", "FaceRegistration", "faceRecognitionService", "supabaseService", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "FaceRecognitionTestPage", "_s", "tabValue", "setTabValue", "currentUser", "setCurrentUser", "users", "setUsers", "loading", "setLoading", "error", "setError", "success", "setSuccess", "detectionResults", "setDetectionResults", "systemStats", "setSystemStats", "getStats", "settingsOpen", "setSettingsOpen", "loadUsers", "usersData", "getUsers", "student", "find", "u", "err", "console", "handleDetection", "result", "prev", "slice", "detectedUsers", "length", "initializeSystem", "initialize", "reloadEncodings", "loadFaceEncodings", "max<PERSON><PERSON><PERSON>", "py", "variant", "gutterBottom", "color", "mb", "severity", "onClose", "display", "justifyContent", "alignItems", "startIcon", "onClick", "direction", "spacing", "flexWrap", "label", "isInitialized", "registeredUsers", "totalEncodings", "borderBottom", "borderColor", "onChange", "_", "newValue", "icon", "firstName", "lastName", "user", "onRegistrationComplete", "onDetection", "autoDetect", "showControls", "map", "Date", "toLocaleTimeString", "message", "detection", "userIndex", "Math", "round", "confidence", "size", "open", "fullWidth", "pt", "disabled", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/FaceRecognitionTestPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Con<PERSON><PERSON>,\n  <PERSON>po<PERSON>,\n  Card,\n  CardContent,\n  Stack,\n  Button,\n  Alert,\n  Tabs,\n  Tab,\n  Box,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  Face as FaceIcon,\n  PhotoCamera as PhotoCameraIcon,\n  Videocam as VideocamIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon\n} from '@mui/icons-material';\nimport FaceDetectionCamera from '../components/FaceRecognition/FaceDetectionCamera';\nimport FaceRegistration from '../components/FaceRecognition/FaceRegistration';\nimport { faceRecognitionService } from '../services/faceRecognitionService';\nimport { supabaseService } from '../services/supabaseService';\nimport { User, FaceDetectionResult } from '../types';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`face-tabpanel-${index}`}\n      aria-labelledby={`face-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst FaceRecognitionTestPage: React.FC = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [detectionResults, setDetectionResults] = useState<FaceDetectionResult[]>([]);\n  const [systemStats, setSystemStats] = useState(faceRecognitionService.getStats());\n  const [settingsOpen, setSettingsOpen] = useState(false);\n\n  /**\n   * Charge les utilisateurs\n   */\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const usersData = await supabaseService.getUsers();\n      setUsers(usersData);\n      \n      // Simuler un utilisateur connecté (premier étudiant)\n      const student = usersData.find(u => u.role === 'student');\n      if (student) {\n        setCurrentUser(student);\n      }\n    } catch (err) {\n      console.error('Erreur chargement utilisateurs:', err);\n      setError('Impossible de charger les utilisateurs');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gère les résultats de détection\n   */\n  const handleDetection = (result: FaceDetectionResult) => {\n    setDetectionResults(prev => [result, ...prev.slice(0, 9)]); // Garder les 10 derniers\n    \n    if (result.detectedUsers.length > 0) {\n      setSuccess(`${result.detectedUsers.length} utilisateur(s) détecté(s)`);\n    }\n  };\n\n  /**\n   * Initialise le système de reconnaissance\n   */\n  const initializeSystem = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      await faceRecognitionService.initialize();\n      setSystemStats(faceRecognitionService.getStats());\n      setSuccess('Système de reconnaissance initialisé avec succès');\n      \n    } catch (err) {\n      console.error('Erreur initialisation:', err);\n      setError('Erreur lors de l\\'initialisation du système');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Recharge les encodages\n   */\n  const reloadEncodings = async () => {\n    try {\n      setLoading(true);\n      await faceRecognitionService.loadFaceEncodings();\n      setSystemStats(faceRecognitionService.getStats());\n      setSuccess('Encodages rechargés avec succès');\n    } catch (err) {\n      console.error('Erreur rechargement:', err);\n      setError('Erreur lors du rechargement des encodages');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadUsers();\n    initializeSystem();\n  }, []);\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      <Typography variant=\"h3\" gutterBottom>\n        Test du Système de Reconnaissance Faciale\n      </Typography>\n\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Interface de test complète pour le système de reconnaissance faciale de PresencePro.\n        Testez l'enregistrement, la détection et la reconnaissance des visages.\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {success && (\n        <Alert severity=\"success\" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>\n          {success}\n        </Alert>\n      )}\n\n      {/* Statistiques du système */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n            <Typography variant=\"h6\">État du Système</Typography>\n            <Button\n              variant=\"outlined\"\n              startIcon={<SettingsIcon />}\n              onClick={() => setSettingsOpen(true)}\n            >\n              Paramètres\n            </Button>\n          </Box>\n          \n          <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\">\n            <Chip \n              label={systemStats.isInitialized ? \"Initialisé\" : \"Non initialisé\"} \n              color={systemStats.isInitialized ? \"success\" : \"error\"}\n            />\n            <Chip \n              label={`${systemStats.registeredUsers} utilisateurs enregistrés`} \n              color=\"primary\" \n            />\n            <Chip \n              label={`${systemStats.totalEncodings} encodages`} \n              color=\"secondary\" \n            />\n            <Chip \n              label={`${users.length} utilisateurs total`} \n              color=\"info\" \n            />\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* Onglets */}\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>\n        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>\n          <Tab label=\"Enregistrement\" icon={<PhotoCameraIcon />} />\n          <Tab label=\"Détection\" icon={<VideocamIcon />} />\n          <Tab label=\"Résultats\" icon={<AnalyticsIcon />} />\n        </Tabs>\n      </Box>\n\n      {/* Contenu des onglets */}\n      <TabPanel value={tabValue} index={0}>\n        {/* Enregistrement facial */}\n        <Stack spacing={3}>\n          <Typography variant=\"h6\">Enregistrement Facial</Typography>\n          \n          {currentUser ? (\n            <Card>\n              <CardContent>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Utilisateur actuel: {currentUser.firstName} {currentUser.lastName}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                  Enregistrez vos photos faciales pour être reconnu automatiquement.\n                </Typography>\n                \n                <FaceRegistration\n                  user={currentUser}\n                  onRegistrationComplete={(success) => {\n                    if (success) {\n                      reloadEncodings();\n                    }\n                  }}\n                />\n              </CardContent>\n            </Card>\n          ) : (\n            <Alert severity=\"info\">\n              Aucun utilisateur sélectionné. Connectez-vous pour enregistrer vos photos faciales.\n            </Alert>\n          )}\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={1}>\n        {/* Détection en temps réel */}\n        <Stack spacing={3}>\n          <Typography variant=\"h6\">Détection en Temps Réel</Typography>\n          \n          <FaceDetectionCamera\n            onDetection={handleDetection}\n            autoDetect={true}\n            showControls={true}\n          />\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={2}>\n        {/* Résultats et historique */}\n        <Stack spacing={3}>\n          <Typography variant=\"h6\">Historique des Détections</Typography>\n          \n          {detectionResults.length === 0 ? (\n            <Alert severity=\"info\">\n              Aucune détection enregistrée. Utilisez l'onglet \"Détection\" pour commencer.\n            </Alert>\n          ) : (\n            <Stack spacing={2}>\n              {detectionResults.map((result, index) => (\n                <Card key={index}>\n                  <CardContent>\n                    <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                      <Typography variant=\"subtitle2\">\n                        Détection #{detectionResults.length - index}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {new Date().toLocaleTimeString()}\n                      </Typography>\n                    </Box>\n                    \n                    <Typography variant=\"body2\" sx={{ mb: 2 }}>\n                      {result.message}\n                    </Typography>\n                    \n                    {result.detectedUsers.length > 0 && (\n                      <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\n                        {result.detectedUsers.map((detection, userIndex) => (\n                          <Chip\n                            key={userIndex}\n                            label={`${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`}\n                            color={detection.confidence > 0.7 ? \"success\" : \"warning\"}\n                            size=\"small\"\n                            icon={<FaceIcon />}\n                          />\n                        ))}\n                      </Stack>\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </Stack>\n          )}\n        </Stack>\n      </TabPanel>\n\n      {/* Dialog des paramètres */}\n      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Paramètres du Système</DialogTitle>\n        <DialogContent>\n          <Stack spacing={3} sx={{ pt: 1 }}>\n            <Box>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                État du Système\n              </Typography>\n              <Stack spacing={1}>\n                <Typography variant=\"body2\">\n                  <strong>Modèles chargés:</strong> {systemStats.isInitialized ? 'Oui' : 'Non'}\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Utilisateurs enregistrés:</strong> {systemStats.registeredUsers}\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Total encodages:</strong> {systemStats.totalEncodings}\n                </Typography>\n              </Stack>\n            </Box>\n\n            <Box>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Actions\n              </Typography>\n              <Stack spacing={2}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={reloadEncodings}\n                  disabled={loading}\n                >\n                  Recharger les Encodages\n                </Button>\n                <Button\n                  variant=\"outlined\"\n                  onClick={initializeSystem}\n                  disabled={loading}\n                >\n                  Réinitialiser le Système\n                </Button>\n              </Stack>\n            </Box>\n\n            <Alert severity=\"info\">\n              <Typography variant=\"body2\">\n                <strong>Instructions:</strong><br />\n                1. Enregistrez d'abord vos photos faciales<br />\n                2. Testez la détection en temps réel<br />\n                3. Vérifiez les résultats dans l'historique\n              </Typography>\n            </Alert>\n          </Stack>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setSettingsOpen(false)}>Fermer</Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default FaceRecognitionTestPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,mBAAmB,MAAM,mDAAmD;AACnF,OAAOC,gBAAgB,MAAM,gDAAgD;AAC7E,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS9D,SAASC,QAAQA,CAACC,KAAoB,EAAE;EACtC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAClD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,iBAAiBJ,KAAK,EAAG;IAC7B,mBAAiB,YAAYA,KAAK,EAAG;IAAA,GACjCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAACrB,GAAG;MAAC+B,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAbQf,QAAQ;AAejB,MAAMgB,uBAAiC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAwB,EAAE,CAAC;EACnF,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC6B,sBAAsB,CAACsC,QAAQ,CAAC,CAAC,CAAC;EACjF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAEvD;AACF;AACA;EACE,MAAMsE,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,SAAS,GAAG,MAAMzC,eAAe,CAAC0C,QAAQ,CAAC,CAAC;MAClDhB,QAAQ,CAACe,SAAS,CAAC;;MAEnB;MACA,MAAME,OAAO,GAAGF,SAAS,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpC,IAAI,KAAK,SAAS,CAAC;MACzD,IAAIkC,OAAO,EAAE;QACXnB,cAAc,CAACmB,OAAO,CAAC;MACzB;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,iCAAiC,EAAEiB,GAAG,CAAC;MACrDhB,QAAQ,CAAC,wCAAwC,CAAC;IACpD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMoB,eAAe,GAAIC,MAA2B,IAAK;IACvDf,mBAAmB,CAACgB,IAAI,IAAI,CAACD,MAAM,EAAE,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5D,IAAIF,MAAM,CAACG,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACnCrB,UAAU,CAAC,GAAGiB,MAAM,CAACG,aAAa,CAACC,MAAM,4BAA4B,CAAC;IACxE;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM/B,sBAAsB,CAACwD,UAAU,CAAC,CAAC;MACzCnB,cAAc,CAACrC,sBAAsB,CAACsC,QAAQ,CAAC,CAAC,CAAC;MACjDL,UAAU,CAAC,kDAAkD,CAAC;IAEhE,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEiB,GAAG,CAAC;MAC5ChB,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM4B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM7B,sBAAsB,CAAC0D,iBAAiB,CAAC,CAAC;MAChDrB,cAAc,CAACrC,sBAAsB,CAACsC,QAAQ,CAAC,CAAC,CAAC;MACjDL,UAAU,CAAC,iCAAiC,CAAC;IAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,sBAAsB,EAAEiB,GAAG,CAAC;MAC1ChB,QAAQ,CAAC,2CAA2C,CAAC;IACvD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAzD,SAAS,CAAC,MAAM;IACdqE,SAAS,CAAC,CAAC;IACXc,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEpD,OAAA,CAAC9B,SAAS;IAACsF,QAAQ,EAAC,IAAI;IAAC9C,EAAE,EAAE;MAAE+C,EAAE,EAAE;IAAE,CAAE;IAAAtD,QAAA,gBACrCH,OAAA,CAAC7B,UAAU;MAACuF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAxD,QAAA,EAAC;IAEtC;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbf,OAAA,CAAC7B,UAAU;MAACuF,OAAO,EAAC,OAAO;MAACE,KAAK,EAAC,gBAAgB;MAAClD,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAAA1D,QAAA,EAAC;IAGlE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZY,KAAK,iBACJ3B,OAAA,CAACxB,KAAK;MAACsF,QAAQ,EAAC,OAAO;MAACpD,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAACE,OAAO,EAAEA,CAAA,KAAMnC,QAAQ,CAAC,IAAI,CAAE;MAAAzB,QAAA,EAClEwB;IAAK;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAc,OAAO,iBACN7B,OAAA,CAACxB,KAAK;MAACsF,QAAQ,EAAC,SAAS;MAACpD,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAACE,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAAC,IAAI,CAAE;MAAA3B,QAAA,EACtE0B;IAAO;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDf,OAAA,CAAC5B,IAAI;MAACsC,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAAA1D,QAAA,eAClBH,OAAA,CAAC3B,WAAW;QAAA8B,QAAA,gBACVH,OAAA,CAACrB,GAAG;UAACqF,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACL,EAAE,EAAE,CAAE;UAAA1D,QAAA,gBAC3EH,OAAA,CAAC7B,UAAU;YAACuF,OAAO,EAAC,IAAI;YAAAvD,QAAA,EAAC;UAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDf,OAAA,CAACzB,MAAM;YACLmF,OAAO,EAAC,UAAU;YAClBS,SAAS,eAAEnE,OAAA,CAACN,YAAY;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BqD,OAAO,EAAEA,CAAA,KAAM/B,eAAe,CAAC,IAAI,CAAE;YAAAlC,QAAA,EACtC;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENf,OAAA,CAAC1B,KAAK;UAAC+F,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAACC,QAAQ,EAAC,MAAM;UAAApE,QAAA,gBAChDH,OAAA,CAACpB,IAAI;YACH4F,KAAK,EAAEvC,WAAW,CAACwC,aAAa,GAAG,YAAY,GAAG,gBAAiB;YACnEb,KAAK,EAAE3B,WAAW,CAACwC,aAAa,GAAG,SAAS,GAAG;UAAQ;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACFf,OAAA,CAACpB,IAAI;YACH4F,KAAK,EAAE,GAAGvC,WAAW,CAACyC,eAAe,2BAA4B;YACjEd,KAAK,EAAC;UAAS;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFf,OAAA,CAACpB,IAAI;YACH4F,KAAK,EAAE,GAAGvC,WAAW,CAAC0C,cAAc,YAAa;YACjDf,KAAK,EAAC;UAAW;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACFf,OAAA,CAACpB,IAAI;YACH4F,KAAK,EAAE,GAAGjD,KAAK,CAAC4B,MAAM,qBAAsB;YAC5CS,KAAK,EAAC;UAAM;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPf,OAAA,CAACrB,GAAG;MAAC+B,EAAE,EAAE;QAAEkE,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEhB,EAAE,EAAE;MAAE,CAAE;MAAA1D,QAAA,eAC1DH,OAAA,CAACvB,IAAI;QAAC2B,KAAK,EAAEe,QAAS;QAAC2D,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAK5D,WAAW,CAAC4D,QAAQ,CAAE;QAAA7E,QAAA,gBACtEH,OAAA,CAACtB,GAAG;UAAC8F,KAAK,EAAC,gBAAgB;UAACS,IAAI,eAAEjF,OAAA,CAACZ,eAAe;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDf,OAAA,CAACtB,GAAG;UAAC8F,KAAK,EAAC,cAAW;UAACS,IAAI,eAAEjF,OAAA,CAACV,YAAY;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDf,OAAA,CAACtB,GAAG;UAAC8F,KAAK,EAAC,cAAW;UAACS,IAAI,eAAEjF,OAAA,CAACR,aAAa;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAAC1B,KAAK;QAACgG,OAAO,EAAE,CAAE;QAAAnE,QAAA,gBAChBH,OAAA,CAAC7B,UAAU;UAACuF,OAAO,EAAC,IAAI;UAAAvD,QAAA,EAAC;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAE1DM,WAAW,gBACVrB,OAAA,CAAC5B,IAAI;UAAA+B,QAAA,eACHH,OAAA,CAAC3B,WAAW;YAAA8B,QAAA,gBACVH,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAxD,QAAA,GAAC,sBACvB,EAACkB,WAAW,CAAC6D,SAAS,EAAC,GAAC,EAAC7D,WAAW,CAAC8D,QAAQ;YAAA;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACbf,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAClD,EAAE,EAAE;gBAAEmD,EAAE,EAAE;cAAE,CAAE;cAAA1D,QAAA,EAAC;YAElE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbf,OAAA,CAACJ,gBAAgB;cACfwF,IAAI,EAAE/D,WAAY;cAClBgE,sBAAsB,EAAGxD,OAAO,IAAK;gBACnC,IAAIA,OAAO,EAAE;kBACXyB,eAAe,CAAC,CAAC;gBACnB;cACF;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAEPf,OAAA,CAACxB,KAAK;UAACsF,QAAQ,EAAC,MAAM;UAAA3D,QAAA,EAAC;QAEvB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAAC1B,KAAK;QAACgG,OAAO,EAAE,CAAE;QAAAnE,QAAA,gBAChBH,OAAA,CAAC7B,UAAU;UAACuF,OAAO,EAAC,IAAI;UAAAvD,QAAA,EAAC;QAAuB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAE7Df,OAAA,CAACL,mBAAmB;UAClB2F,WAAW,EAAExC,eAAgB;UAC7ByC,UAAU,EAAE,IAAK;UACjBC,YAAY,EAAE;QAAK;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAAC1B,KAAK;QAACgG,OAAO,EAAE,CAAE;QAAAnE,QAAA,gBAChBH,OAAA,CAAC7B,UAAU;UAACuF,OAAO,EAAC,IAAI;UAAAvD,QAAA,EAAC;QAAyB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAE9DgB,gBAAgB,CAACoB,MAAM,KAAK,CAAC,gBAC5BnD,OAAA,CAACxB,KAAK;UAACsF,QAAQ,EAAC,MAAM;UAAA3D,QAAA,EAAC;QAEvB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERf,OAAA,CAAC1B,KAAK;UAACgG,OAAO,EAAE,CAAE;UAAAnE,QAAA,EACf4B,gBAAgB,CAAC0D,GAAG,CAAC,CAAC1C,MAAM,EAAE1C,KAAK,kBAClCL,OAAA,CAAC5B,IAAI;YAAA+B,QAAA,eACHH,OAAA,CAAC3B,WAAW;cAAA8B,QAAA,gBACVH,OAAA,CAACrB,GAAG;gBAACqF,OAAO,EAAC,MAAM;gBAACC,cAAc,EAAC,eAAe;gBAACC,UAAU,EAAC,QAAQ;gBAACL,EAAE,EAAE,CAAE;gBAAA1D,QAAA,gBAC3EH,OAAA,CAAC7B,UAAU;kBAACuF,OAAO,EAAC,WAAW;kBAAAvD,QAAA,GAAC,gBACnB,EAAC4B,gBAAgB,CAACoB,MAAM,GAAG9C,KAAK;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbf,OAAA,CAAC7B,UAAU;kBAACuF,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,gBAAgB;kBAAAzD,QAAA,EACjD,IAAIuF,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENf,OAAA,CAAC7B,UAAU;gBAACuF,OAAO,EAAC,OAAO;gBAAChD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBAAA1D,QAAA,EACvC4C,MAAM,CAAC6C;cAAO;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAEZgC,MAAM,CAACG,aAAa,CAACC,MAAM,GAAG,CAAC,iBAC9BnD,OAAA,CAAC1B,KAAK;gBAAC+F,SAAS,EAAC,KAAK;gBAACC,OAAO,EAAE,CAAE;gBAACC,QAAQ,EAAC,MAAM;gBAAApE,QAAA,EAC/C4C,MAAM,CAACG,aAAa,CAACuC,GAAG,CAAC,CAACI,SAAS,EAAEC,SAAS,kBAC7C9F,OAAA,CAACpB,IAAI;kBAEH4F,KAAK,EAAE,GAAGqB,SAAS,CAACT,IAAI,CAACF,SAAS,IAAIW,SAAS,CAACT,IAAI,CAACD,QAAQ,KAAKY,IAAI,CAACC,KAAK,CAACH,SAAS,CAACI,UAAU,GAAG,GAAG,CAAC,IAAK;kBAC7GrC,KAAK,EAAEiC,SAAS,CAACI,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAU;kBAC1DC,IAAI,EAAC,OAAO;kBACZjB,IAAI,eAAEjF,OAAA,CAACd,QAAQ;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE,GAJd+E,SAAS;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKf,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC,GA5BLV,KAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXf,OAAA,CAACnB,MAAM;MAACsH,IAAI,EAAE/D,YAAa;MAAC2B,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAC,KAAK,CAAE;MAACmB,QAAQ,EAAC,IAAI;MAAC4C,SAAS;MAAAjG,QAAA,gBACxFH,OAAA,CAAClB,WAAW;QAAAqB,QAAA,EAAC;MAAqB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChDf,OAAA,CAACjB,aAAa;QAAAoB,QAAA,eACZH,OAAA,CAAC1B,KAAK;UAACgG,OAAO,EAAE,CAAE;UAAC5D,EAAE,EAAE;YAAE2F,EAAE,EAAE;UAAE,CAAE;UAAAlG,QAAA,gBAC/BH,OAAA,CAACrB,GAAG;YAAAwB,QAAA,gBACFH,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAxD,QAAA,EAAC;YAE7C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,KAAK;cAACgG,OAAO,EAAE,CAAE;cAAAnE,QAAA,gBAChBH,OAAA,CAAC7B,UAAU;gBAACuF,OAAO,EAAC,OAAO;gBAAAvD,QAAA,gBACzBH,OAAA;kBAAAG,QAAA,EAAQ;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkB,WAAW,CAACwC,aAAa,GAAG,KAAK,GAAG,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACbf,OAAA,CAAC7B,UAAU;gBAACuF,OAAO,EAAC,OAAO;gBAAAvD,QAAA,gBACzBH,OAAA;kBAAAG,QAAA,EAAQ;gBAAyB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkB,WAAW,CAACyC,eAAe;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACbf,OAAA,CAAC7B,UAAU;gBAACuF,OAAO,EAAC,OAAO;gBAAAvD,QAAA,gBACzBH,OAAA;kBAAAG,QAAA,EAAQ;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkB,WAAW,CAAC0C,cAAc;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENf,OAAA,CAACrB,GAAG;YAAAwB,QAAA,gBACFH,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAxD,QAAA,EAAC;YAE7C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,KAAK;cAACgG,OAAO,EAAE,CAAE;cAAAnE,QAAA,gBAChBH,OAAA,CAACzB,MAAM;gBACLmF,OAAO,EAAC,UAAU;gBAClBU,OAAO,EAAEd,eAAgB;gBACzBgD,QAAQ,EAAE7E,OAAQ;gBAAAtB,QAAA,EACnB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTf,OAAA,CAACzB,MAAM;gBACLmF,OAAO,EAAC,UAAU;gBAClBU,OAAO,EAAEhB,gBAAiB;gBAC1BkD,QAAQ,EAAE7E,OAAQ;gBAAAtB,QAAA,EACnB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENf,OAAA,CAACxB,KAAK;YAACsF,QAAQ,EAAC,MAAM;YAAA3D,QAAA,eACpBH,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAAAvD,QAAA,gBACzBH,OAAA;gBAAAG,QAAA,EAAQ;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAf,OAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,8CACM,eAAAf,OAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,8CACZ,eAAAf,OAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,qDAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChBf,OAAA,CAAChB,aAAa;QAAAmB,QAAA,eACZH,OAAA,CAACzB,MAAM;UAAC6F,OAAO,EAAEA,CAAA,KAAM/B,eAAe,CAAC,KAAK,CAAE;UAAAlC,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACG,EAAA,CArTID,uBAAiC;AAAAsF,GAAA,GAAjCtF,uBAAiC;AAuTvC,eAAeA,uBAAuB;AAAC,IAAAD,EAAA,EAAAuF,GAAA;AAAAC,YAAA,CAAAxF,EAAA;AAAAwF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}