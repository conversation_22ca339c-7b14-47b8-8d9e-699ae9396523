{"ast": null, "code": "export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;", "map": {"version": 3, "names": ["durationSecond", "durationMinute", "durationHour", "durationDay", "durationWeek", "durationMonth", "durationYear"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/d3-time/src/duration.js"], "sourcesContent": ["export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAG,IAAI;AAClC,OAAO,MAAMC,cAAc,GAAGD,cAAc,GAAG,EAAE;AACjD,OAAO,MAAME,YAAY,GAAGD,cAAc,GAAG,EAAE;AAC/C,OAAO,MAAME,WAAW,GAAGD,YAAY,GAAG,EAAE;AAC5C,OAAO,MAAME,YAAY,GAAGD,WAAW,GAAG,CAAC;AAC3C,OAAO,MAAME,aAAa,GAAGF,WAAW,GAAG,EAAE;AAC7C,OAAO,MAAMG,YAAY,GAAGH,WAAW,GAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}