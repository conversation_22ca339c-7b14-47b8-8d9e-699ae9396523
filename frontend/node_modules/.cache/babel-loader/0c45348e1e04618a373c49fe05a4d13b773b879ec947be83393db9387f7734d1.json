{"ast": null, "code": "import { disposeUnusedWeightTensors, extractWeightEntryFactory } from '../common';\nexport function extractParamsFromWeigthMap(weightMap) {\n  var paramMappings = [];\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  function extractFcParams(prefix) {\n    var weights = extractWeightEntry(prefix + \"/weights\", 2);\n    var bias = extractWeightEntry(prefix + \"/bias\", 1);\n    return {\n      weights: weights,\n      bias: bias\n    };\n  }\n  var params = {\n    fc: {\n      age: extractFcParams('fc/age'),\n      gender: extractFcParams('fc/gender')\n    }\n  };\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "extractWeightEntryFactory", "extractParamsFromWeigthMap", "weightMap", "paramMappings", "extractWeightEntry", "extractFcParams", "prefix", "weights", "bias", "params", "fc", "age", "gender"], "sources": ["../../../src/ageGenderNet/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,0BAA0B,EAAEC,yBAAyB,QAAgC,WAAW;AAGzG,OAAM,SAAUC,0BAA0BA,CACxCC,SAA4B;EAG5B,IAAMC,aAAa,GAAmB,EAAE;EAExC,IAAMC,kBAAkB,GAAGJ,yBAAyB,CAACE,SAAS,EAAEC,aAAa,CAAC;EAE9E,SAASE,eAAeA,CAACC,MAAc;IACrC,IAAMC,OAAO,GAAGH,kBAAkB,CAAiBE,MAAM,aAAU,EAAE,CAAC,CAAC;IACvE,IAAME,IAAI,GAAGJ,kBAAkB,CAAiBE,MAAM,UAAO,EAAE,CAAC,CAAC;IACjE,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;EAC1B;EAEA,IAAMC,MAAM,GAAG;IACbC,EAAE,EAAE;MACFC,GAAG,EAAEN,eAAe,CAAC,QAAQ,CAAC;MAC9BO,MAAM,EAAEP,eAAe,CAAC,WAAW;;GAEtC;EAEDN,0BAA0B,CAACG,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAEM,MAAM,EAAAA,MAAA;IAAEN,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}