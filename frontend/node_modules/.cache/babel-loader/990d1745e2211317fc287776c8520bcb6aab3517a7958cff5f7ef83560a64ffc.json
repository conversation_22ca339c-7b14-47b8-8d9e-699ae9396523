{"ast": null, "code": "// Node.js WebSocket entry point\nlet WebSocketImpl;\nif (typeof window === 'undefined') {\n  // Node.js environment\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  WebSocketImpl = require('ws');\n} else {\n  // Browser environment\n  WebSocketImpl = window.WebSocket;\n}\nexport default WebSocketImpl;", "map": {"version": 3, "names": ["WebSocketImpl", "window", "require", "WebSocket"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@supabase/realtime-js/src/WebSocket.ts"], "sourcesContent": ["// Node.js WebSocket entry point\n\nlet WebSocketImpl: any\n\nif (typeof window === 'undefined') {\n  // Node.js environment\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  WebSocketImpl = require('ws')\n} else {\n  // Browser environment\n  WebSocketImpl = window.WebSocket\n}\n\nexport default WebSocketImpl\n"], "mappings": "AAAA;AAEA,IAAIA,aAAkB;AAEtB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACjC;EACA;EACAD,aAAa,GAAGE,OAAO,CAAC,IAAI,CAAC;AAC/B,CAAC,MAAM;EACL;EACAF,aAAa,GAAGC,MAAM,CAACE,SAAS;AAClC;AAEA,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}