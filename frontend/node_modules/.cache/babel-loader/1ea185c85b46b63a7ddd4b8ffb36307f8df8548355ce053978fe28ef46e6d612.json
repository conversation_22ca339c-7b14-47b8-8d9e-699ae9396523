{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getCenterPoint } from '../utils';\nimport { FaceLandmarks } from './FaceLandmarks';\nvar FaceLandmarks68 = /** @class */function (_super) {\n  __extends(FaceLandmarks68, _super);\n  function FaceLandmarks68() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  FaceLandmarks68.prototype.getJawOutline = function () {\n    return this.positions.slice(0, 17);\n  };\n  FaceLandmarks68.prototype.getLeftEyeBrow = function () {\n    return this.positions.slice(17, 22);\n  };\n  FaceLandmarks68.prototype.getRightEyeBrow = function () {\n    return this.positions.slice(22, 27);\n  };\n  FaceLandmarks68.prototype.getNose = function () {\n    return this.positions.slice(27, 36);\n  };\n  FaceLandmarks68.prototype.getLeftEye = function () {\n    return this.positions.slice(36, 42);\n  };\n  FaceLandmarks68.prototype.getRightEye = function () {\n    return this.positions.slice(42, 48);\n  };\n  FaceLandmarks68.prototype.getMouth = function () {\n    return this.positions.slice(48, 68);\n  };\n  FaceLandmarks68.prototype.getRefPointsForAlignment = function () {\n    return [this.getLeftEye(), this.getRightEye(), this.getMouth()].map(getCenterPoint);\n  };\n  return FaceLandmarks68;\n}(FaceLandmarks);\nexport { FaceLandmarks68 };", "map": {"version": 3, "names": ["getCenterPoint", "FaceLandmarks", "FaceLandmarks68", "_super", "__extends", "prototype", "getJawOutline", "positions", "slice", "getLeftEyeBrow", "getRightEyeBrow", "getNose", "getLeftEye", "getRightEye", "getMouth", "getRefPointsForAlignment", "map"], "sources": ["../../../src/classes/FaceLandmarks68.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAG/C,IAAAC,eAAA,0BAAAC,MAAA;EAAqCC,SAAA,CAAAF,eAAA,EAAAC,MAAA;EAArC,SAAAD,gBAAA;;EAoCA;EAnCSA,eAAA,CAAAG,SAAA,CAAAC,aAAa,GAApB;IACE,OAAO,IAAI,CAACC,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACpC,CAAC;EAEMN,eAAA,CAAAG,SAAA,CAAAI,cAAc,GAArB;IACE,OAAO,IAAI,CAACF,SAAS,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC;EAEMN,eAAA,CAAAG,SAAA,CAAAK,eAAe,GAAtB;IACE,OAAO,IAAI,CAACH,SAAS,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC;EAEMN,eAAA,CAAAG,SAAA,CAAAM,OAAO,GAAd;IACE,OAAO,IAAI,CAACJ,SAAS,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC;EAEMN,eAAA,CAAAG,SAAA,CAAAO,UAAU,GAAjB;IACE,OAAO,IAAI,CAACL,SAAS,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC;EAEMN,eAAA,CAAAG,SAAA,CAAAQ,WAAW,GAAlB;IACE,OAAO,IAAI,CAACN,SAAS,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC;EAEMN,eAAA,CAAAG,SAAA,CAAAS,QAAQ,GAAf;IACE,OAAO,IAAI,CAACP,SAAS,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC;EAESN,eAAA,CAAAG,SAAA,CAAAU,wBAAwB,GAAlC;IACE,OAAO,CACL,IAAI,CAACH,UAAU,EAAE,EACjB,IAAI,CAACC,WAAW,EAAE,EAClB,IAAI,CAACC,QAAQ,EAAE,CAChB,CAACE,GAAG,CAAChB,cAAc,CAAC;EACvB,CAAC;EACH,OAAAE,eAAC;AAAD,CAAC,CApCoCD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}