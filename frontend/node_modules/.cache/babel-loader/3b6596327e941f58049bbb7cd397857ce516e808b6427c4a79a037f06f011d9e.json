{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Card, CardContent, Stack, Button, Alert, FormControl, InputLabel, Select, MenuItem, Box, Chip, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Analytics as AnalyticsIcon } from '@mui/icons-material';\nimport AutoAttendanceSystem from '../components/Attendance/AutoAttendanceSystem';\nimport { supabaseService } from '../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AttendanceSessionPage = () => {\n  _s();\n  var _selectedCourse$teach, _selectedCourse$teach2;\n  const [courses, setCourses] = useState([]);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [sessionActive, setSessionActive] = useState(false);\n  const [attendanceRecords, setAttendanceRecords] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportDialogOpen, setReportDialogOpen] = useState(false);\n\n  /**\n   * Charge les cours disponibles\n   */\n  const loadCourses = async () => {\n    try {\n      setLoading(true);\n      const coursesData = await supabaseService.getCourses();\n      setCourses(coursesData);\n    } catch (err) {\n      console.error('Erreur chargement cours:', err);\n      setError('Impossible de charger les cours');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gère la mise à jour des enregistrements de présence\n   */\n  const handleAttendanceUpdate = records => {\n    setAttendanceRecords(records);\n  };\n\n  /**\n   * Génère un rapport de session\n   */\n  const generateSessionReport = () => {\n    if (!selectedCourse || attendanceRecords.length === 0) return;\n    const present = attendanceRecords.filter(r => r.status === 'present').length;\n    const late = attendanceRecords.filter(r => r.status === 'late').length;\n    const absent = attendanceRecords.filter(r => r.status === 'absent').length;\n    const total = attendanceRecords.length;\n    const attendanceRate = total > 0 ? Math.round((present + late) / total * 100) : 0;\n    return {\n      course: selectedCourse,\n      total,\n      present,\n      late,\n      absent,\n      attendanceRate,\n      records: attendanceRecords\n    };\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadCourses();\n  }, []);\n  const sessionReport = generateSessionReport();\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      children: \"Session de Pr\\xE9sence Automatique\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Lancez une session de reconnaissance faciale pour enregistrer automatiquement les pr\\xE9sences des \\xE9tudiants.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      spacing: 4,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Configuration de la Session\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"S\\xE9lectionner un cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: (selectedCourse === null || selectedCourse === void 0 ? void 0 : selectedCourse.id) || '',\n                onChange: e => {\n                  const course = courses.find(c => c.id === e.target.value);\n                  setSelectedCourse(course || null);\n                },\n                label: \"S\\xE9lectionner un cours\",\n                disabled: sessionActive,\n                children: courses.map(course => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: course.id,\n                  children: [course.name, \" - \", course.semester]\n                }, course.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), selectedCourse && /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: selectedCourse.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: selectedCourse.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${selectedCourse.credits} crédits`,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCourse.semester,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `Professeur: ${(_selectedCourse$teach = selectedCourse.teacher) === null || _selectedCourse$teach === void 0 ? void 0 : _selectedCourse$teach.firstName} ${(_selectedCourse$teach2 = selectedCourse.teacher) === null || _selectedCourse$teach2 === void 0 ? void 0 : _selectedCourse$teach2.lastName}`,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), !sessionActive && selectedCourse && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Instructions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 51\n                }, this), \"1. Assurez-vous que les \\xE9tudiants ont enregistr\\xE9 leurs photos faciales\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 91\n                }, this), \"2. Cliquez sur \\\"D\\xE9marrer Session\\\" pour lancer la reconnaissance\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 84\n                }, this), \"3. Les pr\\xE9sences seront enregistr\\xE9es automatiquement\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 73\n                }, this), \"4. Arr\\xEAtez la session quand tous les \\xE9tudiants sont arriv\\xE9s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), selectedCourse && /*#__PURE__*/_jsxDEV(AutoAttendanceSystem, {\n        course: selectedCourse,\n        onAttendanceUpdate: handleAttendanceUpdate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), sessionReport && attendanceRecords.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"R\\xE9sum\\xE9 de la Session\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 30\n              }, this),\n              onClick: () => setReportDialogOpen(true),\n              children: \"Rapport D\\xE9taill\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: {\n              xs: 'column',\n              md: 'row'\n            },\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"success.main\",\n                  children: sessionReport.present\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: \"Pr\\xE9sents\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"warning.main\",\n                  children: sessionReport.late\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: \"En Retard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"error.main\",\n                  children: sessionReport.absent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: \"Absents\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary.main\",\n                  children: [sessionReport.attendanceRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: \"Taux de Pr\\xE9sence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Guide d'Utilisation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"1. Pr\\xE9paration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"\\u2022 Assurez-vous que tous les \\xE9tudiants ont enregistr\\xE9 leurs photos faciales \\u2022 V\\xE9rifiez que la cam\\xE9ra fonctionne correctement \\u2022 S\\xE9lectionnez le cours appropri\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"2. D\\xE9marrage de Session\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"\\u2022 Cliquez sur \\\"D\\xE9marrer Session\\\" au d\\xE9but du cours \\u2022 La cam\\xE9ra se lance automatiquement \\u2022 Les \\xE9tudiants sont d\\xE9tect\\xE9s et marqu\\xE9s pr\\xE9sents en temps r\\xE9el\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"3. Gestion des Retards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"\\u2022 Les \\xE9tudiants arrivant apr\\xE8s le d\\xE9lai configur\\xE9 sont marqu\\xE9s \\\"En retard\\\" \\u2022 Le seuil par d\\xE9faut est de 15 minutes (configurable)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"4. Fin de Session\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"\\u2022 Cliquez sur \\\"Arr\\xEAter Session\\\" quand tous les \\xE9tudiants sont arriv\\xE9s \\u2022 Les \\xE9tudiants non d\\xE9tect\\xE9s sont automatiquement marqu\\xE9s absents \\u2022 Un rapport de session est g\\xE9n\\xE9r\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: reportDialogOpen,\n      onClose: () => setReportDialogOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Rapport de Session D\\xE9taill\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: sessionReport && /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: sessionReport.course.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              children: [\"Session du \", new Date().toLocaleDateString('fr-FR')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: `${sessionReport.present} Présents`,\n              color: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${sessionReport.late} En Retard`,\n              color: \"warning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${sessionReport.absent} Absents`,\n              color: \"error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${sessionReport.attendanceRate}% Présence`,\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Cette session a \\xE9t\\xE9 enregistr\\xE9e automatiquement via reconnaissance faciale. Les donn\\xE9es sont sauvegard\\xE9es dans la base de donn\\xE9es Supabase.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setReportDialogOpen(false),\n          children: \"Fermer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: () => {\n            // Ici on pourrait exporter le rapport\n            console.log('Export rapport:', sessionReport);\n          },\n          children: \"Exporter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(AttendanceSessionPage, \"VJYN311lGH7YSyWu5Kpil7swa70=\");\n_c = AttendanceSessionPage;\nexport default AttendanceSessionPage;\nvar _c;\n$RefreshReg$(_c, \"AttendanceSessionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "Box", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Analytics", "AnalyticsIcon", "AutoAttendanceSystem", "supabaseService", "jsxDEV", "_jsxDEV", "AttendanceSessionPage", "_s", "_selectedCourse$teach", "_selectedCourse$teach2", "courses", "setCourses", "selectedCourse", "setSelectedCourse", "currentUser", "setCurrentUser", "sessionActive", "setSessionActive", "attendanceRecords", "setAttendanceRecords", "loading", "setLoading", "error", "setError", "reportDialogOpen", "setReportDialogOpen", "loadCourses", "coursesData", "getCourses", "err", "console", "handleAttendanceUpdate", "records", "generateSessionReport", "length", "present", "filter", "r", "status", "late", "absent", "total", "attendanceRate", "Math", "round", "course", "sessionReport", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "severity", "onClose", "spacing", "fullWidth", "value", "id", "onChange", "e", "find", "c", "target", "label", "disabled", "map", "name", "semester", "description", "direction", "credits", "size", "teacher", "firstName", "lastName", "onAttendanceUpdate", "display", "justifyContent", "alignItems", "startIcon", "onClick", "xs", "md", "flex", "open", "Date", "toLocaleDateString", "log", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AttendanceSessionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Con<PERSON><PERSON>,\n  <PERSON>po<PERSON>,\n  <PERSON>,\n  CardContent,\n  Stack,\n  Button,\n  Alert,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Box,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  PlayArrow as PlayIcon,\n  Stop as StopIcon,\n  Schedule as ScheduleIcon,\n  Analytics as AnalyticsIcon\n} from '@mui/icons-material';\nimport AutoAttendanceSystem from '../components/Attendance/AutoAttendanceSystem';\nimport { supabaseService } from '../services/supabaseService';\nimport { Course, AttendanceRecord, User } from '../types';\n\nconst AttendanceSessionPage: React.FC = () => {\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [sessionActive, setSessionActive] = useState(false);\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [reportDialogOpen, setReportDialogOpen] = useState(false);\n\n  /**\n   * Charge les cours disponibles\n   */\n  const loadCourses = async () => {\n    try {\n      setLoading(true);\n      const coursesData = await supabaseService.getCourses();\n      setCourses(coursesData);\n    } catch (err) {\n      console.error('Erreur chargement cours:', err);\n      setError('Impossible de charger les cours');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gère la mise à jour des enregistrements de présence\n   */\n  const handleAttendanceUpdate = (records: AttendanceRecord[]) => {\n    setAttendanceRecords(records);\n  };\n\n  /**\n   * Génère un rapport de session\n   */\n  const generateSessionReport = () => {\n    if (!selectedCourse || attendanceRecords.length === 0) return;\n\n    const present = attendanceRecords.filter(r => r.status === 'present').length;\n    const late = attendanceRecords.filter(r => r.status === 'late').length;\n    const absent = attendanceRecords.filter(r => r.status === 'absent').length;\n    const total = attendanceRecords.length;\n    const attendanceRate = total > 0 ? Math.round(((present + late) / total) * 100) : 0;\n\n    return {\n      course: selectedCourse,\n      total,\n      present,\n      late,\n      absent,\n      attendanceRate,\n      records: attendanceRecords\n    };\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadCourses();\n  }, []);\n\n  const sessionReport = generateSessionReport();\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      <Typography variant=\"h3\" gutterBottom>\n        Session de Présence Automatique\n      </Typography>\n\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Lancez une session de reconnaissance faciale pour enregistrer automatiquement \n        les présences des étudiants.\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      <Stack spacing={4}>\n        {/* Sélection du cours */}\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Configuration de la Session\n            </Typography>\n            \n            <Stack spacing={3}>\n              <FormControl fullWidth>\n                <InputLabel>Sélectionner un cours</InputLabel>\n                <Select\n                  value={selectedCourse?.id || ''}\n                  onChange={(e) => {\n                    const course = courses.find(c => c.id === e.target.value);\n                    setSelectedCourse(course || null);\n                  }}\n                  label=\"Sélectionner un cours\"\n                  disabled={sessionActive}\n                >\n                  {courses.map((course) => (\n                    <MenuItem key={course.id} value={course.id}>\n                      {course.name} - {course.semester}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n\n              {selectedCourse && (\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\">{selectedCourse.name}</Typography>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      {selectedCourse.description}\n                    </Typography>\n                    <Stack direction=\"row\" spacing={1}>\n                      <Chip label={`${selectedCourse.credits} crédits`} size=\"small\" />\n                      <Chip label={selectedCourse.semester} size=\"small\" />\n                      <Chip \n                        label={`Professeur: ${selectedCourse.teacher?.firstName} ${selectedCourse.teacher?.lastName}`} \n                        size=\"small\" \n                      />\n                    </Stack>\n                  </CardContent>\n                </Card>\n              )}\n\n              {!sessionActive && selectedCourse && (\n                <Alert severity=\"info\">\n                  <Typography variant=\"body2\">\n                    <strong>Instructions:</strong><br />\n                    1. Assurez-vous que les étudiants ont enregistré leurs photos faciales<br />\n                    2. Cliquez sur \"Démarrer Session\" pour lancer la reconnaissance<br />\n                    3. Les présences seront enregistrées automatiquement<br />\n                    4. Arrêtez la session quand tous les étudiants sont arrivés\n                  </Typography>\n                </Alert>\n              )}\n            </Stack>\n          </CardContent>\n        </Card>\n\n        {/* Système de présence automatique */}\n        {selectedCourse && (\n          <AutoAttendanceSystem\n            course={selectedCourse}\n            onAttendanceUpdate={handleAttendanceUpdate}\n          />\n        )}\n\n        {/* Résumé de session */}\n        {sessionReport && attendanceRecords.length > 0 && (\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\">\n                  Résumé de la Session\n                </Typography>\n                <Button\n                  variant=\"outlined\"\n                  startIcon={<AnalyticsIcon />}\n                  onClick={() => setReportDialogOpen(true)}\n                >\n                  Rapport Détaillé\n                </Button>\n              </Box>\n\n              <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>\n                <Card variant=\"outlined\" sx={{ flex: 1 }}>\n                  <CardContent>\n                    <Typography variant=\"h4\" color=\"success.main\">\n                      {sessionReport.present}\n                    </Typography>\n                    <Typography color=\"text.secondary\">Présents</Typography>\n                  </CardContent>\n                </Card>\n\n                <Card variant=\"outlined\" sx={{ flex: 1 }}>\n                  <CardContent>\n                    <Typography variant=\"h4\" color=\"warning.main\">\n                      {sessionReport.late}\n                    </Typography>\n                    <Typography color=\"text.secondary\">En Retard</Typography>\n                  </CardContent>\n                </Card>\n\n                <Card variant=\"outlined\" sx={{ flex: 1 }}>\n                  <CardContent>\n                    <Typography variant=\"h4\" color=\"error.main\">\n                      {sessionReport.absent}\n                    </Typography>\n                    <Typography color=\"text.secondary\">Absents</Typography>\n                  </CardContent>\n                </Card>\n\n                <Card variant=\"outlined\" sx={{ flex: 1 }}>\n                  <CardContent>\n                    <Typography variant=\"h4\" color=\"primary.main\">\n                      {sessionReport.attendanceRate}%\n                    </Typography>\n                    <Typography color=\"text.secondary\">Taux de Présence</Typography>\n                  </CardContent>\n                </Card>\n              </Stack>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Instructions d'utilisation */}\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Guide d'Utilisation\n            </Typography>\n            \n            <Stack spacing={2}>\n              <Box>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  1. Préparation\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  • Assurez-vous que tous les étudiants ont enregistré leurs photos faciales\n                  • Vérifiez que la caméra fonctionne correctement\n                  • Sélectionnez le cours approprié\n                </Typography>\n              </Box>\n\n              <Box>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  2. Démarrage de Session\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  • Cliquez sur \"Démarrer Session\" au début du cours\n                  • La caméra se lance automatiquement\n                  • Les étudiants sont détectés et marqués présents en temps réel\n                </Typography>\n              </Box>\n\n              <Box>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  3. Gestion des Retards\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  • Les étudiants arrivant après le délai configuré sont marqués \"En retard\"\n                  • Le seuil par défaut est de 15 minutes (configurable)\n                </Typography>\n              </Box>\n\n              <Box>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  4. Fin de Session\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  • Cliquez sur \"Arrêter Session\" quand tous les étudiants sont arrivés\n                  • Les étudiants non détectés sont automatiquement marqués absents\n                  • Un rapport de session est généré\n                </Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Stack>\n\n      {/* Dialog du rapport détaillé */}\n      <Dialog \n        open={reportDialogOpen} \n        onClose={() => setReportDialogOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>Rapport de Session Détaillé</DialogTitle>\n        <DialogContent>\n          {sessionReport && (\n            <Stack spacing={3}>\n              <Box>\n                <Typography variant=\"h6\">{sessionReport.course.name}</Typography>\n                <Typography color=\"text.secondary\">\n                  Session du {new Date().toLocaleDateString('fr-FR')}\n                </Typography>\n              </Box>\n\n              <Stack direction=\"row\" spacing={2}>\n                <Chip \n                  label={`${sessionReport.present} Présents`} \n                  color=\"success\" \n                />\n                <Chip \n                  label={`${sessionReport.late} En Retard`} \n                  color=\"warning\" \n                />\n                <Chip \n                  label={`${sessionReport.absent} Absents`} \n                  color=\"error\" \n                />\n                <Chip \n                  label={`${sessionReport.attendanceRate}% Présence`} \n                  color=\"primary\" \n                />\n              </Stack>\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Cette session a été enregistrée automatiquement via reconnaissance faciale.\n                Les données sont sauvegardées dans la base de données Supabase.\n              </Typography>\n            </Stack>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setReportDialogOpen(false)}>Fermer</Button>\n          <Button variant=\"contained\" onClick={() => {\n            // Ici on pourrait exporter le rapport\n            console.log('Export rapport:', sessionReport);\n          }}>\n            Exporter\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default AttendanceSessionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SAIEC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9D,MAAMC,qBAA+B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAqB,EAAE,CAAC;EAClF,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;;EAE/D;AACF;AACA;EACE,MAAM6C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,WAAW,GAAG,MAAMxB,eAAe,CAACyB,UAAU,CAAC,CAAC;MACtDjB,UAAU,CAACgB,WAAW,CAAC;IACzB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEO,GAAG,CAAC;MAC9CN,QAAQ,CAAC,iCAAiC,CAAC;IAC7C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMU,sBAAsB,GAAIC,OAA2B,IAAK;IAC9Db,oBAAoB,CAACa,OAAO,CAAC;EAC/B,CAAC;;EAED;AACF;AACA;EACE,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACrB,cAAc,IAAIM,iBAAiB,CAACgB,MAAM,KAAK,CAAC,EAAE;IAEvD,MAAMC,OAAO,GAAGjB,iBAAiB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACJ,MAAM;IAC5E,MAAMK,IAAI,GAAGrB,iBAAiB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC,CAACJ,MAAM;IACtE,MAAMM,MAAM,GAAGtB,iBAAiB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACJ,MAAM;IAC1E,MAAMO,KAAK,GAAGvB,iBAAiB,CAACgB,MAAM;IACtC,MAAMQ,cAAc,GAAGD,KAAK,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAE,CAACT,OAAO,GAAGI,IAAI,IAAIE,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;IAEnF,OAAO;MACLI,MAAM,EAAEjC,cAAc;MACtB6B,KAAK;MACLN,OAAO;MACPI,IAAI;MACJC,MAAM;MACNE,cAAc;MACdV,OAAO,EAAEd;IACX,CAAC;EACH,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd4C,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoB,aAAa,GAAGb,qBAAqB,CAAC,CAAC;EAE7C,oBACE5B,OAAA,CAACtB,SAAS;IAACgE,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACrC7C,OAAA,CAACrB,UAAU;MAACmE,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnD,OAAA,CAACrB,UAAU;MAACmE,OAAO,EAAC,OAAO;MAACM,KAAK,EAAC,gBAAgB;MAACT,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAC;IAGlE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZlC,KAAK,iBACJjB,OAAA,CAAChB,KAAK;MAACsE,QAAQ,EAAC,OAAO;MAACX,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAACE,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,IAAI,CAAE;MAAA2B,QAAA,EAClE5B;IAAK;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDnD,OAAA,CAAClB,KAAK;MAAC0E,OAAO,EAAE,CAAE;MAAAX,QAAA,gBAEhB7C,OAAA,CAACpB,IAAI;QAAAiE,QAAA,eACH7C,OAAA,CAACnB,WAAW;UAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;YAACmE,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbnD,OAAA,CAAClB,KAAK;YAAC0E,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAChB7C,OAAA,CAACf,WAAW;cAACwE,SAAS;cAAAZ,QAAA,gBACpB7C,OAAA,CAACd,UAAU;gBAAA2D,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9CnD,OAAA,CAACb,MAAM;gBACLuE,KAAK,EAAE,CAAAnD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoD,EAAE,KAAI,EAAG;gBAChCC,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMrB,MAAM,GAAGnC,OAAO,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKE,CAAC,CAACG,MAAM,CAACN,KAAK,CAAC;kBACzDlD,iBAAiB,CAACgC,MAAM,IAAI,IAAI,CAAC;gBACnC,CAAE;gBACFyB,KAAK,EAAC,0BAAuB;gBAC7BC,QAAQ,EAAEvD,aAAc;gBAAAkC,QAAA,EAEvBxC,OAAO,CAAC8D,GAAG,CAAE3B,MAAM,iBAClBxC,OAAA,CAACZ,QAAQ;kBAAiBsE,KAAK,EAAElB,MAAM,CAACmB,EAAG;kBAAAd,QAAA,GACxCL,MAAM,CAAC4B,IAAI,EAAC,KAAG,EAAC5B,MAAM,CAAC6B,QAAQ;gBAAA,GADnB7B,MAAM,CAACmB,EAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEb5C,cAAc,iBACbP,OAAA,CAACpB,IAAI;cAACkE,OAAO,EAAC,UAAU;cAAAD,QAAA,eACtB7C,OAAA,CAACnB,WAAW;gBAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAAAD,QAAA,EAAEtC,cAAc,CAAC6D;gBAAI;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3DnD,OAAA,CAACrB,UAAU;kBAACyE,KAAK,EAAC,gBAAgB;kBAACL,YAAY;kBAAAF,QAAA,EAC5CtC,cAAc,CAAC+D;gBAAW;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACbnD,OAAA,CAAClB,KAAK;kBAACyF,SAAS,EAAC,KAAK;kBAACf,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBAChC7C,OAAA,CAACV,IAAI;oBAAC2E,KAAK,EAAE,GAAG1D,cAAc,CAACiE,OAAO,UAAW;oBAACC,IAAI,EAAC;kBAAO;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjEnD,OAAA,CAACV,IAAI;oBAAC2E,KAAK,EAAE1D,cAAc,CAAC8D,QAAS;oBAACI,IAAI,EAAC;kBAAO;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrDnD,OAAA,CAACV,IAAI;oBACH2E,KAAK,EAAE,gBAAA9D,qBAAA,GAAeI,cAAc,CAACmE,OAAO,cAAAvE,qBAAA,uBAAtBA,qBAAA,CAAwBwE,SAAS,KAAAvE,sBAAA,GAAIG,cAAc,CAACmE,OAAO,cAAAtE,sBAAA,uBAAtBA,sBAAA,CAAwBwE,QAAQ,EAAG;oBAC9FH,IAAI,EAAC;kBAAO;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP,EAEA,CAACxC,aAAa,IAAIJ,cAAc,iBAC/BP,OAAA,CAAChB,KAAK;cAACsE,QAAQ,EAAC,MAAM;cAAAT,QAAA,eACpB7C,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAAAnD,OAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gFACkC,eAAAnD,OAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,wEACb,eAAAnD,OAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,8DACjB,eAAAnD,OAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,wEAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAGN5C,cAAc,iBACbP,OAAA,CAACH,oBAAoB;QACnB2C,MAAM,EAAEjC,cAAe;QACvBsE,kBAAkB,EAAEnD;MAAuB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACF,EAGAV,aAAa,IAAI5B,iBAAiB,CAACgB,MAAM,GAAG,CAAC,iBAC5C7B,OAAA,CAACpB,IAAI;QAAAiE,QAAA,eACH7C,OAAA,CAACnB,WAAW;UAAAgE,QAAA,gBACV7C,OAAA,CAACX,GAAG;YAACyF,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,eAAe;YAACC,UAAU,EAAC,QAAQ;YAAC3B,EAAE,EAAE,CAAE;YAAAR,QAAA,gBAC3E7C,OAAA,CAACrB,UAAU;cAACmE,OAAO,EAAC,IAAI;cAAAD,QAAA,EAAC;YAEzB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnD,OAAA,CAACjB,MAAM;cACL+D,OAAO,EAAC,UAAU;cAClBmC,SAAS,eAAEjF,OAAA,CAACJ,aAAa;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7B+B,OAAO,EAAEA,CAAA,KAAM9D,mBAAmB,CAAC,IAAI,CAAE;cAAAyB,QAAA,EAC1C;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnD,OAAA,CAAClB,KAAK;YAACyF,SAAS,EAAE;cAAEY,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAM,CAAE;YAAC5B,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACxD7C,OAAA,CAACpB,IAAI;cAACkE,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAE0C,IAAI,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACvC7C,OAAA,CAACnB,WAAW;gBAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAACM,KAAK,EAAC,cAAc;kBAAAP,QAAA,EAC1CJ,aAAa,CAACX;gBAAO;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACbnD,OAAA,CAACrB,UAAU;kBAACyE,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPnD,OAAA,CAACpB,IAAI;cAACkE,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAE0C,IAAI,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACvC7C,OAAA,CAACnB,WAAW;gBAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAACM,KAAK,EAAC,cAAc;kBAAAP,QAAA,EAC1CJ,aAAa,CAACP;gBAAI;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACbnD,OAAA,CAACrB,UAAU;kBAACyE,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPnD,OAAA,CAACpB,IAAI;cAACkE,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAE0C,IAAI,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACvC7C,OAAA,CAACnB,WAAW;gBAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAACM,KAAK,EAAC,YAAY;kBAAAP,QAAA,EACxCJ,aAAa,CAACN;gBAAM;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACbnD,OAAA,CAACrB,UAAU;kBAACyE,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPnD,OAAA,CAACpB,IAAI;cAACkE,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAE0C,IAAI,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACvC7C,OAAA,CAACnB,WAAW;gBAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAACM,KAAK,EAAC,cAAc;kBAAAP,QAAA,GAC1CJ,aAAa,CAACJ,cAAc,EAAC,GAChC;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;kBAACyE,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACP,eAGDnD,OAAA,CAACpB,IAAI;QAAAiE,QAAA,eACH7C,OAAA,CAACnB,WAAW;UAAAgE,QAAA,gBACV7C,OAAA,CAACrB,UAAU;YAACmE,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbnD,OAAA,CAAClB,KAAK;YAAC0E,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAChB7C,OAAA,CAACX,GAAG;cAAAwD,QAAA,gBACF7C,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAACM,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAAC;cAInD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnD,OAAA,CAACX,GAAG;cAAAwD,QAAA,gBACF7C,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAACM,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAAC;cAInD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnD,OAAA,CAACX,GAAG;cAAAwD,QAAA,gBACF7C,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAACM,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAAC;cAGnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnD,OAAA,CAACX,GAAG;cAAAwD,QAAA,gBACF7C,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAACM,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAAC;cAInD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRnD,OAAA,CAACT,MAAM;MACL+F,IAAI,EAAEnE,gBAAiB;MACvBoC,OAAO,EAAEA,CAAA,KAAMnC,mBAAmB,CAAC,KAAK,CAAE;MAC1CsB,QAAQ,EAAC,IAAI;MACbe,SAAS;MAAAZ,QAAA,gBAET7C,OAAA,CAACR,WAAW;QAAAqD,QAAA,EAAC;MAA2B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtDnD,OAAA,CAACP,aAAa;QAAAoD,QAAA,EACXJ,aAAa,iBACZzC,OAAA,CAAClB,KAAK;UAAC0E,OAAO,EAAE,CAAE;UAAAX,QAAA,gBAChB7C,OAAA,CAACX,GAAG;YAAAwD,QAAA,gBACF7C,OAAA,CAACrB,UAAU;cAACmE,OAAO,EAAC,IAAI;cAAAD,QAAA,EAAEJ,aAAa,CAACD,MAAM,CAAC4B;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACjEnD,OAAA,CAACrB,UAAU;cAACyE,KAAK,EAAC,gBAAgB;cAAAP,QAAA,GAAC,aACtB,EAAC,IAAI0C,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENnD,OAAA,CAAClB,KAAK;YAACyF,SAAS,EAAC,KAAK;YAACf,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAChC7C,OAAA,CAACV,IAAI;cACH2E,KAAK,EAAE,GAAGxB,aAAa,CAACX,OAAO,WAAY;cAC3CsB,KAAK,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFnD,OAAA,CAACV,IAAI;cACH2E,KAAK,EAAE,GAAGxB,aAAa,CAACP,IAAI,YAAa;cACzCkB,KAAK,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFnD,OAAA,CAACV,IAAI;cACH2E,KAAK,EAAE,GAAGxB,aAAa,CAACN,MAAM,UAAW;cACzCiB,KAAK,EAAC;YAAO;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFnD,OAAA,CAACV,IAAI;cACH2E,KAAK,EAAE,GAAGxB,aAAa,CAACJ,cAAc,YAAa;cACnDe,KAAK,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAERnD,OAAA,CAACrB,UAAU;YAACmE,OAAO,EAAC,OAAO;YAACM,KAAK,EAAC,gBAAgB;YAAAP,QAAA,EAAC;UAGnD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBnD,OAAA,CAACN,aAAa;QAAAmD,QAAA,gBACZ7C,OAAA,CAACjB,MAAM;UAACmG,OAAO,EAAEA,CAAA,KAAM9D,mBAAmB,CAAC,KAAK,CAAE;UAAAyB,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEnD,OAAA,CAACjB,MAAM;UAAC+D,OAAO,EAAC,WAAW;UAACoC,OAAO,EAAEA,CAAA,KAAM;YACzC;YACAzD,OAAO,CAACgE,GAAG,CAAC,iBAAiB,EAAEhD,aAAa,CAAC;UAC/C,CAAE;UAAAI,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACjD,EAAA,CA/TID,qBAA+B;AAAAyF,EAAA,GAA/BzF,qBAA+B;AAiUrC,eAAeA,qBAAqB;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}