{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getCenterPoint } from '../utils';\nimport { FaceLandmarks } from './FaceLandmarks';\nvar FaceLandmarks5 = /** @class */function (_super) {\n  __extends(FaceLandmarks5, _super);\n  function FaceLandmarks5() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  FaceLandmarks5.prototype.getRefPointsForAlignment = function () {\n    var pts = this.positions;\n    return [pts[0], pts[1], getCenterPoint([pts[3], pts[4]])];\n  };\n  return FaceLandmarks5;\n}(FaceLandmarks);\nexport { FaceLandmarks5 };", "map": {"version": 3, "names": ["getCenterPoint", "FaceLandmarks", "FaceLandmarks5", "_super", "__extends", "prototype", "getRefPointsForAlignment", "pts", "positions"], "sources": ["../../../src/classes/FaceLandmarks5.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAI/C,IAAAC,cAAA,0BAAAC,MAAA;EAAoCC,SAAA,CAAAF,cAAA,EAAAC,MAAA;EAApC,SAAAD,eAAA;;EAUA;EARYA,cAAA,CAAAG,SAAA,CAAAC,wBAAwB,GAAlC;IACE,IAAMC,GAAG,GAAG,IAAI,CAACC,SAAS;IAC1B,OAAO,CACLD,GAAG,CAAC,CAAC,CAAC,EACNA,GAAG,CAAC,CAAC,CAAC,EACNP,cAAc,CAAC,CAACO,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACjC;EACH,CAAC;EACH,OAAAL,cAAC;AAAD,CAAC,CAVmCD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}