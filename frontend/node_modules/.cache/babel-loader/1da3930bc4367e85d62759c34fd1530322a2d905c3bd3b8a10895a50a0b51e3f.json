{"ast": null, "code": "export var FACE_EXPRESSION_LABELS = ['neutral', 'happy', 'sad', 'angry', 'fearful', 'disgusted', 'surprised'];\nvar FaceExpressions = /** @class */function () {\n  function FaceExpressions(probabilities) {\n    var _this = this;\n    if (probabilities.length !== 7) {\n      throw new Error(\"FaceExpressions.constructor - expected probabilities.length to be 7, have: \" + probabilities.length);\n    }\n    FACE_EXPRESSION_LABELS.forEach(function (expression, idx) {\n      _this[expression] = probabilities[idx];\n    });\n  }\n  FaceExpressions.prototype.asSortedArray = function () {\n    var _this = this;\n    return FACE_EXPRESSION_LABELS.map(function (expression) {\n      return {\n        expression: expression,\n        probability: _this[expression]\n      };\n    }).sort(function (e0, e1) {\n      return e1.probability - e0.probability;\n    });\n  };\n  return FaceExpressions;\n}();\nexport { FaceExpressions };", "map": {"version": 3, "names": ["FACE_EXPRESSION_LABELS", "FaceExpressions", "probabilities", "_this", "length", "Error", "for<PERSON>ach", "expression", "idx", "prototype", "asSortedA<PERSON>y", "map", "probability", "sort", "e0", "e1"], "sources": ["../../../src/faceExpressionNet/FaceExpressions.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,IAAMA,sBAAsB,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC;AAE/G,IAAAC,eAAA;EASE,SAAAA,gBAAYC,aAAsC;IAAlD,IAAAC,KAAA;IACE,IAAID,aAAa,CAACE,MAAM,KAAK,CAAC,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,gFAA8EH,aAAa,CAACE,MAAQ,CAAC;;IAGvHJ,sBAAsB,CAACM,OAAO,CAAC,UAACC,UAAU,EAAEC,GAAG;MAC7CL,KAAI,CAACI,UAAU,CAAC,GAAGL,aAAa,CAACM,GAAG,CAAC;IACvC,CAAC,CAAC;EACJ;EAEAP,eAAA,CAAAQ,SAAA,CAAAC,aAAa,GAAb;IAAA,IAAAP,KAAA;IACE,OAAOH,sBAAsB,CAC1BW,GAAG,CAAC,UAAAJ,UAAU;MAAI,OAAC;QAAEA,UAAU,EAAAA,UAAA;QAAEK,WAAW,EAAET,KAAI,CAACI,UAAU;MAAW,CAAE;IAAxD,CAAyD,CAAC,CAC5EM,IAAI,CAAC,UAACC,EAAE,EAAEC,EAAE;MAAK,OAAAA,EAAE,CAACH,WAAW,GAAGE,EAAE,CAACF,WAAW;IAA/B,CAA+B,CAAC;EACtD,CAAC;EACH,OAAAX,eAAC;AAAD,CAAC,CAxBD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}