{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { extendWithFaceExpressions } from '../factories/WithFaceExpressions';\nimport { ComposableTask } from './ComposableTask';\nimport { ComputeAllFaceDescriptorsTask, ComputeSingleFaceDescriptorTask } from './ComputeFaceDescriptorsTasks';\nimport { extractAllFacesAndComputeResults, extractSingleFaceAndComputeResult } from './extractFacesAndComputeResults';\nimport { nets } from './nets';\nimport { PredictAllAgeAndGenderTask, PredictAllAgeAndGenderWithFaceAlignmentTask, PredictSingleAgeAndGenderTask, PredictSingleAgeAndGenderWithFaceAlignmentTask } from './PredictAgeAndGenderTask';\nvar PredictFaceExpressionsTaskBase = /** @class */function (_super) {\n  __extends(PredictFaceExpressionsTaskBase, _super);\n  function PredictFaceExpressionsTaskBase(parentTask, input, extractedFaces) {\n    var _this = _super.call(this) || this;\n    _this.parentTask = parentTask;\n    _this.input = input;\n    _this.extractedFaces = extractedFaces;\n    return _this;\n  }\n  return PredictFaceExpressionsTaskBase;\n}(ComposableTask);\nexport { PredictFaceExpressionsTaskBase };\nvar PredictAllFaceExpressionsTask = /** @class */function (_super) {\n  __extends(PredictAllFaceExpressionsTask, _super);\n  function PredictAllFaceExpressionsTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictAllFaceExpressionsTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResults, faceExpressionsByFace;\n      var _this = this;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResults = _a.sent();\n            return [4 /*yield*/, extractAllFacesAndComputeResults(parentResults, this.input, function (faces) {\n              return __awaiter(_this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                  switch (_a.label) {\n                    case 0:\n                      return [4 /*yield*/, Promise.all(faces.map(function (face) {\n                        return nets.faceExpressionNet.predictExpressions(face);\n                      }))];\n                    case 1:\n                      return [2 /*return*/, _a.sent()];\n                  }\n                });\n              });\n            }, this.extractedFaces)];\n          case 2:\n            faceExpressionsByFace = _a.sent();\n            return [2 /*return*/, parentResults.map(function (parentResult, i) {\n              return extendWithFaceExpressions(parentResult, faceExpressionsByFace[i]);\n            })];\n        }\n      });\n    });\n  };\n  PredictAllFaceExpressionsTask.prototype.withAgeAndGender = function () {\n    return new PredictAllAgeAndGenderTask(this, this.input);\n  };\n  return PredictAllFaceExpressionsTask;\n}(PredictFaceExpressionsTaskBase);\nexport { PredictAllFaceExpressionsTask };\nvar PredictSingleFaceExpressionsTask = /** @class */function (_super) {\n  __extends(PredictSingleFaceExpressionsTask, _super);\n  function PredictSingleFaceExpressionsTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictSingleFaceExpressionsTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResult, faceExpressions;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResult = _a.sent();\n            if (!parentResult) {\n              return [2 /*return*/];\n            }\n            return [4 /*yield*/, extractSingleFaceAndComputeResult(parentResult, this.input, function (face) {\n              return nets.faceExpressionNet.predictExpressions(face);\n            }, this.extractedFaces)];\n          case 2:\n            faceExpressions = _a.sent();\n            return [2 /*return*/, extendWithFaceExpressions(parentResult, faceExpressions)];\n        }\n      });\n    });\n  };\n  PredictSingleFaceExpressionsTask.prototype.withAgeAndGender = function () {\n    return new PredictSingleAgeAndGenderTask(this, this.input);\n  };\n  return PredictSingleFaceExpressionsTask;\n}(PredictFaceExpressionsTaskBase);\nexport { PredictSingleFaceExpressionsTask };\nvar PredictAllFaceExpressionsWithFaceAlignmentTask = /** @class */function (_super) {\n  __extends(PredictAllFaceExpressionsWithFaceAlignmentTask, _super);\n  function PredictAllFaceExpressionsWithFaceAlignmentTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictAllFaceExpressionsWithFaceAlignmentTask.prototype.withAgeAndGender = function () {\n    return new PredictAllAgeAndGenderWithFaceAlignmentTask(this, this.input);\n  };\n  PredictAllFaceExpressionsWithFaceAlignmentTask.prototype.withFaceDescriptors = function () {\n    return new ComputeAllFaceDescriptorsTask(this, this.input);\n  };\n  return PredictAllFaceExpressionsWithFaceAlignmentTask;\n}(PredictAllFaceExpressionsTask);\nexport { PredictAllFaceExpressionsWithFaceAlignmentTask };\nvar PredictSingleFaceExpressionsWithFaceAlignmentTask = /** @class */function (_super) {\n  __extends(PredictSingleFaceExpressionsWithFaceAlignmentTask, _super);\n  function PredictSingleFaceExpressionsWithFaceAlignmentTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  PredictSingleFaceExpressionsWithFaceAlignmentTask.prototype.withAgeAndGender = function () {\n    return new PredictSingleAgeAndGenderWithFaceAlignmentTask(this, this.input);\n  };\n  PredictSingleFaceExpressionsWithFaceAlignmentTask.prototype.withFaceDescriptor = function () {\n    return new ComputeSingleFaceDescriptorTask(this, this.input);\n  };\n  return PredictSingleFaceExpressionsWithFaceAlignmentTask;\n}(PredictSingleFaceExpressionsTask);\nexport { PredictSingleFaceExpressionsWithFaceAlignmentTask };", "map": {"version": 3, "names": ["extendWithFaceExpressions", "ComposableTask", "ComputeAllFaceDescriptorsTask", "ComputeSingleFaceDescriptorTask", "extractAllFacesAndComputeResults", "extractSingleFaceAndComputeResult", "nets", "PredictAllAgeAndGenderTask", "PredictAllAgeAndGenderWithFaceAlignmentTask", "PredictSingleAgeAndGenderTask", "PredictSingleAgeAndGenderWithFaceAlignmentTask", "PredictFaceExpressionsTaskBase", "_super", "__extends", "parentTask", "input", "extractedFaces", "_this", "call", "PredictAllFaceExpressionsTask", "prototype", "run", "parentResults", "_a", "sent", "faces", "__awaiter", "Promise", "all", "map", "face", "faceExpressionNet", "predictExpressions", "faceExpressionsByFace", "parentResult", "i", "withAgeAndGender", "PredictSingleFaceExpressionsTask", "faceExpressions", "PredictAllFaceExpressionsWithFaceAlignmentTask", "withFaceDescriptors", "PredictSingleFaceExpressionsWithFaceAlignmentTask", "withFaceDescriptor"], "sources": ["../../../src/globalApi/PredictFaceExpressionsTask.ts"], "sourcesContent": [null], "mappings": ";AAKA,SAASA,yBAAyB,QAA6B,kCAAkC;AAEjG,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,6BAA6B,EAAEC,+BAA+B,QAAQ,+BAA+B;AAC9G,SAASC,gCAAgC,EAAEC,iCAAiC,QAAQ,iCAAiC;AACrH,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SACEC,0BAA0B,EAC1BC,2CAA2C,EAC3CC,6BAA6B,EAC7BC,8CAA8C,QACzC,2BAA2B;AAElC,IAAAC,8BAAA,0BAAAC,MAAA;EAA4EC,SAAA,CAAAF,8BAAA,EAAAC,MAAA;EAC1E,SAAAD,+BACYG,UAAkE,EAClEC,KAAgB,EAChBC,cAAuD;IAHnE,IAAAC,KAAA,GAKEL,MAAA,CAAAM,IAAA,MAAO;IAJGD,KAAA,CAAAH,UAAU,GAAVA,UAAU;IACVG,KAAA,CAAAF,KAAK,GAALA,KAAK;IACLE,KAAA,CAAAD,cAAc,GAAdA,cAAc;;EAG1B;EACF,OAAAL,8BAAC;AAAD,CAAC,CAR2EV,cAAc;;AAU1F,IAAAkB,6BAAA,0BAAAP,MAAA;EAEUC,SAAA,CAAAM,6BAAA,EAAAP,MAAA;EAFV,SAAAO,8BAAA;;EAyBA;EArBeA,6BAAA,CAAAC,SAAA,CAAAC,GAAG,GAAhB;;;;;;;YAEwB,qBAAM,IAAI,CAACP,UAAU;;YAArCQ,aAAa,GAAGC,EAAA,CAAAC,IAAA,EAAqB;YAEb,qBAAMpB,gCAAgC,CAClEkB,aAAa,EACb,IAAI,CAACP,KAAK,EACV,UAAMU,KAAK;cAAA,OAAAC,SAAA,CAAAT,KAAA;;;;sBAAI,qBAAMU,OAAO,CAACC,GAAG,CAACH,KAAK,CAACI,GAAG,CACxC,UAAAC,IAAI;wBAAI,OAAAxB,IAAI,CAACyB,iBAAiB,CAACC,kBAAkB,CAACF,IAAI,CAA6B;sBAA3E,CAA2E,CACpF,CAAC;;sBAFa,sBAAAP,EAAA,CAAAC,IAAA,EAEb;;;;aAAA,EACF,IAAI,CAACR,cAAc,CACpB;;YAPKiB,qBAAqB,GAAGV,EAAA,CAAAC,IAAA,EAO7B;YAED,sBAAOF,aAAa,CAACO,GAAG,CACtB,UAACK,YAAY,EAAEC,CAAC;cAAK,OAAAnC,yBAAyB,CAAUkC,YAAY,EAAED,qBAAqB,CAACE,CAAC,CAAC,CAAC;YAA1E,CAA0E,CAChG;;;;GACF;EAEDhB,6BAAA,CAAAC,SAAA,CAAAgB,gBAAgB,GAAhB;IACE,OAAO,IAAI7B,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAACQ,KAAK,CAAC;EACzD,CAAC;EACH,OAAAI,6BAAC;AAAD,CAAC,CAvBSR,8BAA8B;;AAyBxC,IAAA0B,gCAAA,0BAAAzB,MAAA;EAEWC,SAAA,CAAAwB,gCAAA,EAAAzB,MAAA;EAFX,SAAAyB,iCAAA;;EAwBA;EApBeA,gCAAA,CAAAjB,SAAA,CAAAC,GAAG,GAAhB;;;;;;YAEuB,qBAAM,IAAI,CAACP,UAAU;;YAApCoB,YAAY,GAAGX,EAAA,CAAAC,IAAA,EAAqB;YAC1C,IAAI,CAACU,YAAY,EAAE;cACjB;;YAGsB,qBAAM7B,iCAAiC,CAC7D6B,YAAY,EACZ,IAAI,CAACnB,KAAK,EACV,UAAAe,IAAI;cAAI,OAAAxB,IAAI,CAACyB,iBAAiB,CAACC,kBAAkB,CAACF,IAAI,CAA6B;YAA3E,CAA2E,EACnF,IAAI,CAACd,cAAc,CACpB;;YALKsB,eAAe,GAAGf,EAAA,CAAAC,IAAA,EAKvB;YAED,sBAAOxB,yBAAyB,CAACkC,YAAY,EAAEI,eAAe,CAAC;;;;GAChE;EAEDD,gCAAA,CAAAjB,SAAA,CAAAgB,gBAAgB,GAAhB;IACE,OAAO,IAAI3B,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAACM,KAAK,CAAC;EAC5D,CAAC;EACH,OAAAsB,gCAAC;AAAD,CAAC,CAtBU1B,8BAA8B;;AAwBzC,IAAA4B,8CAAA,0BAAA3B,MAAA;EAEUC,SAAA,CAAA0B,8CAAA,EAAA3B,MAAA;EAFV,SAAA2B,+CAAA;;EAWA;EAPEA,8CAAA,CAAAnB,SAAA,CAAAgB,gBAAgB,GAAhB;IACE,OAAO,IAAI5B,2CAA2C,CAAC,IAAI,EAAE,IAAI,CAACO,KAAK,CAAC;EAC1E,CAAC;EAEDwB,8CAAA,CAAAnB,SAAA,CAAAoB,mBAAmB,GAAnB;IACE,OAAO,IAAItC,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAACa,KAAK,CAAC;EAC5D,CAAC;EACH,OAAAwB,8CAAC;AAAD,CAAC,CATSpB,6BAA6B;;AAWvC,IAAAsB,iDAAA,0BAAA7B,MAAA;EAEUC,SAAA,CAAA4B,iDAAA,EAAA7B,MAAA;EAFV,SAAA6B,kDAAA;;EAWA;EAPEA,iDAAA,CAAArB,SAAA,CAAAgB,gBAAgB,GAAhB;IACE,OAAO,IAAI1B,8CAA8C,CAAC,IAAI,EAAE,IAAI,CAACK,KAAK,CAAC;EAC7E,CAAC;EAED0B,iDAAA,CAAArB,SAAA,CAAAsB,kBAAkB,GAAlB;IACE,OAAO,IAAIvC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAACY,KAAK,CAAC;EAC9D,CAAC;EACH,OAAA0B,iDAAC;AAAD,CAAC,CATSJ,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}