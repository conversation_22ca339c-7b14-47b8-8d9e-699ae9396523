{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Box } from './Box';\nvar BoundingBox = /** @class */function (_super) {\n  __extends(BoundingBox, _super);\n  function BoundingBox(left, top, right, bottom, allowNegativeDimensions) {\n    if (allowNegativeDimensions === void 0) {\n      allowNegativeDimensions = false;\n    }\n    return _super.call(this, {\n      left: left,\n      top: top,\n      right: right,\n      bottom: bottom\n    }, allowNegativeDimensions) || this;\n  }\n  return BoundingBox;\n}(Box);\nexport { BoundingBox };", "map": {"version": 3, "names": ["Box", "BoundingBox", "_super", "__extends", "left", "top", "right", "bottom", "allowNegativeDimensions", "call"], "sources": ["../../../src/classes/BoundingBox.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,GAAG,QAAQ,OAAO;AAS3B,IAAAC,WAAA,0BAAAC,MAAA;EAAiCC,SAAA,CAAAF,WAAA,EAAAC,MAAA;EAC/B,SAAAD,YAAYG,IAAY,EAAEC,GAAW,EAAEC,KAAa,EAAEC,MAAc,EAAEC,uBAAwC;IAAxC,IAAAA,uBAAA;MAAAA,uBAAA,QAAwC;IAAA;WAC5GN,MAAA,CAAAO,IAAA,OAAM;MAAEL,IAAI,EAAAA,IAAA;MAAEC,GAAG,EAAAA,GAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,EAAEC,uBAAuB,CAAC;EAC9D;EACF,OAAAP,WAAC;AAAD,CAAC,CAJgCD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}