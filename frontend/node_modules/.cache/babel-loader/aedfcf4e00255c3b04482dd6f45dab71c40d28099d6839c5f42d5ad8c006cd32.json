{"ast": null, "code": "export function euclideanDistance(arr1, arr2) {\n  if (arr1.length !== arr2.length) throw new Error('euclideanDistance: arr1.length !== arr2.length');\n  var desc1 = Array.from(arr1);\n  var desc2 = Array.from(arr2);\n  return Math.sqrt(desc1.map(function (val, i) {\n    return val - desc2[i];\n  }).reduce(function (res, diff) {\n    return res + Math.pow(diff, 2);\n  }, 0));\n}", "map": {"version": 3, "names": ["euclideanDistance", "arr1", "arr2", "length", "Error", "desc1", "Array", "from", "desc2", "Math", "sqrt", "map", "val", "i", "reduce", "res", "diff", "pow"], "sources": ["../../src/euclideanDistance.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,iBAAiBA,CAACC,IAA6B,EAAEC,IAA6B;EAC5F,IAAID,IAAI,CAACE,MAAM,KAAKD,IAAI,CAACC,MAAM,EAC7B,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EAEnE,IAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACN,IAAI,CAAC;EAC9B,IAAMO,KAAK,GAAGF,KAAK,CAACC,IAAI,CAACL,IAAI,CAAC;EAE9B,OAAOO,IAAI,CAACC,IAAI,CACdL,KAAK,CACFM,GAAG,CAAC,UAACC,GAAG,EAAEC,CAAC;IAAK,OAAAD,GAAG,GAAGJ,KAAK,CAACK,CAAC,CAAC;EAAd,CAAc,CAAC,CAC/BC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;IAAK,OAAAD,GAAG,GAAGN,IAAI,CAACQ,GAAG,CAACD,IAAI,EAAE,CAAC,CAAC;EAAvB,CAAuB,EAAE,CAAC,CAAC,CACrD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}