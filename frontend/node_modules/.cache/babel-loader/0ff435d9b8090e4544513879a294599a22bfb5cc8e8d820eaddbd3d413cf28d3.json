{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { fullyConnectedLayer } from '../common/fullyConnectedLayer';\nimport { prelu } from './prelu';\nimport { sharedLayer } from './sharedLayers';\nexport function RNet(x, params) {\n  return tf.tidy(function () {\n    var convOut = sharedLayer(x, params);\n    var vectorized = tf.reshape(convOut, [convOut.shape[0], params.fc1.weights.shape[0]]);\n    var fc1 = fullyConnectedLayer(vectorized, params.fc1);\n    var prelu4 = prelu(fc1, params.prelu4_alpha);\n    var fc2_1 = fullyConnectedLayer(prelu4, params.fc2_1);\n    var max = tf.expandDims(tf.max(fc2_1, 1), 1);\n    var prob = tf.softmax(tf.sub(fc2_1, max), 1);\n    var regions = fullyConnectedLayer(prelu4, params.fc2_2);\n    var scores = tf.unstack(prob, 1)[1];\n    return {\n      scores: scores,\n      regions: regions\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "fullyConnectedLayer", "prelu", "<PERSON><PERSON><PERSON><PERSON>", "RNet", "x", "params", "tidy", "convOut", "vectorized", "reshape", "shape", "fc1", "weights", "prelu4", "prelu4_alpha", "fc2_1", "max", "expandDims", "prob", "softmax", "sub", "regions", "fc2_2", "scores", "unstack"], "sources": ["../../../src/mtcnn/RNet.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,OAAM,SAAUC,IAAIA,CAACC,CAAc,EAAEC,MAAkB;EACrD,OAAON,EAAE,CAACO,IAAI,CAAC;IAEb,IAAMC,OAAO,GAAGL,WAAW,CAACE,CAAC,EAAEC,MAAM,CAAC;IACtC,IAAMG,UAAU,GAAGT,EAAE,CAACU,OAAO,CAACF,OAAO,EAAE,CAACA,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAgB;IACtG,IAAMC,GAAG,GAAGX,mBAAmB,CAACQ,UAAU,EAAEH,MAAM,CAACM,GAAG,CAAC;IACvD,IAAME,MAAM,GAAGZ,KAAK,CAAcU,GAAG,EAAEN,MAAM,CAACS,YAAY,CAAC;IAC3D,IAAMC,KAAK,GAAGf,mBAAmB,CAACa,MAAM,EAAER,MAAM,CAACU,KAAK,CAAC;IACvD,IAAMC,GAAG,GAAGjB,EAAE,CAACkB,UAAU,CAAClB,EAAE,CAACiB,GAAG,CAACD,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAMG,IAAI,GAAGnB,EAAE,CAACoB,OAAO,CAACpB,EAAE,CAACqB,GAAG,CAACL,KAAK,EAAEC,GAAG,CAAC,EAAE,CAAC,CAAgB;IAC7D,IAAMK,OAAO,GAAGrB,mBAAmB,CAACa,MAAM,EAAER,MAAM,CAACiB,KAAK,CAAC;IAEzD,IAAMC,MAAM,GAAGxB,EAAE,CAACyB,OAAO,CAACN,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAgB;IACpD,OAAO;MAAEK,MAAM,EAAAA,MAAA;MAAEF,OAAO,EAAAA;IAAA,CAAE;EAC5B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}