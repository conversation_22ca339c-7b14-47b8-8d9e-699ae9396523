{"ast": null, "code": "import { BoundingBox } from '../classes';\nexport function minBbox(pts) {\n  var xs = pts.map(function (pt) {\n    return pt.x;\n  });\n  var ys = pts.map(function (pt) {\n    return pt.y;\n  });\n  var minX = xs.reduce(function (min, x) {\n    return x < min ? x : min;\n  }, Infinity);\n  var minY = ys.reduce(function (min, y) {\n    return y < min ? y : min;\n  }, Infinity);\n  var maxX = xs.reduce(function (max, x) {\n    return max < x ? x : max;\n  }, 0);\n  var maxY = ys.reduce(function (max, y) {\n    return max < y ? y : max;\n  }, 0);\n  return new BoundingBox(minX, minY, maxX, maxY);\n}", "map": {"version": 3, "names": ["BoundingBox", "minBbox", "pts", "xs", "map", "pt", "x", "ys", "y", "minX", "reduce", "min", "Infinity", "minY", "maxX", "max", "maxY"], "sources": ["../../../src/ops/minBbox.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,WAAW,QAAgB,YAAY;AAEhD,OAAM,SAAUC,OAAOA,CAACC,GAAa;EACnC,IAAMC,EAAE,GAAGD,GAAG,CAACE,GAAG,CAAC,UAAAC,EAAE;IAAI,OAAAA,EAAE,CAACC,CAAC;EAAJ,CAAI,CAAC;EAC9B,IAAMC,EAAE,GAAGL,GAAG,CAACE,GAAG,CAAC,UAAAC,EAAE;IAAI,OAAAA,EAAE,CAACG,CAAC;EAAJ,CAAI,CAAC;EAC9B,IAAMC,IAAI,GAAGN,EAAE,CAACO,MAAM,CAAC,UAACC,GAAG,EAAEL,CAAC;IAAK,OAAAA,CAAC,GAAGK,GAAG,GAAGL,CAAC,GAAGK,GAAG;EAAjB,CAAiB,EAAEC,QAAQ,CAAC;EAC/D,IAAMC,IAAI,GAAGN,EAAE,CAACG,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC;IAAK,OAAAA,CAAC,GAAGG,GAAG,GAAGH,CAAC,GAAGG,GAAG;EAAjB,CAAiB,EAAEC,QAAQ,CAAC;EAC/D,IAAME,IAAI,GAAGX,EAAE,CAACO,MAAM,CAAC,UAACK,GAAG,EAAET,CAAC;IAAK,OAAAS,GAAG,GAAGT,CAAC,GAAGA,CAAC,GAAGS,GAAG;EAAjB,CAAiB,EAAE,CAAC,CAAC;EACxD,IAAMC,IAAI,GAAGT,EAAE,CAACG,MAAM,CAAC,UAACK,GAAG,EAAEP,CAAC;IAAK,OAAAO,GAAG,GAAGP,CAAC,GAAGA,CAAC,GAAGO,GAAG;EAAjB,CAAiB,EAAE,CAAC,CAAC;EAExD,OAAO,IAAIf,WAAW,CAACS,IAAI,EAAEI,IAAI,EAAEC,IAAI,EAAEE,IAAI,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}