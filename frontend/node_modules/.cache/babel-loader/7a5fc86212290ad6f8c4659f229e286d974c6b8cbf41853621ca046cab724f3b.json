{"ast": null, "code": "export * from './iou';\nexport * from './minBbox';\nexport * from './nonMaxSuppression';\nexport * from './normalize';\nexport * from './padToSquare';\nexport * from './shuffleArray';\nexport function sigmoid(x) {\n  return 1 / (1 + Math.exp(-x));\n}\nexport function inverseSigmoid(x) {\n  return Math.log(x / (1 - x));\n}", "map": {"version": 3, "names": ["sigmoid", "x", "Math", "exp", "inverseSigmoid", "log"], "sources": ["../../../src/ops/index.ts"], "sourcesContent": [null], "mappings": "AAAA,cAAc,OAAO;AACrB,cAAc,WAAW;AACzB,cAAc,qBAAqB;AACnC,cAAc,aAAa;AAC3B,cAAc,eAAe;AAC7B,cAAc,gBAAgB;AAE9B,OAAM,SAAUA,OAAOA,CAACC,CAAS;EAC/B,OAAO,CAAC,IAAI,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,CAAC,CAAC;AAC/B;AAEA,OAAM,SAAUG,cAAcA,CAACH,CAAS;EACtC,OAAOC,IAAI,CAACG,GAAG,CAACJ,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}