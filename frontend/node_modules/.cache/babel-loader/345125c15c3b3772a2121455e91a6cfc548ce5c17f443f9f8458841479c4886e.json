{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\nimport { Box, Button, Card, CardContent, Typography, Alert, CircularProgress, Chip, Stack, IconButton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Videocam as VideocamIcon, VideocamOff as VideocamOffIcon, PhotoCamera as PhotoCameraIcon, Refresh as RefreshIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport { faceRecognitionService } from '../../services/faceRecognitionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FaceDetectionCamera = ({\n  onDetection,\n  onUserDetected,\n  autoDetect = true,\n  showControls = true,\n  width = 640,\n  height = 480\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const streamRef = useRef(null);\n  const detectionIntervalRef = useRef(null);\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isDetecting, setIsDetecting] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastDetection, setLastDetection] = useState(null);\n  const [stats, setStats] = useState(faceRecognitionService.getStats());\n  const [settingsOpen, setSettingsOpen] = useState(false);\n\n  /**\n   * Initialise la caméra\n   */\n  const startCamera = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Demander l'accès à la caméra\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: {\n            ideal: width\n          },\n          height: {\n            ideal: height\n          },\n          facingMode: 'user'\n        },\n        audio: false\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n      }\n    } catch (err) {\n      console.error('Erreur accès caméra:', err);\n      setError('Impossible d\\'accéder à la caméra. Vérifiez les permissions.');\n    } finally {\n      setLoading(false);\n    }\n  }, [width, height]);\n\n  /**\n   * Arrête la caméra\n   */\n  const stopCamera = useCallback(() => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    if (videoRef.current) {\n      videoRef.current.srcObject = null;\n    }\n    setIsStreaming(false);\n    stopDetection();\n  }, []);\n\n  /**\n   * Démarre la détection automatique\n   */\n  const startDetection = useCallback(async () => {\n    if (!isStreaming || isDetecting) return;\n    try {\n      setIsDetecting(true);\n      setError(null);\n\n      // Initialiser le service si nécessaire\n      await faceRecognitionService.initialize();\n      setStats(faceRecognitionService.getStats());\n\n      // Démarrer la détection en boucle\n      detectionIntervalRef.current = setInterval(async () => {\n        if (videoRef.current && canvasRef.current) {\n          await performDetection();\n        }\n      }, 1000); // Détection toutes les secondes\n    } catch (err) {\n      console.error('Erreur démarrage détection:', err);\n      setError('Erreur lors du démarrage de la détection');\n      setIsDetecting(false);\n    }\n  }, [isStreaming, isDetecting]);\n\n  /**\n   * Arrête la détection automatique\n   */\n  const stopDetection = useCallback(() => {\n    if (detectionIntervalRef.current) {\n      clearInterval(detectionIntervalRef.current);\n      detectionIntervalRef.current = null;\n    }\n    setIsDetecting(false);\n  }, []);\n\n  /**\n   * Effectue une détection sur l'image actuelle\n   */\n  const performDetection = useCallback(async () => {\n    if (!videoRef.current || !canvasRef.current) return;\n    try {\n      const video = videoRef.current;\n      const canvas = canvasRef.current;\n      const ctx = canvas.getContext('2d');\n      if (!ctx) return;\n\n      // Capturer l'image de la vidéo\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      ctx.drawImage(video, 0, 0);\n\n      // Effectuer la détection\n      const result = await faceRecognitionService.detectFaces(canvas);\n      setLastDetection(result);\n\n      // Callbacks\n      if (onDetection) {\n        onDetection(result);\n      }\n      if (onUserDetected && result.detectedUsers.length > 0) {\n        const users = result.detectedUsers.map(detection => detection.user);\n        onUserDetected(users);\n      }\n\n      // Dessiner les rectangles de détection\n      drawDetections(ctx, result);\n    } catch (err) {\n      console.error('Erreur détection:', err);\n    }\n  }, [onDetection, onUserDetected]);\n\n  /**\n   * Dessine les rectangles de détection sur le canvas\n   */\n  const drawDetections = (ctx, result) => {\n    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n\n    // Redessiner l'image vidéo\n    if (videoRef.current) {\n      ctx.drawImage(videoRef.current, 0, 0);\n    }\n\n    // Dessiner les détections\n    result.detectedUsers.forEach(detection => {\n      const {\n        boundingBox,\n        confidence,\n        user\n      } = detection;\n\n      // Rectangle de détection\n      ctx.strokeStyle = confidence > 0.7 ? '#4caf50' : '#ff9800';\n      ctx.lineWidth = 3;\n      ctx.strokeRect(boundingBox.x, boundingBox.y, boundingBox.width, boundingBox.height);\n\n      // Label avec nom et confiance\n      const label = `${user.firstName} ${user.lastName} (${Math.round(confidence * 100)}%)`;\n      ctx.fillStyle = confidence > 0.7 ? '#4caf50' : '#ff9800';\n      ctx.fillRect(boundingBox.x, boundingBox.y - 30, ctx.measureText(label).width + 10, 25);\n      ctx.fillStyle = 'white';\n      ctx.font = '14px Arial';\n      ctx.fillText(label, boundingBox.x + 5, boundingBox.y - 10);\n    });\n  };\n\n  /**\n   * Prend une photo manuelle\n   */\n  const takeSnapshot = useCallback(async () => {\n    await performDetection();\n  }, [performDetection]);\n\n  // Initialisation et nettoyage\n  useEffect(() => {\n    return () => {\n      stopCamera();\n    };\n  }, [stopCamera]);\n\n  // Auto-démarrage de la détection\n  useEffect(() => {\n    if (isStreaming && autoDetect && !isDetecting) {\n      startDetection();\n    }\n  }, [isStreaming, autoDetect, isDetecting, startDetection]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(CardContent, {\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Reconnaissance Faciale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), showControls && /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setSettingsOpen(true),\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: isStreaming ? \"outlined\" : \"contained\",\n              startIcon: isStreaming ? /*#__PURE__*/_jsxDEV(VideocamOffIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(VideocamIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 66\n              }, this),\n              onClick: isStreaming ? stopCamera : startCamera,\n              disabled: loading,\n              children: isStreaming ? 'Arrêter' : 'Démarrer'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          onClose: () => setError(null),\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 1,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: `${stats.registeredUsers} utilisateurs`,\n            color: \"primary\",\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${stats.totalEncodings} encodages`,\n            color: \"secondary\",\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), isDetecting && /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"D\\xE9tection active\",\n            color: \"success\",\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          position: \"relative\",\n          display: \"inline-block\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: videoRef,\n            autoPlay: true,\n            playsInline: true,\n            muted: true,\n            style: {\n              width: '100%',\n              maxWidth: width,\n              height: 'auto',\n              borderRadius: 8,\n              display: isStreaming ? 'block' : 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: canvasRef,\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              pointerEvents: 'none',\n              borderRadius: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n            position: \"absolute\",\n            top: \"50%\",\n            left: \"50%\",\n            sx: {\n              transform: 'translate(-50%, -50%)'\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), showControls && isStreaming && /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          justifyContent: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(PhotoCameraIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 28\n            }, this),\n            onClick: takeSnapshot,\n            children: \"Capturer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: isDetecting ? \"contained\" : \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 28\n            }, this),\n            onClick: isDetecting ? stopDetection : startDetection,\n            color: isDetecting ? \"error\" : \"primary\",\n            children: isDetecting ? 'Arrêter Détection' : 'Démarrer Détection'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), lastDetection && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: lastDetection.detectedUsers.length > 0 ? \"success\" : \"info\",\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Derni\\xE8re d\\xE9tection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), \" \", lastDetection.message]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this), lastDetection.detectedUsers.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            mt: 1,\n            children: lastDetection.detectedUsers.map((detection, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`,\n              color: \"success\",\n              size: \"small\",\n              sx: {\n                mr: 1,\n                mb: 1\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: settingsOpen,\n      onClose: () => setSettingsOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Param\\xE8tres de Reconnaissance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          sx: {\n            pt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Mod\\xE8les charg\\xE9s:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), \" \", stats.isInitialized ? 'Oui' : 'Non']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Utilisateurs enregistr\\xE9s:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), \" \", stats.registeredUsers]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total encodages:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), \" \", stats.totalEncodings]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"R\\xE9solution:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), \" \", width, \"x\", height]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setSettingsOpen(false),\n          children: \"Fermer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n};\n_s(FaceDetectionCamera, \"+qPXN1lpTw12cGeNNGKARAJqHH8=\");\n_c = FaceDetectionCamera;\nexport default FaceDetectionCamera;\nvar _c;\n$RefreshReg$(_c, \"FaceDetectionCamera\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "useCallback", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "Chip", "<PERSON><PERSON>", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Videocam", "VideocamIcon", "VideocamOff", "VideocamOffIcon", "PhotoCamera", "PhotoCameraIcon", "Refresh", "RefreshIcon", "Settings", "SettingsIcon", "faceRecognitionService", "jsxDEV", "_jsxDEV", "FaceDetectionCamera", "onDetection", "onUserDetected", "autoDetect", "showControls", "width", "height", "_s", "videoRef", "canvasRef", "streamRef", "detectionIntervalRef", "isStreaming", "setIsStreaming", "isDetecting", "setIsDetecting", "loading", "setLoading", "error", "setError", "lastDetection", "setLastDetection", "stats", "setStats", "getStats", "settingsOpen", "setSettingsOpen", "startCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "ideal", "facingMode", "audio", "current", "srcObject", "err", "console", "stopCamera", "getTracks", "for<PERSON>ach", "track", "stop", "stopDetection", "startDetection", "initialize", "setInterval", "performDetection", "clearInterval", "canvas", "ctx", "getContext", "videoWidth", "videoHeight", "drawImage", "result", "detectFaces", "detectedUsers", "length", "users", "map", "detection", "user", "drawDetections", "clearRect", "boundingBox", "confidence", "strokeStyle", "lineWidth", "strokeRect", "x", "y", "label", "firstName", "lastName", "Math", "round", "fillStyle", "fillRect", "measureText", "font", "fillText", "takeSnapshot", "children", "spacing", "display", "justifyContent", "alignItems", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "direction", "onClick", "startIcon", "disabled", "severity", "onClose", "flexWrap", "registeredUsers", "color", "size", "totalEncodings", "icon", "position", "ref", "autoPlay", "playsInline", "muted", "style", "max<PERSON><PERSON><PERSON>", "borderRadius", "top", "left", "pointerEvents", "sx", "transform", "mt", "message", "index", "mr", "mb", "open", "pt", "isInitialized", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceDetectionCamera.tsx"], "sourcesContent": ["import React, { useRef, useEffect, useState, useCallback } from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Alert,\n  CircularProgress,\n  Chip,\n  Stack,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  Videocam as VideocamIcon,\n  VideocamOff as VideocamOffIcon,\n  PhotoCamera as PhotoCameraIcon,\n  Refresh as RefreshIcon,\n  Settings as SettingsIcon\n} from '@mui/icons-material';\nimport { faceRecognitionService } from '../../services/faceRecognitionService';\nimport { FaceDetectionResult, User } from '../../types';\n\ninterface FaceDetectionCameraProps {\n  onDetection?: (result: FaceDetectionResult) => void;\n  onUserDetected?: (users: User[]) => void;\n  autoDetect?: boolean;\n  showControls?: boolean;\n  width?: number;\n  height?: number;\n}\n\nconst FaceDetectionCamera: React.FC<FaceDetectionCameraProps> = ({\n  onDetection,\n  onUserDetected,\n  autoDetect = true,\n  showControls = true,\n  width = 640,\n  height = 480\n}) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isDetecting, setIsDetecting] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [lastDetection, setLastDetection] = useState<FaceDetectionResult | null>(null);\n  const [stats, setStats] = useState(faceRecognitionService.getStats());\n  const [settingsOpen, setSettingsOpen] = useState(false);\n\n  /**\n   * Initialise la caméra\n   */\n  const startCamera = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Demander l'accès à la caméra\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: { ideal: width },\n          height: { ideal: height },\n          facingMode: 'user'\n        },\n        audio: false\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n      }\n\n    } catch (err) {\n      console.error('Erreur accès caméra:', err);\n      setError('Impossible d\\'accéder à la caméra. Vérifiez les permissions.');\n    } finally {\n      setLoading(false);\n    }\n  }, [width, height]);\n\n  /**\n   * Arrête la caméra\n   */\n  const stopCamera = useCallback(() => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    \n    if (videoRef.current) {\n      videoRef.current.srcObject = null;\n    }\n\n    setIsStreaming(false);\n    stopDetection();\n  }, []);\n\n  /**\n   * Démarre la détection automatique\n   */\n  const startDetection = useCallback(async () => {\n    if (!isStreaming || isDetecting) return;\n\n    try {\n      setIsDetecting(true);\n      setError(null);\n\n      // Initialiser le service si nécessaire\n      await faceRecognitionService.initialize();\n      setStats(faceRecognitionService.getStats());\n\n      // Démarrer la détection en boucle\n      detectionIntervalRef.current = setInterval(async () => {\n        if (videoRef.current && canvasRef.current) {\n          await performDetection();\n        }\n      }, 1000); // Détection toutes les secondes\n\n    } catch (err) {\n      console.error('Erreur démarrage détection:', err);\n      setError('Erreur lors du démarrage de la détection');\n      setIsDetecting(false);\n    }\n  }, [isStreaming, isDetecting]);\n\n  /**\n   * Arrête la détection automatique\n   */\n  const stopDetection = useCallback(() => {\n    if (detectionIntervalRef.current) {\n      clearInterval(detectionIntervalRef.current);\n      detectionIntervalRef.current = null;\n    }\n    setIsDetecting(false);\n  }, []);\n\n  /**\n   * Effectue une détection sur l'image actuelle\n   */\n  const performDetection = useCallback(async () => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    try {\n      const video = videoRef.current;\n      const canvas = canvasRef.current;\n      const ctx = canvas.getContext('2d');\n      \n      if (!ctx) return;\n\n      // Capturer l'image de la vidéo\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      ctx.drawImage(video, 0, 0);\n\n      // Effectuer la détection\n      const result = await faceRecognitionService.detectFaces(canvas);\n      \n      setLastDetection(result);\n      \n      // Callbacks\n      if (onDetection) {\n        onDetection(result);\n      }\n      \n      if (onUserDetected && result.detectedUsers.length > 0) {\n        const users = result.detectedUsers.map(detection => detection.user);\n        onUserDetected(users);\n      }\n\n      // Dessiner les rectangles de détection\n      drawDetections(ctx, result);\n\n    } catch (err) {\n      console.error('Erreur détection:', err);\n    }\n  }, [onDetection, onUserDetected]);\n\n  /**\n   * Dessine les rectangles de détection sur le canvas\n   */\n  const drawDetections = (ctx: CanvasRenderingContext2D, result: FaceDetectionResult) => {\n    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n    \n    // Redessiner l'image vidéo\n    if (videoRef.current) {\n      ctx.drawImage(videoRef.current, 0, 0);\n    }\n\n    // Dessiner les détections\n    result.detectedUsers.forEach(detection => {\n      const { boundingBox, confidence, user } = detection;\n      \n      // Rectangle de détection\n      ctx.strokeStyle = confidence > 0.7 ? '#4caf50' : '#ff9800';\n      ctx.lineWidth = 3;\n      ctx.strokeRect(boundingBox.x, boundingBox.y, boundingBox.width, boundingBox.height);\n      \n      // Label avec nom et confiance\n      const label = `${user.firstName} ${user.lastName} (${Math.round(confidence * 100)}%)`;\n      ctx.fillStyle = confidence > 0.7 ? '#4caf50' : '#ff9800';\n      ctx.fillRect(boundingBox.x, boundingBox.y - 30, ctx.measureText(label).width + 10, 25);\n      \n      ctx.fillStyle = 'white';\n      ctx.font = '14px Arial';\n      ctx.fillText(label, boundingBox.x + 5, boundingBox.y - 10);\n    });\n  };\n\n  /**\n   * Prend une photo manuelle\n   */\n  const takeSnapshot = useCallback(async () => {\n    await performDetection();\n  }, [performDetection]);\n\n  // Initialisation et nettoyage\n  useEffect(() => {\n    return () => {\n      stopCamera();\n    };\n  }, [stopCamera]);\n\n  // Auto-démarrage de la détection\n  useEffect(() => {\n    if (isStreaming && autoDetect && !isDetecting) {\n      startDetection();\n    }\n  }, [isStreaming, autoDetect, isDetecting, startDetection]);\n\n  return (\n    <Card>\n      <CardContent>\n        <Stack spacing={2}>\n          {/* En-tête */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n            <Typography variant=\"h6\">\n              Reconnaissance Faciale\n            </Typography>\n            \n            {showControls && (\n              <Stack direction=\"row\" spacing={1}>\n                <IconButton onClick={() => setSettingsOpen(true)}>\n                  <SettingsIcon />\n                </IconButton>\n                \n                <Button\n                  variant={isStreaming ? \"outlined\" : \"contained\"}\n                  startIcon={isStreaming ? <VideocamOffIcon /> : <VideocamIcon />}\n                  onClick={isStreaming ? stopCamera : startCamera}\n                  disabled={loading}\n                >\n                  {isStreaming ? 'Arrêter' : 'Démarrer'}\n                </Button>\n              </Stack>\n            )}\n          </Box>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <Alert severity=\"error\" onClose={() => setError(null)}>\n              {error}\n            </Alert>\n          )}\n\n          {/* Statistiques */}\n          <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\n            <Chip \n              label={`${stats.registeredUsers} utilisateurs`} \n              color=\"primary\" \n              size=\"small\" \n            />\n            <Chip \n              label={`${stats.totalEncodings} encodages`} \n              color=\"secondary\" \n              size=\"small\" \n            />\n            {isDetecting && (\n              <Chip \n                label=\"Détection active\" \n                color=\"success\" \n                size=\"small\"\n                icon={<CircularProgress size={16} />}\n              />\n            )}\n          </Stack>\n\n          {/* Zone vidéo */}\n          <Box position=\"relative\" display=\"inline-block\">\n            <video\n              ref={videoRef}\n              autoPlay\n              playsInline\n              muted\n              style={{\n                width: '100%',\n                maxWidth: width,\n                height: 'auto',\n                borderRadius: 8,\n                display: isStreaming ? 'block' : 'none'\n              }}\n            />\n            \n            <canvas\n              ref={canvasRef}\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                pointerEvents: 'none',\n                borderRadius: 8\n              }}\n            />\n\n            {loading && (\n              <Box\n                position=\"absolute\"\n                top=\"50%\"\n                left=\"50%\"\n                sx={{ transform: 'translate(-50%, -50%)' }}\n              >\n                <CircularProgress />\n              </Box>\n            )}\n          </Box>\n\n          {/* Contrôles */}\n          {showControls && isStreaming && (\n            <Stack direction=\"row\" spacing={2} justifyContent=\"center\">\n              <Button\n                variant=\"outlined\"\n                startIcon={<PhotoCameraIcon />}\n                onClick={takeSnapshot}\n              >\n                Capturer\n              </Button>\n              \n              <Button\n                variant={isDetecting ? \"contained\" : \"outlined\"}\n                startIcon={<RefreshIcon />}\n                onClick={isDetecting ? stopDetection : startDetection}\n                color={isDetecting ? \"error\" : \"primary\"}\n              >\n                {isDetecting ? 'Arrêter Détection' : 'Démarrer Détection'}\n              </Button>\n            </Stack>\n          )}\n\n          {/* Dernière détection */}\n          {lastDetection && (\n            <Alert \n              severity={lastDetection.detectedUsers.length > 0 ? \"success\" : \"info\"}\n              sx={{ mt: 2 }}\n            >\n              <Typography variant=\"body2\">\n                <strong>Dernière détection:</strong> {lastDetection.message}\n              </Typography>\n              {lastDetection.detectedUsers.length > 0 && (\n                <Box mt={1}>\n                  {lastDetection.detectedUsers.map((detection, index) => (\n                    <Chip\n                      key={index}\n                      label={`${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`}\n                      color=\"success\"\n                      size=\"small\"\n                      sx={{ mr: 1, mb: 1 }}\n                    />\n                  ))}\n                </Box>\n              )}\n            </Alert>\n          )}\n        </Stack>\n      </CardContent>\n\n      {/* Dialog des paramètres */}\n      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)}>\n        <DialogTitle>Paramètres de Reconnaissance</DialogTitle>\n        <DialogContent>\n          <Stack spacing={2} sx={{ pt: 1 }}>\n            <Typography variant=\"body2\">\n              <strong>Modèles chargés:</strong> {stats.isInitialized ? 'Oui' : 'Non'}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Utilisateurs enregistrés:</strong> {stats.registeredUsers}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Total encodages:</strong> {stats.totalEncodings}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Résolution:</strong> {width}x{height}\n            </Typography>\n          </Stack>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setSettingsOpen(false)}>Fermer</Button>\n        </DialogActions>\n      </Dialog>\n    </Card>\n  );\n};\n\nexport default FaceDetectionCamera;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,sBAAsB,QAAQ,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY/E,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,WAAW;EACXC,cAAc;EACdC,UAAU,GAAG,IAAI;EACjBC,YAAY,GAAG,IAAI;EACnBC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGvC,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAMwC,SAAS,GAAGxC,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAMyC,SAAS,GAAGzC,MAAM,CAAqB,IAAI,CAAC;EAClD,MAAM0C,oBAAoB,GAAG1C,MAAM,CAAwB,IAAI,CAAC;EAEhE,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAA6B,IAAI,CAAC;EACpF,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC0B,sBAAsB,CAAC2B,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;AACF;AACA;EACE,MAAMwD,WAAW,GAAGvD,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF6C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMS,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACL3B,KAAK,EAAE;YAAE4B,KAAK,EAAE5B;UAAM,CAAC;UACvBC,MAAM,EAAE;YAAE2B,KAAK,EAAE3B;UAAO,CAAC;UACzB4B,UAAU,EAAE;QACd,CAAC;QACDC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAI3B,QAAQ,CAAC4B,OAAO,EAAE;QACpB5B,QAAQ,CAAC4B,OAAO,CAACC,SAAS,GAAGT,MAAM;QACnClB,SAAS,CAAC0B,OAAO,GAAGR,MAAM;QAC1Bf,cAAc,CAAC,IAAI,CAAC;MACtB;IAEF,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,sBAAsB,EAAEoB,GAAG,CAAC;MAC1CnB,QAAQ,CAAC,8DAA8D,CAAC;IAC1E,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACZ,KAAK,EAAEC,MAAM,CAAC,CAAC;;EAEnB;AACF;AACA;EACE,MAAMkC,UAAU,GAAGpE,WAAW,CAAC,MAAM;IACnC,IAAIsC,SAAS,CAAC0B,OAAO,EAAE;MACrB1B,SAAS,CAAC0B,OAAO,CAACK,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC5DlC,SAAS,CAAC0B,OAAO,GAAG,IAAI;IAC1B;IAEA,IAAI5B,QAAQ,CAAC4B,OAAO,EAAE;MACpB5B,QAAQ,CAAC4B,OAAO,CAACC,SAAS,GAAG,IAAI;IACnC;IAEAxB,cAAc,CAAC,KAAK,CAAC;IACrBgC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMC,cAAc,GAAG1E,WAAW,CAAC,YAAY;IAC7C,IAAI,CAACwC,WAAW,IAAIE,WAAW,EAAE;IAEjC,IAAI;MACFC,cAAc,CAAC,IAAI,CAAC;MACpBI,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMtB,sBAAsB,CAACkD,UAAU,CAAC,CAAC;MACzCxB,QAAQ,CAAC1B,sBAAsB,CAAC2B,QAAQ,CAAC,CAAC,CAAC;;MAE3C;MACAb,oBAAoB,CAACyB,OAAO,GAAGY,WAAW,CAAC,YAAY;QACrD,IAAIxC,QAAQ,CAAC4B,OAAO,IAAI3B,SAAS,CAAC2B,OAAO,EAAE;UACzC,MAAMa,gBAAgB,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAEZ,CAAC,CAAC,OAAOX,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEoB,GAAG,CAAC;MACjDnB,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,WAAW,EAAEE,WAAW,CAAC,CAAC;;EAE9B;AACF;AACA;EACE,MAAM+B,aAAa,GAAGzE,WAAW,CAAC,MAAM;IACtC,IAAIuC,oBAAoB,CAACyB,OAAO,EAAE;MAChCc,aAAa,CAACvC,oBAAoB,CAACyB,OAAO,CAAC;MAC3CzB,oBAAoB,CAACyB,OAAO,GAAG,IAAI;IACrC;IACArB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMkC,gBAAgB,GAAG7E,WAAW,CAAC,YAAY;IAC/C,IAAI,CAACoC,QAAQ,CAAC4B,OAAO,IAAI,CAAC3B,SAAS,CAAC2B,OAAO,EAAE;IAE7C,IAAI;MACF,MAAMJ,KAAK,GAAGxB,QAAQ,CAAC4B,OAAO;MAC9B,MAAMe,MAAM,GAAG1C,SAAS,CAAC2B,OAAO;MAChC,MAAMgB,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAI,CAACD,GAAG,EAAE;;MAEV;MACAD,MAAM,CAAC9C,KAAK,GAAG2B,KAAK,CAACsB,UAAU;MAC/BH,MAAM,CAAC7C,MAAM,GAAG0B,KAAK,CAACuB,WAAW;MACjCH,GAAG,CAACI,SAAS,CAACxB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE1B;MACA,MAAMyB,MAAM,GAAG,MAAM5D,sBAAsB,CAAC6D,WAAW,CAACP,MAAM,CAAC;MAE/D9B,gBAAgB,CAACoC,MAAM,CAAC;;MAExB;MACA,IAAIxD,WAAW,EAAE;QACfA,WAAW,CAACwD,MAAM,CAAC;MACrB;MAEA,IAAIvD,cAAc,IAAIuD,MAAM,CAACE,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QACrD,MAAMC,KAAK,GAAGJ,MAAM,CAACE,aAAa,CAACG,GAAG,CAACC,SAAS,IAAIA,SAAS,CAACC,IAAI,CAAC;QACnE9D,cAAc,CAAC2D,KAAK,CAAC;MACvB;;MAEA;MACAI,cAAc,CAACb,GAAG,EAAEK,MAAM,CAAC;IAE7B,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,mBAAmB,EAAEoB,GAAG,CAAC;IACzC;EACF,CAAC,EAAE,CAACrC,WAAW,EAAEC,cAAc,CAAC,CAAC;;EAEjC;AACF;AACA;EACE,MAAM+D,cAAc,GAAGA,CAACb,GAA6B,EAAEK,MAA2B,KAAK;IACrFL,GAAG,CAACc,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEd,GAAG,CAACD,MAAM,CAAC9C,KAAK,EAAE+C,GAAG,CAACD,MAAM,CAAC7C,MAAM,CAAC;;IAExD;IACA,IAAIE,QAAQ,CAAC4B,OAAO,EAAE;MACpBgB,GAAG,CAACI,SAAS,CAAChD,QAAQ,CAAC4B,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;;IAEA;IACAqB,MAAM,CAACE,aAAa,CAACjB,OAAO,CAACqB,SAAS,IAAI;MACxC,MAAM;QAAEI,WAAW;QAAEC,UAAU;QAAEJ;MAAK,CAAC,GAAGD,SAAS;;MAEnD;MACAX,GAAG,CAACiB,WAAW,GAAGD,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;MAC1DhB,GAAG,CAACkB,SAAS,GAAG,CAAC;MACjBlB,GAAG,CAACmB,UAAU,CAACJ,WAAW,CAACK,CAAC,EAAEL,WAAW,CAACM,CAAC,EAAEN,WAAW,CAAC9D,KAAK,EAAE8D,WAAW,CAAC7D,MAAM,CAAC;;MAEnF;MACA,MAAMoE,KAAK,GAAG,GAAGV,IAAI,CAACW,SAAS,IAAIX,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,KAAK,CAACV,UAAU,GAAG,GAAG,CAAC,IAAI;MACrFhB,GAAG,CAAC2B,SAAS,GAAGX,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;MACxDhB,GAAG,CAAC4B,QAAQ,CAACb,WAAW,CAACK,CAAC,EAAEL,WAAW,CAACM,CAAC,GAAG,EAAE,EAAErB,GAAG,CAAC6B,WAAW,CAACP,KAAK,CAAC,CAACrE,KAAK,GAAG,EAAE,EAAE,EAAE,CAAC;MAEtF+C,GAAG,CAAC2B,SAAS,GAAG,OAAO;MACvB3B,GAAG,CAAC8B,IAAI,GAAG,YAAY;MACvB9B,GAAG,CAAC+B,QAAQ,CAACT,KAAK,EAAEP,WAAW,CAACK,CAAC,GAAG,CAAC,EAAEL,WAAW,CAACM,CAAC,GAAG,EAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;EACE,MAAMW,YAAY,GAAGhH,WAAW,CAAC,YAAY;IAC3C,MAAM6E,gBAAgB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA/E,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXsE,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACAtE,SAAS,CAAC,MAAM;IACd,IAAI0C,WAAW,IAAIT,UAAU,IAAI,CAACW,WAAW,EAAE;MAC7CgC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAClC,WAAW,EAAET,UAAU,EAAEW,WAAW,EAAEgC,cAAc,CAAC,CAAC;EAE1D,oBACE/C,OAAA,CAACxB,IAAI;IAAA8G,QAAA,gBACHtF,OAAA,CAACvB,WAAW;MAAA6G,QAAA,eACVtF,OAAA,CAAClB,KAAK;QAACyG,OAAO,EAAE,CAAE;QAAAD,QAAA,gBAEhBtF,OAAA,CAAC1B,GAAG;UAACkH,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAAAJ,QAAA,gBACpEtF,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAEzB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ1F,YAAY,iBACXL,OAAA,CAAClB,KAAK;YAACkH,SAAS,EAAC,KAAK;YAACT,OAAO,EAAE,CAAE;YAAAD,QAAA,gBAChCtF,OAAA,CAACjB,UAAU;cAACkH,OAAO,EAAEA,CAAA,KAAMtE,eAAe,CAAC,IAAI,CAAE;cAAA2D,QAAA,eAC/CtF,OAAA,CAACH,YAAY;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEb/F,OAAA,CAACzB,MAAM;cACLoH,OAAO,EAAE9E,WAAW,GAAG,UAAU,GAAG,WAAY;cAChDqF,SAAS,EAAErF,WAAW,gBAAGb,OAAA,CAACT,eAAe;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG/F,OAAA,CAACX,YAAY;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChEE,OAAO,EAAEpF,WAAW,GAAG4B,UAAU,GAAGb,WAAY;cAChDuE,QAAQ,EAAElF,OAAQ;cAAAqE,QAAA,EAEjBzE,WAAW,GAAG,SAAS,GAAG;YAAU;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL5E,KAAK,iBACJnB,OAAA,CAACrB,KAAK;UAACyH,QAAQ,EAAC,OAAO;UAACC,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,IAAI,CAAE;UAAAkE,QAAA,EACnDnE;QAAK;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAGD/F,OAAA,CAAClB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACT,OAAO,EAAE,CAAE;UAACe,QAAQ,EAAC,MAAM;UAAAhB,QAAA,gBAChDtF,OAAA,CAACnB,IAAI;YACH8F,KAAK,EAAE,GAAGpD,KAAK,CAACgF,eAAe,eAAgB;YAC/CC,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACF/F,OAAA,CAACnB,IAAI;YACH8F,KAAK,EAAE,GAAGpD,KAAK,CAACmF,cAAc,YAAa;YAC3CF,KAAK,EAAC,WAAW;YACjBC,IAAI,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,EACDhF,WAAW,iBACVf,OAAA,CAACnB,IAAI;YACH8F,KAAK,EAAC,qBAAkB;YACxB6B,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZE,IAAI,eAAE3G,OAAA,CAACpB,gBAAgB;cAAC6H,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGR/F,OAAA,CAAC1B,GAAG;UAACsI,QAAQ,EAAC,UAAU;UAACpB,OAAO,EAAC,cAAc;UAAAF,QAAA,gBAC7CtF,OAAA;YACE6G,GAAG,EAAEpG,QAAS;YACdqG,QAAQ;YACRC,WAAW;YACXC,KAAK;YACLC,KAAK,EAAE;cACL3G,KAAK,EAAE,MAAM;cACb4G,QAAQ,EAAE5G,KAAK;cACfC,MAAM,EAAE,MAAM;cACd4G,YAAY,EAAE,CAAC;cACf3B,OAAO,EAAE3E,WAAW,GAAG,OAAO,GAAG;YACnC;UAAE;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF/F,OAAA;YACE6G,GAAG,EAAEnG,SAAU;YACfuG,KAAK,EAAE;cACLL,QAAQ,EAAE,UAAU;cACpBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACP/G,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACd+G,aAAa,EAAE,MAAM;cACrBH,YAAY,EAAE;YAChB;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAED9E,OAAO,iBACNjB,OAAA,CAAC1B,GAAG;YACFsI,QAAQ,EAAC,UAAU;YACnBQ,GAAG,EAAC,KAAK;YACTC,IAAI,EAAC,KAAK;YACVE,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAwB,CAAE;YAAAlC,QAAA,eAE3CtF,OAAA,CAACpB,gBAAgB;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL1F,YAAY,IAAIQ,WAAW,iBAC1Bb,OAAA,CAAClB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACT,OAAO,EAAE,CAAE;UAACE,cAAc,EAAC,QAAQ;UAAAH,QAAA,gBACxDtF,OAAA,CAACzB,MAAM;YACLoH,OAAO,EAAC,UAAU;YAClBO,SAAS,eAAElG,OAAA,CAACP,eAAe;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BE,OAAO,EAAEZ,YAAa;YAAAC,QAAA,EACvB;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET/F,OAAA,CAACzB,MAAM;YACLoH,OAAO,EAAE5E,WAAW,GAAG,WAAW,GAAG,UAAW;YAChDmF,SAAS,eAAElG,OAAA,CAACL,WAAW;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BE,OAAO,EAAElF,WAAW,GAAG+B,aAAa,GAAGC,cAAe;YACtDyD,KAAK,EAAEzF,WAAW,GAAG,OAAO,GAAG,SAAU;YAAAuE,QAAA,EAExCvE,WAAW,GAAG,mBAAmB,GAAG;UAAoB;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACR,EAGA1E,aAAa,iBACZrB,OAAA,CAACrB,KAAK;UACJyH,QAAQ,EAAE/E,aAAa,CAACuC,aAAa,CAACC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAO;UACtE0D,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBAEdtF,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAAL,QAAA,gBACzBtF,OAAA;cAAAsF,QAAA,EAAQ;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC1E,aAAa,CAACqG,OAAO;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,EACZ1E,aAAa,CAACuC,aAAa,CAACC,MAAM,GAAG,CAAC,iBACrC7D,OAAA,CAAC1B,GAAG;YAACmJ,EAAE,EAAE,CAAE;YAAAnC,QAAA,EACRjE,aAAa,CAACuC,aAAa,CAACG,GAAG,CAAC,CAACC,SAAS,EAAE2D,KAAK,kBAChD3H,OAAA,CAACnB,IAAI;cAEH8F,KAAK,EAAE,GAAGX,SAAS,CAACC,IAAI,CAACW,SAAS,IAAIZ,SAAS,CAACC,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,KAAK,CAACf,SAAS,CAACK,UAAU,GAAG,GAAG,CAAC,IAAK;cAC7GmC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZc,EAAE,EAAE;gBAAEK,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE;YAAE,GAJhBF,KAAK;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGd/F,OAAA,CAAChB,MAAM;MAAC8I,IAAI,EAAEpG,YAAa;MAAC2E,OAAO,EAAEA,CAAA,KAAM1E,eAAe,CAAC,KAAK,CAAE;MAAA2D,QAAA,gBAChEtF,OAAA,CAACf,WAAW;QAAAqG,QAAA,EAAC;MAA4B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACvD/F,OAAA,CAACd,aAAa;QAAAoG,QAAA,eACZtF,OAAA,CAAClB,KAAK;UAACyG,OAAO,EAAE,CAAE;UAACgC,EAAE,EAAE;YAAEQ,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBAC/BtF,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAAL,QAAA,gBACzBtF,OAAA;cAAAsF,QAAA,EAAQ;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxE,KAAK,CAACyG,aAAa,GAAG,KAAK,GAAG,KAAK;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACb/F,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAAL,QAAA,gBACzBtF,OAAA;cAAAsF,QAAA,EAAQ;YAAyB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxE,KAAK,CAACgF,eAAe;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACb/F,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAAL,QAAA,gBACzBtF,OAAA;cAAAsF,QAAA,EAAQ;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxE,KAAK,CAACmF,cAAc;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACb/F,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAAL,QAAA,gBACzBtF,OAAA;cAAAsF,QAAA,EAAQ;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACzF,KAAK,EAAC,GAAC,EAACC,MAAM;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChB/F,OAAA,CAACb,aAAa;QAAAmG,QAAA,eACZtF,OAAA,CAACzB,MAAM;UAAC0H,OAAO,EAAEA,CAAA,KAAMtE,eAAe,CAAC,KAAK,CAAE;UAAA2D,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAACvF,EAAA,CAtXIP,mBAAuD;AAAAgI,EAAA,GAAvDhI,mBAAuD;AAwX7D,eAAeA,mBAAmB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}