{"ast": null, "code": "export var TinyYolov2SizeType;\n(function (TinyYolov2SizeType) {\n  TinyYolov2SizeType[TinyYolov2SizeType[\"XS\"] = 224] = \"XS\";\n  TinyYolov2SizeType[TinyYolov2SizeType[\"SM\"] = 320] = \"SM\";\n  TinyYolov2SizeType[TinyYolov2SizeType[\"MD\"] = 416] = \"MD\";\n  TinyYolov2SizeType[TinyYolov2SizeType[\"LG\"] = 608] = \"LG\";\n})(TinyYolov2SizeType || (TinyYolov2SizeType = {}));\nvar TinyYolov2Options = /** @class */function () {\n  function TinyYolov2Options(_a) {\n    var _b = _a === void 0 ? {} : _a,\n      inputSize = _b.inputSize,\n      scoreThreshold = _b.scoreThreshold;\n    this._name = 'TinyYolov2Options';\n    this._inputSize = inputSize || 416;\n    this._scoreThreshold = scoreThreshold || 0.5;\n    if (typeof this._inputSize !== 'number' || this._inputSize % 32 !== 0) {\n      throw new Error(this._name + \" - expected inputSize to be a number divisible by 32\");\n    }\n    if (typeof this._scoreThreshold !== 'number' || this._scoreThreshold <= 0 || this._scoreThreshold >= 1) {\n      throw new Error(this._name + \" - expected scoreThreshold to be a number between 0 and 1\");\n    }\n  }\n  Object.defineProperty(TinyYolov2Options.prototype, \"inputSize\", {\n    get: function () {\n      return this._inputSize;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(TinyYolov2Options.prototype, \"scoreThreshold\", {\n    get: function () {\n      return this._scoreThreshold;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  return TinyYolov2Options;\n}();\nexport { TinyYolov2Options };", "map": {"version": 3, "names": ["TinyYolov2SizeType", "TinyYolov2Options", "_a", "_b", "inputSize", "scoreThreshold", "_name", "_inputSize", "_scoreThreshold", "Error", "Object", "defineProperty", "prototype", "get"], "sources": ["../../../src/tinyYolov2/TinyYolov2Options.ts"], "sourcesContent": [null], "mappings": "AAAA,WAAYA,kBAKX;AALD,WAAYA,kBAAkB;EAC5BA,kBAAA,CAAAA,kBAAA,oBAAQ;EACRA,kBAAA,CAAAA,kBAAA,oBAAQ;EACRA,kBAAA,CAAAA,kBAAA,oBAAQ;EACRA,kBAAA,CAAAA,kBAAA,oBAAQ;AACV,CAAC,EALWA,kBAAkB,KAAlBA,kBAAkB;AAY9B,IAAAC,iBAAA;EAME,SAAAA,kBAAYC,EAAsD;QAAtDC,EAAA,GAAAD,EAAA,mBAAAA,EAAsD;MAApDE,SAAA,GAAAD,EAAA,CAAAC,SAAS;MAAEC,cAAA,GAAAF,EAAA,CAAAE,cAAc;IAL7B,KAAAC,KAAK,GAAW,mBAAmB;IAM3C,IAAI,CAACC,UAAU,GAAGH,SAAS,IAAI,GAAG;IAClC,IAAI,CAACI,eAAe,GAAGH,cAAc,IAAI,GAAG;IAE5C,IAAI,OAAO,IAAI,CAACE,UAAU,KAAK,QAAQ,IAAI,IAAI,CAACA,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE;MACrE,MAAM,IAAIE,KAAK,CAAI,IAAI,CAACH,KAAK,yDAAsD,CAAC;;IAGtF,IAAI,OAAO,IAAI,CAACE,eAAe,KAAK,QAAQ,IAAI,IAAI,CAACA,eAAe,IAAI,CAAC,IAAI,IAAI,CAACA,eAAe,IAAI,CAAC,EAAE;MACtG,MAAM,IAAIC,KAAK,CAAI,IAAI,CAACH,KAAK,8DAA2D,CAAC;;EAE7F;EAEAI,MAAA,CAAAC,cAAA,CAAIV,iBAAA,CAAAW,SAAA,aAAS;SAAb,SAAAC,CAAA;MAA0B,OAAO,IAAI,CAACN,UAAU;IAAC,CAAC;;;;EAClDG,MAAA,CAAAC,cAAA,CAAIV,iBAAA,CAAAW,SAAA,kBAAc;SAAlB,SAAAC,CAAA;MAA+B,OAAO,IAAI,CAACL,eAAe;IAAC,CAAC;;;;EAC9D,OAAAP,iBAAC;AAAD,CAAC,CArBD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}