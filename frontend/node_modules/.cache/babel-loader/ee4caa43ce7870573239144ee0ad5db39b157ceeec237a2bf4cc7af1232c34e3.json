{"ast": null, "code": "import { round } from '../utils';\nvar FaceMatch = /** @class */function () {\n  function FaceMatch(label, distance) {\n    this._label = label;\n    this._distance = distance;\n  }\n  Object.defineProperty(FaceMatch.prototype, \"label\", {\n    get: function () {\n      return this._label;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(FaceMatch.prototype, \"distance\", {\n    get: function () {\n      return this._distance;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  FaceMatch.prototype.toString = function (withDistance) {\n    if (withDistance === void 0) {\n      withDistance = true;\n    }\n    return \"\" + this.label + (withDistance ? \" (\" + round(this.distance) + \")\" : '');\n  };\n  return FaceMatch;\n}();\nexport { FaceMatch };", "map": {"version": 3, "names": ["round", "FaceMatch", "label", "distance", "_label", "_distance", "Object", "defineProperty", "prototype", "get", "toString", "withDistance"], "sources": ["../../../src/classes/FaceMatch.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,KAAK,QAAQ,UAAU;AAOhC,IAAAC,SAAA;EAIE,SAAAA,UAAYC,KAAa,EAAEC,QAAgB;IACzC,IAAI,CAACC,MAAM,GAAGF,KAAK;IACnB,IAAI,CAACG,SAAS,GAAGF,QAAQ;EAC3B;EAEAG,MAAA,CAAAC,cAAA,CAAWN,SAAA,CAAAO,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACL,MAAM;IAAC,CAAC;;;;EACjDE,MAAA,CAAAC,cAAA,CAAWN,SAAA,CAAAO,SAAA,YAAQ;SAAnB,SAAAC,CAAA;MAAgC,OAAO,IAAI,CAACJ,SAAS;IAAC,CAAC;;;;EAEhDJ,SAAA,CAAAO,SAAA,CAAAE,QAAQ,GAAf,UAAgBC,YAA4B;IAA5B,IAAAA,YAAA;MAAAA,YAAA,OAA4B;IAAA;IAC1C,OAAO,KAAG,IAAI,CAACT,KAAK,IAAGS,YAAY,GAAG,OAAKX,KAAK,CAAC,IAAI,CAACG,QAAQ,CAAC,MAAG,GAAG,EAAE,CAAE;EAC3E,CAAC;EACH,OAAAF,SAAC;AAAD,CAAC,CAfD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}