{"ast": null, "code": "import { FaceDetection } from '../classes/FaceDetection';\nexport function isWithFaceDetection(obj) {\n  return obj['detection'] instanceof FaceDetection;\n}\nexport function extendWithFaceDetection(sourceObj, detection) {\n  var extension = {\n    detection: detection\n  };\n  return Object.assign({}, sourceObj, extension);\n}", "map": {"version": 3, "names": ["FaceDetection", "isWithFaceDetection", "obj", "extendWithFaceDetection", "sourceObj", "detection", "extension", "Object", "assign"], "sources": ["../../../src/factories/WithFaceDetection.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,aAAa,QAAQ,0BAA0B;AAMxD,OAAM,SAAUC,mBAAmBA,CAACC,GAAQ;EAC1C,OAAOA,GAAG,CAAC,WAAW,CAAC,YAAYF,aAAa;AAClD;AAEA,OAAM,SAAUG,uBAAuBA,CAGrCC,SAAkB,EAClBC,SAAwB;EAGxB,IAAMC,SAAS,GAAG;IAAED,SAAS,EAAAA;EAAA,CAAE;EAC/B,OAAOE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEJ,SAAS,EAAEE,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}