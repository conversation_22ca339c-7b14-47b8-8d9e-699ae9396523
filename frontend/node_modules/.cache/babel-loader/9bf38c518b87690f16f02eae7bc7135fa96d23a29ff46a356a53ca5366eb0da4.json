{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Card, CardContent, Stack, Box, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Button, Dialog, DialogTitle, DialogContent, DialogActions, LinearProgress } from '@mui/material';\nimport { CheckCircle as CheckCircleIcon, Cancel as CancelIcon, Warning as WarningIcon, Schedule as ScheduleIcon, Face as FaceIcon, PhotoCamera as PhotoCameraIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { supabaseService } from '../services/supabaseService';\nimport FaceRegistration from '../components/FaceRecognition/FaceRegistration';\nimport { AttendanceStatus } from '../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user,\n    signOut\n  } = useAuth();\n  const [attendanceRecords, setAttendanceRecords] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);\n\n  /**\n   * Charge les données de l'étudiant\n   */\n  const loadStudentData = async () => {\n    if (!user) return;\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les présences de l'étudiant\n      const studentAttendance = await supabaseService.getAttendanceByStudent(user.id);\n      setAttendanceRecords(studentAttendance);\n\n      // Charger les cours (pour l'instant tous les cours)\n      const allCourses = await supabaseService.getCourses();\n      setCourses(allCourses);\n    } catch (err) {\n      console.error('Erreur chargement données étudiant:', err);\n      setError('Impossible de charger vos données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadStudentData();\n  }, [user]);\n\n  /**\n   * Calcule les statistiques de présence\n   */\n  const getAttendanceStats = () => {\n    const total = attendanceRecords.length;\n    const present = attendanceRecords.filter(r => r.status === AttendanceStatus.PRESENT).length;\n    const late = attendanceRecords.filter(r => r.status === AttendanceStatus.LATE).length;\n    const absent = attendanceRecords.filter(r => r.status === AttendanceStatus.ABSENT).length;\n    const attendanceRate = total > 0 ? Math.round((present + late) / total * 100) : 0;\n    return {\n      total,\n      present,\n      late,\n      absent,\n      attendanceRate\n    };\n  };\n\n  /**\n   * Obtient l'icône pour un statut de présence\n   */\n  const getStatusIcon = status => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 16\n        }, this);\n      case AttendanceStatus.LATE:\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 16\n        }, this);\n      case AttendanceStatus.ABSENT:\n        return /*#__PURE__*/_jsxDEV(CancelIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ScheduleIcon, {\n          color: \"disabled\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  /**\n   * Obtient la couleur pour un statut de présence\n   */\n  const getStatusColor = status => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return 'success';\n      case AttendanceStatus.LATE:\n        return 'warning';\n      case AttendanceStatus.ABSENT:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const stats = getAttendanceStats();\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Utilisateur non connect\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          gutterBottom: true,\n          children: \"Dashboard \\xC9tudiant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: [\"Bienvenue, \", user.firstName, \" \", user.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 24\n          }, this),\n          onClick: () => setFaceRegistrationOpen(true),\n          children: \"G\\xE9rer mes Photos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"error\",\n          onClick: signOut,\n          children: \"D\\xE9connexion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      direction: {\n        xs: 'column',\n        md: 'row'\n      },\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              color: \"success\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.present\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Pr\\xE9sences\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n              color: \"warning\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.late\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Retards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(CancelIcon, {\n              color: \"error\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.absent\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Absences\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n              color: \"primary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: [stats.attendanceRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Taux Pr\\xE9sence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Historique des Pr\\xE9sences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), attendanceRecords.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Aucun enregistrement de pr\\xE9sence trouv\\xE9.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Cours\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Heure\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Statut\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"M\\xE9thode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Notes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: attendanceRecords.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).map(record => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: new Date(record.date).toLocaleDateString('fr-FR')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: record.course.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: record.course.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: new Date(record.time).toLocaleTimeString('fr-FR')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    spacing: 1,\n                    alignItems: \"center\",\n                    children: [getStatusIcon(record.status), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: record.status,\n                      color: getStatusColor(record.status),\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: record.method,\n                    variant: \"outlined\",\n                    size: \"small\",\n                    icon: record.method === 'face_recognition' ? /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 72\n                    }, this) : undefined\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: record.notes || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this)]\n              }, record.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: faceRegistrationOpen,\n      onClose: () => setFaceRegistrationOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PhotoCameraIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Gestion de mes Photos Faciales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Enregistrez vos photos faciales pour \\xEAtre reconnu automatiquement lors des sessions de pr\\xE9sence.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FaceRegistration, {\n          user: user,\n          onRegistrationComplete: success => {\n            if (success) {\n              // Optionnel: recharger les données ou afficher un message\n              console.log('Photos faciales mises à jour avec succès');\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setFaceRegistrationOpen(false),\n          children: \"Fermer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"weEZFEDmZR+VZXkclyVWcrllFAM=\", false, function () {\n  return [useAuth];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Box", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "LinearProgress", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Schedule", "ScheduleIcon", "Face", "FaceIcon", "PhotoCamera", "PhotoCameraIcon", "useAuth", "supabaseService", "FaceRegistration", "AttendanceStatus", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "signOut", "attendanceRecords", "setAttendanceRecords", "courses", "setCourses", "loading", "setLoading", "error", "setError", "faceRegistrationOpen", "setFaceRegistrationOpen", "loadStudentData", "studentAttendance", "getAttendanceByStudent", "id", "allCourses", "getCourses", "err", "console", "getAttendanceStats", "total", "length", "present", "filter", "r", "status", "PRESENT", "late", "LATE", "absent", "ABSENT", "attendanceRate", "Math", "round", "getStatusIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "stats", "children", "severity", "max<PERSON><PERSON><PERSON>", "sx", "py", "display", "justifyContent", "alignItems", "mb", "variant", "gutterBottom", "firstName", "lastName", "direction", "spacing", "startIcon", "onClick", "onClose", "xs", "md", "flex", "fontSize", "component", "sort", "a", "b", "Date", "date", "getTime", "map", "record", "toLocaleDateString", "fontWeight", "course", "name", "code", "time", "toLocaleTimeString", "label", "size", "method", "icon", "undefined", "notes", "open", "fullWidth", "onRegistrationComplete", "success", "log", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Container,\n  Typography,\n  Card,\n  CardContent,\n  Stack,\n  Box,\n  Alert,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  LinearProgress\n} from '@mui/material';\nimport {\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Schedule as ScheduleIcon,\n  Face as FaceIcon,\n  PhotoCamera as PhotoCameraIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { supabaseService } from '../services/supabaseService';\nimport FaceRegistration from '../components/FaceRecognition/FaceRegistration';\nimport { AttendanceRecord, Course, AttendanceStatus } from '../types';\n\nconst StudentDashboard: React.FC = () => {\n  const { user, signOut } = useAuth();\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);\n\n  /**\n   * Charge les données de l'étudiant\n   */\n  const loadStudentData = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les présences de l'étudiant\n      const studentAttendance = await supabaseService.getAttendanceByStudent(user.id);\n      setAttendanceRecords(studentAttendance);\n\n      // Charger les cours (pour l'instant tous les cours)\n      const allCourses = await supabaseService.getCourses();\n      setCourses(allCourses);\n\n    } catch (err) {\n      console.error('Erreur chargement données étudiant:', err);\n      setError('Impossible de charger vos données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadStudentData();\n  }, [user]);\n\n  /**\n   * Calcule les statistiques de présence\n   */\n  const getAttendanceStats = () => {\n    const total = attendanceRecords.length;\n    const present = attendanceRecords.filter(r => r.status === AttendanceStatus.PRESENT).length;\n    const late = attendanceRecords.filter(r => r.status === AttendanceStatus.LATE).length;\n    const absent = attendanceRecords.filter(r => r.status === AttendanceStatus.ABSENT).length;\n    const attendanceRate = total > 0 ? Math.round(((present + late) / total) * 100) : 0;\n\n    return { total, present, late, absent, attendanceRate };\n  };\n\n  /**\n   * Obtient l'icône pour un statut de présence\n   */\n  const getStatusIcon = (status: AttendanceStatus) => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return <CheckCircleIcon color=\"success\" />;\n      case AttendanceStatus.LATE:\n        return <WarningIcon color=\"warning\" />;\n      case AttendanceStatus.ABSENT:\n        return <CancelIcon color=\"error\" />;\n      default:\n        return <ScheduleIcon color=\"disabled\" />;\n    }\n  };\n\n  /**\n   * Obtient la couleur pour un statut de présence\n   */\n  const getStatusColor = (status: AttendanceStatus) => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return 'success';\n      case AttendanceStatus.LATE:\n        return 'warning';\n      case AttendanceStatus.ABSENT:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const stats = getAttendanceStats();\n\n  if (!user) {\n    return (\n      <Container>\n        <Alert severity=\"error\">\n          Utilisateur non connecté\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      {/* En-tête */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={4}>\n        <Box>\n          <Typography variant=\"h3\" gutterBottom>\n            Dashboard Étudiant\n          </Typography>\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            Bienvenue, {user.firstName} {user.lastName}\n          </Typography>\n        </Box>\n        \n        <Stack direction=\"row\" spacing={2}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<FaceIcon />}\n            onClick={() => setFaceRegistrationOpen(true)}\n          >\n            Gérer mes Photos\n          </Button>\n          <Button\n            variant=\"outlined\"\n            color=\"error\"\n            onClick={signOut}\n          >\n            Déconnexion\n          </Button>\n        </Stack>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {loading && <LinearProgress sx={{ mb: 3 }} />}\n\n      {/* Statistiques de présence */}\n      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <CheckCircleIcon color=\"success\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.present}</Typography>\n                <Typography color=\"text.secondary\">Présences</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <WarningIcon color=\"warning\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.late}</Typography>\n                <Typography color=\"text.secondary\">Retards</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <CancelIcon color=\"error\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.absent}</Typography>\n                <Typography color=\"text.secondary\">Absences</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <ScheduleIcon color=\"primary\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.attendanceRate}%</Typography>\n                <Typography color=\"text.secondary\">Taux Présence</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Stack>\n\n      {/* Historique des présences */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Historique des Présences\n          </Typography>\n          \n          {attendanceRecords.length === 0 ? (\n            <Alert severity=\"info\">\n              Aucun enregistrement de présence trouvé.\n            </Alert>\n          ) : (\n            <TableContainer component={Paper} variant=\"outlined\">\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Date</TableCell>\n                    <TableCell>Cours</TableCell>\n                    <TableCell>Heure</TableCell>\n                    <TableCell>Statut</TableCell>\n                    <TableCell>Méthode</TableCell>\n                    <TableCell>Notes</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {attendanceRecords\n                    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n                    .map((record) => (\n                    <TableRow key={record.id}>\n                      <TableCell>\n                        {new Date(record.date).toLocaleDateString('fr-FR')}\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\n                          {record.course.name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {record.course.code}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        {new Date(record.time).toLocaleTimeString('fr-FR')}\n                      </TableCell>\n                      <TableCell>\n                        <Stack direction=\"row\" spacing={1} alignItems=\"center\">\n                          {getStatusIcon(record.status)}\n                          <Chip\n                            label={record.status}\n                            color={getStatusColor(record.status) as any}\n                            size=\"small\"\n                          />\n                        </Stack>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={record.method}\n                          variant=\"outlined\"\n                          size=\"small\"\n                          icon={record.method === 'face_recognition' ? <FaceIcon /> : undefined}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          {record.notes || '-'}\n                        </Typography>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Dialog d'enregistrement facial */}\n      <Dialog \n        open={faceRegistrationOpen} \n        onClose={() => setFaceRegistrationOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n            <PhotoCameraIcon />\n            <Typography variant=\"h6\">\n              Gestion de mes Photos Faciales\n            </Typography>\n          </Stack>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            Enregistrez vos photos faciales pour être reconnu automatiquement \n            lors des sessions de présence.\n          </Typography>\n          \n          <FaceRegistration\n            user={user}\n            onRegistrationComplete={(success) => {\n              if (success) {\n                // Optionnel: recharger les données ou afficher un message\n                console.log('Photos faciales mises à jour avec succès');\n              }\n            }}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setFaceRegistrationOpen(false)}>\n            Fermer\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,OAAOC,gBAAgB,MAAM,gDAAgD;AAC7E,SAAmCC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnC,MAAM,CAACU,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAqB,EAAE,CAAC;EAClF,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEvE;AACF;AACA;EACE,MAAMuD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACZ,IAAI,EAAE;IAEX,IAAI;MACFO,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMI,iBAAiB,GAAG,MAAMpB,eAAe,CAACqB,sBAAsB,CAACd,IAAI,CAACe,EAAE,CAAC;MAC/EZ,oBAAoB,CAACU,iBAAiB,CAAC;;MAEvC;MACA,MAAMG,UAAU,GAAG,MAAMvB,eAAe,CAACwB,UAAU,CAAC,CAAC;MACrDZ,UAAU,CAACW,UAAU,CAAC;IAExB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACX,KAAK,CAAC,qCAAqC,EAAEU,GAAG,CAAC;MACzDT,QAAQ,CAAC,mCAAmC,CAAC;IAC/C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACdsD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACZ,IAAI,CAAC,CAAC;;EAEV;AACF;AACA;EACE,MAAMoB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,KAAK,GAAGnB,iBAAiB,CAACoB,MAAM;IACtC,MAAMC,OAAO,GAAGrB,iBAAiB,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK/B,gBAAgB,CAACgC,OAAO,CAAC,CAACL,MAAM;IAC3F,MAAMM,IAAI,GAAG1B,iBAAiB,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK/B,gBAAgB,CAACkC,IAAI,CAAC,CAACP,MAAM;IACrF,MAAMQ,MAAM,GAAG5B,iBAAiB,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK/B,gBAAgB,CAACoC,MAAM,CAAC,CAACT,MAAM;IACzF,MAAMU,cAAc,GAAGX,KAAK,GAAG,CAAC,GAAGY,IAAI,CAACC,KAAK,CAAE,CAACX,OAAO,GAAGK,IAAI,IAAIP,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;IAEnF,OAAO;MAAEA,KAAK;MAAEE,OAAO;MAAEK,IAAI;MAAEE,MAAM;MAAEE;IAAe,CAAC;EACzD,CAAC;;EAED;AACF;AACA;EACE,MAAMG,aAAa,GAAIT,MAAwB,IAAK;IAClD,QAAQA,MAAM;MACZ,KAAK/B,gBAAgB,CAACgC,OAAO;QAC3B,oBAAO9B,OAAA,CAAChB,eAAe;UAACuD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5C,KAAK7C,gBAAgB,CAACkC,IAAI;QACxB,oBAAOhC,OAAA,CAACZ,WAAW;UAACmD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAK7C,gBAAgB,CAACoC,MAAM;QAC1B,oBAAOlC,OAAA,CAACd,UAAU;UAACqD,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC;QACE,oBAAO3C,OAAA,CAACV,YAAY;UAACiD,KAAK,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5C;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMC,cAAc,GAAIf,MAAwB,IAAK;IACnD,QAAQA,MAAM;MACZ,KAAK/B,gBAAgB,CAACgC,OAAO;QAC3B,OAAO,SAAS;MAClB,KAAKhC,gBAAgB,CAACkC,IAAI;QACxB,OAAO,SAAS;MAClB,KAAKlC,gBAAgB,CAACoC,MAAM;QAC1B,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMW,KAAK,GAAGtB,kBAAkB,CAAC,CAAC;EAElC,IAAI,CAACpB,IAAI,EAAE;IACT,oBACEH,OAAA,CAACtC,SAAS;MAAAoF,QAAA,eACR9C,OAAA,CAAChC,KAAK;QAAC+E,QAAQ,EAAC,OAAO;QAAAD,QAAA,EAAC;MAExB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACE3C,OAAA,CAACtC,SAAS;IAACsF,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAJ,QAAA,gBAErC9C,OAAA,CAACjC,GAAG;MAACoF,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAR,QAAA,gBAC3E9C,OAAA,CAACjC,GAAG;QAAA+E,QAAA,gBACF9C,OAAA,CAACrC,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,EAAC;QAEtC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3C,OAAA,CAACrC,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAAChB,KAAK,EAAC,gBAAgB;UAAAO,QAAA,GAAC,aACnC,EAAC3C,IAAI,CAACsD,SAAS,EAAC,GAAC,EAACtD,IAAI,CAACuD,QAAQ;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN3C,OAAA,CAAClC,KAAK;QAAC6F,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAAd,QAAA,gBAChC9C,OAAA,CAACvB,MAAM;UACL8E,OAAO,EAAC,UAAU;UAClBM,SAAS,eAAE7D,OAAA,CAACR,QAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBmB,OAAO,EAAEA,CAAA,KAAMhD,uBAAuB,CAAC,IAAI,CAAE;UAAAgC,QAAA,EAC9C;QAED;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3C,OAAA,CAACvB,MAAM;UACL8E,OAAO,EAAC,UAAU;UAClBhB,KAAK,EAAC,OAAO;UACbuB,OAAO,EAAE1D,OAAQ;UAAA0C,QAAA,EAClB;QAED;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELhC,KAAK,iBACJX,OAAA,CAAChC,KAAK;MAAC+E,QAAQ,EAAC,OAAO;MAACE,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAACS,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,IAAI,CAAE;MAAAkC,QAAA,EAClEnC;IAAK;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAlC,OAAO,iBAAIT,OAAA,CAAClB,cAAc;MAACmE,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7C3C,OAAA,CAAClC,KAAK;MAAC6F,SAAS,EAAE;QAAEK,EAAE,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAM,CAAE;MAACL,OAAO,EAAE,CAAE;MAACX,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACvE9C,OAAA,CAACpC,IAAI;QAACqF,EAAE,EAAE;UAAEiB,IAAI,EAAE;QAAE,CAAE;QAAApB,QAAA,eACpB9C,OAAA,CAACnC,WAAW;UAAAiF,QAAA,eACV9C,OAAA,CAAClC,KAAK;YAAC6F,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACpD9C,OAAA,CAAChB,eAAe;cAACuD,KAAK,EAAC,SAAS;cAAC4B,QAAQ,EAAC;YAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD3C,OAAA,CAACjC,GAAG;cAAA+E,QAAA,gBACF9C,OAAA,CAACrC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAAAT,QAAA,EAAED,KAAK,CAACnB;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrD3C,OAAA,CAACrC,UAAU;gBAAC4E,KAAK,EAAC,gBAAgB;gBAAAO,QAAA,EAAC;cAAS;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACqF,EAAE,EAAE;UAAEiB,IAAI,EAAE;QAAE,CAAE;QAAApB,QAAA,eACpB9C,OAAA,CAACnC,WAAW;UAAAiF,QAAA,eACV9C,OAAA,CAAClC,KAAK;YAAC6F,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACpD9C,OAAA,CAACZ,WAAW;cAACmD,KAAK,EAAC,SAAS;cAAC4B,QAAQ,EAAC;YAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD3C,OAAA,CAACjC,GAAG;cAAA+E,QAAA,gBACF9C,OAAA,CAACrC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAAAT,QAAA,EAAED,KAAK,CAACd;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClD3C,OAAA,CAACrC,UAAU;gBAAC4E,KAAK,EAAC,gBAAgB;gBAAAO,QAAA,EAAC;cAAO;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACqF,EAAE,EAAE;UAAEiB,IAAI,EAAE;QAAE,CAAE;QAAApB,QAAA,eACpB9C,OAAA,CAACnC,WAAW;UAAAiF,QAAA,eACV9C,OAAA,CAAClC,KAAK;YAAC6F,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACpD9C,OAAA,CAACd,UAAU;cAACqD,KAAK,EAAC,OAAO;cAAC4B,QAAQ,EAAC;YAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C3C,OAAA,CAACjC,GAAG;cAAA+E,QAAA,gBACF9C,OAAA,CAACrC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAAAT,QAAA,EAAED,KAAK,CAACZ;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpD3C,OAAA,CAACrC,UAAU;gBAAC4E,KAAK,EAAC,gBAAgB;gBAAAO,QAAA,EAAC;cAAQ;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACqF,EAAE,EAAE;UAAEiB,IAAI,EAAE;QAAE,CAAE;QAAApB,QAAA,eACpB9C,OAAA,CAACnC,WAAW;UAAAiF,QAAA,eACV9C,OAAA,CAAClC,KAAK;YAAC6F,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACpD9C,OAAA,CAACV,YAAY;cAACiD,KAAK,EAAC,SAAS;cAAC4B,QAAQ,EAAC;YAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD3C,OAAA,CAACjC,GAAG;cAAA+E,QAAA,gBACF9C,OAAA,CAACrC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAAAT,QAAA,GAAED,KAAK,CAACV,cAAc,EAAC,GAAC;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7D3C,OAAA,CAACrC,UAAU;gBAAC4E,KAAK,EAAC,gBAAgB;gBAAAO,QAAA,EAAC;cAAa;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR3C,OAAA,CAACpC,IAAI;MAAAkF,QAAA,eACH9C,OAAA,CAACnC,WAAW;QAAAiF,QAAA,gBACV9C,OAAA,CAACrC,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,EAAC;QAEtC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZtC,iBAAiB,CAACoB,MAAM,KAAK,CAAC,gBAC7BzB,OAAA,CAAChC,KAAK;UAAC+E,QAAQ,EAAC,MAAM;UAAAD,QAAA,EAAC;QAEvB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER3C,OAAA,CAAC3B,cAAc;UAAC+F,SAAS,EAAE5F,KAAM;UAAC+E,OAAO,EAAC,UAAU;UAAAT,QAAA,eAClD9C,OAAA,CAAC9B,KAAK;YAAA4E,QAAA,gBACJ9C,OAAA,CAAC1B,SAAS;cAAAwE,QAAA,eACR9C,OAAA,CAACzB,QAAQ;gBAAAuE,QAAA,gBACP9C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EAAC;gBAAI;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3B3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EAAC;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7B3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EAAC;gBAAO;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ3C,OAAA,CAAC7B,SAAS;cAAA2E,QAAA,EACPzC,iBAAiB,CACfgE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CACvEC,GAAG,CAAEC,MAAM,iBACZ5E,OAAA,CAACzB,QAAQ;gBAAAuE,QAAA,gBACP9C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EACP,IAAI0B,IAAI,CAACI,MAAM,CAACH,IAAI,CAAC,CAACI,kBAAkB,CAAC,OAAO;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACZ3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,gBACR9C,OAAA,CAACrC,UAAU;oBAAC4F,OAAO,EAAC,OAAO;oBAACuB,UAAU,EAAC,QAAQ;oBAAAhC,QAAA,EAC5C8B,MAAM,CAACG,MAAM,CAACC;kBAAI;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACb3C,OAAA,CAACrC,UAAU;oBAAC4F,OAAO,EAAC,SAAS;oBAAChB,KAAK,EAAC,gBAAgB;oBAAAO,QAAA,EACjD8B,MAAM,CAACG,MAAM,CAACE;kBAAI;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,EACP,IAAI0B,IAAI,CAACI,MAAM,CAACM,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACZ3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,eACR9C,OAAA,CAAClC,KAAK;oBAAC6F,SAAS,EAAC,KAAK;oBAACC,OAAO,EAAE,CAAE;oBAACP,UAAU,EAAC,QAAQ;oBAAAP,QAAA,GACnDR,aAAa,CAACsC,MAAM,CAAC/C,MAAM,CAAC,eAC7B7B,OAAA,CAAC/B,IAAI;sBACHmH,KAAK,EAAER,MAAM,CAAC/C,MAAO;sBACrBU,KAAK,EAAEK,cAAc,CAACgC,MAAM,CAAC/C,MAAM,CAAS;sBAC5CwD,IAAI,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACZ3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,eACR9C,OAAA,CAAC/B,IAAI;oBACHmH,KAAK,EAAER,MAAM,CAACU,MAAO;oBACrB/B,OAAO,EAAC,UAAU;oBAClB8B,IAAI,EAAC,OAAO;oBACZE,IAAI,EAAEX,MAAM,CAACU,MAAM,KAAK,kBAAkB,gBAAGtF,OAAA,CAACR,QAAQ;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAAG6C;kBAAU;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ3C,OAAA,CAAC5B,SAAS;kBAAA0E,QAAA,eACR9C,OAAA,CAACrC,UAAU;oBAAC4F,OAAO,EAAC,OAAO;oBAAChB,KAAK,EAAC,gBAAgB;oBAAAO,QAAA,EAC/C8B,MAAM,CAACa,KAAK,IAAI;kBAAG;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GArCCiC,MAAM,CAAC1D,EAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCd,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP3C,OAAA,CAACtB,MAAM;MACLgH,IAAI,EAAE7E,oBAAqB;MAC3BkD,OAAO,EAAEA,CAAA,KAAMjD,uBAAuB,CAAC,KAAK,CAAE;MAC9CkC,QAAQ,EAAC,IAAI;MACb2C,SAAS;MAAA7C,QAAA,gBAET9C,OAAA,CAACrB,WAAW;QAAAmE,QAAA,eACV9C,OAAA,CAAClC,KAAK;UAAC6F,SAAS,EAAC,KAAK;UAACN,UAAU,EAAC,QAAQ;UAACO,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD9C,OAAA,CAACN,eAAe;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnB3C,OAAA,CAACrC,UAAU;YAAC4F,OAAO,EAAC,IAAI;YAAAT,QAAA,EAAC;UAEzB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACd3C,OAAA,CAACpB,aAAa;QAAAkE,QAAA,gBACZ9C,OAAA,CAACrC,UAAU;UAAC4F,OAAO,EAAC,OAAO;UAAChB,KAAK,EAAC,gBAAgB;UAACU,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAGlE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3C,OAAA,CAACH,gBAAgB;UACfM,IAAI,EAAEA,IAAK;UACXyF,sBAAsB,EAAGC,OAAO,IAAK;YACnC,IAAIA,OAAO,EAAE;cACX;cACAvE,OAAO,CAACwE,GAAG,CAAC,0CAA0C,CAAC;YACzD;UACF;QAAE;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB3C,OAAA,CAACnB,aAAa;QAAAiE,QAAA,eACZ9C,OAAA,CAACvB,MAAM;UAACqF,OAAO,EAAEA,CAAA,KAAMhD,uBAAuB,CAAC,KAAK,CAAE;UAAAgC,QAAA,EAAC;QAEvD;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACzC,EAAA,CA5SID,gBAA0B;EAAA,QACJN,OAAO;AAAA;AAAAoG,EAAA,GAD7B9F,gBAA0B;AA8ShC,eAAeA,gBAAgB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}