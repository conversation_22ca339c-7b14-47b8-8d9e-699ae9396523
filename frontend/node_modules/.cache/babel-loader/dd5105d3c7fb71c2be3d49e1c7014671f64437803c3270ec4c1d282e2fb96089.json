{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { FaceDetection } from '../classes';\nimport { BOX_ANCHORS, BOX_ANCHORS_SEPARABLE, DEFAULT_MODEL_NAME, DEFAULT_MODEL_NAME_SEPARABLE_CONV, IOU_THRESHOLD, MEAN_RGB_SEPARABLE } from './const';\nimport { TinyYolov2Base } from './TinyYolov2Base';\nvar TinyYolov2 = /** @class */function (_super) {\n  __extends(TinyYolov2, _super);\n  function TinyYolov2(withSeparableConvs) {\n    if (withSeparableConvs === void 0) {\n      withSeparableConvs = true;\n    }\n    var _this = this;\n    var config = Object.assign({}, {\n      withSeparableConvs: withSeparableConvs,\n      iouThreshold: IOU_THRESHOLD,\n      classes: ['face']\n    }, withSeparableConvs ? {\n      anchors: BOX_ANCHORS_SEPARABLE,\n      meanRgb: MEAN_RGB_SEPARABLE\n    } : {\n      anchors: BOX_ANCHORS,\n      withClassScores: true\n    });\n    _this = _super.call(this, config) || this;\n    return _this;\n  }\n  Object.defineProperty(TinyYolov2.prototype, \"withSeparableConvs\", {\n    get: function () {\n      return this.config.withSeparableConvs;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(TinyYolov2.prototype, \"anchors\", {\n    get: function () {\n      return this.config.anchors;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  TinyYolov2.prototype.locateFaces = function (input, forwardParams) {\n    return __awaiter(this, void 0, void 0, function () {\n      var objectDetections;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.detect(input, forwardParams)];\n          case 1:\n            objectDetections = _a.sent();\n            return [2 /*return*/, objectDetections.map(function (det) {\n              return new FaceDetection(det.score, det.relativeBox, {\n                width: det.imageWidth,\n                height: det.imageHeight\n              });\n            })];\n        }\n      });\n    });\n  };\n  TinyYolov2.prototype.getDefaultModelName = function () {\n    return this.withSeparableConvs ? DEFAULT_MODEL_NAME_SEPARABLE_CONV : DEFAULT_MODEL_NAME;\n  };\n  TinyYolov2.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return _super.prototype.extractParamsFromWeigthMap.call(this, weightMap);\n  };\n  return TinyYolov2;\n}(TinyYolov2Base);\nexport { TinyYolov2 };", "map": {"version": 3, "names": ["FaceDetection", "BOX_ANCHORS", "BOX_ANCHORS_SEPARABLE", "DEFAULT_MODEL_NAME", "DEFAULT_MODEL_NAME_SEPARABLE_CONV", "IOU_THRESHOLD", "MEAN_RGB_SEPARABLE", "TinyYolov2Base", "TinyYolov2", "_super", "__extends", "withSeparableConvs", "_this", "config", "Object", "assign", "iouThreshold", "classes", "anchors", "meanRgb", "withClassScores", "call", "defineProperty", "prototype", "get", "locateFaces", "input", "forwardParams", "detect", "objectDetections", "_a", "sent", "map", "det", "score", "relativeBox", "width", "imageWidth", "height", "imageHeight", "getDefaultModelName", "extractParamsFromWeigthMap", "weightMap"], "sources": ["../../../src/tinyYolov2/TinyYolov2.ts"], "sourcesContent": [null], "mappings": ";AAEA,SAASA,aAAa,QAAe,YAAY;AAGjD,SACEC,WAAW,EACXC,qBAAqB,EACrBC,kBAAkB,EAClBC,iCAAiC,EACjCC,aAAa,EACbC,kBAAkB,QACb,SAAS;AAChB,SAASC,cAAc,QAAQ,kBAAkB;AAIjD,IAAAC,UAAA,0BAAAC,MAAA;EAAgCC,SAAA,CAAAF,UAAA,EAAAC,MAAA;EAE9B,SAAAD,WAAYG,kBAAkC;IAAlC,IAAAA,kBAAA;MAAAA,kBAAA,OAAkC;IAAA;IAA9C,IAAAC,KAAA;IACE,IAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE;MAC/BJ,kBAAkB,EAAAA,kBAAA;MAClBK,YAAY,EAAEX,aAAa;MAC3BY,OAAO,EAAE,CAAC,MAAM;KACjB,EACDN,kBAAkB,GACd;MACAO,OAAO,EAAEhB,qBAAqB;MAC9BiB,OAAO,EAAEb;KACV,GACC;MACAY,OAAO,EAAEjB,WAAW;MACpBmB,eAAe,EAAE;KAClB,CACF;IAEDR,KAAA,GAAAH,MAAA,CAAAY,IAAA,OAAMR,MAAM,CAAC;;EACf;EAEAC,MAAA,CAAAQ,cAAA,CAAWd,UAAA,CAAAe,SAAA,sBAAkB;SAA7B,SAAAC,CAAA;MACE,OAAO,IAAI,CAACX,MAAM,CAACF,kBAAkB;IACvC,CAAC;;;;EAEDG,MAAA,CAAAQ,cAAA,CAAWd,UAAA,CAAAe,SAAA,WAAO;SAAlB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACX,MAAM,CAACK,OAAO;IAC5B,CAAC;;;;EAEYV,UAAA,CAAAe,SAAA,CAAAE,WAAW,GAAxB,UAAyBC,KAAgB,EAAEC,aAAiC;;;;;;YACjD,qBAAM,IAAI,CAACC,MAAM,CAACF,KAAK,EAAEC,aAAa,CAAC;;YAA1DE,gBAAgB,GAAGC,EAAA,CAAAC,IAAA,EAAuC;YAChE,sBAAOF,gBAAgB,CAACG,GAAG,CAAC,UAAAC,GAAG;cAAI,WAAIjC,aAAa,CAACiC,GAAG,CAACC,KAAK,EAAED,GAAG,CAACE,WAAW,EAAE;gBAAEC,KAAK,EAAEH,GAAG,CAACI,UAAU;gBAAEC,MAAM,EAAEL,GAAG,CAACM;cAAW,CAAE,CAAC;YAAjG,CAAiG,CAAC;;;;GACtI;EAES/B,UAAA,CAAAe,SAAA,CAAAiB,mBAAmB,GAA7B;IACE,OAAO,IAAI,CAAC7B,kBAAkB,GAAGP,iCAAiC,GAAGD,kBAAkB;EACzF,CAAC;EAESK,UAAA,CAAAe,SAAA,CAAAkB,0BAA0B,GAApC,UAAqCC,SAA4B;IAC/D,OAAOjC,MAAA,CAAAc,SAAA,CAAMkB,0BAA0B,CAAApB,IAAA,OAACqB,SAAS,CAAC;EACpD,CAAC;EACH,OAAAlC,UAAC;AAAD,CAAC,CA1C+BD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}