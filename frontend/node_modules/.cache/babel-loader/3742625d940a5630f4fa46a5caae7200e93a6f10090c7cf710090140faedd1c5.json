{"ast": null, "code": "export function extractWeightsFactory(weights) {\n  var remainingWeights = weights;\n  function extractWeights(numWeights) {\n    var ret = remainingWeights.slice(0, numWeights);\n    remainingWeights = remainingWeights.slice(numWeights);\n    return ret;\n  }\n  function getRemainingWeights() {\n    return remainingWeights;\n  }\n  return {\n    extractWeights: extractWeights,\n    getRemainingWeights: getRemainingWeights\n  };\n}", "map": {"version": 3, "names": ["extractWeightsFactory", "weights", "remainingWeights", "extractWeights", "numWeights", "ret", "slice", "getRemainingWeights"], "sources": ["../../../src/common/extractWeightsFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,qBAAqBA,CAACC,OAAqB;EACzD,IAAIC,gBAAgB,GAAGD,OAAO;EAE9B,SAASE,cAAcA,CAACC,UAAkB;IACxC,IAAMC,GAAG,GAAGH,gBAAgB,CAACI,KAAK,CAAC,CAAC,EAAEF,UAAU,CAAC;IACjDF,gBAAgB,GAAGA,gBAAgB,CAACI,KAAK,CAACF,UAAU,CAAC;IACrD,OAAOC,GAAG;EACZ;EAEA,SAASE,mBAAmBA,CAAA;IAC1B,OAAOL,gBAAgB;EACzB;EAEA,OAAO;IACLC,cAAc,EAAAA,cAAA;IACdI,mBAAmB,EAAAA;GACpB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}