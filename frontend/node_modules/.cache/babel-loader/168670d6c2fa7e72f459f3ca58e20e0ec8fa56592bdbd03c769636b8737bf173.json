{"ast": null, "code": "import { Point } from '../classes';\nexport var IOU_THRESHOLD = 0.4;\nexport var BOX_ANCHORS = [new Point(0.738768, 0.874946), new Point(2.42204, 2.65704), new Point(4.30971, 7.04493), new Point(10.246, 4.59428), new Point(12.6868, 11.8741)];\nexport var BOX_ANCHORS_SEPARABLE = [new Point(1.603231, 2.094468), new Point(6.041143, 7.080126), new Point(2.882459, 3.518061), new Point(4.266906, 5.178857), new Point(9.041765, 10.66308)];\nexport var MEAN_RGB_SEPARABLE = [117.001, 114.697, 97.404];\nexport var DEFAULT_MODEL_NAME = 'tiny_yolov2_model';\nexport var DEFAULT_MODEL_NAME_SEPARABLE_CONV = 'tiny_yolov2_separable_conv_model';", "map": {"version": 3, "names": ["Point", "IOU_THRESHOLD", "BOX_ANCHORS", "BOX_ANCHORS_SEPARABLE", "MEAN_RGB_SEPARABLE", "DEFAULT_MODEL_NAME", "DEFAULT_MODEL_NAME_SEPARABLE_CONV"], "sources": ["../../../src/tinyYolov2/const.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAElC,OAAO,IAAMC,aAAa,GAAG,GAAG;AAEhC,OAAO,IAAMC,WAAW,GAAG,CACzB,IAAIF,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,EAC3B,IAAIA,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,EAC3B,IAAIA,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,EAC1B,IAAIA,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAC5B;AAED,OAAO,IAAMG,qBAAqB,GAAG,CACnC,IAAIH,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7B,IAAIA,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAC9B;AAED,OAAO,IAAMI,kBAAkB,GAA6B,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;AAEtF,OAAO,IAAMC,kBAAkB,GAAG,mBAAmB;AACrD,OAAO,IAAMC,iCAAiC,GAAG,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}