{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { MtcnnOptions } from '../mtcnn/MtcnnOptions';\nimport { SsdMobilenetv1Options } from '../ssdMobilenetv1';\nimport { TinyYolov2Options } from '../tinyYolov2';\nimport { detectAllFaces } from './detectFaces';\n// export allFaces API for backward compatibility\nexport function allFacesSsdMobilenetv1(input, minConfidence) {\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          console.warn('allFacesSsdMobilenetv1 is deprecated and will be removed soon, use the high level api instead');\n          return [4 /*yield*/, detectAllFaces(input, new SsdMobilenetv1Options(minConfidence ? {\n            minConfidence: minConfidence\n          } : {})).withFaceLandmarks().withFaceDescriptors()];\n        case 1:\n          return [2 /*return*/, _a.sent()];\n      }\n    });\n  });\n}\nexport function allFacesTinyYolov2(input, forwardParams) {\n  if (forwardParams === void 0) {\n    forwardParams = {};\n  }\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          console.warn('allFacesTinyYolov2 is deprecated and will be removed soon, use the high level api instead');\n          return [4 /*yield*/, detectAllFaces(input, new TinyYolov2Options(forwardParams)).withFaceLandmarks().withFaceDescriptors()];\n        case 1:\n          return [2 /*return*/, _a.sent()];\n      }\n    });\n  });\n}\nexport function allFacesMtcnn(input, forwardParams) {\n  if (forwardParams === void 0) {\n    forwardParams = {};\n  }\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          console.warn('allFacesMtcnn is deprecated and will be removed soon, use the high level api instead');\n          return [4 /*yield*/, detectAllFaces(input, new MtcnnOptions(forwardParams)).withFaceLandmarks().withFaceDescriptors()];\n        case 1:\n          return [2 /*return*/, _a.sent()];\n      }\n    });\n  });\n}\nexport var allFaces = allFacesSsdMobilenetv1;", "map": {"version": 3, "names": ["MtcnnOptions", "SsdMobilenetv1Options", "TinyYolov2Options", "detectAllFaces", "allFacesSsdMobilenetv1", "input", "minConfidence", "console", "warn", "withFaceLandmarks", "withFaceDescriptors", "_a", "sent", "allFacesTinyYolov2", "forwardParams", "allFacesMtcnn", "allFaces"], "sources": ["../../../src/globalApi/allFaces.ts"], "sourcesContent": [null], "mappings": ";AAEA,SAAwBA,YAAY,QAAQ,uBAAuB;AACnE,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,SAA6BC,iBAAiB,QAAQ,eAAe;AACrE,SAASC,cAAc,QAAQ,eAAe;AAE9C;AAEA,OAAM,SAAgBC,sBAAsBA,CAC1CC,KAAgB,EAChBC,aAAsB;;;;;UAEtBC,OAAO,CAACC,IAAI,CAAC,+FAA+F,CAAC;UACtG,qBAAML,cAAc,CAACE,KAAK,EAAE,IAAIJ,qBAAqB,CAACK,aAAa,GAAG;YAAEA,aAAa,EAAAA;UAAA,CAAE,GAAG,EAAE,CAAC,CAAC,CAClGG,iBAAiB,EAAE,CACnBC,mBAAmB,EAAE;;UAFxB,sBAAOC,EAAA,CAAAC,IAAA,EAEiB;;;;;AAG1B,OAAM,SAAgBC,kBAAkBA,CACtCR,KAAgB,EAChBS,aAAsC;EAAtC,IAAAA,aAAA;IAAAA,aAAA,KAAsC;EAAA;;;;;UAEtCP,OAAO,CAACC,IAAI,CAAC,2FAA2F,CAAC;UAClG,qBAAML,cAAc,CAACE,KAAK,EAAE,IAAIH,iBAAiB,CAACY,aAAa,CAAC,CAAC,CACrEL,iBAAiB,EAAE,CACnBC,mBAAmB,EAAE;;UAFxB,sBAAOC,EAAA,CAAAC,IAAA,EAEiB;;;;;AAG1B,OAAM,SAAgBG,aAAaA,CACjCV,KAAgB,EAChBS,aAAiC;EAAjC,IAAAA,aAAA;IAAAA,aAAA,KAAiC;EAAA;;;;;UAEjCP,OAAO,CAACC,IAAI,CAAC,sFAAsF,CAAC;UAC7F,qBAAML,cAAc,CAACE,KAAK,EAAE,IAAIL,YAAY,CAACc,aAAa,CAAC,CAAC,CAChEL,iBAAiB,EAAE,CACnBC,mBAAmB,EAAE;;UAFxB,sBAAOC,EAAA,CAAAC,IAAA,EAEiB;;;;;AAG1B,OAAO,IAAMI,QAAQ,GAAGZ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}