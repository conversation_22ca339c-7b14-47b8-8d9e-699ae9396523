{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { Point, Rect } from '../classes';\nimport { FaceDetection } from '../classes/FaceDetection';\nimport { FaceLandmarks5 } from '../classes/FaceLandmarks5';\nimport { toNetInput } from '../dom';\nimport { extendWithFaceDetection, extendWithFaceLandmarks } from '../factories';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { bgrToRgbTensor } from './bgrToRgbTensor';\nimport { CELL_SIZE } from './config';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nimport { getSizesForScale } from './getSizesForScale';\nimport { MtcnnOptions } from './MtcnnOptions';\nimport { pyramidDown } from './pyramidDown';\nimport { stage1 } from './stage1';\nimport { stage2 } from './stage2';\nimport { stage3 } from './stage3';\nvar Mtcnn = /** @class */function (_super) {\n  __extends(Mtcnn, _super);\n  function Mtcnn() {\n    return _super.call(this, 'Mtcnn') || this;\n  }\n  Mtcnn.prototype.load = function (weightsOrUrl) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        console.warn('mtcnn is deprecated and will be removed soon');\n        return [2 /*return*/, _super.prototype.load.call(this, weightsOrUrl)];\n      });\n    });\n  };\n  Mtcnn.prototype.loadFromDisk = function (filePath) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        console.warn('mtcnn is deprecated and will be removed soon');\n        return [2 /*return*/, _super.prototype.loadFromDisk.call(this, filePath)];\n      });\n    });\n  };\n  Mtcnn.prototype.forwardInput = function (input, forwardParams) {\n    if (forwardParams === void 0) {\n      forwardParams = {};\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var params, inputCanvas, stats, tsTotal, imgTensor, onReturn, _a, height, width, _b, minFaceSize, scaleFactor, maxNumScales, scoreThresholds, scaleSteps, scales, ts, out1, out2, out3, results;\n      return __generator(this, function (_c) {\n        switch (_c.label) {\n          case 0:\n            params = this.params;\n            if (!params) {\n              throw new Error('Mtcnn - load model before inference');\n            }\n            inputCanvas = input.canvases[0];\n            if (!inputCanvas) {\n              throw new Error('Mtcnn - inputCanvas is not defined, note that passing tensors into Mtcnn.forwardInput is not supported yet.');\n            }\n            stats = {};\n            tsTotal = Date.now();\n            imgTensor = tf.tidy(function () {\n              return bgrToRgbTensor(tf.expandDims(tf.browser.fromPixels(inputCanvas)).toFloat());\n            });\n            onReturn = function (results) {\n              // dispose tensors on return\n              imgTensor.dispose();\n              stats.total = Date.now() - tsTotal;\n              return results;\n            };\n            _a = imgTensor.shape.slice(1), height = _a[0], width = _a[1];\n            _b = new MtcnnOptions(forwardParams), minFaceSize = _b.minFaceSize, scaleFactor = _b.scaleFactor, maxNumScales = _b.maxNumScales, scoreThresholds = _b.scoreThresholds, scaleSteps = _b.scaleSteps;\n            scales = (scaleSteps || pyramidDown(minFaceSize, scaleFactor, [height, width])).filter(function (scale) {\n              var sizes = getSizesForScale(scale, [height, width]);\n              return Math.min(sizes.width, sizes.height) > CELL_SIZE;\n            }).slice(0, maxNumScales);\n            stats.scales = scales;\n            stats.pyramid = scales.map(function (scale) {\n              return getSizesForScale(scale, [height, width]);\n            });\n            ts = Date.now();\n            return [4 /*yield*/, stage1(imgTensor, scales, scoreThresholds[0], params.pnet, stats)];\n          case 1:\n            out1 = _c.sent();\n            stats.total_stage1 = Date.now() - ts;\n            if (!out1.boxes.length) {\n              return [2 /*return*/, onReturn({\n                results: [],\n                stats: stats\n              })];\n            }\n            stats.stage2_numInputBoxes = out1.boxes.length;\n            // using the inputCanvas to extract and resize the image patches, since it is faster\n            // than doing this on the gpu\n            ts = Date.now();\n            return [4 /*yield*/, stage2(inputCanvas, out1.boxes, scoreThresholds[1], params.rnet, stats)];\n          case 2:\n            out2 = _c.sent();\n            stats.total_stage2 = Date.now() - ts;\n            if (!out2.boxes.length) {\n              return [2 /*return*/, onReturn({\n                results: [],\n                stats: stats\n              })];\n            }\n            stats.stage3_numInputBoxes = out2.boxes.length;\n            ts = Date.now();\n            return [4 /*yield*/, stage3(inputCanvas, out2.boxes, scoreThresholds[2], params.onet, stats)];\n          case 3:\n            out3 = _c.sent();\n            stats.total_stage3 = Date.now() - ts;\n            results = out3.boxes.map(function (box, idx) {\n              return extendWithFaceLandmarks(extendWithFaceDetection({}, new FaceDetection(out3.scores[idx], new Rect(box.left / width, box.top / height, box.width / width, box.height / height), {\n                height: height,\n                width: width\n              })), new FaceLandmarks5(out3.points[idx].map(function (pt) {\n                return pt.sub(new Point(box.left, box.top)).div(new Point(box.width, box.height));\n              }), {\n                width: box.width,\n                height: box.height\n              }));\n            });\n            return [2 /*return*/, onReturn({\n              results: results,\n              stats: stats\n            })];\n        }\n      });\n    });\n  };\n  Mtcnn.prototype.forward = function (input, forwardParams) {\n    if (forwardParams === void 0) {\n      forwardParams = {};\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [4 /*yield*/, _a.apply(this, [_b.sent(), forwardParams])];\n          case 2:\n            return [2 /*return*/, _b.sent().results];\n        }\n      });\n    });\n  };\n  Mtcnn.prototype.forwardWithStats = function (input, forwardParams) {\n    if (forwardParams === void 0) {\n      forwardParams = {};\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent(), forwardParams])];\n        }\n      });\n    });\n  };\n  Mtcnn.prototype.getDefaultModelName = function () {\n    return 'mtcnn_model';\n  };\n  Mtcnn.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMap(weightMap);\n  };\n  Mtcnn.prototype.extractParams = function (weights) {\n    return extractParams(weights);\n  };\n  return Mtcnn;\n}(NeuralNetwork);\nexport { Mtcnn };", "map": {"version": 3, "names": ["tf", "Point", "Rect", "FaceDetection", "FaceLandmarks5", "toNetInput", "extendWithFaceDetection", "extendWithFaceLandmarks", "NeuralNetwork", "bgrToRgbTensor", "CELL_SIZE", "extractParams", "extractParamsFromWeigthMap", "getSizesForScale", "MtcnnOptions", "pyramidDown", "stage1", "stage2", "stage3", "Mtcnn", "_super", "__extends", "call", "prototype", "load", "weightsOrUrl", "console", "warn", "loadFromDisk", "filePath", "forwardInput", "input", "forwardParams", "params", "Error", "inputCanvas", "canvases", "stats", "tsTotal", "Date", "now", "imgTensor", "tidy", "expandDims", "browser", "fromPixels", "toFloat", "onReturn", "results", "dispose", "total", "_a", "shape", "slice", "height", "width", "_b", "minFaceSize", "scaleFactor", "maxNumScales", "scoreT<PERSON><PERSON>olds", "scaleSteps", "scales", "filter", "scale", "sizes", "Math", "min", "pyramid", "map", "ts", "pnet", "out1", "_c", "sent", "total_stage1", "boxes", "length", "stage2_numInputBoxes", "rnet", "out2", "total_stage2", "stage3_numInputBoxes", "onet", "out3", "total_stage3", "box", "idx", "scores", "left", "top", "points", "pt", "sub", "div", "forward", "apply", "forwardWithStats", "getDefaultModelName", "weightMap", "weights"], "sources": ["../../../src/mtcnn/Mtcnn.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,KAAK,EAAEC,IAAI,QAAQ,YAAY;AACxC,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,cAAc;AAC/E,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAAwBC,YAAY,QAAQ,gBAAgB;AAC5D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,MAAM,QAAQ,UAAU;AAGjC,IAAAC,KAAA,0BAAAC,MAAA;EAA2BC,SAAA,CAAAF,KAAA,EAAAC,MAAA;EAEzB,SAAAD,MAAA;WACEC,MAAA,CAAAE,IAAA,OAAM,OAAO,CAAC;EAChB;EAEaH,KAAA,CAAAI,SAAA,CAAAC,IAAI,GAAjB,UAAkBC,YAA+C;;;QAC/DC,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5D,sBAAOP,MAAA,CAAAG,SAAA,CAAMC,IAAI,CAAAF,IAAA,OAACG,YAAY,CAAC;;;GAChC;EAEYN,KAAA,CAAAI,SAAA,CAAAK,YAAY,GAAzB,UAA0BC,QAA4B;;;QACpDH,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5D,sBAAOP,MAAA,CAAAG,SAAA,CAAMK,YAAY,CAAAN,IAAA,OAACO,QAAQ,CAAC;;;GACpC;EAEYV,KAAA,CAAAI,SAAA,CAAAO,YAAY,GAAzB,UACEC,KAAe,EACfC,aAAiC;IAAjC,IAAAA,aAAA;MAAAA,aAAA,KAAiC;IAAA;;;;;;YAGzBC,MAAM,GAAK,IAAI,CAAAA,MAAT;YAEd,IAAI,CAACA,MAAM,EAAE;cACX,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;;YAGlDC,WAAW,GAAGJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;YAErC,IAAI,CAACD,WAAW,EAAE;cAChB,MAAM,IAAID,KAAK,CAAC,6GAA6G,CAAC;;YAG1HG,KAAK,GAAQ,EAAE;YAEfC,OAAO,GAAGC,IAAI,CAACC,GAAG,EAAE;YAEpBC,SAAS,GAAGzC,EAAE,CAAC0C,IAAI,CAAC;cACxB,OAAAjC,cAAc,CACZT,EAAE,CAAC2C,UAAU,CAAC3C,EAAE,CAAC4C,OAAO,CAACC,UAAU,CAACV,WAAW,CAAC,CAAC,CAACW,OAAO,EAAiB,CAC3E;YAFD,CAEC,CACF;YAEKC,QAAQ,GAAG,SAAAA,CAACC,OAAY;cAC5B;cACAP,SAAS,CAACQ,OAAO,EAAE;cACnBZ,KAAK,CAACa,KAAK,GAAGX,IAAI,CAACC,GAAG,EAAE,GAAGF,OAAO;cAClC,OAAOU,OAAO;YAChB,CAAC;YAEKG,EAAA,GAAkBV,SAAS,CAACW,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,EAAzCC,MAAM,GAAAH,EAAA,KAAEI,KAAK,GAAAJ,EAAA;YAEdK,EAAA,GAMF,IAAI1C,YAAY,CAACkB,aAAa,CAAC,EALjCyB,WAAW,GAAAD,EAAA,CAAAC,WAAA,EACXC,WAAW,GAAAF,EAAA,CAAAE,WAAA,EACXC,YAAY,GAAAH,EAAA,CAAAG,YAAA,EACZC,eAAe,GAAAJ,EAAA,CAAAI,eAAA,EACfC,UAAU,GAAAL,EAAA,CAAAK,UAAA;YAGNC,MAAM,GAAG,CAACD,UAAU,IAAI9C,WAAW,CAAC0C,WAAW,EAAEC,WAAW,EAAE,CAACJ,MAAM,EAAEC,KAAK,CAAC,CAAC,EACjFQ,MAAM,CAAC,UAAAC,KAAK;cACX,IAAMC,KAAK,GAAGpD,gBAAgB,CAACmD,KAAK,EAAE,CAACV,MAAM,EAAEC,KAAK,CAAC,CAAC;cACtD,OAAOW,IAAI,CAACC,GAAG,CAACF,KAAK,CAACV,KAAK,EAAEU,KAAK,CAACX,MAAM,CAAC,GAAG5C,SAAS;YACxD,CAAC,CAAC,CACD2C,KAAK,CAAC,CAAC,EAAEM,YAAY,CAAC;YAEzBtB,KAAK,CAACyB,MAAM,GAAGA,MAAM;YACrBzB,KAAK,CAAC+B,OAAO,GAAGN,MAAM,CAACO,GAAG,CAAC,UAAAL,KAAK;cAAI,OAAAnD,gBAAgB,CAACmD,KAAK,EAAE,CAACV,MAAM,EAAEC,KAAK,CAAC,CAAC;YAAxC,CAAwC,CAAC;YAEzEe,EAAE,GAAG/B,IAAI,CAACC,GAAG,EAAE;YACN,qBAAMxB,MAAM,CAACyB,SAAS,EAAEqB,MAAM,EAAEF,eAAe,CAAC,CAAC,CAAC,EAAE3B,MAAM,CAACsC,IAAI,EAAElC,KAAK,CAAC;;YAA9EmC,IAAI,GAAGC,EAAA,CAAAC,IAAA,EAAuE;YACpFrC,KAAK,CAACsC,YAAY,GAAGpC,IAAI,CAACC,GAAG,EAAE,GAAG8B,EAAE;YAEpC,IAAI,CAACE,IAAI,CAACI,KAAK,CAACC,MAAM,EAAE;cACtB,sBAAO9B,QAAQ,CAAC;gBAAEC,OAAO,EAAE,EAAE;gBAAEX,KAAK,EAAAA;cAAA,CAAE,CAAC;;YAGzCA,KAAK,CAACyC,oBAAoB,GAAGN,IAAI,CAACI,KAAK,CAACC,MAAM;YAC9C;YACA;YACAP,EAAE,GAAG/B,IAAI,CAACC,GAAG,EAAE;YACF,qBAAMvB,MAAM,CAACkB,WAAW,EAAEqC,IAAI,CAACI,KAAK,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAE3B,MAAM,CAAC8C,IAAI,EAAE1C,KAAK,CAAC;;YAApF2C,IAAI,GAAGP,EAAA,CAAAC,IAAA,EAA6E;YAC1FrC,KAAK,CAAC4C,YAAY,GAAG1C,IAAI,CAACC,GAAG,EAAE,GAAG8B,EAAE;YAEpC,IAAI,CAACU,IAAI,CAACJ,KAAK,CAACC,MAAM,EAAE;cACtB,sBAAO9B,QAAQ,CAAC;gBAAEC,OAAO,EAAE,EAAE;gBAAEX,KAAK,EAAAA;cAAA,CAAE,CAAC;;YAGzCA,KAAK,CAAC6C,oBAAoB,GAAGF,IAAI,CAACJ,KAAK,CAACC,MAAM;YAE9CP,EAAE,GAAG/B,IAAI,CAACC,GAAG,EAAE;YACF,qBAAMtB,MAAM,CAACiB,WAAW,EAAE6C,IAAI,CAACJ,KAAK,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAE3B,MAAM,CAACkD,IAAI,EAAE9C,KAAK,CAAC;;YAApF+C,IAAI,GAAGX,EAAA,CAAAC,IAAA,EAA6E;YAC1FrC,KAAK,CAACgD,YAAY,GAAG9C,IAAI,CAACC,GAAG,EAAE,GAAG8B,EAAE;YAE9BtB,OAAO,GAAGoC,IAAI,CAACR,KAAK,CAACP,GAAG,CAAC,UAACiB,GAAG,EAAEC,GAAG;cAAK,OAAAhF,uBAAuB,CAClED,uBAAuB,CACrB,EAAE,EACF,IAAIH,aAAa,CACfiF,IAAI,CAACI,MAAM,CAACD,GAAG,CAAC,EAChB,IAAIrF,IAAI,CACNoF,GAAG,CAACG,IAAI,GAAGlC,KAAK,EAChB+B,GAAG,CAACI,GAAG,GAAGpC,MAAM,EAChBgC,GAAG,CAAC/B,KAAK,GAAGA,KAAK,EACjB+B,GAAG,CAAChC,MAAM,GAAGA,MAAM,CACpB,EACD;gBACEA,MAAM,EAAAA,MAAA;gBACNC,KAAK,EAAAA;eACN,CACF,CACF,EACD,IAAInD,cAAc,CAChBgF,IAAI,CAACO,MAAM,CAACJ,GAAG,CAAC,CAAClB,GAAG,CAAC,UAAAuB,EAAE;gBAAI,OAAAA,EAAE,CAACC,GAAG,CAAC,IAAI5F,KAAK,CAACqF,GAAG,CAACG,IAAI,EAAEH,GAAG,CAACI,GAAG,CAAC,CAAC,CAACI,GAAG,CAAC,IAAI7F,KAAK,CAACqF,GAAG,CAAC/B,KAAK,EAAE+B,GAAG,CAAChC,MAAM,CAAC,CAAC;cAA1E,CAA0E,CAAC,EACtG;gBAAEC,KAAK,EAAE+B,GAAG,CAAC/B,KAAK;gBAAED,MAAM,EAAEgC,GAAG,CAAChC;cAAM,CAAE,CACzC,CACF;YArB4C,CAqB5C,CAAC;YAEF,sBAAOP,QAAQ,CAAC;cAAEC,OAAO,EAAAA,OAAA;cAAEX,KAAK,EAAAA;YAAA,CAAE,CAAC;;;;GACpC;EAEYlB,KAAA,CAAAI,SAAA,CAAAwE,OAAO,GAApB,UACEhE,KAAgB,EAChBC,aAAiC;IAAjC,IAAAA,aAAA;MAAAA,aAAA,KAAiC;IAAA;;;;;;YAGzBmB,EAAA,OAAI,CAACrB,YAAY;YACrB,qBAAMzB,UAAU,CAAC0B,KAAK,CAAC;;YADzB,qBAAMoB,EAAA,CAAA6C,KAAA,KAAI,GACRxC,EAAA,CAAAkB,IAAA,EAAuB,EACvB1C,aAAa,EACd;;YAJH,sBACEwB,EAAA,CAAAkB,IAAA,EAGC,CACD1B,OAAO;;;;GACV;EAEY7B,KAAA,CAAAI,SAAA,CAAA0E,gBAAgB,GAA7B,UACElE,KAAgB,EAChBC,aAAiC;IAAjC,IAAAA,aAAA;MAAAA,aAAA,KAAiC;IAAA;;;;;;YAE1BmB,EAAA,OAAI,CAACrB,YAAY;YACtB,qBAAMzB,UAAU,CAAC0B,KAAK,CAAC;;YADzB,sBAAOoB,EAAA,CAAA6C,KAAA,KAAI,GACTxC,EAAA,CAAAkB,IAAA,EAAuB,EACvB1C,aAAa,EACd;;;;GACF;EAESb,KAAA,CAAAI,SAAA,CAAA2E,mBAAmB,GAA7B;IACE,OAAO,aAAa;EACtB,CAAC;EAES/E,KAAA,CAAAI,SAAA,CAAAX,0BAA0B,GAApC,UAAqCuF,SAA4B;IAC/D,OAAOvF,0BAA0B,CAACuF,SAAS,CAAC;EAC9C,CAAC;EAEShF,KAAA,CAAAI,SAAA,CAAAZ,aAAa,GAAvB,UAAwByF,OAAqB;IAC3C,OAAOzF,aAAa,CAACyF,OAAO,CAAC;EAC/B,CAAC;EACH,OAAAjF,KAAC;AAAD,CAAC,CA1J0BX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}