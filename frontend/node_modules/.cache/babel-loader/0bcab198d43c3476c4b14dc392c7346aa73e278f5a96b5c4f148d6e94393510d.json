{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { depthwiseSeparableConv } from '../common/depthwiseSeparableConv';\nexport function denseBlock3(x, denseBlockParams, isFirstLayer) {\n  if (isFirstLayer === void 0) {\n    isFirstLayer = false;\n  }\n  return tf.tidy(function () {\n    var out1 = tf.relu(isFirstLayer ? tf.add(tf.conv2d(x, denseBlockParams.conv0.filters, [2, 2], 'same'), denseBlockParams.conv0.bias) : depthwiseSeparableConv(x, denseBlockParams.conv0, [2, 2]));\n    var out2 = depthwiseSeparableConv(out1, denseBlockParams.conv1, [1, 1]);\n    var in3 = tf.relu(tf.add(out1, out2));\n    var out3 = depthwiseSeparableConv(in3, denseBlockParams.conv2, [1, 1]);\n    return tf.relu(tf.add(out1, tf.add(out2, out3)));\n  });\n}\nexport function denseBlock4(x, denseBlockParams, isFirstLayer, isScaleDown) {\n  if (isFirstLayer === void 0) {\n    isFirstLayer = false;\n  }\n  if (isScaleDown === void 0) {\n    isScaleDown = true;\n  }\n  return tf.tidy(function () {\n    var out1 = tf.relu(isFirstLayer ? tf.add(tf.conv2d(x, denseBlockParams.conv0.filters, isScaleDown ? [2, 2] : [1, 1], 'same'), denseBlockParams.conv0.bias) : depthwiseSeparableConv(x, denseBlockParams.conv0, isScaleDown ? [2, 2] : [1, 1]));\n    var out2 = depthwiseSeparableConv(out1, denseBlockParams.conv1, [1, 1]);\n    var in3 = tf.relu(tf.add(out1, out2));\n    var out3 = depthwiseSeparableConv(in3, denseBlockParams.conv2, [1, 1]);\n    var in4 = tf.relu(tf.add(out1, tf.add(out2, out3)));\n    var out4 = depthwiseSeparableConv(in4, denseBlockParams.conv3, [1, 1]);\n    return tf.relu(tf.add(out1, tf.add(out2, tf.add(out3, out4))));\n  });\n}", "map": {"version": 3, "names": ["tf", "depthwiseSeparableConv", "denseBlock3", "x", "denseBlockParams", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tidy", "out1", "relu", "add", "conv2d", "conv0", "filters", "bias", "out2", "conv1", "in3", "out3", "conv2", "denseBlock4", "isScaleDown", "in4", "out4", "conv3"], "sources": ["../../../src/faceFeatureExtractor/denseBlock.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,sBAAsB,QAAQ,kCAAkC;AAGzE,OAAM,SAAUC,WAAWA,CACzBC,CAAc,EACdC,gBAAmC,EACnCC,YAA6B;EAA7B,IAAAA,YAAA;IAAAA,YAAA,QAA6B;EAAA;EAE7B,OAAOL,EAAE,CAACM,IAAI,CAAC;IACb,IAAMC,IAAI,GAAGP,EAAE,CAACQ,IAAI,CAClBH,YAAY,GACRL,EAAE,CAACS,GAAG,CACNT,EAAE,CAACU,MAAM,CAACP,CAAC,EAAGC,gBAAgB,CAACO,KAAoB,CAACC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAC5ER,gBAAgB,CAACO,KAAK,CAACE,IAAI,CAC5B,GACCZ,sBAAsB,CAACE,CAAC,EAAEC,gBAAgB,CAACO,KAA4B,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACtE;IAChB,IAAMG,IAAI,GAAGb,sBAAsB,CAACM,IAAI,EAAEH,gBAAgB,CAACW,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzE,IAAMC,GAAG,GAAGhB,EAAE,CAACQ,IAAI,CAACR,EAAE,CAACS,GAAG,CAACF,IAAI,EAAEO,IAAI,CAAC,CAAgB;IACtD,IAAMG,IAAI,GAAGhB,sBAAsB,CAACe,GAAG,EAAEZ,gBAAgB,CAACc,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAExE,OAAOlB,EAAE,CAACQ,IAAI,CAACR,EAAE,CAACS,GAAG,CAACF,IAAI,EAAEP,EAAE,CAACS,GAAG,CAACK,IAAI,EAAEG,IAAI,CAAC,CAAC,CAAgB;EACjE,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUE,WAAWA,CACzBhB,CAAc,EACdC,gBAAmC,EACnCC,YAA6B,EAC7Be,WAA2B;EAD3B,IAAAf,YAAA;IAAAA,YAAA,QAA6B;EAAA;EAC7B,IAAAe,WAAA;IAAAA,WAAA,OAA2B;EAAA;EAE3B,OAAOpB,EAAE,CAACM,IAAI,CAAC;IACb,IAAMC,IAAI,GAAGP,EAAE,CAACQ,IAAI,CAClBH,YAAY,GACRL,EAAE,CAACS,GAAG,CACNT,EAAE,CAACU,MAAM,CAACP,CAAC,EAAGC,gBAAgB,CAACO,KAAoB,CAACC,OAAO,EAAEQ,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EACnGhB,gBAAgB,CAACO,KAAK,CAACE,IAAI,CAC5B,GACCZ,sBAAsB,CAACE,CAAC,EAAEC,gBAAgB,CAACO,KAA4B,EAAES,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAC7F;IAChB,IAAMN,IAAI,GAAGb,sBAAsB,CAACM,IAAI,EAAEH,gBAAgB,CAACW,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzE,IAAMC,GAAG,GAAGhB,EAAE,CAACQ,IAAI,CAACR,EAAE,CAACS,GAAG,CAACF,IAAI,EAAEO,IAAI,CAAC,CAAgB;IACtD,IAAMG,IAAI,GAAGhB,sBAAsB,CAACe,GAAG,EAAEZ,gBAAgB,CAACc,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAExE,IAAMG,GAAG,GAAGrB,EAAE,CAACQ,IAAI,CAACR,EAAE,CAACS,GAAG,CAACF,IAAI,EAAEP,EAAE,CAACS,GAAG,CAACK,IAAI,EAAEG,IAAI,CAAC,CAAC,CAAgB;IACpE,IAAMK,IAAI,GAAGrB,sBAAsB,CAACoB,GAAG,EAAEjB,gBAAgB,CAACmB,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAExE,OAAOvB,EAAE,CAACQ,IAAI,CAACR,EAAE,CAACS,GAAG,CAACF,IAAI,EAAEP,EAAE,CAACS,GAAG,CAACK,IAAI,EAAEd,EAAE,CAACS,GAAG,CAACQ,IAAI,EAAEK,IAAI,CAAC,CAAC,CAAC,CAAgB;EAC/E,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}