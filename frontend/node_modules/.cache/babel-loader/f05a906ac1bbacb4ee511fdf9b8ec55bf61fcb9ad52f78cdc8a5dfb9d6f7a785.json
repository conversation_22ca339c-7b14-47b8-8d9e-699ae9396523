{"ast": null, "code": "/**\n * Gets the first element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @alias first\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the first element of `array`.\n * @example\n *\n * _.head([1, 2, 3]);\n * // => 1\n *\n * _.head([]);\n * // => undefined\n */\nfunction head(array) {\n  return array && array.length ? array[0] : undefined;\n}\nmodule.exports = head;", "map": {"version": 3, "names": ["head", "array", "length", "undefined", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/head.js"], "sourcesContent": ["/**\n * Gets the first element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @alias first\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the first element of `array`.\n * @example\n *\n * _.head([1, 2, 3]);\n * // => 1\n *\n * _.head([]);\n * // => undefined\n */\nfunction head(array) {\n  return (array && array.length) ? array[0] : undefined;\n}\n\nmodule.exports = head;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,KAAK,EAAE;EACnB,OAAQA,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAID,KAAK,CAAC,CAAC,CAAC,GAAGE,SAAS;AACvD;AAEAC,MAAM,CAACC,OAAO,GAAGL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}