{"ast": null, "code": "/**\n * Service Supabase pour PresencePro\n */\n\nimport { supabase } from '../config/supabase';\nimport { UserRole } from '../types';\nclass SupabaseService {\n  // ==================== GESTION DES UTILISATEURS ====================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('*').order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(this.mapUserFromDB)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des utilisateurs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('*').eq('id', userId).single();\n      if (error) {\n        if (error.code === 'PGRST116') return null; // Not found\n        throw error;\n      }\n      return data ? this.mapUserFromDB(data) : null;\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData) {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      const {\n        data,\n        error\n      } = await supabase.from('users').insert([dbUser]).select().single();\n      if (error) throw error;\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur avec un ID spécifique (pour l'authentification)\n   */\n  async createUserWithId(userId, userData) {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      const {\n        error\n      } = await supabase.from('users').insert([{\n        ...dbUser,\n        id: userId\n      }]);\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur avec ID:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId, userData) {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      const {\n        data,\n        error\n      } = await supabase.from('users').update({\n        ...dbUser,\n        updated_at: new Date().toISOString()\n      }).eq('id', userId).select().single();\n      if (error) throw error;\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId) {\n    try {\n      const {\n        error\n      } = await supabase.from('users').delete().eq('id', userId);\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours avec leurs relations\n   */\n  async getCourses() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('courses').select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(this.mapCourseFromDB)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('courses').select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `).eq('teacher_id', teacherId).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(this.mapCourseFromDB)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours de l\\'enseignant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData) {\n    try {\n      const dbCourse = this.mapCourseToDB(courseData);\n      const {\n        data,\n        error\n      } = await supabase.from('courses').insert([dbCourse]).select().single();\n      if (error) throw error;\n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de la création du cours:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData) {\n    try {\n      const dbAttendance = this.mapAttendanceToDB(attendanceData);\n      const {\n        data,\n        error\n      } = await supabase.from('attendance').insert([dbAttendance]).select().single();\n      if (error) throw error;\n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de l\\'enregistrement de la présence:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère toutes les présences\n   */\n  async getAttendanceRecords() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('attendance').select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(*)\n        `).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(this.mapAttendanceFromDB)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId, date) {\n    try {\n      let query = supabase.from('attendance').select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `).eq('course_id', courseId);\n      if (date) {\n        query = query.eq('date', date);\n      }\n      query = query.order('created_at', {\n        ascending: false\n      });\n      const {\n        data,\n        error\n      } = await query;\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(this.mapAttendanceFromDB)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('attendance').select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `).eq('student_id', studentId).order('date', {\n        ascending: false\n      });\n      if (error) throw error;\n      return (data === null || data === void 0 ? void 0 : data.map(this.mapAttendanceFromDB)) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences de l\\'étudiant:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId, file) {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/profile.${fileExt}`;\n      const filePath = `profile-images/${fileName}`;\n      const {\n        error: uploadError\n      } = await supabase.storage.from('images').upload(filePath, file, {\n        upsert: true\n      });\n      if (uploadError) throw uploadError;\n      const {\n        data\n      } = supabase.storage.from('images').getPublicUrl(filePath);\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId, file) {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/${Date.now()}.${fileExt}`;\n      const filePath = `face-images/${fileName}`;\n      const {\n        error: uploadError\n      } = await supabase.storage.from('images').upload(filePath, file);\n      if (uploadError) throw uploadError;\n      const {\n        data\n      } = supabase.storage.from('images').getPublicUrl(filePath);\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image faciale:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl) {\n    try {\n      // Extraire le chemin du fichier depuis l'URL\n      const urlParts = imageUrl.split('/');\n      const bucketIndex = urlParts.findIndex(part => part === 'images');\n      if (bucketIndex === -1) throw new Error('URL d\\'image invalide');\n      const filePath = urlParts.slice(bucketIndex + 1).join('/');\n      const {\n        error\n      } = await supabase.storage.from('images').remove([filePath]);\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats() {\n    try {\n      // Compter les utilisateurs par rôle\n      const {\n        data: users,\n        error: usersError\n      } = await supabase.from('users').select('role');\n      if (usersError) throw usersError;\n\n      // Compter les cours\n      const {\n        count: coursesCount,\n        error: coursesError\n      } = await supabase.from('courses').select('*', {\n        count: 'exact',\n        head: true\n      });\n      if (coursesError) throw coursesError;\n\n      // Compter les présences\n      const {\n        count: attendanceCount,\n        error: attendanceError\n      } = await supabase.from('attendance').select('*', {\n        count: 'exact',\n        head: true\n      });\n      if (attendanceError) throw attendanceError;\n      const stats = {\n        totalUsers: (users === null || users === void 0 ? void 0 : users.length) || 0,\n        totalStudents: (users === null || users === void 0 ? void 0 : users.filter(user => user.role === UserRole.STUDENT).length) || 0,\n        totalTeachers: (users === null || users === void 0 ? void 0 : users.filter(user => user.role === UserRole.TEACHER).length) || 0,\n        totalAdmins: (users === null || users === void 0 ? void 0 : users.filter(user => user.role === UserRole.ADMIN).length) || 0,\n        totalCourses: coursesCount || 0,\n        totalAttendances: attendanceCount || 0\n      };\n      return stats;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  }\n\n  // ==================== MÉTHODES DE MAPPING ====================\n\n  mapUserFromDB(dbUser) {\n    const firstName = dbUser.first_name || '';\n    const lastName = dbUser.last_name || '';\n    const role = dbUser.role || 'student';\n    return {\n      id: dbUser.id,\n      username: dbUser.username || '',\n      email: dbUser.email || '',\n      firstName,\n      lastName,\n      fullName: `${firstName} ${lastName}`.trim() || 'Utilisateur',\n      role: role,\n      roleDisplay: this.getRoleDisplay(role),\n      phoneNumber: dbUser.phone_number || undefined,\n      dateOfBirth: dbUser.date_of_birth || undefined,\n      address: dbUser.address || undefined,\n      profilePicture: dbUser.profile_picture || undefined,\n      isActive: dbUser.is_active !== false,\n      dateJoined: dbUser.created_at || new Date().toISOString(),\n      lastLogin: dbUser.last_login || undefined\n    };\n  }\n  mapUserToDB(user) {\n    return {\n      username: user.username,\n      email: user.email,\n      first_name: user.firstName,\n      last_name: user.lastName,\n      role: user.role,\n      phone_number: user.phoneNumber,\n      date_of_birth: user.dateOfBirth,\n      address: user.address,\n      profile_picture: user.profilePicture,\n      is_active: user.isActive,\n      last_login: user.lastLogin\n    };\n  }\n  mapCourseFromDB(dbCourse) {\n    return {\n      id: dbCourse.id,\n      name: dbCourse.name,\n      code: dbCourse.code,\n      description: dbCourse.description,\n      teacher: this.mapUserFromDB(dbCourse.teacher),\n      studentGroup: dbCourse.student_group,\n      schedule: dbCourse.schedule || [],\n      academicYear: dbCourse.academic_year,\n      semester: dbCourse.semester,\n      credits: dbCourse.credits,\n      isActive: dbCourse.is_active,\n      createdAt: dbCourse.created_at,\n      updatedAt: dbCourse.updated_at\n    };\n  }\n  mapCourseToDB(course) {\n    var _course$teacher, _course$studentGroup;\n    return {\n      name: course.name,\n      code: course.code,\n      description: course.description,\n      teacher_id: (_course$teacher = course.teacher) === null || _course$teacher === void 0 ? void 0 : _course$teacher.id,\n      student_group_id: (_course$studentGroup = course.studentGroup) === null || _course$studentGroup === void 0 ? void 0 : _course$studentGroup.id,\n      schedule: course.schedule,\n      academic_year: course.academicYear,\n      semester: course.semester,\n      credits: course.credits,\n      is_active: course.isActive\n    };\n  }\n  mapAttendanceFromDB(dbAttendance) {\n    return {\n      id: dbAttendance.id,\n      student: this.mapUserFromDB(dbAttendance.student),\n      course: dbAttendance.course,\n      date: dbAttendance.date,\n      time: dbAttendance.time,\n      status: dbAttendance.status,\n      method: dbAttendance.method,\n      confidence: dbAttendance.confidence,\n      notes: dbAttendance.notes,\n      createdAt: dbAttendance.created_at,\n      updatedAt: dbAttendance.updated_at\n    };\n  }\n  mapAttendanceToDB(attendance) {\n    var _attendance$student, _attendance$course;\n    return {\n      student_id: (_attendance$student = attendance.student) === null || _attendance$student === void 0 ? void 0 : _attendance$student.id,\n      course_id: (_attendance$course = attendance.course) === null || _attendance$course === void 0 ? void 0 : _attendance$course.id,\n      date: attendance.date,\n      time: attendance.time,\n      status: attendance.status,\n      method: attendance.method,\n      confidence: attendance.confidence,\n      notes: attendance.notes\n    };\n  }\n\n  /**\n   * Récupère un utilisateur par email\n   */\n  async getUserByEmail(email) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('*').eq('email', email).single();\n      if (error) throw error;\n      if (!data) throw new Error('Utilisateur non trouvé');\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur par email:', error);\n      throw error;\n    }\n  }\n  getRoleDisplay(role) {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'Administrateur';\n      case UserRole.TEACHER:\n        return 'Enseignant';\n      case UserRole.STUDENT:\n        return 'Étudiant';\n      default:\n        return 'Utilisateur';\n    }\n  }\n}\n\n// Instance singleton du service Supabase\nexport const supabaseService = new SupabaseService();\nexport default supabaseService;", "map": {"version": 3, "names": ["supabase", "UserRole", "SupabaseService", "getUsers", "data", "error", "from", "select", "order", "ascending", "map", "mapUserFromDB", "console", "getUserById", "userId", "eq", "single", "code", "createUser", "userData", "dbUser", "mapUserToDB", "insert", "createUserWithId", "id", "updateUser", "update", "updated_at", "Date", "toISOString", "deleteUser", "delete", "getCourses", "mapCourseFromDB", "getCoursesByTeacher", "teacherId", "createCourse", "courseData", "dbCourse", "mapCourseToDB", "recordAttendance", "attendanceData", "dbAttendance", "mapAttendanceToDB", "getAttendanceRecords", "mapAttendanceFromDB", "getAttendanceByCourse", "courseId", "date", "query", "getAttendanceByStudent", "studentId", "uploadProfileImage", "file", "fileExt", "name", "split", "pop", "fileName", "filePath", "uploadError", "storage", "upload", "upsert", "getPublicUrl", "publicUrl", "uploadFaceImage", "now", "deleteImage", "imageUrl", "urlParts", "bucketIndex", "findIndex", "part", "Error", "slice", "join", "remove", "getGlobalStats", "users", "usersError", "count", "coursesCount", "coursesError", "head", "attendanceCount", "attendanceError", "stats", "totalUsers", "length", "totalStudents", "filter", "user", "role", "STUDENT", "totalTeachers", "TEACHER", "totalAdmins", "ADMIN", "totalCourses", "totalAttendances", "firstName", "first_name", "lastName", "last_name", "username", "email", "fullName", "trim", "roleDisplay", "getRoleDisplay", "phoneNumber", "phone_number", "undefined", "dateOfBirth", "date_of_birth", "address", "profilePicture", "profile_picture", "isActive", "is_active", "dateJoined", "created_at", "lastLogin", "last_login", "description", "teacher", "studentGroup", "student_group", "schedule", "academicYear", "academic_year", "semester", "credits", "createdAt", "updatedAt", "course", "_course$teacher", "_course$studentGroup", "teacher_id", "student_group_id", "student", "time", "status", "method", "confidence", "notes", "attendance", "_attendance$student", "_attendance$course", "student_id", "course_id", "getUserByEmail", "supabaseService"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/services/supabaseService.ts"], "sourcesContent": ["/**\n * Service Supabase pour PresencePro\n */\n\nimport { supabase } from '../config/supabase';\nimport { User, Course, AttendanceRecord, UserRole } from '../types';\n\nclass SupabaseService {\n  \n  // ==================== GESTION DES UTILISATEURS ====================\n  \n  /**\n   * Récupère tous les utilisateurs\n   */\n  async getUsers(): Promise<User[]> {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(this.mapUserFromDB) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des utilisateurs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère un utilisateur par ID\n   */\n  async getUserById(userId: string): Promise<User | null> {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        if (error.code === 'PGRST116') return null; // Not found\n        throw error;\n      }\n      \n      return data ? this.mapUserFromDB(data) : null;\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur\n   */\n  async createUser(userData: Omit<User, 'id'>): Promise<User> {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n\n      const { data, error } = await supabase\n        .from('users')\n        .insert([dbUser])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouvel utilisateur avec un ID spécifique (pour l'authentification)\n   */\n  async createUserWithId(userId: string, userData: Omit<User, 'id'>): Promise<void> {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n      \n      const { error } = await supabase\n        .from('users')\n        .insert([{ ...dbUser, id: userId }]);\n\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'utilisateur avec ID:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Met à jour un utilisateur\n   */\n  async updateUser(userId: string, userData: Partial<User>): Promise<User> {\n    try {\n      const dbUser = this.mapUserToDB(userData);\n\n      const { data, error } = await supabase\n        .from('users')\n        .update({ ...dbUser, updated_at: new Date().toISOString() })\n        .eq('id', userId)\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime un utilisateur\n   */\n  async deleteUser(userId: string): Promise<void> {\n    try {\n      const { error } = await supabase\n        .from('users')\n        .delete()\n        .eq('id', userId);\n\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'utilisateur:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES COURS ====================\n\n  /**\n   * Récupère tous les cours avec leurs relations\n   */\n  async getCourses(): Promise<Course[]> {\n    try {\n      const { data, error } = await supabase\n        .from('courses')\n        .select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(this.mapCourseFromDB) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les cours d'un enseignant\n   */\n  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {\n    try {\n      const { data, error } = await supabase\n        .from('courses')\n        .select(`\n          *,\n          teacher:teacher_id(id, username, email, first_name, last_name, role),\n          student_group:student_group_id(*)\n        `)\n        .eq('teacher_id', teacherId)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(this.mapCourseFromDB) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours de l\\'enseignant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée un nouveau cours\n   */\n  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const dbCourse = this.mapCourseToDB(courseData);\n      \n      const { data, error } = await supabase\n        .from('courses')\n        .insert([dbCourse])\n        .select()\n        .single();\n\n      if (error) throw error;\n      \n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de la création du cours:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES PRÉSENCES ====================\n\n  /**\n   * Enregistre une présence\n   */\n  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {\n    try {\n      const dbAttendance = this.mapAttendanceToDB(attendanceData);\n      \n      const { data, error } = await supabase\n        .from('attendance')\n        .insert([dbAttendance])\n        .select()\n        .single();\n\n      if (error) throw error;\n      \n      return data.id;\n    } catch (error) {\n      console.error('Erreur lors de l\\'enregistrement de la présence:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère toutes les présences\n   */\n  async getAttendanceRecords(): Promise<AttendanceRecord[]> {\n    try {\n      const { data, error } = await supabase\n        .from('attendance')\n        .select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(*)\n        `)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n\n      return data?.map(this.mapAttendanceFromDB) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un cours\n   */\n  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {\n    try {\n      let query = supabase\n        .from('attendance')\n        .select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `)\n        .eq('course_id', courseId);\n      \n      if (date) {\n        query = query.eq('date', date);\n      }\n      \n      query = query.order('created_at', { ascending: false });\n      \n      const { data, error } = await query;\n\n      if (error) throw error;\n      \n      return data?.map(this.mapAttendanceFromDB) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Récupère les présences d'un étudiant\n   */\n  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {\n    try {\n      const { data, error } = await supabase\n        .from('attendance')\n        .select(`\n          *,\n          student:student_id(id, username, email, first_name, last_name, role),\n          course:course_id(id, name, code)\n        `)\n        .eq('student_id', studentId)\n        .order('date', { ascending: false });\n\n      if (error) throw error;\n      \n      return data?.map(this.mapAttendanceFromDB) || [];\n    } catch (error) {\n      console.error('Erreur lors de la récupération des présences de l\\'étudiant:', error);\n      throw error;\n    }\n  }\n\n  // ==================== GESTION DES FICHIERS ====================\n\n  /**\n   * Upload une image de profil\n   */\n  async uploadProfileImage(userId: string, file: File): Promise<string> {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/profile.${fileExt}`;\n      const filePath = `profile-images/${fileName}`;\n\n      const { error: uploadError } = await supabase.storage\n        .from('images')\n        .upload(filePath, file, { upsert: true });\n\n      if (uploadError) throw uploadError;\n\n      const { data } = supabase.storage\n        .from('images')\n        .getPublicUrl(filePath);\n\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload une image pour la reconnaissance faciale\n   */\n  async uploadFaceImage(userId: string, file: File): Promise<string> {\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `${userId}/${Date.now()}.${fileExt}`;\n      const filePath = `face-images/${fileName}`;\n\n      const { error: uploadError } = await supabase.storage\n        .from('images')\n        .upload(filePath, file);\n\n      if (uploadError) throw uploadError;\n\n      const { data } = supabase.storage\n        .from('images')\n        .getPublicUrl(filePath);\n\n      return data.publicUrl;\n    } catch (error) {\n      console.error('Erreur lors de l\\'upload de l\\'image faciale:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Supprime une image\n   */\n  async deleteImage(imageUrl: string): Promise<void> {\n    try {\n      // Extraire le chemin du fichier depuis l'URL\n      const urlParts = imageUrl.split('/');\n      const bucketIndex = urlParts.findIndex(part => part === 'images');\n      if (bucketIndex === -1) throw new Error('URL d\\'image invalide');\n      \n      const filePath = urlParts.slice(bucketIndex + 1).join('/');\n\n      const { error } = await supabase.storage\n        .from('images')\n        .remove([filePath]);\n\n      if (error) throw error;\n    } catch (error) {\n      console.error('Erreur lors de la suppression de l\\'image:', error);\n      throw error;\n    }\n  }\n\n  // ==================== STATISTIQUES ====================\n\n  /**\n   * Récupère les statistiques globales\n   */\n  async getGlobalStats(): Promise<any> {\n    try {\n      // Compter les utilisateurs par rôle\n      const { data: users, error: usersError } = await supabase\n        .from('users')\n        .select('role');\n\n      if (usersError) throw usersError;\n\n      // Compter les cours\n      const { count: coursesCount, error: coursesError } = await supabase\n        .from('courses')\n        .select('*', { count: 'exact', head: true });\n\n      if (coursesError) throw coursesError;\n\n      // Compter les présences\n      const { count: attendanceCount, error: attendanceError } = await supabase\n        .from('attendance')\n        .select('*', { count: 'exact', head: true });\n\n      if (attendanceError) throw attendanceError;\n\n      const stats = {\n        totalUsers: users?.length || 0,\n        totalStudents: users?.filter(user => user.role === UserRole.STUDENT).length || 0,\n        totalTeachers: users?.filter(user => user.role === UserRole.TEACHER).length || 0,\n        totalAdmins: users?.filter(user => user.role === UserRole.ADMIN).length || 0,\n        totalCourses: coursesCount || 0,\n        totalAttendances: attendanceCount || 0\n      };\n\n      return stats;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  }\n\n  // ==================== MÉTHODES DE MAPPING ====================\n\n  private mapUserFromDB(dbUser: any): User {\n    const firstName = dbUser.first_name || '';\n    const lastName = dbUser.last_name || '';\n    const role = dbUser.role || 'student';\n\n    return {\n      id: dbUser.id,\n      username: dbUser.username || '',\n      email: dbUser.email || '',\n      firstName,\n      lastName,\n      fullName: `${firstName} ${lastName}`.trim() || 'Utilisateur',\n      role: role as UserRole,\n      roleDisplay: this.getRoleDisplay(role as UserRole),\n      phoneNumber: dbUser.phone_number || undefined,\n      dateOfBirth: dbUser.date_of_birth || undefined,\n      address: dbUser.address || undefined,\n      profilePicture: dbUser.profile_picture || undefined,\n      isActive: dbUser.is_active !== false,\n      dateJoined: dbUser.created_at || new Date().toISOString(),\n      lastLogin: dbUser.last_login || undefined\n    };\n  }\n\n  private mapUserToDB(user: Partial<User>): any {\n    return {\n      username: user.username,\n      email: user.email,\n      first_name: user.firstName,\n      last_name: user.lastName,\n      role: user.role,\n      phone_number: user.phoneNumber,\n      date_of_birth: user.dateOfBirth,\n      address: user.address,\n      profile_picture: user.profilePicture,\n      is_active: user.isActive,\n      last_login: user.lastLogin\n    };\n  }\n\n  private mapCourseFromDB(dbCourse: any): Course {\n    return {\n      id: dbCourse.id,\n      name: dbCourse.name,\n      code: dbCourse.code,\n      description: dbCourse.description,\n      teacher: this.mapUserFromDB(dbCourse.teacher),\n      studentGroup: dbCourse.student_group,\n      schedule: dbCourse.schedule || [],\n      academicYear: dbCourse.academic_year,\n      semester: dbCourse.semester,\n      credits: dbCourse.credits,\n      isActive: dbCourse.is_active,\n      createdAt: dbCourse.created_at,\n      updatedAt: dbCourse.updated_at\n    };\n  }\n\n  private mapCourseToDB(course: Partial<Course>): any {\n    return {\n      name: course.name,\n      code: course.code,\n      description: course.description,\n      teacher_id: course.teacher?.id,\n      student_group_id: course.studentGroup?.id,\n      schedule: course.schedule,\n      academic_year: course.academicYear,\n      semester: course.semester,\n      credits: course.credits,\n      is_active: course.isActive\n    };\n  }\n\n  private mapAttendanceFromDB(dbAttendance: any): AttendanceRecord {\n    return {\n      id: dbAttendance.id,\n      student: this.mapUserFromDB(dbAttendance.student),\n      course: dbAttendance.course,\n      date: dbAttendance.date,\n      time: dbAttendance.time,\n      status: dbAttendance.status,\n      method: dbAttendance.method,\n      confidence: dbAttendance.confidence,\n      notes: dbAttendance.notes,\n      createdAt: dbAttendance.created_at,\n      updatedAt: dbAttendance.updated_at\n    };\n  }\n\n  private mapAttendanceToDB(attendance: Partial<AttendanceRecord>): any {\n    return {\n      student_id: attendance.student?.id,\n      course_id: attendance.course?.id,\n      date: attendance.date,\n      time: attendance.time,\n      status: attendance.status,\n      method: attendance.method,\n      confidence: attendance.confidence,\n      notes: attendance.notes\n    };\n  }\n\n\n\n  /**\n   * Récupère un utilisateur par email\n   */\n  async getUserByEmail(email: string): Promise<User> {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('email', email)\n        .single();\n\n      if (error) throw error;\n      if (!data) throw new Error('Utilisateur non trouvé');\n\n      return this.mapUserFromDB(data);\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'utilisateur par email:', error);\n      throw error;\n    }\n  }\n\n  private getRoleDisplay(role: UserRole): string {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'Administrateur';\n      case UserRole.TEACHER:\n        return 'Enseignant';\n      case UserRole.STUDENT:\n        return 'Étudiant';\n      default:\n        return 'Utilisateur';\n    }\n  }\n}\n\n// Instance singleton du service Supabase\nexport const supabaseService = new SupabaseService();\nexport default supabaseService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,SAAyCC,QAAQ,QAAQ,UAAU;AAEnE,MAAMC,eAAe,CAAC;EAEpB;;EAEA;AACF;AACA;EACE,MAAMC,QAAQA,CAAA,EAAoB;IAChC,IAAI;MACF,MAAM;QAAEC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,IAAI,CAACC,aAAa,CAAC,KAAI,EAAE;IAC5C,CAAC,CAAC,OAAON,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,WAAWA,CAACC,MAAc,EAAwB;IACtD,IAAI;MACF,MAAM;QAAEV,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXQ,EAAE,CAAC,IAAI,EAAED,MAAM,CAAC,CAChBE,MAAM,CAAC,CAAC;MAEX,IAAIX,KAAK,EAAE;QACT,IAAIA,KAAK,CAACY,IAAI,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC,CAAC;QAC5C,MAAMZ,KAAK;MACb;MAEA,OAAOD,IAAI,GAAG,IAAI,CAACO,aAAa,CAACP,IAAI,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMa,UAAUA,CAACC,QAA0B,EAAiB;IAC1D,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;MAEzC,MAAM;QAAEf,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbgB,MAAM,CAAC,CAACF,MAAM,CAAC,CAAC,CAChBb,MAAM,CAAC,CAAC,CACRS,MAAM,CAAC,CAAC;MAEX,IAAIX,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,IAAI,CAACM,aAAa,CAACP,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMkB,gBAAgBA,CAACT,MAAc,EAAEK,QAA0B,EAAiB;IAChF,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;MAEzC,MAAM;QAAEd;MAAM,CAAC,GAAG,MAAML,QAAQ,CAC7BM,IAAI,CAAC,OAAO,CAAC,CACbgB,MAAM,CAAC,CAAC;QAAE,GAAGF,MAAM;QAAEI,EAAE,EAAEV;MAAO,CAAC,CAAC,CAAC;MAEtC,IAAIT,KAAK,EAAE,MAAMA,KAAK;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoB,UAAUA,CAACX,MAAc,EAAEK,QAAuB,EAAiB;IACvE,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;MAEzC,MAAM;QAAEf,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACboB,MAAM,CAAC;QAAE,GAAGN,MAAM;QAAEO,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,CAAC,CAC3Dd,EAAE,CAAC,IAAI,EAAED,MAAM,CAAC,CAChBP,MAAM,CAAC,CAAC,CACRS,MAAM,CAAC,CAAC;MAEX,IAAIX,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,IAAI,CAACM,aAAa,CAACP,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMyB,UAAUA,CAAChB,MAAc,EAAiB;IAC9C,IAAI;MACF,MAAM;QAAET;MAAM,CAAC,GAAG,MAAML,QAAQ,CAC7BM,IAAI,CAAC,OAAO,CAAC,CACbyB,MAAM,CAAC,CAAC,CACRhB,EAAE,CAAC,IAAI,EAAED,MAAM,CAAC;MAEnB,IAAIT,KAAK,EAAE,MAAMA,KAAK;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAM2B,UAAUA,CAAA,EAAsB;IACpC,IAAI;MACF,MAAM;QAAE5B,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,IAAI,CAACuB,eAAe,CAAC,KAAI,EAAE;IAC9C,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM6B,mBAAmBA,CAACC,SAAiB,EAAqB;IAC9D,IAAI;MACF,MAAM;QAAE/B,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDQ,EAAE,CAAC,YAAY,EAAEoB,SAAS,CAAC,CAC3B3B,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,IAAI,CAACuB,eAAe,CAAC,KAAI,EAAE;IAC9C,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;MAClF,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM+B,YAAYA,CAACC,UAA8B,EAAmB;IAClE,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACF,UAAU,CAAC;MAE/C,MAAM;QAAEjC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfgB,MAAM,CAAC,CAACgB,QAAQ,CAAC,CAAC,CAClB/B,MAAM,CAAC,CAAC,CACRS,MAAM,CAAC,CAAC;MAEX,IAAIX,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAOD,IAAI,CAACoB,EAAE;IAChB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMmC,gBAAgBA,CAACC,cAA4C,EAAmB;IACpF,IAAI;MACF,MAAMC,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAACF,cAAc,CAAC;MAE3D,MAAM;QAAErC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBgB,MAAM,CAAC,CAACoB,YAAY,CAAC,CAAC,CACtBnC,MAAM,CAAC,CAAC,CACRS,MAAM,CAAC,CAAC;MAEX,IAAIX,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAOD,IAAI,CAACoB,EAAE;IAChB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMuC,oBAAoBA,CAAA,EAAgC;IACxD,IAAI;MACF,MAAM;QAAExC,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,IAAI,CAACmC,mBAAmB,CAAC,KAAI,EAAE;IAClD,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMyC,qBAAqBA,CAACC,QAAgB,EAAEC,IAAa,EAA+B;IACxF,IAAI;MACF,IAAIC,KAAK,GAAGjD,QAAQ,CACjBM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDQ,EAAE,CAAC,WAAW,EAAEgC,QAAQ,CAAC;MAE5B,IAAIC,IAAI,EAAE;QACRC,KAAK,GAAGA,KAAK,CAAClC,EAAE,CAAC,MAAM,EAAEiC,IAAI,CAAC;MAChC;MAEAC,KAAK,GAAGA,KAAK,CAACzC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAEvD,MAAM;QAAEL,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM4C,KAAK;MAEnC,IAAI5C,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,IAAI,CAACmC,mBAAmB,CAAC,KAAI,EAAE;IAClD,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM6C,sBAAsBA,CAACC,SAAiB,EAA+B;IAC3E,IAAI;MACF,MAAM;QAAE/C,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDQ,EAAE,CAAC,YAAY,EAAEoC,SAAS,CAAC,CAC3B3C,KAAK,CAAC,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAEtC,IAAIJ,KAAK,EAAE,MAAMA,KAAK;MAEtB,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,IAAI,CAACmC,mBAAmB,CAAC,KAAI,EAAE;IAClD,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACpF,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAM+C,kBAAkBA,CAACtC,MAAc,EAAEuC,IAAU,EAAmB;IACpE,IAAI;MACF,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,GAAG5C,MAAM,YAAYwC,OAAO,EAAE;MAC/C,MAAMK,QAAQ,GAAG,kBAAkBD,QAAQ,EAAE;MAE7C,MAAM;QAAErD,KAAK,EAAEuD;MAAY,CAAC,GAAG,MAAM5D,QAAQ,CAAC6D,OAAO,CAClDvD,IAAI,CAAC,QAAQ,CAAC,CACdwD,MAAM,CAACH,QAAQ,EAAEN,IAAI,EAAE;QAAEU,MAAM,EAAE;MAAK,CAAC,CAAC;MAE3C,IAAIH,WAAW,EAAE,MAAMA,WAAW;MAElC,MAAM;QAAExD;MAAK,CAAC,GAAGJ,QAAQ,CAAC6D,OAAO,CAC9BvD,IAAI,CAAC,QAAQ,CAAC,CACd0D,YAAY,CAACL,QAAQ,CAAC;MAEzB,OAAOvD,IAAI,CAAC6D,SAAS;IACvB,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM6D,eAAeA,CAACpD,MAAc,EAAEuC,IAAU,EAAmB;IACjE,IAAI;MACF,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,GAAG5C,MAAM,IAAIc,IAAI,CAACuC,GAAG,CAAC,CAAC,IAAIb,OAAO,EAAE;MACrD,MAAMK,QAAQ,GAAG,eAAeD,QAAQ,EAAE;MAE1C,MAAM;QAAErD,KAAK,EAAEuD;MAAY,CAAC,GAAG,MAAM5D,QAAQ,CAAC6D,OAAO,CAClDvD,IAAI,CAAC,QAAQ,CAAC,CACdwD,MAAM,CAACH,QAAQ,EAAEN,IAAI,CAAC;MAEzB,IAAIO,WAAW,EAAE,MAAMA,WAAW;MAElC,MAAM;QAAExD;MAAK,CAAC,GAAGJ,QAAQ,CAAC6D,OAAO,CAC9BvD,IAAI,CAAC,QAAQ,CAAC,CACd0D,YAAY,CAACL,QAAQ,CAAC;MAEzB,OAAOvD,IAAI,CAAC6D,SAAS;IACvB,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM+D,WAAWA,CAACC,QAAgB,EAAiB;IACjD,IAAI;MACF;MACA,MAAMC,QAAQ,GAAGD,QAAQ,CAACb,KAAK,CAAC,GAAG,CAAC;MACpC,MAAMe,WAAW,GAAGD,QAAQ,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,KAAK,QAAQ,CAAC;MACjE,IAAIF,WAAW,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,uBAAuB,CAAC;MAEhE,MAAMf,QAAQ,GAAGW,QAAQ,CAACK,KAAK,CAACJ,WAAW,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;MAE1D,MAAM;QAAEvE;MAAM,CAAC,GAAG,MAAML,QAAQ,CAAC6D,OAAO,CACrCvD,IAAI,CAAC,QAAQ,CAAC,CACduE,MAAM,CAAC,CAAClB,QAAQ,CAAC,CAAC;MAErB,IAAItD,KAAK,EAAE,MAAMA,KAAK;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMyE,cAAcA,CAAA,EAAiB;IACnC,IAAI;MACF;MACA,MAAM;QAAE1E,IAAI,EAAE2E,KAAK;QAAE1E,KAAK,EAAE2E;MAAW,CAAC,GAAG,MAAMhF,QAAQ,CACtDM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,MAAM,CAAC;MAEjB,IAAIyE,UAAU,EAAE,MAAMA,UAAU;;MAEhC;MACA,MAAM;QAAEC,KAAK,EAAEC,YAAY;QAAE7E,KAAK,EAAE8E;MAAa,CAAC,GAAG,MAAMnF,QAAQ,CAChEM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,EAAE;QAAE0E,KAAK,EAAE,OAAO;QAAEG,IAAI,EAAE;MAAK,CAAC,CAAC;MAE9C,IAAID,YAAY,EAAE,MAAMA,YAAY;;MAEpC;MACA,MAAM;QAAEF,KAAK,EAAEI,eAAe;QAAEhF,KAAK,EAAEiF;MAAgB,CAAC,GAAG,MAAMtF,QAAQ,CACtEM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC,GAAG,EAAE;QAAE0E,KAAK,EAAE,OAAO;QAAEG,IAAI,EAAE;MAAK,CAAC,CAAC;MAE9C,IAAIE,eAAe,EAAE,MAAMA,eAAe;MAE1C,MAAMC,KAAK,GAAG;QACZC,UAAU,EAAE,CAAAT,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,MAAM,KAAI,CAAC;QAC9BC,aAAa,EAAE,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK5F,QAAQ,CAAC6F,OAAO,CAAC,CAACL,MAAM,KAAI,CAAC;QAChFM,aAAa,EAAE,CAAAhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK5F,QAAQ,CAAC+F,OAAO,CAAC,CAACP,MAAM,KAAI,CAAC;QAChFQ,WAAW,EAAE,CAAAlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK5F,QAAQ,CAACiG,KAAK,CAAC,CAACT,MAAM,KAAI,CAAC;QAC5EU,YAAY,EAAEjB,YAAY,IAAI,CAAC;QAC/BkB,gBAAgB,EAAEf,eAAe,IAAI;MACvC,CAAC;MAED,OAAOE,KAAK;IACd,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEQM,aAAaA,CAACS,MAAW,EAAQ;IACvC,MAAMiF,SAAS,GAAGjF,MAAM,CAACkF,UAAU,IAAI,EAAE;IACzC,MAAMC,QAAQ,GAAGnF,MAAM,CAACoF,SAAS,IAAI,EAAE;IACvC,MAAMX,IAAI,GAAGzE,MAAM,CAACyE,IAAI,IAAI,SAAS;IAErC,OAAO;MACLrE,EAAE,EAAEJ,MAAM,CAACI,EAAE;MACbiF,QAAQ,EAAErF,MAAM,CAACqF,QAAQ,IAAI,EAAE;MAC/BC,KAAK,EAAEtF,MAAM,CAACsF,KAAK,IAAI,EAAE;MACzBL,SAAS;MACTE,QAAQ;MACRI,QAAQ,EAAE,GAAGN,SAAS,IAAIE,QAAQ,EAAE,CAACK,IAAI,CAAC,CAAC,IAAI,aAAa;MAC5Df,IAAI,EAAEA,IAAgB;MACtBgB,WAAW,EAAE,IAAI,CAACC,cAAc,CAACjB,IAAgB,CAAC;MAClDkB,WAAW,EAAE3F,MAAM,CAAC4F,YAAY,IAAIC,SAAS;MAC7CC,WAAW,EAAE9F,MAAM,CAAC+F,aAAa,IAAIF,SAAS;MAC9CG,OAAO,EAAEhG,MAAM,CAACgG,OAAO,IAAIH,SAAS;MACpCI,cAAc,EAAEjG,MAAM,CAACkG,eAAe,IAAIL,SAAS;MACnDM,QAAQ,EAAEnG,MAAM,CAACoG,SAAS,KAAK,KAAK;MACpCC,UAAU,EAAErG,MAAM,CAACsG,UAAU,IAAI,IAAI9F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACzD8F,SAAS,EAAEvG,MAAM,CAACwG,UAAU,IAAIX;IAClC,CAAC;EACH;EAEQ5F,WAAWA,CAACuE,IAAmB,EAAO;IAC5C,OAAO;MACLa,QAAQ,EAAEb,IAAI,CAACa,QAAQ;MACvBC,KAAK,EAAEd,IAAI,CAACc,KAAK;MACjBJ,UAAU,EAAEV,IAAI,CAACS,SAAS;MAC1BG,SAAS,EAAEZ,IAAI,CAACW,QAAQ;MACxBV,IAAI,EAAED,IAAI,CAACC,IAAI;MACfmB,YAAY,EAAEpB,IAAI,CAACmB,WAAW;MAC9BI,aAAa,EAAEvB,IAAI,CAACsB,WAAW;MAC/BE,OAAO,EAAExB,IAAI,CAACwB,OAAO;MACrBE,eAAe,EAAE1B,IAAI,CAACyB,cAAc;MACpCG,SAAS,EAAE5B,IAAI,CAAC2B,QAAQ;MACxBK,UAAU,EAAEhC,IAAI,CAAC+B;IACnB,CAAC;EACH;EAEQ1F,eAAeA,CAACK,QAAa,EAAU;IAC7C,OAAO;MACLd,EAAE,EAAEc,QAAQ,CAACd,EAAE;MACf+B,IAAI,EAAEjB,QAAQ,CAACiB,IAAI;MACnBtC,IAAI,EAAEqB,QAAQ,CAACrB,IAAI;MACnB4G,WAAW,EAAEvF,QAAQ,CAACuF,WAAW;MACjCC,OAAO,EAAE,IAAI,CAACnH,aAAa,CAAC2B,QAAQ,CAACwF,OAAO,CAAC;MAC7CC,YAAY,EAAEzF,QAAQ,CAAC0F,aAAa;MACpCC,QAAQ,EAAE3F,QAAQ,CAAC2F,QAAQ,IAAI,EAAE;MACjCC,YAAY,EAAE5F,QAAQ,CAAC6F,aAAa;MACpCC,QAAQ,EAAE9F,QAAQ,CAAC8F,QAAQ;MAC3BC,OAAO,EAAE/F,QAAQ,CAAC+F,OAAO;MACzBd,QAAQ,EAAEjF,QAAQ,CAACkF,SAAS;MAC5Bc,SAAS,EAAEhG,QAAQ,CAACoF,UAAU;MAC9Ba,SAAS,EAAEjG,QAAQ,CAACX;IACtB,CAAC;EACH;EAEQY,aAAaA,CAACiG,MAAuB,EAAO;IAAA,IAAAC,eAAA,EAAAC,oBAAA;IAClD,OAAO;MACLnF,IAAI,EAAEiF,MAAM,CAACjF,IAAI;MACjBtC,IAAI,EAAEuH,MAAM,CAACvH,IAAI;MACjB4G,WAAW,EAAEW,MAAM,CAACX,WAAW;MAC/Bc,UAAU,GAAAF,eAAA,GAAED,MAAM,CAACV,OAAO,cAAAW,eAAA,uBAAdA,eAAA,CAAgBjH,EAAE;MAC9BoH,gBAAgB,GAAAF,oBAAA,GAAEF,MAAM,CAACT,YAAY,cAAAW,oBAAA,uBAAnBA,oBAAA,CAAqBlH,EAAE;MACzCyG,QAAQ,EAAEO,MAAM,CAACP,QAAQ;MACzBE,aAAa,EAAEK,MAAM,CAACN,YAAY;MAClCE,QAAQ,EAAEI,MAAM,CAACJ,QAAQ;MACzBC,OAAO,EAAEG,MAAM,CAACH,OAAO;MACvBb,SAAS,EAAEgB,MAAM,CAACjB;IACpB,CAAC;EACH;EAEQ1E,mBAAmBA,CAACH,YAAiB,EAAoB;IAC/D,OAAO;MACLlB,EAAE,EAAEkB,YAAY,CAAClB,EAAE;MACnBqH,OAAO,EAAE,IAAI,CAAClI,aAAa,CAAC+B,YAAY,CAACmG,OAAO,CAAC;MACjDL,MAAM,EAAE9F,YAAY,CAAC8F,MAAM;MAC3BxF,IAAI,EAAEN,YAAY,CAACM,IAAI;MACvB8F,IAAI,EAAEpG,YAAY,CAACoG,IAAI;MACvBC,MAAM,EAAErG,YAAY,CAACqG,MAAM;MAC3BC,MAAM,EAAEtG,YAAY,CAACsG,MAAM;MAC3BC,UAAU,EAAEvG,YAAY,CAACuG,UAAU;MACnCC,KAAK,EAAExG,YAAY,CAACwG,KAAK;MACzBZ,SAAS,EAAE5F,YAAY,CAACgF,UAAU;MAClCa,SAAS,EAAE7F,YAAY,CAACf;IAC1B,CAAC;EACH;EAEQgB,iBAAiBA,CAACwG,UAAqC,EAAO;IAAA,IAAAC,mBAAA,EAAAC,kBAAA;IACpE,OAAO;MACLC,UAAU,GAAAF,mBAAA,GAAED,UAAU,CAACN,OAAO,cAAAO,mBAAA,uBAAlBA,mBAAA,CAAoB5H,EAAE;MAClC+H,SAAS,GAAAF,kBAAA,GAAEF,UAAU,CAACX,MAAM,cAAAa,kBAAA,uBAAjBA,kBAAA,CAAmB7H,EAAE;MAChCwB,IAAI,EAAEmG,UAAU,CAACnG,IAAI;MACrB8F,IAAI,EAAEK,UAAU,CAACL,IAAI;MACrBC,MAAM,EAAEI,UAAU,CAACJ,MAAM;MACzBC,MAAM,EAAEG,UAAU,CAACH,MAAM;MACzBC,UAAU,EAAEE,UAAU,CAACF,UAAU;MACjCC,KAAK,EAAEC,UAAU,CAACD;IACpB,CAAC;EACH;;EAIA;AACF;AACA;EACE,MAAMM,cAAcA,CAAC9C,KAAa,EAAiB;IACjD,IAAI;MACF,MAAM;QAAEtG,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAML,QAAQ,CACnCM,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXQ,EAAE,CAAC,OAAO,EAAE2F,KAAK,CAAC,CAClB1F,MAAM,CAAC,CAAC;MAEX,IAAIX,KAAK,EAAE,MAAMA,KAAK;MACtB,IAAI,CAACD,IAAI,EAAE,MAAM,IAAIsE,KAAK,CAAC,wBAAwB,CAAC;MAEpD,OAAO,IAAI,CAAC/D,aAAa,CAACP,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;MACnF,MAAMA,KAAK;IACb;EACF;EAEQyG,cAAcA,CAACjB,IAAc,EAAU;IAC7C,QAAQA,IAAI;MACV,KAAK5F,QAAQ,CAACiG,KAAK;QACjB,OAAO,gBAAgB;MACzB,KAAKjG,QAAQ,CAAC+F,OAAO;QACnB,OAAO,YAAY;MACrB,KAAK/F,QAAQ,CAAC6F,OAAO;QACnB,OAAO,UAAU;MACnB;QACE,OAAO,aAAa;IACxB;EACF;AACF;;AAEA;AACA,OAAO,MAAM2D,eAAe,GAAG,IAAIvJ,eAAe,CAAC,CAAC;AACpD,eAAeuJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}