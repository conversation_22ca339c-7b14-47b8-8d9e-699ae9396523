{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\n/**\r\n * Pads the smaller dimension of an image tensor with zeros, such that width === height.\r\n *\r\n * @param imgTensor The image tensor.\r\n * @param isCenterImage (optional, default: false) If true, add an equal amount of padding on\r\n * both sides of the minor dimension oof the image.\r\n * @returns The padded tensor with width === height.\r\n */\nexport function padToSquare(imgTensor, isCenterImage) {\n  if (isCenterImage === void 0) {\n    isCenterImage = false;\n  }\n  return tf.tidy(function () {\n    var _a = imgTensor.shape.slice(1),\n      height = _a[0],\n      width = _a[1];\n    if (height === width) {\n      return imgTensor;\n    }\n    var dimDiff = Math.abs(height - width);\n    var paddingAmount = Math.round(dimDiff * (isCenterImage ? 0.5 : 1));\n    var paddingAxis = height > width ? 2 : 1;\n    var createPaddingTensor = function (paddingAmount) {\n      var paddingTensorShape = imgTensor.shape.slice();\n      paddingTensorShape[paddingAxis] = paddingAmount;\n      return tf.fill(paddingTensorShape, 0);\n    };\n    var paddingTensorAppend = createPaddingTensor(paddingAmount);\n    var remainingPaddingAmount = dimDiff - paddingTensorAppend.shape[paddingAxis];\n    var paddingTensorPrepend = isCenterImage && remainingPaddingAmount ? createPaddingTensor(remainingPaddingAmount) : null;\n    var tensorsToStack = [paddingTensorPrepend, imgTensor, paddingTensorAppend].filter(function (t) {\n      return !!t;\n    }).map(function (t) {\n      return t.toFloat();\n    });\n    return tf.concat(tensorsToStack, paddingAxis);\n  });\n}", "map": {"version": 3, "names": ["tf", "padToSquare", "imgTensor", "isCenterImage", "tidy", "_a", "shape", "slice", "height", "width", "dimDiff", "Math", "abs", "paddingAmount", "round", "paddingAxis", "createPaddingTensor", "paddingTensorShape", "fill", "paddingTensorAppend", "remainingPaddingAmount", "paddingTensorPrepend", "tensorsToStack", "filter", "t", "map", "toFloat", "concat"], "sources": ["../../../src/ops/padToSquare.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C;;;;;;;;AAQA,OAAM,SAAUC,WAAWA,CACzBC,SAAsB,EACtBC,aAA8B;EAA9B,IAAAA,aAAA;IAAAA,aAAA,QAA8B;EAAA;EAE9B,OAAOH,EAAE,CAACI,IAAI,CAAC;IAEP,IAAAC,EAAA,GAAAH,SAAA,CAAAI,KAAA,CAAAC,KAAA,GAA0C;MAAzCC,MAAA,GAAAH,EAAA,GAAM;MAAEI,KAAA,GAAAJ,EAAA,GAAiC;IAChD,IAAIG,MAAM,KAAKC,KAAK,EAAE;MACpB,OAAOP,SAAS;;IAGlB,IAAMQ,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,GAAGC,KAAK,CAAC;IACxC,IAAMI,aAAa,GAAGF,IAAI,CAACG,KAAK,CAACJ,OAAO,IAAIP,aAAa,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IACrE,IAAMY,WAAW,GAAGP,MAAM,GAAGC,KAAK,GAAG,CAAC,GAAG,CAAC;IAE1C,IAAMO,mBAAmB,GAAG,SAAAA,CAACH,aAAqB;MAChD,IAAMI,kBAAkB,GAAGf,SAAS,CAACI,KAAK,CAACC,KAAK,EAAE;MAClDU,kBAAkB,CAACF,WAAW,CAAC,GAAGF,aAAa;MAC/C,OAAOb,EAAE,CAACkB,IAAI,CAACD,kBAAkB,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,IAAME,mBAAmB,GAAGH,mBAAmB,CAACH,aAAa,CAAC;IAC9D,IAAMO,sBAAsB,GAAGV,OAAO,GAAIS,mBAAmB,CAACb,KAAK,CAACS,WAAW,CAAY;IAE3F,IAAMM,oBAAoB,GAAGlB,aAAa,IAAIiB,sBAAsB,GAChEJ,mBAAmB,CAACI,sBAAsB,CAAC,GAC3C,IAAI;IAER,IAAME,cAAc,GAAG,CACrBD,oBAAoB,EACpBnB,SAAS,EACTiB,mBAAmB,CACpB,CACEI,MAAM,CAAC,UAAAC,CAAC;MAAI,QAAC,CAACA,CAAC;IAAH,CAAG,CAAC,CAChBC,GAAG,CAAC,UAACD,CAAY;MAAK,OAAAA,CAAC,CAACE,OAAO,EAAE;IAAX,CAAW,CAAkB;IACtD,OAAO1B,EAAE,CAAC2B,MAAM,CAACL,cAAc,EAAEP,WAAW,CAAC;EAC/C,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}