{"ast": null, "code": "import { env } from '../env';\nexport function bufferToImage(buf) {\n  return new Promise(function (resolve, reject) {\n    if (!(buf instanceof Blob)) {\n      return reject('bufferToImage - expected buf to be of type: Blob');\n    }\n    var reader = new FileReader();\n    reader.onload = function () {\n      if (typeof reader.result !== 'string') {\n        return reject('bufferToImage - expected reader.result to be a string, in onload');\n      }\n      var img = env.getEnv().createImageElement();\n      img.onload = function () {\n        return resolve(img);\n      };\n      img.onerror = reject;\n      img.src = reader.result;\n    };\n    reader.onerror = reject;\n    reader.readAsDataURL(buf);\n  });\n}", "map": {"version": 3, "names": ["env", "bufferToImage", "buf", "Promise", "resolve", "reject", "Blob", "reader", "FileReader", "onload", "result", "img", "getEnv", "createImageElement", "onerror", "src", "readAsDataURL"], "sources": ["../../../src/dom/bufferToImage.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAE5B,OAAM,SAAUC,aAAaA,CAACC,GAAS;EACrC,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM;IACjC,IAAI,EAAEH,GAAG,YAAYI,IAAI,CAAC,EAAE;MAC1B,OAAOD,MAAM,CAAC,kDAAkD,CAAC;;IAGnE,IAAME,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG;MACd,IAAI,OAAOF,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;QACrC,OAAOL,MAAM,CAAC,kEAAkE,CAAC;;MAGnF,IAAMM,GAAG,GAAGX,GAAG,CAACY,MAAM,EAAE,CAACC,kBAAkB,EAAE;MAC7CF,GAAG,CAACF,MAAM,GAAG;QAAM,OAAAL,OAAO,CAACO,GAAG,CAAC;MAAZ,CAAY;MAC/BA,GAAG,CAACG,OAAO,GAAGT,MAAM;MACpBM,GAAG,CAACI,GAAG,GAAGR,MAAM,CAACG,MAAM;IACzB,CAAC;IACDH,MAAM,CAACO,OAAO,GAAGT,MAAM;IACvBE,MAAM,CAACS,aAAa,CAACd,GAAG,CAAC;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}