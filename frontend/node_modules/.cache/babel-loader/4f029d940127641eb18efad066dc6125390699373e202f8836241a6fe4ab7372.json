{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Composant principal de l'application PresencePro\n */\n\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { UserRole } from './types';\nimport MainLayout from './components/Layout/MainLayout';\nimport LoginPage from './pages/LoginPage';\nimport DashboardPage from './pages/DashboardPage';\nimport AdminDashboard from './pages/AdminDashboard';\nimport TeacherDashboard from './pages/TeacherDashboard';\nimport StudentDashboard from './pages/StudentDashboard';\nimport AttendanceSessionPage from './pages/AttendanceSessionPage';\nimport FaceRecognitionTestPage from './pages/FaceRecognitionTestPage';\nimport SupabaseTest from './components/Firebase/FirebaseTest';\nimport SupabaseTestSimple from './pages/SupabaseTestSimple';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Configuration du thème Material-UI\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    background: {\n      default: '#f5f5f5'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h4: {\n      fontWeight: 600\n    },\n    h6: {\n      fontWeight: 500\n    }\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          borderRadius: 8\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8\n        }\n      }\n    }\n  }\n});\n\n// Composant pour protéger les routes authentifiées avec vérification de rôle\n\nconst ProtectedRoute = ({\n  children,\n  requiredRole\n}) => {\n  _s();\n  const {\n    user,\n    loading,\n    isAuthenticated,\n    getRoleBasedRoute\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Chargement...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 12\n    }, this);\n  }\n  if (!isAuthenticated || !user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Vérifier le rôle requis si spécifié\n  if (requiredRole && user.role !== requiredRole) {\n    // Rediriger vers le dashboard approprié selon le rôle de l'utilisateur\n    const userDashboard = getRoleBasedRoute();\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: userDashboard,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(MainLayout, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 10\n  }, this);\n};\n\n// Composant pour rediriger les utilisateurs connectés vers leur dashboard\n_s(ProtectedRoute, \"pXBjVgVlFSd/VXEdjafYfNrHaCA=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  _s2();\n  const {\n    user,\n    loading,\n    isAuthenticated,\n    getRoleBasedRoute\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Chargement...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 12\n    }, this);\n  }\n  if (isAuthenticated && user) {\n    // Rediriger vers le dashboard approprié selon le rôle\n    const userDashboard = getRoleBasedRoute();\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: userDashboard,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Composant principal des routes\n_s2(PublicRoute, \"pXBjVgVlFSd/VXEdjafYfNrHaCA=\", false, function () {\n  return [useAuth];\n});\n_c2 = PublicRoute;\nconst AppRoutes = () => {\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/admin-dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: UserRole.ADMIN,\n        children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/teacher-dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: UserRole.TEACHER,\n        children: /*#__PURE__*/_jsxDEV(TeacherDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/student-dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: UserRole.STUDENT,\n        children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/attendance-session\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(AttendanceSessionPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/face-recognition-test\",\n      element: /*#__PURE__*/_jsxDEV(FaceRecognitionTestPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/supabase-test\",\n      element: /*#__PURE__*/_jsxDEV(SupabaseTestSimple, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/firebase-test\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(SupabaseTest, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n\n// Composant principal de l'application\n_c3 = AppRoutes;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n};\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"AppRoutes\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "UserRole", "MainLayout", "LoginPage", "DashboardPage", "AdminDashboard", "TeacherDashboard", "StudentDashboard", "AttendanceSessionPage", "FaceRecognitionTestPage", "SupabaseTest", "SupabaseTestSimple", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "theme", "palette", "primary", "main", "secondary", "background", "default", "typography", "fontFamily", "h4", "fontWeight", "h6", "components", "MuiCard", "styleOverrides", "root", "boxShadow", "borderRadius", "MuiB<PERSON>on", "textTransform", "ProtectedRoute", "children", "requiredRole", "_s", "user", "loading", "isAuthenticated", "getRoleBasedRoute", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "role", "userDashboard", "_c", "PublicRoute", "_s2", "_c2", "AppRoutes", "path", "element", "ADMIN", "TEACHER", "STUDENT", "_c3", "App", "_c4", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/App.tsx"], "sourcesContent": ["/**\n * Composant principal de l'application PresencePro\n */\n\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { UserRole } from './types';\nimport MainLayout from './components/Layout/MainLayout';\nimport LoginPage from './pages/LoginPage';\nimport DashboardPage from './pages/DashboardPage';\nimport AdminDashboard from './pages/AdminDashboard';\nimport TeacherDashboard from './pages/TeacherDashboard';\nimport StudentDashboard from './pages/StudentDashboard';\nimport AttendanceSessionPage from './pages/AttendanceSessionPage';\nimport FaceRecognitionTestPage from './pages/FaceRecognitionTestPage';\nimport SupabaseTest from './components/Firebase/FirebaseTest';\nimport SupabaseTestSimple from './pages/SupabaseTestSimple';\nimport TestConnection from './components/TestConnection';\n\n// Configuration du thème Material-UI\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h4: {\n      fontWeight: 600,\n    },\n    h6: {\n      fontWeight: 500,\n    },\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          borderRadius: 8,\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8,\n        },\n      },\n    },\n  },\n});\n\n// Composant pour protéger les routes authentifiées avec vérification de rôle\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRole?: UserRole;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredRole }) => {\n  const { user, loading, isAuthenticated, getRoleBasedRoute } = useAuth();\n\n  if (loading) {\n    return <div>Chargement...</div>;\n  }\n\n  if (!isAuthenticated || !user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Vérifier le rôle requis si spécifié\n  if (requiredRole && user.role !== requiredRole) {\n    // Rediriger vers le dashboard approprié selon le rôle de l'utilisateur\n    const userDashboard = getRoleBasedRoute();\n    return <Navigate to={userDashboard} replace />;\n  }\n\n  return <MainLayout>{children}</MainLayout>;\n};\n\n// Composant pour rediriger les utilisateurs connectés vers leur dashboard\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { user, loading, isAuthenticated, getRoleBasedRoute } = useAuth();\n\n  if (loading) {\n    return <div>Chargement...</div>;\n  }\n\n  if (isAuthenticated && user) {\n    // Rediriger vers le dashboard approprié selon le rôle\n    const userDashboard = getRoleBasedRoute();\n    return <Navigate to={userDashboard} replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Composant principal des routes\nconst AppRoutes: React.FC = () => {\n  return (\n    <Routes>\n      {/* Route publique - Page de connexion */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute>\n            <LoginPage />\n          </PublicRoute>\n        }\n      />\n\n      {/* Routes protégées par rôle */}\n\n      {/* Dashboard Administrateur */}\n      <Route\n        path=\"/admin-dashboard\"\n        element={\n          <ProtectedRoute requiredRole={UserRole.ADMIN}>\n            <AdminDashboard />\n          </ProtectedRoute>\n        }\n      />\n\n      {/* Dashboard Professeur */}\n      <Route\n        path=\"/teacher-dashboard\"\n        element={\n          <ProtectedRoute requiredRole={UserRole.TEACHER}>\n            <TeacherDashboard />\n          </ProtectedRoute>\n        }\n      />\n\n      {/* Dashboard Étudiant */}\n      <Route\n        path=\"/student-dashboard\"\n        element={\n          <ProtectedRoute requiredRole={UserRole.STUDENT}>\n            <StudentDashboard />\n          </ProtectedRoute>\n        }\n      />\n\n      {/* Routes protégées générales */}\n      <Route\n        path=\"/dashboard\"\n        element={\n          <ProtectedRoute>\n            <DashboardPage />\n          </ProtectedRoute>\n        }\n      />\n\n      <Route\n        path=\"/attendance-session\"\n        element={\n          <ProtectedRoute>\n            <AttendanceSessionPage />\n          </ProtectedRoute>\n        }\n      />\n\n      {/* Routes de test */}\n      <Route\n        path=\"/face-recognition-test\"\n        element={<FaceRecognitionTestPage />}\n      />\n\n      <Route\n        path=\"/supabase-test\"\n        element={<SupabaseTestSimple />}\n      />\n\n      <Route\n        path=\"/firebase-test\"\n        element={\n          <ProtectedRoute>\n            <SupabaseTest />\n          </ProtectedRoute>\n        }\n      />\n\n      {/* Redirection par défaut vers la page de connexion */}\n      <Route path=\"/\" element={<Navigate to=\"/login\" replace />} />\n\n      {/* Route 404 - Rediriger vers login */}\n      <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n    </Routes>\n  );\n};\n\n// Composant principal de l'application\nconst App: React.FC = () => {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <AuthProvider>\n        <Router>\n          <AppRoutes />\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,kBAAkB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG5D;AACA,MAAMC,KAAK,GAAGnB,WAAW,CAAC;EACxBoB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,4CAA4C;IACxDC,EAAE,EAAE;MACFC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFD,UAAU,EAAE;IACd;EACF,CAAC;EACDE,UAAU,EAAE;IACVC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,SAAS,EAAE,2BAA2B;UACtCC,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTJ,cAAc,EAAE;QACdC,IAAI,EAAE;UACJI,aAAa,EAAE,MAAM;UACrBF,YAAY,EAAE;QAChB;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;;AAMA,MAAMG,cAA6C,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM;IAAEC,IAAI;IAAEC,OAAO;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAG3C,OAAO,CAAC,CAAC;EAEvE,IAAIyC,OAAO,EAAE;IACX,oBAAO5B,OAAA;MAAAwB,QAAA,EAAK;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjC;EAEA,IAAI,CAACL,eAAe,IAAI,CAACF,IAAI,EAAE;IAC7B,oBAAO3B,OAAA,CAAClB,QAAQ;MAACqD,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACA,IAAIT,YAAY,IAAIE,IAAI,CAACU,IAAI,KAAKZ,YAAY,EAAE;IAC9C;IACA,MAAMa,aAAa,GAAGR,iBAAiB,CAAC,CAAC;IACzC,oBAAO9B,OAAA,CAAClB,QAAQ;MAACqD,EAAE,EAAEG,aAAc;MAACF,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAChD;EAEA,oBAAOlC,OAAA,CAACX,UAAU;IAAAmC,QAAA,EAAEA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAa,CAAC;AAC5C,CAAC;;AAED;AAAAR,EAAA,CArBMH,cAA6C;EAAA,QACapC,OAAO;AAAA;AAAAoD,EAAA,GADjEhB,cAA6C;AAsBnD,MAAMiB,WAAoD,GAAGA,CAAC;EAAEhB;AAAS,CAAC,KAAK;EAAAiB,GAAA;EAC7E,MAAM;IAAEd,IAAI;IAAEC,OAAO;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAG3C,OAAO,CAAC,CAAC;EAEvE,IAAIyC,OAAO,EAAE;IACX,oBAAO5B,OAAA;MAAAwB,QAAA,EAAK;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjC;EAEA,IAAIL,eAAe,IAAIF,IAAI,EAAE;IAC3B;IACA,MAAMW,aAAa,GAAGR,iBAAiB,CAAC,CAAC;IACzC,oBAAO9B,OAAA,CAAClB,QAAQ;MAACqD,EAAE,EAAEG,aAAc;MAACF,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAChD;EAEA,oBAAOlC,OAAA,CAAAE,SAAA;IAAAsB,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAiB,GAAA,CAhBMD,WAAoD;EAAA,QACMrD,OAAO;AAAA;AAAAuD,GAAA,GADjEF,WAAoD;AAiB1D,MAAMG,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACE3C,OAAA,CAACpB,MAAM;IAAA4C,QAAA,gBAELxB,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,QAAQ;MACbC,OAAO,eACL7C,OAAA,CAACwC,WAAW;QAAAhB,QAAA,eACVxB,OAAA,CAACV,SAAS;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAKFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,kBAAkB;MACvBC,OAAO,eACL7C,OAAA,CAACuB,cAAc;QAACE,YAAY,EAAErC,QAAQ,CAAC0D,KAAM;QAAAtB,QAAA,eAC3CxB,OAAA,CAACR,cAAc;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,oBAAoB;MACzBC,OAAO,eACL7C,OAAA,CAACuB,cAAc;QAACE,YAAY,EAAErC,QAAQ,CAAC2D,OAAQ;QAAAvB,QAAA,eAC7CxB,OAAA,CAACP,gBAAgB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,oBAAoB;MACzBC,OAAO,eACL7C,OAAA,CAACuB,cAAc;QAACE,YAAY,EAAErC,QAAQ,CAAC4D,OAAQ;QAAAxB,QAAA,eAC7CxB,OAAA,CAACN,gBAAgB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,YAAY;MACjBC,OAAO,eACL7C,OAAA,CAACuB,cAAc;QAAAC,QAAA,eACbxB,OAAA,CAACT,aAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,qBAAqB;MAC1BC,OAAO,eACL7C,OAAA,CAACuB,cAAc;QAAAC,QAAA,eACbxB,OAAA,CAACL,qBAAqB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,wBAAwB;MAC7BC,OAAO,eAAE7C,OAAA,CAACJ,uBAAuB;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,gBAAgB;MACrBC,OAAO,eAAE7C,OAAA,CAACF,kBAAkB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFlC,OAAA,CAACnB,KAAK;MACJ+D,IAAI,EAAC,gBAAgB;MACrBC,OAAO,eACL7C,OAAA,CAACuB,cAAc;QAAAC,QAAA,eACbxB,OAAA,CAACH,YAAY;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFlC,OAAA,CAACnB,KAAK;MAAC+D,IAAI,EAAC,GAAG;MAACC,OAAO,eAAE7C,OAAA,CAAClB,QAAQ;QAACqD,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7DlC,OAAA,CAACnB,KAAK;MAAC+D,IAAI,EAAC,GAAG;MAACC,OAAO,eAAE7C,OAAA,CAAClB,QAAQ;QAACqD,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvD,CAAC;AAEb,CAAC;;AAED;AAAAe,GAAA,GA7FMN,SAAmB;AA8FzB,MAAMO,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACElD,OAAA,CAACjB,aAAa;IAACoB,KAAK,EAAEA,KAAM;IAAAqB,QAAA,gBAC1BxB,OAAA,CAACf,WAAW;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACflC,OAAA,CAACd,YAAY;MAAAsC,QAAA,eACXxB,OAAA,CAACrB,MAAM;QAAA6C,QAAA,eACLxB,OAAA,CAAC2C,SAAS;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACiB,GAAA,GAXID,GAAa;AAanB,eAAeA,GAAG;AAAC,IAAAX,EAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}