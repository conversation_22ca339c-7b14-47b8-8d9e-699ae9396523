{"ast": null, "code": "/**\n * Types TypeScript pour PresencePro\n */\n\n// Types d'utilisateur\nexport let UserRole = /*#__PURE__*/function (UserRole) {\n  UserRole[\"ADMIN\"] = \"admin\";\n  UserRole[\"TEACHER\"] = \"teacher\";\n  UserRole[\"STUDENT\"] = \"student\";\n  return UserRole;\n}({});\n\n// Types d'authentification\n\n// Types de cours\n\n// Types de groupes d'étudiants\n\n// Types de présence\n\nexport let AttendanceStatus = /*#__PURE__*/function (AttendanceStatus) {\n  AttendanceStatus[\"PRESENT\"] = \"present\";\n  AttendanceStatus[\"ABSENT\"] = \"absent\";\n  AttendanceStatus[\"LATE\"] = \"late\";\n  AttendanceStatus[\"EXCUSED\"] = \"excused\";\n  return AttendanceStatus;\n}({});\nexport let AttendanceMethod = /*#__PURE__*/function (AttendanceMethod) {\n  AttendanceMethod[\"MANUAL\"] = \"manual\";\n  AttendanceMethod[\"FACE_RECOGNITION\"] = \"face_recognition\";\n  AttendanceMethod[\"QR_CODE\"] = \"qr_code\";\n  return AttendanceMethod;\n}({});\n\n// Types de session de cours\n\nexport let SessionStatus = /*#__PURE__*/function (SessionStatus) {\n  SessionStatus[\"SCHEDULED\"] = \"scheduled\";\n  SessionStatus[\"IN_PROGRESS\"] = \"in_progress\";\n  SessionStatus[\"COMPLETED\"] = \"completed\";\n  SessionStatus[\"CANCELLED\"] = \"cancelled\";\n  return SessionStatus;\n}({});\n\n// Types de reconnaissance faciale\n\n// Types de rapports\n\n// Types d'interface utilisateur\n\n// Types de notification\n\n// Types d'API\n\n// Types de formulaires\n\n// Types de contexte", "map": {"version": 3, "names": ["UserRole", "AttendanceStatus", "AttendanceMethod", "SessionStatus"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/types/index.ts"], "sourcesContent": ["/**\n * Types TypeScript pour PresencePro\n */\n\n// Types d'utilisateur\nexport enum UserRole {\n  ADMIN = 'admin',\n  TEACHER = 'teacher',\n  STUDENT = 'student'\n}\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  fullName: string;\n  role: UserRole;\n  roleDisplay: string;\n  phoneNumber?: string;\n  dateOfBirth?: string;\n  address?: string;\n  profilePicture?: string;\n  isActive: boolean;\n  dateJoined: string;\n  lastLogin?: string;\n}\n\nexport interface UserProfile {\n  user: User;\n  studentId?: string;\n  employeeId?: string;\n  emergencyContactName?: string;\n  emergencyContactPhone?: string;\n  languagePreference: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Types d'authentification\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message: string;\n  user?: User;\n  token?: string;\n  permissions?: string[];\n}\n\n// Types de cours\nexport interface Course {\n  id: string;\n  name: string;\n  code: string;\n  description?: string;\n  teacher?: User;\n  studentGroup: StudentGroup;\n  schedule: CourseSchedule[];\n  academicYear: string;\n  semester: string;\n  credits: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface CourseSchedule {\n  id: string;\n  dayOfWeek: number; // 0 = Dimanche, 1 = Lundi, etc.\n  startTime: string;\n  endTime: string;\n  room?: string;\n  building?: string;\n}\n\n// Types de groupes d'étudiants\nexport interface StudentGroup {\n  id: string;\n  name: string;\n  description?: string;\n  academicYear: string;\n  level: string;\n  specialization?: string;\n  studentCount: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Types de présence\nexport interface AttendanceRecord {\n  id: string;\n  student?: User;\n  course?: Course;\n  date: string;\n  time: string;\n  status: AttendanceStatus;\n  method: AttendanceMethod;\n  confidence?: number; // Pour la reconnaissance faciale\n  notes?: string;\n  createdAt: string;\n  updatedAt?: string;\n}\n\nexport enum AttendanceStatus {\n  PRESENT = 'present',\n  ABSENT = 'absent',\n  LATE = 'late',\n  EXCUSED = 'excused'\n}\n\nexport enum AttendanceMethod {\n  MANUAL = 'manual',\n  FACE_RECOGNITION = 'face_recognition',\n  QR_CODE = 'qr_code'\n}\n\n// Types de session de cours\nexport interface CourseSession {\n  id: string;\n  course: Course;\n  date: string;\n  startTime: string;\n  endTime: string;\n  room?: string;\n  building?: string;\n  status: SessionStatus;\n  attendanceRecords: AttendanceRecord[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport enum SessionStatus {\n  SCHEDULED = 'scheduled',\n  IN_PROGRESS = 'in_progress',\n  COMPLETED = 'completed',\n  CANCELLED = 'cancelled'\n}\n\n// Types de reconnaissance faciale\nexport interface FaceEncoding {\n  id: string;\n  user: User;\n  encoding: number[];\n  confidence: number;\n  imageUrl?: string;\n  createdAt: string;\n  isActive: boolean;\n}\n\nexport interface FaceDetectionResult {\n  success: boolean;\n  detectedUsers: {\n    user: User;\n    confidence: number;\n    boundingBox: {\n      x: number;\n      y: number;\n      width: number;\n      height: number;\n    };\n  }[];\n  message?: string;\n}\n\n// Types de rapports\nexport interface AttendanceReport {\n  student: User;\n  course: Course;\n  totalSessions: number;\n  presentSessions: number;\n  absentSessions: number;\n  lateSessions: number;\n  attendanceRate: number;\n  period: {\n    startDate: string;\n    endDate: string;\n  };\n}\n\nexport interface CourseAttendanceStats {\n  course: Course;\n  totalStudents: number;\n  averageAttendanceRate: number;\n  sessionStats: {\n    date: string;\n    presentCount: number;\n    absentCount: number;\n    lateCount: number;\n    attendanceRate: number;\n  }[];\n}\n\n// Types d'interface utilisateur\nexport interface DashboardStats {\n  totalStudents: number;\n  totalTeachers: number;\n  totalCourses: number;\n  todaySessions: number;\n  averageAttendanceRate: number;\n  recentActivity: ActivityItem[];\n}\n\nexport interface ActivityItem {\n  id: string;\n  type: 'attendance' | 'course' | 'user' | 'system';\n  message: string;\n  timestamp: string;\n  user?: User;\n  course?: Course;\n}\n\n// Types de notification\nexport interface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'info' | 'success' | 'warning' | 'error';\n  isRead: boolean;\n  createdAt: string;\n  actionUrl?: string;\n}\n\n// Types d'API\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n  errors?: Record<string, string[]>;\n}\n\nexport interface PaginatedResponse<T = any> {\n  success: boolean;\n  data: T[];\n  pagination: {\n    page: number;\n    pageSize: number;\n    totalItems: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrevious: boolean;\n  };\n}\n\n// Types de formulaires\nexport interface CreateUserForm {\n  username: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: UserRole;\n  phoneNumber?: string;\n  dateOfBirth?: string;\n  address?: string;\n  password: string;\n  passwordConfirm: string;\n}\n\nexport interface UpdateUserForm {\n  firstName: string;\n  lastName: string;\n  email: string;\n  phoneNumber?: string;\n  dateOfBirth?: string;\n  address?: string;\n  isActive: boolean;\n}\n\nexport interface CreateCourseForm {\n  name: string;\n  code: string;\n  description?: string;\n  teacherId: string;\n  studentGroupId: string;\n  academicYear: string;\n  semester: string;\n  credits: number;\n  schedule: Omit<CourseSchedule, 'id'>[];\n}\n\n// Types de contexte\nexport interface AuthContextType {\n  user: User | null;\n  token: string | null;\n  permissions: string[];\n  isLoading: boolean;\n  login: (credentials: LoginCredentials) => Promise<boolean>;\n  logout: () => void;\n  updateUser: (userData: Partial<User>) => void;\n  hasPermission: (permission: string) => boolean;\n}\n\nexport interface ThemeContextType {\n  isDarkMode: boolean;\n  toggleDarkMode: () => void;\n  language: string;\n  setLanguage: (language: string) => void;\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,WAAYA,QAAQ,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA;;AAmCpB;;AAcA;;AA0BA;;AAcA;;AAeA,WAAYC,gBAAgB,0BAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAA,OAAhBA,gBAAgB;AAAA;AAO5B,WAAYC,gBAAgB,0BAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAA,OAAhBA,gBAAgB;AAAA;;AAM5B;;AAeA,WAAYC,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;;AAOzB;;AA0BA;;AA4BA;;AAmBA;;AAWA;;AAqBA;;AAoCA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}