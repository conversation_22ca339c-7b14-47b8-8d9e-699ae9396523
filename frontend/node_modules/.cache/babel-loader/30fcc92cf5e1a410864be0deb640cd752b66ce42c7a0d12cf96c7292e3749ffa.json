{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { isTensor3D, isTensor4D } from '../utils';\nimport { awaitMediaLoaded } from './awaitMediaLoaded';\nimport { isMediaElement } from './isMediaElement';\nimport { NetInput } from './NetInput';\nimport { resolveInput } from './resolveInput';\n/**\r\n * Validates the input to make sure, they are valid net inputs and awaits all media elements\r\n * to be finished loading.\r\n *\r\n * @param input The input, which can be a media element or an array of different media elements.\r\n * @returns A NetInput instance, which can be passed into one of the neural networks.\r\n */\nexport function toNetInput(inputs) {\n  return __awaiter(this, void 0, void 0, function () {\n    var inputArgArray, getIdxHint, inputArray;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          if (inputs instanceof NetInput) {\n            return [2 /*return*/, inputs];\n          }\n          inputArgArray = Array.isArray(inputs) ? inputs : [inputs];\n          if (!inputArgArray.length) {\n            throw new Error('toNetInput - empty array passed as input');\n          }\n          getIdxHint = function (idx) {\n            return Array.isArray(inputs) ? \" at input index \" + idx + \":\" : '';\n          };\n          inputArray = inputArgArray.map(resolveInput);\n          inputArray.forEach(function (input, i) {\n            if (!isMediaElement(input) && !isTensor3D(input) && !isTensor4D(input)) {\n              if (typeof inputArgArray[i] === 'string') {\n                throw new Error(\"toNetInput -\" + getIdxHint(i) + \" string passed, but could not resolve HTMLElement for element id \" + inputArgArray[i]);\n              }\n              throw new Error(\"toNetInput -\" + getIdxHint(i) + \" expected media to be of type HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | tf.Tensor3D, or to be an element id\");\n            }\n            if (isTensor4D(input)) {\n              // if tf.Tensor4D is passed in the input array, the batch size has to be 1\n              var batchSize = input.shape[0];\n              if (batchSize !== 1) {\n                throw new Error(\"toNetInput -\" + getIdxHint(i) + \" tf.Tensor4D with batchSize \" + batchSize + \" passed, but not supported in input array\");\n              }\n            }\n          });\n          // wait for all media elements being loaded\n          return [4 /*yield*/, Promise.all(inputArray.map(function (input) {\n            return isMediaElement(input) && awaitMediaLoaded(input);\n          }))];\n        case 1:\n          // wait for all media elements being loaded\n          _a.sent();\n          return [2 /*return*/, new NetInput(inputArray, Array.isArray(inputs))];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["isTensor3D", "isTensor4D", "awaitMediaLoaded", "isMediaElement", "NetInput", "resolveInput", "toNetInput", "inputs", "inputArgArray", "Array", "isArray", "length", "Error", "getIdxHint", "idx", "inputArray", "map", "for<PERSON>ach", "input", "i", "batchSize", "shape", "Promise", "all", "_a", "sent"], "sources": ["../../../src/dom/toNetInput.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,UAAU,EAAEC,UAAU,QAAQ,UAAU;AACjD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,YAAY,QAAQ,gBAAgB;AAG7C;;;;;;;AAOA,OAAM,SAAgBC,UAAUA,CAACC,MAAiB;;;;;;UAChD,IAAIA,MAAM,YAAYH,QAAQ,EAAE;YAC9B,sBAAOG,MAAM;;UAGXC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GACnCA,MAAM,GACN,CAACA,MAAM,CAAC;UAEd,IAAI,CAACC,aAAa,CAACG,MAAM,EAAE;YACzB,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;;UAGvDC,UAAU,GAAG,SAAAA,CAACC,GAAW;YAAK,OAAAL,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAG,qBAAmBO,GAAG,MAAG,GAAG,EAAE;UAAtD,CAAsD;UAEpFC,UAAU,GAAGP,aAAa,CAACQ,GAAG,CAACX,YAAY,CAAC;UAElDU,UAAU,CAACE,OAAO,CAAC,UAACC,KAAK,EAAEC,CAAC;YAC1B,IAAI,CAAChB,cAAc,CAACe,KAAK,CAAC,IAAI,CAAClB,UAAU,CAACkB,KAAK,CAAC,IAAI,CAACjB,UAAU,CAACiB,KAAK,CAAC,EAAE;cAEtE,IAAI,OAAOV,aAAa,CAACW,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACxC,MAAM,IAAIP,KAAK,CAAC,iBAAeC,UAAU,CAACM,CAAC,CAAC,yEAAoEX,aAAa,CAACW,CAAC,CAAG,CAAC;;cAGrI,MAAM,IAAIP,KAAK,CAAC,iBAAeC,UAAU,CAACM,CAAC,CAAC,gIAA6H,CAAC;;YAG5K,IAAIlB,UAAU,CAACiB,KAAK,CAAC,EAAE;cACrB;cACA,IAAME,SAAS,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;cAChC,IAAID,SAAS,KAAK,CAAC,EAAE;gBACnB,MAAM,IAAIR,KAAK,CAAC,iBAAeC,UAAU,CAACM,CAAC,CAAC,oCAA+BC,SAAS,8CAA2C,CAAC;;;UAGtI,CAAC,CAAC;UAEF;UACA,qBAAME,OAAO,CAACC,GAAG,CACfR,UAAU,CAACC,GAAG,CAAC,UAAAE,KAAK;YAAI,OAAAf,cAAc,CAACe,KAAK,CAAC,IAAIhB,gBAAgB,CAACgB,KAAK,CAAC;UAAhD,CAAgD,CAAC,CAC1E;;UAHD;UACAM,EAAA,CAAAC,IAAA,EAEC;UAED,sBAAO,IAAIrB,QAAQ,CAACW,UAAU,EAAEN,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}