{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { fullyConnectedLayer } from '../common/fullyConnectedLayer';\nimport { seperateWeightMaps } from '../faceProcessor/util';\nimport { TinyXception } from '../xception/TinyXception';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nimport { Gender } from './types';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { NetInput, toNetInput } from '../dom';\nvar AgeGenderNet = /** @class */function (_super) {\n  __extends(AgeGenderNet, _super);\n  function AgeGenderNet(faceFeatureExtractor) {\n    if (faceFeatureExtractor === void 0) {\n      faceFeatureExtractor = new TinyXception(2);\n    }\n    var _this = _super.call(this, 'AgeGenderNet') || this;\n    _this._faceFeatureExtractor = faceFeatureExtractor;\n    return _this;\n  }\n  Object.defineProperty(AgeGenderNet.prototype, \"faceFeatureExtractor\", {\n    get: function () {\n      return this._faceFeatureExtractor;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  AgeGenderNet.prototype.runNet = function (input) {\n    var _this = this;\n    var params = this.params;\n    if (!params) {\n      throw new Error(this._name + \" - load model before inference\");\n    }\n    return tf.tidy(function () {\n      var bottleneckFeatures = input instanceof NetInput ? _this.faceFeatureExtractor.forwardInput(input) : input;\n      var pooled = tf.avgPool(bottleneckFeatures, [7, 7], [2, 2], 'valid').as2D(bottleneckFeatures.shape[0], -1);\n      var age = fullyConnectedLayer(pooled, params.fc.age).as1D();\n      var gender = fullyConnectedLayer(pooled, params.fc.gender);\n      return {\n        age: age,\n        gender: gender\n      };\n    });\n  };\n  AgeGenderNet.prototype.forwardInput = function (input) {\n    var _this = this;\n    return tf.tidy(function () {\n      var _a = _this.runNet(input),\n        age = _a.age,\n        gender = _a.gender;\n      return {\n        age: age,\n        gender: tf.softmax(gender)\n      };\n    });\n  };\n  AgeGenderNet.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  AgeGenderNet.prototype.predictAgeAndGender = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var netInput, out, ages, genders, ageAndGenderTensors, predictionsByBatch;\n      var _this = this;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            netInput = _a.sent();\n            return [4 /*yield*/, this.forwardInput(netInput)];\n          case 2:\n            out = _a.sent();\n            ages = tf.unstack(out.age);\n            genders = tf.unstack(out.gender);\n            ageAndGenderTensors = ages.map(function (ageTensor, i) {\n              return {\n                ageTensor: ageTensor,\n                genderTensor: genders[i]\n              };\n            });\n            return [4 /*yield*/, Promise.all(ageAndGenderTensors.map(function (_a) {\n              var ageTensor = _a.ageTensor,\n                genderTensor = _a.genderTensor;\n              return __awaiter(_this, void 0, void 0, function () {\n                var age, probMale, isMale, gender, genderProbability;\n                return __generator(this, function (_b) {\n                  switch (_b.label) {\n                    case 0:\n                      return [4 /*yield*/, ageTensor.data()];\n                    case 1:\n                      age = _b.sent()[0];\n                      return [4 /*yield*/, genderTensor.data()];\n                    case 2:\n                      probMale = _b.sent()[0];\n                      isMale = probMale > 0.5;\n                      gender = isMale ? Gender.MALE : Gender.FEMALE;\n                      genderProbability = isMale ? probMale : 1 - probMale;\n                      ageTensor.dispose();\n                      genderTensor.dispose();\n                      return [2 /*return*/, {\n                        age: age,\n                        gender: gender,\n                        genderProbability: genderProbability\n                      }];\n                  }\n                });\n              });\n            }))];\n          case 3:\n            predictionsByBatch = _a.sent();\n            out.age.dispose();\n            out.gender.dispose();\n            return [2 /*return*/, netInput.isBatchInput ? predictionsByBatch : predictionsByBatch[0]];\n        }\n      });\n    });\n  };\n  AgeGenderNet.prototype.getDefaultModelName = function () {\n    return 'age_gender_model';\n  };\n  AgeGenderNet.prototype.dispose = function (throwOnRedispose) {\n    if (throwOnRedispose === void 0) {\n      throwOnRedispose = true;\n    }\n    this.faceFeatureExtractor.dispose(throwOnRedispose);\n    _super.prototype.dispose.call(this, throwOnRedispose);\n  };\n  AgeGenderNet.prototype.loadClassifierParams = function (weights) {\n    var _a = this.extractClassifierParams(weights),\n      params = _a.params,\n      paramMappings = _a.paramMappings;\n    this._params = params;\n    this._paramMappings = paramMappings;\n  };\n  AgeGenderNet.prototype.extractClassifierParams = function (weights) {\n    return extractParams(weights);\n  };\n  AgeGenderNet.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    var _a = seperateWeightMaps(weightMap),\n      featureExtractorMap = _a.featureExtractorMap,\n      classifierMap = _a.classifierMap;\n    this.faceFeatureExtractor.loadFromWeightMap(featureExtractorMap);\n    return extractParamsFromWeigthMap(classifierMap);\n  };\n  AgeGenderNet.prototype.extractParams = function (weights) {\n    var classifierWeightSize = 512 * 1 + 1 + (512 * 2 + 2);\n    var featureExtractorWeights = weights.slice(0, weights.length - classifierWeightSize);\n    var classifierWeights = weights.slice(weights.length - classifierWeightSize);\n    this.faceFeatureExtractor.extractWeights(featureExtractorWeights);\n    return this.extractClassifierParams(classifierWeights);\n  };\n  return AgeGenderNet;\n}(NeuralNetwork);\nexport { AgeGenderNet };", "map": {"version": 3, "names": ["tf", "fullyConnectedLayer", "seperateWeightMaps", "TinyXception", "extractParams", "extractParamsFromWeigthMap", "Gender", "NeuralNetwork", "NetInput", "toNetInput", "AgeGenderNet", "_super", "__extends", "faceFeatureExtractor", "_this", "call", "_faceFeatureExtractor", "Object", "defineProperty", "prototype", "get", "runNet", "input", "params", "Error", "_name", "tidy", "bottleneckFeatures", "forwardInput", "pooled", "avgPool", "as2D", "shape", "age", "fc", "as1D", "gender", "_a", "softmax", "forward", "apply", "_b", "sent", "predictAgeAndGender", "netInput", "out", "ages", "unstack", "genders", "ageAndGenderTensors", "map", "ageTensor", "i", "genderTensor", "Promise", "all", "data", "probMale", "isMale", "MALE", "FEMALE", "genderProbability", "dispose", "predictionsByBatch", "isBatchInput", "getDefaultModelName", "throwOnRedispose", "loadClassifierParams", "weights", "extractClassifierParams", "paramMappings", "_params", "_paramMappings", "weightMap", "featureExtractorMap", "classifierMap", "loadFromWeightMap", "classifierWeightSize", "featureExtractorWeights", "slice", "length", "classifierWeights", "extractWeights"], "sources": ["../../../src/ageGenderNet/AgeGenderNet.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAAiCC,MAAM,QAA8B,SAAS;AAC9E,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,QAAQ,EAAaC,UAAU,QAAQ,QAAQ;AAExD,IAAAC,YAAA,0BAAAC,MAAA;EAAkCC,SAAA,CAAAF,YAAA,EAAAC,MAAA;EAIhC,SAAAD,aAAYG,oBAAwD;IAAxD,IAAAA,oBAAA;MAAAA,oBAAA,OAAyCV,YAAY,CAAC,CAAC,CAAC;IAAA;IAApE,IAAAW,KAAA,GACEH,MAAA,CAAAI,IAAA,OAAM,cAAc,CAAC;IACrBD,KAAI,CAACE,qBAAqB,GAAGH,oBAAoB;;EACnD;EAEAI,MAAA,CAAAC,cAAA,CAAWR,YAAA,CAAAS,SAAA,wBAAoB;SAA/B,SAAAC,CAAA;MACE,OAAO,IAAI,CAACJ,qBAAqB;IACnC,CAAC;;;;EAEMN,YAAA,CAAAS,SAAA,CAAAE,MAAM,GAAb,UAAcC,KAA6B;IAA3C,IAAAR,KAAA;IAEU,IAAAS,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAI,IAAI,CAACC,KAAK,mCAAgC,CAAC;;IAGhE,OAAOzB,EAAE,CAAC0B,IAAI,CAAC;MACb,IAAMC,kBAAkB,GAAGL,KAAK,YAAYd,QAAQ,GAChDM,KAAI,CAACD,oBAAoB,CAACe,YAAY,CAACN,KAAK,CAAC,GAC7CA,KAAK;MAET,IAAMO,MAAM,GAAG7B,EAAE,CAAC8B,OAAO,CAACH,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAACI,IAAI,CAACJ,kBAAkB,CAACK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5G,IAAMC,GAAG,GAAGhC,mBAAmB,CAAC4B,MAAM,EAAEN,MAAM,CAACW,EAAE,CAACD,GAAG,CAAC,CAACE,IAAI,EAAE;MAC7D,IAAMC,MAAM,GAAGnC,mBAAmB,CAAC4B,MAAM,EAAEN,MAAM,CAACW,EAAE,CAACE,MAAM,CAAC;MAC5D,OAAO;QAAEH,GAAG,EAAAA,GAAA;QAAEG,MAAM,EAAAA;MAAA,CAAE;IACxB,CAAC,CAAC;EACJ,CAAC;EAEM1B,YAAA,CAAAS,SAAA,CAAAS,YAAY,GAAnB,UAAoBN,KAA6B;IAAjD,IAAAR,KAAA;IACE,OAAOd,EAAE,CAAC0B,IAAI,CAAC;MACP,IAAAW,EAAA,GAAAvB,KAAA,CAAAO,MAAA,CAAAC,KAAA,CAAoC;QAAlCW,GAAA,GAAAI,EAAA,CAAAJ,GAAG;QAAEG,MAAA,GAAAC,EAAA,CAAAD,MAA6B;MAC1C,OAAO;QAAEH,GAAG,EAAAA,GAAA;QAAEG,MAAM,EAAEpC,EAAE,CAACsC,OAAO,CAACF,MAAM;MAAC,CAAE;IAC5C,CAAC,CAAC;EACJ,CAAC;EAEY1B,YAAA,CAAAS,SAAA,CAAAoB,OAAO,GAApB,UAAqBjB,KAAgB;;;;;;YAC5Be,EAAA,OAAI,CAACT,YAAY;YAAC,qBAAMnB,UAAU,CAACa,KAAK,CAAC;;YAAhD,sBAAOe,EAAA,CAAAG,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAEYhC,YAAA,CAAAS,SAAA,CAAAwB,mBAAmB,GAAhC,UAAiCrB,KAAgB;;;;;;;YAC9B,qBAAMb,UAAU,CAACa,KAAK,CAAC;;YAAlCsB,QAAQ,GAAGP,EAAA,CAAAK,IAAA,EAAuB;YAC5B,qBAAM,IAAI,CAACd,YAAY,CAACgB,QAAQ,CAAC;;YAAvCC,GAAG,GAAGR,EAAA,CAAAK,IAAA,EAAiC;YAEvCI,IAAI,GAAG9C,EAAE,CAAC+C,OAAO,CAACF,GAAG,CAACZ,GAAG,CAAC;YAC1Be,OAAO,GAAGhD,EAAE,CAAC+C,OAAO,CAACF,GAAG,CAACT,MAAM,CAAC;YAChCa,mBAAmB,GAAGH,IAAI,CAACI,GAAG,CAAC,UAACC,SAAS,EAAEC,CAAC;cAAK,OAAC;gBACtDD,SAAS,EAAAA,SAAA;gBACTE,YAAY,EAAEL,OAAO,CAACI,CAAC;eACxB;YAHsD,CAGrD,CAAC;YAEwB,qBAAME,OAAO,CAACC,GAAG,CAC1CN,mBAAmB,CAACC,GAAG,CAAC,UAAOb,EAA2B;kBAAzBc,SAAA,GAAAd,EAAA,CAAAc,SAAS;gBAAEE,YAAA,GAAAhB,EAAA,CAAAgB,YAAY;;;;;;sBACzC,qBAAMF,SAAS,CAACK,IAAI,EAAE;;sBAA7BvB,GAAG,GAAIQ,EAAA,CAAAC,IAAA,EAAsB,CAAE,CAAC,CAAC;sBACrB,qBAAMW,YAAY,CAACG,IAAI,EAAE;;sBAArCC,QAAQ,GAAIhB,EAAA,CAAAC,IAAA,EAAyB,CAAE,CAAC,CAAC;sBACzCgB,MAAM,GAAGD,QAAQ,GAAG,GAAG;sBACvBrB,MAAM,GAAGsB,MAAM,GAAGpD,MAAM,CAACqD,IAAI,GAAGrD,MAAM,CAACsD,MAAM;sBAC7CC,iBAAiB,GAAGH,MAAM,GAAGD,QAAQ,GAAI,CAAC,GAAGA,QAAS;sBAE5DN,SAAS,CAACW,OAAO,EAAE;sBACnBT,YAAY,CAACS,OAAO,EAAE;sBACtB,sBAAO;wBAAE7B,GAAG,EAAAA,GAAA;wBAAEG,MAAM,EAAAA,MAAA;wBAAEyB,iBAAiB,EAAAA;sBAAA,CAAE;;;;aAC1C,CAAC,CACH;;YAZKE,kBAAkB,GAAG1B,EAAA,CAAAK,IAAA,EAY1B;YACDG,GAAG,CAACZ,GAAG,CAAC6B,OAAO,EAAE;YACjBjB,GAAG,CAACT,MAAM,CAAC0B,OAAO,EAAE;YAEpB,sBAAOlB,QAAQ,CAACoB,YAAY,GACxBD,kBAAkB,GAClBA,kBAAkB,CAAC,CAAC,CAAC;;;;GAC1B;EAESrD,YAAA,CAAAS,SAAA,CAAA8C,mBAAmB,GAA7B;IACE,OAAO,kBAAkB;EAC3B,CAAC;EAEMvD,YAAA,CAAAS,SAAA,CAAA2C,OAAO,GAAd,UAAeI,gBAAgC;IAAhC,IAAAA,gBAAA;MAAAA,gBAAA,OAAgC;IAAA;IAC7C,IAAI,CAACrD,oBAAoB,CAACiD,OAAO,CAACI,gBAAgB,CAAC;IACnDvD,MAAA,CAAAQ,SAAA,CAAM2C,OAAO,CAAA/C,IAAA,OAACmD,gBAAgB,CAAC;EACjC,CAAC;EAEMxD,YAAA,CAAAS,SAAA,CAAAgD,oBAAoB,GAA3B,UAA4BC,OAAqB;IACzC,IAAA/B,EAAA,QAAAgC,uBAAA,CAAAD,OAAA,CAAiE;MAA/D7C,MAAA,GAAAc,EAAA,CAAAd,MAAM;MAAE+C,aAAA,GAAAjC,EAAA,CAAAiC,aAAuD;IACvE,IAAI,CAACC,OAAO,GAAGhD,MAAM;IACrB,IAAI,CAACiD,cAAc,GAAGF,aAAa;EACrC,CAAC;EAEM5D,YAAA,CAAAS,SAAA,CAAAkD,uBAAuB,GAA9B,UAA+BD,OAAqB;IAClD,OAAOhE,aAAa,CAACgE,OAAO,CAAC;EAC/B,CAAC;EAES1D,YAAA,CAAAS,SAAA,CAAAd,0BAA0B,GAApC,UAAqCoE,SAA4B;IAEzD,IAAApC,EAAA,GAAAnC,kBAAA,CAAAuE,SAAA,CAAsE;MAApEC,mBAAA,GAAArC,EAAA,CAAAqC,mBAAmB;MAAEC,aAAA,GAAAtC,EAAA,CAAAsC,aAA+C;IAE5E,IAAI,CAAC9D,oBAAoB,CAAC+D,iBAAiB,CAACF,mBAAmB,CAAC;IAEhE,OAAOrE,0BAA0B,CAACsE,aAAa,CAAC;EAClD,CAAC;EAESjE,YAAA,CAAAS,SAAA,CAAAf,aAAa,GAAvB,UAAwBgE,OAAqB;IAE3C,IAAMS,oBAAoB,GAAI,GAAG,GAAG,CAAC,GAAG,CAAC,IAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAE1D,IAAMC,uBAAuB,GAAGV,OAAO,CAACW,KAAK,CAAC,CAAC,EAAEX,OAAO,CAACY,MAAM,GAAGH,oBAAoB,CAAC;IACvF,IAAMI,iBAAiB,GAAGb,OAAO,CAACW,KAAK,CAACX,OAAO,CAACY,MAAM,GAAGH,oBAAoB,CAAC;IAE9E,IAAI,CAAChE,oBAAoB,CAACqE,cAAc,CAACJ,uBAAuB,CAAC;IACjE,OAAO,IAAI,CAACT,uBAAuB,CAACY,iBAAiB,CAAC;EACxD,CAAC;EACH,OAAAvE,YAAC;AAAD,CAAC,CAlHiCH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}