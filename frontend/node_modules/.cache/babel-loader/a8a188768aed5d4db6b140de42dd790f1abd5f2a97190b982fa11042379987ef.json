{"ast": null, "code": "import { env } from '../env';\nimport { createCanvas, createCanvasFromMedia } from './createCanvas';\nimport { getContext2dOrThrow } from './getContext2dOrThrow';\nimport { getMediaDimensions } from './getMediaDimensions';\nexport function imageToSquare(input, inputSize, centerImage) {\n  if (centerImage === void 0) {\n    centerImage = false;\n  }\n  var _a = env.getEnv(),\n    Image = _a.Image,\n    Canvas = _a.Canvas;\n  if (!(input instanceof Image || input instanceof Canvas)) {\n    throw new Error('imageToSquare - expected arg0 to be HTMLImageElement | HTMLCanvasElement');\n  }\n  var dims = getMediaDimensions(input);\n  var scale = inputSize / Math.max(dims.height, dims.width);\n  var width = scale * dims.width;\n  var height = scale * dims.height;\n  var targetCanvas = createCanvas({\n    width: inputSize,\n    height: inputSize\n  });\n  var inputCanvas = input instanceof Canvas ? input : createCanvasFromMedia(input);\n  var offset = Math.abs(width - height) / 2;\n  var dx = centerImage && width < height ? offset : 0;\n  var dy = centerImage && height < width ? offset : 0;\n  getContext2dOrThrow(targetCanvas).drawImage(inputCanvas, dx, dy, width, height);\n  return targetCanvas;\n}", "map": {"version": 3, "names": ["env", "createCanvas", "createCanvasFromMedia", "getContext2dOrThrow", "getMediaDimensions", "imageToSquare", "input", "inputSize", "centerImage", "_a", "getEnv", "Image", "<PERSON><PERSON>", "Error", "dims", "scale", "Math", "max", "height", "width", "targetCanvas", "inputCanvas", "offset", "abs", "dx", "dy", "drawImage"], "sources": ["../../../src/dom/imageToSquare.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAC5B,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,kBAAkB,QAAQ,sBAAsB;AAEzD,OAAM,SAAUC,aAAaA,CAACC,KAA2C,EAAEC,SAAiB,EAAEC,WAA4B;EAA5B,IAAAA,WAAA;IAAAA,WAAA,QAA4B;EAAA;EAElH,IAAAC,EAAA,GAAAT,GAAA,CAAAU,MAAA,EAAgC;IAA9BC,KAAA,GAAAF,EAAA,CAAAE,KAAK;IAAEC,MAAA,GAAAH,EAAA,CAAAG,MAAuB;EAEtC,IAAI,EAAEN,KAAK,YAAYK,KAAK,IAAIL,KAAK,YAAYM,MAAM,CAAC,EAAE;IACxD,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;;EAG7F,IAAMC,IAAI,GAAGV,kBAAkB,CAACE,KAAK,CAAC;EACtC,IAAMS,KAAK,GAAGR,SAAS,GAAGS,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,MAAM,EAAEJ,IAAI,CAACK,KAAK,CAAC;EAC3D,IAAMA,KAAK,GAAGJ,KAAK,GAAGD,IAAI,CAACK,KAAK;EAChC,IAAMD,MAAM,GAAGH,KAAK,GAAGD,IAAI,CAACI,MAAM;EAElC,IAAME,YAAY,GAAGnB,YAAY,CAAC;IAAEkB,KAAK,EAAEZ,SAAS;IAAEW,MAAM,EAAEX;EAAS,CAAE,CAAC;EAC1E,IAAMc,WAAW,GAAGf,KAAK,YAAYM,MAAM,GAAGN,KAAK,GAAGJ,qBAAqB,CAACI,KAAK,CAAC;EAElF,IAAMgB,MAAM,GAAGN,IAAI,CAACO,GAAG,CAACJ,KAAK,GAAGD,MAAM,CAAC,GAAG,CAAC;EAC3C,IAAMM,EAAE,GAAGhB,WAAW,IAAIW,KAAK,GAAGD,MAAM,GAAGI,MAAM,GAAG,CAAC;EACrD,IAAMG,EAAE,GAAGjB,WAAW,IAAIU,MAAM,GAAGC,KAAK,GAAGG,MAAM,GAAG,CAAC;EACrDnB,mBAAmB,CAACiB,YAAY,CAAC,CAACM,SAAS,CAACL,WAAW,EAAEG,EAAE,EAAEC,EAAE,EAAEN,KAAK,EAAED,MAAM,CAAC;EAE/E,OAAOE,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}