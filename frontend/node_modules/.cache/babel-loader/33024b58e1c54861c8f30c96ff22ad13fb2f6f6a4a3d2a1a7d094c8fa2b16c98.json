{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { extractConvParamsFactory } from '../common';\nimport { extractSeparableConvParamsFactory } from '../common/extractSeparableConvParamsFactory';\nimport { extractWeightsFactory } from '../common/extractWeightsFactory';\nfunction extractorsFactory(extractWeights, paramMappings) {\n  var extractConvParams = extractConvParamsFactory(extractWeights, paramMappings);\n  function extractBatchNormParams(size, mappedPrefix) {\n    var sub = tf.tensor1d(extractWeights(size));\n    var truediv = tf.tensor1d(extractWeights(size));\n    paramMappings.push({\n      paramPath: mappedPrefix + \"/sub\"\n    }, {\n      paramPath: mappedPrefix + \"/truediv\"\n    });\n    return {\n      sub: sub,\n      truediv: truediv\n    };\n  }\n  function extractConvWithBatchNormParams(channelsIn, channelsOut, mappedPrefix) {\n    var conv = extractConvParams(channelsIn, channelsOut, 3, mappedPrefix + \"/conv\");\n    var bn = extractBatchNormParams(channelsOut, mappedPrefix + \"/bn\");\n    return {\n      conv: conv,\n      bn: bn\n    };\n  }\n  var extractSeparableConvParams = extractSeparableConvParamsFactory(extractWeights, paramMappings);\n  return {\n    extractConvParams: extractConvParams,\n    extractConvWithBatchNormParams: extractConvWithBatchNormParams,\n    extractSeparableConvParams: extractSeparableConvParams\n  };\n}\nexport function extractParams(weights, config, boxEncodingSize, filterSizes) {\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var paramMappings = [];\n  var _b = extractorsFactory(extractWeights, paramMappings),\n    extractConvParams = _b.extractConvParams,\n    extractConvWithBatchNormParams = _b.extractConvWithBatchNormParams,\n    extractSeparableConvParams = _b.extractSeparableConvParams;\n  var params;\n  if (config.withSeparableConvs) {\n    var s0 = filterSizes[0],\n      s1 = filterSizes[1],\n      s2 = filterSizes[2],\n      s3 = filterSizes[3],\n      s4 = filterSizes[4],\n      s5 = filterSizes[5],\n      s6 = filterSizes[6],\n      s7 = filterSizes[7],\n      s8 = filterSizes[8];\n    var conv0 = config.isFirstLayerConv2d ? extractConvParams(s0, s1, 3, 'conv0') : extractSeparableConvParams(s0, s1, 'conv0');\n    var conv1 = extractSeparableConvParams(s1, s2, 'conv1');\n    var conv2 = extractSeparableConvParams(s2, s3, 'conv2');\n    var conv3 = extractSeparableConvParams(s3, s4, 'conv3');\n    var conv4 = extractSeparableConvParams(s4, s5, 'conv4');\n    var conv5 = extractSeparableConvParams(s5, s6, 'conv5');\n    var conv6 = s7 ? extractSeparableConvParams(s6, s7, 'conv6') : undefined;\n    var conv7 = s8 ? extractSeparableConvParams(s7, s8, 'conv7') : undefined;\n    var conv8 = extractConvParams(s8 || s7 || s6, 5 * boxEncodingSize, 1, 'conv8');\n    params = {\n      conv0: conv0,\n      conv1: conv1,\n      conv2: conv2,\n      conv3: conv3,\n      conv4: conv4,\n      conv5: conv5,\n      conv6: conv6,\n      conv7: conv7,\n      conv8: conv8\n    };\n  } else {\n    var s0 = filterSizes[0],\n      s1 = filterSizes[1],\n      s2 = filterSizes[2],\n      s3 = filterSizes[3],\n      s4 = filterSizes[4],\n      s5 = filterSizes[5],\n      s6 = filterSizes[6],\n      s7 = filterSizes[7],\n      s8 = filterSizes[8];\n    var conv0 = extractConvWithBatchNormParams(s0, s1, 'conv0');\n    var conv1 = extractConvWithBatchNormParams(s1, s2, 'conv1');\n    var conv2 = extractConvWithBatchNormParams(s2, s3, 'conv2');\n    var conv3 = extractConvWithBatchNormParams(s3, s4, 'conv3');\n    var conv4 = extractConvWithBatchNormParams(s4, s5, 'conv4');\n    var conv5 = extractConvWithBatchNormParams(s5, s6, 'conv5');\n    var conv6 = extractConvWithBatchNormParams(s6, s7, 'conv6');\n    var conv7 = extractConvWithBatchNormParams(s7, s8, 'conv7');\n    var conv8 = extractConvParams(s8, 5 * boxEncodingSize, 1, 'conv8');\n    params = {\n      conv0: conv0,\n      conv1: conv1,\n      conv2: conv2,\n      conv3: conv3,\n      conv4: conv4,\n      conv5: conv5,\n      conv6: conv6,\n      conv7: conv7,\n      conv8: conv8\n    };\n  }\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["tf", "extractConvParamsFactory", "extractSeparableConvParamsFactory", "extractWeightsFactory", "extractorsFactory", "extractWeights", "paramMappings", "extractConvParams", "extractBatchNormParams", "size", "mappedPrefix", "sub", "tensor1d", "truediv", "push", "<PERSON><PERSON><PERSON><PERSON>", "extractConvWithBatchNormParams", "channelsIn", "channelsOut", "conv", "bn", "extractSeparableConvParams", "extractParams", "weights", "config", "boxEncodingSize", "filterSizes", "_a", "getRemainingWeights", "_b", "params", "withSeparableConvs", "s0", "s1", "s2", "s3", "s4", "s5", "s6", "s7", "s8", "conv0", "isFirstLayerConv2d", "conv1", "conv2", "conv3", "conv4", "conv5", "conv6", "undefined", "conv7", "conv8", "length", "Error"], "sources": ["../../../src/tinyYolov2/extractParams.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,wBAAwB,QAAQ,WAAW;AACpD,SAASC,iCAAiC,QAAQ,6CAA6C;AAC/F,SAASC,qBAAqB,QAAQ,iCAAiC;AAKvE,SAASC,iBAAiBA,CAACC,cAAsC,EAAEC,aAA6B;EAE9F,IAAMC,iBAAiB,GAAGN,wBAAwB,CAACI,cAAc,EAAEC,aAAa,CAAC;EAEjF,SAASE,sBAAsBA,CAACC,IAAY,EAAEC,YAAoB;IAEhE,IAAMC,GAAG,GAAGX,EAAE,CAACY,QAAQ,CAACP,cAAc,CAACI,IAAI,CAAC,CAAC;IAC7C,IAAMI,OAAO,GAAGb,EAAE,CAACY,QAAQ,CAACP,cAAc,CAACI,IAAI,CAAC,CAAC;IAEjDH,aAAa,CAACQ,IAAI,CAChB;MAAEC,SAAS,EAAKL,YAAY;IAAM,CAAE,EACpC;MAAEK,SAAS,EAAKL,YAAY;IAAU,CAAE,CACzC;IAED,OAAO;MAAEC,GAAG,EAAAA,GAAA;MAAEE,OAAO,EAAAA;IAAA,CAAE;EACzB;EAEA,SAASG,8BAA8BA,CAACC,UAAkB,EAAEC,WAAmB,EAAER,YAAoB;IAEnG,IAAMS,IAAI,GAAGZ,iBAAiB,CAACU,UAAU,EAAEC,WAAW,EAAE,CAAC,EAAKR,YAAY,UAAO,CAAC;IAClF,IAAMU,EAAE,GAAGZ,sBAAsB,CAACU,WAAW,EAAKR,YAAY,QAAK,CAAC;IAEpE,OAAO;MAAES,IAAI,EAAAA,IAAA;MAAEC,EAAE,EAAAA;IAAA,CAAE;EACrB;EACA,IAAMC,0BAA0B,GAAGnB,iCAAiC,CAACG,cAAc,EAAEC,aAAa,CAAC;EAEnG,OAAO;IACLC,iBAAiB,EAAAA,iBAAA;IACjBS,8BAA8B,EAAAA,8BAAA;IAC9BK,0BAA0B,EAAAA;GAC3B;AAEH;AAEA,OAAM,SAAUC,aAAaA,CAC3BC,OAAqB,EACrBC,MAAwB,EACxBC,eAAuB,EACvBC,WAAqB;EAGf,IAAAC,EAAA,GAAAxB,qBAAA,CAAAoB,OAAA,CAG4B;IAFhClB,cAAA,GAAAsB,EAAA,CAAAtB,cAAc;IACduB,mBAAA,GAAAD,EAAA,CAAAC,mBACgC;EAElC,IAAMtB,aAAa,GAAmB,EAAE;EAElC,IAAAuB,EAAA,GAAAzB,iBAAA,CAAAC,cAAA,EAAAC,aAAA,CAI8C;IAHlDC,iBAAA,GAAAsB,EAAA,CAAAtB,iBAAiB;IACjBS,8BAAA,GAAAa,EAAA,CAAAb,8BAA8B;IAC9BK,0BAAA,GAAAQ,EAAA,CAAAR,0BACkD;EAEpD,IAAIS,MAA2B;EAE/B,IAAIN,MAAM,CAACO,kBAAkB,EAAE;IACtB,IAAAC,EAAA,GAAAN,WAAA,GAAE;MAAEO,EAAA,GAAAP,WAAA,GAAE;MAAEQ,EAAA,GAAAR,WAAA,GAAE;MAAES,EAAA,GAAAT,WAAA,GAAE;MAAEU,EAAA,GAAAV,WAAA,GAAE;MAAEW,EAAA,GAAAX,WAAA,GAAE;MAAEY,EAAA,GAAAZ,WAAA,GAAE;MAAEa,EAAA,GAAAb,WAAA,GAAE;MAAEc,EAAA,GAAAd,WAAA,GAAE;IAEzC,IAAMe,KAAK,GAAGjB,MAAM,CAACkB,kBAAkB,GACnCnC,iBAAiB,CAACyB,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,GACrCZ,0BAA0B,CAACW,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC/C,IAAMU,KAAK,GAAGtB,0BAA0B,CAACY,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IACzD,IAAMU,KAAK,GAAGvB,0BAA0B,CAACa,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IACzD,IAAMU,KAAK,GAAGxB,0BAA0B,CAACc,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IACzD,IAAMU,KAAK,GAAGzB,0BAA0B,CAACe,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IACzD,IAAMU,KAAK,GAAG1B,0BAA0B,CAACgB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IACzD,IAAMU,KAAK,GAAGT,EAAE,GAAGlB,0BAA0B,CAACiB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC,GAAGU,SAAS;IAC1E,IAAMC,KAAK,GAAGV,EAAE,GAAGnB,0BAA0B,CAACkB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC,GAAGS,SAAS;IAC1E,IAAME,KAAK,GAAG5C,iBAAiB,CAACiC,EAAE,IAAID,EAAE,IAAID,EAAE,EAAE,CAAC,GAAGb,eAAe,EAAE,CAAC,EAAE,OAAO,CAAC;IAChFK,MAAM,GAAG;MAAEW,KAAK,EAAAA,KAAA;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;GAC3E,MAAM;IACE,IAAAnB,EAAA,GAAAN,WAAA,GAAE;MAAEO,EAAA,GAAAP,WAAA,GAAE;MAAEQ,EAAA,GAAAR,WAAA,GAAE;MAAES,EAAA,GAAAT,WAAA,GAAE;MAAEU,EAAA,GAAAV,WAAA,GAAE;MAAEW,EAAA,GAAAX,WAAA,GAAE;MAAEY,EAAA,GAAAZ,WAAA,GAAE;MAAEa,EAAA,GAAAb,WAAA,GAAE;MAAEc,EAAA,GAAAd,WAAA,GAAE;IACzC,IAAMe,KAAK,GAAGzB,8BAA8B,CAACgB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAE;IAC9D,IAAMU,KAAK,GAAG3B,8BAA8B,CAACiB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMU,KAAK,GAAG5B,8BAA8B,CAACkB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMU,KAAK,GAAG7B,8BAA8B,CAACmB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMU,KAAK,GAAG9B,8BAA8B,CAACoB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMU,KAAK,GAAG/B,8BAA8B,CAACqB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMU,KAAK,GAAGhC,8BAA8B,CAACsB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMW,KAAK,GAAGlC,8BAA8B,CAACuB,EAAE,EAAEC,EAAE,EAAE,OAAO,CAAC;IAC7D,IAAMW,KAAK,GAAG5C,iBAAiB,CAACiC,EAAE,EAAE,CAAC,GAAGf,eAAe,EAAE,CAAC,EAAE,OAAO,CAAC;IACpEK,MAAM,GAAG;MAAEW,KAAK,EAAAA,KAAA;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;;EAG5E,IAAIvB,mBAAmB,EAAE,CAACwB,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCzB,mBAAmB,EAAE,CAACwB,MAAQ,CAAC;;EAInF,OAAO;IAAEtB,MAAM,EAAAA,MAAA;IAAExB,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}