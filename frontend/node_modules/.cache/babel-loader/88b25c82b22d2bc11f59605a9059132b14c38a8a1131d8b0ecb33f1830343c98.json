{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Box } from '../classes';\nvar MtcnnBox = /** @class */function (_super) {\n  __extends(MtcnnBox, _super);\n  function MtcnnBox(left, top, right, bottom) {\n    return _super.call(this, {\n      left: left,\n      top: top,\n      right: right,\n      bottom: bottom\n    }, true) || this;\n  }\n  return MtcnnBox;\n}(Box);\nexport { MtcnnBox };", "map": {"version": 3, "names": ["Box", "MtcnnBox", "_super", "__extends", "left", "top", "right", "bottom", "call"], "sources": ["../../../src/mtcnn/MtcnnBox.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,GAAG,QAAQ,YAAY;AAEhC,IAAAC,QAAA,0BAAAC,MAAA;EAA8BC,SAAA,CAAAF,QAAA,EAAAC,MAAA;EAC5B,SAAAD,SAAYG,IAAY,EAAEC,GAAW,EAAEC,KAAa,EAAEC,MAAc;WAClEL,MAAA,CAAAM,IAAA,OAAM;MAAEJ,IAAI,EAAAA,IAAA;MAAEC,GAAG,EAAAA,GAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,EAAE,IAAI,CAAC;EAC3C;EACF,OAAAN,QAAC;AAAD,CAAC,CAJ6BD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}