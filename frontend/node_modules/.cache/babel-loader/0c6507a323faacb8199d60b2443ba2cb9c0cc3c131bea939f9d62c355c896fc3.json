{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx\",\n  _s = $RefreshSig$();\n/**\n * Composant de test pour Firebase\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, TextField, Alert, CircularProgress, List, ListItem, ListItemText } from '@mui/material';\nimport { firebaseService } from '../../services/firebaseService';\nimport { mockFirebaseService } from '../../services/mockFirebaseService';\nimport { useFirebase } from '../../hooks/useFirebase';\nimport { UserRole } from '../../types';\n\n// Utiliser le service simulé si Firebase n'est pas configuré\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isFirebaseConfigured = process.env.REACT_APP_FIREBASE_PROJECT_ID && process.env.REACT_APP_FIREBASE_PROJECT_ID !== '';\nconst activeService = isFirebaseConfigured ? firebaseService : mockFirebaseService;\nconst FirebaseTest = () => {\n  _s();\n  const {\n    firebaseUser,\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout\n  } = useFirebase();\n  const [testResults, setTestResults] = useState([]);\n  const [isTestingConnection, setIsTestingConnection] = useState(false);\n\n  // Formulaire de test\n  const [email, setEmail] = useState('<EMAIL>');\n  const [password, setPassword] = useState('test123456');\n  useEffect(() => {\n    testFirebaseConnection();\n  }, []);\n  const addTestResult = message => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()} - ${message}`]);\n  };\n  const testFirebaseConnection = async () => {\n    setIsTestingConnection(true);\n    const serviceName = isFirebaseConfigured ? 'Firebase' : 'Service simulé (localStorage)';\n    addTestResult(`🔄 Test de connexion ${serviceName}...`);\n    try {\n      // Test 1: Connexion à Firestore/localStorage\n      const stats = await activeService.getGlobalStats();\n      addTestResult(`✅ Connexion ${serviceName} réussie`);\n      addTestResult(`📊 Statistiques: ${JSON.stringify(stats)}`);\n\n      // Test 2: Test de lecture des utilisateurs\n      const users = await activeService.getUsers();\n      addTestResult(`👥 ${users.length} utilisateurs trouvés`);\n\n      // Test 3: Test de lecture des cours\n      const courses = await activeService.getCourses();\n      addTestResult(`📚 ${courses.length} cours trouvés`);\n      addTestResult(`✅ Tous les tests ${serviceName} réussis !`);\n      if (!isFirebaseConfigured) {\n        addTestResult('💡 Pour utiliser Firebase réel, configurez REACT_APP_FIREBASE_PROJECT_ID');\n      }\n    } catch (err) {\n      addTestResult(`❌ Erreur ${serviceName}: ${err.message}`);\n    } finally {\n      setIsTestingConnection(false);\n    }\n  };\n  const handleTestSignUp = async () => {\n    try {\n      addTestResult('🔄 Test d\\'inscription...');\n      await signUp(email, password, {\n        firstName: 'Test',\n        lastName: 'User',\n        role: UserRole.STUDENT,\n        username: 'testuser'\n      });\n      addTestResult('✅ Inscription réussie');\n    } catch (err) {\n      addTestResult(`❌ Erreur d'inscription: ${err.message}`);\n    }\n  };\n  const handleTestSignIn = async () => {\n    try {\n      addTestResult('🔄 Test de connexion...');\n      await signIn(email, password);\n      addTestResult('✅ Connexion réussie');\n    } catch (err) {\n      addTestResult(`❌ Erreur de connexion: ${err.message}`);\n    }\n  };\n  const handleTestLogout = async () => {\n    try {\n      addTestResult('🔄 Test de déconnexion...');\n      await logout();\n      addTestResult('✅ Déconnexion réussie');\n    } catch (err) {\n      addTestResult(`❌ Erreur de déconnexion: ${err.message}`);\n    }\n  };\n  const clearResults = () => {\n    setTestResults([]);\n  };\n  const seedTestData = async () => {\n    if (!isFirebaseConfigured) {\n      addTestResult('🔄 Création de données de test...');\n      try {\n        await mockFirebaseService.seedTestData();\n        addTestResult('✅ Données de test créées avec succès');\n        addTestResult('🔄 Rechargement des statistiques...');\n        await testFirebaseConnection();\n      } catch (err) {\n        addTestResult(`❌ Erreur lors de la création des données: ${err.message}`);\n      }\n    }\n  };\n  const clearTestData = async () => {\n    if (!isFirebaseConfigured) {\n      addTestResult('🔄 Suppression des données de test...');\n      try {\n        await mockFirebaseService.clearAllData();\n        addTestResult('✅ Données de test supprimées');\n      } catch (err) {\n        addTestResult(`❌ Erreur lors de la suppression: ${err.message}`);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 3,\n        fontWeight: 'bold'\n      },\n      children: \"Test Firebase - PresencePro\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"\\xC9tat de la connexion Firebase\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: \"Chargement...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this), firebaseUser ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: [\"Utilisateur connect\\xE9: \", firebaseUser.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), user && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Profil: \", user.fullName, \" (\", user.roleDisplay, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Aucun utilisateur connect\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"Tests d'authentification\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            size: \"small\",\n            sx: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Mot de passe\",\n            type: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            size: \"small\",\n            sx: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleTestSignUp,\n            disabled: loading,\n            children: \"Test Inscription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleTestSignIn,\n            disabled: loading,\n            children: \"Test Connexion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: handleTestLogout,\n            disabled: loading || !firebaseUser,\n            children: \"Test D\\xE9connexion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"Tests de connexion Firebase\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: testFirebaseConnection,\n            disabled: isTestingConnection,\n            children: isTestingConnection ? 'Test en cours...' : 'Tester la connexion'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: clearResults,\n            children: \"Effacer les r\\xE9sultats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"R\\xE9sultats des tests\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), testResults.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: \"Aucun test ex\\xE9cut\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          children: testResults.map((result, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              py: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: result,\n              primaryTypographyProps: {\n                variant: 'body2',\n                fontFamily: 'monospace'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(FirebaseTest, \"PtEDBn0LfE51PY3ywTpNLcUEREk=\", false, function () {\n  return [useFirebase];\n});\n_c = FirebaseTest;\nexport default FirebaseTest;\nvar _c;\n$RefreshReg$(_c, \"FirebaseTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "firebaseService", "mockFirebaseService", "useFirebase", "UserRole", "jsxDEV", "_jsxDEV", "isFirebaseConfigured", "process", "env", "REACT_APP_FIREBASE_PROJECT_ID", "activeService", "FirebaseTest", "_s", "firebaseUser", "user", "loading", "error", "signIn", "signUp", "logout", "testResults", "setTestResults", "isTestingConnection", "setIsTestingConnection", "email", "setEmail", "password", "setPassword", "testFirebaseConnection", "addTestResult", "message", "prev", "Date", "toLocaleTimeString", "serviceName", "stats", "getGlobalStats", "JSON", "stringify", "users", "getUsers", "length", "courses", "getCourses", "err", "handleTestSignUp", "firstName", "lastName", "role", "STUDENT", "username", "handleTestSignIn", "handleTestLogout", "clearResults", "seedTestData", "clearTestData", "clearAllData", "sx", "p", "children", "variant", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "gap", "size", "severity", "fullName", "roleDisplay", "label", "value", "onChange", "e", "target", "flex", "type", "flexWrap", "onClick", "disabled", "color", "dense", "map", "result", "index", "py", "primary", "primaryTypographyProps", "fontFamily", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/Firebase/FirebaseTest.tsx"], "sourcesContent": ["/**\n * Composant de test pour Firebase\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  TextField,\n  Alert,\n  CircularProgress,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n} from '@mui/material';\nimport { firebaseService } from '../../services/firebaseService';\nimport { mockFirebaseService } from '../../services/mockFirebaseService';\nimport { useFirebase } from '../../hooks/useFirebase';\nimport { UserRole } from '../../types';\n\n// Utiliser le service simulé si Firebase n'est pas configuré\nconst isFirebaseConfigured = process.env.REACT_APP_FIREBASE_PROJECT_ID &&\n                             process.env.REACT_APP_FIREBASE_PROJECT_ID !== '';\nconst activeService = isFirebaseConfigured ? firebaseService : mockFirebaseService;\n\nconst FirebaseTest: React.FC = () => {\n  const { firebaseUser, user, loading, error, signIn, signUp, logout } = useFirebase();\n  const [testResults, setTestResults] = useState<string[]>([]);\n  const [isTestingConnection, setIsTestingConnection] = useState(false);\n  \n  // Formulaire de test\n  const [email, setEmail] = useState('<EMAIL>');\n  const [password, setPassword] = useState('test123456');\n\n  useEffect(() => {\n    testFirebaseConnection();\n  }, []);\n\n  const addTestResult = (message: string) => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()} - ${message}`]);\n  };\n\n  const testFirebaseConnection = async () => {\n    setIsTestingConnection(true);\n    const serviceName = isFirebaseConfigured ? 'Firebase' : 'Service simulé (localStorage)';\n    addTestResult(`🔄 Test de connexion ${serviceName}...`);\n\n    try {\n      // Test 1: Connexion à Firestore/localStorage\n      const stats = await activeService.getGlobalStats();\n      addTestResult(`✅ Connexion ${serviceName} réussie`);\n      addTestResult(`📊 Statistiques: ${JSON.stringify(stats)}`);\n\n      // Test 2: Test de lecture des utilisateurs\n      const users = await activeService.getUsers();\n      addTestResult(`👥 ${users.length} utilisateurs trouvés`);\n\n      // Test 3: Test de lecture des cours\n      const courses = await activeService.getCourses();\n      addTestResult(`📚 ${courses.length} cours trouvés`);\n\n      addTestResult(`✅ Tous les tests ${serviceName} réussis !`);\n\n      if (!isFirebaseConfigured) {\n        addTestResult('💡 Pour utiliser Firebase réel, configurez REACT_APP_FIREBASE_PROJECT_ID');\n      }\n    } catch (err: any) {\n      addTestResult(`❌ Erreur ${serviceName}: ${err.message}`);\n    } finally {\n      setIsTestingConnection(false);\n    }\n  };\n\n  const handleTestSignUp = async () => {\n    try {\n      addTestResult('🔄 Test d\\'inscription...');\n      await signUp(email, password, {\n        firstName: 'Test',\n        lastName: 'User',\n        role: UserRole.STUDENT,\n        username: 'testuser'\n      });\n      addTestResult('✅ Inscription réussie');\n    } catch (err: any) {\n      addTestResult(`❌ Erreur d'inscription: ${err.message}`);\n    }\n  };\n\n  const handleTestSignIn = async () => {\n    try {\n      addTestResult('🔄 Test de connexion...');\n      await signIn(email, password);\n      addTestResult('✅ Connexion réussie');\n    } catch (err: any) {\n      addTestResult(`❌ Erreur de connexion: ${err.message}`);\n    }\n  };\n\n  const handleTestLogout = async () => {\n    try {\n      addTestResult('🔄 Test de déconnexion...');\n      await logout();\n      addTestResult('✅ Déconnexion réussie');\n    } catch (err: any) {\n      addTestResult(`❌ Erreur de déconnexion: ${err.message}`);\n    }\n  };\n\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  const seedTestData = async () => {\n    if (!isFirebaseConfigured) {\n      addTestResult('🔄 Création de données de test...');\n      try {\n        await mockFirebaseService.seedTestData();\n        addTestResult('✅ Données de test créées avec succès');\n        addTestResult('🔄 Rechargement des statistiques...');\n        await testFirebaseConnection();\n      } catch (err: any) {\n        addTestResult(`❌ Erreur lors de la création des données: ${err.message}`);\n      }\n    }\n  };\n\n  const clearTestData = async () => {\n    if (!isFirebaseConfigured) {\n      addTestResult('🔄 Suppression des données de test...');\n      try {\n        await mockFirebaseService.clearAllData();\n        addTestResult('✅ Données de test supprimées');\n      } catch (err: any) {\n        addTestResult(`❌ Erreur lors de la suppression: ${err.message}`);\n      }\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" sx={{ mb: 3, fontWeight: 'bold' }}>\n        Test Firebase - PresencePro\n      </Typography>\n\n      {/* État de connexion Firebase */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            État de la connexion Firebase\n          </Typography>\n          \n          {loading && (\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <CircularProgress size={20} />\n              <Typography>Chargement...</Typography>\n            </Box>\n          )}\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {firebaseUser ? (\n            <Alert severity=\"success\">\n              <Typography variant=\"subtitle2\">\n                Utilisateur connecté: {firebaseUser.email}\n              </Typography>\n              {user && (\n                <Typography variant=\"body2\">\n                  Profil: {user.fullName} ({user.roleDisplay})\n                </Typography>\n              )}\n            </Alert>\n          ) : (\n            <Alert severity=\"info\">\n              Aucun utilisateur connecté\n            </Alert>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Tests d'authentification */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            Tests d'authentification\n          </Typography>\n          \n          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n            <TextField\n              label=\"Email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              size=\"small\"\n              sx={{ flex: 1 }}\n            />\n            <TextField\n              label=\"Mot de passe\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              size=\"small\"\n              sx={{ flex: 1 }}\n            />\n          </Box>\n\n          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n            <Button \n              variant=\"contained\" \n              onClick={handleTestSignUp}\n              disabled={loading}\n            >\n              Test Inscription\n            </Button>\n            <Button \n              variant=\"contained\" \n              onClick={handleTestSignIn}\n              disabled={loading}\n            >\n              Test Connexion\n            </Button>\n            <Button \n              variant=\"outlined\" \n              onClick={handleTestLogout}\n              disabled={loading || !firebaseUser}\n            >\n              Test Déconnexion\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Tests de connexion */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            Tests de connexion Firebase\n          </Typography>\n          \n          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n            <Button \n              variant=\"contained\" \n              onClick={testFirebaseConnection}\n              disabled={isTestingConnection}\n            >\n              {isTestingConnection ? 'Test en cours...' : 'Tester la connexion'}\n            </Button>\n            <Button \n              variant=\"outlined\" \n              onClick={clearResults}\n            >\n              Effacer les résultats\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Résultats des tests */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            Résultats des tests\n          </Typography>\n          \n          {testResults.length === 0 ? (\n            <Typography color=\"text.secondary\">\n              Aucun test exécuté\n            </Typography>\n          ) : (\n            <List dense>\n              {testResults.map((result, index) => (\n                <ListItem key={index} sx={{ py: 0.5 }}>\n                  <ListItemText \n                    primary={result}\n                    primaryTypographyProps={{ \n                      variant: 'body2',\n                      fontFamily: 'monospace'\n                    }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default FirebaseTest;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAEhBC,IAAI,EACJC,QAAQ,EACRC,YAAY,QACP,eAAe;AACtB,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,oBAAoB,GAAGC,OAAO,CAACC,GAAG,CAACC,6BAA6B,IACzCF,OAAO,CAACC,GAAG,CAACC,6BAA6B,KAAK,EAAE;AAC7E,MAAMC,aAAa,GAAGJ,oBAAoB,GAAGN,eAAe,GAAGC,mBAAmB;AAElF,MAAMU,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC,YAAY;IAAEC,IAAI;IAAEC,OAAO;IAAEC,KAAK;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGjB,WAAW,CAAC,CAAC;EACpF,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,sBAAsB,CAAC;EAC1D,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,YAAY,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACdwC,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAIC,OAAe,IAAK;IACzCT,cAAc,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,MAAMH,OAAO,EAAE,CAAC,CAAC;EACtF,CAAC;EAED,MAAMF,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzCL,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAMW,WAAW,GAAG5B,oBAAoB,GAAG,UAAU,GAAG,+BAA+B;IACvFuB,aAAa,CAAC,wBAAwBK,WAAW,KAAK,CAAC;IAEvD,IAAI;MACF;MACA,MAAMC,KAAK,GAAG,MAAMzB,aAAa,CAAC0B,cAAc,CAAC,CAAC;MAClDP,aAAa,CAAC,eAAeK,WAAW,UAAU,CAAC;MACnDL,aAAa,CAAC,oBAAoBQ,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;;MAE1D;MACA,MAAMI,KAAK,GAAG,MAAM7B,aAAa,CAAC8B,QAAQ,CAAC,CAAC;MAC5CX,aAAa,CAAC,MAAMU,KAAK,CAACE,MAAM,uBAAuB,CAAC;;MAExD;MACA,MAAMC,OAAO,GAAG,MAAMhC,aAAa,CAACiC,UAAU,CAAC,CAAC;MAChDd,aAAa,CAAC,MAAMa,OAAO,CAACD,MAAM,gBAAgB,CAAC;MAEnDZ,aAAa,CAAC,oBAAoBK,WAAW,YAAY,CAAC;MAE1D,IAAI,CAAC5B,oBAAoB,EAAE;QACzBuB,aAAa,CAAC,0EAA0E,CAAC;MAC3F;IACF,CAAC,CAAC,OAAOe,GAAQ,EAAE;MACjBf,aAAa,CAAC,YAAYK,WAAW,KAAKU,GAAG,CAACd,OAAO,EAAE,CAAC;IAC1D,CAAC,SAAS;MACRP,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFhB,aAAa,CAAC,2BAA2B,CAAC;MAC1C,MAAMX,MAAM,CAACM,KAAK,EAAEE,QAAQ,EAAE;QAC5BoB,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE7C,QAAQ,CAAC8C,OAAO;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFrB,aAAa,CAAC,uBAAuB,CAAC;IACxC,CAAC,CAAC,OAAOe,GAAQ,EAAE;MACjBf,aAAa,CAAC,2BAA2Be,GAAG,CAACd,OAAO,EAAE,CAAC;IACzD;EACF,CAAC;EAED,MAAMqB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFtB,aAAa,CAAC,yBAAyB,CAAC;MACxC,MAAMZ,MAAM,CAACO,KAAK,EAAEE,QAAQ,CAAC;MAC7BG,aAAa,CAAC,qBAAqB,CAAC;IACtC,CAAC,CAAC,OAAOe,GAAQ,EAAE;MACjBf,aAAa,CAAC,0BAA0Be,GAAG,CAACd,OAAO,EAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFvB,aAAa,CAAC,2BAA2B,CAAC;MAC1C,MAAMV,MAAM,CAAC,CAAC;MACdU,aAAa,CAAC,uBAAuB,CAAC;IACxC,CAAC,CAAC,OAAOe,GAAQ,EAAE;MACjBf,aAAa,CAAC,4BAA4Be,GAAG,CAACd,OAAO,EAAE,CAAC;IAC1D;EACF,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzBhC,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAChD,oBAAoB,EAAE;MACzBuB,aAAa,CAAC,mCAAmC,CAAC;MAClD,IAAI;QACF,MAAM5B,mBAAmB,CAACqD,YAAY,CAAC,CAAC;QACxCzB,aAAa,CAAC,sCAAsC,CAAC;QACrDA,aAAa,CAAC,qCAAqC,CAAC;QACpD,MAAMD,sBAAsB,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOgB,GAAQ,EAAE;QACjBf,aAAa,CAAC,6CAA6Ce,GAAG,CAACd,OAAO,EAAE,CAAC;MAC3E;IACF;EACF,CAAC;EAED,MAAMyB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACjD,oBAAoB,EAAE;MACzBuB,aAAa,CAAC,uCAAuC,CAAC;MACtD,IAAI;QACF,MAAM5B,mBAAmB,CAACuD,YAAY,CAAC,CAAC;QACxC3B,aAAa,CAAC,8BAA8B,CAAC;MAC/C,CAAC,CAAC,OAAOe,GAAQ,EAAE;QACjBf,aAAa,CAAC,oCAAoCe,GAAG,CAACd,OAAO,EAAE,CAAC;MAClE;IACF;EACF,CAAC;EAED,oBACEzB,OAAA,CAAChB,GAAG;IAACoE,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBtD,OAAA,CAACb,UAAU;MAACoE,OAAO,EAAC,IAAI;MAACH,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAH,QAAA,EAAC;IAE5D;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb7D,OAAA,CAACf,IAAI;MAACmE,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClBtD,OAAA,CAACd,WAAW;QAAAoE,QAAA,gBACVtD,OAAA,CAACb,UAAU;UAACoE,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAExC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZnD,OAAO,iBACNV,OAAA,CAAChB,GAAG;UAACoE,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,gBACzDtD,OAAA,CAACT,gBAAgB;YAAC0E,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9B7D,OAAA,CAACb,UAAU;YAAAmE,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACN,EAEAlD,KAAK,iBACJX,OAAA,CAACV,KAAK;UAAC4E,QAAQ,EAAC,OAAO;UAACd,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EACnC3C;QAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEArD,YAAY,gBACXR,OAAA,CAACV,KAAK;UAAC4E,QAAQ,EAAC,SAAS;UAAAZ,QAAA,gBACvBtD,OAAA,CAACb,UAAU;YAACoE,OAAO,EAAC,WAAW;YAAAD,QAAA,GAAC,2BACR,EAAC9C,YAAY,CAACW,KAAK;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACZpD,IAAI,iBACHT,OAAA,CAACb,UAAU;YAACoE,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,UAClB,EAAC7C,IAAI,CAAC0D,QAAQ,EAAC,IAAE,EAAC1D,IAAI,CAAC2D,WAAW,EAAC,GAC7C;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAER7D,OAAA,CAACV,KAAK;UAAC4E,QAAQ,EAAC,MAAM;UAAAZ,QAAA,EAAC;QAEvB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACf,IAAI;MAACmE,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClBtD,OAAA,CAACd,WAAW;QAAAoE,QAAA,gBACVtD,OAAA,CAACb,UAAU;UAACoE,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAExC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAAChB,GAAG;UAACoE,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE,CAAC;YAAER,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBAC1CtD,OAAA,CAACX,SAAS;YACRgF,KAAK,EAAC,OAAO;YACbC,KAAK,EAAEnD,KAAM;YACboD,QAAQ,EAAGC,CAAC,IAAKpD,QAAQ,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1CL,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEsB,IAAI,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF7D,OAAA,CAACX,SAAS;YACRgF,KAAK,EAAC,cAAc;YACpBM,IAAI,EAAC,UAAU;YACfL,KAAK,EAAEjD,QAAS;YAChBkD,QAAQ,EAAGC,CAAC,IAAKlD,WAAW,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC7CL,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEsB,IAAI,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7D,OAAA,CAAChB,GAAG;UAACoE,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE,CAAC;YAAEY,QAAQ,EAAE;UAAO,CAAE;UAAAtB,QAAA,gBACrDtD,OAAA,CAACZ,MAAM;YACLmE,OAAO,EAAC,WAAW;YACnBsB,OAAO,EAAErC,gBAAiB;YAC1BsC,QAAQ,EAAEpE,OAAQ;YAAA4C,QAAA,EACnB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA,CAACZ,MAAM;YACLmE,OAAO,EAAC,WAAW;YACnBsB,OAAO,EAAE/B,gBAAiB;YAC1BgC,QAAQ,EAAEpE,OAAQ;YAAA4C,QAAA,EACnB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA,CAACZ,MAAM;YACLmE,OAAO,EAAC,UAAU;YAClBsB,OAAO,EAAE9B,gBAAiB;YAC1B+B,QAAQ,EAAEpE,OAAO,IAAI,CAACF,YAAa;YAAA8C,QAAA,EACpC;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACf,IAAI;MAACmE,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClBtD,OAAA,CAACd,WAAW;QAAAoE,QAAA,gBACVtD,OAAA,CAACb,UAAU;UAACoE,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAExC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAAChB,GAAG;UAACoE,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE,CAAC;YAAER,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBAC1CtD,OAAA,CAACZ,MAAM;YACLmE,OAAO,EAAC,WAAW;YACnBsB,OAAO,EAAEtD,sBAAuB;YAChCuD,QAAQ,EAAE7D,mBAAoB;YAAAqC,QAAA,EAE7BrC,mBAAmB,GAAG,kBAAkB,GAAG;UAAqB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACT7D,OAAA,CAACZ,MAAM;YACLmE,OAAO,EAAC,UAAU;YAClBsB,OAAO,EAAE7B,YAAa;YAAAM,QAAA,EACvB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7D,OAAA,CAACf,IAAI;MAAAqE,QAAA,eACHtD,OAAA,CAACd,WAAW;QAAAoE,QAAA,gBACVtD,OAAA,CAACb,UAAU;UAACoE,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAExC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ9C,WAAW,CAACqB,MAAM,KAAK,CAAC,gBACvBpC,OAAA,CAACb,UAAU;UAAC4F,KAAK,EAAC,gBAAgB;UAAAzB,QAAA,EAAC;QAEnC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEb7D,OAAA,CAACR,IAAI;UAACwF,KAAK;UAAA1B,QAAA,EACRvC,WAAW,CAACkE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BnF,OAAA,CAACP,QAAQ;YAAa2D,EAAE,EAAE;cAAEgC,EAAE,EAAE;YAAI,CAAE;YAAA9B,QAAA,eACpCtD,OAAA,CAACN,YAAY;cACX2F,OAAO,EAAEH,MAAO;cAChBI,sBAAsB,EAAE;gBACtB/B,OAAO,EAAE,OAAO;gBAChBgC,UAAU,EAAE;cACd;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAPWsB,KAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtD,EAAA,CAxQID,YAAsB;EAAA,QAC6CT,WAAW;AAAA;AAAA2F,EAAA,GAD9ElF,YAAsB;AA0Q5B,eAAeA,YAAY;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}