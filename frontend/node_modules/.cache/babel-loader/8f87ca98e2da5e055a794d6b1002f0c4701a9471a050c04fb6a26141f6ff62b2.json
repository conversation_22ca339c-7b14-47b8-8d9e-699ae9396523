{"ast": null, "code": "import { minBbox } from '../ops';\nimport { getCenterPoint } from '../utils';\nimport { Box } from './Box';\nimport { Dimensions } from './Dimensions';\nimport { FaceDetection } from './FaceDetection';\nimport { Point } from './Point';\nimport { Rect } from './Rect';\n// face alignment constants\nvar relX = 0.5;\nvar relY = 0.43;\nvar relScale = 0.45;\nvar FaceLandmarks = /** @class */function () {\n  function FaceLandmarks(relativeFaceLandmarkPositions, imgDims, shift) {\n    if (shift === void 0) {\n      shift = new Point(0, 0);\n    }\n    var width = imgDims.width,\n      height = imgDims.height;\n    this._imgDims = new Dimensions(width, height);\n    this._shift = shift;\n    this._positions = relativeFaceLandmarkPositions.map(function (pt) {\n      return pt.mul(new Point(width, height)).add(shift);\n    });\n  }\n  Object.defineProperty(FaceLandmarks.prototype, \"shift\", {\n    get: function () {\n      return new Point(this._shift.x, this._shift.y);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(FaceLandmarks.prototype, \"imageWidth\", {\n    get: function () {\n      return this._imgDims.width;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(FaceLandmarks.prototype, \"imageHeight\", {\n    get: function () {\n      return this._imgDims.height;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(FaceLandmarks.prototype, \"positions\", {\n    get: function () {\n      return this._positions;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(FaceLandmarks.prototype, \"relativePositions\", {\n    get: function () {\n      var _this = this;\n      return this._positions.map(function (pt) {\n        return pt.sub(_this._shift).div(new Point(_this.imageWidth, _this.imageHeight));\n      });\n    },\n    enumerable: true,\n    configurable: true\n  });\n  FaceLandmarks.prototype.forSize = function (width, height) {\n    return new this.constructor(this.relativePositions, {\n      width: width,\n      height: height\n    });\n  };\n  FaceLandmarks.prototype.shiftBy = function (x, y) {\n    return new this.constructor(this.relativePositions, this._imgDims, new Point(x, y));\n  };\n  FaceLandmarks.prototype.shiftByPoint = function (pt) {\n    return this.shiftBy(pt.x, pt.y);\n  };\n  /**\r\n   * Aligns the face landmarks after face detection from the relative positions of the faces\r\n   * bounding box, or it's current shift. This function should be used to align the face images\r\n   * after face detection has been performed, before they are passed to the face recognition net.\r\n   * This will make the computed face descriptor more accurate.\r\n   *\r\n   * @param detection (optional) The bounding box of the face or the face detection result. If\r\n   * no argument was passed the position of the face landmarks are assumed to be relative to\r\n   * it's current shift.\r\n   * @returns The bounding box of the aligned face.\r\n   */\n  FaceLandmarks.prototype.align = function (detection, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    if (detection) {\n      var box = detection instanceof FaceDetection ? detection.box.floor() : new Box(detection);\n      return this.shiftBy(box.x, box.y).align(null, options);\n    }\n    var _a = Object.assign({}, {\n        useDlibAlignment: false,\n        minBoxPadding: 0.2\n      }, options),\n      useDlibAlignment = _a.useDlibAlignment,\n      minBoxPadding = _a.minBoxPadding;\n    if (useDlibAlignment) {\n      return this.alignDlib();\n    }\n    return this.alignMinBbox(minBoxPadding);\n  };\n  FaceLandmarks.prototype.alignDlib = function () {\n    var centers = this.getRefPointsForAlignment();\n    var leftEyeCenter = centers[0],\n      rightEyeCenter = centers[1],\n      mouthCenter = centers[2];\n    var distToMouth = function (pt) {\n      return mouthCenter.sub(pt).magnitude();\n    };\n    var eyeToMouthDist = (distToMouth(leftEyeCenter) + distToMouth(rightEyeCenter)) / 2;\n    var size = Math.floor(eyeToMouthDist / relScale);\n    var refPoint = getCenterPoint(centers);\n    // TODO: pad in case rectangle is out of image bounds\n    var x = Math.floor(Math.max(0, refPoint.x - relX * size));\n    var y = Math.floor(Math.max(0, refPoint.y - relY * size));\n    return new Rect(x, y, Math.min(size, this.imageWidth + x), Math.min(size, this.imageHeight + y));\n  };\n  FaceLandmarks.prototype.alignMinBbox = function (padding) {\n    var box = minBbox(this.positions);\n    return box.pad(box.width * padding, box.height * padding);\n  };\n  FaceLandmarks.prototype.getRefPointsForAlignment = function () {\n    throw new Error('getRefPointsForAlignment not implemented by base class');\n  };\n  return FaceLandmarks;\n}();\nexport { FaceLandmarks };", "map": {"version": 3, "names": ["minBbox", "getCenterPoint", "Box", "Dimensions", "FaceDetection", "Point", "Rect", "relX", "relY", "relScale", "FaceLandmarks", "relativeFaceLandmarkPositions", "imgDims", "shift", "width", "height", "_imgDims", "_shift", "_positions", "map", "pt", "mul", "add", "Object", "defineProperty", "prototype", "get", "x", "y", "_this", "sub", "div", "imageWidth", "imageHeight", "forSize", "constructor", "relativePositions", "shiftBy", "shiftByPoint", "align", "detection", "options", "box", "floor", "_a", "assign", "useDlibAlignment", "minBoxPadding", "alignDlib", "alignMinBbox", "centers", "getRefPointsForAlignment", "leftEyeCenter", "rightEyeCenter", "mouthCenter", "distToMouth", "magnitude", "eyeToMouthDist", "size", "Math", "refPoint", "max", "min", "padding", "positions", "pad", "Error"], "sources": ["../../../src/classes/FaceLandmarks.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,OAAO,QAAQ,QAAQ;AAChC,SAASC,cAAc,QAAQ,UAAU;AAEzC,SAASC,GAAG,QAAQ,OAAO;AAC3B,SAASC,UAAU,QAAqB,cAAc;AACtD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAAgBC,IAAI,QAAQ,QAAQ;AAEpC;AACA,IAAMC,IAAI,GAAG,GAAG;AAChB,IAAMC,IAAI,GAAG,IAAI;AACjB,IAAMC,QAAQ,GAAG,IAAI;AAOrB,IAAAC,aAAA;EAKE,SAAAA,cACEC,6BAAsC,EACtCC,OAAoB,EACpBC,KAA8B;IAA9B,IAAAA,KAAA;MAAAA,KAAA,OAAmBR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAEtB,IAAAS,KAAA,GAAAF,OAAA,CAAAE,KAAK;MAAEC,MAAA,GAAAH,OAAA,CAAAG,MAAM;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAIb,UAAU,CAACW,KAAK,EAAEC,MAAM,CAAC;IAC7C,IAAI,CAACE,MAAM,GAAGJ,KAAK;IACnB,IAAI,CAACK,UAAU,GAAGP,6BAA6B,CAACQ,GAAG,CACjD,UAAAC,EAAE;MAAI,OAAAA,EAAE,CAACC,GAAG,CAAC,IAAIhB,KAAK,CAACS,KAAK,EAAEC,MAAM,CAAC,CAAC,CAACO,GAAG,CAACT,KAAK,CAAC;IAA3C,CAA2C,CAClD;EACH;EAEAU,MAAA,CAAAC,cAAA,CAAWd,aAAA,CAAAe,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA4B,OAAO,IAAIrB,KAAK,CAAC,IAAI,CAACY,MAAM,CAACU,CAAC,EAAE,IAAI,CAACV,MAAM,CAACW,CAAC,CAAC;IAAC,CAAC;;;;EAC5EL,MAAA,CAAAC,cAAA,CAAWd,aAAA,CAAAe,SAAA,cAAU;SAArB,SAAAC,CAAA;MAAkC,OAAO,IAAI,CAACV,QAAQ,CAACF,KAAK;IAAC,CAAC;;;;EAC9DS,MAAA,CAAAC,cAAA,CAAWd,aAAA,CAAAe,SAAA,eAAW;SAAtB,SAAAC,CAAA;MAAmC,OAAO,IAAI,CAACV,QAAQ,CAACD,MAAM;IAAC,CAAC;;;;EAChEQ,MAAA,CAAAC,cAAA,CAAWd,aAAA,CAAAe,SAAA,aAAS;SAApB,SAAAC,CAAA;MAAkC,OAAO,IAAI,CAACR,UAAU;IAAC,CAAC;;;;EAC1DK,MAAA,CAAAC,cAAA,CAAWd,aAAA,CAAAe,SAAA,qBAAiB;SAA5B,SAAAC,CAAA;MAAA,IAAAG,KAAA;MACE,OAAO,IAAI,CAACX,UAAU,CAACC,GAAG,CACxB,UAAAC,EAAE;QAAI,OAAAA,EAAE,CAACU,GAAG,CAACD,KAAI,CAACZ,MAAM,CAAC,CAACc,GAAG,CAAC,IAAI1B,KAAK,CAACwB,KAAI,CAACG,UAAU,EAAEH,KAAI,CAACI,WAAW,CAAC,CAAC;MAArE,CAAqE,CAC5E;IACH,CAAC;;;;EAEMvB,aAAA,CAAAe,SAAA,CAAAS,OAAO,GAAd,UAAwCpB,KAAa,EAAEC,MAAc;IACnE,OAAO,IAAK,IAAI,CAACoB,WAAmB,CAClC,IAAI,CAACC,iBAAiB,EACtB;MAAEtB,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAClB;EACH,CAAC;EAEML,aAAA,CAAAe,SAAA,CAAAY,OAAO,GAAd,UAAwCV,CAAS,EAAEC,CAAS;IAC1D,OAAO,IAAK,IAAI,CAACO,WAAmB,CAClC,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACpB,QAAQ,EACb,IAAIX,KAAK,CAACsB,CAAC,EAAEC,CAAC,CAAC,CAChB;EACH,CAAC;EAEMlB,aAAA,CAAAe,SAAA,CAAAa,YAAY,GAAnB,UAA6ClB,EAAS;IACpD,OAAO,IAAI,CAACiB,OAAO,CAACjB,EAAE,CAACO,CAAC,EAAEP,EAAE,CAACQ,CAAC,CAAC;EACjC,CAAC;EAED;;;;;;;;;;;EAWOlB,aAAA,CAAAe,SAAA,CAAAc,KAAK,GAAZ,UACEC,SAAuD,EACvDC,OAAqE;IAArE,IAAAA,OAAA;MAAAA,OAAA,KAAqE;IAAA;IAErE,IAAID,SAAS,EAAE;MACb,IAAME,GAAG,GAAGF,SAAS,YAAYpC,aAAa,GAC1CoC,SAAS,CAACE,GAAG,CAACC,KAAK,EAAE,GACrB,IAAIzC,GAAG,CAACsC,SAAS,CAAC;MAEtB,OAAO,IAAI,CAACH,OAAO,CAACK,GAAG,CAACf,CAAC,EAAEe,GAAG,CAACd,CAAC,CAAC,CAACW,KAAK,CAAC,IAAI,EAAEE,OAAO,CAAC;;IAGlD,IAAAG,EAAA,GAAArB,MAAA,CAAAsB,MAAA;QAAAC,gBAAA;QAAAC,aAAA;MAAA,GAAAN,OAAA,CAAiH;MAA/GK,gBAAA,GAAAF,EAAA,CAAAE,gBAAgB;MAAEC,aAAA,GAAAH,EAAA,CAAAG,aAA6F;IAEvH,IAAID,gBAAgB,EAAE;MACpB,OAAO,IAAI,CAACE,SAAS,EAAE;;IAGzB,OAAO,IAAI,CAACC,YAAY,CAACF,aAAa,CAAC;EACzC,CAAC;EAEOrC,aAAA,CAAAe,SAAA,CAAAuB,SAAS,GAAjB;IAEE,IAAME,OAAO,GAAG,IAAI,CAACC,wBAAwB,EAAE;IAExC,IAAAC,aAAA,GAAAF,OAAA,GAAa;MAAEG,cAAA,GAAAH,OAAA,GAAc;MAAEI,WAAA,GAAAJ,OAAA,GAAW;IACjD,IAAMK,WAAW,GAAG,SAAAA,CAACnC,EAAS;MAAK,OAAAkC,WAAW,CAACxB,GAAG,CAACV,EAAE,CAAC,CAACoC,SAAS,EAAE;IAA/B,CAA+B;IAClE,IAAMC,cAAc,GAAG,CAACF,WAAW,CAACH,aAAa,CAAC,GAAGG,WAAW,CAACF,cAAc,CAAC,IAAI,CAAC;IAErF,IAAMK,IAAI,GAAGC,IAAI,CAAChB,KAAK,CAACc,cAAc,GAAGhD,QAAQ,CAAC;IAElD,IAAMmD,QAAQ,GAAG3D,cAAc,CAACiD,OAAO,CAAC;IACxC;IACA,IAAMvB,CAAC,GAAGgC,IAAI,CAAChB,KAAK,CAACgB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACjC,CAAC,GAAIpB,IAAI,GAAGmD,IAAK,CAAC,CAAC;IAC7D,IAAM9B,CAAC,GAAG+B,IAAI,CAAChB,KAAK,CAACgB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAED,QAAQ,CAAChC,CAAC,GAAIpB,IAAI,GAAGkD,IAAK,CAAC,CAAC;IAE7D,OAAO,IAAIpD,IAAI,CAACqB,CAAC,EAAEC,CAAC,EAAE+B,IAAI,CAACG,GAAG,CAACJ,IAAI,EAAE,IAAI,CAAC1B,UAAU,GAAGL,CAAC,CAAC,EAAEgC,IAAI,CAACG,GAAG,CAACJ,IAAI,EAAE,IAAI,CAACzB,WAAW,GAAGL,CAAC,CAAC,CAAC;EAClG,CAAC;EAEOlB,aAAA,CAAAe,SAAA,CAAAwB,YAAY,GAApB,UAAqBc,OAAe;IAClC,IAAMrB,GAAG,GAAG1C,OAAO,CAAC,IAAI,CAACgE,SAAS,CAAC;IACnC,OAAOtB,GAAG,CAACuB,GAAG,CAACvB,GAAG,CAAC5B,KAAK,GAAGiD,OAAO,EAAErB,GAAG,CAAC3B,MAAM,GAAGgD,OAAO,CAAC;EAC3D,CAAC;EAESrD,aAAA,CAAAe,SAAA,CAAA0B,wBAAwB,GAAlC;IACE,MAAM,IAAIe,KAAK,CAAC,wDAAwD,CAAC;EAC3E,CAAC;EACH,OAAAxD,aAAC;AAAD,CAAC,CAzGD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}