{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { _generateLinkResponse, _noResolveJsonResponse, _request, _userResponse } from './lib/fetch';\nimport { resolveFetch, validateUUID } from './lib/helpers';\nimport { SIGN_OUT_SCOPES } from './lib/types';\nimport { isAuthError } from './lib/errors';\nexport default class GoTrueAdminApi {\n  constructor({\n    url = '',\n    headers = {},\n    fetch\n  }) {\n    this.url = url;\n    this.headers = headers;\n    this.fetch = resolveFetch(fetch);\n    this.mfa = {\n      listFactors: this._listFactors.bind(this),\n      deleteFactor: this._deleteFactor.bind(this)\n    };\n  }\n  /**\n   * Removes a logged-in session.\n   * @param jwt A valid, logged-in JWT.\n   * @param scope The logout sope.\n   */\n  async signOut(jwt, scope = SIGN_OUT_SCOPES[0]) {\n    if (SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n      throw new Error(`@supabase/auth-js: Parameter scope must be one of ${SIGN_OUT_SCOPES.join(', ')}`);\n    }\n    try {\n      await _request(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n        headers: this.headers,\n        jwt,\n        noResolveJson: true\n      });\n      return {\n        data: null,\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sends an invite link to an email address.\n   * @param email The email address of the user.\n   * @param options Additional options to be included when inviting.\n   */\n  async inviteUserByEmail(email, options = {}) {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/invite`, {\n        body: {\n          email,\n          data: options.data\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Generates email links and OTPs to be sent via a custom email provider.\n   * @param email The user's email.\n   * @param options.password User password. For signup only.\n   * @param options.data Optional user metadata. For signup only.\n   * @param options.redirectTo The redirect url which should be appended to the generated link\n   */\n  async generateLink(params) {\n    try {\n      const {\n          options\n        } = params,\n        rest = __rest(params, [\"options\"]);\n      const body = Object.assign(Object.assign({}, rest), options);\n      if ('newEmail' in rest) {\n        // replace newEmail with new_email in request body\n        body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n        delete body['newEmail'];\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n        body: body,\n        headers: this.headers,\n        xform: _generateLinkResponse,\n        redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            properties: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  // User Admin API\n  /**\n   * Creates a new user.\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async createUser(attributes) {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/admin/users`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Get a list of users.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n   */\n  async listUsers(params) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    try {\n      const pagination = {\n        nextPage: null,\n        lastPage: 0,\n        total: 0\n      };\n      const response = await _request(this.fetch, 'GET', `${this.url}/admin/users`, {\n        headers: this.headers,\n        noResolveJson: true,\n        query: {\n          page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n          per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : ''\n        },\n        xform: _noResolveJsonResponse\n      });\n      if (response.error) throw response.error;\n      const users = await response.json();\n      const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n      const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n      if (links.length > 0) {\n        links.forEach(link => {\n          const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n          const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n          pagination[`${rel}Page`] = page;\n        });\n        pagination.total = parseInt(total);\n      }\n      return {\n        data: Object.assign(Object.assign({}, users), pagination),\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            users: []\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Get user by id.\n   *\n   * @param uid The user's unique identifier\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async getUserById(uid) {\n    validateUUID(uid);\n    try {\n      return await _request(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n        headers: this.headers,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Updates the user data.\n   *\n   * @param attributes The data you want to update.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async updateUserById(uid, attributes) {\n    validateUUID(uid);\n    try {\n      return await _request(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Delete a user. Requires a `service_role` key.\n   *\n   * @param id The user id you want to remove.\n   * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n   * Defaults to false for backward compatibility.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async deleteUser(id, shouldSoftDelete = false) {\n    validateUUID(id);\n    try {\n      return await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n        headers: this.headers,\n        body: {\n          should_soft_delete: shouldSoftDelete\n        },\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _listFactors(params) {\n    validateUUID(params.userId);\n    try {\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n        headers: this.headers,\n        xform: factors => {\n          return {\n            data: {\n              factors\n            },\n            error: null\n          };\n        }\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _deleteFactor(params) {\n    validateUUID(params.userId);\n    validateUUID(params.id);\n    try {\n      const data = await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n        headers: this.headers\n      });\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["_generateLinkResponse", "_noResolveJsonResponse", "_request", "_userResponse", "resolveFetch", "validateUUID", "SIGN_OUT_SCOPES", "isAuthError", "GoTrueAdminApi", "constructor", "url", "headers", "fetch", "mfa", "listFactors", "_listFactors", "bind", "deleteFactor", "_deleteFactor", "signOut", "jwt", "scope", "indexOf", "Error", "join", "noResolveJson", "data", "error", "inviteUserByEmail", "email", "options", "body", "redirectTo", "xform", "user", "generateLink", "params", "rest", "__rest", "Object", "assign", "new_email", "newEmail", "properties", "createUser", "attributes", "listUsers", "pagination", "nextPage", "lastPage", "total", "response", "query", "page", "_b", "_a", "toString", "per_page", "_d", "_c", "perPage", "users", "json", "_e", "get", "links", "_g", "_f", "split", "length", "for<PERSON>ach", "link", "parseInt", "substring", "rel", "JSON", "parse", "getUserById", "uid", "updateUserById", "deleteUser", "id", "shouldSoftDelete", "should_soft_delete", "userId", "factors"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@supabase/auth-js/src/GoTrueAdminApi.ts"], "sourcesContent": ["import {\n  Fetch,\n  _generateLinkResponse,\n  _noResolveJsonResponse,\n  _request,\n  _userResponse,\n} from './lib/fetch'\nimport { resolveFetch, validateUUID } from './lib/helpers'\nimport {\n  AdminUserAttributes,\n  GenerateLinkParams,\n  GenerateLinkResponse,\n  Pagination,\n  User,\n  UserResponse,\n  GoTrueAdminMFAApi,\n  AuthMFAAdminDeleteFactorParams,\n  AuthMFAAdminDeleteFactorResponse,\n  AuthMFAAdminListFactorsParams,\n  AuthMFAAdminListFactorsResponse,\n  PageParams,\n  SIGN_OUT_SCOPES,\n  SignOutScope,\n} from './lib/types'\nimport { AuthError, isAuthError } from './lib/errors'\n\nexport default class GoTrueAdminApi {\n  /** Contains all MFA administration methods. */\n  mfa: GoTrueAdminMFAApi\n\n  protected url: string\n  protected headers: {\n    [key: string]: string\n  }\n  protected fetch: Fetch\n\n  constructor({\n    url = '',\n    headers = {},\n    fetch,\n  }: {\n    url: string\n    headers?: {\n      [key: string]: string\n    }\n    fetch?: Fetch\n  }) {\n    this.url = url\n    this.headers = headers\n    this.fetch = resolveFetch(fetch)\n    this.mfa = {\n      listFactors: this._listFactors.bind(this),\n      deleteFactor: this._deleteFactor.bind(this),\n    }\n  }\n\n  /**\n   * Removes a logged-in session.\n   * @param jwt A valid, logged-in JWT.\n   * @param scope The logout sope.\n   */\n  async signOut(\n    jwt: string,\n    scope: SignOutScope = SIGN_OUT_SCOPES[0]\n  ): Promise<{ data: null; error: AuthError | null }> {\n    if (SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n      throw new Error(\n        `@supabase/auth-js: Parameter scope must be one of ${SIGN_OUT_SCOPES.join(', ')}`\n      )\n    }\n\n    try {\n      await _request(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n        headers: this.headers,\n        jwt,\n        noResolveJson: true,\n      })\n      return { data: null, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Sends an invite link to an email address.\n   * @param email The email address of the user.\n   * @param options Additional options to be included when inviting.\n   */\n  async inviteUserByEmail(\n    email: string,\n    options: {\n      /** A custom data object to store additional metadata about the user. This maps to the `auth.users.user_metadata` column. */\n      data?: object\n\n      /** The URL which will be appended to the email link sent to the user's email address. Once clicked the user will end up on this URL. */\n      redirectTo?: string\n    } = {}\n  ): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/invite`, {\n        body: { email, data: options.data },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Generates email links and OTPs to be sent via a custom email provider.\n   * @param email The user's email.\n   * @param options.password User password. For signup only.\n   * @param options.data Optional user metadata. For signup only.\n   * @param options.redirectTo The redirect url which should be appended to the generated link\n   */\n  async generateLink(params: GenerateLinkParams): Promise<GenerateLinkResponse> {\n    try {\n      const { options, ...rest } = params\n      const body: any = { ...rest, ...options }\n      if ('newEmail' in rest) {\n        // replace newEmail with new_email in request body\n        body.new_email = rest?.newEmail\n        delete body['newEmail']\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n        body: body,\n        headers: this.headers,\n        xform: _generateLinkResponse,\n        redirectTo: options?.redirectTo,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            properties: null,\n            user: null,\n          },\n          error,\n        }\n      }\n      throw error\n    }\n  }\n\n  // User Admin API\n  /**\n   * Creates a new user.\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async createUser(attributes: AdminUserAttributes): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/admin/users`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Get a list of users.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n   */\n  async listUsers(\n    params?: PageParams\n  ): Promise<\n    | { data: { users: User[]; aud: string } & Pagination; error: null }\n    | { data: { users: [] }; error: AuthError }\n  > {\n    try {\n      const pagination: Pagination = { nextPage: null, lastPage: 0, total: 0 }\n      const response = await _request(this.fetch, 'GET', `${this.url}/admin/users`, {\n        headers: this.headers,\n        noResolveJson: true,\n        query: {\n          page: params?.page?.toString() ?? '',\n          per_page: params?.perPage?.toString() ?? '',\n        },\n        xform: _noResolveJsonResponse,\n      })\n      if (response.error) throw response.error\n\n      const users = await response.json()\n      const total = response.headers.get('x-total-count') ?? 0\n      const links = response.headers.get('link')?.split(',') ?? []\n      if (links.length > 0) {\n        links.forEach((link: string) => {\n          const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1))\n          const rel = JSON.parse(link.split(';')[1].split('=')[1])\n          pagination[`${rel}Page`] = page\n        })\n\n        pagination.total = parseInt(total)\n      }\n      return { data: { ...users, ...pagination }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { users: [] }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Get user by id.\n   *\n   * @param uid The user's unique identifier\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async getUserById(uid: string): Promise<UserResponse> {\n    validateUUID(uid)\n\n    try {\n      return await _request(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n        headers: this.headers,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates the user data.\n   *\n   * @param attributes The data you want to update.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async updateUserById(uid: string, attributes: AdminUserAttributes): Promise<UserResponse> {\n    validateUUID(uid)\n\n    try {\n      return await _request(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Delete a user. Requires a `service_role` key.\n   *\n   * @param id The user id you want to remove.\n   * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n   * Defaults to false for backward compatibility.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async deleteUser(id: string, shouldSoftDelete = false): Promise<UserResponse> {\n    validateUUID(id)\n\n    try {\n      return await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n        headers: this.headers,\n        body: {\n          should_soft_delete: shouldSoftDelete,\n        },\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  private async _listFactors(\n    params: AuthMFAAdminListFactorsParams\n  ): Promise<AuthMFAAdminListFactorsResponse> {\n    validateUUID(params.userId)\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'GET',\n        `${this.url}/admin/users/${params.userId}/factors`,\n        {\n          headers: this.headers,\n          xform: (factors: any) => {\n            return { data: { factors }, error: null }\n          },\n        }\n      )\n      return { data, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  private async _deleteFactor(\n    params: AuthMFAAdminDeleteFactorParams\n  ): Promise<AuthMFAAdminDeleteFactorResponse> {\n    validateUUID(params.userId)\n    validateUUID(params.id)\n\n    try {\n      const data = await _request(\n        this.fetch,\n        'DELETE',\n        `${this.url}/admin/users/${params.userId}/factors/${params.id}`,\n        {\n          headers: this.headers,\n        }\n      )\n\n      return { data, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAEEA,qBAAqB,EACrBC,sBAAsB,EACtBC,QAAQ,EACRC,aAAa,QACR,aAAa;AACpB,SAASC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC1D,SAaEC,eAAe,QAEV,aAAa;AACpB,SAAoBC,WAAW,QAAQ,cAAc;AAErD,eAAc,MAAOC,cAAc;EAUjCC,YAAY;IACVC,GAAG,GAAG,EAAE;IACRC,OAAO,GAAG,EAAE;IACZC;EAAK,CAON;IACC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGR,YAAY,CAACQ,KAAK,CAAC;IAChC,IAAI,CAACC,GAAG,GAAG;MACTC,WAAW,EAAE,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC;MACzCC,YAAY,EAAE,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI;KAC3C;EACH;EAEA;;;;;EAKA,MAAMG,OAAOA,CACXC,GAAW,EACXC,KAAA,GAAsBf,eAAe,CAAC,CAAC,CAAC;IAExC,IAAIA,eAAe,CAACgB,OAAO,CAACD,KAAK,CAAC,GAAG,CAAC,EAAE;MACtC,MAAM,IAAIE,KAAK,CACb,qDAAqDjB,eAAe,CAACkB,IAAI,CAAC,IAAI,CAAC,EAAE,CAClF;;IAGH,IAAI;MACF,MAAMtB,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACF,GAAG,iBAAiBW,KAAK,EAAE,EAAE;QACtEV,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBS,GAAG;QACHK,aAAa,EAAE;OAChB,CAAC;MACF,OAAO;QAAEC,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAI,CAAE;KACnC,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE,IAAI;UAAEC;QAAK,CAAE;;MAG9B,MAAMA,KAAK;;EAEf;EAEA;;;;;EAKA,MAAMC,iBAAiBA,CACrBC,KAAa,EACbC,OAAA,GAMI,EAAE;IAEN,IAAI;MACF,OAAO,MAAM5B,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACF,GAAG,SAAS,EAAE;QAC9DqB,IAAI,EAAE;UAAEF,KAAK;UAAEH,IAAI,EAAEI,OAAO,CAACJ;QAAI,CAAE;QACnCf,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBqB,UAAU,EAAEF,OAAO,CAACE,UAAU;QAC9BC,KAAK,EAAE9B;OACR,CAAC;KACH,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE;YAAEQ,IAAI,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;;;;;EAOA,MAAMQ,YAAYA,CAACC,MAA0B;IAC3C,IAAI;MACF,MAAM;UAAEN;QAAO,IAAcM,MAAM;QAAfC,IAAI,GAAAC,MAAA,CAAKF,MAAM,EAA7B,WAAoB,CAAS;MACnC,MAAML,IAAI,GAAAQ,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAaH,IAAI,GAAKP,OAAO,CAAE;MACzC,IAAI,UAAU,IAAIO,IAAI,EAAE;QACtB;QACAN,IAAI,CAACU,SAAS,GAAGJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,QAAQ;QAC/B,OAAOX,IAAI,CAAC,UAAU,CAAC;;MAEzB,OAAO,MAAM7B,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACF,GAAG,sBAAsB,EAAE;QAC3EqB,IAAI,EAAEA,IAAI;QACVpB,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBsB,KAAK,EAAEjC,qBAAqB;QAC5BgC,UAAU,EAAEF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE;OACtB,CAAC;KACH,CAAC,OAAOL,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UACLD,IAAI,EAAE;YACJiB,UAAU,EAAE,IAAI;YAChBT,IAAI,EAAE;WACP;UACDP;SACD;;MAEH,MAAMA,KAAK;;EAEf;EAEA;EACA;;;;EAIA,MAAMiB,UAAUA,CAACC,UAA+B;IAC9C,IAAI;MACF,OAAO,MAAM3C,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACF,GAAG,cAAc,EAAE;QACnEqB,IAAI,EAAEc,UAAU;QAChBlC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBsB,KAAK,EAAE9B;OACR,CAAC;KACH,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE;YAAEQ,IAAI,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;;;;EAMA,MAAMmB,SAASA,CACbV,MAAmB;;IAKnB,IAAI;MACF,MAAMW,UAAU,GAAe;QAAEC,QAAQ,EAAE,IAAI;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAC,CAAE;MACxE,MAAMC,QAAQ,GAAG,MAAMjD,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,cAAc,EAAE;QAC5EC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBc,aAAa,EAAE,IAAI;QACnB2B,KAAK,EAAE;UACLC,IAAI,EAAE,CAAAC,EAAA,IAAAC,EAAA,GAAAnB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,IAAI,cAAAE,EAAA,uBAAAA,EAAA,CAAEC,QAAQ,EAAE,cAAAF,EAAA,cAAAA,EAAA,GAAI,EAAE;UACpCG,QAAQ,EAAE,CAAAC,EAAA,IAAAC,EAAA,GAAAvB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEwB,OAAO,cAAAD,EAAA,uBAAAA,EAAA,CAAEH,QAAQ,EAAE,cAAAE,EAAA,cAAAA,EAAA,GAAI;SAC1C;QACDzB,KAAK,EAAEhC;OACR,CAAC;MACF,IAAIkD,QAAQ,CAACxB,KAAK,EAAE,MAAMwB,QAAQ,CAACxB,KAAK;MAExC,MAAMkC,KAAK,GAAG,MAAMV,QAAQ,CAACW,IAAI,EAAE;MACnC,MAAMZ,KAAK,GAAG,CAAAa,EAAA,GAAAZ,QAAQ,CAACxC,OAAO,CAACqD,GAAG,CAAC,eAAe,CAAC,cAAAD,EAAA,cAAAA,EAAA,GAAI,CAAC;MACxD,MAAME,KAAK,GAAG,CAAAC,EAAA,IAAAC,EAAA,GAAAhB,QAAQ,CAACxC,OAAO,CAACqD,GAAG,CAAC,MAAM,CAAC,cAAAG,EAAA,uBAAAA,EAAA,CAAEC,KAAK,CAAC,GAAG,CAAC,cAAAF,EAAA,cAAAA,EAAA,GAAI,EAAE;MAC5D,IAAID,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;QACpBJ,KAAK,CAACK,OAAO,CAAEC,IAAY,IAAI;UAC7B,MAAMlB,IAAI,GAAGmB,QAAQ,CAACD,IAAI,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACvE,MAAMC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACxDrB,UAAU,CAAC,GAAG2B,GAAG,MAAM,CAAC,GAAGrB,IAAI;QACjC,CAAC,CAAC;QAEFN,UAAU,CAACG,KAAK,GAAGsB,QAAQ,CAACtB,KAAK,CAAC;;MAEpC,OAAO;QAAExB,IAAI,EAAAa,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOqB,KAAK,GAAKd,UAAU,CAAE;QAAEpB,KAAK,EAAE;MAAI,CAAE;KAC1D,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE;YAAEmC,KAAK,EAAE;UAAE,CAAE;UAAElC;QAAK,CAAE;;MAEvC,MAAMA,KAAK;;EAEf;EAEA;;;;;;;EAOA,MAAMkD,WAAWA,CAACC,GAAW;IAC3BzE,YAAY,CAACyE,GAAG,CAAC;IAEjB,IAAI;MACF,OAAO,MAAM5E,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,gBAAgBoE,GAAG,EAAE,EAAE;QACzEnE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBsB,KAAK,EAAE9B;OACR,CAAC;KACH,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE;YAAEQ,IAAI,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;;;;;EAOA,MAAMoD,cAAcA,CAACD,GAAW,EAAEjC,UAA+B;IAC/DxC,YAAY,CAACyE,GAAG,CAAC;IAEjB,IAAI;MACF,OAAO,MAAM5E,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,gBAAgBoE,GAAG,EAAE,EAAE;QACzE/C,IAAI,EAAEc,UAAU;QAChBlC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBsB,KAAK,EAAE9B;OACR,CAAC;KACH,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE;YAAEQ,IAAI,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;EASA,MAAMqD,UAAUA,CAACC,EAAU,EAAEC,gBAAgB,GAAG,KAAK;IACnD7E,YAAY,CAAC4E,EAAE,CAAC;IAEhB,IAAI;MACF,OAAO,MAAM/E,QAAQ,CAAC,IAAI,CAACU,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAACF,GAAG,gBAAgBuE,EAAE,EAAE,EAAE;QAC3EtE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBoB,IAAI,EAAE;UACJoD,kBAAkB,EAAED;SACrB;QACDjD,KAAK,EAAE9B;OACR,CAAC;KACH,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE;YAAEQ,IAAI,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEQ,MAAMZ,YAAYA,CACxBqB,MAAqC;IAErC/B,YAAY,CAAC+B,MAAM,CAACgD,MAAM,CAAC;IAE3B,IAAI;MACF,MAAM;QAAE1D,IAAI;QAAEC;MAAK,CAAE,GAAG,MAAMzB,QAAQ,CACpC,IAAI,CAACU,KAAK,EACV,KAAK,EACL,GAAG,IAAI,CAACF,GAAG,gBAAgB0B,MAAM,CAACgD,MAAM,UAAU,EAClD;QACEzE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBsB,KAAK,EAAGoD,OAAY,IAAI;UACtB,OAAO;YAAE3D,IAAI,EAAE;cAAE2D;YAAO,CAAE;YAAE1D,KAAK,EAAE;UAAI,CAAE;QAC3C;OACD,CACF;MACD,OAAO;QAAED,IAAI;QAAEC;MAAK,CAAE;KACvB,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE,IAAI;UAAEC;QAAK,CAAE;;MAG9B,MAAMA,KAAK;;EAEf;EAEQ,MAAMT,aAAaA,CACzBkB,MAAsC;IAEtC/B,YAAY,CAAC+B,MAAM,CAACgD,MAAM,CAAC;IAC3B/E,YAAY,CAAC+B,MAAM,CAAC6C,EAAE,CAAC;IAEvB,IAAI;MACF,MAAMvD,IAAI,GAAG,MAAMxB,QAAQ,CACzB,IAAI,CAACU,KAAK,EACV,QAAQ,EACR,GAAG,IAAI,CAACF,GAAG,gBAAgB0B,MAAM,CAACgD,MAAM,YAAYhD,MAAM,CAAC6C,EAAE,EAAE,EAC/D;QACEtE,OAAO,EAAE,IAAI,CAACA;OACf,CACF;MAED,OAAO;QAAEe,IAAI;QAAEC,KAAK,EAAE;MAAI,CAAE;KAC7B,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;QACtB,OAAO;UAAED,IAAI,EAAE,IAAI;UAAEC;QAAK,CAAE;;MAG9B,MAAMA,KAAK;;EAEf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}