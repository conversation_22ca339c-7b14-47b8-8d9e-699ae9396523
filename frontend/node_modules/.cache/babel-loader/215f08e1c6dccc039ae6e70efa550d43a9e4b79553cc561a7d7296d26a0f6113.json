{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { toNetInput } from '../dom';\nimport { FaceFeatureExtractor } from '../faceFeatureExtractor/FaceFeatureExtractor';\nimport { FaceProcessor } from '../faceProcessor/FaceProcessor';\nimport { FaceExpressions } from './FaceExpressions';\nvar FaceExpressionNet = /** @class */function (_super) {\n  __extends(FaceExpressionNet, _super);\n  function FaceExpressionNet(faceFeatureExtractor) {\n    if (faceFeatureExtractor === void 0) {\n      faceFeatureExtractor = new FaceFeatureExtractor();\n    }\n    return _super.call(this, 'FaceExpressionNet', faceFeatureExtractor) || this;\n  }\n  FaceExpressionNet.prototype.forwardInput = function (input) {\n    var _this = this;\n    return tf.tidy(function () {\n      return tf.softmax(_this.runNet(input));\n    });\n  };\n  FaceExpressionNet.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  FaceExpressionNet.prototype.predictExpressions = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var netInput, out, probabilitesByBatch, predictionsByBatch;\n      var _this = this;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            netInput = _a.sent();\n            return [4 /*yield*/, this.forwardInput(netInput)];\n          case 2:\n            out = _a.sent();\n            return [4 /*yield*/, Promise.all(tf.unstack(out).map(function (t) {\n              return __awaiter(_this, void 0, void 0, function () {\n                var data;\n                return __generator(this, function (_a) {\n                  switch (_a.label) {\n                    case 0:\n                      return [4 /*yield*/, t.data()];\n                    case 1:\n                      data = _a.sent();\n                      t.dispose();\n                      return [2 /*return*/, data];\n                  }\n                });\n              });\n            }))];\n          case 3:\n            probabilitesByBatch = _a.sent();\n            out.dispose();\n            predictionsByBatch = probabilitesByBatch.map(function (probabilites) {\n              return new FaceExpressions(probabilites);\n            });\n            return [2 /*return*/, netInput.isBatchInput ? predictionsByBatch : predictionsByBatch[0]];\n        }\n      });\n    });\n  };\n  FaceExpressionNet.prototype.getDefaultModelName = function () {\n    return 'face_expression_model';\n  };\n  FaceExpressionNet.prototype.getClassifierChannelsIn = function () {\n    return 256;\n  };\n  FaceExpressionNet.prototype.getClassifierChannelsOut = function () {\n    return 7;\n  };\n  return FaceExpressionNet;\n}(FaceProcessor);\nexport { FaceExpressionNet };", "map": {"version": 3, "names": ["tf", "toNetInput", "FaceFeatureExtractor", "FaceProcessor", "FaceExpressions", "FaceExpressionNet", "_super", "__extends", "faceFeatureExtractor", "call", "prototype", "forwardInput", "input", "_this", "tidy", "softmax", "runNet", "forward", "_a", "apply", "_b", "sent", "predictExpressions", "netInput", "out", "Promise", "all", "unstack", "map", "t", "__awaiter", "data", "dispose", "probabilitesByBatch", "predictionsByBatch", "probabilites", "isBatchInput", "getDefaultModelName", "getClassifierChannelsIn", "getClassifierChannelsOut"], "sources": ["../../../src/faceExpressionNet/FaceExpressionNet.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,oBAAoB,QAAQ,8CAA8C;AAEnF,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,eAAe,QAAQ,mBAAmB;AAEnD,IAAAC,iBAAA,0BAAAC,MAAA;EAAuCC,SAAA,CAAAF,iBAAA,EAAAC,MAAA;EAErC,SAAAD,kBAAYG,oBAAuE;IAAvE,IAAAA,oBAAA;MAAAA,oBAAA,OAAiDN,oBAAoB,EAAE;IAAA;WACjFI,MAAA,CAAAG,IAAA,OAAM,mBAAmB,EAAED,oBAAoB,CAAC;EAClD;EAEOH,iBAAA,CAAAK,SAAA,CAAAC,YAAY,GAAnB,UAAoBC,KAA6B;IAAjD,IAAAC,KAAA;IACE,OAAOb,EAAE,CAACc,IAAI,CAAC;MAAM,OAAAd,EAAE,CAACe,OAAO,CAACF,KAAI,CAACG,MAAM,CAACJ,KAAK,CAAC,CAAC;IAA9B,CAA8B,CAAC;EACtD,CAAC;EAEYP,iBAAA,CAAAK,SAAA,CAAAO,OAAO,GAApB,UAAqBL,KAAgB;;;;;;YAC5BM,EAAA,OAAI,CAACP,YAAY;YAAC,qBAAMV,UAAU,CAACW,KAAK,CAAC;;YAAhD,sBAAOM,EAAA,CAAAC,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAEYhB,iBAAA,CAAAK,SAAA,CAAAY,kBAAkB,GAA/B,UAAgCV,KAAgB;;;;;;;YAC7B,qBAAMX,UAAU,CAACW,KAAK,CAAC;;YAAlCW,QAAQ,GAAGL,EAAA,CAAAG,IAAA,EAAuB;YAC5B,qBAAM,IAAI,CAACV,YAAY,CAACY,QAAQ,CAAC;;YAAvCC,GAAG,GAAGN,EAAA,CAAAG,IAAA,EAAiC;YACjB,qBAAMI,OAAO,CAACC,GAAG,CAAC1B,EAAE,CAAC2B,OAAO,CAACH,GAAG,CAAC,CAACI,GAAG,CAAC,UAAMC,CAAC;cAAA,OAAAC,SAAA,CAAAjB,KAAA;;;;;sBAC1D,qBAAMgB,CAAC,CAACE,IAAI,EAAE;;sBAArBA,IAAI,GAAGb,EAAA,CAAAG,IAAA,EAAc;sBAC3BQ,CAAC,CAACG,OAAO,EAAE;sBACX,sBAAOD,IAAI;;;;aACZ,CAAC,CAAC;;YAJGE,mBAAmB,GAAGf,EAAA,CAAAG,IAAA,EAIzB;YACHG,GAAG,CAACQ,OAAO,EAAE;YAEPE,kBAAkB,GAAGD,mBAAmB,CAC3CL,GAAG,CAAC,UAAAO,YAAY;cAAI,WAAI/B,eAAe,CAAC+B,YAA4B,CAAC;YAAjD,CAAiD,CAAC;YAEzE,sBAAOZ,QAAQ,CAACa,YAAY,GACxBF,kBAAkB,GAClBA,kBAAkB,CAAC,CAAC,CAAC;;;;GAC1B;EAES7B,iBAAA,CAAAK,SAAA,CAAA2B,mBAAmB,GAA7B;IACE,OAAO,uBAAuB;EAChC,CAAC;EAEShC,iBAAA,CAAAK,SAAA,CAAA4B,uBAAuB,GAAjC;IACE,OAAO,GAAG;EACZ,CAAC;EAESjC,iBAAA,CAAAK,SAAA,CAAA6B,wBAAwB,GAAlC;IACE,OAAO,CAAC;EACV,CAAC;EACH,OAAAlC,iBAAC;AAAD,CAAC,CA3CsCF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}