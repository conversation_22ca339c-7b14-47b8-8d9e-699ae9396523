{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { SeparableConvParams } from './types';\nexport function extractSeparableConvParamsFactory(extractWeights, paramMappings) {\n  return function (channelsIn, channelsOut, mappedPrefix) {\n    var depthwise_filter = tf.tensor4d(extractWeights(3 * 3 * channelsIn), [3, 3, channelsIn, 1]);\n    var pointwise_filter = tf.tensor4d(extractWeights(channelsIn * channelsOut), [1, 1, channelsIn, channelsOut]);\n    var bias = tf.tensor1d(extractWeights(channelsOut));\n    paramMappings.push({\n      paramPath: mappedPrefix + \"/depthwise_filter\"\n    }, {\n      paramPath: mappedPrefix + \"/pointwise_filter\"\n    }, {\n      paramPath: mappedPrefix + \"/bias\"\n    });\n    return new SeparableConvParams(depthwise_filter, pointwise_filter, bias);\n  };\n}\nexport function loadSeparableConvParamsFactory(extractWeightEntry) {\n  return function (prefix) {\n    var depthwise_filter = extractWeightEntry(prefix + \"/depthwise_filter\", 4);\n    var pointwise_filter = extractWeightEntry(prefix + \"/pointwise_filter\", 4);\n    var bias = extractWeightEntry(prefix + \"/bias\", 1);\n    return new SeparableConvParams(depthwise_filter, pointwise_filter, bias);\n  };\n}", "map": {"version": 3, "names": ["tf", "SeparableConvParams", "extractSeparableConvParamsFactory", "extractWeights", "paramMappings", "channelsIn", "channelsOut", "mappedPrefix", "depthwise_filter", "tensor4d", "pointwise_filter", "bias", "tensor1d", "push", "<PERSON><PERSON><PERSON><PERSON>", "loadSeparableConvParamsFactory", "extractWeightEntry", "prefix"], "sources": ["../../../src/common/extractSeparableConvParamsFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAA+CC,mBAAmB,QAAQ,SAAS;AAEnF,OAAM,SAAUC,iCAAiCA,CAC/CC,cAAsC,EACtCC,aAA6B;EAG7B,OAAO,UAASC,UAAkB,EAAEC,WAAmB,EAAEC,YAAoB;IAC3E,IAAMC,gBAAgB,GAAGR,EAAE,CAACS,QAAQ,CAACN,cAAc,CAAC,CAAC,GAAG,CAAC,GAAGE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEA,UAAU,EAAE,CAAC,CAAC,CAAC;IAC/F,IAAMK,gBAAgB,GAAGV,EAAE,CAACS,QAAQ,CAACN,cAAc,CAACE,UAAU,GAAGC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAED,UAAU,EAAEC,WAAW,CAAC,CAAC;IAC/G,IAAMK,IAAI,GAAGX,EAAE,CAACY,QAAQ,CAACT,cAAc,CAACG,WAAW,CAAC,CAAC;IAErDF,aAAa,CAACS,IAAI,CAChB;MAAEC,SAAS,EAAKP,YAAY;IAAmB,CAAE,EACjD;MAAEO,SAAS,EAAKP,YAAY;IAAmB,CAAE,EACjD;MAAEO,SAAS,EAAKP,YAAY;IAAO,CAAE,CACtC;IAED,OAAO,IAAIN,mBAAmB,CAC5BO,gBAAgB,EAChBE,gBAAgB,EAChBC,IAAI,CACL;EACH,CAAC;AAEH;AAEA,OAAM,SAAUI,8BAA8BA,CAC5CC,kBAAqE;EAGrE,OAAO,UAAUC,MAAc;IAC7B,IAAMT,gBAAgB,GAAGQ,kBAAkB,CAAiBC,MAAM,sBAAmB,EAAE,CAAC,CAAC;IACzF,IAAMP,gBAAgB,GAAGM,kBAAkB,CAAiBC,MAAM,sBAAmB,EAAE,CAAC,CAAC;IACzF,IAAMN,IAAI,GAAGK,kBAAkB,CAAiBC,MAAM,UAAO,EAAE,CAAC,CAAC;IAEjE,OAAO,IAAIhB,mBAAmB,CAC5BO,gBAAgB,EAChBE,gBAAgB,EAChBC,IAAI,CACL;EACH,CAAC;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}