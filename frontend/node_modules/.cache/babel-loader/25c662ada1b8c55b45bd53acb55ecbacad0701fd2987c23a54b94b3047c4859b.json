{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Container, Paper, TextField, Button, Typography, Box, Alert, CircularProgress, Card, CardContent, Divider, Stack, IconButton, InputAdornment } from '@mui/material';\nimport { Visibility, VisibilityOff, School as SchoolIcon, Face as FaceIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const {\n    signIn,\n    isAuthenticated,\n    getRoleBasedRoute\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Rediriger si déjà connecté\n  useEffect(() => {\n    if (isAuthenticated) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || getRoleBasedRoute();\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate, location, getRoleBasedRoute]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError(null);\n    setLoading(true);\n    try {\n      console.log('🔐 Tentative de connexion avec:', email);\n      const result = await signIn(email, password);\n      console.log('📊 Résultat de la connexion:', result);\n      if (result.error) {\n        console.error('❌ Erreur de connexion:', result.error);\n        setError(result.error);\n      } else if (result.user) {\n        console.log('✅ Utilisateur connecté:', result.user);\n        // Redirection automatique vers le dashboard approprié\n        const targetRoute = getRoleBasedRoute();\n        console.log('🎯 Redirection vers:', targetRoute);\n        navigate(targetRoute, {\n          replace: true\n        });\n      } else {\n        console.warn('⚠️ Aucun utilisateur retourné');\n        setError('Erreur de connexion - aucun utilisateur retourné');\n      }\n    } catch (err) {\n      console.error('❌ Exception lors de la connexion:', err);\n      setError('Erreur de connexion. Veuillez réessayer.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDemoLogin = async role => {\n    setError(null);\n    setLoading(true);\n\n    // Comptes de démonstration\n    const demoAccounts = {\n      admin: {\n        email: '<EMAIL>',\n        password: 'admin123'\n      },\n      teacher: {\n        email: '<EMAIL>',\n        password: 'teacher123'\n      },\n      student: {\n        email: '<EMAIL>',\n        password: 'student123'\n      }\n    };\n    const account = demoAccounts[role];\n    try {\n      const result = await signIn(account.email, account.password);\n      if (result.error) {\n        setError(result.error);\n      } else if (result.user) {\n        const targetRoute = getRoleBasedRoute();\n        navigate(targetRoute, {\n          replace: true\n        });\n      }\n    } catch (err) {\n      setError('Erreur de connexion avec le compte de démonstration.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"sm\",\n    sx: {\n      py: 8\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(FaceIcon, {\n          sx: {\n            fontSize: 40,\n            color: 'primary.main',\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SchoolIcon, {\n          sx: {\n            fontSize: 40,\n            color: 'secondary.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        component: \"h1\",\n        gutterBottom: true,\n        align: \"center\",\n        children: \"PresencePro\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        align: \"center\",\n        children: \"Syst\\xE8me de Gestion de Pr\\xE9sence par Reconnaissance Faciale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h2\",\n        gutterBottom: true,\n        align: \"center\",\n        children: \"Connexion\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"form\",\n        onSubmit: handleSubmit,\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Email\",\n          type: \"email\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          margin: \"normal\",\n          required: true,\n          autoComplete: \"email\",\n          autoFocus: true,\n          disabled: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Mot de passe\",\n          type: showPassword ? 'text' : 'password',\n          value: password,\n          onChange: e => setPassword(e.target.value),\n          margin: \"normal\",\n          required: true,\n          autoComplete: \"current-password\",\n          disabled: loading,\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setShowPassword(!showPassword),\n                edge: \"end\",\n                children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 37\n                }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 57\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          fullWidth: true,\n          variant: \"contained\",\n          sx: {\n            mt: 3,\n            mb: 2,\n            py: 1.5\n          },\n          disabled: loading || !email || !password,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 24\n          }, this) : 'Se connecter'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Comptes de d\\xE9monstration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              py: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"error.main\",\n                  children: \"Administrateur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Acc\\xE8s complet au syst\\xE8me\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                onClick: () => handleDemoLogin('admin'),\n                disabled: loading,\n                size: \"small\",\n                children: \"Connexion Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              py: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"primary.main\",\n                  children: \"Professeur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Gestion des cours et pr\\xE9sences\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"primary\",\n                onClick: () => handleDemoLogin('teacher'),\n                disabled: loading,\n                size: \"small\",\n                children: \"Connexion Prof\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              py: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"secondary.main\",\n                  children: \"\\xC9tudiant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Consultation des pr\\xE9sences\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"secondary\",\n                onClick: () => handleDemoLogin('student'),\n                disabled: loading,\n                size: \"small\",\n                children: \"Connexion \\xC9tudiant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 4,\n        textAlign: \"center\",\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Utilisez les comptes de d\\xE9monstration ci-dessus pour tester les diff\\xE9rents r\\xF4les\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"ys6FUcX+dxx7eQ8KF3zrbh3CVGg=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "Container", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "<PERSON><PERSON>", "IconButton", "InputAdornment", "Visibility", "VisibilityOff", "School", "SchoolIcon", "Face", "FaceIcon", "useAuth", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "error", "setError", "loading", "setLoading", "signIn", "isAuthenticated", "getRoleBasedRoute", "navigate", "location", "_location$state", "_location$state$from", "from", "state", "pathname", "replace", "handleSubmit", "e", "preventDefault", "console", "log", "result", "user", "targetRoute", "warn", "err", "handleDemoLogin", "role", "demoAccounts", "admin", "teacher", "student", "account", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "display", "flexDirection", "alignItems", "mb", "fontSize", "color", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "gutterBottom", "align", "elevation", "p", "severity", "onSubmit", "mt", "fullWidth", "label", "type", "value", "onChange", "target", "margin", "required", "autoComplete", "autoFocus", "disabled", "InputProps", "endAdornment", "position", "onClick", "edge", "size", "my", "spacing", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Container,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Box,\n  Alert,\n  CircularProgress,\n  Card,\n  CardContent,\n  Divider,\n  Stack,\n  IconButton,\n  InputAdornment\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  School as SchoolIcon,\n  Face as FaceIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst LoginPage: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n\n  const { signIn, isAuthenticated, getRoleBasedRoute } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Rediriger si déjà connecté\n  useEffect(() => {\n    if (isAuthenticated) {\n      const from = (location.state as any)?.from?.pathname || getRoleBasedRoute();\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, location, getRoleBasedRoute]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError(null);\n    setLoading(true);\n\n    try {\n      console.log('🔐 Tentative de connexion avec:', email);\n      const result = await signIn(email, password);\n      console.log('📊 Résultat de la connexion:', result);\n\n      if (result.error) {\n        console.error('❌ Erreur de connexion:', result.error);\n        setError(result.error);\n      } else if (result.user) {\n        console.log('✅ Utilisateur connecté:', result.user);\n        // Redirection automatique vers le dashboard approprié\n        const targetRoute = getRoleBasedRoute();\n        console.log('🎯 Redirection vers:', targetRoute);\n        navigate(targetRoute, { replace: true });\n      } else {\n        console.warn('⚠️ Aucun utilisateur retourné');\n        setError('Erreur de connexion - aucun utilisateur retourné');\n      }\n    } catch (err) {\n      console.error('❌ Exception lors de la connexion:', err);\n      setError('Erreur de connexion. Veuillez réessayer.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoLogin = async (role: 'admin' | 'teacher' | 'student') => {\n    setError(null);\n    setLoading(true);\n\n    // Comptes de démonstration\n    const demoAccounts = {\n      admin: { email: '<EMAIL>', password: 'admin123' },\n      teacher: { email: '<EMAIL>', password: 'teacher123' },\n      student: { email: '<EMAIL>', password: 'student123' }\n    };\n\n    const account = demoAccounts[role];\n\n    try {\n      const result = await signIn(account.email, account.password);\n\n      if (result.error) {\n        setError(result.error);\n      } else if (result.user) {\n        const targetRoute = getRoleBasedRoute();\n        navigate(targetRoute, { replace: true });\n      }\n    } catch (err) {\n      setError('Erreur de connexion avec le compte de démonstration.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container maxWidth=\"sm\" sx={{ py: 8 }}>\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" mb={4}>\n        <Box display=\"flex\" alignItems=\"center\" mb={2}>\n          <FaceIcon sx={{ fontSize: 40, color: 'primary.main', mr: 1 }} />\n          <SchoolIcon sx={{ fontSize: 40, color: 'secondary.main' }} />\n        </Box>\n        <Typography variant=\"h3\" component=\"h1\" gutterBottom align=\"center\">\n          PresencePro\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\" align=\"center\">\n          Système de Gestion de Présence par Reconnaissance Faciale\n        </Typography>\n      </Box>\n\n      <Paper elevation={3} sx={{ p: 4 }}>\n        <Typography variant=\"h4\" component=\"h2\" gutterBottom align=\"center\">\n          Connexion\n        </Typography>\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 3 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 2 }}>\n          <TextField\n            fullWidth\n            label=\"Email\"\n            type=\"email\"\n            value={email}\n            onChange={(e) => setEmail(e.target.value)}\n            margin=\"normal\"\n            required\n            autoComplete=\"email\"\n            autoFocus\n            disabled={loading}\n          />\n\n          <TextField\n            fullWidth\n            label=\"Mot de passe\"\n            type={showPassword ? 'text' : 'password'}\n            value={password}\n            onChange={(e) => setPassword(e.target.value)}\n            margin=\"normal\"\n            required\n            autoComplete=\"current-password\"\n            disabled={loading}\n            InputProps={{\n              endAdornment: (\n                <InputAdornment position=\"end\">\n                  <IconButton\n                    onClick={() => setShowPassword(!showPassword)}\n                    edge=\"end\"\n                  >\n                    {showPassword ? <VisibilityOff /> : <Visibility />}\n                  </IconButton>\n                </InputAdornment>\n              ),\n            }}\n          />\n\n          <Button\n            type=\"submit\"\n            fullWidth\n            variant=\"contained\"\n            sx={{ mt: 3, mb: 2, py: 1.5 }}\n            disabled={loading || !email || !password}\n          >\n            {loading ? <CircularProgress size={24} /> : 'Se connecter'}\n          </Button>\n        </Box>\n\n        <Divider sx={{ my: 3 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Comptes de démonstration\n          </Typography>\n        </Divider>\n\n        <Stack spacing={2}>\n          <Card variant=\"outlined\">\n            <CardContent sx={{ py: 2 }}>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                <Box>\n                  <Typography variant=\"subtitle2\" color=\"error.main\">\n                    Administrateur\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Accès complet au système\n                  </Typography>\n                </Box>\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  onClick={() => handleDemoLogin('admin')}\n                  disabled={loading}\n                  size=\"small\"\n                >\n                  Connexion Admin\n                </Button>\n              </Box>\n            </CardContent>\n          </Card>\n\n          <Card variant=\"outlined\">\n            <CardContent sx={{ py: 2 }}>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                <Box>\n                  <Typography variant=\"subtitle2\" color=\"primary.main\">\n                    Professeur\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Gestion des cours et présences\n                  </Typography>\n                </Box>\n                <Button\n                  variant=\"outlined\"\n                  color=\"primary\"\n                  onClick={() => handleDemoLogin('teacher')}\n                  disabled={loading}\n                  size=\"small\"\n                >\n                  Connexion Prof\n                </Button>\n              </Box>\n            </CardContent>\n          </Card>\n\n          <Card variant=\"outlined\">\n            <CardContent sx={{ py: 2 }}>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                <Box>\n                  <Typography variant=\"subtitle2\" color=\"secondary.main\">\n                    Étudiant\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Consultation des présences\n                  </Typography>\n                </Box>\n                <Button\n                  variant=\"outlined\"\n                  color=\"secondary\"\n                  onClick={() => handleDemoLogin('student')}\n                  disabled={loading}\n                  size=\"small\"\n                >\n                  Connexion Étudiant\n                </Button>\n              </Box>\n            </CardContent>\n          </Card>\n        </Stack>\n\n        <Box mt={4} textAlign=\"center\">\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Utilisez les comptes de démonstration ci-dessus pour tester les différents rôles\n          </Typography>\n        </Box>\n      </Paper>\n    </Container>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,cAAc,QACT,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEuC,MAAM;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAChE,MAAMkB,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,IAAIuC,eAAe,EAAE;MAAA,IAAAI,eAAA,EAAAC,oBAAA;MACnB,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAACD,QAAQ,CAACI,KAAK,cAAAH,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBE,IAAI,cAAAD,oBAAA,uBAA7BA,oBAAA,CAA+BG,QAAQ,KAAIP,iBAAiB,CAAC,CAAC;MAC3EC,QAAQ,CAACI,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACT,eAAe,EAAEE,QAAQ,EAAEC,QAAQ,EAAEF,iBAAiB,CAAC,CAAC;EAE5D,MAAMS,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBhB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACFe,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEzB,KAAK,CAAC;MACrD,MAAM0B,MAAM,GAAG,MAAMhB,MAAM,CAACV,KAAK,EAAEE,QAAQ,CAAC;MAC5CsB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,MAAM,CAAC;MAEnD,IAAIA,MAAM,CAACpB,KAAK,EAAE;QAChBkB,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEoB,MAAM,CAACpB,KAAK,CAAC;QACrDC,QAAQ,CAACmB,MAAM,CAACpB,KAAK,CAAC;MACxB,CAAC,MAAM,IAAIoB,MAAM,CAACC,IAAI,EAAE;QACtBH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,MAAM,CAACC,IAAI,CAAC;QACnD;QACA,MAAMC,WAAW,GAAGhB,iBAAiB,CAAC,CAAC;QACvCY,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEG,WAAW,CAAC;QAChDf,QAAQ,CAACe,WAAW,EAAE;UAAER,OAAO,EAAE;QAAK,CAAC,CAAC;MAC1C,CAAC,MAAM;QACLI,OAAO,CAACK,IAAI,CAAC,+BAA+B,CAAC;QAC7CtB,QAAQ,CAAC,kDAAkD,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZN,OAAO,CAAClB,KAAK,CAAC,mCAAmC,EAAEwB,GAAG,CAAC;MACvDvB,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,eAAe,GAAG,MAAOC,IAAqC,IAAK;IACvEzB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAMwB,YAAY,GAAG;MACnBC,KAAK,EAAE;QAAElC,KAAK,EAAE,uBAAuB;QAAEE,QAAQ,EAAE;MAAW,CAAC;MAC/DiC,OAAO,EAAE;QAAEnC,KAAK,EAAE,wBAAwB;QAAEE,QAAQ,EAAE;MAAa,CAAC;MACpEkC,OAAO,EAAE;QAAEpC,KAAK,EAAE,2BAA2B;QAAEE,QAAQ,EAAE;MAAa;IACxE,CAAC;IAED,MAAMmC,OAAO,GAAGJ,YAAY,CAACD,IAAI,CAAC;IAElC,IAAI;MACF,MAAMN,MAAM,GAAG,MAAMhB,MAAM,CAAC2B,OAAO,CAACrC,KAAK,EAAEqC,OAAO,CAACnC,QAAQ,CAAC;MAE5D,IAAIwB,MAAM,CAACpB,KAAK,EAAE;QAChBC,QAAQ,CAACmB,MAAM,CAACpB,KAAK,CAAC;MACxB,CAAC,MAAM,IAAIoB,MAAM,CAACC,IAAI,EAAE;QACtB,MAAMC,WAAW,GAAGhB,iBAAiB,CAAC,CAAC;QACvCC,QAAQ,CAACe,WAAW,EAAE;UAAER,OAAO,EAAE;QAAK,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZvB,QAAQ,CAAC,sDAAsD,CAAC;IAClE,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEZ,OAAA,CAACtB,SAAS;IAAC+D,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACrC5C,OAAA,CAACjB,GAAG;MAAC8D,OAAO,EAAC,MAAM;MAACC,aAAa,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAJ,QAAA,gBACnE5C,OAAA,CAACjB,GAAG;QAAC8D,OAAO,EAAC,MAAM;QAACE,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAJ,QAAA,gBAC5C5C,OAAA,CAACH,QAAQ;UAAC6C,EAAE,EAAE;YAAEO,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEvD,OAAA,CAACL,UAAU;UAAC+C,EAAE,EAAE;YAAEO,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAiB;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNvD,OAAA,CAAClB,UAAU;QAAC0E,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACC,KAAK,EAAC,QAAQ;QAAAf,QAAA,EAAC;MAEpE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvD,OAAA,CAAClB,UAAU;QAAC0E,OAAO,EAAC,IAAI;QAACN,KAAK,EAAC,gBAAgB;QAACS,KAAK,EAAC,QAAQ;QAAAf,QAAA,EAAC;MAE/D;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENvD,OAAA,CAACrB,KAAK;MAACiF,SAAS,EAAE,CAAE;MAAClB,EAAE,EAAE;QAAEmB,CAAC,EAAE;MAAE,CAAE;MAAAjB,QAAA,gBAChC5C,OAAA,CAAClB,UAAU;QAAC0E,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACC,KAAK,EAAC,QAAQ;QAAAf,QAAA,EAAC;MAEpE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ9C,KAAK,iBACJT,OAAA,CAAChB,KAAK;QAAC8E,QAAQ,EAAC,OAAO;QAACpB,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCnC;MAAK;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDvD,OAAA,CAACjB,GAAG;QAAC0E,SAAS,EAAC,MAAM;QAACM,QAAQ,EAAEvC,YAAa;QAACkB,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBAC1D5C,OAAA,CAACpB,SAAS;UACRqF,SAAS;UACTC,KAAK,EAAC,OAAO;UACbC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEjE,KAAM;UACbkE,QAAQ,EAAG5C,CAAC,IAAKrB,QAAQ,CAACqB,CAAC,CAAC6C,MAAM,CAACF,KAAK,CAAE;UAC1CG,MAAM,EAAC,QAAQ;UACfC,QAAQ;UACRC,YAAY,EAAC,OAAO;UACpBC,SAAS;UACTC,QAAQ,EAAEhE;QAAQ;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAEFvD,OAAA,CAACpB,SAAS;UACRqF,SAAS;UACTC,KAAK,EAAC,cAAc;UACpBC,IAAI,EAAE5D,YAAY,GAAG,MAAM,GAAG,UAAW;UACzC6D,KAAK,EAAE/D,QAAS;UAChBgE,QAAQ,EAAG5C,CAAC,IAAKnB,WAAW,CAACmB,CAAC,CAAC6C,MAAM,CAACF,KAAK,CAAE;UAC7CG,MAAM,EAAC,QAAQ;UACfC,QAAQ;UACRC,YAAY,EAAC,kBAAkB;UAC/BE,QAAQ,EAAEhE,OAAQ;UAClBiE,UAAU,EAAE;YACVC,YAAY,eACV7E,OAAA,CAACT,cAAc;cAACuF,QAAQ,EAAC,KAAK;cAAAlC,QAAA,eAC5B5C,OAAA,CAACV,UAAU;gBACTyF,OAAO,EAAEA,CAAA,KAAMvE,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9CyE,IAAI,EAAC,KAAK;gBAAApC,QAAA,EAETrC,YAAY,gBAAGP,OAAA,CAACP,aAAa;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACR,UAAU;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFvD,OAAA,CAACnB,MAAM;UACLsF,IAAI,EAAC,QAAQ;UACbF,SAAS;UACTT,OAAO,EAAC,WAAW;UACnBd,EAAE,EAAE;YAAEsB,EAAE,EAAE,CAAC;YAAEhB,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAE;UAAI,CAAE;UAC9BgC,QAAQ,EAAEhE,OAAO,IAAI,CAACR,KAAK,IAAI,CAACE,QAAS;UAAAuC,QAAA,EAExCjC,OAAO,gBAAGX,OAAA,CAACf,gBAAgB;YAACgG,IAAI,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAc;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvD,OAAA,CAACZ,OAAO;QAACsD,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAAtC,QAAA,eACrB5C,OAAA,CAAClB,UAAU;UAAC0E,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,gBAAgB;UAAAN,QAAA,EAAC;QAEnD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEVvD,OAAA,CAACX,KAAK;QAAC8F,OAAO,EAAE,CAAE;QAAAvC,QAAA,gBAChB5C,OAAA,CAACd,IAAI;UAACsE,OAAO,EAAC,UAAU;UAAAZ,QAAA,eACtB5C,OAAA,CAACb,WAAW;YAACuD,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,eACzB5C,OAAA,CAACjB,GAAG;cAAC8D,OAAO,EAAC,MAAM;cAACuC,cAAc,EAAC,eAAe;cAACrC,UAAU,EAAC,QAAQ;cAAAH,QAAA,gBACpE5C,OAAA,CAACjB,GAAG;gBAAA6D,QAAA,gBACF5C,OAAA,CAAClB,UAAU;kBAAC0E,OAAO,EAAC,WAAW;kBAACN,KAAK,EAAC,YAAY;kBAAAN,QAAA,EAAC;gBAEnD;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAAClB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,EAAC;gBAEnD;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvD,OAAA,CAACnB,MAAM;gBACL2E,OAAO,EAAC,UAAU;gBAClBN,KAAK,EAAC,OAAO;gBACb6B,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,OAAO,CAAE;gBACxCyC,QAAQ,EAAEhE,OAAQ;gBAClBsE,IAAI,EAAC,OAAO;gBAAArC,QAAA,EACb;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPvD,OAAA,CAACd,IAAI;UAACsE,OAAO,EAAC,UAAU;UAAAZ,QAAA,eACtB5C,OAAA,CAACb,WAAW;YAACuD,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,eACzB5C,OAAA,CAACjB,GAAG;cAAC8D,OAAO,EAAC,MAAM;cAACuC,cAAc,EAAC,eAAe;cAACrC,UAAU,EAAC,QAAQ;cAAAH,QAAA,gBACpE5C,OAAA,CAACjB,GAAG;gBAAA6D,QAAA,gBACF5C,OAAA,CAAClB,UAAU;kBAAC0E,OAAO,EAAC,WAAW;kBAACN,KAAK,EAAC,cAAc;kBAAAN,QAAA,EAAC;gBAErD;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAAClB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,EAAC;gBAEnD;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvD,OAAA,CAACnB,MAAM;gBACL2E,OAAO,EAAC,UAAU;gBAClBN,KAAK,EAAC,SAAS;gBACf6B,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,SAAS,CAAE;gBAC1CyC,QAAQ,EAAEhE,OAAQ;gBAClBsE,IAAI,EAAC,OAAO;gBAAArC,QAAA,EACb;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPvD,OAAA,CAACd,IAAI;UAACsE,OAAO,EAAC,UAAU;UAAAZ,QAAA,eACtB5C,OAAA,CAACb,WAAW;YAACuD,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,eACzB5C,OAAA,CAACjB,GAAG;cAAC8D,OAAO,EAAC,MAAM;cAACuC,cAAc,EAAC,eAAe;cAACrC,UAAU,EAAC,QAAQ;cAAAH,QAAA,gBACpE5C,OAAA,CAACjB,GAAG;gBAAA6D,QAAA,gBACF5C,OAAA,CAAClB,UAAU;kBAAC0E,OAAO,EAAC,WAAW;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,EAAC;gBAEvD;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAAClB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,EAAC;gBAEnD;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvD,OAAA,CAACnB,MAAM;gBACL2E,OAAO,EAAC,UAAU;gBAClBN,KAAK,EAAC,WAAW;gBACjB6B,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,SAAS,CAAE;gBAC1CyC,QAAQ,EAAEhE,OAAQ;gBAClBsE,IAAI,EAAC,OAAO;gBAAArC,QAAA,EACb;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERvD,OAAA,CAACjB,GAAG;QAACiF,EAAE,EAAE,CAAE;QAACqB,SAAS,EAAC,QAAQ;QAAAzC,QAAA,eAC5B5C,OAAA,CAAClB,UAAU;UAAC0E,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,gBAAgB;UAAAN,QAAA,EAAC;QAEnD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACrD,EAAA,CAlPID,SAAmB;EAAA,QAOgCH,OAAO,EAC7CtB,WAAW,EACXC,WAAW;AAAA;AAAA6G,EAAA,GATxBrF,SAAmB;AAoPzB,eAAeA,SAAS;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}