{"ast": null, "code": "/**\n * Service de reconnaissance faciale pour PresencePro\n * Utilise face-api.js pour la détection et reconnaissance faciale\n */\n\nimport * as faceapi from 'face-api.js';\nimport { supabase } from '../config/supabase';\nclass FaceRecognitionService {\n  constructor() {\n    this.isInitialized = false;\n    this.labeledDescriptors = [];\n  }\n  /**\n   * Initialise les modèles de reconnaissance faciale\n   */\n  async initialize() {\n    if (this.isInitialized) return;\n    try {\n      console.log('Chargement des modèles de reconnaissance faciale...');\n\n      // Charger les modèles depuis un CDN\n      const MODEL_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';\n      await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL), faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL), faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL), faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL), faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL)]);\n      this.isInitialized = true;\n      console.log('✅ Modèles de reconnaissance faciale chargés');\n\n      // Charger les encodages existants\n      await this.loadFaceEncodings();\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des modèles:', error);\n      throw new Error('Impossible de charger les modèles de reconnaissance faciale');\n    }\n  }\n\n  /**\n   * Charge tous les encodages faciaux depuis Supabase\n   */\n  async loadFaceEncodings() {\n    try {\n      const {\n        data: encodings,\n        error\n      } = await supabase.from('face_encodings').select(`\n          *,\n          user:user_id(id, username, email, first_name, last_name)\n        `).eq('is_active', true);\n      if (error) throw error;\n      this.labeledDescriptors = [];\n\n      // Grouper les encodages par utilisateur\n      const userEncodings = new Map();\n      encodings === null || encodings === void 0 ? void 0 : encodings.forEach(encoding => {\n        const userId = encoding.user.id;\n        if (!userEncodings.has(userId)) {\n          userEncodings.set(userId, {\n            user: encoding.user,\n            descriptors: []\n          });\n        }\n\n        // Convertir l'encodage en Float32Array\n        const descriptor = new Float32Array(encoding.encoding);\n        userEncodings.get(userId).descriptors.push(descriptor);\n      });\n\n      // Créer les LabeledFaceDescriptors\n      userEncodings.forEach(({\n        user,\n        descriptors\n      }, userId) => {\n        if (descriptors.length > 0) {\n          const label = `${user.firstName} ${user.lastName} (${user.username})`;\n          this.labeledDescriptors.push(new faceapi.LabeledFaceDescriptors(label, descriptors));\n        }\n      });\n      console.log(`✅ ${this.labeledDescriptors.length} utilisateurs chargés pour la reconnaissance`);\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des encodages:', error);\n    }\n  }\n\n  /**\n   * Détecte et reconnaît les visages dans une image\n   */\n  async detectFaces(imageElement) {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n    try {\n      // Détecter les visages avec landmarks et descripteurs\n      const detections = await faceapi.detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks().withFaceDescriptors();\n      if (detections.length === 0) {\n        return {\n          success: true,\n          detectedUsers: [],\n          message: 'Aucun visage détecté'\n        };\n      }\n\n      // Créer un matcher pour la reconnaissance\n      const faceMatcher = new faceapi.FaceMatcher(this.labeledDescriptors, 0.6);\n      const detectedUsers = detections.map(detection => {\n        const bestMatch = faceMatcher.findBestMatch(detection.descriptor);\n\n        // Extraire les informations utilisateur du label\n        const isMatch = bestMatch.distance < 0.6;\n        const label = isMatch ? bestMatch.label : 'Inconnu';\n        let user = null;\n        if (isMatch && label !== 'unknown') {\n          // Parser le label pour extraire les infos utilisateur\n          const match = label.match(/^(.+) \\((.+)\\)$/);\n          if (match) {\n            const [, fullName, username] = match;\n            const [firstName, ...lastNameParts] = fullName.split(' ');\n            user = {\n              id: username,\n              // Temporaire, sera résolu plus tard\n              username,\n              firstName,\n              lastName: lastNameParts.join(' '),\n              fullName,\n              email: '',\n              // Sera résolu plus tard\n              role: 'student',\n              roleDisplay: 'Étudiant',\n              isActive: true,\n              dateJoined: new Date().toISOString()\n            };\n          }\n        }\n        return {\n          user: user,\n          confidence: Math.round((1 - bestMatch.distance) * 100) / 100,\n          boundingBox: {\n            x: detection.detection.box.x,\n            y: detection.detection.box.y,\n            width: detection.detection.box.width,\n            height: detection.detection.box.height\n          }\n        };\n      }).filter(result => result.user !== null);\n      return {\n        success: true,\n        detectedUsers,\n        message: `${detectedUsers.length} utilisateur(s) reconnu(s)`\n      };\n    } catch (error) {\n      console.error('❌ Erreur lors de la détection:', error);\n      return {\n        success: false,\n        detectedUsers: [],\n        message: `Erreur de détection: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      };\n    }\n  }\n\n  /**\n   * Enregistre un nouvel encodage facial pour un utilisateur\n   */\n  async registerFaceEncoding(userId, imageElement, imageFile) {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n    try {\n      // Détecter le visage dans l'image\n      const detection = await faceapi.detectSingleFace(imageElement, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks().withFaceDescriptor();\n      if (!detection) {\n        return {\n          success: false,\n          message: 'Aucun visage détecté dans l\\'image'\n        };\n      }\n\n      // Upload de l'image si fournie\n      let imageUrl;\n      if (imageFile) {\n        try {\n          const fileExt = imageFile.name.split('.').pop();\n          const fileName = `${userId}/face_${Date.now()}.${fileExt}`;\n          const filePath = `face-images/${fileName}`;\n          const {\n            error: uploadError\n          } = await supabase.storage.from('images').upload(filePath, imageFile);\n          if (uploadError) throw uploadError;\n          const {\n            data\n          } = supabase.storage.from('images').getPublicUrl(filePath);\n          imageUrl = data.publicUrl;\n        } catch (uploadError) {\n          console.warn('Erreur upload image:', uploadError);\n        }\n      }\n\n      // Sauvegarder l'encodage en base\n      const {\n        data,\n        error\n      } = await supabase.from('face_encodings').insert([{\n        user_id: userId,\n        encoding: Array.from(detection.descriptor),\n        confidence: 0.9,\n        // Confiance par défaut pour un encodage manuel\n        image_url: imageUrl,\n        is_active: true\n      }]).select().single();\n      if (error) throw error;\n\n      // Recharger les encodages\n      await this.loadFaceEncodings();\n      return {\n        success: true,\n        message: 'Encodage facial enregistré avec succès',\n        encodingId: data.id\n      };\n    } catch (error) {\n      console.error('❌ Erreur lors de l\\'enregistrement:', error);\n      return {\n        success: false,\n        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      };\n    }\n  }\n\n  /**\n   * Supprime tous les encodages d'un utilisateur\n   */\n  async deleteFaceEncodings(userId) {\n    try {\n      const {\n        error\n      } = await supabase.from('face_encodings').update({\n        is_active: false\n      }).eq('user_id', userId);\n      if (error) throw error;\n\n      // Recharger les encodages\n      await this.loadFaceEncodings();\n    } catch (error) {\n      console.error('❌ Erreur lors de la suppression:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Obtient les statistiques de reconnaissance\n   */\n  getStats() {\n    const totalEncodings = this.labeledDescriptors.reduce((sum, descriptor) => sum + descriptor.descriptors.length, 0);\n    return {\n      isInitialized: this.isInitialized,\n      registeredUsers: this.labeledDescriptors.length,\n      totalEncodings\n    };\n  }\n\n  /**\n   * Redimensionne une image pour optimiser la détection\n   */\n  static resizeImage(canvas, maxWidth = 640, maxHeight = 480) {\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    const {\n      width,\n      height\n    } = canvas;\n\n    // Calculer les nouvelles dimensions en gardant le ratio\n    let newWidth = width;\n    let newHeight = height;\n    if (width > maxWidth) {\n      newWidth = maxWidth;\n      newHeight = height * maxWidth / width;\n    }\n    if (newHeight > maxHeight) {\n      newHeight = maxHeight;\n      newWidth = newWidth * maxHeight / newHeight;\n    }\n\n    // Redimensionner si nécessaire\n    if (newWidth !== width || newHeight !== height) {\n      const imageData = ctx.getImageData(0, 0, width, height);\n      canvas.width = newWidth;\n      canvas.height = newHeight;\n      ctx.putImageData(imageData, 0, 0);\n    }\n  }\n}\n\n// Instance singleton\nexport const faceRecognitionService = new FaceRecognitionService();\nexport default faceRecognitionService;", "map": {"version": 3, "names": ["<PERSON>ap<PERSON>", "supabase", "FaceRecognitionService", "constructor", "isInitialized", "labeledDescriptors", "initialize", "console", "log", "MODEL_URL", "Promise", "all", "nets", "tinyFaceDetector", "loadFromUri", "faceLandmark68Net", "faceRecognitionNet", "faceExpressionNet", "ssdMobilenetv1", "loadFaceEncodings", "error", "Error", "data", "encodings", "from", "select", "eq", "userEncodings", "Map", "for<PERSON>ach", "encoding", "userId", "user", "id", "has", "set", "descriptors", "descriptor", "Float32Array", "get", "push", "length", "label", "firstName", "lastName", "username", "LabeledFaceDescriptors", "detectFaces", "imageElement", "detections", "detectAllFaces", "TinyFaceDetectorOptions", "withFaceLandmarks", "withFaceDescriptors", "success", "detectedUsers", "message", "faceMatcher", "FaceMatcher", "map", "detection", "bestMatch", "findBestMatch", "isMatch", "distance", "match", "fullName", "lastNameParts", "split", "join", "email", "role", "roleDisplay", "isActive", "dateJoined", "Date", "toISOString", "confidence", "Math", "round", "boundingBox", "x", "box", "y", "width", "height", "filter", "result", "registerFaceEncoding", "imageFile", "detectSingleFace", "withFaceDescriptor", "imageUrl", "fileExt", "name", "pop", "fileName", "now", "filePath", "uploadError", "storage", "upload", "getPublicUrl", "publicUrl", "warn", "insert", "user_id", "Array", "image_url", "is_active", "single", "encodingId", "deleteFaceEncodings", "update", "getStats", "totalEncodings", "reduce", "sum", "registeredUsers", "resizeImage", "canvas", "max<PERSON><PERSON><PERSON>", "maxHeight", "ctx", "getContext", "newWidth", "newHeight", "imageData", "getImageData", "putImageData", "faceRecognitionService"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/services/faceRecognitionService.ts"], "sourcesContent": ["/**\n * Service de reconnaissance faciale pour PresencePro\n * Utilise face-api.js pour la détection et reconnaissance faciale\n */\n\nimport * as faceapi from 'face-api.js';\nimport { supabase } from '../config/supabase';\nimport { FaceEncoding, FaceDetectionResult, User } from '../types';\n\nclass FaceRecognitionService {\n  private isInitialized = false;\n  private labeledDescriptors: faceapi.LabeledFaceDescriptors[] = [];\n\n  /**\n   * Initialise les modèles de reconnaissance faciale\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n\n    try {\n      console.log('Chargement des modèles de reconnaissance faciale...');\n      \n      // Charger les modèles depuis un CDN\n      const MODEL_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';\n      await Promise.all([\n        faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),\n        faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),\n        faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL),\n        faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL),\n        faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL)\n      ]);\n\n      this.isInitialized = true;\n      console.log('✅ Modèles de reconnaissance faciale chargés');\n      \n      // Charger les encodages existants\n      await this.loadFaceEncodings();\n      \n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des modèles:', error);\n      throw new Error('Impossible de charger les modèles de reconnaissance faciale');\n    }\n  }\n\n  /**\n   * Charge tous les encodages faciaux depuis Supabase\n   */\n  async loadFaceEncodings(): Promise<void> {\n    try {\n      const { data: encodings, error } = await supabase\n        .from('face_encodings')\n        .select(`\n          *,\n          user:user_id(id, username, email, first_name, last_name)\n        `)\n        .eq('is_active', true);\n\n      if (error) throw error;\n\n      this.labeledDescriptors = [];\n\n      // Grouper les encodages par utilisateur\n      const userEncodings = new Map<string, { user: User; descriptors: Float32Array[] }>();\n\n      encodings?.forEach(encoding => {\n        const userId = encoding.user.id;\n        if (!userEncodings.has(userId)) {\n          userEncodings.set(userId, {\n            user: encoding.user,\n            descriptors: []\n          });\n        }\n        \n        // Convertir l'encodage en Float32Array\n        const descriptor = new Float32Array(encoding.encoding);\n        userEncodings.get(userId)!.descriptors.push(descriptor);\n      });\n\n      // Créer les LabeledFaceDescriptors\n      userEncodings.forEach(({ user, descriptors }, userId) => {\n        if (descriptors.length > 0) {\n          const label = `${user.firstName} ${user.lastName} (${user.username})`;\n          this.labeledDescriptors.push(\n            new faceapi.LabeledFaceDescriptors(label, descriptors)\n          );\n        }\n      });\n\n      console.log(`✅ ${this.labeledDescriptors.length} utilisateurs chargés pour la reconnaissance`);\n      \n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des encodages:', error);\n    }\n  }\n\n  /**\n   * Détecte et reconnaît les visages dans une image\n   */\n  async detectFaces(\n    imageElement: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement\n  ): Promise<FaceDetectionResult> {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n\n    try {\n      // Détecter les visages avec landmarks et descripteurs\n      const detections = await faceapi\n        .detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions())\n        .withFaceLandmarks()\n        .withFaceDescriptors();\n\n      if (detections.length === 0) {\n        return {\n          success: true,\n          detectedUsers: [],\n          message: 'Aucun visage détecté'\n        };\n      }\n\n      // Créer un matcher pour la reconnaissance\n      const faceMatcher = new faceapi.FaceMatcher(this.labeledDescriptors, 0.6);\n      \n      const detectedUsers = detections.map(detection => {\n        const bestMatch = faceMatcher.findBestMatch(detection.descriptor);\n        \n        // Extraire les informations utilisateur du label\n        const isMatch = bestMatch.distance < 0.6;\n        const label = isMatch ? bestMatch.label : 'Inconnu';\n        \n        let user: User | null = null;\n        if (isMatch && label !== 'unknown') {\n          // Parser le label pour extraire les infos utilisateur\n          const match = label.match(/^(.+) \\((.+)\\)$/);\n          if (match) {\n            const [, fullName, username] = match;\n            const [firstName, ...lastNameParts] = fullName.split(' ');\n            user = {\n              id: username, // Temporaire, sera résolu plus tard\n              username,\n              firstName,\n              lastName: lastNameParts.join(' '),\n              fullName,\n              email: '', // Sera résolu plus tard\n              role: 'student' as any,\n              roleDisplay: 'Étudiant',\n              isActive: true,\n              dateJoined: new Date().toISOString()\n            };\n          }\n        }\n\n        return {\n          user: user!,\n          confidence: Math.round((1 - bestMatch.distance) * 100) / 100,\n          boundingBox: {\n            x: detection.detection.box.x,\n            y: detection.detection.box.y,\n            width: detection.detection.box.width,\n            height: detection.detection.box.height\n          }\n        };\n      }).filter(result => result.user !== null);\n\n      return {\n        success: true,\n        detectedUsers,\n        message: `${detectedUsers.length} utilisateur(s) reconnu(s)`\n      };\n\n    } catch (error) {\n      console.error('❌ Erreur lors de la détection:', error);\n      return {\n        success: false,\n        detectedUsers: [],\n        message: `Erreur de détection: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      };\n    }\n  }\n\n  /**\n   * Enregistre un nouvel encodage facial pour un utilisateur\n   */\n  async registerFaceEncoding(\n    userId: string,\n    imageElement: HTMLImageElement | HTMLCanvasElement,\n    imageFile?: File\n  ): Promise<{ success: boolean; message: string; encodingId?: string }> {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n\n    try {\n      // Détecter le visage dans l'image\n      const detection = await faceapi\n        .detectSingleFace(imageElement, new faceapi.TinyFaceDetectorOptions())\n        .withFaceLandmarks()\n        .withFaceDescriptor();\n\n      if (!detection) {\n        return {\n          success: false,\n          message: 'Aucun visage détecté dans l\\'image'\n        };\n      }\n\n      // Upload de l'image si fournie\n      let imageUrl: string | undefined;\n      if (imageFile) {\n        try {\n          const fileExt = imageFile.name.split('.').pop();\n          const fileName = `${userId}/face_${Date.now()}.${fileExt}`;\n          const filePath = `face-images/${fileName}`;\n\n          const { error: uploadError } = await supabase.storage\n            .from('images')\n            .upload(filePath, imageFile);\n\n          if (uploadError) throw uploadError;\n\n          const { data } = supabase.storage\n            .from('images')\n            .getPublicUrl(filePath);\n\n          imageUrl = data.publicUrl;\n        } catch (uploadError) {\n          console.warn('Erreur upload image:', uploadError);\n        }\n      }\n\n      // Sauvegarder l'encodage en base\n      const { data, error } = await supabase\n        .from('face_encodings')\n        .insert([{\n          user_id: userId,\n          encoding: Array.from(detection.descriptor),\n          confidence: 0.9, // Confiance par défaut pour un encodage manuel\n          image_url: imageUrl,\n          is_active: true\n        }])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Recharger les encodages\n      await this.loadFaceEncodings();\n\n      return {\n        success: true,\n        message: 'Encodage facial enregistré avec succès',\n        encodingId: data.id\n      };\n\n    } catch (error) {\n      console.error('❌ Erreur lors de l\\'enregistrement:', error);\n      return {\n        success: false,\n        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`\n      };\n    }\n  }\n\n  /**\n   * Supprime tous les encodages d'un utilisateur\n   */\n  async deleteFaceEncodings(userId: string): Promise<void> {\n    try {\n      const { error } = await supabase\n        .from('face_encodings')\n        .update({ is_active: false })\n        .eq('user_id', userId);\n\n      if (error) throw error;\n\n      // Recharger les encodages\n      await this.loadFaceEncodings();\n      \n    } catch (error) {\n      console.error('❌ Erreur lors de la suppression:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Obtient les statistiques de reconnaissance\n   */\n  getStats(): {\n    isInitialized: boolean;\n    registeredUsers: number;\n    totalEncodings: number;\n  } {\n    const totalEncodings = this.labeledDescriptors.reduce(\n      (sum, descriptor) => sum + descriptor.descriptors.length,\n      0\n    );\n\n    return {\n      isInitialized: this.isInitialized,\n      registeredUsers: this.labeledDescriptors.length,\n      totalEncodings\n    };\n  }\n\n  /**\n   * Redimensionne une image pour optimiser la détection\n   */\n  static resizeImage(\n    canvas: HTMLCanvasElement,\n    maxWidth: number = 640,\n    maxHeight: number = 480\n  ): void {\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const { width, height } = canvas;\n    \n    // Calculer les nouvelles dimensions en gardant le ratio\n    let newWidth = width;\n    let newHeight = height;\n    \n    if (width > maxWidth) {\n      newWidth = maxWidth;\n      newHeight = (height * maxWidth) / width;\n    }\n    \n    if (newHeight > maxHeight) {\n      newHeight = maxHeight;\n      newWidth = (newWidth * maxHeight) / newHeight;\n    }\n\n    // Redimensionner si nécessaire\n    if (newWidth !== width || newHeight !== height) {\n      const imageData = ctx.getImageData(0, 0, width, height);\n      canvas.width = newWidth;\n      canvas.height = newHeight;\n      ctx.putImageData(imageData, 0, 0);\n    }\n  }\n}\n\n// Instance singleton\nexport const faceRecognitionService = new FaceRecognitionService();\nexport default faceRecognitionService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,OAAO,MAAM,aAAa;AACtC,SAASC,QAAQ,QAAQ,oBAAoB;AAG7C,MAAMC,sBAAsB,CAAC;EAAAC,YAAA;IAAA,KACnBC,aAAa,GAAG,KAAK;IAAA,KACrBC,kBAAkB,GAAqC,EAAE;EAAA;EAEjE;AACF;AACA;EACE,MAAMC,UAAUA,CAAA,EAAkB;IAChC,IAAI,IAAI,CAACF,aAAa,EAAE;IAExB,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;MAElE;MACA,MAAMC,SAAS,GAAG,gFAAgF;MAClG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChBX,OAAO,CAACY,IAAI,CAACC,gBAAgB,CAACC,WAAW,CAACL,SAAS,CAAC,EACpDT,OAAO,CAACY,IAAI,CAACG,iBAAiB,CAACD,WAAW,CAACL,SAAS,CAAC,EACrDT,OAAO,CAACY,IAAI,CAACI,kBAAkB,CAACF,WAAW,CAACL,SAAS,CAAC,EACtDT,OAAO,CAACY,IAAI,CAACK,iBAAiB,CAACH,WAAW,CAACL,SAAS,CAAC,EACrDT,OAAO,CAACY,IAAI,CAACM,cAAc,CAACJ,WAAW,CAACL,SAAS,CAAC,CACnD,CAAC;MAEF,IAAI,CAACL,aAAa,GAAG,IAAI;MACzBG,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACA,MAAM,IAAI,CAACW,iBAAiB,CAAC,CAAC;IAEhC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC;IAChF;EACF;;EAEA;AACF;AACA;EACE,MAAMF,iBAAiBA,CAAA,EAAkB;IACvC,IAAI;MACF,MAAM;QAAEG,IAAI,EAAEC,SAAS;QAAEH;MAAM,CAAC,GAAG,MAAMnB,QAAQ,CAC9CuB,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC;AAChB;AACA;AACA,SAAS,CAAC,CACDC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC;MAExB,IAAIN,KAAK,EAAE,MAAMA,KAAK;MAEtB,IAAI,CAACf,kBAAkB,GAAG,EAAE;;MAE5B;MACA,MAAMsB,aAAa,GAAG,IAAIC,GAAG,CAAsD,CAAC;MAEpFL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,OAAO,CAACC,QAAQ,IAAI;QAC7B,MAAMC,MAAM,GAAGD,QAAQ,CAACE,IAAI,CAACC,EAAE;QAC/B,IAAI,CAACN,aAAa,CAACO,GAAG,CAACH,MAAM,CAAC,EAAE;UAC9BJ,aAAa,CAACQ,GAAG,CAACJ,MAAM,EAAE;YACxBC,IAAI,EAAEF,QAAQ,CAACE,IAAI;YACnBI,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMC,UAAU,GAAG,IAAIC,YAAY,CAACR,QAAQ,CAACA,QAAQ,CAAC;QACtDH,aAAa,CAACY,GAAG,CAACR,MAAM,CAAC,CAAEK,WAAW,CAACI,IAAI,CAACH,UAAU,CAAC;MACzD,CAAC,CAAC;;MAEF;MACAV,aAAa,CAACE,OAAO,CAAC,CAAC;QAAEG,IAAI;QAAEI;MAAY,CAAC,EAAEL,MAAM,KAAK;QACvD,IAAIK,WAAW,CAACK,MAAM,GAAG,CAAC,EAAE;UAC1B,MAAMC,KAAK,GAAG,GAAGV,IAAI,CAACW,SAAS,IAAIX,IAAI,CAACY,QAAQ,KAAKZ,IAAI,CAACa,QAAQ,GAAG;UACrE,IAAI,CAACxC,kBAAkB,CAACmC,IAAI,CAC1B,IAAIxC,OAAO,CAAC8C,sBAAsB,CAACJ,KAAK,EAAEN,WAAW,CACvD,CAAC;QACH;MACF,CAAC,CAAC;MAEF7B,OAAO,CAACC,GAAG,CAAC,KAAK,IAAI,CAACH,kBAAkB,CAACoC,MAAM,8CAA8C,CAAC;IAEhG,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF;;EAEA;AACF;AACA;EACE,MAAM2B,WAAWA,CACfC,YAAqE,EACvC;IAC9B,IAAI,CAAC,IAAI,CAAC5C,aAAa,EAAE;MACvB,MAAM,IAAI,CAACE,UAAU,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACA,MAAM2C,UAAU,GAAG,MAAMjD,OAAO,CAC7BkD,cAAc,CAACF,YAAY,EAAE,IAAIhD,OAAO,CAACmD,uBAAuB,CAAC,CAAC,CAAC,CACnEC,iBAAiB,CAAC,CAAC,CACnBC,mBAAmB,CAAC,CAAC;MAExB,IAAIJ,UAAU,CAACR,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO;UACLa,OAAO,EAAE,IAAI;UACbC,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE;QACX,CAAC;MACH;;MAEA;MACA,MAAMC,WAAW,GAAG,IAAIzD,OAAO,CAAC0D,WAAW,CAAC,IAAI,CAACrD,kBAAkB,EAAE,GAAG,CAAC;MAEzE,MAAMkD,aAAa,GAAGN,UAAU,CAACU,GAAG,CAACC,SAAS,IAAI;QAChD,MAAMC,SAAS,GAAGJ,WAAW,CAACK,aAAa,CAACF,SAAS,CAACvB,UAAU,CAAC;;QAEjE;QACA,MAAM0B,OAAO,GAAGF,SAAS,CAACG,QAAQ,GAAG,GAAG;QACxC,MAAMtB,KAAK,GAAGqB,OAAO,GAAGF,SAAS,CAACnB,KAAK,GAAG,SAAS;QAEnD,IAAIV,IAAiB,GAAG,IAAI;QAC5B,IAAI+B,OAAO,IAAIrB,KAAK,KAAK,SAAS,EAAE;UAClC;UACA,MAAMuB,KAAK,GAAGvB,KAAK,CAACuB,KAAK,CAAC,iBAAiB,CAAC;UAC5C,IAAIA,KAAK,EAAE;YACT,MAAM,GAAGC,QAAQ,EAAErB,QAAQ,CAAC,GAAGoB,KAAK;YACpC,MAAM,CAACtB,SAAS,EAAE,GAAGwB,aAAa,CAAC,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;YACzDpC,IAAI,GAAG;cACLC,EAAE,EAAEY,QAAQ;cAAE;cACdA,QAAQ;cACRF,SAAS;cACTC,QAAQ,EAAEuB,aAAa,CAACE,IAAI,CAAC,GAAG,CAAC;cACjCH,QAAQ;cACRI,KAAK,EAAE,EAAE;cAAE;cACXC,IAAI,EAAE,SAAgB;cACtBC,WAAW,EAAE,UAAU;cACvBC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC;UACH;QACF;QAEA,OAAO;UACL5C,IAAI,EAAEA,IAAK;UACX6C,UAAU,EAAEC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGlB,SAAS,CAACG,QAAQ,IAAI,GAAG,CAAC,GAAG,GAAG;UAC5DgB,WAAW,EAAE;YACXC,CAAC,EAAErB,SAAS,CAACA,SAAS,CAACsB,GAAG,CAACD,CAAC;YAC5BE,CAAC,EAAEvB,SAAS,CAACA,SAAS,CAACsB,GAAG,CAACC,CAAC;YAC5BC,KAAK,EAAExB,SAAS,CAACA,SAAS,CAACsB,GAAG,CAACE,KAAK;YACpCC,MAAM,EAAEzB,SAAS,CAACA,SAAS,CAACsB,GAAG,CAACG;UAClC;QACF,CAAC;MACH,CAAC,CAAC,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACvD,IAAI,KAAK,IAAI,CAAC;MAEzC,OAAO;QACLsB,OAAO,EAAE,IAAI;QACbC,aAAa;QACbC,OAAO,EAAE,GAAGD,aAAa,CAACd,MAAM;MAClC,CAAC;IAEH,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO;QACLkC,OAAO,EAAE,KAAK;QACdC,aAAa,EAAE,EAAE;QACjBC,OAAO,EAAE,wBAAwBpC,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACoC,OAAO,GAAG,iBAAiB;MAC7F,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMgC,oBAAoBA,CACxBzD,MAAc,EACdiB,YAAkD,EAClDyC,SAAgB,EACqD;IACrE,IAAI,CAAC,IAAI,CAACrF,aAAa,EAAE;MACvB,MAAM,IAAI,CAACE,UAAU,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACA,MAAMsD,SAAS,GAAG,MAAM5D,OAAO,CAC5B0F,gBAAgB,CAAC1C,YAAY,EAAE,IAAIhD,OAAO,CAACmD,uBAAuB,CAAC,CAAC,CAAC,CACrEC,iBAAiB,CAAC,CAAC,CACnBuC,kBAAkB,CAAC,CAAC;MAEvB,IAAI,CAAC/B,SAAS,EAAE;QACd,OAAO;UACLN,OAAO,EAAE,KAAK;UACdE,OAAO,EAAE;QACX,CAAC;MACH;;MAEA;MACA,IAAIoC,QAA4B;MAChC,IAAIH,SAAS,EAAE;QACb,IAAI;UACF,MAAMI,OAAO,GAAGJ,SAAS,CAACK,IAAI,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAC2B,GAAG,CAAC,CAAC;UAC/C,MAAMC,QAAQ,GAAG,GAAGjE,MAAM,SAAS4C,IAAI,CAACsB,GAAG,CAAC,CAAC,IAAIJ,OAAO,EAAE;UAC1D,MAAMK,QAAQ,GAAG,eAAeF,QAAQ,EAAE;UAE1C,MAAM;YAAE5E,KAAK,EAAE+E;UAAY,CAAC,GAAG,MAAMlG,QAAQ,CAACmG,OAAO,CAClD5E,IAAI,CAAC,QAAQ,CAAC,CACd6E,MAAM,CAACH,QAAQ,EAAET,SAAS,CAAC;UAE9B,IAAIU,WAAW,EAAE,MAAMA,WAAW;UAElC,MAAM;YAAE7E;UAAK,CAAC,GAAGrB,QAAQ,CAACmG,OAAO,CAC9B5E,IAAI,CAAC,QAAQ,CAAC,CACd8E,YAAY,CAACJ,QAAQ,CAAC;UAEzBN,QAAQ,GAAGtE,IAAI,CAACiF,SAAS;QAC3B,CAAC,CAAC,OAAOJ,WAAW,EAAE;UACpB5F,OAAO,CAACiG,IAAI,CAAC,sBAAsB,EAAEL,WAAW,CAAC;QACnD;MACF;;MAEA;MACA,MAAM;QAAE7E,IAAI;QAAEF;MAAM,CAAC,GAAG,MAAMnB,QAAQ,CACnCuB,IAAI,CAAC,gBAAgB,CAAC,CACtBiF,MAAM,CAAC,CAAC;QACPC,OAAO,EAAE3E,MAAM;QACfD,QAAQ,EAAE6E,KAAK,CAACnF,IAAI,CAACoC,SAAS,CAACvB,UAAU,CAAC;QAC1CwC,UAAU,EAAE,GAAG;QAAE;QACjB+B,SAAS,EAAEhB,QAAQ;QACnBiB,SAAS,EAAE;MACb,CAAC,CAAC,CAAC,CACFpF,MAAM,CAAC,CAAC,CACRqF,MAAM,CAAC,CAAC;MAEX,IAAI1F,KAAK,EAAE,MAAMA,KAAK;;MAEtB;MACA,MAAM,IAAI,CAACD,iBAAiB,CAAC,CAAC;MAE9B,OAAO;QACLmC,OAAO,EAAE,IAAI;QACbE,OAAO,EAAE,wCAAwC;QACjDuD,UAAU,EAAEzF,IAAI,CAACW;MACnB,CAAC;IAEH,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO;QACLkC,OAAO,EAAE,KAAK;QACdE,OAAO,EAAE,WAAWpC,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACoC,OAAO,GAAG,iBAAiB;MAChF,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMwD,mBAAmBA,CAACjF,MAAc,EAAiB;IACvD,IAAI;MACF,MAAM;QAAEX;MAAM,CAAC,GAAG,MAAMnB,QAAQ,CAC7BuB,IAAI,CAAC,gBAAgB,CAAC,CACtByF,MAAM,CAAC;QAAEJ,SAAS,EAAE;MAAM,CAAC,CAAC,CAC5BnF,EAAE,CAAC,SAAS,EAAEK,MAAM,CAAC;MAExB,IAAIX,KAAK,EAAE,MAAMA,KAAK;;MAEtB;MACA,MAAM,IAAI,CAACD,iBAAiB,CAAC,CAAC;IAEhC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE8F,QAAQA,CAAA,EAIN;IACA,MAAMC,cAAc,GAAG,IAAI,CAAC9G,kBAAkB,CAAC+G,MAAM,CACnD,CAACC,GAAG,EAAEhF,UAAU,KAAKgF,GAAG,GAAGhF,UAAU,CAACD,WAAW,CAACK,MAAM,EACxD,CACF,CAAC;IAED,OAAO;MACLrC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCkH,eAAe,EAAE,IAAI,CAACjH,kBAAkB,CAACoC,MAAM;MAC/C0E;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOI,WAAWA,CAChBC,MAAyB,EACzBC,QAAgB,GAAG,GAAG,EACtBC,SAAiB,GAAG,GAAG,EACjB;IACN,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV,MAAM;MAAEvC,KAAK;MAAEC;IAAO,CAAC,GAAGmC,MAAM;;IAEhC;IACA,IAAIK,QAAQ,GAAGzC,KAAK;IACpB,IAAI0C,SAAS,GAAGzC,MAAM;IAEtB,IAAID,KAAK,GAAGqC,QAAQ,EAAE;MACpBI,QAAQ,GAAGJ,QAAQ;MACnBK,SAAS,GAAIzC,MAAM,GAAGoC,QAAQ,GAAIrC,KAAK;IACzC;IAEA,IAAI0C,SAAS,GAAGJ,SAAS,EAAE;MACzBI,SAAS,GAAGJ,SAAS;MACrBG,QAAQ,GAAIA,QAAQ,GAAGH,SAAS,GAAII,SAAS;IAC/C;;IAEA;IACA,IAAID,QAAQ,KAAKzC,KAAK,IAAI0C,SAAS,KAAKzC,MAAM,EAAE;MAC9C,MAAM0C,SAAS,GAAGJ,GAAG,CAACK,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE5C,KAAK,EAAEC,MAAM,CAAC;MACvDmC,MAAM,CAACpC,KAAK,GAAGyC,QAAQ;MACvBL,MAAM,CAACnC,MAAM,GAAGyC,SAAS;MACzBH,GAAG,CAACM,YAAY,CAACF,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACnC;EACF;AACF;;AAEA;AACA,OAAO,MAAMG,sBAAsB,GAAG,IAAIhI,sBAAsB,CAAC,CAAC;AAClE,eAAegI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}