{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState, useCallback } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Alert, Stack, Stepper, Step, StepLabel, StepContent, CircularProgress, Avatar, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { PhotoCamera as PhotoCameraIcon, Face as FaceIcon, CheckCircle as CheckCircleIcon, Refresh as RefreshIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { faceRecognitionService } from '../../services/faceRecognitionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FaceRegistration = ({\n  user,\n  onRegistrationComplete,\n  showExistingEncodings = true\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const streamRef = useRef(null);\n  const [activeStep, setActiveStep] = useState(0);\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [registrationComplete, setRegistrationComplete] = useState(false);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const steps = [{\n    label: 'Préparation',\n    description: 'Positionnez-vous face à la caméra dans un endroit bien éclairé'\n  }, {\n    label: 'Capture d\\'images',\n    description: 'Prenez 3-5 photos de votre visage sous différents angles'\n  }, {\n    label: 'Validation',\n    description: 'Vérifiez et confirmez vos photos faciales'\n  }];\n\n  /**\n   * Démarre la caméra\n   */\n  const startCamera = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: {\n            ideal: 640\n          },\n          height: {\n            ideal: 480\n          },\n          facingMode: 'user'\n        },\n        audio: false\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n        setActiveStep(1);\n      }\n    } catch (err) {\n      console.error('Erreur accès caméra:', err);\n      setError('Impossible d\\'accéder à la caméra. Vérifiez les permissions.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * Arrête la caméra\n   */\n  const stopCamera = useCallback(() => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    if (videoRef.current) {\n      videoRef.current.srcObject = null;\n    }\n    setIsStreaming(false);\n  }, []);\n\n  /**\n   * Capture une image depuis la vidéo\n   */\n  const captureImage = useCallback(async () => {\n    if (!videoRef.current || !canvasRef.current) return;\n    try {\n      setLoading(true);\n      setError(null);\n      const video = videoRef.current;\n      const canvas = canvasRef.current;\n      const ctx = canvas.getContext('2d');\n      if (!ctx) return;\n\n      // Capturer l'image\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      ctx.drawImage(video, 0, 0);\n\n      // Convertir en blob puis en URL\n      canvas.toBlob(blob => {\n        if (blob) {\n          const imageUrl = URL.createObjectURL(blob);\n          setCapturedImages(prev => [...prev, imageUrl]);\n          if (capturedImages.length >= 2) {\n            // 3 images au total\n            setActiveStep(2);\n          }\n        }\n      }, 'image/jpeg', 0.8);\n    } catch (err) {\n      console.error('Erreur capture:', err);\n      setError('Erreur lors de la capture d\\'image');\n    } finally {\n      setLoading(false);\n    }\n  }, [capturedImages.length]);\n\n  /**\n   * Supprime une image capturée\n   */\n  const removeImage = index => {\n    setCapturedImages(prev => {\n      const newImages = [...prev];\n      URL.revokeObjectURL(newImages[index]); // Libérer la mémoire\n      newImages.splice(index, 1);\n      return newImages;\n    });\n    if (capturedImages.length <= 3) {\n      setActiveStep(1);\n    }\n  };\n\n  /**\n   * Upload depuis un fichier\n   */\n  const handleFileUpload = useCallback(async event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (!file) return;\n\n    // Vérifier le type de fichier\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image valide');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Créer une URL pour l'image\n      const imageUrl = URL.createObjectURL(file);\n      setCapturedImages(prev => [...prev, imageUrl]);\n\n      // Enregistrer directement l'encodage\n      const img = new Image();\n      img.onload = async () => {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n        const result = await faceRecognitionService.registerFaceEncoding(user.id, canvas, file);\n        if (result.success) {\n          setSuccess('Photo faciale enregistrée avec succès !');\n          setRegistrationComplete(true);\n          if (onRegistrationComplete) {\n            onRegistrationComplete(true);\n          }\n        } else {\n          setError(result.message);\n        }\n      };\n      img.src = imageUrl;\n    } catch (err) {\n      console.error('Erreur upload:', err);\n      setError('Erreur lors du traitement de l\\'image');\n    } finally {\n      setLoading(false);\n    }\n  }, [user.id, onRegistrationComplete]);\n\n  /**\n   * Confirme l'enregistrement des images\n   */\n  const confirmRegistration = async () => {\n    if (capturedImages.length === 0) return;\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Enregistrer chaque image capturée\n      let successCount = 0;\n      for (let i = 0; i < capturedImages.length; i++) {\n        const imageUrl = capturedImages[i];\n\n        // Convertir l'URL en canvas\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        await new Promise((resolve, reject) => {\n          img.onload = async () => {\n            try {\n              const canvas = document.createElement('canvas');\n              const ctx = canvas.getContext('2d');\n              if (!ctx) return reject('Canvas non supporté');\n              canvas.width = img.width;\n              canvas.height = img.height;\n              ctx.drawImage(img, 0, 0);\n              const result = await faceRecognitionService.registerFaceEncoding(user.id, canvas);\n              if (result.success) {\n                successCount++;\n              }\n              resolve(result);\n            } catch (err) {\n              reject(err);\n            }\n          };\n          img.onerror = reject;\n          img.src = imageUrl;\n        });\n      }\n      if (successCount > 0) {\n        setSuccess(`${successCount} photo(s) faciale(s) enregistrée(s) avec succès !`);\n        setRegistrationComplete(true);\n        stopCamera();\n        if (onRegistrationComplete) {\n          onRegistrationComplete(true);\n        }\n      } else {\n        setError('Aucune photo n\\'a pu être enregistrée');\n      }\n    } catch (err) {\n      console.error('Erreur enregistrement:', err);\n      setError('Erreur lors de l\\'enregistrement des photos faciales');\n    } finally {\n      setLoading(false);\n      setConfirmDialogOpen(false);\n    }\n  };\n\n  /**\n   * Recommence le processus\n   */\n  const restart = () => {\n    // Nettoyer les URLs d'objets\n    capturedImages.forEach(url => URL.revokeObjectURL(url));\n    setCapturedImages([]);\n    setActiveStep(0);\n    setRegistrationComplete(false);\n    setSuccess(null);\n    setError(null);\n    stopCamera();\n  };\n\n  // Nettoyage au démontage\n  React.useEffect(() => {\n    return () => {\n      stopCamera();\n      capturedImages.forEach(url => URL.revokeObjectURL(url));\n    };\n  }, [stopCamera, capturedImages]);\n  if (registrationComplete) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 3,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"success\",\n            sx: {\n              fontSize: 64\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            align: \"center\",\n            children: \"Enregistrement Facial Termin\\xE9 !\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            align: \"center\",\n            children: \"Vos photos faciales ont \\xE9t\\xE9 enregistr\\xE9es avec succ\\xE8s. Vous pouvez maintenant \\xEAtre reconnu automatiquement lors des sessions de pr\\xE9sence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 26\n            }, this),\n            onClick: restart,\n            children: \"Enregistrer de Nouvelles Photos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Enregistrement Facial\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"text.secondary\",\n        sx: {\n          mb: 3\n        },\n        children: \"Enregistrez vos photos faciales pour \\xEAtre reconnu automatiquement lors des sessions de pr\\xE9sence.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        onClose: () => setError(null),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        onClose: () => setSuccess(null),\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n        activeStep: activeStep,\n        orientation: \"vertical\",\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(Step, {\n          children: [/*#__PURE__*/_jsxDEV(StepLabel, {\n            children: step.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StepContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                mb: 2\n              },\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), index === 0 && /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: /*#__PURE__*/_jsxDEV(PhotoCameraIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 34\n                }, this),\n                onClick: startCamera,\n                disabled: loading,\n                children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 34\n                }, this) : 'Démarrer la Caméra'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Ou\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                accept: \"image/*\",\n                onChange: handleFileUpload,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => {\n                  var _fileInputRef$current;\n                  return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n                },\n                disabled: loading,\n                children: \"Choisir une Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this), index === 1 && isStreaming && /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                position: \"relative\",\n                display: \"inline-block\",\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: videoRef,\n                  autoPlay: true,\n                  playsInline: true,\n                  muted: true,\n                  style: {\n                    width: '100%',\n                    maxWidth: 400,\n                    borderRadius: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n                  ref: canvasRef,\n                  style: {\n                    display: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 34\n                }, this),\n                onClick: captureImage,\n                disabled: loading || capturedImages.length >= 5,\n                children: [\"Capturer (\", capturedImages.length, \"/5)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 19\n            }, this), index === 2 && /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: [\"Photos captur\\xE9es (\", capturedImages.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                spacing: 2,\n                flexWrap: \"wrap\",\n                children: capturedImages.map((imageUrl, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                  position: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: imageUrl,\n                    sx: {\n                      width: 80,\n                      height: 80\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    color: \"error\",\n                    onClick: () => removeImage(imgIndex),\n                    sx: {\n                      position: 'absolute',\n                      top: -8,\n                      right: -8,\n                      minWidth: 24,\n                      width: 24,\n                      height: 24,\n                      borderRadius: '50%'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 27\n                  }, this)]\n                }, imgIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                onClick: () => setConfirmDialogOpen(true),\n                disabled: capturedImages.length === 0 || loading,\n                children: \"Confirmer l'Enregistrement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)]\n        }, step.label, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: confirmDialogOpen,\n      onClose: () => setConfirmDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirmer l'Enregistrement\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Voulez-vous enregistrer ces \", capturedImages.length, \" photo(s) faciale(s) ? Cette action remplacera vos anciens enregistrements faciaux.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setConfirmDialogOpen(false),\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: confirmRegistration,\n          variant: \"contained\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 24\n          }, this) : 'Confirmer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 353,\n    columnNumber: 5\n  }, this);\n};\n_s(FaceRegistration, \"nl1HCvomM/nb5D9nMOABcS/eWTI=\");\n_c = FaceRegistration;\nexport default FaceRegistration;\nvar _c;\n$RefreshReg$(_c, \"FaceRegistration\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useCallback", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "Avatar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "PhotoCamera", "PhotoCameraIcon", "Face", "FaceIcon", "CheckCircle", "CheckCircleIcon", "Refresh", "RefreshIcon", "Delete", "DeleteIcon", "faceRecognitionService", "jsxDEV", "_jsxDEV", "FaceRegistration", "user", "onRegistrationComplete", "showExistingEncodings", "_s", "videoRef", "canvasRef", "fileInputRef", "streamRef", "activeStep", "setActiveStep", "isStreaming", "setIsStreaming", "loading", "setLoading", "error", "setError", "success", "setSuccess", "capturedImages", "setCapturedImages", "registrationComplete", "setRegistrationComplete", "confirmDialogOpen", "setConfirmDialogOpen", "steps", "label", "description", "startCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "width", "ideal", "height", "facingMode", "audio", "current", "srcObject", "err", "console", "stopCamera", "getTracks", "for<PERSON>ach", "track", "stop", "captureImage", "canvas", "ctx", "getContext", "videoWidth", "videoHeight", "drawImage", "toBlob", "blob", "imageUrl", "URL", "createObjectURL", "prev", "length", "removeImage", "index", "newImages", "revokeObjectURL", "splice", "handleFileUpload", "event", "_event$target$files", "file", "target", "files", "type", "startsWith", "img", "Image", "onload", "document", "createElement", "result", "registerFaceEncoding", "id", "message", "src", "confirmRegistration", "successCount", "i", "crossOrigin", "Promise", "resolve", "reject", "onerror", "restart", "url", "useEffect", "children", "spacing", "alignItems", "color", "sx", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "align", "startIcon", "onClick", "gutterBottom", "mb", "severity", "onClose", "orientation", "map", "step", "disabled", "size", "ref", "accept", "onChange", "style", "display", "_fileInputRef$current", "click", "position", "autoPlay", "playsInline", "muted", "max<PERSON><PERSON><PERSON>", "borderRadius", "direction", "flexWrap", "imgIndex", "top", "right", "min<PERSON><PERSON><PERSON>", "open", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/FaceRecognition/FaceRegistration.tsx"], "sourcesContent": ["import React, { useRef, useState, useCallback } from 'react';\nimport {\n  <PERSON>,\n  <PERSON>,\n  CardContent,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON>l,\n  StepContent,\n  CircularProgress,\n  Avatar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  PhotoCamera as PhotoCameraIcon,\n  Face as FaceIcon,\n  CheckCircle as CheckCircleIcon,\n  Refresh as RefreshIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { faceRecognitionService } from '../../services/faceRecognitionService';\nimport { User } from '../../types';\n\ninterface FaceRegistrationProps {\n  user: User;\n  onRegistrationComplete?: (success: boolean) => void;\n  showExistingEncodings?: boolean;\n}\n\nconst FaceRegistration: React.FC<FaceRegistrationProps> = ({\n  user,\n  onRegistrationComplete,\n  showExistingEncodings = true\n}) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n\n  const [activeStep, setActiveStep] = useState(0);\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [capturedImages, setCapturedImages] = useState<string[]>([]);\n  const [registrationComplete, setRegistrationComplete] = useState(false);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n\n  const steps = [\n    {\n      label: 'Préparation',\n      description: 'Positionnez-vous face à la caméra dans un endroit bien éclairé'\n    },\n    {\n      label: 'Capture d\\'images',\n      description: 'Prenez 3-5 photos de votre visage sous différents angles'\n    },\n    {\n      label: 'Validation',\n      description: 'Vérifiez et confirmez vos photos faciales'\n    }\n  ];\n\n  /**\n   * Démarre la caméra\n   */\n  const startCamera = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: { ideal: 640 },\n          height: { ideal: 480 },\n          facingMode: 'user'\n        },\n        audio: false\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n        setActiveStep(1);\n      }\n\n    } catch (err) {\n      console.error('Erreur accès caméra:', err);\n      setError('Impossible d\\'accéder à la caméra. Vérifiez les permissions.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * Arrête la caméra\n   */\n  const stopCamera = useCallback(() => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    \n    if (videoRef.current) {\n      videoRef.current.srcObject = null;\n    }\n\n    setIsStreaming(false);\n  }, []);\n\n  /**\n   * Capture une image depuis la vidéo\n   */\n  const captureImage = useCallback(async () => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const video = videoRef.current;\n      const canvas = canvasRef.current;\n      const ctx = canvas.getContext('2d');\n      \n      if (!ctx) return;\n\n      // Capturer l'image\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      ctx.drawImage(video, 0, 0);\n\n      // Convertir en blob puis en URL\n      canvas.toBlob((blob) => {\n        if (blob) {\n          const imageUrl = URL.createObjectURL(blob);\n          setCapturedImages(prev => [...prev, imageUrl]);\n          \n          if (capturedImages.length >= 2) { // 3 images au total\n            setActiveStep(2);\n          }\n        }\n      }, 'image/jpeg', 0.8);\n\n    } catch (err) {\n      console.error('Erreur capture:', err);\n      setError('Erreur lors de la capture d\\'image');\n    } finally {\n      setLoading(false);\n    }\n  }, [capturedImages.length]);\n\n  /**\n   * Supprime une image capturée\n   */\n  const removeImage = (index: number) => {\n    setCapturedImages(prev => {\n      const newImages = [...prev];\n      URL.revokeObjectURL(newImages[index]); // Libérer la mémoire\n      newImages.splice(index, 1);\n      return newImages;\n    });\n    \n    if (capturedImages.length <= 3) {\n      setActiveStep(1);\n    }\n  };\n\n  /**\n   * Upload depuis un fichier\n   */\n  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    // Vérifier le type de fichier\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image valide');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Créer une URL pour l'image\n      const imageUrl = URL.createObjectURL(file);\n      setCapturedImages(prev => [...prev, imageUrl]);\n\n      // Enregistrer directement l'encodage\n      const img = new Image();\n      img.onload = async () => {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n\n        const result = await faceRecognitionService.registerFaceEncoding(\n          user.id,\n          canvas,\n          file\n        );\n\n        if (result.success) {\n          setSuccess('Photo faciale enregistrée avec succès !');\n          setRegistrationComplete(true);\n          if (onRegistrationComplete) {\n            onRegistrationComplete(true);\n          }\n        } else {\n          setError(result.message);\n        }\n      };\n      img.src = imageUrl;\n\n    } catch (err) {\n      console.error('Erreur upload:', err);\n      setError('Erreur lors du traitement de l\\'image');\n    } finally {\n      setLoading(false);\n    }\n  }, [user.id, onRegistrationComplete]);\n\n  /**\n   * Confirme l'enregistrement des images\n   */\n  const confirmRegistration = async () => {\n    if (capturedImages.length === 0) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Enregistrer chaque image capturée\n      let successCount = 0;\n      \n      for (let i = 0; i < capturedImages.length; i++) {\n        const imageUrl = capturedImages[i];\n        \n        // Convertir l'URL en canvas\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        \n        await new Promise((resolve, reject) => {\n          img.onload = async () => {\n            try {\n              const canvas = document.createElement('canvas');\n              const ctx = canvas.getContext('2d');\n              if (!ctx) return reject('Canvas non supporté');\n\n              canvas.width = img.width;\n              canvas.height = img.height;\n              ctx.drawImage(img, 0, 0);\n\n              const result = await faceRecognitionService.registerFaceEncoding(\n                user.id,\n                canvas\n              );\n\n              if (result.success) {\n                successCount++;\n              }\n              resolve(result);\n            } catch (err) {\n              reject(err);\n            }\n          };\n          img.onerror = reject;\n          img.src = imageUrl;\n        });\n      }\n\n      if (successCount > 0) {\n        setSuccess(`${successCount} photo(s) faciale(s) enregistrée(s) avec succès !`);\n        setRegistrationComplete(true);\n        stopCamera();\n        \n        if (onRegistrationComplete) {\n          onRegistrationComplete(true);\n        }\n      } else {\n        setError('Aucune photo n\\'a pu être enregistrée');\n      }\n\n    } catch (err) {\n      console.error('Erreur enregistrement:', err);\n      setError('Erreur lors de l\\'enregistrement des photos faciales');\n    } finally {\n      setLoading(false);\n      setConfirmDialogOpen(false);\n    }\n  };\n\n  /**\n   * Recommence le processus\n   */\n  const restart = () => {\n    // Nettoyer les URLs d'objets\n    capturedImages.forEach(url => URL.revokeObjectURL(url));\n    setCapturedImages([]);\n    setActiveStep(0);\n    setRegistrationComplete(false);\n    setSuccess(null);\n    setError(null);\n    stopCamera();\n  };\n\n  // Nettoyage au démontage\n  React.useEffect(() => {\n    return () => {\n      stopCamera();\n      capturedImages.forEach(url => URL.revokeObjectURL(url));\n    };\n  }, [stopCamera, capturedImages]);\n\n  if (registrationComplete) {\n    return (\n      <Card>\n        <CardContent>\n          <Stack spacing={3} alignItems=\"center\">\n            <CheckCircleIcon color=\"success\" sx={{ fontSize: 64 }} />\n            <Typography variant=\"h6\" align=\"center\">\n              Enregistrement Facial Terminé !\n            </Typography>\n            <Typography color=\"text.secondary\" align=\"center\">\n              Vos photos faciales ont été enregistrées avec succès. \n              Vous pouvez maintenant être reconnu automatiquement lors des sessions de présence.\n            </Typography>\n            <Button\n              variant=\"outlined\"\n              startIcon={<RefreshIcon />}\n              onClick={restart}\n            >\n              Enregistrer de Nouvelles Photos\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Enregistrement Facial\n        </Typography>\n        \n        <Typography color=\"text.secondary\" sx={{ mb: 3 }}>\n          Enregistrez vos photos faciales pour être reconnu automatiquement \n          lors des sessions de présence.\n        </Typography>\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }} onClose={() => setError(null)}>\n            {error}\n          </Alert>\n        )}\n\n        {success && (\n          <Alert severity=\"success\" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>\n            {success}\n          </Alert>\n        )}\n\n        <Stepper activeStep={activeStep} orientation=\"vertical\">\n          {steps.map((step, index) => (\n            <Step key={step.label}>\n              <StepLabel>{step.label}</StepLabel>\n              <StepContent>\n                <Typography sx={{ mb: 2 }}>{step.description}</Typography>\n                \n                {index === 0 && (\n                  <Stack spacing={2}>\n                    <Button\n                      variant=\"contained\"\n                      startIcon={<PhotoCameraIcon />}\n                      onClick={startCamera}\n                      disabled={loading}\n                    >\n                      {loading ? <CircularProgress size={20} /> : 'Démarrer la Caméra'}\n                    </Button>\n                    \n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Ou\n                    </Typography>\n                    \n                    <input\n                      ref={fileInputRef}\n                      type=\"file\"\n                      accept=\"image/*\"\n                      onChange={handleFileUpload}\n                      style={{ display: 'none' }}\n                    />\n                    <Button\n                      variant=\"outlined\"\n                      onClick={() => fileInputRef.current?.click()}\n                      disabled={loading}\n                    >\n                      Choisir une Photo\n                    </Button>\n                  </Stack>\n                )}\n\n                {index === 1 && isStreaming && (\n                  <Stack spacing={2}>\n                    <Box position=\"relative\" display=\"inline-block\">\n                      <video\n                        ref={videoRef}\n                        autoPlay\n                        playsInline\n                        muted\n                        style={{\n                          width: '100%',\n                          maxWidth: 400,\n                          borderRadius: 8\n                        }}\n                      />\n                      <canvas\n                        ref={canvasRef}\n                        style={{ display: 'none' }}\n                      />\n                    </Box>\n                    \n                    <Button\n                      variant=\"contained\"\n                      startIcon={<FaceIcon />}\n                      onClick={captureImage}\n                      disabled={loading || capturedImages.length >= 5}\n                    >\n                      Capturer ({capturedImages.length}/5)\n                    </Button>\n                  </Stack>\n                )}\n\n                {index === 2 && (\n                  <Stack spacing={2}>\n                    <Typography variant=\"subtitle2\">\n                      Photos capturées ({capturedImages.length})\n                    </Typography>\n                    \n                    <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\">\n                      {capturedImages.map((imageUrl, imgIndex) => (\n                        <Box key={imgIndex} position=\"relative\">\n                          <Avatar\n                            src={imageUrl}\n                            sx={{ width: 80, height: 80 }}\n                          />\n                          <Button\n                            size=\"small\"\n                            color=\"error\"\n                            onClick={() => removeImage(imgIndex)}\n                            sx={{\n                              position: 'absolute',\n                              top: -8,\n                              right: -8,\n                              minWidth: 24,\n                              width: 24,\n                              height: 24,\n                              borderRadius: '50%'\n                            }}\n                          >\n                            <DeleteIcon fontSize=\"small\" />\n                          </Button>\n                        </Box>\n                      ))}\n                    </Stack>\n\n                    <Button\n                      variant=\"contained\"\n                      onClick={() => setConfirmDialogOpen(true)}\n                      disabled={capturedImages.length === 0 || loading}\n                    >\n                      Confirmer l'Enregistrement\n                    </Button>\n                  </Stack>\n                )}\n              </StepContent>\n            </Step>\n          ))}\n        </Stepper>\n      </CardContent>\n\n      {/* Dialog de confirmation */}\n      <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>\n        <DialogTitle>Confirmer l'Enregistrement</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Voulez-vous enregistrer ces {capturedImages.length} photo(s) faciale(s) ?\n            Cette action remplacera vos anciens enregistrements faciaux.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setConfirmDialogOpen(false)}>Annuler</Button>\n          <Button \n            onClick={confirmRegistration}\n            variant=\"contained\"\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={20} /> : 'Confirmer'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Card>\n  );\n};\n\nexport default FaceRegistration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,gBAAgB,EAChBC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,sBAAsB,QAAQ,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS/E,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,IAAI;EACJC,sBAAsB;EACtBC,qBAAqB,GAAG;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGtC,MAAM,CAAmB,IAAI,CAAC;EAC/C,MAAMuC,SAAS,GAAGvC,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAMwC,YAAY,GAAGxC,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMyC,SAAS,GAAGzC,MAAM,CAAqB,IAAI,CAAC;EAElD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMyD,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE;EACf,CAAC,CACF;;EAED;AACF;AACA;EACE,MAAMC,WAAW,GAAG3D,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF6C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMa,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAC;UACrBC,MAAM,EAAE;YAAED,KAAK,EAAE;UAAI,CAAC;UACtBE,UAAU,EAAE;QACd,CAAC;QACDC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAIjC,QAAQ,CAACkC,OAAO,EAAE;QACpBlC,QAAQ,CAACkC,OAAO,CAACC,SAAS,GAAGX,MAAM;QACnCrB,SAAS,CAAC+B,OAAO,GAAGV,MAAM;QAC1BjB,cAAc,CAAC,IAAI,CAAC;QACpBF,aAAa,CAAC,CAAC,CAAC;MAClB;IAEF,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAE0B,GAAG,CAAC;MAC1CzB,QAAQ,CAAC,8DAA8D,CAAC;IAC1E,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAM6B,UAAU,GAAG1E,WAAW,CAAC,MAAM;IACnC,IAAIuC,SAAS,CAAC+B,OAAO,EAAE;MACrB/B,SAAS,CAAC+B,OAAO,CAACK,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC5DvC,SAAS,CAAC+B,OAAO,GAAG,IAAI;IAC1B;IAEA,IAAIlC,QAAQ,CAACkC,OAAO,EAAE;MACpBlC,QAAQ,CAACkC,OAAO,CAACC,SAAS,GAAG,IAAI;IACnC;IAEA5B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMoC,YAAY,GAAG/E,WAAW,CAAC,YAAY;IAC3C,IAAI,CAACoC,QAAQ,CAACkC,OAAO,IAAI,CAACjC,SAAS,CAACiC,OAAO,EAAE;IAE7C,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMiB,KAAK,GAAG5B,QAAQ,CAACkC,OAAO;MAC9B,MAAMU,MAAM,GAAG3C,SAAS,CAACiC,OAAO;MAChC,MAAMW,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAI,CAACD,GAAG,EAAE;;MAEV;MACAD,MAAM,CAACf,KAAK,GAAGD,KAAK,CAACmB,UAAU;MAC/BH,MAAM,CAACb,MAAM,GAAGH,KAAK,CAACoB,WAAW;MACjCH,GAAG,CAACI,SAAS,CAACrB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE1B;MACAgB,MAAM,CAACM,MAAM,CAAEC,IAAI,IAAK;QACtB,IAAIA,IAAI,EAAE;UACR,MAAMC,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;UAC1CpC,iBAAiB,CAACwC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,QAAQ,CAAC,CAAC;UAE9C,IAAItC,cAAc,CAAC0C,MAAM,IAAI,CAAC,EAAE;YAAE;YAChCnD,aAAa,CAAC,CAAC,CAAC;UAClB;QACF;MACF,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC;IAEvB,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,iBAAiB,EAAE0B,GAAG,CAAC;MACrCzB,QAAQ,CAAC,oCAAoC,CAAC;IAChD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,cAAc,CAAC0C,MAAM,CAAC,CAAC;;EAE3B;AACF;AACA;EACE,MAAMC,WAAW,GAAIC,KAAa,IAAK;IACrC3C,iBAAiB,CAACwC,IAAI,IAAI;MACxB,MAAMI,SAAS,GAAG,CAAC,GAAGJ,IAAI,CAAC;MAC3BF,GAAG,CAACO,eAAe,CAACD,SAAS,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MACvCC,SAAS,CAACE,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MAC1B,OAAOC,SAAS;IAClB,CAAC,CAAC;IAEF,IAAI7C,cAAc,CAAC0C,MAAM,IAAI,CAAC,EAAE;MAC9BnD,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMyD,gBAAgB,GAAGlG,WAAW,CAAC,MAAOmG,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACzF,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI,CAACC,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC1D,QAAQ,CAAC,+CAA+C,CAAC;MACzD;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMyC,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACW,IAAI,CAAC;MAC1ClD,iBAAiB,CAACwC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,QAAQ,CAAC,CAAC;;MAE9C;MACA,MAAMkB,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,YAAY;QACvB,MAAM5B,MAAM,GAAG6B,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAM7B,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QACnC,IAAI,CAACD,GAAG,EAAE;QAEVD,MAAM,CAACf,KAAK,GAAGyC,GAAG,CAACzC,KAAK;QACxBe,MAAM,CAACb,MAAM,GAAGuC,GAAG,CAACvC,MAAM;QAC1Bc,GAAG,CAACI,SAAS,CAACqB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAExB,MAAMK,MAAM,GAAG,MAAMnF,sBAAsB,CAACoF,oBAAoB,CAC9DhF,IAAI,CAACiF,EAAE,EACPjC,MAAM,EACNqB,IACF,CAAC;QAED,IAAIU,MAAM,CAAC/D,OAAO,EAAE;UAClBC,UAAU,CAAC,yCAAyC,CAAC;UACrDI,uBAAuB,CAAC,IAAI,CAAC;UAC7B,IAAIpB,sBAAsB,EAAE;YAC1BA,sBAAsB,CAAC,IAAI,CAAC;UAC9B;QACF,CAAC,MAAM;UACLc,QAAQ,CAACgE,MAAM,CAACG,OAAO,CAAC;QAC1B;MACF,CAAC;MACDR,GAAG,CAACS,GAAG,GAAG3B,QAAQ;IAEpB,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,gBAAgB,EAAE0B,GAAG,CAAC;MACpCzB,QAAQ,CAAC,uCAAuC,CAAC;IACnD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACb,IAAI,CAACiF,EAAE,EAAEhF,sBAAsB,CAAC,CAAC;;EAErC;AACF;AACA;EACE,MAAMmF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIlE,cAAc,CAAC0C,MAAM,KAAK,CAAC,EAAE;IAEjC,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAIsE,YAAY,GAAG,CAAC;MAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpE,cAAc,CAAC0C,MAAM,EAAE0B,CAAC,EAAE,EAAE;QAC9C,MAAM9B,QAAQ,GAAGtC,cAAc,CAACoE,CAAC,CAAC;;QAElC;QACA,MAAMZ,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACa,WAAW,GAAG,WAAW;QAE7B,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACrChB,GAAG,CAACE,MAAM,GAAG,YAAY;YACvB,IAAI;cACF,MAAM5B,MAAM,GAAG6B,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;cAC/C,MAAM7B,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;cACnC,IAAI,CAACD,GAAG,EAAE,OAAOyC,MAAM,CAAC,qBAAqB,CAAC;cAE9C1C,MAAM,CAACf,KAAK,GAAGyC,GAAG,CAACzC,KAAK;cACxBe,MAAM,CAACb,MAAM,GAAGuC,GAAG,CAACvC,MAAM;cAC1Bc,GAAG,CAACI,SAAS,CAACqB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;cAExB,MAAMK,MAAM,GAAG,MAAMnF,sBAAsB,CAACoF,oBAAoB,CAC9DhF,IAAI,CAACiF,EAAE,EACPjC,MACF,CAAC;cAED,IAAI+B,MAAM,CAAC/D,OAAO,EAAE;gBAClBqE,YAAY,EAAE;cAChB;cACAI,OAAO,CAACV,MAAM,CAAC;YACjB,CAAC,CAAC,OAAOvC,GAAG,EAAE;cACZkD,MAAM,CAAClD,GAAG,CAAC;YACb;UACF,CAAC;UACDkC,GAAG,CAACiB,OAAO,GAAGD,MAAM;UACpBhB,GAAG,CAACS,GAAG,GAAG3B,QAAQ;QACpB,CAAC,CAAC;MACJ;MAEA,IAAI6B,YAAY,GAAG,CAAC,EAAE;QACpBpE,UAAU,CAAC,GAAGoE,YAAY,mDAAmD,CAAC;QAC9EhE,uBAAuB,CAAC,IAAI,CAAC;QAC7BqB,UAAU,CAAC,CAAC;QAEZ,IAAIzC,sBAAsB,EAAE;UAC1BA,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF,CAAC,MAAM;QACLc,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IAEF,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,EAAE0B,GAAG,CAAC;MAC5CzB,QAAQ,CAAC,sDAAsD,CAAC;IAClE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;MACjBU,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMqE,OAAO,GAAGA,CAAA,KAAM;IACpB;IACA1E,cAAc,CAAC0B,OAAO,CAACiD,GAAG,IAAIpC,GAAG,CAACO,eAAe,CAAC6B,GAAG,CAAC,CAAC;IACvD1E,iBAAiB,CAAC,EAAE,CAAC;IACrBV,aAAa,CAAC,CAAC,CAAC;IAChBY,uBAAuB,CAAC,KAAK,CAAC;IAC9BJ,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IACd2B,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA7E,KAAK,CAACiI,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXpD,UAAU,CAAC,CAAC;MACZxB,cAAc,CAAC0B,OAAO,CAACiD,GAAG,IAAIpC,GAAG,CAACO,eAAe,CAAC6B,GAAG,CAAC,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAACnD,UAAU,EAAExB,cAAc,CAAC,CAAC;EAEhC,IAAIE,oBAAoB,EAAE;IACxB,oBACEtB,OAAA,CAAC5B,IAAI;MAAA6H,QAAA,eACHjG,OAAA,CAAC3B,WAAW;QAAA4H,QAAA,eACVjG,OAAA,CAACvB,KAAK;UAACyH,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAF,QAAA,gBACpCjG,OAAA,CAACP,eAAe;YAAC2G,KAAK,EAAC,SAAS;YAACC,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAG;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD1G,OAAA,CAAC1B,UAAU;YAACqI,OAAO,EAAC,IAAI;YAACC,KAAK,EAAC,QAAQ;YAAAX,QAAA,EAAC;UAExC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1G,OAAA,CAAC1B,UAAU;YAAC8H,KAAK,EAAC,gBAAgB;YAACQ,KAAK,EAAC,QAAQ;YAAAX,QAAA,EAAC;UAGlD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1G,OAAA,CAACzB,MAAM;YACLoI,OAAO,EAAC,UAAU;YAClBE,SAAS,eAAE7G,OAAA,CAACL,WAAW;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BI,OAAO,EAAEhB,OAAQ;YAAAG,QAAA,EAClB;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACE1G,OAAA,CAAC5B,IAAI;IAAA6H,QAAA,gBACHjG,OAAA,CAAC3B,WAAW;MAAA4H,QAAA,gBACVjG,OAAA,CAAC1B,UAAU;QAACqI,OAAO,EAAC,IAAI;QAACI,YAAY;QAAAd,QAAA,EAAC;MAEtC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb1G,OAAA,CAAC1B,UAAU;QAAC8H,KAAK,EAAC,gBAAgB;QAACC,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,EAAC;MAGlD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ1F,KAAK,iBACJhB,OAAA,CAACxB,KAAK;QAACyI,QAAQ,EAAC,OAAO;QAACZ,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAACE,OAAO,EAAEA,CAAA,KAAMjG,QAAQ,CAAC,IAAI,CAAE;QAAAgF,QAAA,EAClEjF;MAAK;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAEAxF,OAAO,iBACNlB,OAAA,CAACxB,KAAK;QAACyI,QAAQ,EAAC,SAAS;QAACZ,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAACE,OAAO,EAAEA,CAAA,KAAM/F,UAAU,CAAC,IAAI,CAAE;QAAA8E,QAAA,EACtE/E;MAAO;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,eAED1G,OAAA,CAACtB,OAAO;QAACgC,UAAU,EAAEA,UAAW;QAACyG,WAAW,EAAC,UAAU;QAAAlB,QAAA,EACpDvE,KAAK,CAAC0F,GAAG,CAAC,CAACC,IAAI,EAAErD,KAAK,kBACrBhE,OAAA,CAACrB,IAAI;UAAAsH,QAAA,gBACHjG,OAAA,CAACpB,SAAS;YAAAqH,QAAA,EAAEoB,IAAI,CAAC1F;UAAK;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnC1G,OAAA,CAACnB,WAAW;YAAAoH,QAAA,gBACVjG,OAAA,CAAC1B,UAAU;cAAC+H,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,EAAEoB,IAAI,CAACzF;YAAW;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,EAEzD1C,KAAK,KAAK,CAAC,iBACVhE,OAAA,CAACvB,KAAK;cAACyH,OAAO,EAAE,CAAE;cAAAD,QAAA,gBAChBjG,OAAA,CAACzB,MAAM;gBACLoI,OAAO,EAAC,WAAW;gBACnBE,SAAS,eAAE7G,OAAA,CAACX,eAAe;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC/BI,OAAO,EAAEjF,WAAY;gBACrByF,QAAQ,EAAExG,OAAQ;gBAAAmF,QAAA,EAEjBnF,OAAO,gBAAGd,OAAA,CAAClB,gBAAgB;kBAACyI,IAAI,EAAE;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAoB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAET1G,OAAA,CAAC1B,UAAU;gBAACqI,OAAO,EAAC,OAAO;gBAACP,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EAAC;cAEnD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb1G,OAAA;gBACEwH,GAAG,EAAEhH,YAAa;gBAClBkE,IAAI,EAAC,MAAM;gBACX+C,MAAM,EAAC,SAAS;gBAChBC,QAAQ,EAAEtD,gBAAiB;gBAC3BuD,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACF1G,OAAA,CAACzB,MAAM;gBACLoI,OAAO,EAAC,UAAU;gBAClBG,OAAO,EAAEA,CAAA;kBAAA,IAAAe,qBAAA;kBAAA,QAAAA,qBAAA,GAAMrH,YAAY,CAACgC,OAAO,cAAAqF,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;gBAAA,CAAC;gBAC7CR,QAAQ,EAAExG,OAAQ;gBAAAmF,QAAA,EACnB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACR,EAEA1C,KAAK,KAAK,CAAC,IAAIpD,WAAW,iBACzBZ,OAAA,CAACvB,KAAK;cAACyH,OAAO,EAAE,CAAE;cAAAD,QAAA,gBAChBjG,OAAA,CAAC7B,GAAG;gBAAC4J,QAAQ,EAAC,UAAU;gBAACH,OAAO,EAAC,cAAc;gBAAA3B,QAAA,gBAC7CjG,OAAA;kBACEwH,GAAG,EAAElH,QAAS;kBACd0H,QAAQ;kBACRC,WAAW;kBACXC,KAAK;kBACLP,KAAK,EAAE;oBACLxF,KAAK,EAAE,MAAM;oBACbgG,QAAQ,EAAE,GAAG;oBACbC,YAAY,EAAE;kBAChB;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF1G,OAAA;kBACEwH,GAAG,EAAEjH,SAAU;kBACfoH,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1G,OAAA,CAACzB,MAAM;gBACLoI,OAAO,EAAC,WAAW;gBACnBE,SAAS,eAAE7G,OAAA,CAACT,QAAQ;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBI,OAAO,EAAE7D,YAAa;gBACtBqE,QAAQ,EAAExG,OAAO,IAAIM,cAAc,CAAC0C,MAAM,IAAI,CAAE;gBAAAmC,QAAA,GACjD,YACW,EAAC7E,cAAc,CAAC0C,MAAM,EAAC,KACnC;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACR,EAEA1C,KAAK,KAAK,CAAC,iBACVhE,OAAA,CAACvB,KAAK;cAACyH,OAAO,EAAE,CAAE;cAAAD,QAAA,gBAChBjG,OAAA,CAAC1B,UAAU;gBAACqI,OAAO,EAAC,WAAW;gBAAAV,QAAA,GAAC,uBACZ,EAAC7E,cAAc,CAAC0C,MAAM,EAAC,GAC3C;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb1G,OAAA,CAACvB,KAAK;gBAAC4J,SAAS,EAAC,KAAK;gBAACnC,OAAO,EAAE,CAAE;gBAACoC,QAAQ,EAAC,MAAM;gBAAArC,QAAA,EAC/C7E,cAAc,CAACgG,GAAG,CAAC,CAAC1D,QAAQ,EAAE6E,QAAQ,kBACrCvI,OAAA,CAAC7B,GAAG;kBAAgB4J,QAAQ,EAAC,UAAU;kBAAA9B,QAAA,gBACrCjG,OAAA,CAACjB,MAAM;oBACLsG,GAAG,EAAE3B,QAAS;oBACd2C,EAAE,EAAE;sBAAElE,KAAK,EAAE,EAAE;sBAAEE,MAAM,EAAE;oBAAG;kBAAE;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACF1G,OAAA,CAACzB,MAAM;oBACLgJ,IAAI,EAAC,OAAO;oBACZnB,KAAK,EAAC,OAAO;oBACbU,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAACwE,QAAQ,CAAE;oBACrClC,EAAE,EAAE;sBACF0B,QAAQ,EAAE,UAAU;sBACpBS,GAAG,EAAE,CAAC,CAAC;sBACPC,KAAK,EAAE,CAAC,CAAC;sBACTC,QAAQ,EAAE,EAAE;sBACZvG,KAAK,EAAE,EAAE;sBACTE,MAAM,EAAE,EAAE;sBACV+F,YAAY,EAAE;oBAChB,CAAE;oBAAAnC,QAAA,eAEFjG,OAAA,CAACH,UAAU;sBAACyG,QAAQ,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA,GApBD6B,QAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBb,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eAER1G,OAAA,CAACzB,MAAM;gBACLoI,OAAO,EAAC,WAAW;gBACnBG,OAAO,EAAEA,CAAA,KAAMrF,oBAAoB,CAAC,IAAI,CAAE;gBAC1C6F,QAAQ,EAAElG,cAAc,CAAC0C,MAAM,KAAK,CAAC,IAAIhD,OAAQ;gBAAAmF,QAAA,EAClD;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA,GA9GLW,IAAI,CAAC1F,KAAK;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+Gf,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGd1G,OAAA,CAAChB,MAAM;MAAC2J,IAAI,EAAEnH,iBAAkB;MAAC0F,OAAO,EAAEA,CAAA,KAAMzF,oBAAoB,CAAC,KAAK,CAAE;MAAAwE,QAAA,gBAC1EjG,OAAA,CAACf,WAAW;QAAAgH,QAAA,EAAC;MAA0B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrD1G,OAAA,CAACd,aAAa;QAAA+G,QAAA,eACZjG,OAAA,CAAC1B,UAAU;UAAA2H,QAAA,GAAC,8BACkB,EAAC7E,cAAc,CAAC0C,MAAM,EAAC,qFAErD;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB1G,OAAA,CAACb,aAAa;QAAA8G,QAAA,gBACZjG,OAAA,CAACzB,MAAM;UAACuI,OAAO,EAAEA,CAAA,KAAMrF,oBAAoB,CAAC,KAAK,CAAE;UAAAwE,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpE1G,OAAA,CAACzB,MAAM;UACLuI,OAAO,EAAExB,mBAAoB;UAC7BqB,OAAO,EAAC,WAAW;UACnBW,QAAQ,EAAExG,OAAQ;UAAAmF,QAAA,EAEjBnF,OAAO,gBAAGd,OAAA,CAAClB,gBAAgB;YAACyI,IAAI,EAAE;UAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAACrG,EAAA,CA/dIJ,gBAAiD;AAAA2I,EAAA,GAAjD3I,gBAAiD;AAievD,eAAeA,gBAAgB;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}