{"ast": null, "code": "import { disposeUnusedWeightTensors, extractWeightEntryFactory } from '../common';\nimport { isTensor2D } from '../utils';\nfunction extractorsFactory(weightMap, paramMappings) {\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  function extractScaleLayerParams(prefix) {\n    var weights = extractWeightEntry(prefix + \"/scale/weights\", 1);\n    var biases = extractWeightEntry(prefix + \"/scale/biases\", 1);\n    return {\n      weights: weights,\n      biases: biases\n    };\n  }\n  function extractConvLayerParams(prefix) {\n    var filters = extractWeightEntry(prefix + \"/conv/filters\", 4);\n    var bias = extractWeightEntry(prefix + \"/conv/bias\", 1);\n    var scale = extractScaleLayerParams(prefix);\n    return {\n      conv: {\n        filters: filters,\n        bias: bias\n      },\n      scale: scale\n    };\n  }\n  function extractResidualLayerParams(prefix) {\n    return {\n      conv1: extractConvLayerParams(prefix + \"/conv1\"),\n      conv2: extractConvLayerParams(prefix + \"/conv2\")\n    };\n  }\n  return {\n    extractConvLayerParams: extractConvLayerParams,\n    extractResidualLayerParams: extractResidualLayerParams\n  };\n}\nexport function extractParamsFromWeigthMap(weightMap) {\n  var paramMappings = [];\n  var _a = extractorsFactory(weightMap, paramMappings),\n    extractConvLayerParams = _a.extractConvLayerParams,\n    extractResidualLayerParams = _a.extractResidualLayerParams;\n  var conv32_down = extractConvLayerParams('conv32_down');\n  var conv32_1 = extractResidualLayerParams('conv32_1');\n  var conv32_2 = extractResidualLayerParams('conv32_2');\n  var conv32_3 = extractResidualLayerParams('conv32_3');\n  var conv64_down = extractResidualLayerParams('conv64_down');\n  var conv64_1 = extractResidualLayerParams('conv64_1');\n  var conv64_2 = extractResidualLayerParams('conv64_2');\n  var conv64_3 = extractResidualLayerParams('conv64_3');\n  var conv128_down = extractResidualLayerParams('conv128_down');\n  var conv128_1 = extractResidualLayerParams('conv128_1');\n  var conv128_2 = extractResidualLayerParams('conv128_2');\n  var conv256_down = extractResidualLayerParams('conv256_down');\n  var conv256_1 = extractResidualLayerParams('conv256_1');\n  var conv256_2 = extractResidualLayerParams('conv256_2');\n  var conv256_down_out = extractResidualLayerParams('conv256_down_out');\n  var fc = weightMap['fc'];\n  paramMappings.push({\n    originalPath: 'fc',\n    paramPath: 'fc'\n  });\n  if (!isTensor2D(fc)) {\n    throw new Error(\"expected weightMap[fc] to be a Tensor2D, instead have \" + fc);\n  }\n  var params = {\n    conv32_down: conv32_down,\n    conv32_1: conv32_1,\n    conv32_2: conv32_2,\n    conv32_3: conv32_3,\n    conv64_down: conv64_down,\n    conv64_1: conv64_1,\n    conv64_2: conv64_2,\n    conv64_3: conv64_3,\n    conv128_down: conv128_down,\n    conv128_1: conv128_1,\n    conv128_2: conv128_2,\n    conv256_down: conv256_down,\n    conv256_1: conv256_1,\n    conv256_2: conv256_2,\n    conv256_down_out: conv256_down_out,\n    fc: fc\n  };\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "extractWeightEntryFactory", "isTensor2D", "extractorsFactory", "weightMap", "paramMappings", "extractWeightEntry", "extractScaleLayerParams", "prefix", "weights", "biases", "extractConvLayerParams", "filters", "bias", "scale", "conv", "extractResidualLayerParams", "conv1", "conv2", "extractParamsFromWeigthMap", "_a", "conv32_down", "conv32_1", "conv32_2", "conv32_3", "conv64_down", "conv64_1", "conv64_2", "conv64_3", "conv128_down", "conv128_1", "conv128_2", "conv256_down", "conv256_1", "conv256_2", "conv256_down_out", "fc", "push", "originalPath", "<PERSON><PERSON><PERSON><PERSON>", "Error", "params"], "sources": ["../../../src/faceRecognitionNet/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,0BAA0B,EAAEC,yBAAyB,QAAsB,WAAW;AAC/F,SAASC,UAAU,QAAQ,UAAU;AAGrC,SAASC,iBAAiBA,CAACC,SAAc,EAAEC,aAA6B;EAEtE,IAAMC,kBAAkB,GAAGL,yBAAyB,CAACG,SAAS,EAAEC,aAAa,CAAC;EAE9E,SAASE,uBAAuBA,CAACC,MAAc;IAE7C,IAAMC,OAAO,GAAGH,kBAAkB,CAAiBE,MAAM,mBAAgB,EAAE,CAAC,CAAC;IAC7E,IAAME,MAAM,GAAGJ,kBAAkB,CAAiBE,MAAM,kBAAe,EAAE,CAAC,CAAC;IAE3E,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE;EAC5B;EAEA,SAASC,sBAAsBA,CAACH,MAAc;IAE5C,IAAMI,OAAO,GAAGN,kBAAkB,CAAiBE,MAAM,kBAAe,EAAE,CAAC,CAAC;IAC5E,IAAMK,IAAI,GAAGP,kBAAkB,CAAiBE,MAAM,eAAY,EAAE,CAAC,CAAC;IACtE,IAAMM,KAAK,GAAGP,uBAAuB,CAACC,MAAM,CAAC;IAE7C,OAAO;MAAEO,IAAI,EAAE;QAAEH,OAAO,EAAAA,OAAA;QAAEC,IAAI,EAAAA;MAAA,CAAE;MAAEC,KAAK,EAAAA;IAAA,CAAE;EAC3C;EAEA,SAASE,0BAA0BA,CAACR,MAAc;IAChD,OAAO;MACLS,KAAK,EAAEN,sBAAsB,CAAIH,MAAM,WAAQ,CAAC;MAChDU,KAAK,EAAEP,sBAAsB,CAAIH,MAAM,WAAQ;KAChD;EACH;EAEA,OAAO;IACLG,sBAAsB,EAAAA,sBAAA;IACtBK,0BAA0B,EAAAA;GAC3B;AAEH;AAEA,OAAM,SAAUG,0BAA0BA,CACxCf,SAA4B;EAG5B,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAAe,EAAA,GAAAjB,iBAAA,CAAAC,SAAA,EAAAC,aAAA,CAGyC;IAF7CM,sBAAA,GAAAS,EAAA,CAAAT,sBAAsB;IACtBK,0BAAA,GAAAI,EAAA,CAAAJ,0BAC6C;EAE/C,IAAMK,WAAW,GAAGV,sBAAsB,CAAC,aAAa,CAAC;EACzD,IAAMW,QAAQ,GAAGN,0BAA0B,CAAC,UAAU,CAAC;EACvD,IAAMO,QAAQ,GAAGP,0BAA0B,CAAC,UAAU,CAAC;EACvD,IAAMQ,QAAQ,GAAGR,0BAA0B,CAAC,UAAU,CAAC;EAEvD,IAAMS,WAAW,GAAGT,0BAA0B,CAAC,aAAa,CAAC;EAC7D,IAAMU,QAAQ,GAAGV,0BAA0B,CAAC,UAAU,CAAC;EACvD,IAAMW,QAAQ,GAAGX,0BAA0B,CAAC,UAAU,CAAC;EACvD,IAAMY,QAAQ,GAAGZ,0BAA0B,CAAC,UAAU,CAAC;EAEvD,IAAMa,YAAY,GAAGb,0BAA0B,CAAC,cAAc,CAAC;EAC/D,IAAMc,SAAS,GAAGd,0BAA0B,CAAC,WAAW,CAAC;EACzD,IAAMe,SAAS,GAAGf,0BAA0B,CAAC,WAAW,CAAC;EAEzD,IAAMgB,YAAY,GAAGhB,0BAA0B,CAAC,cAAc,CAAC;EAC/D,IAAMiB,SAAS,GAAGjB,0BAA0B,CAAC,WAAW,CAAC;EACzD,IAAMkB,SAAS,GAAGlB,0BAA0B,CAAC,WAAW,CAAC;EACzD,IAAMmB,gBAAgB,GAAGnB,0BAA0B,CAAC,kBAAkB,CAAC;EAEvE,IAAMoB,EAAE,GAAGhC,SAAS,CAAC,IAAI,CAAC;EAC1BC,aAAa,CAACgC,IAAI,CAAC;IAAEC,YAAY,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAI,CAAE,CAAC;EAE3D,IAAI,CAACrC,UAAU,CAACkC,EAAE,CAAC,EAAE;IACnB,MAAM,IAAII,KAAK,CAAC,2DAAyDJ,EAAI,CAAC;;EAGhF,IAAMK,MAAM,GAAG;IACbpB,WAAW,EAAAA,WAAA;IACXC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,WAAW,EAAAA,WAAA;IACXC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,YAAY,EAAAA,YAAA;IACZC,SAAS,EAAAA,SAAA;IACTC,SAAS,EAAAA,SAAA;IACTC,YAAY,EAAAA,YAAA;IACZC,SAAS,EAAAA,SAAA;IACTC,SAAS,EAAAA,SAAA;IACTC,gBAAgB,EAAAA,gBAAA;IAChBC,EAAE,EAAAA;GACH;EAEDpC,0BAA0B,CAACI,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAEoC,MAAM,EAAAA,MAAA;IAAEpC,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}