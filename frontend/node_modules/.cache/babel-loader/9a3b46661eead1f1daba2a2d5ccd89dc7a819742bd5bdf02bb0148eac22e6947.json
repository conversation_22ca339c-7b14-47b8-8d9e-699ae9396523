{"ast": null, "code": "var isNumber = function (arg) {\n  return typeof arg === 'number';\n};\nexport function validateConfig(config) {\n  if (!config) {\n    throw new Error(\"invalid config: \" + config);\n  }\n  if (typeof config.withSeparableConvs !== 'boolean') {\n    throw new Error(\"config.withSeparableConvs has to be a boolean, have: \" + config.withSeparableConvs);\n  }\n  if (!isNumber(config.iouThreshold) || config.iouThreshold < 0 || config.iouThreshold > 1.0) {\n    throw new Error(\"config.iouThreshold has to be a number between [0, 1], have: \" + config.iouThreshold);\n  }\n  if (!Array.isArray(config.classes) || !config.classes.length || !config.classes.every(function (c) {\n    return typeof c === 'string';\n  })) {\n    throw new Error(\"config.classes has to be an array class names: string[], have: \" + JSON.stringify(config.classes));\n  }\n  if (!Array.isArray(config.anchors) || !config.anchors.length || !config.anchors.map(function (a) {\n    return a || {};\n  }).every(function (a) {\n    return isNumber(a.x) && isNumber(a.y);\n  })) {\n    throw new Error(\"config.anchors has to be an array of { x: number, y: number }, have: \" + JSON.stringify(config.anchors));\n  }\n  if (config.meanRgb && (!Array.isArray(config.meanRgb) || config.meanRgb.length !== 3 || !config.meanRgb.every(isNumber))) {\n    throw new Error(\"config.meanRgb has to be an array of shape [number, number, number], have: \" + JSON.stringify(config.meanRgb));\n  }\n}", "map": {"version": 3, "names": ["isNumber", "arg", "validateConfig", "config", "Error", "withSeparableConvs", "iouThreshold", "Array", "isArray", "classes", "length", "every", "c", "JSON", "stringify", "anchors", "map", "a", "x", "y", "meanRgb"], "sources": ["../../../src/tinyYolov2/config.ts"], "sourcesContent": [null], "mappings": "AAaA,IAAMA,QAAQ,GAAG,SAAAA,CAACC,GAAQ;EAAK,cAAOA,GAAG,KAAK,QAAQ;AAAvB,CAAuB;AAEtD,OAAM,SAAUC,cAAcA,CAACC,MAAW;EACxC,IAAI,CAACA,MAAM,EAAE;IACX,MAAM,IAAIC,KAAK,CAAC,qBAAmBD,MAAQ,CAAC;;EAG9C,IAAI,OAAOA,MAAM,CAACE,kBAAkB,KAAK,SAAS,EAAE;IAClD,MAAM,IAAID,KAAK,CAAC,0DAAwDD,MAAM,CAACE,kBAAoB,CAAC;;EAGtG,IAAI,CAACL,QAAQ,CAACG,MAAM,CAACG,YAAY,CAAC,IAAIH,MAAM,CAACG,YAAY,GAAG,CAAC,IAAIH,MAAM,CAACG,YAAY,GAAG,GAAG,EAAE;IAC1F,MAAM,IAAIF,KAAK,CAAC,kEAAgED,MAAM,CAACG,YAAc,CAAC;;EAGxG,IACE,CAACC,KAAK,CAACC,OAAO,CAACL,MAAM,CAACM,OAAO,CAAC,IAC3B,CAACN,MAAM,CAACM,OAAO,CAACC,MAAM,IACtB,CAACP,MAAM,CAACM,OAAO,CAACE,KAAK,CAAC,UAACC,CAAM;IAAK,cAAOA,CAAC,KAAK,QAAQ;EAArB,CAAqB,CAAC,EAC3D;IAEA,MAAM,IAAIR,KAAK,CAAC,oEAAkES,IAAI,CAACC,SAAS,CAACX,MAAM,CAACM,OAAO,CAAG,CAAC;;EAGrH,IACE,CAACF,KAAK,CAACC,OAAO,CAACL,MAAM,CAACY,OAAO,CAAC,IAC3B,CAACZ,MAAM,CAACY,OAAO,CAACL,MAAM,IACtB,CAACP,MAAM,CAACY,OAAO,CAACC,GAAG,CAAC,UAACC,CAAM;IAAK,OAAAA,CAAC,IAAI,EAAE;EAAP,CAAO,CAAC,CAACN,KAAK,CAAC,UAACM,CAAM;IAAK,OAAAjB,QAAQ,CAACiB,CAAC,CAACC,CAAC,CAAC,IAAIlB,QAAQ,CAACiB,CAAC,CAACE,CAAC,CAAC;EAA9B,CAA8B,CAAC,EAC7F;IAEA,MAAM,IAAIf,KAAK,CAAC,0EAAwES,IAAI,CAACC,SAAS,CAACX,MAAM,CAACY,OAAO,CAAG,CAAC;;EAG3H,IAAIZ,MAAM,CAACiB,OAAO,KAChB,CAACb,KAAK,CAACC,OAAO,CAACL,MAAM,CAACiB,OAAO,CAAC,IAC3BjB,MAAM,CAACiB,OAAO,CAACV,MAAM,KAAK,CAAC,IAC3B,CAACP,MAAM,CAACiB,OAAO,CAACT,KAAK,CAACX,QAAQ,CAAC,CACnC,EAAE;IAED,MAAM,IAAII,KAAK,CAAC,gFAA8ES,IAAI,CAACC,SAAS,CAACX,MAAM,CAACiB,OAAO,CAAG,CAAC;;AAEnI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}