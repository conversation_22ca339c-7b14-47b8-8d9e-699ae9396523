{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { FaceDetection } from '../classes/FaceDetection';\nimport { env } from '../env';\nimport { createCanvas } from './createCanvas';\nimport { getContext2dOrThrow } from './getContext2dOrThrow';\nimport { imageTensorToCanvas } from './imageTensorToCanvas';\nimport { toNetInput } from './toNetInput';\n/**\r\n * Extracts the image regions containing the detected faces.\r\n *\r\n * @param input The image that face detection has been performed on.\r\n * @param detections The face detection results or face bounding boxes for that image.\r\n * @returns The Canvases of the corresponding image region for each detected face.\r\n */\nexport function extractFaces(input, detections) {\n  return __awaiter(this, void 0, void 0, function () {\n    var Canvas, canvas, netInput, tensorOrCanvas, _a, ctx, boxes;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          Canvas = env.getEnv().Canvas;\n          canvas = input;\n          if (!!(input instanceof Canvas)) return [3 /*break*/, 5];\n          return [4 /*yield*/, toNetInput(input)];\n        case 1:\n          netInput = _b.sent();\n          if (netInput.batchSize > 1) {\n            throw new Error('extractFaces - batchSize > 1 not supported');\n          }\n          tensorOrCanvas = netInput.getInput(0);\n          if (!(tensorOrCanvas instanceof Canvas)) return [3 /*break*/, 2];\n          _a = tensorOrCanvas;\n          return [3 /*break*/, 4];\n        case 2:\n          return [4 /*yield*/, imageTensorToCanvas(tensorOrCanvas)];\n        case 3:\n          _a = _b.sent();\n          _b.label = 4;\n        case 4:\n          canvas = _a;\n          _b.label = 5;\n        case 5:\n          ctx = getContext2dOrThrow(canvas);\n          boxes = detections.map(function (det) {\n            return det instanceof FaceDetection ? det.forSize(canvas.width, canvas.height).box.floor() : det;\n          }).map(function (box) {\n            return box.clipAtImageBorders(canvas.width, canvas.height);\n          });\n          return [2 /*return*/, boxes.map(function (_a) {\n            var x = _a.x,\n              y = _a.y,\n              width = _a.width,\n              height = _a.height;\n            var faceImg = createCanvas({\n              width: width,\n              height: height\n            });\n            getContext2dOrThrow(faceImg).putImageData(ctx.getImageData(x, y, width, height), 0, 0);\n            return faceImg;\n          })];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["FaceDetection", "env", "createCanvas", "getContext2dOrThrow", "imageTensorToCanvas", "toNetInput", "extractFaces", "input", "detections", "<PERSON><PERSON>", "getEnv", "canvas", "netInput", "_b", "sent", "batchSize", "Error", "tensorOrCanvas", "getInput", "_a", "ctx", "boxes", "map", "det", "forSize", "width", "height", "box", "floor", "clipAtImageBorders", "x", "y", "faceImg", "putImageData", "getImageData"], "sources": ["../../../src/dom/extractFaces.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,aAAa,QAAQ,0BAA0B;AAExD,SAASC,GAAG,QAAQ,QAAQ;AAC5B,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,UAAU,QAAQ,cAAc;AAGzC;;;;;;;AAOA,OAAM,SAAgBC,YAAYA,CAChCC,KAAgB,EAChBC,UAAuC;;;;;;UAG/BC,MAAM,GAAKR,GAAG,CAACS,MAAM,EAAE,CAAAD,MAAjB;UAEVE,MAAM,GAAGJ,KAA0B;eAEnC,EAAEA,KAAK,YAAYE,MAAM,CAAC,EAA1B;UACe,qBAAMJ,UAAU,CAACE,KAAK,CAAC;;UAAlCK,QAAQ,GAAGC,EAAA,CAAAC,IAAA,EAAuB;UAExC,IAAIF,QAAQ,CAACG,SAAS,GAAG,CAAC,EAAE;YAC1B,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;;UAGzDC,cAAc,GAAGL,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC;gBAClCD,cAAc,YAAYR,MAAM,GAAhC;UACLU,EAAA,GAAAF,cAAc;;;UACd,qBAAMb,mBAAmB,CAACa,cAAc,CAAC;;UAAzCE,EAAA,GAAAN,EAAA,CAAAC,IAAA,EAAyC;;;UAF7CH,MAAM,GAAAQ,EAEuC;;;UAGzCC,GAAG,GAAGjB,mBAAmB,CAACQ,MAAM,CAAC;UACjCU,KAAK,GAAGb,UAAU,CAACc,GAAG,CAC1B,UAAAC,GAAG;YAAI,OAAAA,GAAG,YAAYvB,aAAa,GAC/BuB,GAAG,CAACC,OAAO,CAACb,MAAM,CAACc,KAAK,EAAEd,MAAM,CAACe,MAAM,CAAC,CAACC,GAAG,CAACC,KAAK,EAAE,GACpDL,GAAG;UAFA,CAEA,CACR,CACED,GAAG,CAAC,UAAAK,GAAG;YAAI,OAAAA,GAAG,CAACE,kBAAkB,CAAClB,MAAM,CAACc,KAAK,EAAEd,MAAM,CAACe,MAAM,CAAC;UAAnD,CAAmD,CAAC;UAElE,sBAAOL,KAAK,CAACC,GAAG,CAAC,UAACH,EAAuB;gBAArBW,CAAA,GAAAX,EAAA,CAAAW,CAAC;cAAEC,CAAA,GAAAZ,EAAA,CAAAY,CAAC;cAAEN,KAAA,GAAAN,EAAA,CAAAM,KAAK;cAAEC,MAAA,GAAAP,EAAA,CAAAO,MAAM;YACrC,IAAMM,OAAO,GAAG9B,YAAY,CAAC;cAAEuB,KAAK,EAAAA,KAAA;cAAEC,MAAM,EAAAA;YAAA,CAAE,CAAC;YAC/CvB,mBAAmB,CAAC6B,OAAO,CAAC,CACzBC,YAAY,CAACb,GAAG,CAACc,YAAY,CAACJ,CAAC,EAAEC,CAAC,EAAEN,KAAK,EAAEC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5D,OAAOM,OAAO;UAChB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}