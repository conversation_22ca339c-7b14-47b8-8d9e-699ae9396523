{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { createFileSystem } from './createFileSystem';\nexport function createNodejsEnv() {\n  var Canvas = global['Canvas'] || global['HTMLCanvasElement'];\n  var Image = global['Image'] || global['HTMLImageElement'];\n  var createCanvasElement = function () {\n    if (Canvas) {\n      return new Canvas();\n    }\n    throw new Error('createCanvasElement - missing Canvas implementation for nodejs environment');\n  };\n  var createImageElement = function () {\n    if (Image) {\n      return new Image();\n    }\n    throw new Error('createImageElement - missing Image implementation for nodejs environment');\n  };\n  var fetch = global['fetch'] || function () {\n    throw new Error('fetch - missing fetch implementation for nodejs environment');\n  };\n  var fileSystem = createFileSystem();\n  return __assign({\n    Canvas: Canvas || (/** @class */function () {\n      function Canvas() {}\n      return Canvas;\n    }()),\n    CanvasRenderingContext2D: global['CanvasRenderingContext2D'] || (/** @class */function () {\n      function class_1() {}\n      return class_1;\n    }()),\n    Image: Image || (/** @class */function () {\n      function Image() {}\n      return Image;\n    }()),\n    ImageData: global['ImageData'] || (/** @class */function () {\n      function class_2() {}\n      return class_2;\n    }()),\n    Video: global['HTMLVideoElement'] || (/** @class */function () {\n      function class_3() {}\n      return class_3;\n    }()),\n    createCanvasElement: createCanvasElement,\n    createImageElement: createImageElement,\n    fetch: fetch\n  }, fileSystem);\n}", "map": {"version": 3, "names": ["createFileSystem", "createNodejsEnv", "<PERSON><PERSON>", "global", "Image", "createCanvasElement", "Error", "createImageElement", "fetch", "fileSystem", "__assign", "CanvasRenderingContext2D", "class_1", "ImageData", "class_2", "Video", "class_3"], "sources": ["../../../src/env/createNodejsEnv.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAGrD,OAAM,SAAUC,eAAeA,CAAA;EAE7B,IAAMC,MAAM,GAAGC,MAAM,CAAC,QAAQ,CAAC,IAAIA,MAAM,CAAC,mBAAmB,CAAC;EAC9D,IAAMC,KAAK,GAAGD,MAAM,CAAC,OAAO,CAAC,IAAIA,MAAM,CAAC,kBAAkB,CAAC;EAE3D,IAAME,mBAAmB,GAAG,SAAAA,CAAA;IAC1B,IAAIH,MAAM,EAAE;MACV,OAAO,IAAIA,MAAM,EAAE;;IAErB,MAAM,IAAII,KAAK,CAAC,4EAA4E,CAAC;EAC/F,CAAC;EAED,IAAMC,kBAAkB,GAAG,SAAAA,CAAA;IACzB,IAAIH,KAAK,EAAE;MACT,OAAO,IAAIA,KAAK,EAAE;;IAEpB,MAAM,IAAIE,KAAK,CAAC,0EAA0E,CAAC;EAC7F,CAAC;EAED,IAAME,KAAK,GAAGL,MAAM,CAAC,OAAO,CAAC,IAAI;IAC/B,MAAM,IAAIG,KAAK,CAAC,6DAA6D,CAAC;EAChF,CAAC;EAED,IAAMG,UAAU,GAAGT,gBAAgB,EAAE;EAErC,OAAAU,QAAA;IACER,MAAM,EAAEA,MAAM;MAAI,SAAAA,OAAA,GAAO;MAAA,OAAAA,MAAC;IAAD,CAAC,CAAR,EAAQ;IAC1BS,wBAAwB,EAAER,MAAM,CAAC,0BAA0B,CAAC;MAAI,SAAAS,QAAA,GAAO;MAAA,OAAAA,OAAC;IAAD,CAAC,CAAR,EAAQ;IACxER,KAAK,EAAEA,KAAK;MAAI,SAAAA,MAAA,GAAO;MAAA,OAAAA,KAAC;IAAD,CAAC,CAAR,EAAQ;IACxBS,SAAS,EAAEV,MAAM,CAAC,WAAW,CAAC;MAAI,SAAAW,QAAA,GAAO;MAAA,OAAAA,OAAC;IAAD,CAAC,CAAR,EAAQ;IAC1CC,KAAK,EAAEZ,MAAM,CAAC,kBAAkB,CAAC;MAAI,SAAAa,QAAA,GAAO;MAAA,OAAAA,OAAC;IAAD,CAAC,CAAR,EAAQ;IAC7CX,mBAAmB,EAAAA,mBAAA;IACnBE,kBAAkB,EAAAA,kBAAA;IAClBC,KAAK,EAAAA;EAAA,GACFC,UAAU;AAEjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}