{"ast": null, "code": "var isFunction = require('./isFunction'),\n  isLength = require('./isLength');\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\nmodule.exports = isArrayLike;", "map": {"version": 3, "names": ["isFunction", "require", "<PERSON><PERSON><PERSON><PERSON>", "isArrayLike", "value", "length", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/isArrayLike.js"], "sourcesContent": ["var isFunction = require('./isFunction'),\n    isLength = require('./isLength');\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\nmodule.exports = isArrayLike;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;EACpCC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAI,IAAI,IAAIF,QAAQ,CAACE,KAAK,CAACC,MAAM,CAAC,IAAI,CAACL,UAAU,CAACI,KAAK,CAAC;AACtE;AAEAE,MAAM,CAACC,OAAO,GAAGJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}