{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { Rect } from '../classes';\nimport { FaceDetection } from '../classes/FaceDetection';\nimport { toNetInput } from '../dom';\nimport { NeuralNetwork } from '../NeuralNetwork';\nimport { extractParams } from './extractParams';\nimport { extractParamsFromWeigthMap } from './extractParamsFromWeigthMap';\nimport { mobileNetV1 } from './mobileNetV1';\nimport { nonMaxSuppression } from './nonMaxSuppression';\nimport { outputLayer } from './outputLayer';\nimport { predictionLayer } from './predictionLayer';\nimport { SsdMobilenetv1Options } from './SsdMobilenetv1Options';\nvar SsdMobilenetv1 = /** @class */function (_super) {\n  __extends(SsdMobilenetv1, _super);\n  function SsdMobilenetv1() {\n    return _super.call(this, 'SsdMobilenetv1') || this;\n  }\n  SsdMobilenetv1.prototype.forwardInput = function (input) {\n    var params = this.params;\n    if (!params) {\n      throw new Error('SsdMobilenetv1 - load model before inference');\n    }\n    return tf.tidy(function () {\n      var batchTensor = input.toBatchTensor(512, false).toFloat();\n      var x = tf.sub(tf.mul(batchTensor, tf.scalar(0.007843137718737125)), tf.scalar(1));\n      var features = mobileNetV1(x, params.mobilenetv1);\n      var _a = predictionLayer(features.out, features.conv11, params.prediction_layer),\n        boxPredictions = _a.boxPredictions,\n        classPredictions = _a.classPredictions;\n      return outputLayer(boxPredictions, classPredictions, params.output_layer);\n    });\n  };\n  SsdMobilenetv1.prototype.forward = function (input) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            _a = this.forwardInput;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            return [2 /*return*/, _a.apply(this, [_b.sent()])];\n        }\n      });\n    });\n  };\n  SsdMobilenetv1.prototype.locateFaces = function (input, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var _a, maxResults, minConfidence, netInput, _b, _boxes, _scores, boxes, scores, i, scoresData, _c, _d, iouThreshold, indices, reshapedDims, inputSize, padX, padY, boxesData, results;\n      return __generator(this, function (_e) {\n        switch (_e.label) {\n          case 0:\n            _a = new SsdMobilenetv1Options(options), maxResults = _a.maxResults, minConfidence = _a.minConfidence;\n            return [4 /*yield*/, toNetInput(input)];\n          case 1:\n            netInput = _e.sent();\n            _b = this.forwardInput(netInput), _boxes = _b.boxes, _scores = _b.scores;\n            boxes = _boxes[0];\n            scores = _scores[0];\n            for (i = 1; i < _boxes.length; i++) {\n              _boxes[i].dispose();\n              _scores[i].dispose();\n            }\n            _d = (_c = Array).from;\n            return [4 /*yield*/, scores.data()];\n          case 2:\n            scoresData = _d.apply(_c, [_e.sent()]);\n            iouThreshold = 0.5;\n            indices = nonMaxSuppression(boxes, scoresData, maxResults, iouThreshold, minConfidence);\n            reshapedDims = netInput.getReshapedInputDimensions(0);\n            inputSize = netInput.inputSize;\n            padX = inputSize / reshapedDims.width;\n            padY = inputSize / reshapedDims.height;\n            boxesData = boxes.arraySync();\n            results = indices.map(function (idx) {\n              var _a = [Math.max(0, boxesData[idx][0]), Math.min(1.0, boxesData[idx][2])].map(function (val) {\n                  return val * padY;\n                }),\n                top = _a[0],\n                bottom = _a[1];\n              var _b = [Math.max(0, boxesData[idx][1]), Math.min(1.0, boxesData[idx][3])].map(function (val) {\n                  return val * padX;\n                }),\n                left = _b[0],\n                right = _b[1];\n              return new FaceDetection(scoresData[idx], new Rect(left, top, right - left, bottom - top), {\n                height: netInput.getInputHeight(0),\n                width: netInput.getInputWidth(0)\n              });\n            });\n            boxes.dispose();\n            scores.dispose();\n            return [2 /*return*/, results];\n        }\n      });\n    });\n  };\n  SsdMobilenetv1.prototype.getDefaultModelName = function () {\n    return 'ssd_mobilenetv1_model';\n  };\n  SsdMobilenetv1.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return extractParamsFromWeigthMap(weightMap);\n  };\n  SsdMobilenetv1.prototype.extractParams = function (weights) {\n    return extractParams(weights);\n  };\n  return SsdMobilenetv1;\n}(NeuralNetwork);\nexport { SsdMobilenetv1 };", "map": {"version": 3, "names": ["tf", "Rect", "FaceDetection", "toNetInput", "NeuralNetwork", "extractParams", "extractParamsFromWeigthMap", "mobileNetV1", "nonMaxSuppression", "outputLayer", "prediction<PERSON>ayer", "SsdMobilenetv1Options", "SsdMobilenetv1", "_super", "__extends", "call", "prototype", "forwardInput", "input", "params", "Error", "tidy", "batchTensor", "toBatchTensor", "toFloat", "x", "sub", "mul", "scalar", "features", "mobilenetv1", "_a", "out", "conv11", "prediction_layer", "boxPredictions", "classPredictions", "output_layer", "forward", "apply", "_b", "sent", "locateFaces", "options", "maxResults", "minConfidence", "netInput", "_e", "_boxes", "boxes", "_scores", "scores", "i", "length", "dispose", "_d", "_c", "Array", "from", "data", "scoresData", "iouThreshold", "indices", "reshapedDims", "getReshapedInputDimensions", "inputSize", "padX", "width", "padY", "height", "boxesData", "arraySync", "results", "map", "idx", "top", "bottom", "left", "right", "getInputHeight", "getInputWidth", "getDefaultModelName", "weightMap", "weights"], "sources": ["../../../src/ssdMobilenetv1/SsdMobilenetv1.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAA8BC,UAAU,QAAQ,QAAQ;AACxD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAAiCC,qBAAqB,QAAQ,yBAAyB;AAIvF,IAAAC,cAAA,0BAAAC,MAAA;EAAoCC,SAAA,CAAAF,cAAA,EAAAC,MAAA;EAElC,SAAAD,eAAA;WACEC,MAAA,CAAAE,IAAA,OAAM,gBAAgB,CAAC;EACzB;EAEOH,cAAA,CAAAI,SAAA,CAAAC,YAAY,GAAnB,UAAoBC,KAAe;IAEzB,IAAAC,MAAA,QAAAA,MAAM;IAEd,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;;IAGjE,OAAOpB,EAAE,CAACqB,IAAI,CAAC;MACb,IAAMC,WAAW,GAAGJ,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAACC,OAAO,EAAE;MAE7D,IAAMC,CAAC,GAAGzB,EAAE,CAAC0B,GAAG,CAAC1B,EAAE,CAAC2B,GAAG,CAACL,WAAW,EAAEtB,EAAE,CAAC4B,MAAM,CAAC,oBAAoB,CAAC,CAAC,EAAE5B,EAAE,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAgB;MACnG,IAAMC,QAAQ,GAAGtB,WAAW,CAACkB,CAAC,EAAEN,MAAM,CAACW,WAAW,CAAC;MAE7C,IAAAC,EAAA,GAAArB,eAAA,CAAAmB,QAAA,CAAAG,GAAA,EAAAH,QAAA,CAAAI,MAAA,EAAAd,MAAA,CAAAe,gBAAA,CAGqE;QAFzEC,cAAA,GAAAJ,EAAA,CAAAI,cAAc;QACdC,gBAAA,GAAAL,EAAA,CAAAK,gBACyE;MAE3E,OAAO3B,WAAW,CAAC0B,cAAc,EAAEC,gBAAgB,EAAEjB,MAAM,CAACkB,YAAY,CAAC;IAC3E,CAAC,CAAC;EACJ,CAAC;EAEYzB,cAAA,CAAAI,SAAA,CAAAsB,OAAO,GAApB,UAAqBpB,KAAgB;;;;;;YAC5Ba,EAAA,OAAI,CAACd,YAAY;YAAC,qBAAMd,UAAU,CAACe,KAAK,CAAC;;YAAhD,sBAAOa,EAAA,CAAAQ,KAAA,KAAI,GAAcC,EAAA,CAAAC,IAAA,EAAuB,EAAC;;;;GAClD;EAEY7B,cAAA,CAAAI,SAAA,CAAA0B,WAAW,GAAxB,UACExB,KAAgB,EAChByB,OAAoC;IAApC,IAAAA,OAAA;MAAAA,OAAA,KAAoC;IAAA;;;;;;YAG9BZ,EAAA,GAAgC,IAAIpB,qBAAqB,CAACgC,OAAO,CAAC,EAAhEC,UAAU,GAAAb,EAAA,CAAAa,UAAA,EAAEC,aAAa,GAAAd,EAAA,CAAAc,aAAA;YAEhB,qBAAM1C,UAAU,CAACe,KAAK,CAAC;;YAAlC4B,QAAQ,GAAGC,EAAA,CAAAN,IAAA,EAAuB;YAElCD,EAAA,GAGF,IAAI,CAACvB,YAAY,CAAC6B,QAAQ,CAAC,EAFtBE,MAAM,GAAAR,EAAA,CAAAS,KAAA,EACLC,OAAO,GAAAV,EAAA,CAAAW,MAAA;YAKXF,KAAK,GAAGD,MAAM,CAAC,CAAC,CAAC;YACjBG,MAAM,GAAGD,OAAO,CAAC,CAAC,CAAC;YACzB,KAASE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;cACtCJ,MAAM,CAACI,CAAC,CAAC,CAACE,OAAO,EAAE;cACnBJ,OAAO,CAACE,CAAC,CAAC,CAACE,OAAO,EAAE;;YAIHC,EAAA,IAAAC,EAAA,GAAAC,KAAK,EAACC,IAAI;YAAC,qBAAMP,MAAM,CAACQ,IAAI,EAAE;;YAA3CC,UAAU,GAAGL,EAAA,CAAAhB,KAAA,CAAAiB,EAAA,GAAWT,EAAA,CAAAN,IAAA,EAAmB,EAAC;YAE5CoB,YAAY,GAAG,GAAG;YAClBC,OAAO,GAAGtD,iBAAiB,CAC/ByC,KAAK,EACLW,UAAU,EACVhB,UAAU,EACViB,YAAY,EACZhB,aAAa,CACd;YAEKkB,YAAY,GAAGjB,QAAQ,CAACkB,0BAA0B,CAAC,CAAC,CAAC;YACrDC,SAAS,GAAGnB,QAAQ,CAACmB,SAAmB;YACxCC,IAAI,GAAGD,SAAS,GAAGF,YAAY,CAACI,KAAK;YACrCC,IAAI,GAAGH,SAAS,GAAGF,YAAY,CAACM,MAAM;YAEtCC,SAAS,GAAGrB,KAAK,CAACsB,SAAS,EAAE;YAC7BC,OAAO,GAAGV,OAAO,CACpBW,GAAG,CAAC,UAAAC,GAAG;cACA,IAAA3C,EAAA,I;;kBAGkB;gBAHjB4C,GAAA,GAAA5C,EAAA,GAAG;gBAAE6C,MAAA,GAAA7C,EAAA,GAGY;cAClB,IAAAS,EAAA,I;;kBAGkB;gBAHjBqC,IAAA,GAAArC,EAAA,GAAI;gBAAEsC,KAAA,GAAAtC,EAAA,GAGW;cACxB,OAAO,IAAItC,aAAa,CACtB0D,UAAU,CAACc,GAAG,CAAC,EACf,IAAIzE,IAAI,CACN4E,IAAI,EACJF,GAAG,EACHG,KAAK,GAAGD,IAAI,EACZD,MAAM,GAAGD,GAAG,CACb,EACD;gBACEN,MAAM,EAAEvB,QAAQ,CAACiC,cAAc,CAAC,CAAC,CAAC;gBAClCZ,KAAK,EAAErB,QAAQ,CAACkC,aAAa,CAAC,CAAC;eAChC,CACF;YACH,CAAC,CAAC;YAEJ/B,KAAK,CAACK,OAAO,EAAE;YACfH,MAAM,CAACG,OAAO,EAAE;YAEhB,sBAAOkB,OAAO;;;;GACf;EAES5D,cAAA,CAAAI,SAAA,CAAAiE,mBAAmB,GAA7B;IACE,OAAO,uBAAuB;EAChC,CAAC;EAESrE,cAAA,CAAAI,SAAA,CAAAV,0BAA0B,GAApC,UAAqC4E,SAA4B;IAC/D,OAAO5E,0BAA0B,CAAC4E,SAAS,CAAC;EAC9C,CAAC;EAEStE,cAAA,CAAAI,SAAA,CAAAX,aAAa,GAAvB,UAAwB8E,OAAqB;IAC3C,OAAO9E,aAAa,CAAC8E,OAAO,CAAC;EAC/B,CAAC;EACH,OAAAvE,cAAC;AAAD,CAAC,CApHmCR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}