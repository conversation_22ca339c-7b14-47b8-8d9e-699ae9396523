{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { extendWithFaceDescriptor } from '../factories/WithFaceDescriptor';\nimport { ComposableTask } from './ComposableTask';\nimport { extractAllFacesAndComputeResults, extractSingleFaceAndComputeResult } from './extractFacesAndComputeResults';\nimport { nets } from './nets';\nimport { PredictAllAgeAndGenderWithFaceAlignmentTask, PredictSingleAgeAndGenderWithFaceAlignmentTask } from './PredictAgeAndGenderTask';\nimport { PredictAllFaceExpressionsWithFaceAlignmentTask, PredictSingleFaceExpressionsWithFaceAlignmentTask } from './PredictFaceExpressionsTask';\nvar ComputeFaceDescriptorsTaskBase = /** @class */function (_super) {\n  __extends(ComputeFaceDescriptorsTaskBase, _super);\n  function ComputeFaceDescriptorsTaskBase(parentTask, input) {\n    var _this = _super.call(this) || this;\n    _this.parentTask = parentTask;\n    _this.input = input;\n    return _this;\n  }\n  return ComputeFaceDescriptorsTaskBase;\n}(ComposableTask);\nexport { ComputeFaceDescriptorsTaskBase };\nvar ComputeAllFaceDescriptorsTask = /** @class */function (_super) {\n  __extends(ComputeAllFaceDescriptorsTask, _super);\n  function ComputeAllFaceDescriptorsTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ComputeAllFaceDescriptorsTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResults, descriptors;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResults = _a.sent();\n            return [4 /*yield*/, extractAllFacesAndComputeResults(parentResults, this.input, function (faces) {\n              return Promise.all(faces.map(function (face) {\n                return nets.faceRecognitionNet.computeFaceDescriptor(face);\n              }));\n            }, null, function (parentResult) {\n              return parentResult.landmarks.align(null, {\n                useDlibAlignment: true\n              });\n            })];\n          case 2:\n            descriptors = _a.sent();\n            return [2 /*return*/, descriptors.map(function (descriptor, i) {\n              return extendWithFaceDescriptor(parentResults[i], descriptor);\n            })];\n        }\n      });\n    });\n  };\n  ComputeAllFaceDescriptorsTask.prototype.withFaceExpressions = function () {\n    return new PredictAllFaceExpressionsWithFaceAlignmentTask(this, this.input);\n  };\n  ComputeAllFaceDescriptorsTask.prototype.withAgeAndGender = function () {\n    return new PredictAllAgeAndGenderWithFaceAlignmentTask(this, this.input);\n  };\n  return ComputeAllFaceDescriptorsTask;\n}(ComputeFaceDescriptorsTaskBase);\nexport { ComputeAllFaceDescriptorsTask };\nvar ComputeSingleFaceDescriptorTask = /** @class */function (_super) {\n  __extends(ComputeSingleFaceDescriptorTask, _super);\n  function ComputeSingleFaceDescriptorTask() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ComputeSingleFaceDescriptorTask.prototype.run = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var parentResult, descriptor;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.parentTask];\n          case 1:\n            parentResult = _a.sent();\n            if (!parentResult) {\n              return [2 /*return*/];\n            }\n            return [4 /*yield*/, extractSingleFaceAndComputeResult(parentResult, this.input, function (face) {\n              return nets.faceRecognitionNet.computeFaceDescriptor(face);\n            }, null, function (parentResult) {\n              return parentResult.landmarks.align(null, {\n                useDlibAlignment: true\n              });\n            })];\n          case 2:\n            descriptor = _a.sent();\n            return [2 /*return*/, extendWithFaceDescriptor(parentResult, descriptor)];\n        }\n      });\n    });\n  };\n  ComputeSingleFaceDescriptorTask.prototype.withFaceExpressions = function () {\n    return new PredictSingleFaceExpressionsWithFaceAlignmentTask(this, this.input);\n  };\n  ComputeSingleFaceDescriptorTask.prototype.withAgeAndGender = function () {\n    return new PredictSingleAgeAndGenderWithFaceAlignmentTask(this, this.input);\n  };\n  return ComputeSingleFaceDescriptorTask;\n}(ComputeFaceDescriptorsTaskBase);\nexport { ComputeSingleFaceDescriptorTask };", "map": {"version": 3, "names": ["extendWithFaceDescriptor", "ComposableTask", "extractAllFacesAndComputeResults", "extractSingleFaceAndComputeResult", "nets", "PredictAllAgeAndGenderWithFaceAlignmentTask", "PredictSingleAgeAndGenderWithFaceAlignmentTask", "PredictAllFaceExpressionsWithFaceAlignmentTask", "PredictSingleFaceExpressionsWithFaceAlignmentTask", "ComputeFaceDescriptorsTaskBase", "_super", "__extends", "parentTask", "input", "_this", "call", "ComputeAllFaceDescriptorsTask", "prototype", "run", "parentResults", "_a", "sent", "faces", "Promise", "all", "map", "face", "faceRecognitionNet", "computeFaceDescriptor", "parentResult", "landmarks", "align", "useDlibAlignment", "descriptors", "descriptor", "i", "withFaceExpressions", "withAgeAndGender", "ComputeSingleFaceDescriptorTask"], "sources": ["../../../src/globalApi/ComputeFaceDescriptorsTasks.ts"], "sourcesContent": [null], "mappings": ";AACA,SAASA,wBAAwB,QAA4B,iCAAiC;AAG9F,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,gCAAgC,EAAEC,iCAAiC,QAAQ,iCAAiC;AACrH,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SACEC,2CAA2C,EAC3CC,8CAA8C,QACzC,2BAA2B;AAClC,SACEC,8CAA8C,EAC9CC,iDAAiD,QAC5C,8BAA8B;AAErC,IAAAC,8BAAA,0BAAAC,MAAA;EAA4EC,SAAA,CAAAF,8BAAA,EAAAC,MAAA;EAC1E,SAAAD,+BACYG,UAAkE,EAClEC,KAAgB;IAF5B,IAAAC,KAAA,GAIEJ,MAAA,CAAAK,IAAA,MAAO;IAHGD,KAAA,CAAAF,UAAU,GAAVA,UAAU;IACVE,KAAA,CAAAD,KAAK,GAALA,KAAK;;EAGjB;EACF,OAAAJ,8BAAC;AAAD,CAAC,CAP2ER,cAAc;;AAS1F,IAAAe,6BAAA,0BAAAN,MAAA;EAEUC,SAAA,CAAAK,6BAAA,EAAAN,MAAA;EAFV,SAAAM,8BAAA;;EA4BA;EAxBeA,6BAAA,CAAAC,SAAA,CAAAC,GAAG,GAAhB;;;;;;YAEwB,qBAAM,IAAI,CAACN,UAAU;;YAArCO,aAAa,GAAGC,EAAA,CAAAC,IAAA,EAAqB;YAEvB,qBAAMnB,gCAAgC,CACxDiB,aAAa,EACb,IAAI,CAACN,KAAK,EACV,UAAAS,KAAK;cAAI,OAAAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAACG,GAAG,CAAC,UAAAC,IAAI;gBACjC,OAAAtB,IAAI,CAACuB,kBAAkB,CAACC,qBAAqB,CAACF,IAAI,CAA0B;cAA5E,CAA4E,CAC7E,CAAC;YAFO,CAEP,EACF,IAAI,EACJ,UAAAG,YAAY;cAAI,OAAAA,YAAY,CAACC,SAAS,CAACC,KAAK,CAAC,IAAI,EAAE;gBAAEC,gBAAgB,EAAE;cAAI,CAAE,CAAC;YAA9D,CAA8D,CAC/E;;YARKC,WAAW,GAAGb,EAAA,CAAAC,IAAA,EAQnB;YAED,sBAAOY,WAAW,CAACR,GAAG,CAAC,UAACS,UAAU,EAAEC,CAAC;cAAK,OAAAnC,wBAAwB,CAAUmB,aAAa,CAACgB,CAAC,CAAC,EAAED,UAAU,CAAC;YAA/D,CAA+D,CAAC;;;;GAC3G;EAEDlB,6BAAA,CAAAC,SAAA,CAAAmB,mBAAmB,GAAnB;IACE,OAAO,IAAI7B,8CAA8C,CAAC,IAAI,EAAE,IAAI,CAACM,KAAK,CAAC;EAC7E,CAAC;EAEDG,6BAAA,CAAAC,SAAA,CAAAoB,gBAAgB,GAAhB;IACE,OAAO,IAAIhC,2CAA2C,CAAC,IAAI,EAAE,IAAI,CAACQ,KAAK,CAAC;EAC1E,CAAC;EACH,OAAAG,6BAAC;AAAD,CAAC,CA1BSP,8BAA8B;;AA4BxC,IAAA6B,+BAAA,0BAAA5B,MAAA;EAEUC,SAAA,CAAA2B,+BAAA,EAAA5B,MAAA;EAFV,SAAA4B,gCAAA;;EA4BA;EAxBeA,+BAAA,CAAArB,SAAA,CAAAC,GAAG,GAAhB;;;;;;YAEuB,qBAAM,IAAI,CAACN,UAAU;;YAApCiB,YAAY,GAAGT,EAAA,CAAAC,IAAA,EAAqB;YAC1C,IAAI,CAACQ,YAAY,EAAE;cACjB;;YAEiB,qBAAM1B,iCAAiC,CACxD0B,YAAY,EACZ,IAAI,CAAChB,KAAK,EACV,UAAAa,IAAI;cAAI,OAAAtB,IAAI,CAACuB,kBAAkB,CAACC,qBAAqB,CAACF,IAAI,CAA0B;YAA5E,CAA4E,EACpF,IAAI,EACJ,UAAAG,YAAY;cAAI,OAAAA,YAAY,CAACC,SAAS,CAACC,KAAK,CAAC,IAAI,EAAE;gBAAEC,gBAAgB,EAAE;cAAI,CAAE,CAAC;YAA9D,CAA8D,CAC/E;;YANKE,UAAU,GAAGd,EAAA,CAAAC,IAAA,EAMlB;YAED,sBAAOrB,wBAAwB,CAAC6B,YAAY,EAAEK,UAAU,CAAC;;;;GAC1D;EAEDI,+BAAA,CAAArB,SAAA,CAAAmB,mBAAmB,GAAnB;IACE,OAAO,IAAI5B,iDAAiD,CAAC,IAAI,EAAE,IAAI,CAACK,KAAK,CAAC;EAChF,CAAC;EAEDyB,+BAAA,CAAArB,SAAA,CAAAoB,gBAAgB,GAAhB;IACE,OAAO,IAAI/B,8CAA8C,CAAC,IAAI,EAAE,IAAI,CAACO,KAAK,CAAC;EAC7E,CAAC;EACH,OAAAyB,+BAAC;AAAD,CAAC,CA1BS7B,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}