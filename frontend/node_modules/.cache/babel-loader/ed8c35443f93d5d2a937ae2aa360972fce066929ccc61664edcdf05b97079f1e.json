{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { boxPredictionLayer } from './boxPredictionLayer';\nimport { pointwiseConvLayer } from './pointwiseConvLayer';\nexport function predictionLayer(x, conv11, params) {\n  return tf.tidy(function () {\n    var conv0 = pointwiseConvLayer(x, params.conv_0, [1, 1]);\n    var conv1 = pointwiseConvLayer(conv0, params.conv_1, [2, 2]);\n    var conv2 = pointwiseConvLayer(conv1, params.conv_2, [1, 1]);\n    var conv3 = pointwiseConvLayer(conv2, params.conv_3, [2, 2]);\n    var conv4 = pointwiseConvLayer(conv3, params.conv_4, [1, 1]);\n    var conv5 = pointwiseConvLayer(conv4, params.conv_5, [2, 2]);\n    var conv6 = pointwiseConvLayer(conv5, params.conv_6, [1, 1]);\n    var conv7 = pointwiseConvLayer(conv6, params.conv_7, [2, 2]);\n    var boxPrediction0 = boxPredictionLayer(conv11, params.box_predictor_0);\n    var boxPrediction1 = boxPredictionLayer(x, params.box_predictor_1);\n    var boxPrediction2 = boxPredictionLayer(conv1, params.box_predictor_2);\n    var boxPrediction3 = boxPredictionLayer(conv3, params.box_predictor_3);\n    var boxPrediction4 = boxPredictionLayer(conv5, params.box_predictor_4);\n    var boxPrediction5 = boxPredictionLayer(conv7, params.box_predictor_5);\n    var boxPredictions = tf.concat([boxPrediction0.boxPredictionEncoding, boxPrediction1.boxPredictionEncoding, boxPrediction2.boxPredictionEncoding, boxPrediction3.boxPredictionEncoding, boxPrediction4.boxPredictionEncoding, boxPrediction5.boxPredictionEncoding], 1);\n    var classPredictions = tf.concat([boxPrediction0.classPrediction, boxPrediction1.classPrediction, boxPrediction2.classPrediction, boxPrediction3.classPrediction, boxPrediction4.classPrediction, boxPrediction5.classPrediction], 1);\n    return {\n      boxPredictions: boxPredictions,\n      classPredictions: classPredictions\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "boxPredictionLayer", "pointwiseConv<PERSON><PERSON>er", "prediction<PERSON>ayer", "x", "conv11", "params", "tidy", "conv0", "conv_0", "conv1", "conv_1", "conv2", "conv_2", "conv3", "conv_3", "conv4", "conv_4", "conv5", "conv_5", "conv6", "conv_6", "conv7", "conv_7", "boxPrediction0", "box_predictor_0", "boxPrediction1", "box_predictor_1", "boxPrediction2", "box_predictor_2", "boxPrediction3", "box_predictor_3", "boxPrediction4", "box_predictor_4", "boxPrediction5", "box_predictor_5", "boxPredictions", "concat", "boxPredictionEncoding", "classPredictions", "classPrediction"], "sources": ["../../../src/ssdMobilenetv1/predictionLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,kBAAkB,QAAQ,sBAAsB;AAGzD,OAAM,SAAUC,eAAeA,CAC7BC,CAAc,EACdC,MAAmB,EACnBC,MAA6B;EAE7B,OAAON,EAAE,CAACO,IAAI,CAAC;IAEb,IAAMC,KAAK,GAAGN,kBAAkB,CAACE,CAAC,EAAEE,MAAM,CAACG,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1D,IAAMC,KAAK,GAAGR,kBAAkB,CAACM,KAAK,EAAEF,MAAM,CAACK,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,IAAMC,KAAK,GAAGV,kBAAkB,CAACQ,KAAK,EAAEJ,MAAM,CAACO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,IAAMC,KAAK,GAAGZ,kBAAkB,CAACU,KAAK,EAAEN,MAAM,CAACS,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,IAAMC,KAAK,GAAGd,kBAAkB,CAACY,KAAK,EAAER,MAAM,CAACW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,IAAMC,KAAK,GAAGhB,kBAAkB,CAACc,KAAK,EAAEV,MAAM,CAACa,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,IAAMC,KAAK,GAAGlB,kBAAkB,CAACgB,KAAK,EAAEZ,MAAM,CAACe,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,IAAMC,KAAK,GAAGpB,kBAAkB,CAACkB,KAAK,EAAEd,MAAM,CAACiB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE9D,IAAMC,cAAc,GAAGvB,kBAAkB,CAACI,MAAM,EAAEC,MAAM,CAACmB,eAAe,CAAC;IACzE,IAAMC,cAAc,GAAGzB,kBAAkB,CAACG,CAAC,EAAEE,MAAM,CAACqB,eAAe,CAAC;IACpE,IAAMC,cAAc,GAAG3B,kBAAkB,CAACS,KAAK,EAAEJ,MAAM,CAACuB,eAAe,CAAC;IACxE,IAAMC,cAAc,GAAG7B,kBAAkB,CAACa,KAAK,EAAER,MAAM,CAACyB,eAAe,CAAC;IACxE,IAAMC,cAAc,GAAG/B,kBAAkB,CAACiB,KAAK,EAAEZ,MAAM,CAAC2B,eAAe,CAAC;IACxE,IAAMC,cAAc,GAAGjC,kBAAkB,CAACqB,KAAK,EAAEhB,MAAM,CAAC6B,eAAe,CAAC;IAExE,IAAMC,cAAc,GAAGpC,EAAE,CAACqC,MAAM,CAAC,CAC/Bb,cAAc,CAACc,qBAAqB,EACpCZ,cAAc,CAACY,qBAAqB,EACpCV,cAAc,CAACU,qBAAqB,EACpCR,cAAc,CAACQ,qBAAqB,EACpCN,cAAc,CAACM,qBAAqB,EACpCJ,cAAc,CAACI,qBAAqB,CACrC,EAAE,CAAC,CAAgB;IAEpB,IAAMC,gBAAgB,GAAGvC,EAAE,CAACqC,MAAM,CAAC,CACjCb,cAAc,CAACgB,eAAe,EAC9Bd,cAAc,CAACc,eAAe,EAC9BZ,cAAc,CAACY,eAAe,EAC9BV,cAAc,CAACU,eAAe,EAC9BR,cAAc,CAACQ,eAAe,EAC9BN,cAAc,CAACM,eAAe,CAC/B,EAAE,CAAC,CAAgB;IAEpB,OAAO;MACLJ,cAAc,EAAAA,cAAA;MACdG,gBAAgB,EAAAA;KACjB;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}