{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Hook personnalisé pour utiliser Firebase dans PresencePro (maintenant Supabase)\n * Maintient la compatibilité avec l'interface Firebase\n */\n\nimport { useSupabase } from './useSupabase';\nexport const useFirebase = () => {\n  _s();\n  const {\n    supabaseUser,\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  } = useSupabase();\n\n  // Retourner l'interface compatible avec Firebase\n  return {\n    firebaseUser: supabaseUser,\n    // Alias pour compatibilité\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  };\n};\n_s(useFirebase, \"csVGFyf86rsn5/c/8R7VengNdp4=\", false, function () {\n  return [useSupabase];\n});\nexport default useFirebase;", "map": {"version": 3, "names": ["useSupabase", "useFirebase", "_s", "supabaseUser", "user", "loading", "error", "signIn", "signUp", "logout", "refreshUserData", "firebaseUser"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts"], "sourcesContent": ["/**\n * Hook personnalisé pour utiliser Firebase dans PresencePro (maintenant Supabase)\n * Maintient la compatibilité avec l'interface Firebase\n */\n\nimport { useSupabase } from './useSupabase';\nimport { User } from '../types';\n\ninterface UseFirebaseReturn {\n  // État d'authentification (compatibilité Firebase)\n  firebaseUser: any | null; // Maintenant c'est supabaseUser\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n\n  // Méthodes d'authentification\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, userData: Partial<User>) => Promise<void>;\n  logout: () => Promise<void>;\n\n  // Méthodes de données\n  refreshUserData: () => Promise<void>;\n}\n\nexport const useFirebase = (): UseFirebaseReturn => {\n  const {\n    supabaseUser,\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  } = useSupabase();\n\n  // Retourner l'interface compatible avec Firebase\n  return {\n    firebaseUser: supabaseUser, // Alias pour compatibilité\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  };\n};\n\nexport default useFirebase;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,QAAQ,eAAe;AAmB3C,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAyB;EAAAC,EAAA;EAClD,MAAM;IACJC,YAAY;IACZC,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC;EACF,CAAC,GAAGV,WAAW,CAAC,CAAC;;EAEjB;EACA,OAAO;IACLW,YAAY,EAAER,YAAY;IAAE;IAC5BC,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC;EACF,CAAC;AACH,CAAC;AAACR,EAAA,CAvBWD,WAAW;EAAA,QAUlBD,WAAW;AAAA;AAejB,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}