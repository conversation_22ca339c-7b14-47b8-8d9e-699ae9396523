{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { env } from '../env';\nimport { isTensor4D } from '../utils';\nexport function imageTensorToCanvas(imgTensor, canvas) {\n  return __awaiter(this, void 0, void 0, function () {\n    var targetCanvas, _a, height, width, numChannels, imgTensor3D;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          targetCanvas = canvas || env.getEnv().createCanvasElement();\n          _a = imgTensor.shape.slice(isTensor4D(imgTensor) ? 1 : 0), height = _a[0], width = _a[1], numChannels = _a[2];\n          imgTensor3D = tf.tidy(function () {\n            return imgTensor.as3D(height, width, numChannels).toInt();\n          });\n          return [4 /*yield*/, tf.browser.toPixels(imgTensor3D, targetCanvas)];\n        case 1:\n          _b.sent();\n          imgTensor3D.dispose();\n          return [2 /*return*/, targetCanvas];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "env", "isTensor4D", "imageTensorToCanvas", "imgTensor", "canvas", "targetCanvas", "getEnv", "createCanvasElement", "_a", "shape", "slice", "height", "width", "numChannels", "imgTensor3D", "tidy", "as3D", "toInt", "browser", "toPixels", "_b", "sent", "dispose"], "sources": ["../../../src/dom/imageTensorToCanvas.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,GAAG,QAAQ,QAAQ;AAC5B,SAASC,UAAU,QAAQ,UAAU;AAErC,OAAM,SAAgBC,mBAAmBA,CACvCC,SAAoB,EACpBC,MAA0B;;;;;;UAGpBC,YAAY,GAAGD,MAAM,IAAIJ,GAAG,CAACM,MAAM,EAAE,CAACC,mBAAmB,EAAE;UAE3DC,EAAA,GAA+BL,SAAS,CAACM,KAAK,CAACC,KAAK,CAACT,UAAU,CAACE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAlFQ,MAAM,GAAAH,EAAA,KAAEI,KAAK,GAAAJ,EAAA,KAAEK,WAAW,GAAAL,EAAA;UAC3BM,WAAW,GAAGf,EAAE,CAACgB,IAAI,CAAC;YAAM,OAAAZ,SAAS,CAACa,IAAI,CAACL,MAAM,EAAEC,KAAK,EAAEC,WAAW,CAAC,CAACI,KAAK,EAAE;UAAlD,CAAkD,CAAC;UACrF,qBAAMlB,EAAE,CAACmB,OAAO,CAACC,QAAQ,CAACL,WAAW,EAAET,YAAY,CAAC;;UAApDe,EAAA,CAAAC,IAAA,EAAoD;UAEpDP,WAAW,CAACQ,OAAO,EAAE;UAErB,sBAAOjB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}