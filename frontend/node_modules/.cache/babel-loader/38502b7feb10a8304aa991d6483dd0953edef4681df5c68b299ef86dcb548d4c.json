{"ast": null, "code": "import { env } from '../env';\nimport { isMediaLoaded } from './isMediaLoaded';\nexport function awaitMediaLoaded(media) {\n  return new Promise(function (resolve, reject) {\n    if (media instanceof env.getEnv().Canvas || isMediaLoaded(media)) {\n      return resolve();\n    }\n    function onLoad(e) {\n      if (!e.currentTarget) return;\n      e.currentTarget.removeEventListener('load', onLoad);\n      e.currentTarget.removeEventListener('error', onError);\n      resolve(e);\n    }\n    function onError(e) {\n      if (!e.currentTarget) return;\n      e.currentTarget.removeEventListener('load', onLoad);\n      e.currentTarget.removeEventListener('error', onError);\n      reject(e);\n    }\n    media.addEventListener('load', onLoad);\n    media.addEventListener('error', onError);\n  });\n}", "map": {"version": 3, "names": ["env", "isMediaLoaded", "awaitMediaLoaded", "media", "Promise", "resolve", "reject", "getEnv", "<PERSON><PERSON>", "onLoad", "e", "currentTarget", "removeEventListener", "onError", "addEventListener"], "sources": ["../../../src/dom/awaitMediaLoaded.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAC5B,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,OAAM,SAAUC,gBAAgBA,CAACC,KAA8D;EAE7F,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM;IACjC,IAAIH,KAAK,YAAYH,GAAG,CAACO,MAAM,EAAE,CAACC,MAAM,IAAIP,aAAa,CAACE,KAAK,CAAC,EAAE;MAChE,OAAOE,OAAO,EAAE;;IAGlB,SAASI,MAAMA,CAACC,CAAQ;MACtB,IAAI,CAACA,CAAC,CAACC,aAAa,EAAE;MACtBD,CAAC,CAACC,aAAa,CAACC,mBAAmB,CAAC,MAAM,EAAEH,MAAM,CAAC;MACnDC,CAAC,CAACC,aAAa,CAACC,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;MACrDR,OAAO,CAACK,CAAC,CAAC;IACZ;IAEA,SAASG,OAAOA,CAACH,CAAQ;MACvB,IAAI,CAACA,CAAC,CAACC,aAAa,EAAE;MACtBD,CAAC,CAACC,aAAa,CAACC,mBAAmB,CAAC,MAAM,EAAEH,MAAM,CAAC;MACnDC,CAAC,CAACC,aAAa,CAACC,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;MACrDP,MAAM,CAACI,CAAC,CAAC;IACX;IAEAP,KAAK,CAACW,gBAAgB,CAAC,MAAM,EAAEL,MAAM,CAAC;IACtCN,KAAK,CAACW,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;EAC1C,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}