{"ast": null, "code": "import { createBrowserEnv } from './createBrowserEnv';\nimport { createFileSystem } from './createFileSystem';\nimport { createNodejsEnv } from './createNodejsEnv';\nimport { isBrowser } from './isBrowser';\nimport { isNodejs } from './isNodejs';\nvar environment;\nfunction getEnv() {\n  if (!environment) {\n    throw new Error('getEnv - environment is not defined, check isNodejs() and isBrowser()');\n  }\n  return environment;\n}\nfunction setEnv(env) {\n  environment = env;\n}\nfunction initialize() {\n  // check for isBrowser() first to prevent electron renderer process\n  // to be initialized with wrong environment due to isNodejs() returning true\n  if (isBrowser()) {\n    setEnv(createBrowserEnv());\n  }\n  if (isNodejs()) {\n    setEnv(createNodejsEnv());\n  }\n}\nfunction monkeyPatch(env) {\n  if (!environment) {\n    initialize();\n  }\n  if (!environment) {\n    throw new Error('monkeyPatch - environment is not defined, check isNodejs() and isBrowser()');\n  }\n  var _a = env.Canvas,\n    Canvas = _a === void 0 ? environment.Canvas : _a,\n    _b = env.Image,\n    Image = _b === void 0 ? environment.Image : _b;\n  environment.Canvas = Canvas;\n  environment.Image = Image;\n  environment.createCanvasElement = env.createCanvasElement || function () {\n    return new Canvas();\n  };\n  environment.createImageElement = env.createImageElement || function () {\n    return new Image();\n  };\n  environment.ImageData = env.ImageData || environment.ImageData;\n  environment.Video = env.Video || environment.Video;\n  environment.fetch = env.fetch || environment.fetch;\n  environment.readFile = env.readFile || environment.readFile;\n}\nexport var env = {\n  getEnv: getEnv,\n  setEnv: setEnv,\n  initialize: initialize,\n  createBrowserEnv: createBrowserEnv,\n  createFileSystem: createFileSystem,\n  createNodejsEnv: createNodejsEnv,\n  monkeyPatch: monkeyPatch,\n  isBrowser: isBrowser,\n  isNodejs: isNodejs\n};\ninitialize();", "map": {"version": 3, "names": ["createBrowserEnv", "createFileSystem", "createNodejsEnv", "<PERSON><PERSON><PERSON><PERSON>", "isNodejs", "environment", "getEnv", "Error", "setEnv", "env", "initialize", "monkeyPatch", "_a", "<PERSON><PERSON>", "_b", "Image", "createCanvasElement", "createImageElement", "ImageData", "Video", "fetch", "readFile"], "sources": ["../../../src/env/index.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,QAAQ,YAAY;AAGrC,IAAIC,WAA+B;AAEnC,SAASC,MAAMA,CAAA;EACb,IAAI,CAACD,WAAW,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,uEAAuE,CAAC;;EAE1F,OAAOF,WAAW;AACpB;AAEA,SAASG,MAAMA,CAACC,GAAgB;EAC9BJ,WAAW,GAAGI,GAAG;AACnB;AAEA,SAASC,UAAUA,CAAA;EACjB;EACA;EACA,IAAIP,SAAS,EAAE,EAAE;IACfK,MAAM,CAACR,gBAAgB,EAAE,CAAC;;EAE5B,IAAII,QAAQ,EAAE,EAAE;IACdI,MAAM,CAACN,eAAe,EAAE,CAAC;;AAE7B;AAEA,SAASS,WAAWA,CAACF,GAAyB;EAC5C,IAAI,CAACJ,WAAW,EAAE;IAChBK,UAAU,EAAE;;EAGd,IAAI,CAACL,WAAW,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,4EAA4E,CAAC;;EAGvF,IAAAK,EAAA,GAAAH,GAAA,CAAAI,MAA2B;IAA3BA,MAAA,GAAAD,EAAA,cAAAP,WAAA,CAAAQ,MAAA,GAAAD,EAA2B;IAAEE,EAAA,GAAAL,GAAA,CAAAM,KAAyB;IAAzBA,KAAA,GAAAD,EAAA,cAAAT,WAAA,CAAAU,KAAA,GAAAD,EAAyB;EAC9DT,WAAW,CAACQ,MAAM,GAAGA,MAAM;EAC3BR,WAAW,CAACU,KAAK,GAAGA,KAAK;EACzBV,WAAW,CAACW,mBAAmB,GAAGP,GAAG,CAACO,mBAAmB,IAAK;IAAM,WAAIH,MAAM,EAAE;EAAZ,CAAa;EACjFR,WAAW,CAACY,kBAAkB,GAAGR,GAAG,CAACQ,kBAAkB,IAAK;IAAM,WAAIF,KAAK,EAAE;EAAX,CAAY;EAE9EV,WAAW,CAACa,SAAS,GAAGT,GAAG,CAACS,SAAS,IAAIb,WAAW,CAACa,SAAS;EAC9Db,WAAW,CAACc,KAAK,GAAGV,GAAG,CAACU,KAAK,IAAId,WAAW,CAACc,KAAK;EAClDd,WAAW,CAACe,KAAK,GAAGX,GAAG,CAACW,KAAK,IAAIf,WAAW,CAACe,KAAK;EAClDf,WAAW,CAACgB,QAAQ,GAAGZ,GAAG,CAACY,QAAQ,IAAIhB,WAAW,CAACgB,QAAQ;AAC7D;AAEA,OAAO,IAAMZ,GAAG,GAAG;EACjBH,MAAM,EAAAA,MAAA;EACNE,MAAM,EAAAA,MAAA;EACNE,UAAU,EAAAA,UAAA;EACVV,gBAAgB,EAAAA,gBAAA;EAChBC,gBAAgB,EAAAA,gBAAA;EAChBC,eAAe,EAAAA,eAAA;EACfS,WAAW,EAAAA,WAAA;EACXR,SAAS,EAAAA,SAAA;EACTC,QAAQ,EAAAA;CACT;AAEDM,UAAU,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}