{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13v5c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1v-5c0-.55-.45-1-1-1s-1 .45-1 1v6c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-6c0-.55-.45-1-1-1s-1 .45-1 1m-6-.33 1.88-1.88c.39-.39 1.02-.39 1.41 0s.39 1.02 0 1.41l-3.59 3.59c-.39.39-1.02.39-1.41 0L7.7 12.2a.996.996 0 0 1 0-1.41c.39-.39 1.02-.39 1.41 0L11 12.67V4c0-.55.45-1 1-1s1 .45 1 1z\"\n}), 'SaveAltRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@mui/icons-material/esm/SaveAltRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13v5c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1v-5c0-.55-.45-1-1-1s-1 .45-1 1v6c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-6c0-.55-.45-1-1-1s-1 .45-1 1m-6-.33 1.88-1.88c.39-.39 1.02-.39 1.41 0s.39 1.02 0 1.41l-3.59 3.59c-.39.39-1.02.39-1.41 0L7.7 12.2a.996.996 0 0 1 0-1.41c.39-.39 1.02-.39 1.41 0L11 12.67V4c0-.55.45-1 1-1s1 .45 1 1z\"\n}), 'SaveAltRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}