{"ast": null, "code": "import { disposeUnusedWeightTensors } from '../common/disposeUnusedWeightTensors';\nimport { loadSeparableConvParamsFactory } from '../common/extractSeparableConvParamsFactory';\nimport { extractWeightEntryFactory } from '../common/extractWeightEntryFactory';\nfunction extractorsFactory(weightMap, paramMappings) {\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  function extractBatchNormParams(prefix) {\n    var sub = extractWeightEntry(prefix + \"/sub\", 1);\n    var truediv = extractWeightEntry(prefix + \"/truediv\", 1);\n    return {\n      sub: sub,\n      truediv: truediv\n    };\n  }\n  function extractConvParams(prefix) {\n    var filters = extractWeightEntry(prefix + \"/filters\", 4);\n    var bias = extractWeightEntry(prefix + \"/bias\", 1);\n    return {\n      filters: filters,\n      bias: bias\n    };\n  }\n  function extractConvWithBatchNormParams(prefix) {\n    var conv = extractConvParams(prefix + \"/conv\");\n    var bn = extractBatchNormParams(prefix + \"/bn\");\n    return {\n      conv: conv,\n      bn: bn\n    };\n  }\n  var extractSeparableConvParams = loadSeparableConvParamsFactory(extractWeightEntry);\n  return {\n    extractConvParams: extractConvParams,\n    extractConvWithBatchNormParams: extractConvWithBatchNormParams,\n    extractSeparableConvParams: extractSeparableConvParams\n  };\n}\nexport function extractParamsFromWeigthMap(weightMap, config) {\n  var paramMappings = [];\n  var _a = extractorsFactory(weightMap, paramMappings),\n    extractConvParams = _a.extractConvParams,\n    extractConvWithBatchNormParams = _a.extractConvWithBatchNormParams,\n    extractSeparableConvParams = _a.extractSeparableConvParams;\n  var params;\n  if (config.withSeparableConvs) {\n    var numFilters = config.filterSizes && config.filterSizes.length || 9;\n    params = {\n      conv0: config.isFirstLayerConv2d ? extractConvParams('conv0') : extractSeparableConvParams('conv0'),\n      conv1: extractSeparableConvParams('conv1'),\n      conv2: extractSeparableConvParams('conv2'),\n      conv3: extractSeparableConvParams('conv3'),\n      conv4: extractSeparableConvParams('conv4'),\n      conv5: extractSeparableConvParams('conv5'),\n      conv6: numFilters > 7 ? extractSeparableConvParams('conv6') : undefined,\n      conv7: numFilters > 8 ? extractSeparableConvParams('conv7') : undefined,\n      conv8: extractConvParams('conv8')\n    };\n  } else {\n    params = {\n      conv0: extractConvWithBatchNormParams('conv0'),\n      conv1: extractConvWithBatchNormParams('conv1'),\n      conv2: extractConvWithBatchNormParams('conv2'),\n      conv3: extractConvWithBatchNormParams('conv3'),\n      conv4: extractConvWithBatchNormParams('conv4'),\n      conv5: extractConvWithBatchNormParams('conv5'),\n      conv6: extractConvWithBatchNormParams('conv6'),\n      conv7: extractConvWithBatchNormParams('conv7'),\n      conv8: extractConvParams('conv8')\n    };\n  }\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "loadSeparableConvParamsFactory", "extractWeightEntryFactory", "extractorsFactory", "weightMap", "paramMappings", "extractWeightEntry", "extractBatchNormParams", "prefix", "sub", "truediv", "extractConvParams", "filters", "bias", "extractConvWithBatchNormParams", "conv", "bn", "extractSeparableConvParams", "extractParamsFromWeigthMap", "config", "_a", "params", "withSeparableConvs", "numFilters", "filterSizes", "length", "conv0", "isFirstLayerConv2d", "conv1", "conv2", "conv3", "conv4", "conv5", "conv6", "undefined", "conv7", "conv8"], "sources": ["../../../src/tinyYolov2/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": "AAGA,SAASA,0BAA0B,QAAQ,sCAAsC;AACjF,SAASC,8BAA8B,QAAQ,6CAA6C;AAC5F,SAASC,yBAAyB,QAAQ,qCAAqC;AAK/E,SAASC,iBAAiBA,CAACC,SAAc,EAAEC,aAA6B;EAEtE,IAAMC,kBAAkB,GAAGJ,yBAAyB,CAACE,SAAS,EAAEC,aAAa,CAAC;EAE9E,SAASE,sBAAsBA,CAACC,MAAc;IAC5C,IAAMC,GAAG,GAAGH,kBAAkB,CAAiBE,MAAM,SAAM,EAAE,CAAC,CAAC;IAC/D,IAAME,OAAO,GAAGJ,kBAAkB,CAAiBE,MAAM,aAAU,EAAE,CAAC,CAAC;IACvE,OAAO;MAAEC,GAAG,EAAAA,GAAA;MAAEC,OAAO,EAAAA;IAAA,CAAE;EACzB;EAEA,SAASC,iBAAiBA,CAACH,MAAc;IACvC,IAAMI,OAAO,GAAGN,kBAAkB,CAAiBE,MAAM,aAAU,EAAE,CAAC,CAAC;IACvE,IAAMK,IAAI,GAAGP,kBAAkB,CAAiBE,MAAM,UAAO,EAAE,CAAC,CAAC;IACjE,OAAO;MAAEI,OAAO,EAAAA,OAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;EAC1B;EAEA,SAASC,8BAA8BA,CAACN,MAAc;IACpD,IAAMO,IAAI,GAAGJ,iBAAiB,CAAIH,MAAM,UAAO,CAAC;IAChD,IAAMQ,EAAE,GAAGT,sBAAsB,CAAIC,MAAM,QAAK,CAAC;IACjD,OAAO;MAAEO,IAAI,EAAAA,IAAA;MAAEC,EAAE,EAAAA;IAAA,CAAE;EACrB;EAEA,IAAMC,0BAA0B,GAAGhB,8BAA8B,CAACK,kBAAkB,CAAC;EAErF,OAAO;IACLK,iBAAiB,EAAAA,iBAAA;IACjBG,8BAA8B,EAAAA,8BAAA;IAC9BG,0BAA0B,EAAAA;GAC3B;AAEH;AAEA,OAAM,SAAUC,0BAA0BA,CACxCd,SAA4B,EAC5Be,MAAwB;EAGxB,IAAMd,aAAa,GAAmB,EAAE;EAElC,IAAAe,EAAA,GAAAjB,iBAAA,CAAAC,SAAA,EAAAC,aAAA,CAIyC;IAH7CM,iBAAA,GAAAS,EAAA,CAAAT,iBAAiB;IACjBG,8BAAA,GAAAM,EAAA,CAAAN,8BAA8B;IAC9BG,0BAAA,GAAAG,EAAA,CAAAH,0BAC6C;EAE/C,IAAII,MAA2B;EAE/B,IAAIF,MAAM,CAACG,kBAAkB,EAAE;IAC7B,IAAMC,UAAU,GAAIJ,MAAM,CAACK,WAAW,IAAIL,MAAM,CAACK,WAAW,CAACC,MAAM,IAAI,CAAE;IACzEJ,MAAM,GAAG;MACPK,KAAK,EAAEP,MAAM,CAACQ,kBAAkB,GAAGhB,iBAAiB,CAAC,OAAO,CAAC,GAAGM,0BAA0B,CAAC,OAAO,CAAC;MACnGW,KAAK,EAAEX,0BAA0B,CAAC,OAAO,CAAC;MAC1CY,KAAK,EAAEZ,0BAA0B,CAAC,OAAO,CAAC;MAC1Ca,KAAK,EAAEb,0BAA0B,CAAC,OAAO,CAAC;MAC1Cc,KAAK,EAAEd,0BAA0B,CAAC,OAAO,CAAC;MAC1Ce,KAAK,EAAEf,0BAA0B,CAAC,OAAO,CAAC;MAC1CgB,KAAK,EAAEV,UAAU,GAAG,CAAC,GAAGN,0BAA0B,CAAC,OAAO,CAAC,GAAGiB,SAAS;MACvEC,KAAK,EAAEZ,UAAU,GAAG,CAAC,GAAGN,0BAA0B,CAAC,OAAO,CAAC,GAAGiB,SAAS;MACvEE,KAAK,EAAEzB,iBAAiB,CAAC,OAAO;KACjC;GACF,MAAM;IACLU,MAAM,GAAG;MACPK,KAAK,EAAEZ,8BAA8B,CAAC,OAAO,CAAC;MAC9Cc,KAAK,EAAEd,8BAA8B,CAAC,OAAO,CAAC;MAC9Ce,KAAK,EAAEf,8BAA8B,CAAC,OAAO,CAAC;MAC9CgB,KAAK,EAAEhB,8BAA8B,CAAC,OAAO,CAAC;MAC9CiB,KAAK,EAAEjB,8BAA8B,CAAC,OAAO,CAAC;MAC9CkB,KAAK,EAAElB,8BAA8B,CAAC,OAAO,CAAC;MAC9CmB,KAAK,EAAEnB,8BAA8B,CAAC,OAAO,CAAC;MAC9CqB,KAAK,EAAErB,8BAA8B,CAAC,OAAO,CAAC;MAC9CsB,KAAK,EAAEzB,iBAAiB,CAAC,OAAO;KACjC;;EAGHX,0BAA0B,CAACI,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAEgB,MAAM,EAAAA,MAAA;IAAEhB,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}