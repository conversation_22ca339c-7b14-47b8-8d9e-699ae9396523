{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { env } from '../env';\nimport { padToSquare } from '../ops/padToSquare';\nimport { computeReshapedDimensions, isTensor3D, isTensor4D, range } from '../utils';\nimport { createCanvasFromMedia } from './createCanvas';\nimport { imageToSquare } from './imageToSquare';\nvar NetInput = /** @class */function () {\n  function NetInput(inputs, treatAsBatchInput) {\n    var _this = this;\n    if (treatAsBatchInput === void 0) {\n      treatAsBatchInput = false;\n    }\n    this._imageTensors = [];\n    this._canvases = [];\n    this._treatAsBatchInput = false;\n    this._inputDimensions = [];\n    if (!Array.isArray(inputs)) {\n      throw new Error(\"NetInput.constructor - expected inputs to be an Array of TResolvedNetInput or to be instanceof tf.Tensor4D, instead have \" + inputs);\n    }\n    this._treatAsBatchInput = treatAsBatchInput;\n    this._batchSize = inputs.length;\n    inputs.forEach(function (input, idx) {\n      if (isTensor3D(input)) {\n        _this._imageTensors[idx] = input;\n        _this._inputDimensions[idx] = input.shape;\n        return;\n      }\n      if (isTensor4D(input)) {\n        var batchSize = input.shape[0];\n        if (batchSize !== 1) {\n          throw new Error(\"NetInput - tf.Tensor4D with batchSize \" + batchSize + \" passed, but not supported in input array\");\n        }\n        _this._imageTensors[idx] = input;\n        _this._inputDimensions[idx] = input.shape.slice(1);\n        return;\n      }\n      var canvas = input instanceof env.getEnv().Canvas ? input : createCanvasFromMedia(input);\n      _this._canvases[idx] = canvas;\n      _this._inputDimensions[idx] = [canvas.height, canvas.width, 3];\n    });\n  }\n  Object.defineProperty(NetInput.prototype, \"imageTensors\", {\n    get: function () {\n      return this._imageTensors;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NetInput.prototype, \"canvases\", {\n    get: function () {\n      return this._canvases;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NetInput.prototype, \"isBatchInput\", {\n    get: function () {\n      return this.batchSize > 1 || this._treatAsBatchInput;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NetInput.prototype, \"batchSize\", {\n    get: function () {\n      return this._batchSize;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NetInput.prototype, \"inputDimensions\", {\n    get: function () {\n      return this._inputDimensions;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NetInput.prototype, \"inputSize\", {\n    get: function () {\n      return this._inputSize;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(NetInput.prototype, \"reshapedInputDimensions\", {\n    get: function () {\n      var _this = this;\n      return range(this.batchSize, 0, 1).map(function (_, batchIdx) {\n        return _this.getReshapedInputDimensions(batchIdx);\n      });\n    },\n    enumerable: true,\n    configurable: true\n  });\n  NetInput.prototype.getInput = function (batchIdx) {\n    return this.canvases[batchIdx] || this.imageTensors[batchIdx];\n  };\n  NetInput.prototype.getInputDimensions = function (batchIdx) {\n    return this._inputDimensions[batchIdx];\n  };\n  NetInput.prototype.getInputHeight = function (batchIdx) {\n    return this._inputDimensions[batchIdx][0];\n  };\n  NetInput.prototype.getInputWidth = function (batchIdx) {\n    return this._inputDimensions[batchIdx][1];\n  };\n  NetInput.prototype.getReshapedInputDimensions = function (batchIdx) {\n    if (typeof this.inputSize !== 'number') {\n      throw new Error('getReshapedInputDimensions - inputSize not set, toBatchTensor has not been called yet');\n    }\n    var width = this.getInputWidth(batchIdx);\n    var height = this.getInputHeight(batchIdx);\n    return computeReshapedDimensions({\n      width: width,\n      height: height\n    }, this.inputSize);\n  };\n  /**\r\n   * Create a batch tensor from all input canvases and tensors\r\n   * with size [batchSize, inputSize, inputSize, 3].\r\n   *\r\n   * @param inputSize Height and width of the tensor.\r\n   * @param isCenterImage (optional, default: false) If true, add an equal amount of padding on\r\n   * both sides of the minor dimension oof the image.\r\n   * @returns The batch tensor.\r\n   */\n  NetInput.prototype.toBatchTensor = function (inputSize, isCenterInputs) {\n    var _this = this;\n    if (isCenterInputs === void 0) {\n      isCenterInputs = true;\n    }\n    this._inputSize = inputSize;\n    return tf.tidy(function () {\n      var inputTensors = range(_this.batchSize, 0, 1).map(function (batchIdx) {\n        var input = _this.getInput(batchIdx);\n        if (input instanceof tf.Tensor) {\n          var imgTensor = isTensor4D(input) ? input : input.expandDims();\n          imgTensor = padToSquare(imgTensor, isCenterInputs);\n          if (imgTensor.shape[1] !== inputSize || imgTensor.shape[2] !== inputSize) {\n            imgTensor = tf.image.resizeBilinear(imgTensor, [inputSize, inputSize]);\n          }\n          return imgTensor.as3D(inputSize, inputSize, 3);\n        }\n        if (input instanceof env.getEnv().Canvas) {\n          return tf.browser.fromPixels(imageToSquare(input, inputSize, isCenterInputs));\n        }\n        throw new Error(\"toBatchTensor - at batchIdx \" + batchIdx + \", expected input to be instanceof tf.Tensor or instanceof HTMLCanvasElement, instead have \" + input);\n      });\n      var batchTensor = tf.stack(inputTensors.map(function (t) {\n        return t.toFloat();\n      })).as4D(_this.batchSize, inputSize, inputSize, 3);\n      return batchTensor;\n    });\n  };\n  return NetInput;\n}();\nexport { NetInput };", "map": {"version": 3, "names": ["tf", "env", "padToSquare", "computeReshapedDimensions", "isTensor3D", "isTensor4D", "range", "createCanvasFromMedia", "imageToSquare", "NetInput", "inputs", "treatAsBatchInput", "_this", "_imageTensors", "_canvases", "_treatAsBatchInput", "_inputDimensions", "Array", "isArray", "Error", "_batchSize", "length", "for<PERSON>ach", "input", "idx", "shape", "batchSize", "slice", "canvas", "getEnv", "<PERSON><PERSON>", "height", "width", "Object", "defineProperty", "prototype", "get", "_inputSize", "map", "_", "batchIdx", "getReshapedInputDimensions", "getInput", "canvases", "imageTensors", "getInputDimensions", "getInputHeight", "getInputWidth", "inputSize", "toBatchTensor", "isCenterInputs", "tidy", "inputTensors", "Tensor", "imgTensor", "expandDims", "image", "resizeBilinear", "as3D", "browser", "fromPixels", "batchTensor", "stack", "t", "toFloat", "as4D"], "sources": ["../../../src/dom/NetInput.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,GAAG,QAAQ,QAAQ;AAC5B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,yBAAyB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,QAAQ,UAAU;AACnF,SAASC,qBAAqB,QAAQ,gBAAgB;AACtD,SAASC,aAAa,QAAQ,iBAAiB;AAG/C,IAAAC,QAAA;EASE,SAAAA,SACEC,MAAgC,EAChCC,iBAAkC;IAFpC,IAAAC,KAAA;IAEE,IAAAD,iBAAA;MAAAA,iBAAA,QAAkC;IAAA;IAV5B,KAAAE,aAAa,GAAqC,EAAE;IACpD,KAAAC,SAAS,GAAwB,EAAE;IAEnC,KAAAC,kBAAkB,GAAY,KAAK;IAEnC,KAAAC,gBAAgB,GAAe,EAAE;IAOvC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;MAC1B,MAAM,IAAIS,KAAK,CAAC,8HAA4HT,MAAQ,CAAC;;IAGvJ,IAAI,CAACK,kBAAkB,GAAGJ,iBAAiB;IAC3C,IAAI,CAACS,UAAU,GAAGV,MAAM,CAACW,MAAM;IAE/BX,MAAM,CAACY,OAAO,CAAC,UAACC,KAAK,EAAEC,GAAG;MAExB,IAAIpB,UAAU,CAACmB,KAAK,CAAC,EAAE;QACrBX,KAAI,CAACC,aAAa,CAACW,GAAG,CAAC,GAAGD,KAAK;QAC/BX,KAAI,CAACI,gBAAgB,CAACQ,GAAG,CAAC,GAAGD,KAAK,CAACE,KAAK;QACxC;;MAGF,IAAIpB,UAAU,CAACkB,KAAK,CAAC,EAAE;QACrB,IAAMG,SAAS,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;QAChC,IAAIC,SAAS,KAAK,CAAC,EAAE;UACnB,MAAM,IAAIP,KAAK,CAAC,2CAAyCO,SAAS,8CAA2C,CAAC;;QAGhHd,KAAI,CAACC,aAAa,CAACW,GAAG,CAAC,GAAGD,KAAK;QAC/BX,KAAI,CAACI,gBAAgB,CAACQ,GAAG,CAAC,GAAGD,KAAK,CAACE,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;QACjD;;MAGF,IAAMC,MAAM,GAAGL,KAAK,YAAYtB,GAAG,CAAC4B,MAAM,EAAE,CAACC,MAAM,GAAGP,KAAK,GAAGhB,qBAAqB,CAACgB,KAAK,CAAC;MAC1FX,KAAI,CAACE,SAAS,CAACU,GAAG,CAAC,GAAGI,MAAM;MAC5BhB,KAAI,CAACI,gBAAgB,CAACQ,GAAG,CAAC,GAAG,CAACI,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,KAAK,EAAE,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ;EAEAC,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,gBAAY;SAAvB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACvB,aAAa;IAC3B,CAAC;;;;EAEDoB,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,YAAQ;SAAnB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACtB,SAAS;IACvB,CAAC;;;;EAEDmB,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,gBAAY;SAAvB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACV,SAAS,GAAG,CAAC,IAAI,IAAI,CAACX,kBAAkB;IACtD,CAAC;;;;EAEDkB,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,aAAS;SAApB,SAAAC,CAAA;MACE,OAAO,IAAI,CAAChB,UAAU;IACxB,CAAC;;;;EAEDa,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,mBAAe;SAA1B,SAAAC,CAAA;MACE,OAAO,IAAI,CAACpB,gBAAgB;IAC9B,CAAC;;;;EAEDiB,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,aAAS;SAApB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACC,UAAU;IACxB,CAAC;;;;EAEDJ,MAAA,CAAAC,cAAA,CAAWzB,QAAA,CAAA0B,SAAA,2BAAuB;SAAlC,SAAAC,CAAA;MAAA,IAAAxB,KAAA;MACE,OAAON,KAAK,CAAC,IAAI,CAACoB,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAACY,GAAG,CACpC,UAACC,CAAC,EAAEC,QAAQ;QAAK,OAAA5B,KAAI,CAAC6B,0BAA0B,CAACD,QAAQ,CAAC;MAAzC,CAAyC,CAC3D;IACH,CAAC;;;;EAEM/B,QAAA,CAAA0B,SAAA,CAAAO,QAAQ,GAAf,UAAgBF,QAAgB;IAC9B,OAAO,IAAI,CAACG,QAAQ,CAACH,QAAQ,CAAC,IAAI,IAAI,CAACI,YAAY,CAACJ,QAAQ,CAAC;EAC/D,CAAC;EAEM/B,QAAA,CAAA0B,SAAA,CAAAU,kBAAkB,GAAzB,UAA0BL,QAAgB;IACxC,OAAO,IAAI,CAACxB,gBAAgB,CAACwB,QAAQ,CAAC;EACxC,CAAC;EAEM/B,QAAA,CAAA0B,SAAA,CAAAW,cAAc,GAArB,UAAsBN,QAAgB;IACpC,OAAO,IAAI,CAACxB,gBAAgB,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC;EAEM/B,QAAA,CAAA0B,SAAA,CAAAY,aAAa,GAApB,UAAqBP,QAAgB;IACnC,OAAO,IAAI,CAACxB,gBAAgB,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC;EAEM/B,QAAA,CAAA0B,SAAA,CAAAM,0BAA0B,GAAjC,UAAkCD,QAAgB;IAChD,IAAI,OAAO,IAAI,CAACQ,SAAS,KAAK,QAAQ,EAAE;MACtC,MAAM,IAAI7B,KAAK,CAAC,uFAAuF,CAAC;;IAG1G,IAAMa,KAAK,GAAG,IAAI,CAACe,aAAa,CAACP,QAAQ,CAAC;IAC1C,IAAMT,MAAM,GAAG,IAAI,CAACe,cAAc,CAACN,QAAQ,CAAC;IAC5C,OAAOrC,yBAAyB,CAAC;MAAE6B,KAAK,EAAAA,KAAA;MAAED,MAAM,EAAAA;IAAA,CAAE,EAAE,IAAI,CAACiB,SAAS,CAAC;EACrE,CAAC;EAED;;;;;;;;;EASOvC,QAAA,CAAA0B,SAAA,CAAAc,aAAa,GAApB,UAAqBD,SAAiB,EAAEE,cAA8B;IAAtE,IAAAtC,KAAA;IAAwC,IAAAsC,cAAA;MAAAA,cAAA,OAA8B;IAAA;IAEpE,IAAI,CAACb,UAAU,GAAGW,SAAS;IAE3B,OAAOhD,EAAE,CAACmD,IAAI,CAAC;MAEb,IAAMC,YAAY,GAAG9C,KAAK,CAACM,KAAI,CAACc,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAACY,GAAG,CAAC,UAAAE,QAAQ;QAC3D,IAAMjB,KAAK,GAAGX,KAAI,CAAC8B,QAAQ,CAACF,QAAQ,CAAC;QAErC,IAAIjB,KAAK,YAAYvB,EAAE,CAACqD,MAAM,EAAE;UAC9B,IAAIC,SAAS,GAAGjD,UAAU,CAACkB,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,CAACgC,UAAU,EAAc;UAC1ED,SAAS,GAAGpD,WAAW,CAACoD,SAAS,EAAEJ,cAAc,CAAC;UAElD,IAAII,SAAS,CAAC7B,KAAK,CAAC,CAAC,CAAC,KAAKuB,SAAS,IAAIM,SAAS,CAAC7B,KAAK,CAAC,CAAC,CAAC,KAAKuB,SAAS,EAAE;YACxEM,SAAS,GAAGtD,EAAE,CAACwD,KAAK,CAACC,cAAc,CAACH,SAAS,EAAE,CAACN,SAAS,EAAEA,SAAS,CAAC,CAAC;;UAGxE,OAAOM,SAAS,CAACI,IAAI,CAACV,SAAS,EAAEA,SAAS,EAAE,CAAC,CAAC;;QAGhD,IAAIzB,KAAK,YAAYtB,GAAG,CAAC4B,MAAM,EAAE,CAACC,MAAM,EAAE;UACxC,OAAO9B,EAAE,CAAC2D,OAAO,CAACC,UAAU,CAACpD,aAAa,CAACe,KAAK,EAAEyB,SAAS,EAAEE,cAAc,CAAC,CAAC;;QAG/E,MAAM,IAAI/B,KAAK,CAAC,iCAA+BqB,QAAQ,kGAA6FjB,KAAO,CAAC;MAC9J,CAAC,CAAC;MAEF,IAAMsC,WAAW,GAAG7D,EAAE,CAAC8D,KAAK,CAACV,YAAY,CAACd,GAAG,CAAC,UAAAyB,CAAC;QAAI,OAAAA,CAAC,CAACC,OAAO,EAAE;MAAX,CAAW,CAAC,CAAC,CAACC,IAAI,CAACrD,KAAI,CAACc,SAAS,EAAEsB,SAAS,EAAEA,SAAS,EAAE,CAAC,CAAC;MAE9G,OAAOa,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EACH,OAAApD,QAAC;AAAD,CAAC,CA9ID", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}