{"ast": null, "code": "import { isTensor } from '../utils';\nexport function extractWeightEntryFactory(weightMap, paramMappings) {\n  return function (originalPath, paramRank, mappedPath) {\n    var tensor = weightMap[originalPath];\n    if (!isTensor(tensor, paramRank)) {\n      throw new Error(\"expected weightMap[\" + originalPath + \"] to be a Tensor\" + paramRank + \"D, instead have \" + tensor);\n    }\n    paramMappings.push({\n      originalPath: originalPath,\n      paramPath: mappedPath || originalPath\n    });\n    return tensor;\n  };\n}", "map": {"version": 3, "names": ["isTensor", "extractWeightEntryFactory", "weightMap", "paramMappings", "originalPath", "paramRank", "mappedPath", "tensor", "Error", "push", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/common/extractWeightEntryFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,QAAQ,QAAQ,UAAU;AAGnC,OAAM,SAAUC,yBAAyBA,CAACC,SAAc,EAAEC,aAA6B;EAErF,OAAO,UAAaC,YAAoB,EAAEC,SAAiB,EAAEC,UAAmB;IAC9E,IAAMC,MAAM,GAAGL,SAAS,CAACE,YAAY,CAAC;IAEtC,IAAI,CAACJ,QAAQ,CAACO,MAAM,EAAEF,SAAS,CAAC,EAAE;MAChC,MAAM,IAAIG,KAAK,CAAC,wBAAsBJ,YAAY,wBAAmBC,SAAS,wBAAmBE,MAAQ,CAAC;;IAG5GJ,aAAa,CAACM,IAAI,CAChB;MAAEL,YAAY,EAAAA,YAAA;MAAEM,SAAS,EAAEJ,UAAU,IAAIF;IAAY,CAAE,CACxD;IAED,OAAOG,MAAM;EACf,CAAC;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}