{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { fetchOrThrow } from './fetchOrThrow';\nexport function fetchJson(uri) {\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          return [4 /*yield*/, fetchOrThrow(uri)];\n        case 1:\n          return [2 /*return*/, _a.sent().json()];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["fetchOrThrow", "<PERSON><PERSON><PERSON>", "uri", "_a", "sent", "json"], "sources": ["../../../src/dom/fetchJson.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAE7C,OAAM,SAAgBC,SAASA,CAAIC,GAAW;;;;;UACpC,qBAAMF,YAAY,CAACE,GAAG,CAAC;;UAA/B,sBAAQC,EAAA,CAAAC,IAAA,EAAuB,CAAEC,IAAI,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}