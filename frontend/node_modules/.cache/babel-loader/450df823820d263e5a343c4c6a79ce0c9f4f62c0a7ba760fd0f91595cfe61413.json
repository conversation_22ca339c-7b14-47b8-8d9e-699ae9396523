{"ast": null, "code": "export function nonMaxSuppression(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold) {\n  var numBoxes = boxes.shape[0];\n  var outputSize = Math.min(maxOutputSize, numBoxes);\n  var candidates = scores.map(function (score, boxIndex) {\n    return {\n      score: score,\n      boxIndex: boxIndex\n    };\n  }).filter(function (c) {\n    return c.score > scoreThreshold;\n  }).sort(function (c1, c2) {\n    return c2.score - c1.score;\n  });\n  var suppressFunc = function (x) {\n    return x <= iouThreshold ? 1 : 0;\n  };\n  var selected = [];\n  candidates.forEach(function (c) {\n    if (selected.length >= outputSize) {\n      return;\n    }\n    var originalScore = c.score;\n    for (var j = selected.length - 1; j >= 0; --j) {\n      var iou = IOU(boxes, c.boxIndex, selected[j]);\n      if (iou === 0.0) {\n        continue;\n      }\n      c.score *= suppressFunc(iou);\n      if (c.score <= scoreThreshold) {\n        break;\n      }\n    }\n    if (originalScore === c.score) {\n      selected.push(c.boxIndex);\n    }\n  });\n  return selected;\n}\nfunction IOU(boxes, i, j) {\n  var boxesData = boxes.arraySync();\n  var yminI = Math.min(boxesData[i][0], boxesData[i][2]);\n  var xminI = Math.min(boxesData[i][1], boxesData[i][3]);\n  var ymaxI = Math.max(boxesData[i][0], boxesData[i][2]);\n  var xmaxI = Math.max(boxesData[i][1], boxesData[i][3]);\n  var yminJ = Math.min(boxesData[j][0], boxesData[j][2]);\n  var xminJ = Math.min(boxesData[j][1], boxesData[j][3]);\n  var ymaxJ = Math.max(boxesData[j][0], boxesData[j][2]);\n  var xmaxJ = Math.max(boxesData[j][1], boxesData[j][3]);\n  var areaI = (ymaxI - yminI) * (xmaxI - xminI);\n  var areaJ = (ymaxJ - yminJ) * (xmaxJ - xminJ);\n  if (areaI <= 0 || areaJ <= 0) {\n    return 0.0;\n  }\n  var intersectionYmin = Math.max(yminI, yminJ);\n  var intersectionXmin = Math.max(xminI, xminJ);\n  var intersectionYmax = Math.min(ymaxI, ymaxJ);\n  var intersectionXmax = Math.min(xmaxI, xmaxJ);\n  var intersectionArea = Math.max(intersectionYmax - intersectionYmin, 0.0) * Math.max(intersectionXmax - intersectionXmin, 0.0);\n  return intersectionArea / (areaI + areaJ - intersectionArea);\n}", "map": {"version": 3, "names": ["nonMaxSuppression", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "numBoxes", "shape", "outputSize", "Math", "min", "candidates", "map", "score", "boxIndex", "filter", "c", "sort", "c1", "c2", "suppressFunc", "x", "selected", "for<PERSON>ach", "length", "originalScore", "j", "iou", "IOU", "push", "i", "boxesData", "arraySync", "yminI", "xminI", "ymaxI", "max", "xmaxI", "yminJ", "xminJ", "ymaxJ", "xmaxJ", "areaI", "areaJ", "intersectionYmin", "intersectionXmin", "intersectionYmax", "intersectionXmax", "intersectionArea"], "sources": ["../../../src/ssdMobilenetv1/nonMaxSuppression.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,iBAAiBA,CAC/BC,KAAkB,EAClBC,MAAgB,EAChBC,aAAqB,EACrBC,YAAoB,EACpBC,cAAsB;EAGtB,IAAMC,QAAQ,GAAGL,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;EAC/B,IAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CACzBP,aAAa,EACbG,QAAQ,CACT;EAED,IAAMK,UAAU,GAAGT,MAAM,CACtBU,GAAG,CAAC,UAACC,KAAK,EAAEC,QAAQ;IAAK,OAAC;MAAED,KAAK,EAAAA,KAAA;MAAEC,QAAQ,EAAAA;IAAA,CAAE;EAApB,CAAqB,CAAC,CAC/CC,MAAM,CAAC,UAAAC,CAAC;IAAI,OAAAA,CAAC,CAACH,KAAK,GAAGR,cAAc;EAAxB,CAAwB,CAAC,CACrCY,IAAI,CAAC,UAACC,EAAE,EAAEC,EAAE;IAAK,OAAAA,EAAE,CAACN,KAAK,GAAGK,EAAE,CAACL,KAAK;EAAnB,CAAmB,CAAC;EAExC,IAAMO,YAAY,GAAG,SAAAA,CAACC,CAAS;IAAK,OAAAA,CAAC,IAAIjB,YAAY,GAAG,CAAC,GAAG,CAAC;EAAzB,CAAyB;EAE7D,IAAMkB,QAAQ,GAAa,EAAE;EAE7BX,UAAU,CAACY,OAAO,CAAC,UAAAP,CAAC;IAClB,IAAIM,QAAQ,CAACE,MAAM,IAAIhB,UAAU,EAAE;MACjC;;IAEF,IAAMiB,aAAa,GAAGT,CAAC,CAACH,KAAK;IAE7B,KAAK,IAAIa,CAAC,GAAGJ,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC7C,IAAMC,GAAG,GAAGC,GAAG,CAAC3B,KAAK,EAAEe,CAAC,CAACF,QAAQ,EAAEQ,QAAQ,CAACI,CAAC,CAAC,CAAC;MAC/C,IAAIC,GAAG,KAAK,GAAG,EAAE;QACf;;MAEFX,CAAC,CAACH,KAAK,IAAIO,YAAY,CAACO,GAAG,CAAC;MAC5B,IAAIX,CAAC,CAACH,KAAK,IAAIR,cAAc,EAAE;QAC7B;;;IAIJ,IAAIoB,aAAa,KAAKT,CAAC,CAACH,KAAK,EAAE;MAC7BS,QAAQ,CAACO,IAAI,CAACb,CAAC,CAACF,QAAQ,CAAC;;EAE7B,CAAC,CAAC;EAEF,OAAOQ,QAAQ;AACjB;AAEA,SAASM,GAAGA,CAAC3B,KAAkB,EAAE6B,CAAS,EAAEJ,CAAS;EACnD,IAAMK,SAAS,GAAG9B,KAAK,CAAC+B,SAAS,EAAE;EACnC,IAAMC,KAAK,GAAGxB,IAAI,CAACC,GAAG,CAACqB,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMI,KAAK,GAAGzB,IAAI,CAACC,GAAG,CAACqB,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMK,KAAK,GAAG1B,IAAI,CAAC2B,GAAG,CAACL,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMO,KAAK,GAAG5B,IAAI,CAAC2B,GAAG,CAACL,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMQ,KAAK,GAAG7B,IAAI,CAACC,GAAG,CAACqB,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMa,KAAK,GAAG9B,IAAI,CAACC,GAAG,CAACqB,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMc,KAAK,GAAG/B,IAAI,CAAC2B,GAAG,CAACL,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMe,KAAK,GAAGhC,IAAI,CAAC2B,GAAG,CAACL,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAMgB,KAAK,GAAG,CAACP,KAAK,GAAGF,KAAK,KAAKI,KAAK,GAAGH,KAAK,CAAC;EAC/C,IAAMS,KAAK,GAAG,CAACH,KAAK,GAAGF,KAAK,KAAKG,KAAK,GAAGF,KAAK,CAAC;EAC/C,IAAIG,KAAK,IAAI,CAAC,IAAIC,KAAK,IAAI,CAAC,EAAE;IAC5B,OAAO,GAAG;;EAEZ,IAAMC,gBAAgB,GAAGnC,IAAI,CAAC2B,GAAG,CAACH,KAAK,EAAEK,KAAK,CAAC;EAC/C,IAAMO,gBAAgB,GAAGpC,IAAI,CAAC2B,GAAG,CAACF,KAAK,EAAEK,KAAK,CAAC;EAC/C,IAAMO,gBAAgB,GAAGrC,IAAI,CAACC,GAAG,CAACyB,KAAK,EAAEK,KAAK,CAAC;EAC/C,IAAMO,gBAAgB,GAAGtC,IAAI,CAACC,GAAG,CAAC2B,KAAK,EAAEI,KAAK,CAAC;EAC/C,IAAMO,gBAAgB,GAClBvC,IAAI,CAAC2B,GAAG,CAACU,gBAAgB,GAAGF,gBAAgB,EAAE,GAAG,CAAC,GAClDnC,IAAI,CAAC2B,GAAG,CAACW,gBAAgB,GAAGF,gBAAgB,EAAE,GAAG,CAAC;EACtD,OAAOG,gBAAgB,IAAIN,KAAK,GAAGC,KAAK,GAAGK,gBAAgB,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}