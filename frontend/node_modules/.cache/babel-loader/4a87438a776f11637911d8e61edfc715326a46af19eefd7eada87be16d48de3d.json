{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 6c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7m0 1c.46 0 .9.06 1.33.15l-2.72 4.7-2.32-3.56C9.31 7.49 10.6 7 12 7m-6 6c0-1.54.59-2.95 1.55-4.01L10.81 14H6.09c-.05-.33-.09-.66-.09-1m.35 2h5.33l-2.03 3.5.11.06c-1.59-.64-2.84-1.94-3.41-3.56M12 19c-.48 0-.94-.06-1.39-.17l2.85-4.92 2.11 3.9c-1 .74-2.23 1.19-3.57 1.19m6-6c0 1.6-.63 3.06-1.66 4.13L13.57 12h4.34c.05.33.09.66.09 1m-5.74-2 2.05-3.54c1.56.65 2.77 1.94 3.34 3.54z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.55 8.99C6.59 10.05 6 11.46 6 13c0 .34.04.67.09 1h4.72zm6.76-1.53L12.26 11h5.39c-.57-1.6-1.78-2.89-3.34-3.54m-.98-.31C12.9 7.06 12.46 7 12 7c-1.4 0-2.69.49-3.71 1.29l2.32 3.56zM11.68 15H6.35c.57 1.62 1.82 2.92 3.41 3.56l-.11-.06zm7.35-7.61 1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61M12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7M9 1h6v2H9zm7.34 16.13C17.37 16.06 18 14.6 18 13c0-.34-.04-.67-.09-1h-4.34zm-5.73 1.7c.45.11.91.17 1.39.17 1.34 0 2.57-.45 3.57-1.19l-2.11-3.9z\"\n}, \"1\")], 'ShutterSpeedTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/@mui/icons-material/esm/ShutterSpeedTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 6c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7m0 1c.46 0 .9.06 1.33.15l-2.72 4.7-2.32-3.56C9.31 7.49 10.6 7 12 7m-6 6c0-1.54.59-2.95 1.55-4.01L10.81 14H6.09c-.05-.33-.09-.66-.09-1m.35 2h5.33l-2.03 3.5.11.06c-1.59-.64-2.84-1.94-3.41-3.56M12 19c-.48 0-.94-.06-1.39-.17l2.85-4.92 2.11 3.9c-1 .74-2.23 1.19-3.57 1.19m6-6c0 1.6-.63 3.06-1.66 4.13L13.57 12h4.34c.05.33.09.66.09 1m-5.74-2 2.05-3.54c1.56.65 2.77 1.94 3.34 3.54z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.55 8.99C6.59 10.05 6 11.46 6 13c0 .34.04.67.09 1h4.72zm6.76-1.53L12.26 11h5.39c-.57-1.6-1.78-2.89-3.34-3.54m-.98-.31C12.9 7.06 12.46 7 12 7c-1.4 0-2.69.49-3.71 1.29l2.32 3.56zM11.68 15H6.35c.57 1.62 1.82 2.92 3.41 3.56l-.11-.06zm7.35-7.61 1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61M12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7M9 1h6v2H9zm7.34 16.13C17.37 16.06 18 14.6 18 13c0-.34-.04-.67-.09-1h-4.34zm-5.73 1.7c.45.11.91.17 1.39.17 1.34 0 2.57-.45 3.57-1.19l-2.11-3.9z\"\n}, \"1\")], 'ShutterSpeedTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,ibAAib;EACpbC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}