{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function fullyConnectedLayer(x, params) {\n  return tf.tidy(function () {\n    return tf.add(tf.matMul(x, params.weights), params.bias);\n  });\n}", "map": {"version": 3, "names": ["tf", "fullyConnectedLayer", "x", "params", "tidy", "add", "<PERSON><PERSON><PERSON>", "weights", "bias"], "sources": ["../../../src/common/fullyConnectedLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAI3C,OAAM,SAAUC,mBAAmBA,CACjCC,CAAc,EACdC,MAAgB;EAEhB,OAAOH,EAAE,CAACI,IAAI,CAAC;IACb,OAAAJ,EAAE,CAACK,GAAG,CACJL,EAAE,CAACM,MAAM,CAACJ,CAAC,EAAEC,MAAM,CAACI,OAAO,CAAC,EAC5BJ,MAAM,CAACK,IAAI,CACZ;EAHD,CAGC,CACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}