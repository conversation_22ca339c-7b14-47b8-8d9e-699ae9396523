{"ast": null, "code": "import { extractFCParamsFactory, extractWeightsFactory } from '../common';\nexport function extractParams(weights, channelsIn, channelsOut) {\n  var paramMappings = [];\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var extractFCParams = extractFCParamsFactory(extractWeights, paramMappings);\n  var fc = extractFCParams(channelsIn, channelsOut, 'fc');\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    paramMappings: paramMappings,\n    params: {\n      fc: fc\n    }\n  };\n}", "map": {"version": 3, "names": ["extractFCParamsFactory", "extractWeightsFactory", "extractParams", "weights", "channelsIn", "channelsOut", "paramMappings", "_a", "extractWeights", "getRemainingWeights", "extractFCParams", "fc", "length", "Error", "params"], "sources": ["../../../src/faceProcessor/extractParams.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,qBAAqB,QAAsB,WAAW;AAGvF,OAAM,SAAUC,aAAaA,CAACC,OAAqB,EAAEC,UAAkB,EAAEC,WAAmB;EAE1F,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAAC,EAAA,GAAAN,qBAAA,CAAAE,OAAA,CAG4B;IAFhCK,cAAA,GAAAD,EAAA,CAAAC,cAAc;IACdC,mBAAA,GAAAF,EAAA,CAAAE,mBACgC;EAElC,IAAMC,eAAe,GAAGV,sBAAsB,CAACQ,cAAc,EAAEF,aAAa,CAAC;EAE7E,IAAMK,EAAE,GAAGD,eAAe,CAACN,UAAU,EAAEC,WAAW,EAAE,IAAI,CAAC;EAEzD,IAAII,mBAAmB,EAAE,CAACG,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCJ,mBAAmB,EAAE,CAACG,MAAQ,CAAC;;EAGnF,OAAO;IACLN,aAAa,EAAAA,aAAA;IACbQ,MAAM,EAAE;MAAEH,EAAE,EAAAA;IAAA;GACb;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}