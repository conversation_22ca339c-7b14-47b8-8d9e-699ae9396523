{"ast": null, "code": "import { Gender } from '../ageGenderNet/types';\nimport { isValidProbablitiy } from '../utils';\nexport function isWithGender(obj) {\n  return (obj['gender'] === Gender.MALE || obj['gender'] === Gender.FEMALE) && isValidProbablitiy(obj['genderProbability']);\n}\nexport function extendWithGender(sourceObj, gender, genderProbability) {\n  var extension = {\n    gender: gender,\n    genderProbability: genderProbability\n  };\n  return Object.assign({}, sourceObj, extension);\n}", "map": {"version": 3, "names": ["Gender", "isValidProbablitiy", "isWithGender", "obj", "MALE", "FEMALE", "extendWithGender", "sourceObj", "gender", "genderProbability", "extension", "Object", "assign"], "sources": ["../../../src/factories/WithGender.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,kBAAkB,QAAQ,UAAU;AAO7C,OAAM,SAAUC,YAAYA,CAACC,GAAQ;EACnC,OAAO,CAACA,GAAG,CAAC,QAAQ,CAAC,KAAKH,MAAM,CAACI,IAAI,IAAID,GAAG,CAAC,QAAQ,CAAC,KAAKH,MAAM,CAACK,MAAM,KACnEJ,kBAAkB,CAACE,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACnD;AAEA,OAAM,SAAUG,gBAAgBA,CAG9BC,SAAkB,EAClBC,MAAc,EACdC,iBAAyB;EAGzB,IAAMC,SAAS,GAAG;IAAEF,MAAM,EAAAA,MAAA;IAAEC,iBAAiB,EAAAA;EAAA,CAAE;EAC/C,OAAOE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEL,SAAS,EAAEG,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}