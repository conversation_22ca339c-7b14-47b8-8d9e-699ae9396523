{"ast": null, "code": "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { FaceDetection } from '../classes';\nimport { TinyYolov2Base } from '../tinyYolov2/TinyYolov2Base';\nimport { BOX_ANCHORS, IOU_THRESHOLD, MEAN_RGB } from './const';\nvar TinyFaceDetector = /** @class */function (_super) {\n  __extends(TinyFaceDetector, _super);\n  function TinyFaceDetector() {\n    var _this = this;\n    var config = {\n      withSeparableConvs: true,\n      iouThreshold: IOU_THRESHOLD,\n      classes: ['face'],\n      anchors: BOX_ANCHORS,\n      meanRgb: MEAN_RGB,\n      isFirstLayerConv2d: true,\n      filterSizes: [3, 16, 32, 64, 128, 256, 512]\n    };\n    _this = _super.call(this, config) || this;\n    return _this;\n  }\n  Object.defineProperty(TinyFaceDetector.prototype, \"anchors\", {\n    get: function () {\n      return this.config.anchors;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  TinyFaceDetector.prototype.locateFaces = function (input, forwardParams) {\n    return __awaiter(this, void 0, void 0, function () {\n      var objectDetections;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.detect(input, forwardParams)];\n          case 1:\n            objectDetections = _a.sent();\n            return [2 /*return*/, objectDetections.map(function (det) {\n              return new FaceDetection(det.score, det.relativeBox, {\n                width: det.imageWidth,\n                height: det.imageHeight\n              });\n            })];\n        }\n      });\n    });\n  };\n  TinyFaceDetector.prototype.getDefaultModelName = function () {\n    return 'tiny_face_detector_model';\n  };\n  TinyFaceDetector.prototype.extractParamsFromWeigthMap = function (weightMap) {\n    return _super.prototype.extractParamsFromWeigthMap.call(this, weightMap);\n  };\n  return TinyFaceDetector;\n}(TinyYolov2Base);\nexport { TinyFaceDetector };", "map": {"version": 3, "names": ["FaceDetection", "TinyYolov2Base", "BOX_ANCHORS", "IOU_THRESHOLD", "MEAN_RGB", "TinyFaceDetector", "_super", "__extends", "_this", "config", "withSeparableConvs", "iouThreshold", "classes", "anchors", "meanRgb", "isFirstLayerConv2d", "filterSizes", "call", "Object", "defineProperty", "prototype", "get", "locateFaces", "input", "forwardParams", "detect", "objectDetections", "_a", "sent", "map", "det", "score", "relativeBox", "width", "imageWidth", "height", "imageHeight", "getDefaultModelName", "extractParamsFromWeigthMap", "weightMap"], "sources": ["../../../src/tinyFaceDetector/TinyFaceDetector.ts"], "sourcesContent": [null], "mappings": ";AAEA,SAASA,aAAa,QAAe,YAAY;AAIjD,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,SAASC,WAAW,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,SAAS;AAE9D,IAAAC,gBAAA,0BAAAC,MAAA;EAAsCC,SAAA,CAAAF,gBAAA,EAAAC,MAAA;EAEpC,SAAAD,iBAAA;IAAA,IAAAG,KAAA;IACE,IAAMC,MAAM,GAAG;MACbC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAER,aAAa;MAC3BS,OAAO,EAAE,CAAC,MAAM,CAAC;MACjBC,OAAO,EAAEX,WAAW;MACpBY,OAAO,EAAEV,QAAQ;MACjBW,kBAAkB,EAAE,IAAI;MACxBC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;KAC3C;IAEDR,KAAA,GAAAF,MAAA,CAAAW,IAAA,OAAMR,MAAM,CAAC;;EACf;EAEAS,MAAA,CAAAC,cAAA,CAAWd,gBAAA,CAAAe,SAAA,WAAO;SAAlB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACZ,MAAM,CAACI,OAAO;IAC5B,CAAC;;;;EAEYR,gBAAA,CAAAe,SAAA,CAAAE,WAAW,GAAxB,UAAyBC,KAAgB,EAAEC,aAAiC;;;;;;YACjD,qBAAM,IAAI,CAACC,MAAM,CAACF,KAAK,EAAEC,aAAa,CAAC;;YAA1DE,gBAAgB,GAAGC,EAAA,CAAAC,IAAA,EAAuC;YAChE,sBAAOF,gBAAgB,CAACG,GAAG,CAAC,UAAAC,GAAG;cAAI,WAAI9B,aAAa,CAAC8B,GAAG,CAACC,KAAK,EAAED,GAAG,CAACE,WAAW,EAAE;gBAAEC,KAAK,EAAEH,GAAG,CAACI,UAAU;gBAAEC,MAAM,EAAEL,GAAG,CAACM;cAAW,CAAE,CAAC;YAAjG,CAAiG,CAAC;;;;GACtI;EAES/B,gBAAA,CAAAe,SAAA,CAAAiB,mBAAmB,GAA7B;IACE,OAAO,0BAA0B;EACnC,CAAC;EAEShC,gBAAA,CAAAe,SAAA,CAAAkB,0BAA0B,GAApC,UAAqCC,SAA4B;IAC/D,OAAOjC,MAAA,CAAAc,SAAA,CAAMkB,0BAA0B,CAAArB,IAAA,OAACsB,SAAS,CAAC;EACpD,CAAC;EACH,OAAAlC,gBAAC;AAAD,CAAC,CAhCqCJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}