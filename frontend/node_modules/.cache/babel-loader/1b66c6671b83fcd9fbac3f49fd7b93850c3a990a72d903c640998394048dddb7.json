{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { leaky } from './leaky';\nexport function depthwiseSeparableConv(x, params) {\n  return tf.tidy(function () {\n    var out = tf.pad(x, [[0, 0], [1, 1], [1, 1], [0, 0]]);\n    out = tf.separableConv2d(out, params.depthwise_filter, params.pointwise_filter, [1, 1], 'valid');\n    out = tf.add(out, params.bias);\n    return leaky(out);\n  });\n}", "map": {"version": 3, "names": ["tf", "leaky", "depthwiseSeparableConv", "x", "params", "tidy", "out", "pad", "separableConv2d", "depthwise_filter", "pointwise_filter", "add", "bias"], "sources": ["../../../src/tinyYolov2/depthwiseSeparableConv.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,KAAK,QAAQ,SAAS;AAE/B,OAAM,SAAUC,sBAAsBA,CAACC,CAAc,EAAEC,MAA2B;EAChF,OAAOJ,EAAE,CAACK,IAAI,CAAC;IACb,IAAIC,GAAG,GAAGN,EAAE,CAACO,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAgB;IAEpEG,GAAG,GAAGN,EAAE,CAACQ,eAAe,CAACF,GAAG,EAAEF,MAAM,CAACK,gBAAgB,EAAEL,MAAM,CAACM,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;IAChGJ,GAAG,GAAGN,EAAE,CAACW,GAAG,CAACL,GAAG,EAAEF,MAAM,CAACQ,IAAI,CAAC;IAE9B,OAAOX,KAAK,CAACK,GAAG,CAAC;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}