{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Contexte d'authentification pour PresencePro avec Supabase\n */\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../config/supabase';\nimport { UserRole } from '../types';\nimport { supabaseService } from '../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * Crée un utilisateur temporaire basé sur l'email\n * Utilisé quand l'utilisateur existe dans Supabase Auth mais pas dans notre table\n */\nfunction createTempUserFromEmail(email, id) {\n  // Déterminer le rôle basé sur l'email\n  let role;\n  let firstName;\n  let lastName;\n  if (email.includes('admin')) {\n    role = UserRole.ADMIN;\n    firstName = 'Admin';\n    lastName = 'PresencePro';\n  } else if (email.includes('martin') || email.includes('teacher')) {\n    role = UserRole.TEACHER;\n    firstName = 'Jean';\n    lastName = 'Martin';\n  } else {\n    role = UserRole.STUDENT;\n    firstName = 'Étudiant';\n    lastName = 'Temporaire';\n  }\n  return {\n    id: id || email,\n    // Utiliser l'ID Supabase ou l'email comme fallback\n    username: email.split('@')[0],\n    email,\n    firstName,\n    lastName,\n    fullName: `${firstName} ${lastName}`,\n    role,\n    roleDisplay: role === UserRole.ADMIN ? 'Administrateur' : role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n    isActive: true,\n    dateJoined: new Date().toISOString()\n  };\n}\n\n// Provider\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [supabaseUser, setSupabaseUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Fonction pour obtenir la route basée sur le rôle\n  const getRoleBasedRoute = () => {\n    if (!user) return '/login';\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return '/admin-dashboard';\n      case UserRole.TEACHER:\n        return '/teacher-dashboard';\n      case UserRole.STUDENT:\n        return '/student-dashboard';\n      default:\n        return '/login';\n    }\n  };\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        console.log('🚀 Initialisation de l\\'authentification...');\n\n        // Obtenir la session actuelle\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = await supabase.auth.getSession();\n        if (sessionError) {\n          console.error('❌ Erreur lors de la récupération de la session:', sessionError);\n          setLoading(false);\n          return;\n        }\n        if (session !== null && session !== void 0 && session.user) {\n          console.log('✅ Session trouvée pour:', session.user.email);\n          setSupabaseUser(session.user);\n\n          // Récupérer les données utilisateur depuis notre base\n          try {\n            console.log('🔍 Récupération du profil pour:', session.user.email);\n            const userData = await supabaseService.getUserByEmail(session.user.email);\n            console.log('✅ Profil trouvé:', userData);\n            setUser(userData);\n          } catch (error) {\n            console.error('❌ Erreur lors de la récupération des données utilisateur:', error);\n            // Créer un utilisateur temporaire pour éviter la redirection\n            const tempUser = createTempUserFromEmail(session.user.email, session.user.id);\n            console.log('🔧 Utilisateur temporaire créé:', tempUser);\n            setUser(tempUser);\n          }\n        } else {\n          console.log('ℹ️ Aucune session active trouvée');\n        }\n      } catch (error) {\n        console.error('❌ Erreur lors de l\\'initialisation de l\\'authentification:', error);\n      } finally {\n        console.log('✅ Initialisation terminée');\n        setLoading(false);\n      }\n    };\n\n    // Délai pour éviter les problèmes de timing\n    const timer = setTimeout(initializeAuth, 100);\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      console.log('🔄 Changement d\\'état d\\'authentification:', event);\n      if (event === 'SIGNED_IN' && session !== null && session !== void 0 && session.user) {\n        console.log('✅ Utilisateur connecté:', session.user.email);\n        setSupabaseUser(session.user);\n        try {\n          const userData = await supabaseService.getUserByEmail(session.user.email);\n          setUser(userData);\n        } catch (error) {\n          console.error('❌ Erreur lors de la récupération des données utilisateur:', error);\n          // Créer un utilisateur temporaire\n          const tempUser = createTempUserFromEmail(session.user.email, session.user.id);\n          setUser(tempUser);\n        }\n      } else if (event === 'SIGNED_OUT') {\n        console.log('👋 Utilisateur déconnecté');\n        setSupabaseUser(null);\n        setUser(null);\n      }\n      setLoading(false);\n    });\n    return () => {\n      clearTimeout(timer);\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  // Fonction de connexion\n  const signIn = async (email, password) => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) {\n        return {\n          user: null,\n          error: error.message\n        };\n      }\n      if (data.user) {\n        setSupabaseUser(data.user);\n        try {\n          console.log('🔍 Recherche du profil utilisateur dans la base...');\n          const userData = await supabaseService.getUserByEmail(data.user.email);\n          console.log('✅ Profil utilisateur trouvé:', userData);\n          setUser(userData);\n          return {\n            user: userData,\n            error: null\n          };\n        } catch (userError) {\n          console.error('❌ Erreur lors de la récupération du profil utilisateur:', userError);\n\n          // Créer un utilisateur temporaire pour permettre la connexion\n          console.log('🔧 Création d\\'un profil utilisateur temporaire...');\n          const tempUser = createTempUserFromEmail(data.user.email, data.user.id);\n          setUser(tempUser);\n          return {\n            user: tempUser,\n            error: null\n          };\n        }\n      }\n      return {\n        user: null,\n        error: 'Erreur de connexion'\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur inconnue'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction d'inscription\n  const signUp = async (email, password, userData) => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password\n      });\n      if (error) {\n        return {\n          user: null,\n          error: error.message\n        };\n      }\n      if (data.user) {\n        // Créer l'utilisateur dans notre base de données\n        try {\n          const newUser = {\n            username: userData.username || email.split('@')[0],\n            email,\n            firstName: userData.firstName || '',\n            lastName: userData.lastName || '',\n            role: userData.role || UserRole.STUDENT,\n            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' : userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n            isActive: true,\n            dateJoined: new Date().toISOString()\n          };\n          const createdUser = await supabaseService.createUser(newUser);\n          setUser(createdUser);\n          return {\n            user: createdUser,\n            error: null\n          };\n        } catch (userError) {\n          return {\n            user: null,\n            error: 'Erreur lors de la création du profil utilisateur'\n          };\n        }\n      }\n      return {\n        user: null,\n        error: 'Erreur lors de l\\'inscription'\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur inconnue'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction de déconnexion\n  const signOut = async () => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSupabaseUser(null);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  };\n\n  // Fonction de mise à jour du profil\n  const updateProfile = async userData => {\n    if (!user) {\n      return {\n        user: null,\n        error: 'Aucun utilisateur connecté'\n      };\n    }\n    try {\n      const updatedUser = await supabaseService.updateUser(user.id, userData);\n      setUser(updatedUser);\n      return {\n        user: updatedUser,\n        error: null\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur de mise à jour'\n      };\n    }\n  };\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n    getRoleBasedRoute\n  };\n\n  // Afficher un loader pendant l'initialisation avec timeout\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Chargement...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          marginTop: '10px',\n          color: '#666'\n        },\n        children: \"V\\xE9rification de l'authentification...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\n_s(AuthProvider, \"D2vDCBuOQTUoDpzCc/919H0GjjI=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "supabase", "UserRole", "supabaseService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "createTempUserFromEmail", "email", "id", "role", "firstName", "lastName", "includes", "ADMIN", "TEACHER", "STUDENT", "username", "split", "fullName", "roleDisplay", "isActive", "dateJoined", "Date", "toISOString", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "supabaseUser", "setSupabaseUser", "loading", "setLoading", "getRoleBasedRoute", "initializeAuth", "console", "log", "data", "session", "error", "sessionError", "auth", "getSession", "userData", "getUserByEmail", "tempUser", "timer", "setTimeout", "subscription", "onAuthStateChange", "event", "clearTimeout", "unsubscribe", "signIn", "password", "signInWithPassword", "message", "userError", "Error", "signUp", "newUser", "trim", "created<PERSON>ser", "createUser", "signOut", "updateProfile", "updatedUser", "updateUser", "value", "isAuthenticated", "style", "display", "flexDirection", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "color", "Provider", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["/**\n * Contexte d'authentification pour PresencePro avec Supabase\n */\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { supabase } from '../config/supabase';\nimport { User as SupabaseUser } from '@supabase/supabase-js';\nimport { User, UserRole } from '../types';\nimport { supabaseService } from '../services/supabaseService';\n\ninterface AuthContextType {\n  user: User | null;\n  supabaseUser: SupabaseUser | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;\n  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;\n  signOut: () => Promise<void>;\n  updateProfile: (userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;\n  getRoleBasedRoute: () => string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n/**\n * Crée un utilisateur temporaire basé sur l'email\n * Utilisé quand l'utilisateur existe dans Supabase Auth mais pas dans notre table\n */\nfunction createTempUserFromEmail(email: string, id?: string): User {\n  // Déterminer le rôle basé sur l'email\n  let role: UserRole;\n  let firstName: string;\n  let lastName: string;\n\n  if (email.includes('admin')) {\n    role = UserRole.ADMIN;\n    firstName = 'Admin';\n    lastName = 'PresencePro';\n  } else if (email.includes('martin') || email.includes('teacher')) {\n    role = UserRole.TEACHER;\n    firstName = 'Jean';\n    lastName = 'Martin';\n  } else {\n    role = UserRole.STUDENT;\n    firstName = 'Étudiant';\n    lastName = 'Temporaire';\n  }\n\n  return {\n    id: id || email, // Utiliser l'ID Supabase ou l'email comme fallback\n    username: email.split('@')[0],\n    email,\n    firstName,\n    lastName,\n    fullName: `${firstName} ${lastName}`,\n    role,\n    roleDisplay: role === UserRole.ADMIN ? 'Administrateur' :\n                role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n    isActive: true,\n    dateJoined: new Date().toISOString()\n  };\n}\n\n// Provider\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Fonction pour obtenir la route basée sur le rôle\n  const getRoleBasedRoute = (): string => {\n    if (!user) return '/login';\n\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return '/admin-dashboard';\n      case UserRole.TEACHER:\n        return '/teacher-dashboard';\n      case UserRole.STUDENT:\n        return '/student-dashboard';\n      default:\n        return '/login';\n    }\n  };\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        console.log('🚀 Initialisation de l\\'authentification...');\n\n        // Obtenir la session actuelle\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n\n        if (sessionError) {\n          console.error('❌ Erreur lors de la récupération de la session:', sessionError);\n          setLoading(false);\n          return;\n        }\n\n        if (session?.user) {\n          console.log('✅ Session trouvée pour:', session.user.email);\n          setSupabaseUser(session.user);\n\n          // Récupérer les données utilisateur depuis notre base\n          try {\n            console.log('🔍 Récupération du profil pour:', session.user.email);\n            const userData = await supabaseService.getUserByEmail(session.user.email!);\n            console.log('✅ Profil trouvé:', userData);\n            setUser(userData);\n          } catch (error) {\n            console.error('❌ Erreur lors de la récupération des données utilisateur:', error);\n            // Créer un utilisateur temporaire pour éviter la redirection\n            const tempUser = createTempUserFromEmail(session.user.email!, session.user.id);\n            console.log('🔧 Utilisateur temporaire créé:', tempUser);\n            setUser(tempUser);\n          }\n        } else {\n          console.log('ℹ️ Aucune session active trouvée');\n        }\n      } catch (error) {\n        console.error('❌ Erreur lors de l\\'initialisation de l\\'authentification:', error);\n      } finally {\n        console.log('✅ Initialisation terminée');\n        setLoading(false);\n      }\n    };\n\n    // Délai pour éviter les problèmes de timing\n    const timer = setTimeout(initializeAuth, 100);\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('🔄 Changement d\\'état d\\'authentification:', event);\n\n        if (event === 'SIGNED_IN' && session?.user) {\n          console.log('✅ Utilisateur connecté:', session.user.email);\n          setSupabaseUser(session.user);\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email!);\n            setUser(userData);\n          } catch (error) {\n            console.error('❌ Erreur lors de la récupération des données utilisateur:', error);\n            // Créer un utilisateur temporaire\n            const tempUser = createTempUserFromEmail(session.user.email!, session.user.id);\n            setUser(tempUser);\n          }\n        } else if (event === 'SIGNED_OUT') {\n          console.log('👋 Utilisateur déconnecté');\n          setSupabaseUser(null);\n          setUser(null);\n        }\n        setLoading(false);\n      }\n    );\n\n    return () => {\n      clearTimeout(timer);\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  // Fonction de connexion\n  const signIn = async (email: string, password: string): Promise<{ user: User | null; error: string | null }> => {\n    try {\n      setLoading(true);\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { user: null, error: error.message };\n      }\n\n      if (data.user) {\n        setSupabaseUser(data.user);\n\n        try {\n          console.log('🔍 Recherche du profil utilisateur dans la base...');\n          const userData = await supabaseService.getUserByEmail(data.user.email!);\n          console.log('✅ Profil utilisateur trouvé:', userData);\n          setUser(userData);\n          return { user: userData, error: null };\n        } catch (userError) {\n          console.error('❌ Erreur lors de la récupération du profil utilisateur:', userError);\n\n          // Créer un utilisateur temporaire pour permettre la connexion\n          console.log('🔧 Création d\\'un profil utilisateur temporaire...');\n          const tempUser = createTempUserFromEmail(data.user.email!, data.user.id);\n          setUser(tempUser);\n          return { user: tempUser, error: null };\n        }\n      }\n\n      return { user: null, error: 'Erreur de connexion' };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction d'inscription\n  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {\n    try {\n      setLoading(true);\n\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { user: null, error: error.message };\n      }\n\n      if (data.user) {\n        // Créer l'utilisateur dans notre base de données\n        try {\n          const newUser: Omit<User, 'id'> = {\n            username: userData.username || email.split('@')[0],\n            email,\n            firstName: userData.firstName || '',\n            lastName: userData.lastName || '',\n            role: userData.role || UserRole.STUDENT,\n            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' :\n                        userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n            isActive: true,\n            dateJoined: new Date().toISOString(),\n          };\n\n          const createdUser = await supabaseService.createUser(newUser);\n          setUser(createdUser);\n          return { user: createdUser, error: null };\n        } catch (userError) {\n          return { user: null, error: 'Erreur lors de la création du profil utilisateur' };\n        }\n      }\n\n      return { user: null, error: 'Erreur lors de l\\'inscription' };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction de déconnexion\n  const signOut = async (): Promise<void> => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSupabaseUser(null);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  };\n\n  // Fonction de mise à jour du profil\n  const updateProfile = async (userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {\n    if (!user) {\n      return { user: null, error: 'Aucun utilisateur connecté' };\n    }\n\n    try {\n      const updatedUser = await supabaseService.updateUser(user.id, userData);\n      setUser(updatedUser);\n      return { user: updatedUser, error: null };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur de mise à jour' };\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    supabaseUser,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n    getRoleBasedRoute,\n  };\n\n  // Afficher un loader pendant l'initialisation avec timeout\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      }}>\n        <div>Chargement...</div>\n        <div style={{ fontSize: '14px', marginTop: '10px', color: '#666' }}>\n          Vérification de l'authentification...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAAeC,QAAQ,QAAQ,UAAU;AACzC,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc9D,MAAMC,WAAW,gBAAGT,aAAa,CAA8BU,SAAS,CAAC;;AAEzE;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACC,KAAa,EAAEC,EAAW,EAAQ;EACjE;EACA,IAAIC,IAAc;EAClB,IAAIC,SAAiB;EACrB,IAAIC,QAAgB;EAEpB,IAAIJ,KAAK,CAACK,QAAQ,CAAC,OAAO,CAAC,EAAE;IAC3BH,IAAI,GAAGT,QAAQ,CAACa,KAAK;IACrBH,SAAS,GAAG,OAAO;IACnBC,QAAQ,GAAG,aAAa;EAC1B,CAAC,MAAM,IAAIJ,KAAK,CAACK,QAAQ,CAAC,QAAQ,CAAC,IAAIL,KAAK,CAACK,QAAQ,CAAC,SAAS,CAAC,EAAE;IAChEH,IAAI,GAAGT,QAAQ,CAACc,OAAO;IACvBJ,SAAS,GAAG,MAAM;IAClBC,QAAQ,GAAG,QAAQ;EACrB,CAAC,MAAM;IACLF,IAAI,GAAGT,QAAQ,CAACe,OAAO;IACvBL,SAAS,GAAG,UAAU;IACtBC,QAAQ,GAAG,YAAY;EACzB;EAEA,OAAO;IACLH,EAAE,EAAEA,EAAE,IAAID,KAAK;IAAE;IACjBS,QAAQ,EAAET,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7BV,KAAK;IACLG,SAAS;IACTC,QAAQ;IACRO,QAAQ,EAAE,GAAGR,SAAS,IAAIC,QAAQ,EAAE;IACpCF,IAAI;IACJU,WAAW,EAAEV,IAAI,KAAKT,QAAQ,CAACa,KAAK,GAAG,gBAAgB,GAC3CJ,IAAI,KAAKT,QAAQ,CAACc,OAAO,GAAG,YAAY,GAAG,UAAU;IACjEM,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACrC,CAAC;AACH;;AAEA;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMoC,iBAAiB,GAAGA,CAAA,KAAc;IACtC,IAAI,CAACN,IAAI,EAAE,OAAO,QAAQ;IAE1B,QAAQA,IAAI,CAAClB,IAAI;MACf,KAAKT,QAAQ,CAACa,KAAK;QACjB,OAAO,kBAAkB;MAC3B,KAAKb,QAAQ,CAACc,OAAO;QACnB,OAAO,oBAAoB;MAC7B,KAAKd,QAAQ,CAACe,OAAO;QACnB,OAAO,oBAAoB;MAC7B;QACE,OAAO,QAAQ;IACnB;EACF,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMoC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;QAE1D;QACA,MAAM;UAAEC,IAAI,EAAE;YAAEC;UAAQ,CAAC;UAAEC,KAAK,EAAEC;QAAa,CAAC,GAAG,MAAMzC,QAAQ,CAAC0C,IAAI,CAACC,UAAU,CAAC,CAAC;QAEnF,IAAIF,YAAY,EAAE;UAChBL,OAAO,CAACI,KAAK,CAAC,iDAAiD,EAAEC,YAAY,CAAC;UAC9ER,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,IAAIM,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEX,IAAI,EAAE;UACjBQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEE,OAAO,CAACX,IAAI,CAACpB,KAAK,CAAC;UAC1DuB,eAAe,CAACQ,OAAO,CAACX,IAAI,CAAC;;UAE7B;UACA,IAAI;YACFQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,OAAO,CAACX,IAAI,CAACpB,KAAK,CAAC;YAClE,MAAMoC,QAAQ,GAAG,MAAM1C,eAAe,CAAC2C,cAAc,CAACN,OAAO,CAACX,IAAI,CAACpB,KAAM,CAAC;YAC1E4B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,QAAQ,CAAC;YACzCf,OAAO,CAACe,QAAQ,CAAC;UACnB,CAAC,CAAC,OAAOJ,KAAK,EAAE;YACdJ,OAAO,CAACI,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;YACjF;YACA,MAAMM,QAAQ,GAAGvC,uBAAuB,CAACgC,OAAO,CAACX,IAAI,CAACpB,KAAK,EAAG+B,OAAO,CAACX,IAAI,CAACnB,EAAE,CAAC;YAC9E2B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAES,QAAQ,CAAC;YACxDjB,OAAO,CAACiB,QAAQ,CAAC;UACnB;QACF,CAAC,MAAM;UACLV,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QACjD;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;MACpF,CAAC,SAAS;QACRJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,MAAMc,KAAK,GAAGC,UAAU,CAACb,cAAc,EAAE,GAAG,CAAC;;IAE7C;IACA,MAAM;MAAEG,IAAI,EAAE;QAAEW;MAAa;IAAE,CAAC,GAAGjD,QAAQ,CAAC0C,IAAI,CAACQ,iBAAiB,CAChE,OAAOC,KAAK,EAAEZ,OAAO,KAAK;MACxBH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEc,KAAK,CAAC;MAEhE,IAAIA,KAAK,KAAK,WAAW,IAAIZ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEX,IAAI,EAAE;QAC1CQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEE,OAAO,CAACX,IAAI,CAACpB,KAAK,CAAC;QAC1DuB,eAAe,CAACQ,OAAO,CAACX,IAAI,CAAC;QAC7B,IAAI;UACF,MAAMgB,QAAQ,GAAG,MAAM1C,eAAe,CAAC2C,cAAc,CAACN,OAAO,CAACX,IAAI,CAACpB,KAAM,CAAC;UAC1EqB,OAAO,CAACe,QAAQ,CAAC;QACnB,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdJ,OAAO,CAACI,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;UACjF;UACA,MAAMM,QAAQ,GAAGvC,uBAAuB,CAACgC,OAAO,CAACX,IAAI,CAACpB,KAAK,EAAG+B,OAAO,CAACX,IAAI,CAACnB,EAAE,CAAC;UAC9EoB,OAAO,CAACiB,QAAQ,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,KAAK,KAAK,YAAY,EAAE;QACjCf,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCN,eAAe,CAAC,IAAI,CAAC;QACrBF,OAAO,CAAC,IAAI,CAAC;MACf;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAM;MACXmB,YAAY,CAACL,KAAK,CAAC;MACnBE,YAAY,CAACI,WAAW,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,MAAM,GAAG,MAAAA,CAAO9C,KAAa,EAAE+C,QAAgB,KAA2D;IAC9G,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM;QAAEK,IAAI;QAAEE;MAAM,CAAC,GAAG,MAAMxC,QAAQ,CAAC0C,IAAI,CAACc,kBAAkB,CAAC;QAC7DhD,KAAK;QACL+C;MACF,CAAC,CAAC;MAEF,IAAIf,KAAK,EAAE;QACT,OAAO;UAAEZ,IAAI,EAAE,IAAI;UAAEY,KAAK,EAAEA,KAAK,CAACiB;QAAQ,CAAC;MAC7C;MAEA,IAAInB,IAAI,CAACV,IAAI,EAAE;QACbG,eAAe,CAACO,IAAI,CAACV,IAAI,CAAC;QAE1B,IAAI;UACFQ,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAMO,QAAQ,GAAG,MAAM1C,eAAe,CAAC2C,cAAc,CAACP,IAAI,CAACV,IAAI,CAACpB,KAAM,CAAC;UACvE4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEO,QAAQ,CAAC;UACrDf,OAAO,CAACe,QAAQ,CAAC;UACjB,OAAO;YAAEhB,IAAI,EAAEgB,QAAQ;YAAEJ,KAAK,EAAE;UAAK,CAAC;QACxC,CAAC,CAAC,OAAOkB,SAAS,EAAE;UAClBtB,OAAO,CAACI,KAAK,CAAC,yDAAyD,EAAEkB,SAAS,CAAC;;UAEnF;UACAtB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAMS,QAAQ,GAAGvC,uBAAuB,CAAC+B,IAAI,CAACV,IAAI,CAACpB,KAAK,EAAG8B,IAAI,CAACV,IAAI,CAACnB,EAAE,CAAC;UACxEoB,OAAO,CAACiB,QAAQ,CAAC;UACjB,OAAO;YAAElB,IAAI,EAAEkB,QAAQ;YAAEN,KAAK,EAAE;UAAK,CAAC;QACxC;MACF;MAEA,OAAO;QAAEZ,IAAI,EAAE,IAAI;QAAEY,KAAK,EAAE;MAAsB,CAAC;IACrD,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEZ,IAAI,EAAE,IAAI;QAAEY,KAAK,EAAEA,KAAK,YAAYmB,KAAK,GAAGnB,KAAK,CAACiB,OAAO,GAAG;MAAkB,CAAC;IAC1F,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2B,MAAM,GAAG,MAAAA,CAAOpD,KAAa,EAAE+C,QAAgB,EAAEX,QAAuB,KAA2D;IACvI,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM;QAAEK,IAAI;QAAEE;MAAM,CAAC,GAAG,MAAMxC,QAAQ,CAAC0C,IAAI,CAACkB,MAAM,CAAC;QACjDpD,KAAK;QACL+C;MACF,CAAC,CAAC;MAEF,IAAIf,KAAK,EAAE;QACT,OAAO;UAAEZ,IAAI,EAAE,IAAI;UAAEY,KAAK,EAAEA,KAAK,CAACiB;QAAQ,CAAC;MAC7C;MAEA,IAAInB,IAAI,CAACV,IAAI,EAAE;QACb;QACA,IAAI;UACF,MAAMiC,OAAyB,GAAG;YAChC5C,QAAQ,EAAE2B,QAAQ,CAAC3B,QAAQ,IAAIT,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClDV,KAAK;YACLG,SAAS,EAAEiC,QAAQ,CAACjC,SAAS,IAAI,EAAE;YACnCC,QAAQ,EAAEgC,QAAQ,CAAChC,QAAQ,IAAI,EAAE;YACjCF,IAAI,EAAEkC,QAAQ,CAAClC,IAAI,IAAIT,QAAQ,CAACe,OAAO;YACvCG,QAAQ,EAAE,GAAGyB,QAAQ,CAACjC,SAAS,IAAI,EAAE,IAAIiC,QAAQ,CAAChC,QAAQ,IAAI,EAAE,EAAE,CAACkD,IAAI,CAAC,CAAC;YACzE1C,WAAW,EAAEwB,QAAQ,CAAClC,IAAI,KAAKT,QAAQ,CAACa,KAAK,GAAG,gBAAgB,GACpD8B,QAAQ,CAAClC,IAAI,KAAKT,QAAQ,CAACc,OAAO,GAAG,YAAY,GAAG,UAAU;YAC1EM,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACrC,CAAC;UAED,MAAMuC,WAAW,GAAG,MAAM7D,eAAe,CAAC8D,UAAU,CAACH,OAAO,CAAC;UAC7DhC,OAAO,CAACkC,WAAW,CAAC;UACpB,OAAO;YAAEnC,IAAI,EAAEmC,WAAW;YAAEvB,KAAK,EAAE;UAAK,CAAC;QAC3C,CAAC,CAAC,OAAOkB,SAAS,EAAE;UAClB,OAAO;YAAE9B,IAAI,EAAE,IAAI;YAAEY,KAAK,EAAE;UAAmD,CAAC;QAClF;MACF;MAEA,OAAO;QAAEZ,IAAI,EAAE,IAAI;QAAEY,KAAK,EAAE;MAAgC,CAAC;IAC/D,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEZ,IAAI,EAAE,IAAI;QAAEY,KAAK,EAAEA,KAAK,YAAYmB,KAAK,GAAGnB,KAAK,CAACiB,OAAO,GAAG;MAAkB,CAAC;IAC1F,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgC,OAAO,GAAG,MAAAA,CAAA,KAA2B;IACzC,IAAI;MACF,MAAMjE,QAAQ,CAAC0C,IAAI,CAACuB,OAAO,CAAC,CAAC;MAC7BpC,OAAO,CAAC,IAAI,CAAC;MACbE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM0B,aAAa,GAAG,MAAOtB,QAAuB,IAA2D;IAC7G,IAAI,CAAChB,IAAI,EAAE;MACT,OAAO;QAAEA,IAAI,EAAE,IAAI;QAAEY,KAAK,EAAE;MAA6B,CAAC;IAC5D;IAEA,IAAI;MACF,MAAM2B,WAAW,GAAG,MAAMjE,eAAe,CAACkE,UAAU,CAACxC,IAAI,CAACnB,EAAE,EAAEmC,QAAQ,CAAC;MACvEf,OAAO,CAACsC,WAAW,CAAC;MACpB,OAAO;QAAEvC,IAAI,EAAEuC,WAAW;QAAE3B,KAAK,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEZ,IAAI,EAAE,IAAI;QAAEY,KAAK,EAAEA,KAAK,YAAYmB,KAAK,GAAGnB,KAAK,CAACiB,OAAO,GAAG;MAAwB,CAAC;IAChG;EACF,CAAC;EAED,MAAMY,KAAsB,GAAG;IAC7BzC,IAAI;IACJE,YAAY;IACZE,OAAO;IACPsC,eAAe,EAAE,CAAC,CAAC1C,IAAI;IACvB0B,MAAM;IACNM,MAAM;IACNK,OAAO;IACPC,aAAa;IACbhC;EACF,CAAC;;EAED;EACA,IAAIF,OAAO,EAAE;IACX,oBACE5B,OAAA;MAAKmE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAnD,QAAA,gBACAtB,OAAA;QAAAsB,QAAA,EAAK;MAAa;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxB7E,OAAA;QAAKmE,KAAK,EAAE;UAAEM,QAAQ,EAAE,MAAM;UAAEK,SAAS,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAzD,QAAA,EAAC;MAEpE;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA,CAACC,WAAW,CAAC+E,QAAQ;IAACf,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAAoD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAtD,EAAA,CA3PaF,YAAyC;AAAA4D,EAAA,GAAzC5D,YAAyC;AA4PtD,OAAO,MAAM6D,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAG3F,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAImF,OAAO,KAAKlF,SAAS,EAAE;IACzB,MAAM,IAAIqD,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAO6B,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAejF,WAAW;AAAC,IAAAgF,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}