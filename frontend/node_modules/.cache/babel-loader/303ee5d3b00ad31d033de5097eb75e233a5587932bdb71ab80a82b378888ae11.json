{"ast": null, "code": "import { disposeUnusedWeightTensors, extractWeightEntryFactory } from '../common';\nimport { isTensor3D } from '../utils';\nfunction extractorsFactory(weightMap, paramMappings) {\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  function extractPointwiseConvParams(prefix, idx, mappedPrefix) {\n    var filters = extractWeightEntry(prefix + \"/Conv2d_\" + idx + \"_pointwise/weights\", 4, mappedPrefix + \"/filters\");\n    var batch_norm_offset = extractWeightEntry(prefix + \"/Conv2d_\" + idx + \"_pointwise/convolution_bn_offset\", 1, mappedPrefix + \"/batch_norm_offset\");\n    return {\n      filters: filters,\n      batch_norm_offset: batch_norm_offset\n    };\n  }\n  function extractConvPairParams(idx) {\n    var mappedPrefix = \"mobilenetv1/conv_\" + idx;\n    var prefixDepthwiseConv = \"MobilenetV1/Conv2d_\" + idx + \"_depthwise\";\n    var mappedPrefixDepthwiseConv = mappedPrefix + \"/depthwise_conv\";\n    var mappedPrefixPointwiseConv = mappedPrefix + \"/pointwise_conv\";\n    var filters = extractWeightEntry(prefixDepthwiseConv + \"/depthwise_weights\", 4, mappedPrefixDepthwiseConv + \"/filters\");\n    var batch_norm_scale = extractWeightEntry(prefixDepthwiseConv + \"/BatchNorm/gamma\", 1, mappedPrefixDepthwiseConv + \"/batch_norm_scale\");\n    var batch_norm_offset = extractWeightEntry(prefixDepthwiseConv + \"/BatchNorm/beta\", 1, mappedPrefixDepthwiseConv + \"/batch_norm_offset\");\n    var batch_norm_mean = extractWeightEntry(prefixDepthwiseConv + \"/BatchNorm/moving_mean\", 1, mappedPrefixDepthwiseConv + \"/batch_norm_mean\");\n    var batch_norm_variance = extractWeightEntry(prefixDepthwiseConv + \"/BatchNorm/moving_variance\", 1, mappedPrefixDepthwiseConv + \"/batch_norm_variance\");\n    return {\n      depthwise_conv: {\n        filters: filters,\n        batch_norm_scale: batch_norm_scale,\n        batch_norm_offset: batch_norm_offset,\n        batch_norm_mean: batch_norm_mean,\n        batch_norm_variance: batch_norm_variance\n      },\n      pointwise_conv: extractPointwiseConvParams('MobilenetV1', idx, mappedPrefixPointwiseConv)\n    };\n  }\n  function extractMobilenetV1Params() {\n    return {\n      conv_0: extractPointwiseConvParams('MobilenetV1', 0, 'mobilenetv1/conv_0'),\n      conv_1: extractConvPairParams(1),\n      conv_2: extractConvPairParams(2),\n      conv_3: extractConvPairParams(3),\n      conv_4: extractConvPairParams(4),\n      conv_5: extractConvPairParams(5),\n      conv_6: extractConvPairParams(6),\n      conv_7: extractConvPairParams(7),\n      conv_8: extractConvPairParams(8),\n      conv_9: extractConvPairParams(9),\n      conv_10: extractConvPairParams(10),\n      conv_11: extractConvPairParams(11),\n      conv_12: extractConvPairParams(12),\n      conv_13: extractConvPairParams(13)\n    };\n  }\n  function extractConvParams(prefix, mappedPrefix) {\n    var filters = extractWeightEntry(prefix + \"/weights\", 4, mappedPrefix + \"/filters\");\n    var bias = extractWeightEntry(prefix + \"/biases\", 1, mappedPrefix + \"/bias\");\n    return {\n      filters: filters,\n      bias: bias\n    };\n  }\n  function extractBoxPredictorParams(idx) {\n    var box_encoding_predictor = extractConvParams(\"Prediction/BoxPredictor_\" + idx + \"/BoxEncodingPredictor\", \"prediction_layer/box_predictor_\" + idx + \"/box_encoding_predictor\");\n    var class_predictor = extractConvParams(\"Prediction/BoxPredictor_\" + idx + \"/ClassPredictor\", \"prediction_layer/box_predictor_\" + idx + \"/class_predictor\");\n    return {\n      box_encoding_predictor: box_encoding_predictor,\n      class_predictor: class_predictor\n    };\n  }\n  function extractPredictionLayerParams() {\n    return {\n      conv_0: extractPointwiseConvParams('Prediction', 0, 'prediction_layer/conv_0'),\n      conv_1: extractPointwiseConvParams('Prediction', 1, 'prediction_layer/conv_1'),\n      conv_2: extractPointwiseConvParams('Prediction', 2, 'prediction_layer/conv_2'),\n      conv_3: extractPointwiseConvParams('Prediction', 3, 'prediction_layer/conv_3'),\n      conv_4: extractPointwiseConvParams('Prediction', 4, 'prediction_layer/conv_4'),\n      conv_5: extractPointwiseConvParams('Prediction', 5, 'prediction_layer/conv_5'),\n      conv_6: extractPointwiseConvParams('Prediction', 6, 'prediction_layer/conv_6'),\n      conv_7: extractPointwiseConvParams('Prediction', 7, 'prediction_layer/conv_7'),\n      box_predictor_0: extractBoxPredictorParams(0),\n      box_predictor_1: extractBoxPredictorParams(1),\n      box_predictor_2: extractBoxPredictorParams(2),\n      box_predictor_3: extractBoxPredictorParams(3),\n      box_predictor_4: extractBoxPredictorParams(4),\n      box_predictor_5: extractBoxPredictorParams(5)\n    };\n  }\n  return {\n    extractMobilenetV1Params: extractMobilenetV1Params,\n    extractPredictionLayerParams: extractPredictionLayerParams\n  };\n}\nexport function extractParamsFromWeigthMap(weightMap) {\n  var paramMappings = [];\n  var _a = extractorsFactory(weightMap, paramMappings),\n    extractMobilenetV1Params = _a.extractMobilenetV1Params,\n    extractPredictionLayerParams = _a.extractPredictionLayerParams;\n  var extra_dim = weightMap['Output/extra_dim'];\n  paramMappings.push({\n    originalPath: 'Output/extra_dim',\n    paramPath: 'output_layer/extra_dim'\n  });\n  if (!isTensor3D(extra_dim)) {\n    throw new Error(\"expected weightMap['Output/extra_dim'] to be a Tensor3D, instead have \" + extra_dim);\n  }\n  var params = {\n    mobilenetv1: extractMobilenetV1Params(),\n    prediction_layer: extractPredictionLayerParams(),\n    output_layer: {\n      extra_dim: extra_dim\n    }\n  };\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "extractWeightEntryFactory", "isTensor3D", "extractorsFactory", "weightMap", "paramMappings", "extractWeightEntry", "extractPointwiseConvParams", "prefix", "idx", "mappedPrefix", "filters", "batch_norm_offset", "extractConvPairParams", "prefixDepthwiseConv", "mappedPrefixDepthwiseConv", "mappedPrefixPointwiseConv", "batch_norm_scale", "batch_norm_mean", "batch_norm_variance", "depthwise_conv", "pointwise_conv", "extractMobilenetV1Params", "conv_0", "conv_1", "conv_2", "conv_3", "conv_4", "conv_5", "conv_6", "conv_7", "conv_8", "conv_9", "conv_10", "conv_11", "conv_12", "conv_13", "extractConvParams", "bias", "extractBoxPredictorParams", "box_encoding_predictor", "class_predictor", "extractPredictionLayerParams", "box_predictor_0", "box_predictor_1", "box_predictor_2", "box_predictor_3", "box_predictor_4", "box_predictor_5", "extractParamsFromWeigthMap", "_a", "extra_dim", "push", "originalPath", "<PERSON><PERSON><PERSON><PERSON>", "Error", "params", "mobilenetv1", "prediction_layer", "output_layer"], "sources": ["../../../src/ssdMobilenetv1/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": "AAEA,SAAqBA,0BAA0B,EAAEC,yBAAyB,QAAsB,WAAW;AAC3G,SAASC,UAAU,QAAQ,UAAU;AAGrC,SAASC,iBAAiBA,CAACC,SAAc,EAAEC,aAA6B;EAEtE,IAAMC,kBAAkB,GAAGL,yBAAyB,CAACG,SAAS,EAAEC,aAAa,CAAC;EAE9E,SAASE,0BAA0BA,CAACC,MAAc,EAAEC,GAAW,EAAEC,YAAoB;IAEnF,IAAMC,OAAO,GAAGL,kBAAkB,CAAiBE,MAAM,gBAAWC,GAAG,uBAAoB,EAAE,CAAC,EAAKC,YAAY,aAAU,CAAC;IAC1H,IAAME,iBAAiB,GAAGN,kBAAkB,CAAiBE,MAAM,gBAAWC,GAAG,qCAAkC,EAAE,CAAC,EAAKC,YAAY,uBAAoB,CAAC;IAE5J,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEC,iBAAiB,EAAAA;IAAA,CAAE;EACvC;EAEA,SAASC,qBAAqBA,CAACJ,GAAW;IAExC,IAAMC,YAAY,GAAG,sBAAoBD,GAAK;IAC9C,IAAMK,mBAAmB,GAAG,wBAAsBL,GAAG,eAAY;IACjE,IAAMM,yBAAyB,GAAML,YAAY,oBAAiB;IAClE,IAAMM,yBAAyB,GAAMN,YAAY,oBAAiB;IAElE,IAAMC,OAAO,GAAGL,kBAAkB,CAAiBQ,mBAAmB,uBAAoB,EAAE,CAAC,EAAKC,yBAAyB,aAAU,CAAC;IACtI,IAAME,gBAAgB,GAAGX,kBAAkB,CAAiBQ,mBAAmB,qBAAkB,EAAE,CAAC,EAAKC,yBAAyB,sBAAmB,CAAC;IACtJ,IAAMH,iBAAiB,GAAGN,kBAAkB,CAAiBQ,mBAAmB,oBAAiB,EAAE,CAAC,EAAKC,yBAAyB,uBAAoB,CAAC;IACvJ,IAAMG,eAAe,GAAGZ,kBAAkB,CAAiBQ,mBAAmB,2BAAwB,EAAE,CAAC,EAAKC,yBAAyB,qBAAkB,CAAC;IAC1J,IAAMI,mBAAmB,GAAGb,kBAAkB,CAAiBQ,mBAAmB,+BAA4B,EAAE,CAAC,EAAKC,yBAAyB,yBAAsB,CAAC;IAEtK,OAAO;MACLK,cAAc,EAAE;QACdT,OAAO,EAAAA,OAAA;QACPM,gBAAgB,EAAAA,gBAAA;QAChBL,iBAAiB,EAAAA,iBAAA;QACjBM,eAAe,EAAAA,eAAA;QACfC,mBAAmB,EAAAA;OACpB;MACDE,cAAc,EAAEd,0BAA0B,CAAC,aAAa,EAAEE,GAAG,EAAEO,yBAAyB;KACzF;EACH;EAEA,SAASM,wBAAwBA,CAAA;IAC/B,OAAO;MACLC,MAAM,EAAEhB,0BAA0B,CAAC,aAAa,EAAE,CAAC,EAAE,oBAAoB,CAAC;MAC1EiB,MAAM,EAAEX,qBAAqB,CAAC,CAAC,CAAC;MAChCY,MAAM,EAAEZ,qBAAqB,CAAC,CAAC,CAAC;MAChCa,MAAM,EAAEb,qBAAqB,CAAC,CAAC,CAAC;MAChCc,MAAM,EAAEd,qBAAqB,CAAC,CAAC,CAAC;MAChCe,MAAM,EAAEf,qBAAqB,CAAC,CAAC,CAAC;MAChCgB,MAAM,EAAEhB,qBAAqB,CAAC,CAAC,CAAC;MAChCiB,MAAM,EAAEjB,qBAAqB,CAAC,CAAC,CAAC;MAChCkB,MAAM,EAAElB,qBAAqB,CAAC,CAAC,CAAC;MAChCmB,MAAM,EAAEnB,qBAAqB,CAAC,CAAC,CAAC;MAChCoB,OAAO,EAAEpB,qBAAqB,CAAC,EAAE,CAAC;MAClCqB,OAAO,EAAErB,qBAAqB,CAAC,EAAE,CAAC;MAClCsB,OAAO,EAAEtB,qBAAqB,CAAC,EAAE,CAAC;MAClCuB,OAAO,EAAEvB,qBAAqB,CAAC,EAAE;KAClC;EACH;EAEA,SAASwB,iBAAiBA,CAAC7B,MAAc,EAAEE,YAAoB;IAC7D,IAAMC,OAAO,GAAGL,kBAAkB,CAAiBE,MAAM,aAAU,EAAE,CAAC,EAAKE,YAAY,aAAU,CAAC;IAClG,IAAM4B,IAAI,GAAGhC,kBAAkB,CAAiBE,MAAM,YAAS,EAAE,CAAC,EAAKE,YAAY,UAAO,CAAC;IAE3F,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAE2B,IAAI,EAAAA;IAAA,CAAE;EAC1B;EAEA,SAASC,yBAAyBA,CAAC9B,GAAW;IAE5C,IAAM+B,sBAAsB,GAAGH,iBAAiB,CAC9C,6BAA2B5B,GAAG,0BAAuB,EACrD,oCAAkCA,GAAG,4BAAyB,CAC/D;IACD,IAAMgC,eAAe,GAAGJ,iBAAiB,CACvC,6BAA2B5B,GAAG,oBAAiB,EAC/C,oCAAkCA,GAAG,qBAAkB,CACxD;IAED,OAAO;MAAE+B,sBAAsB,EAAAA,sBAAA;MAAEC,eAAe,EAAAA;IAAA,CAAE;EACpD;EAEA,SAASC,4BAA4BA,CAAA;IACnC,OAAO;MACLnB,MAAM,EAAEhB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EiB,MAAM,EAAEjB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EkB,MAAM,EAAElB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EmB,MAAM,EAAEnB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EoB,MAAM,EAAEpB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EqB,MAAM,EAAErB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EsB,MAAM,EAAEtB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EuB,MAAM,EAAEvB,0BAA0B,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC;MAC9EoC,eAAe,EAAEJ,yBAAyB,CAAC,CAAC,CAAC;MAC7CK,eAAe,EAAEL,yBAAyB,CAAC,CAAC,CAAC;MAC7CM,eAAe,EAAEN,yBAAyB,CAAC,CAAC,CAAC;MAC7CO,eAAe,EAAEP,yBAAyB,CAAC,CAAC,CAAC;MAC7CQ,eAAe,EAAER,yBAAyB,CAAC,CAAC,CAAC;MAC7CS,eAAe,EAAET,yBAAyB,CAAC,CAAC;KAC7C;EACH;EAEA,OAAO;IACLjB,wBAAwB,EAAAA,wBAAA;IACxBoB,4BAA4B,EAAAA;GAC7B;AACH;AAEA,OAAM,SAAUO,0BAA0BA,CACxC7C,SAA4B;EAG5B,IAAMC,aAAa,GAAmB,EAAE;EAElC,IAAA6C,EAAA,GAAA/C,iBAAA,CAAAC,SAAA,EAAAC,aAAA,CAGyC;IAF7CiB,wBAAA,GAAA4B,EAAA,CAAA5B,wBAAwB;IACxBoB,4BAAA,GAAAQ,EAAA,CAAAR,4BAC6C;EAE/C,IAAMS,SAAS,GAAG/C,SAAS,CAAC,kBAAkB,CAAC;EAC/CC,aAAa,CAAC+C,IAAI,CAAC;IAAEC,YAAY,EAAE,kBAAkB;IAAEC,SAAS,EAAE;EAAwB,CAAE,CAAC;EAE7F,IAAI,CAACpD,UAAU,CAACiD,SAAS,CAAC,EAAE;IAC1B,MAAM,IAAII,KAAK,CAAC,2EAAyEJ,SAAW,CAAC;;EAGvG,IAAMK,MAAM,GAAG;IACbC,WAAW,EAAEnC,wBAAwB,EAAE;IACvCoC,gBAAgB,EAAEhB,4BAA4B,EAAE;IAChDiB,YAAY,EAAE;MACZR,SAAS,EAAAA;;GAEZ;EAEDnD,0BAA0B,CAACI,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAEmD,MAAM,EAAAA,MAAA;IAAEnD,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}