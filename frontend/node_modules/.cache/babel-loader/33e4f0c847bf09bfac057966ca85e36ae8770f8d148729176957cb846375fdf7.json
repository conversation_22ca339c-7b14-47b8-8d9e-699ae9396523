{"ast": null, "code": "export function createBrowserEnv() {\n  var fetch = window['fetch'] || function () {\n    throw new Error('fetch - missing fetch implementation for browser environment');\n  };\n  var readFile = function () {\n    throw new Error('readFile - filesystem not available for browser environment');\n  };\n  return {\n    Canvas: HTMLCanvasElement,\n    CanvasRenderingContext2D: CanvasRenderingContext2D,\n    Image: HTMLImageElement,\n    ImageData: ImageData,\n    Video: HTMLVideoElement,\n    createCanvasElement: function () {\n      return document.createElement('canvas');\n    },\n    createImageElement: function () {\n      return document.createElement('img');\n    },\n    fetch: fetch,\n    readFile: readFile\n  };\n}", "map": {"version": 3, "names": ["createBrowserEnv", "fetch", "window", "Error", "readFile", "<PERSON><PERSON>", "HTMLCanvasElement", "CanvasRenderingContext2D", "Image", "HTMLImageElement", "ImageData", "Video", "HTMLVideoElement", "createCanvasElement", "document", "createElement", "createImageElement"], "sources": ["../../../src/env/createBrowserEnv.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,gBAAgBA,CAAA;EAE9B,IAAMC,KAAK,GAAGC,MAAM,CAAC,OAAO,CAAC,IAAI;IAC/B,MAAM,IAAIC,KAAK,CAAC,8DAA8D,CAAC;EACjF,CAAC;EAED,IAAMC,QAAQ,GAAG,SAAAA,CAAA;IACf,MAAM,IAAID,KAAK,CAAC,6DAA6D,CAAC;EAChF,CAAC;EAED,OAAO;IACLE,MAAM,EAAEC,iBAAiB;IACzBC,wBAAwB,EAAEA,wBAAwB;IAClDC,KAAK,EAAEC,gBAAgB;IACvBC,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEC,gBAAgB;IACvBC,mBAAmB,EAAE,SAAAA,CAAA;MAAM,OAAAC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAAhC,CAAgC;IAC3DC,kBAAkB,EAAE,SAAAA,CAAA;MAAM,OAAAF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAA7B,CAA6B;IACvDd,KAAK,EAAAA,KAAA;IACLG,QAAQ,EAAAA;GACT;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}