{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nexport function normalize(x) {\n  return tf.tidy(function () {\n    return tf.mul(tf.sub(x, tf.scalar(127.5)), tf.scalar(0.0078125));\n  });\n}", "map": {"version": 3, "names": ["tf", "normalize", "x", "tidy", "mul", "sub", "scalar"], "sources": ["../../../src/mtcnn/normalize.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,OAAM,SAAUC,SAASA,CAACC,CAAc;EACtC,OAAOF,EAAE,CAACG,IAAI,CACZ;IAAM,OAAAH,EAAE,CAACI,GAAG,CAACJ,EAAE,CAACK,GAAG,CAACH,CAAC,EAAEF,EAAE,CAACM,MAAM,CAAC,KAAK,CAAC,CAAC,EAAEN,EAAE,CAACM,MAAM,CAAC,SAAS,CAAC,CAAC;EAAzD,CAAyD,CAChE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}