{"ast": null, "code": "export function iou(box1, box2, isIOU) {\n  if (isIOU === void 0) {\n    isIOU = true;\n  }\n  var width = Math.max(0.0, Math.min(box1.right, box2.right) - Math.max(box1.left, box2.left));\n  var height = Math.max(0.0, Math.min(box1.bottom, box2.bottom) - Math.max(box1.top, box2.top));\n  var interSection = width * height;\n  return isIOU ? interSection / (box1.area + box2.area - interSection) : interSection / Math.min(box1.area, box2.area);\n}", "map": {"version": 3, "names": ["iou", "box1", "box2", "isIOU", "width", "Math", "max", "min", "right", "left", "height", "bottom", "top", "interSection", "area"], "sources": ["../../../src/ops/iou.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAM,SAAUA,GAAGA,CAACC,IAAS,EAAEC,IAAS,EAAEC,KAAqB;EAArB,IAAAA,KAAA;IAAAA,KAAA,OAAqB;EAAA;EAC7D,IAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAACN,IAAI,CAACO,KAAK,EAAEN,IAAI,CAACM,KAAK,CAAC,GAAGH,IAAI,CAACC,GAAG,CAACL,IAAI,CAACQ,IAAI,EAAEP,IAAI,CAACO,IAAI,CAAC,CAAC;EAC9F,IAAMC,MAAM,GAAGL,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAACN,IAAI,CAACU,MAAM,EAAET,IAAI,CAACS,MAAM,CAAC,GAAGN,IAAI,CAACC,GAAG,CAACL,IAAI,CAACW,GAAG,EAAEV,IAAI,CAACU,GAAG,CAAC,CAAC;EAC/F,IAAMC,YAAY,GAAGT,KAAK,GAAGM,MAAM;EAEnC,OAAOP,KAAK,GACRU,YAAY,IAAIZ,IAAI,CAACa,IAAI,GAAGZ,IAAI,CAACY,IAAI,GAAGD,YAAY,CAAC,GACrDA,YAAY,GAAGR,IAAI,CAACE,GAAG,CAACN,IAAI,CAACa,IAAI,EAAEZ,IAAI,CAACY,IAAI,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}