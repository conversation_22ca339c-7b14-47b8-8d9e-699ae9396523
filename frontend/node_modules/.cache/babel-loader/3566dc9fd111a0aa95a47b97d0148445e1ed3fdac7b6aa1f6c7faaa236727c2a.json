{"ast": null, "code": "import { FaceDetection } from '../classes/FaceDetection';\nimport { FaceLandmarks } from '../classes/FaceLandmarks';\nimport { isWithFaceDetection } from './WithFaceDetection';\nexport function isWithFaceLandmarks(obj) {\n  return isWithFaceDetection(obj) && obj['landmarks'] instanceof FaceLandmarks && obj['unshiftedLandmarks'] instanceof FaceLandmarks && obj['alignedRect'] instanceof FaceDetection;\n}\nexport function extendWithFaceLandmarks(sourceObj, unshiftedLandmarks) {\n  var shift = sourceObj.detection.box;\n  var landmarks = unshiftedLandmarks.shiftBy(shift.x, shift.y);\n  var rect = landmarks.align();\n  var imageDims = sourceObj.detection.imageDims;\n  var alignedRect = new FaceDetection(sourceObj.detection.score, rect.rescale(imageDims.reverse()), imageDims);\n  var extension = {\n    landmarks: landmarks,\n    unshiftedLandmarks: unshiftedLandmarks,\n    alignedRect: alignedRect\n  };\n  return Object.assign({}, sourceObj, extension);\n}", "map": {"version": 3, "names": ["FaceDetection", "FaceLandmarks", "isWithFaceDetection", "isWithFaceLandmarks", "obj", "extendWithFaceLandmarks", "sourceObj", "unshiftedLandmarks", "shift", "detection", "box", "landmarks", "shiftBy", "x", "y", "rect", "align", "imageDims", "alignedRect", "score", "rescale", "reverse", "extension", "Object", "assign"], "sources": ["../../../src/factories/WithFaceLandmarks.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,aAAa,QAAQ,0BAA0B;AACxD,SAASC,aAAa,QAAQ,0BAA0B;AAExD,SAASC,mBAAmB,QAA2B,qBAAqB;AAW5E,OAAM,SAAUC,mBAAmBA,CAACC,GAAQ;EAC1C,OAAOF,mBAAmB,CAACE,GAAG,CAAC,IAC1BA,GAAG,CAAC,WAAW,CAAC,YAAYH,aAAa,IACzCG,GAAG,CAAC,oBAAoB,CAAC,YAAYH,aAAa,IAClDG,GAAG,CAAC,aAAa,CAAC,YAAYJ,aAAa;AAClD;AAEA,OAAM,SAAUK,uBAAuBA,CAIrCC,SAAkB,EAClBC,kBAAkC;EAG1B,IAAAC,KAAA,GAAAF,SAAA,CAAAG,SAAA,CAAAC,GAAU;EAClB,IAAMC,SAAS,GAAGJ,kBAAkB,CAACK,OAAO,CAAiBJ,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,CAAC;EAE9E,IAAMC,IAAI,GAAGJ,SAAS,CAACK,KAAK,EAAE;EACtB,IAAAC,SAAA,GAAAX,SAAA,CAAAG,SAAA,CAAAQ,SAAS;EACjB,IAAMC,WAAW,GAAG,IAAIlB,aAAa,CAACM,SAAS,CAACG,SAAS,CAACU,KAAK,EAAEJ,IAAI,CAACK,OAAO,CAACH,SAAS,CAACI,OAAO,EAAE,CAAC,EAAEJ,SAAS,CAAC;EAE9G,IAAMK,SAAS,GAAG;IAChBX,SAAS,EAAAA,SAAA;IACTJ,kBAAkB,EAAAA,kBAAA;IAClBW,WAAW,EAAAA;GACZ;EAED,OAAOK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAElB,SAAS,EAAEgB,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}