{"ast": null, "code": "/**\n * Configuration Supabase pour PresencePro\n */\n\nimport { createClient } from '@supabase/supabase-js';\n\n// Configuration Supabase pour PresencePro\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';\n\n// Créer le client Supabase\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10\n    }\n  }\n});\n\n// Exports pour compatibilité avec l'ancien code Firebase\nexport const auth = supabase.auth;\nexport const db = supabase;\nexport const storage = supabase.storage;\nexport default supabase;", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "auth", "autoRefreshToken", "persistSession", "detectSessionInUrl", "realtime", "params", "eventsPerSecond", "db", "storage"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/config/supabase.ts"], "sourcesContent": ["/**\n * Configuration Supabase pour PresencePro\n */\n\nimport { createClient } from '@supabase/supabase-js';\n\n// Configuration Supabase pour PresencePro\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key';\n\n// Créer le client Supabase\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10\n    }\n  }\n});\n\n// Exports pour compatibilité avec l'ancien code Firebase\nexport const auth = supabase.auth;\nexport const db = supabase;\nexport const storage = supabase.storage;\n\nexport default supabase;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,kCAAkC;AAC5F,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,eAAe;;AAElF;AACA,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,EAAE;EACjEG,IAAI,EAAE;IACJC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE;EACtB,CAAC;EACDC,QAAQ,EAAE;IACRC,MAAM,EAAE;MACNC,eAAe,EAAE;IACnB;EACF;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMN,IAAI,GAAGD,QAAQ,CAACC,IAAI;AACjC,OAAO,MAAMO,EAAE,GAAGR,QAAQ;AAC1B,OAAO,MAAMS,OAAO,GAAGT,QAAQ,CAACS,OAAO;AAEvC,eAAeT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}