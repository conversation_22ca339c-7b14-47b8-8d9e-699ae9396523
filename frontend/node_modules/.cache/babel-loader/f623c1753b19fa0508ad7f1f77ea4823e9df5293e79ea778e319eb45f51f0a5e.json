{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/DebugAuth.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Button, Typography, TextField, Alert } from '@mui/material';\nimport { supabase } from '../config/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DebugAuth = () => {\n  _s();\n  const [email, setEmail] = useState('<EMAIL>');\n  const [password, setPassword] = useState('admin123');\n  const [loading, setLoading] = useState(false);\n  const [result, setResult] = useState('');\n  const testLogin = async () => {\n    setLoading(true);\n    setResult('');\n    try {\n      var _data$user, _data$user2;\n      console.log('🔐 Tentative de connexion avec:', email);\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) {\n        console.error('❌ Erreur de connexion:', error);\n        setResult(`Erreur: ${error.message}`);\n        return;\n      }\n      console.log('✅ Connexion réussie:', data);\n      setResult(`Connexion réussie! User ID: ${(_data$user = data.user) === null || _data$user === void 0 ? void 0 : _data$user.id}, Email: ${(_data$user2 = data.user) === null || _data$user2 === void 0 ? void 0 : _data$user2.email}`);\n\n      // Vérifier la session\n      const {\n        data: session\n      } = await supabase.auth.getSession();\n      console.log('📋 Session actuelle:', session);\n    } catch (err) {\n      console.error('❌ Erreur générale:', err);\n      setResult(`Erreur générale: ${err instanceof Error ? err.message : 'Erreur inconnue'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testLogout = async () => {\n    try {\n      await supabase.auth.signOut();\n      setResult('Déconnexion effectuée');\n      console.log('👋 Déconnexion effectuée');\n    } catch (err) {\n      console.error('❌ Erreur de déconnexion:', err);\n    }\n  };\n  const checkSession = async () => {\n    try {\n      var _session$user;\n      const {\n        data: {\n          session\n        }\n      } = await supabase.auth.getSession();\n      console.log('📋 Session actuelle:', session);\n      setResult(session ? `Session active: ${(_session$user = session.user) === null || _session$user === void 0 ? void 0 : _session$user.email}` : 'Aucune session active');\n    } catch (err) {\n      console.error('❌ Erreur de vérification de session:', err);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    maxWidth: 600,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Debug Authentification\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 2,\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        label: \"Email\",\n        value: email,\n        onChange: e => setEmail(e.target.value),\n        margin: \"normal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        label: \"Mot de passe\",\n        type: \"password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        margin: \"normal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 2,\n      display: \"flex\",\n      gap: 2,\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: testLogin,\n        disabled: loading,\n        children: loading ? 'Connexion...' : 'Se connecter'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: testLogout,\n        children: \"Se d\\xE9connecter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: checkSession,\n        children: \"V\\xE9rifier session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), result && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: result.includes('Erreur') ? 'error' : 'success',\n      children: result\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mt: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Ouvrez la console du navigateur pour voir les logs d\\xE9taill\\xE9s.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(DebugAuth, \"sbQsOGXOk54XvR5vGGzgEFXXx0I=\");\n_c = DebugAuth;\nexport default DebugAuth;\nvar _c;\n$RefreshReg$(_c, \"DebugAuth\");", "map": {"version": 3, "names": ["React", "useState", "Box", "<PERSON><PERSON>", "Typography", "TextField", "<PERSON><PERSON>", "supabase", "jsxDEV", "_jsxDEV", "DebugAuth", "_s", "email", "setEmail", "password", "setPassword", "loading", "setLoading", "result", "setResult", "testLogin", "_data$user", "_data$user2", "console", "log", "data", "error", "auth", "signInWithPassword", "message", "user", "id", "session", "getSession", "err", "Error", "testLogout", "signOut", "checkSession", "_session$user", "p", "max<PERSON><PERSON><PERSON>", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "fullWidth", "label", "value", "onChange", "e", "target", "margin", "type", "display", "gap", "onClick", "disabled", "severity", "includes", "mt", "color", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/DebugAuth.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Box, <PERSON><PERSON>, <PERSON>pography, <PERSON><PERSON>ield, Alert } from '@mui/material';\nimport { supabase } from '../config/supabase';\n\nconst DebugAuth: React.FC = () => {\n  const [email, setEmail] = useState('<EMAIL>');\n  const [password, setPassword] = useState('admin123');\n  const [loading, setLoading] = useState(false);\n  const [result, setResult] = useState<string>('');\n\n  const testLogin = async () => {\n    setLoading(true);\n    setResult('');\n\n    try {\n      console.log('🔐 Tentative de connexion avec:', email);\n      \n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        console.error('❌ Erreur de connexion:', error);\n        setResult(`Erreur: ${error.message}`);\n        return;\n      }\n\n      console.log('✅ Connexion réussie:', data);\n      setResult(`Connexion réussie! User ID: ${data.user?.id}, Email: ${data.user?.email}`);\n\n      // Vérifier la session\n      const { data: session } = await supabase.auth.getSession();\n      console.log('📋 Session actuelle:', session);\n\n    } catch (err) {\n      console.error('❌ Erreur générale:', err);\n      setResult(`Erreur générale: ${err instanceof Error ? err.message : 'Erreur inconnue'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testLogout = async () => {\n    try {\n      await supabase.auth.signOut();\n      setResult('Déconnexion effectuée');\n      console.log('👋 Déconnexion effectuée');\n    } catch (err) {\n      console.error('❌ Erreur de déconnexion:', err);\n    }\n  };\n\n  const checkSession = async () => {\n    try {\n      const { data: { session } } = await supabase.auth.getSession();\n      console.log('📋 Session actuelle:', session);\n      setResult(session ? `Session active: ${session.user?.email}` : 'Aucune session active');\n    } catch (err) {\n      console.error('❌ Erreur de vérification de session:', err);\n    }\n  };\n\n  return (\n    <Box p={3} maxWidth={600}>\n      <Typography variant=\"h4\" gutterBottom>\n        Debug Authentification\n      </Typography>\n      \n      <Box mb={2}>\n        <TextField\n          fullWidth\n          label=\"Email\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n          margin=\"normal\"\n        />\n        <TextField\n          fullWidth\n          label=\"Mot de passe\"\n          type=\"password\"\n          value={password}\n          onChange={(e) => setPassword(e.target.value)}\n          margin=\"normal\"\n        />\n      </Box>\n\n      <Box mb={2} display=\"flex\" gap={2}>\n        <Button \n          variant=\"contained\" \n          onClick={testLogin} \n          disabled={loading}\n        >\n          {loading ? 'Connexion...' : 'Se connecter'}\n        </Button>\n        \n        <Button \n          variant=\"outlined\" \n          onClick={testLogout}\n        >\n          Se déconnecter\n        </Button>\n        \n        <Button \n          variant=\"outlined\" \n          onClick={checkSession}\n        >\n          Vérifier session\n        </Button>\n      </Box>\n\n      {result && (\n        <Alert severity={result.includes('Erreur') ? 'error' : 'success'}>\n          {result}\n        </Alert>\n      )}\n\n      <Box mt={3}>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Ouvrez la console du navigateur pour voir les logs détaillés.\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\nexport default DebugAuth;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACzE,SAASC,QAAQ,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,uBAAuB,CAAC;EAC3D,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,UAAU,CAAC;EACpD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAEhD,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BH,UAAU,CAAC,IAAI,CAAC;IAChBE,SAAS,CAAC,EAAE,CAAC;IAEb,IAAI;MAAA,IAAAE,UAAA,EAAAC,WAAA;MACFC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEZ,KAAK,CAAC;MAErD,MAAM;QAAEa,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMnB,QAAQ,CAACoB,IAAI,CAACC,kBAAkB,CAAC;QAC7DhB,KAAK;QACLE;MACF,CAAC,CAAC;MAEF,IAAIY,KAAK,EAAE;QACTH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CP,SAAS,CAAC,WAAWO,KAAK,CAACG,OAAO,EAAE,CAAC;QACrC;MACF;MAEAN,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,IAAI,CAAC;MACzCN,SAAS,CAAC,gCAAAE,UAAA,GAA+BI,IAAI,CAACK,IAAI,cAAAT,UAAA,uBAATA,UAAA,CAAWU,EAAE,aAAAT,WAAA,GAAYG,IAAI,CAACK,IAAI,cAAAR,WAAA,uBAATA,WAAA,CAAWV,KAAK,EAAE,CAAC;;MAErF;MACA,MAAM;QAAEa,IAAI,EAAEO;MAAQ,CAAC,GAAG,MAAMzB,QAAQ,CAACoB,IAAI,CAACM,UAAU,CAAC,CAAC;MAC1DV,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEQ,OAAO,CAAC;IAE9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZX,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEQ,GAAG,CAAC;MACxCf,SAAS,CAAC,oBAAoBe,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACL,OAAO,GAAG,iBAAiB,EAAE,CAAC;IACzF,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAM7B,QAAQ,CAACoB,IAAI,CAACU,OAAO,CAAC,CAAC;MAC7BlB,SAAS,CAAC,uBAAuB,CAAC;MAClCI,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZX,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEQ,GAAG,CAAC;IAChD;EACF,CAAC;EAED,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,aAAA;MACF,MAAM;QAAEd,IAAI,EAAE;UAAEO;QAAQ;MAAE,CAAC,GAAG,MAAMzB,QAAQ,CAACoB,IAAI,CAACM,UAAU,CAAC,CAAC;MAC9DV,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEQ,OAAO,CAAC;MAC5Cb,SAAS,CAACa,OAAO,GAAG,oBAAAO,aAAA,GAAmBP,OAAO,CAACF,IAAI,cAAAS,aAAA,uBAAZA,aAAA,CAAc3B,KAAK,EAAE,GAAG,uBAAuB,CAAC;IACzF,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACZX,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAEQ,GAAG,CAAC;IAC5D;EACF,CAAC;EAED,oBACEzB,OAAA,CAACP,GAAG;IAACsC,CAAC,EAAE,CAAE;IAACC,QAAQ,EAAE,GAAI;IAAAC,QAAA,gBACvBjC,OAAA,CAACL,UAAU;MAACuC,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbvC,OAAA,CAACP,GAAG;MAAC+C,EAAE,EAAE,CAAE;MAAAP,QAAA,gBACTjC,OAAA,CAACJ,SAAS;QACR6C,SAAS;QACTC,KAAK,EAAC,OAAO;QACbC,KAAK,EAAExC,KAAM;QACbyC,QAAQ,EAAGC,CAAC,IAAKzC,QAAQ,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC1CI,MAAM,EAAC;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACFvC,OAAA,CAACJ,SAAS;QACR6C,SAAS;QACTC,KAAK,EAAC,cAAc;QACpBM,IAAI,EAAC,UAAU;QACfL,KAAK,EAAEtC,QAAS;QAChBuC,QAAQ,EAAGC,CAAC,IAAKvC,WAAW,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC7CI,MAAM,EAAC;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvC,OAAA,CAACP,GAAG;MAAC+C,EAAE,EAAE,CAAE;MAACS,OAAO,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAAAjB,QAAA,gBAChCjC,OAAA,CAACN,MAAM;QACLwC,OAAO,EAAC,WAAW;QACnBiB,OAAO,EAAExC,SAAU;QACnByC,QAAQ,EAAE7C,OAAQ;QAAA0B,QAAA,EAEjB1B,OAAO,GAAG,cAAc,GAAG;MAAc;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAETvC,OAAA,CAACN,MAAM;QACLwC,OAAO,EAAC,UAAU;QAClBiB,OAAO,EAAExB,UAAW;QAAAM,QAAA,EACrB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETvC,OAAA,CAACN,MAAM;QACLwC,OAAO,EAAC,UAAU;QAClBiB,OAAO,EAAEtB,YAAa;QAAAI,QAAA,EACvB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL9B,MAAM,iBACLT,OAAA,CAACH,KAAK;MAACwD,QAAQ,EAAE5C,MAAM,CAAC6C,QAAQ,CAAC,QAAQ,CAAC,GAAG,OAAO,GAAG,SAAU;MAAArB,QAAA,EAC9DxB;IAAM;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACR,eAEDvC,OAAA,CAACP,GAAG;MAAC8D,EAAE,EAAE,CAAE;MAAAtB,QAAA,eACTjC,OAAA,CAACL,UAAU;QAACuC,OAAO,EAAC,OAAO;QAACsB,KAAK,EAAC,gBAAgB;QAAAvB,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CAxHID,SAAmB;AAAAwD,EAAA,GAAnBxD,SAAmB;AA0HzB,eAAeA,SAAS;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}