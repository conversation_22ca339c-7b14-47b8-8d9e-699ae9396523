{"ast": null, "code": "import { extractWeightEntryFactory, loadSeparableConvParamsFactory } from '../common';\nimport { loadConvParamsFactory } from '../common/loadConvParamsFactory';\nexport function loadParamsFactory(weightMap, paramMappings) {\n  var extractWeightEntry = extractWeightEntryFactory(weightMap, paramMappings);\n  var extractConvParams = loadConvParamsFactory(extractWeightEntry);\n  var extractSeparableConvParams = loadSeparableConvParamsFactory(extractWeightEntry);\n  function extractDenseBlock3Params(prefix, isFirstLayer) {\n    if (isFirstLayer === void 0) {\n      isFirstLayer = false;\n    }\n    var conv0 = isFirstLayer ? extractConvParams(prefix + \"/conv0\") : extractSeparableConvParams(prefix + \"/conv0\");\n    var conv1 = extractSeparableConvParams(prefix + \"/conv1\");\n    var conv2 = extractSeparableConvParams(prefix + \"/conv2\");\n    return {\n      conv0: conv0,\n      conv1: conv1,\n      conv2: conv2\n    };\n  }\n  function extractDenseBlock4Params(prefix, isFirstLayer) {\n    if (isFirstLayer === void 0) {\n      isFirstLayer = false;\n    }\n    var conv0 = isFirstLayer ? extractConvParams(prefix + \"/conv0\") : extractSeparableConvParams(prefix + \"/conv0\");\n    var conv1 = extractSeparableConvParams(prefix + \"/conv1\");\n    var conv2 = extractSeparableConvParams(prefix + \"/conv2\");\n    var conv3 = extractSeparableConvParams(prefix + \"/conv3\");\n    return {\n      conv0: conv0,\n      conv1: conv1,\n      conv2: conv2,\n      conv3: conv3\n    };\n  }\n  return {\n    extractDenseBlock3Params: extractDenseBlock3Params,\n    extractDenseBlock4Params: extractDenseBlock4Params\n  };\n}", "map": {"version": 3, "names": ["extractWeightEntryFactory", "loadSeparableConvParamsFactory", "loadConvParamsFactory", "loadParamsFactory", "weightMap", "paramMappings", "extractWeightEntry", "extractConvParams", "extractSeparableConvParams", "extractDenseBlock3Params", "prefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conv0", "conv1", "conv2", "extractDenseBlock4Params", "conv3"], "sources": ["../../../src/faceFeatureExtractor/loadParamsFactory.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,8BAA8B,QAAsB,WAAW;AACnG,SAASC,qBAAqB,QAAQ,iCAAiC;AAGvE,OAAM,SAAUC,iBAAiBA,CAACC,SAAc,EAAEC,aAA6B;EAE7E,IAAMC,kBAAkB,GAAGN,yBAAyB,CAACI,SAAS,EAAEC,aAAa,CAAC;EAE9E,IAAME,iBAAiB,GAAGL,qBAAqB,CAACI,kBAAkB,CAAC;EACnE,IAAME,0BAA0B,GAAGP,8BAA8B,CAACK,kBAAkB,CAAC;EAErF,SAASG,wBAAwBA,CAACC,MAAc,EAAEC,YAA6B;IAA7B,IAAAA,YAAA;MAAAA,YAAA,QAA6B;IAAA;IAC7E,IAAMC,KAAK,GAAGD,YAAY,GACtBJ,iBAAiB,CAAIG,MAAM,WAAQ,CAAC,GACpCF,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IACjD,IAAMG,KAAK,GAAGL,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IAC3D,IAAMI,KAAK,GAAGN,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IAE3D,OAAO;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;EAChC;EAEA,SAASC,wBAAwBA,CAACL,MAAc,EAAEC,YAA6B;IAA7B,IAAAA,YAAA;MAAAA,YAAA,QAA6B;IAAA;IAC7E,IAAMC,KAAK,GAAGD,YAAY,GACtBJ,iBAAiB,CAAIG,MAAM,WAAQ,CAAC,GACpCF,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IACjD,IAAMG,KAAK,GAAGL,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IAC3D,IAAMI,KAAK,GAAGN,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IAC3D,IAAMM,KAAK,GAAGR,0BAA0B,CAAIE,MAAM,WAAQ,CAAC;IAE3D,OAAO;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEE,KAAK,EAAAA;IAAA,CAAE;EACvC;EAEA,OAAO;IACLP,wBAAwB,EAAAA,wBAAA;IACxBM,wBAAwB,EAAAA;GACzB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}