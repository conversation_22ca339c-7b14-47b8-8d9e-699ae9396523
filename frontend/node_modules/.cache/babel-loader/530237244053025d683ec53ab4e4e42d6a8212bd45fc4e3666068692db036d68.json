{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Hook personnalisé pour utiliser Firebase dans PresencePro\n */\n\nimport { useState, useEffect } from 'react';\nimport { signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, onAuthStateChanged } from 'firebase/auth';\nimport { auth } from '../config/firebase';\nimport { firebaseService } from '../services/firebaseService';\nexport const useFirebase = () => {\n  _s();\n  const [firebaseUser, setFirebaseUser] = useState(null);\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Écouter les changements d'authentification Firebase\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async firebaseUser => {\n      setLoading(true);\n      setError(null);\n      try {\n        if (firebaseUser) {\n          setFirebaseUser(firebaseUser);\n          // Récupérer les données utilisateur depuis Firestore\n          const userData = await firebaseService.getUserById(firebaseUser.uid);\n          setUser(userData);\n        } else {\n          setFirebaseUser(null);\n          setUser(null);\n        }\n      } catch (err) {\n        console.error('Erreur lors de la récupération des données utilisateur:', err);\n        setError('Erreur lors de la récupération des données utilisateur');\n      } finally {\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n\n  // Connexion\n  const signIn = async (email, password) => {\n    setLoading(true);\n    setError(null);\n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n      // L'utilisateur sera automatiquement mis à jour via onAuthStateChanged\n    } catch (err) {\n      console.error('Erreur de connexion:', err);\n      setError(getFirebaseErrorMessage(err.code));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Inscription\n  const signUp = async (email, password, userData) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const firebaseUser = userCredential.user;\n\n      // Créer le profil utilisateur dans Firestore\n      const newUserData = {\n        username: userData.username || email.split('@')[0],\n        email: firebaseUser.email || email,\n        firstName: userData.firstName || '',\n        lastName: userData.lastName || '',\n        fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n        role: userData.role || 'student',\n        roleDisplay: userData.roleDisplay || 'Étudiant',\n        phoneNumber: userData.phoneNumber,\n        dateOfBirth: userData.dateOfBirth,\n        address: userData.address,\n        profilePicture: userData.profilePicture,\n        isActive: true,\n        dateJoined: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      };\n      await firebaseService.createUser(newUserData);\n    } catch (err) {\n      console.error('Erreur d\\'inscription:', err);\n      setError(getFirebaseErrorMessage(err.code));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Déconnexion\n  const logout = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      await signOut(auth);\n      // L'utilisateur sera automatiquement mis à jour via onAuthStateChanged\n    } catch (err) {\n      console.error('Erreur de déconnexion:', err);\n      setError('Erreur lors de la déconnexion');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Actualiser les données utilisateur\n  const refreshUserData = async () => {\n    if (!firebaseUser) return;\n    setLoading(true);\n    setError(null);\n    try {\n      const userData = await firebaseService.getUserById(firebaseUser.uid);\n      setUser(userData);\n    } catch (err) {\n      console.error('Erreur lors de l\\'actualisation des données:', err);\n      setError('Erreur lors de l\\'actualisation des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    firebaseUser,\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  };\n};\n\n// Fonction utilitaire pour traduire les erreurs Firebase\n_s(useFirebase, \"rQexA9JYNA/DM7IwYl5I9Wip9x8=\");\nconst getFirebaseErrorMessage = errorCode => {\n  switch (errorCode) {\n    case 'auth/user-not-found':\n      return 'Aucun utilisateur trouvé avec cette adresse email';\n    case 'auth/wrong-password':\n      return 'Mot de passe incorrect';\n    case 'auth/email-already-in-use':\n      return 'Cette adresse email est déjà utilisée';\n    case 'auth/weak-password':\n      return 'Le mot de passe doit contenir au moins 6 caractères';\n    case 'auth/invalid-email':\n      return 'Adresse email invalide';\n    case 'auth/too-many-requests':\n      return 'Trop de tentatives. Veuillez réessayer plus tard';\n    case 'auth/network-request-failed':\n      return 'Erreur de connexion réseau';\n    default:\n      return 'Une erreur est survenue. Veuillez réessayer';\n  }\n};\nexport default useFirebase;", "map": {"version": 3, "names": ["useState", "useEffect", "signInWithEmailAndPassword", "createUserWithEmailAndPassword", "signOut", "onAuthStateChanged", "auth", "firebaseService", "useFirebase", "_s", "firebaseUser", "setFirebaseUser", "user", "setUser", "loading", "setLoading", "error", "setError", "unsubscribe", "userData", "getUserById", "uid", "err", "console", "signIn", "email", "password", "getFirebaseErrorMessage", "code", "signUp", "userCredential", "newUserData", "username", "split", "firstName", "lastName", "fullName", "trim", "role", "roleDisplay", "phoneNumber", "dateOfBirth", "address", "profilePicture", "isActive", "dateJoined", "Date", "toISOString", "lastLogin", "createUser", "logout", "refreshUserData", "errorCode"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/hooks/useFirebase.ts"], "sourcesContent": ["/**\n * Hook personnalisé pour utiliser Firebase dans PresencePro\n */\n\nimport { useState, useEffect } from 'react';\nimport {\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { auth } from '../config/firebase';\nimport { firebaseService } from '../services/firebaseService';\nimport { User, UserRole } from '../types';\n\ninterface UseFirebaseReturn {\n  // État d'authentification\n  firebaseUser: FirebaseUser | null;\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n  \n  // Méthodes d'authentification\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, userData: Partial<User>) => Promise<void>;\n  logout: () => Promise<void>;\n  \n  // Méthodes de données\n  refreshUserData: () => Promise<void>;\n}\n\nexport const useFirebase = (): UseFirebaseReturn => {\n  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Écouter les changements d'authentification Firebase\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\n      setLoading(true);\n      setError(null);\n      \n      try {\n        if (firebaseUser) {\n          setFirebaseUser(firebaseUser);\n          // Récupérer les données utilisateur depuis Firestore\n          const userData = await firebaseService.getUserById(firebaseUser.uid);\n          setUser(userData);\n        } else {\n          setFirebaseUser(null);\n          setUser(null);\n        }\n      } catch (err) {\n        console.error('Erreur lors de la récupération des données utilisateur:', err);\n        setError('Erreur lors de la récupération des données utilisateur');\n      } finally {\n        setLoading(false);\n      }\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  // Connexion\n  const signIn = async (email: string, password: string): Promise<void> => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n      // L'utilisateur sera automatiquement mis à jour via onAuthStateChanged\n    } catch (err: any) {\n      console.error('Erreur de connexion:', err);\n      setError(getFirebaseErrorMessage(err.code));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Inscription\n  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<void> => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const firebaseUser = userCredential.user;\n      \n      // Créer le profil utilisateur dans Firestore\n      const newUserData: Omit<User, 'id'> = {\n        username: userData.username || email.split('@')[0],\n        email: firebaseUser.email || email,\n        firstName: userData.firstName || '',\n        lastName: userData.lastName || '',\n        fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n        role: userData.role || 'student',\n        roleDisplay: userData.roleDisplay || 'Étudiant',\n        phoneNumber: userData.phoneNumber,\n        dateOfBirth: userData.dateOfBirth,\n        address: userData.address,\n        profilePicture: userData.profilePicture,\n        isActive: true,\n        dateJoined: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      };\n      \n      await firebaseService.createUser(newUserData);\n      \n    } catch (err: any) {\n      console.error('Erreur d\\'inscription:', err);\n      setError(getFirebaseErrorMessage(err.code));\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Déconnexion\n  const logout = async (): Promise<void> => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      await signOut(auth);\n      // L'utilisateur sera automatiquement mis à jour via onAuthStateChanged\n    } catch (err: any) {\n      console.error('Erreur de déconnexion:', err);\n      setError('Erreur lors de la déconnexion');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Actualiser les données utilisateur\n  const refreshUserData = async (): Promise<void> => {\n    if (!firebaseUser) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const userData = await firebaseService.getUserById(firebaseUser.uid);\n      setUser(userData);\n    } catch (err) {\n      console.error('Erreur lors de l\\'actualisation des données:', err);\n      setError('Erreur lors de l\\'actualisation des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    firebaseUser,\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    logout,\n    refreshUserData\n  };\n};\n\n// Fonction utilitaire pour traduire les erreurs Firebase\nconst getFirebaseErrorMessage = (errorCode: string): string => {\n  switch (errorCode) {\n    case 'auth/user-not-found':\n      return 'Aucun utilisateur trouvé avec cette adresse email';\n    case 'auth/wrong-password':\n      return 'Mot de passe incorrect';\n    case 'auth/email-already-in-use':\n      return 'Cette adresse email est déjà utilisée';\n    case 'auth/weak-password':\n      return 'Le mot de passe doit contenir au moins 6 caractères';\n    case 'auth/invalid-email':\n      return 'Adresse email invalide';\n    case 'auth/too-many-requests':\n      return 'Trop de tentatives. Veuillez réessayer plus tard';\n    case 'auth/network-request-failed':\n      return 'Erreur de connexion réseau';\n    default:\n      return 'Une erreur est survenue. Veuillez réessayer';\n  }\n};\n\nexport default useFirebase;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,OAAO,EACPC,kBAAkB,QAEb,eAAe;AACtB,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,eAAe,QAAQ,6BAA6B;AAmB7D,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAyB;EAAAC,EAAA;EAClD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiB,WAAW,GAAGb,kBAAkB,CAACC,IAAI,EAAE,MAAOI,YAAY,IAAK;MACnEK,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,IAAIP,YAAY,EAAE;UAChBC,eAAe,CAACD,YAAY,CAAC;UAC7B;UACA,MAAMS,QAAQ,GAAG,MAAMZ,eAAe,CAACa,WAAW,CAACV,YAAY,CAACW,GAAG,CAAC;UACpER,OAAO,CAACM,QAAQ,CAAC;QACnB,CAAC,MAAM;UACLR,eAAe,CAAC,IAAI,CAAC;UACrBE,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZC,OAAO,CAACP,KAAK,CAAC,yDAAyD,EAAEM,GAAG,CAAC;QAC7EL,QAAQ,CAAC,wDAAwD,CAAC;MACpE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMG,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,MAAM,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAoB;IACvEX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMf,0BAA0B,CAACI,IAAI,EAAEmB,KAAK,EAAEC,QAAQ,CAAC;MACvD;IACF,CAAC,CAAC,OAAOJ,GAAQ,EAAE;MACjBC,OAAO,CAACP,KAAK,CAAC,sBAAsB,EAAEM,GAAG,CAAC;MAC1CL,QAAQ,CAACU,uBAAuB,CAACL,GAAG,CAACM,IAAI,CAAC,CAAC;MAC3C,MAAMN,GAAG;IACX,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,MAAM,GAAG,MAAAA,CAAOJ,KAAa,EAAEC,QAAgB,EAAEP,QAAuB,KAAoB;IAChGJ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMa,cAAc,GAAG,MAAM3B,8BAA8B,CAACG,IAAI,EAAEmB,KAAK,EAAEC,QAAQ,CAAC;MAClF,MAAMhB,YAAY,GAAGoB,cAAc,CAAClB,IAAI;;MAExC;MACA,MAAMmB,WAA6B,GAAG;QACpCC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAIP,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDR,KAAK,EAAEf,YAAY,CAACe,KAAK,IAAIA,KAAK;QAClCS,SAAS,EAAEf,QAAQ,CAACe,SAAS,IAAI,EAAE;QACnCC,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ,IAAI,EAAE;QACjCC,QAAQ,EAAE,GAAGjB,QAAQ,CAACe,SAAS,IAAI,EAAE,IAAIf,QAAQ,CAACgB,QAAQ,IAAI,EAAE,EAAE,CAACE,IAAI,CAAC,CAAC;QACzEC,IAAI,EAAEnB,QAAQ,CAACmB,IAAI,IAAI,SAAS;QAChCC,WAAW,EAAEpB,QAAQ,CAACoB,WAAW,IAAI,UAAU;QAC/CC,WAAW,EAAErB,QAAQ,CAACqB,WAAW;QACjCC,WAAW,EAAEtB,QAAQ,CAACsB,WAAW;QACjCC,OAAO,EAAEvB,QAAQ,CAACuB,OAAO;QACzBC,cAAc,EAAExB,QAAQ,CAACwB,cAAc;QACvCC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAED,MAAMxC,eAAe,CAAC0C,UAAU,CAAClB,WAAW,CAAC;IAE/C,CAAC,CAAC,OAAOT,GAAQ,EAAE;MACjBC,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEM,GAAG,CAAC;MAC5CL,QAAQ,CAACU,uBAAuB,CAACL,GAAG,CAACM,IAAI,CAAC,CAAC;MAC3C,MAAMN,GAAG;IACX,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxCnC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMb,OAAO,CAACE,IAAI,CAAC;MACnB;IACF,CAAC,CAAC,OAAOgB,GAAQ,EAAE;MACjBC,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEM,GAAG,CAAC;MAC5CL,QAAQ,CAAC,+BAA+B,CAAC;MACzC,MAAMK,GAAG;IACX,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoC,eAAe,GAAG,MAAAA,CAAA,KAA2B;IACjD,IAAI,CAACzC,YAAY,EAAE;IAEnBK,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMZ,eAAe,CAACa,WAAW,CAACV,YAAY,CAACW,GAAG,CAAC;MACpER,OAAO,CAACM,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACP,KAAK,CAAC,8CAA8C,EAAEM,GAAG,CAAC;MAClEL,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLL,YAAY;IACZE,IAAI;IACJE,OAAO;IACPE,KAAK;IACLQ,MAAM;IACNK,MAAM;IACNqB,MAAM;IACNC;EACF,CAAC;AACH,CAAC;;AAED;AAAA1C,EAAA,CAvIaD,WAAW;AAwIxB,MAAMmB,uBAAuB,GAAIyB,SAAiB,IAAa;EAC7D,QAAQA,SAAS;IACf,KAAK,qBAAqB;MACxB,OAAO,mDAAmD;IAC5D,KAAK,qBAAqB;MACxB,OAAO,wBAAwB;IACjC,KAAK,2BAA2B;MAC9B,OAAO,uCAAuC;IAChD,KAAK,oBAAoB;MACvB,OAAO,qDAAqD;IAC9D,KAAK,oBAAoB;MACvB,OAAO,wBAAwB;IACjC,KAAK,wBAAwB;MAC3B,OAAO,kDAAkD;IAC3D,KAAK,6BAA6B;MAChC,OAAO,4BAA4B;IACrC;MACE,OAAO,6CAA6C;EACxD;AACF,CAAC;AAED,eAAe5C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}