{"ast": null, "code": "export function getModelUris(uri, defaultModelName) {\n  var defaultManifestFilename = defaultModelName + \"-weights_manifest.json\";\n  if (!uri) {\n    return {\n      modelBaseUri: '',\n      manifestUri: defaultManifestFilename\n    };\n  }\n  if (uri === '/') {\n    return {\n      modelBaseUri: '/',\n      manifestUri: \"/\" + defaultManifestFilename\n    };\n  }\n  var protocol = uri.startsWith('http://') ? 'http://' : uri.startsWith('https://') ? 'https://' : '';\n  uri = uri.replace(protocol, '');\n  var parts = uri.split('/').filter(function (s) {\n    return s;\n  });\n  var manifestFile = uri.endsWith('.json') ? parts[parts.length - 1] : defaultManifestFilename;\n  var modelBaseUri = protocol + (uri.endsWith('.json') ? parts.slice(0, parts.length - 1) : parts).join('/');\n  modelBaseUri = uri.startsWith('/') ? \"/\" + modelBaseUri : modelBaseUri;\n  return {\n    modelBaseUri: modelBaseUri,\n    manifestUri: modelBaseUri === '/' ? \"/\" + manifestFile : modelBaseUri + \"/\" + manifestFile\n  };\n}", "map": {"version": 3, "names": ["getModelUris", "uri", "defaultModelName", "defaultManifestFilename", "modelBaseUri", "manifestUri", "protocol", "startsWith", "replace", "parts", "split", "filter", "s", "manifestFile", "endsWith", "length", "slice", "join"], "sources": ["../../../src/common/getModelUris.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,YAAYA,CAACC,GAAuB,EAAEC,gBAAwB;EAC5E,IAAMC,uBAAuB,GAAMD,gBAAgB,2BAAwB;EAE3E,IAAI,CAACD,GAAG,EAAE;IACR,OAAO;MACLG,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAEF;KACd;;EAGH,IAAIF,GAAG,KAAK,GAAG,EAAE;IACf,OAAO;MACLG,YAAY,EAAE,GAAG;MACjBC,WAAW,EAAE,MAAIF;KAClB;;EAEH,IAAMG,QAAQ,GAAGL,GAAG,CAACM,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,GAAGN,GAAG,CAACM,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,EAAE;EACrGN,GAAG,GAAGA,GAAG,CAACO,OAAO,CAACF,QAAQ,EAAE,EAAE,CAAC;EAE/B,IAAMG,KAAK,GAAGR,GAAG,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,UAAAC,CAAC;IAAI,OAAAA,CAAC;EAAD,CAAC,CAAC;EAE3C,IAAMC,YAAY,GAAGZ,GAAG,CAACa,QAAQ,CAAC,OAAO,CAAC,GACtCL,KAAK,CAACA,KAAK,CAACM,MAAM,GAAG,CAAC,CAAC,GACvBZ,uBAAuB;EAE3B,IAAIC,YAAY,GAAGE,QAAQ,GAAG,CAACL,GAAG,CAACa,QAAQ,CAAC,OAAO,CAAC,GAAGL,KAAK,CAACO,KAAK,CAAC,CAAC,EAAEP,KAAK,CAACM,MAAM,GAAG,CAAC,CAAC,GAAGN,KAAK,EAAEQ,IAAI,CAAC,GAAG,CAAC;EAC1Gb,YAAY,GAAGH,GAAG,CAACM,UAAU,CAAC,GAAG,CAAC,GAAG,MAAIH,YAAc,GAAGA,YAAY;EAEtE,OAAO;IACLA,YAAY,EAAAA,YAAA;IACZC,WAAW,EAAED,YAAY,KAAK,GAAG,GAAG,MAAIS,YAAc,GAAMT,YAAY,SAAIS;GAC7E;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}