{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { getModelUris } from '../common/getModelUris';\nimport { fetchJson } from './fetchJson';\nexport function loadWeightMap(uri, defaultModelName) {\n  return __awaiter(this, void 0, void 0, function () {\n    var _a, manifestUri, modelBaseUri, manifest;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          _a = getModelUris(uri, defaultModelName), manifestUri = _a.manifestUri, modelBaseUri = _a.modelBaseUri;\n          return [4 /*yield*/, fetchJson(manifestUri)];\n        case 1:\n          manifest = _b.sent();\n          return [2 /*return*/, tf.io.loadWeights(manifest, modelBaseUri)];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "getModelUris", "<PERSON><PERSON><PERSON>", "loadWeightMap", "uri", "defaultModelName", "_a", "manifestUri", "modelBaseUri", "manifest", "_b", "sent", "io", "loadWeights"], "sources": ["../../../src/dom/loadWeightMap.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,SAAS,QAAQ,aAAa;AAEvC,OAAM,SAAgBC,aAAaA,CACjCC,GAAuB,EACvBC,gBAAwB;;;;;;UAElBC,EAAA,GAAgCL,YAAY,CAACG,GAAG,EAAEC,gBAAgB,CAAC,EAAjEE,WAAW,GAAAD,EAAA,CAAAC,WAAA,EAAEC,YAAY,GAAAF,EAAA,CAAAE,YAAA;UAEhB,qBAAMN,SAAS,CAA8BK,WAAW,CAAC;;UAApEE,QAAQ,GAAGC,EAAA,CAAAC,IAAA,EAAyD;UAE1E,sBAAOX,EAAE,CAACY,EAAE,CAACC,WAAW,CAACJ,QAAQ,EAAED,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}