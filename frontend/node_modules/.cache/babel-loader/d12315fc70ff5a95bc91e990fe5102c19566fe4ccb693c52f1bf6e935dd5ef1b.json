{"ast": null, "code": "import { __assign } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { extractConvParamsFactory, extractFCParamsFactory, extractWeightsFactory } from '../common';\nfunction extractorsFactory(extractWeights, paramMappings) {\n  var extractConvParams = extractConvParamsFactory(extractWeights, paramMappings);\n  var extractFCParams = extractFCParamsFactory(extractWeights, paramMappings);\n  function extractPReluParams(size, paramPath) {\n    var alpha = tf.tensor1d(extractWeights(size));\n    paramMappings.push({\n      paramPath: paramPath\n    });\n    return alpha;\n  }\n  function extractSharedParams(numFilters, mappedPrefix, isRnet) {\n    if (isRnet === void 0) {\n      isRnet = false;\n    }\n    var conv1 = extractConvParams(numFilters[0], numFilters[1], 3, mappedPrefix + \"/conv1\");\n    var prelu1_alpha = extractPReluParams(numFilters[1], mappedPrefix + \"/prelu1_alpha\");\n    var conv2 = extractConvParams(numFilters[1], numFilters[2], 3, mappedPrefix + \"/conv2\");\n    var prelu2_alpha = extractPReluParams(numFilters[2], mappedPrefix + \"/prelu2_alpha\");\n    var conv3 = extractConvParams(numFilters[2], numFilters[3], isRnet ? 2 : 3, mappedPrefix + \"/conv3\");\n    var prelu3_alpha = extractPReluParams(numFilters[3], mappedPrefix + \"/prelu3_alpha\");\n    return {\n      conv1: conv1,\n      prelu1_alpha: prelu1_alpha,\n      conv2: conv2,\n      prelu2_alpha: prelu2_alpha,\n      conv3: conv3,\n      prelu3_alpha: prelu3_alpha\n    };\n  }\n  function extractPNetParams() {\n    var sharedParams = extractSharedParams([3, 10, 16, 32], 'pnet');\n    var conv4_1 = extractConvParams(32, 2, 1, 'pnet/conv4_1');\n    var conv4_2 = extractConvParams(32, 4, 1, 'pnet/conv4_2');\n    return __assign(__assign({}, sharedParams), {\n      conv4_1: conv4_1,\n      conv4_2: conv4_2\n    });\n  }\n  function extractRNetParams() {\n    var sharedParams = extractSharedParams([3, 28, 48, 64], 'rnet', true);\n    var fc1 = extractFCParams(576, 128, 'rnet/fc1');\n    var prelu4_alpha = extractPReluParams(128, 'rnet/prelu4_alpha');\n    var fc2_1 = extractFCParams(128, 2, 'rnet/fc2_1');\n    var fc2_2 = extractFCParams(128, 4, 'rnet/fc2_2');\n    return __assign(__assign({}, sharedParams), {\n      fc1: fc1,\n      prelu4_alpha: prelu4_alpha,\n      fc2_1: fc2_1,\n      fc2_2: fc2_2\n    });\n  }\n  function extractONetParams() {\n    var sharedParams = extractSharedParams([3, 32, 64, 64], 'onet');\n    var conv4 = extractConvParams(64, 128, 2, 'onet/conv4');\n    var prelu4_alpha = extractPReluParams(128, 'onet/prelu4_alpha');\n    var fc1 = extractFCParams(1152, 256, 'onet/fc1');\n    var prelu5_alpha = extractPReluParams(256, 'onet/prelu5_alpha');\n    var fc2_1 = extractFCParams(256, 2, 'onet/fc2_1');\n    var fc2_2 = extractFCParams(256, 4, 'onet/fc2_2');\n    var fc2_3 = extractFCParams(256, 10, 'onet/fc2_3');\n    return __assign(__assign({}, sharedParams), {\n      conv4: conv4,\n      prelu4_alpha: prelu4_alpha,\n      fc1: fc1,\n      prelu5_alpha: prelu5_alpha,\n      fc2_1: fc2_1,\n      fc2_2: fc2_2,\n      fc2_3: fc2_3\n    });\n  }\n  return {\n    extractPNetParams: extractPNetParams,\n    extractRNetParams: extractRNetParams,\n    extractONetParams: extractONetParams\n  };\n}\nexport function extractParams(weights) {\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var paramMappings = [];\n  var _b = extractorsFactory(extractWeights, paramMappings),\n    extractPNetParams = _b.extractPNetParams,\n    extractRNetParams = _b.extractRNetParams,\n    extractONetParams = _b.extractONetParams;\n  var pnet = extractPNetParams();\n  var rnet = extractRNetParams();\n  var onet = extractONetParams();\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  return {\n    params: {\n      pnet: pnet,\n      rnet: rnet,\n      onet: onet\n    },\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["tf", "extractConvParamsFactory", "extractFCParamsFactory", "extractWeightsFactory", "extractorsFactory", "extractWeights", "paramMappings", "extractConvParams", "extractFCParams", "extractPReluParams", "size", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "tensor1d", "push", "extractSharedParams", "numFilters", "mappedPrefix", "isRnet", "conv1", "prelu1_alpha", "conv2", "prelu2_alpha", "conv3", "prelu3_alpha", "extractPNetParams", "sharedParams", "conv4_1", "conv4_2", "__assign", "extractRNetParams", "fc1", "prelu4_alpha", "fc2_1", "fc2_2", "extractONetParams", "conv4", "prelu5_alpha", "fc2_3", "extractParams", "weights", "_a", "getRemainingWeights", "_b", "pnet", "rnet", "onet", "length", "Error", "params"], "sources": ["../../../src/mtcnn/extractParams.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SACEC,wBAAwB,EACxBC,sBAAsB,EACtBC,qBAAqB,QAGhB,WAAW;AAGlB,SAASC,iBAAiBA,CAACC,cAAsC,EAAEC,aAA6B;EAE9F,IAAMC,iBAAiB,GAAGN,wBAAwB,CAACI,cAAc,EAAEC,aAAa,CAAC;EACjF,IAAME,eAAe,GAAGN,sBAAsB,CAACG,cAAc,EAAEC,aAAa,CAAC;EAE7E,SAASG,kBAAkBA,CAACC,IAAY,EAAEC,SAAiB;IACzD,IAAMC,KAAK,GAAGZ,EAAE,CAACa,QAAQ,CAACR,cAAc,CAACK,IAAI,CAAC,CAAC;IAC/CJ,aAAa,CAACQ,IAAI,CAAC;MAAEH,SAAS,EAAAA;IAAA,CAAE,CAAC;IACjC,OAAOC,KAAK;EACd;EAEA,SAASG,mBAAmBA,CAACC,UAAoB,EAAEC,YAAoB,EAAEC,MAAuB;IAAvB,IAAAA,MAAA;MAAAA,MAAA,QAAuB;IAAA;IAE9F,IAAMC,KAAK,GAAGZ,iBAAiB,CAACS,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAKC,YAAY,WAAQ,CAAC;IACzF,IAAMG,YAAY,GAAGX,kBAAkB,CAACO,UAAU,CAAC,CAAC,CAAC,EAAKC,YAAY,kBAAe,CAAC;IACtF,IAAMI,KAAK,GAAGd,iBAAiB,CAACS,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAKC,YAAY,WAAQ,CAAC;IACzF,IAAMK,YAAY,GAAGb,kBAAkB,CAACO,UAAU,CAAC,CAAC,CAAC,EAAKC,YAAY,kBAAe,CAAC;IACtF,IAAMM,KAAK,GAAGhB,iBAAiB,CAACS,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEE,MAAM,GAAG,CAAC,GAAG,CAAC,EAAKD,YAAY,WAAQ,CAAC;IACtG,IAAMO,YAAY,GAAGf,kBAAkB,CAACO,UAAU,CAAC,CAAC,CAAC,EAAKC,YAAY,kBAAe,CAAC;IAEtF,OAAO;MAAEE,KAAK,EAAAA,KAAA;MAAEC,YAAY,EAAAA,YAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,YAAY,EAAAA,YAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,YAAY,EAAAA;IAAA,CAAE;EAC1E;EAEA,SAASC,iBAAiBA,CAAA;IAExB,IAAMC,YAAY,GAAGX,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC;IACjE,IAAMY,OAAO,GAAGpB,iBAAiB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC;IAC3D,IAAMqB,OAAO,GAAGrB,iBAAiB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC;IAE3D,OAAAsB,QAAA,CAAAA,QAAA,KAAYH,YAAY;MAAEC,OAAO,EAAAA,OAAA;MAAEC,OAAO,EAAAA;IAAA;EAC5C;EAEA,SAASE,iBAAiBA,CAAA;IAExB,IAAMJ,YAAY,GAAGX,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC;IACvE,IAAMgB,GAAG,GAAGvB,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC;IACjD,IAAMwB,YAAY,GAAGvB,kBAAkB,CAAC,GAAG,EAAE,mBAAmB,CAAC;IACjE,IAAMwB,KAAK,GAAGzB,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC;IACnD,IAAM0B,KAAK,GAAG1B,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC;IAEnD,OAAAqB,QAAA,CAAAA,QAAA,KAAYH,YAAY;MAAEK,GAAG,EAAAA,GAAA;MAAEC,YAAY,EAAAA,YAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA;EAC3D;EAEA,SAASC,iBAAiBA,CAAA;IAExB,IAAMT,YAAY,GAAGX,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC;IACjE,IAAMqB,KAAK,GAAG7B,iBAAiB,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC;IACzD,IAAMyB,YAAY,GAAGvB,kBAAkB,CAAC,GAAG,EAAE,mBAAmB,CAAC;IACjE,IAAMsB,GAAG,GAAGvB,eAAe,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC;IAClD,IAAM6B,YAAY,GAAG5B,kBAAkB,CAAC,GAAG,EAAE,mBAAmB,CAAC;IACjE,IAAMwB,KAAK,GAAGzB,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC;IACnD,IAAM0B,KAAK,GAAG1B,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC;IACnD,IAAM8B,KAAK,GAAG9B,eAAe,CAAC,GAAG,EAAE,EAAE,EAAE,YAAY,CAAC;IAEpD,OAAAqB,QAAA,CAAAA,QAAA,KAAYH,YAAY;MAAEU,KAAK,EAAAA,KAAA;MAAEJ,YAAY,EAAAA,YAAA;MAAED,GAAG,EAAAA,GAAA;MAAEM,YAAY,EAAAA,YAAA;MAAEJ,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEI,KAAK,EAAAA;IAAA;EACvF;EAEA,OAAO;IACLb,iBAAiB,EAAAA,iBAAA;IACjBK,iBAAiB,EAAAA,iBAAA;IACjBK,iBAAiB,EAAAA;GAClB;AAEH;AAEA,OAAM,SAAUI,aAAaA,CAACC,OAAqB;EAE3C,IAAAC,EAAA,GAAAtC,qBAAA,CAAAqC,OAAA,CAG4B;IAFhCnC,cAAA,GAAAoC,EAAA,CAAApC,cAAc;IACdqC,mBAAA,GAAAD,EAAA,CAAAC,mBACgC;EAElC,IAAMpC,aAAa,GAAmB,EAAE;EAElC,IAAAqC,EAAA,GAAAvC,iBAAA,CAAAC,cAAA,EAAAC,aAAA,CAI8C;IAHlDmB,iBAAA,GAAAkB,EAAA,CAAAlB,iBAAiB;IACjBK,iBAAA,GAAAa,EAAA,CAAAb,iBAAiB;IACjBK,iBAAA,GAAAQ,EAAA,CAAAR,iBACkD;EAEpD,IAAMS,IAAI,GAAGnB,iBAAiB,EAAE;EAChC,IAAMoB,IAAI,GAAGf,iBAAiB,EAAE;EAChC,IAAMgB,IAAI,GAAGX,iBAAiB,EAAE;EAEhC,IAAIO,mBAAmB,EAAE,CAACK,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCN,mBAAmB,EAAE,CAACK,MAAQ,CAAC;;EAGnF,OAAO;IAAEE,MAAM,EAAE;MAAEL,IAAI,EAAAA,IAAA;MAAEC,IAAI,EAAAA,IAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;IAAExC,aAAa,EAAAA;EAAA,CAAE;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}