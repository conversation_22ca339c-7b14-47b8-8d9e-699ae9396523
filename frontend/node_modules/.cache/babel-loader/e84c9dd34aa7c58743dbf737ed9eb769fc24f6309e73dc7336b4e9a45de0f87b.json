{"ast": null, "code": "var MtcnnOptions = /** @class */function () {\n  function MtcnnOptions(_a) {\n    var _b = _a === void 0 ? {} : _a,\n      minFaceSize = _b.minFaceSize,\n      scaleFactor = _b.scaleFactor,\n      maxNumScales = _b.maxNumScales,\n      scoreThresholds = _b.scoreThresholds,\n      scaleSteps = _b.scaleSteps;\n    this._name = 'MtcnnOptions';\n    this._minFaceSize = minFaceSize || 20;\n    this._scaleFactor = scaleFactor || 0.709;\n    this._maxNumScales = maxNumScales || 10;\n    this._scoreThresholds = scoreThresholds || [0.6, 0.7, 0.7];\n    this._scaleSteps = scaleSteps;\n    if (typeof this._minFaceSize !== 'number' || this._minFaceSize < 0) {\n      throw new Error(this._name + \" - expected minFaceSize to be a number > 0\");\n    }\n    if (typeof this._scaleFactor !== 'number' || this._scaleFactor <= 0 || this._scaleFactor >= 1) {\n      throw new Error(this._name + \" - expected scaleFactor to be a number between 0 and 1\");\n    }\n    if (typeof this._maxNumScales !== 'number' || this._maxNumScales < 0) {\n      throw new Error(this._name + \" - expected maxNumScales to be a number > 0\");\n    }\n    if (!Array.isArray(this._scoreThresholds) || this._scoreThresholds.length !== 3 || this._scoreThresholds.some(function (th) {\n      return typeof th !== 'number';\n    })) {\n      throw new Error(this._name + \" - expected scoreThresholds to be an array of numbers of length 3\");\n    }\n    if (this._scaleSteps && (!Array.isArray(this._scaleSteps) || this._scaleSteps.some(function (th) {\n      return typeof th !== 'number';\n    }))) {\n      throw new Error(this._name + \" - expected scaleSteps to be an array of numbers\");\n    }\n  }\n  Object.defineProperty(MtcnnOptions.prototype, \"minFaceSize\", {\n    get: function () {\n      return this._minFaceSize;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(MtcnnOptions.prototype, \"scaleFactor\", {\n    get: function () {\n      return this._scaleFactor;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(MtcnnOptions.prototype, \"maxNumScales\", {\n    get: function () {\n      return this._maxNumScales;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(MtcnnOptions.prototype, \"scoreThresholds\", {\n    get: function () {\n      return this._scoreThresholds;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(MtcnnOptions.prototype, \"scaleSteps\", {\n    get: function () {\n      return this._scaleSteps;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  return MtcnnOptions;\n}();\nexport { MtcnnOptions };", "map": {"version": 3, "names": ["MtcnnOptions", "_a", "_b", "minFaceSize", "scaleFactor", "maxNumScales", "scoreT<PERSON><PERSON>olds", "scaleSteps", "_name", "_minFaceSize", "_scaleFactor", "_maxNumScales", "_scoreThresholds", "_scaleSteps", "Error", "Array", "isArray", "length", "some", "th", "Object", "defineProperty", "prototype", "get"], "sources": ["../../../src/mtcnn/MtcnnOptions.ts"], "sourcesContent": [null], "mappings": "AAQA,IAAAA,YAAA;EASE,SAAAA,aAAYC,EAA2F;QAA3FC,EAAA,GAAAD,EAAA,mBAAAA,EAA2F;MAAzFE,WAAA,GAAAD,EAAA,CAAAC,WAAW;MAAEC,WAAA,GAAAF,EAAA,CAAAE,WAAW;MAAEC,YAAA,GAAAH,EAAA,CAAAG,YAAY;MAAEC,eAAA,GAAAJ,EAAA,CAAAI,eAAe;MAAEC,UAAA,GAAAL,EAAA,CAAAK,UAAU;IARvE,KAAAC,KAAK,GAAW,cAAc;IAStC,IAAI,CAACC,YAAY,GAAGN,WAAW,IAAI,EAAE;IACrC,IAAI,CAACO,YAAY,GAAGN,WAAW,IAAI,KAAK;IACxC,IAAI,CAACO,aAAa,GAAGN,YAAY,IAAI,EAAE;IACvC,IAAI,CAACO,gBAAgB,GAAGN,eAAe,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC1D,IAAI,CAACO,WAAW,GAAGN,UAAU;IAE7B,IAAI,OAAO,IAAI,CAACE,YAAY,KAAK,QAAQ,IAAI,IAAI,CAACA,YAAY,GAAG,CAAC,EAAE;MAClE,MAAM,IAAIK,KAAK,CAAI,IAAI,CAACN,KAAK,+CAA4C,CAAC;;IAG5E,IAAI,OAAO,IAAI,CAACE,YAAY,KAAK,QAAQ,IAAI,IAAI,CAACA,YAAY,IAAI,CAAC,IAAI,IAAI,CAACA,YAAY,IAAI,CAAC,EAAE;MAC7F,MAAM,IAAII,KAAK,CAAI,IAAI,CAACN,KAAK,2DAAwD,CAAC;;IAGxF,IAAI,OAAO,IAAI,CAACG,aAAa,KAAK,QAAQ,IAAI,IAAI,CAACA,aAAa,GAAG,CAAC,EAAE;MACpE,MAAM,IAAIG,KAAK,CAAI,IAAI,CAACN,KAAK,gDAA6C,CAAC;;IAG7E,IACE,CAACO,KAAK,CAACC,OAAO,CAAC,IAAI,CAACJ,gBAAgB,CAAC,IAChC,IAAI,CAACA,gBAAgB,CAACK,MAAM,KAAK,CAAC,IAClC,IAAI,CAACL,gBAAgB,CAACM,IAAI,CAAC,UAAAC,EAAE;MAAI,cAAOA,EAAE,KAAK,QAAQ;IAAtB,CAAsB,CAAC,EAC7D;MACA,MAAM,IAAIL,KAAK,CAAI,IAAI,CAACN,KAAK,sEAAmE,CAAC;;IAGnG,IACE,IAAI,CAACK,WAAW,KACV,CAACE,KAAK,CAACC,OAAO,CAAC,IAAI,CAACH,WAAW,CAAC,IAAI,IAAI,CAACA,WAAW,CAACK,IAAI,CAAC,UAAAC,EAAE;MAAI,cAAOA,EAAE,KAAK,QAAQ;IAAtB,CAAsB,CAAC,CAAC,EAC9F;MACA,MAAM,IAAIL,KAAK,CAAI,IAAI,CAACN,KAAK,qDAAkD,CAAC;;EAEpF;EAEAY,MAAA,CAAAC,cAAA,CAAIrB,YAAA,CAAAsB,SAAA,eAAW;SAAf,SAAAC,CAAA;MAA4B,OAAO,IAAI,CAACd,YAAY;IAAC,CAAC;;;;EACtDW,MAAA,CAAAC,cAAA,CAAIrB,YAAA,CAAAsB,SAAA,eAAW;SAAf,SAAAC,CAAA;MAA4B,OAAO,IAAI,CAACb,YAAY;IAAC,CAAC;;;;EACtDU,MAAA,CAAAC,cAAA,CAAIrB,YAAA,CAAAsB,SAAA,gBAAY;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACZ,aAAa;IAAC,CAAC;;;;EACxDS,MAAA,CAAAC,cAAA,CAAIrB,YAAA,CAAAsB,SAAA,mBAAe;SAAnB,SAAAC,CAAA;MAAkC,OAAO,IAAI,CAACX,gBAAgB;IAAC,CAAC;;;;EAChEQ,MAAA,CAAAC,cAAA,CAAIrB,YAAA,CAAAsB,SAAA,cAAU;SAAd,SAAAC,CAAA;MAAyC,OAAO,IAAI,CAACV,WAAW;IAAC,CAAC;;;;EACpE,OAAAb,YAAC;AAAD,CAAC,CAjDD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}