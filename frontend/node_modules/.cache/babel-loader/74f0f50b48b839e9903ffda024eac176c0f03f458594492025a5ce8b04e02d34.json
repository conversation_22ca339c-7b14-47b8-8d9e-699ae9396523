{"ast": null, "code": "import { Box } from '../classes';\nimport { getContext2dOrThrow } from '../dom/getContext2dOrThrow';\nimport { AnchorPosition, DrawTextField, DrawTextFieldOptions } from './DrawTextField';\nvar DrawBoxOptions = /** @class */function () {\n  function DrawBoxOptions(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var boxColor = options.boxColor,\n      lineWidth = options.lineWidth,\n      label = options.label,\n      drawLabelOptions = options.drawLabelOptions;\n    this.boxColor = boxColor || 'rgba(0, 0, 255, 1)';\n    this.lineWidth = lineWidth || 2;\n    this.label = label;\n    var defaultDrawLabelOptions = {\n      anchorPosition: AnchorPosition.BOTTOM_LEFT,\n      backgroundColor: this.boxColor\n    };\n    this.drawLabelOptions = new DrawTextFieldOptions(Object.assign({}, defaultDrawLabelOptions, drawLabelOptions));\n  }\n  return DrawBoxOptions;\n}();\nexport { DrawBoxOptions };\nvar DrawBox = /** @class */function () {\n  function DrawBox(box, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.box = new Box(box);\n    this.options = new DrawBoxOptions(options);\n  }\n  DrawBox.prototype.draw = function (canvasArg) {\n    var ctx = getContext2dOrThrow(canvasArg);\n    var _a = this.options,\n      boxColor = _a.boxColor,\n      lineWidth = _a.lineWidth;\n    var _b = this.box,\n      x = _b.x,\n      y = _b.y,\n      width = _b.width,\n      height = _b.height;\n    ctx.strokeStyle = boxColor;\n    ctx.lineWidth = lineWidth;\n    ctx.strokeRect(x, y, width, height);\n    var label = this.options.label;\n    if (label) {\n      new DrawTextField([label], {\n        x: x - lineWidth / 2,\n        y: y\n      }, this.options.drawLabelOptions).draw(canvasArg);\n    }\n  };\n  return DrawBox;\n}();\nexport { DrawBox };", "map": {"version": 3, "names": ["Box", "getContext2dOrThrow", "AnchorPosition", "DrawTextField", "DrawTextFieldOptions", "DrawBoxOptions", "options", "boxColor", "lineWidth", "label", "drawLabelOptions", "defaultDrawLabelOptions", "anchorPosition", "BOTTOM_LEFT", "backgroundColor", "Object", "assign", "DrawBox", "box", "prototype", "draw", "canvasArg", "ctx", "_a", "_b", "x", "y", "width", "height", "strokeStyle", "strokeRect"], "sources": ["../../../src/draw/DrawBox.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,GAAG,QAA6B,YAAY;AACrD,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,cAAc,EAAEC,aAAa,EAAEC,oBAAoB,QAA+B,iBAAiB;AAS5G,IAAAC,cAAA;EAME,SAAAA,eAAYC,OAA6B;IAA7B,IAAAA,OAAA;MAAAA,OAAA,KAA6B;IAAA;IAC/B,IAAAC,QAAA,GAAAD,OAAA,CAAAC,QAAQ;MAAEC,SAAA,GAAAF,OAAA,CAAAE,SAAS;MAAEC,KAAA,GAAAH,OAAA,CAAAG,KAAK;MAAEC,gBAAA,GAAAJ,OAAA,CAAAI,gBAAgB;IACpD,IAAI,CAACH,QAAQ,GAAGA,QAAQ,IAAI,oBAAoB;IAChD,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,CAAC;IAC/B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAElB,IAAME,uBAAuB,GAAG;MAC9BC,cAAc,EAAEV,cAAc,CAACW,WAAW;MAC1CC,eAAe,EAAE,IAAI,CAACP;KACvB;IACD,IAAI,CAACG,gBAAgB,GAAG,IAAIN,oBAAoB,CAACW,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEL,uBAAuB,EAAED,gBAAgB,CAAC,CAAC;EAChH;EACF,OAAAL,cAAC;AAAD,CAAC,CAlBD;;AAoBA,IAAAY,OAAA;EAIE,SAAAA,QACEC,GAAyB,EACzBZ,OAA6B;IAA7B,IAAAA,OAAA;MAAAA,OAAA,KAA6B;IAAA;IAE7B,IAAI,CAACY,GAAG,GAAG,IAAIlB,GAAG,CAACkB,GAAG,CAAC;IACvB,IAAI,CAACZ,OAAO,GAAG,IAAID,cAAc,CAACC,OAAO,CAAC;EAC5C;EAEAW,OAAA,CAAAE,SAAA,CAAAC,IAAI,GAAJ,UAAKC,SAAgE;IACnE,IAAMC,GAAG,GAAGrB,mBAAmB,CAACoB,SAAS,CAAC;IAEpC,IAAAE,EAAA,QAAAjB,OAAsC;MAApCC,QAAA,GAAAgB,EAAA,CAAAhB,QAAQ;MAAEC,SAAA,GAAAe,EAAA,CAAAf,SAA0B;IAEtC,IAAAgB,EAAA,QAAAN,GAAkC;MAAhCO,CAAA,GAAAD,EAAA,CAAAC,CAAC;MAAEC,CAAA,GAAAF,EAAA,CAAAE,CAAC;MAAEC,KAAA,GAAAH,EAAA,CAAAG,KAAK;MAAEC,MAAA,GAAAJ,EAAA,CAAAI,MAAmB;IACxCN,GAAG,CAACO,WAAW,GAAGtB,QAAQ;IAC1Be,GAAG,CAACd,SAAS,GAAGA,SAAS;IACzBc,GAAG,CAACQ,UAAU,CAACL,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAE3B,IAAAnB,KAAA,QAAAH,OAAA,CAAAG,KAAK;IACb,IAAIA,KAAK,EAAE;MACT,IAAIN,aAAa,CAAC,CAACM,KAAK,CAAC,EAAE;QAAEgB,CAAC,EAAEA,CAAC,GAAIjB,SAAS,GAAG,CAAE;QAAEkB,CAAC,EAAAA;MAAA,CAAE,EAAE,IAAI,CAACpB,OAAO,CAACI,gBAAgB,CAAC,CAACU,IAAI,CAACC,SAAS,CAAC;;EAE5G,CAAC;EACH,OAAAJ,OAAC;AAAD,CAAC,CA3BD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}