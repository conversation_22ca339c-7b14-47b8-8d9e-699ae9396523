{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Card, CardContent, Stack, Chip, Button, Alert, Tabs, Tab, IconButton, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Dashboard as DashboardIcon, People as PeopleIcon, School as SchoolIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon, PersonAdd as PersonAddIcon, Login as LoginIcon, MoreVert as MoreVertIcon, Face as FaceIcon } from '@mui/icons-material';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';\nimport { supabaseService } from '../services/supabaseService';\nimport { UserRole } from '../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `admin-tabpanel-${index}`,\n    \"aria-labelledby\": `admin-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst AdminDashboard = () => {\n  _s();\n  const [tabValue, setTabValue] = useState(0);\n  const [stats, setStats] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [attendanceRecords, setAttendanceRecords] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // États pour la gestion multi-utilisateurs\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [userMenuAnchor, setUserMenuAnchor] = useState(null);\n  const [loginAsDialogOpen, setLoginAsDialogOpen] = useState(false);\n  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);\n\n  // Données pour les graphiques\n  const [attendanceChartData, setAttendanceChartData] = useState([]);\n  const [courseStatsData, setCourseStatsData] = useState([]);\n\n  /**\n   * Charge toutes les données du dashboard\n   */\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les données en parallèle\n      const [usersData, coursesData, attendanceData] = await Promise.all([supabaseService.getUsers(), supabaseService.getCourses(), supabaseService.getAttendanceRecords()]);\n      setUsers(usersData);\n      setCourses(coursesData);\n      setAttendanceRecords(attendanceData);\n\n      // Calculer les statistiques\n      const dashboardStats = {\n        totalStudents: usersData.filter(u => u.role === UserRole.STUDENT).length,\n        totalTeachers: usersData.filter(u => u.role === UserRole.TEACHER).length,\n        totalCourses: coursesData.length,\n        todaySessions: 0,\n        // À calculer selon les sessions du jour\n        averageAttendanceRate: calculateAverageAttendanceRate(attendanceData),\n        recentActivity: [] // À implémenter\n      };\n      setStats(dashboardStats);\n\n      // Préparer les données pour les graphiques\n      prepareChartData(attendanceData, coursesData);\n    } catch (err) {\n      console.error('Erreur chargement dashboard:', err);\n      setError('Impossible de charger les données du dashboard');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Calcule le taux de présence moyen\n   */\n  const calculateAverageAttendanceRate = records => {\n    if (records.length === 0) return 0;\n    const presentRecords = records.filter(r => r.status === 'present' || r.status === 'late');\n    return Math.round(presentRecords.length / records.length * 100);\n  };\n\n  /**\n   * Prépare les données pour les graphiques\n   */\n  const prepareChartData = (attendance, courses) => {\n    // Données de présence par jour (7 derniers jours)\n    const last7Days = Array.from({\n      length: 7\n    }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      return date.toISOString().split('T')[0];\n    }).reverse();\n    const attendanceByDay = last7Days.map(date => {\n      const dayRecords = attendance.filter(r => r.date === date);\n      const present = dayRecords.filter(r => r.status === 'present').length;\n      const late = dayRecords.filter(r => r.status === 'late').length;\n      const absent = dayRecords.filter(r => r.status === 'absent').length;\n      return {\n        date: new Date(date).toLocaleDateString('fr-FR', {\n          weekday: 'short',\n          day: 'numeric'\n        }),\n        present,\n        late,\n        absent,\n        total: dayRecords.length,\n        rate: dayRecords.length > 0 ? Math.round((present + late) / dayRecords.length * 100) : 0\n      };\n    });\n    setAttendanceChartData(attendanceByDay);\n\n    // Statistiques par cours\n    const courseStats = courses.map(course => {\n      const courseRecords = attendance.filter(r => r.course.id === course.id);\n      const present = courseRecords.filter(r => r.status === 'present').length;\n      const late = courseRecords.filter(r => r.status === 'late').length;\n      const absent = courseRecords.filter(r => r.status === 'absent').length;\n      return {\n        name: course.name.length > 15 ? course.name.substring(0, 15) + '...' : course.name,\n        present,\n        late,\n        absent,\n        total: courseRecords.length,\n        rate: courseRecords.length > 0 ? Math.round((present + late) / courseRecords.length * 100) : 0\n      };\n    });\n    setCourseStatsData(courseStats);\n  };\n\n  /**\n   * Se connecter en tant qu'autre utilisateur\n   */\n  const loginAsUser = async user => {\n    try {\n      // Ici, on implémenterait la logique de connexion en tant qu'autre utilisateur\n      // Pour l'instant, on simule juste\n      console.log(`Connexion en tant que ${user.firstName} ${user.lastName}`);\n      setLoginAsDialogOpen(false);\n      setSelectedUser(null);\n\n      // Rediriger vers le dashboard approprié selon le rôle\n      // window.location.href = user.role === 'teacher' ? '/teacher-dashboard' : '/student-dashboard';\n    } catch (err) {\n      console.error('Erreur connexion utilisateur:', err);\n      setError('Impossible de se connecter en tant que cet utilisateur');\n    }\n  };\n\n  /**\n   * Ouvre le menu utilisateur\n   */\n  const handleUserMenuClick = (event, user) => {\n    setUserMenuAnchor(event.currentTarget);\n    setSelectedUser(user);\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      children: \"Dashboard Administrateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this), stats && /*#__PURE__*/_jsxDEV(Stack, {\n      direction: {\n        xs: 'column',\n        md: 'row'\n      },\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(PeopleIcon, {\n              color: \"primary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalStudents\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"\\xC9tudiants\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(SchoolIcon, {\n              color: \"secondary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalTeachers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Professeurs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(DashboardIcon, {\n              color: \"success\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalCourses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n              color: \"warning\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: [stats.averageAttendanceRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Taux Pr\\xE9sence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: (_, newValue) => setTabValue(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Vue d'ensemble\",\n          icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Utilisateurs\",\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Cours\",\n          icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Reconnaissance Faciale\",\n          icon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 4,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"\\xC9volution des Pr\\xE9sences (7 derniers jours)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(LineChart, {\n                data: attendanceChartData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Line, {\n                  type: \"monotone\",\n                  dataKey: \"rate\",\n                  stroke: \"#8884d8\",\n                  name: \"Taux %\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Pr\\xE9sences par Cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: courseStatsData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"present\",\n                  stackId: \"a\",\n                  fill: \"#4caf50\",\n                  name: \"Pr\\xE9sent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"late\",\n                  stackId: \"a\",\n                  fill: \"#ff9800\",\n                  name: \"Retard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"absent\",\n                  stackId: \"a\",\n                  fill: \"#f44336\",\n                  name: \"Absent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Gestion des Utilisateurs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 26\n            }, this),\n            onClick: () => {/* Ouvrir dialog création utilisateur */},\n            children: \"Nouvel Utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: users.map(user => /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 2,\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      children: [user.firstName, \" \", user.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      color: \"text.secondary\",\n                      children: [user.email, \" \\u2022 \", user.roleDisplay]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: user.role,\n                    color: user.role === 'admin' ? 'error' : user.role === 'teacher' ? 'primary' : 'secondary',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: e => handleUserMenuClick(e, user),\n                  children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)\n          }, user.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Gestion des Cours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: courses.map(course => /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: course.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              children: course.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              sx: {\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: `${course.credits} crédits`,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: course.semester,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Syst\\xE8me de Reconnaissance Faciale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Statistiques du syst\\xE8me de reconnaissance faciale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: () => setFaceRegistrationOpen(true),\n              children: \"G\\xE9rer les Encodages Faciaux\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: userMenuAnchor,\n      open: Boolean(userMenuAnchor),\n      onClose: () => setUserMenuAnchor(null),\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => setLoginAsDialogOpen(true),\n        children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), \"Se connecter en tant que\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {/* Éditer utilisateur */},\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), \"Modifier\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: loginAsDialogOpen,\n      onClose: () => setLoginAsDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Se connecter en tant qu'utilisateur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Voulez-vous vous connecter en tant que\", ' ', /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [selectedUser.firstName, \" \", selectedUser.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), \" ?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setLoginAsDialogOpen(false),\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => selectedUser && loginAsUser(selectedUser),\n          variant: \"contained\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 232,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"tttEJ3/5KgrC/HMBrjjpp2fBUm4=\");\n_c2 = AdminDashboard;\nexport default AdminDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tabs", "Tab", "IconButton", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Dashboard", "DashboardIcon", "People", "PeopleIcon", "School", "SchoolIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "PersonAdd", "PersonAddIcon", "<PERSON><PERSON>", "LoginIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Face", "FaceIcon", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "supabaseService", "UserRole", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminDashboard", "_s", "tabValue", "setTabValue", "stats", "setStats", "users", "setUsers", "courses", "setCourses", "attendanceRecords", "setAttendanceRecords", "loading", "setLoading", "error", "setError", "selected<PERSON>ser", "setSelectedUser", "userMenuAnchor", "setUserMenuAnchor", "loginAsDialogOpen", "setLoginAsDialogOpen", "faceRegistrationOpen", "setFaceRegistrationOpen", "attendanceChartData", "setAttendanceChartData", "courseStatsData", "setCourseStatsData", "loadDashboardData", "usersData", "coursesData", "attendanceData", "Promise", "all", "getUsers", "getCourses", "getAttendanceRecords", "dashboardStats", "totalStudents", "filter", "u", "STUDENT", "length", "totalTeachers", "TEACHER", "totalCourses", "todaySessions", "averageAttendanceRate", "calculateAverageAttendanceRate", "recentActivity", "prepareChartData", "err", "console", "records", "presentRecords", "r", "status", "Math", "round", "attendance", "last7Days", "Array", "from", "_", "i", "date", "Date", "setDate", "getDate", "toISOString", "split", "reverse", "attendanceByDay", "map", "dayRecords", "present", "late", "absent", "toLocaleDateString", "weekday", "day", "total", "rate", "courseStats", "course", "courseRecords", "name", "substring", "loginAsUser", "user", "log", "firstName", "lastName", "handleUserMenuClick", "event", "currentTarget", "COLORS", "max<PERSON><PERSON><PERSON>", "py", "variant", "gutterBottom", "severity", "mb", "onClose", "direction", "xs", "md", "spacing", "flex", "alignItems", "color", "fontSize", "borderBottom", "borderColor", "onChange", "newValue", "label", "icon", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "stackId", "fill", "display", "justifyContent", "startIcon", "onClick", "email", "roleDisplay", "size", "e", "description", "mt", "credits", "semester", "anchorEl", "open", "Boolean", "mr", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Container,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Stack,\n  Chip,\n  Button,\n  Alert,\n  Tabs,\n  Tab,\n  IconButton,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  People as PeopleIcon,\n  School as SchoolIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon,\n  PersonAdd as PersonAddIcon,\n  Login as LoginIcon,\n  MoreVert as MoreVertIcon,\n  Face as FaceIcon\n} from '@mui/icons-material';\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  BarChart,\n  Bar,\n  Pie<PERSON>hart,\n  Pie,\n  Cell\n} from 'recharts';\nimport { supabaseService } from '../services/supabaseService';\nimport { faceRecognitionService } from '../services/faceRecognitionService';\nimport { User, Course, AttendanceRecord, DashboardStats, UserRole } from '../types';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`admin-tabpanel-${index}`}\n      aria-labelledby={`admin-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst AdminDashboard: React.FC = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  // États pour la gestion multi-utilisateurs\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);\n  const [loginAsDialogOpen, setLoginAsDialogOpen] = useState(false);\n  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);\n\n  // Données pour les graphiques\n  const [attendanceChartData, setAttendanceChartData] = useState<any[]>([]);\n  const [courseStatsData, setCourseStatsData] = useState<any[]>([]);\n\n  /**\n   * Charge toutes les données du dashboard\n   */\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les données en parallèle\n      const [usersData, coursesData, attendanceData] = await Promise.all([\n        supabaseService.getUsers(),\n        supabaseService.getCourses(),\n        supabaseService.getAttendanceRecords()\n      ]);\n\n      setUsers(usersData);\n      setCourses(coursesData);\n      setAttendanceRecords(attendanceData);\n\n      // Calculer les statistiques\n      const dashboardStats: DashboardStats = {\n        totalStudents: usersData.filter(u => u.role === UserRole.STUDENT).length,\n        totalTeachers: usersData.filter(u => u.role === UserRole.TEACHER).length,\n        totalCourses: coursesData.length,\n        todaySessions: 0, // À calculer selon les sessions du jour\n        averageAttendanceRate: calculateAverageAttendanceRate(attendanceData),\n        recentActivity: [] // À implémenter\n      };\n\n      setStats(dashboardStats);\n\n      // Préparer les données pour les graphiques\n      prepareChartData(attendanceData, coursesData);\n\n    } catch (err) {\n      console.error('Erreur chargement dashboard:', err);\n      setError('Impossible de charger les données du dashboard');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Calcule le taux de présence moyen\n   */\n  const calculateAverageAttendanceRate = (records: AttendanceRecord[]): number => {\n    if (records.length === 0) return 0;\n    \n    const presentRecords = records.filter(r => r.status === 'present' || r.status === 'late');\n    return Math.round((presentRecords.length / records.length) * 100);\n  };\n\n  /**\n   * Prépare les données pour les graphiques\n   */\n  const prepareChartData = (attendance: AttendanceRecord[], courses: Course[]) => {\n    // Données de présence par jour (7 derniers jours)\n    const last7Days = Array.from({ length: 7 }, (_, i) => {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      return date.toISOString().split('T')[0];\n    }).reverse();\n\n    const attendanceByDay = last7Days.map(date => {\n      const dayRecords = attendance.filter(r => r.date === date);\n      const present = dayRecords.filter(r => r.status === 'present').length;\n      const late = dayRecords.filter(r => r.status === 'late').length;\n      const absent = dayRecords.filter(r => r.status === 'absent').length;\n      \n      return {\n        date: new Date(date).toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' }),\n        present,\n        late,\n        absent,\n        total: dayRecords.length,\n        rate: dayRecords.length > 0 ? Math.round(((present + late) / dayRecords.length) * 100) : 0\n      };\n    });\n\n    setAttendanceChartData(attendanceByDay);\n\n    // Statistiques par cours\n    const courseStats = courses.map(course => {\n      const courseRecords = attendance.filter(r => r.course.id === course.id);\n      const present = courseRecords.filter(r => r.status === 'present').length;\n      const late = courseRecords.filter(r => r.status === 'late').length;\n      const absent = courseRecords.filter(r => r.status === 'absent').length;\n      \n      return {\n        name: course.name.length > 15 ? course.name.substring(0, 15) + '...' : course.name,\n        present,\n        late,\n        absent,\n        total: courseRecords.length,\n        rate: courseRecords.length > 0 ? Math.round(((present + late) / courseRecords.length) * 100) : 0\n      };\n    });\n\n    setCourseStatsData(courseStats);\n  };\n\n  /**\n   * Se connecter en tant qu'autre utilisateur\n   */\n  const loginAsUser = async (user: User) => {\n    try {\n      // Ici, on implémenterait la logique de connexion en tant qu'autre utilisateur\n      // Pour l'instant, on simule juste\n      console.log(`Connexion en tant que ${user.firstName} ${user.lastName}`);\n      setLoginAsDialogOpen(false);\n      setSelectedUser(null);\n      \n      // Rediriger vers le dashboard approprié selon le rôle\n      // window.location.href = user.role === 'teacher' ? '/teacher-dashboard' : '/student-dashboard';\n      \n    } catch (err) {\n      console.error('Erreur connexion utilisateur:', err);\n      setError('Impossible de se connecter en tant que cet utilisateur');\n    }\n  };\n\n  /**\n   * Ouvre le menu utilisateur\n   */\n  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>, user: User) => {\n    setUserMenuAnchor(event.currentTarget);\n    setSelectedUser(user);\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 4 }}>\n      <Typography variant=\"h3\" gutterBottom>\n        Dashboard Administrateur\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Statistiques générales */}\n      {stats && (\n        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <PeopleIcon color=\"primary\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.totalStudents}</Typography>\n                  <Typography color=\"text.secondary\">Étudiants</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <SchoolIcon color=\"secondary\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.totalTeachers}</Typography>\n                  <Typography color=\"text.secondary\">Professeurs</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <DashboardIcon color=\"success\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.totalCourses}</Typography>\n                  <Typography color=\"text.secondary\">Cours</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n\n          <Card sx={{ flex: 1 }}>\n            <CardContent>\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                <AnalyticsIcon color=\"warning\" fontSize=\"large\" />\n                <Box>\n                  <Typography variant=\"h4\">{stats.averageAttendanceRate}%</Typography>\n                  <Typography color=\"text.secondary\">Taux Présence</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Stack>\n      )}\n\n      {/* Onglets */}\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>\n        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>\n          <Tab label=\"Vue d'ensemble\" icon={<DashboardIcon />} />\n          <Tab label=\"Utilisateurs\" icon={<PeopleIcon />} />\n          <Tab label=\"Cours\" icon={<SchoolIcon />} />\n          <Tab label=\"Reconnaissance Faciale\" icon={<FaceIcon />} />\n        </Tabs>\n      </Box>\n\n      {/* Contenu des onglets */}\n      <TabPanel value={tabValue} index={0}>\n        {/* Vue d'ensemble avec graphiques */}\n        <Stack spacing={4}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Évolution des Présences (7 derniers jours)\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <LineChart data={attendanceChartData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"date\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Line type=\"monotone\" dataKey=\"rate\" stroke=\"#8884d8\" name=\"Taux %\" />\n                </LineChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Présences par Cours\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={courseStatsData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"name\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"present\" stackId=\"a\" fill=\"#4caf50\" name=\"Présent\" />\n                  <Bar dataKey=\"late\" stackId=\"a\" fill=\"#ff9800\" name=\"Retard\" />\n                  <Bar dataKey=\"absent\" stackId=\"a\" fill=\"#f44336\" name=\"Absent\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={1}>\n        {/* Gestion des utilisateurs */}\n        <Stack spacing={3}>\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n            <Typography variant=\"h6\">Gestion des Utilisateurs</Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<PersonAddIcon />}\n              onClick={() => {/* Ouvrir dialog création utilisateur */}}\n            >\n              Nouvel Utilisateur\n            </Button>\n          </Box>\n\n          <Stack spacing={2}>\n            {users.map((user) => (\n              <Card key={user.id}>\n                <CardContent>\n                  <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n                      <Box>\n                        <Typography variant=\"h6\">\n                          {user.firstName} {user.lastName}\n                        </Typography>\n                        <Typography color=\"text.secondary\">\n                          {user.email} • {user.roleDisplay}\n                        </Typography>\n                      </Box>\n                      <Chip \n                        label={user.role} \n                        color={user.role === 'admin' ? 'error' : user.role === 'teacher' ? 'primary' : 'secondary'}\n                        size=\"small\"\n                      />\n                    </Stack>\n                    \n                    <IconButton onClick={(e) => handleUserMenuClick(e, user)}>\n                      <MoreVertIcon />\n                    </IconButton>\n                  </Box>\n                </CardContent>\n              </Card>\n            ))}\n          </Stack>\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={2}>\n        {/* Gestion des cours */}\n        <Typography variant=\"h6\" gutterBottom>Gestion des Cours</Typography>\n        <Stack spacing={2}>\n          {courses.map((course) => (\n            <Card key={course.id}>\n              <CardContent>\n                <Typography variant=\"h6\">{course.name}</Typography>\n                <Typography color=\"text.secondary\">\n                  {course.description}\n                </Typography>\n                <Stack direction=\"row\" spacing={1} sx={{ mt: 1 }}>\n                  <Chip label={`${course.credits} crédits`} size=\"small\" />\n                  <Chip label={course.semester} size=\"small\" />\n                </Stack>\n              </CardContent>\n            </Card>\n          ))}\n        </Stack>\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={3}>\n        {/* Reconnaissance faciale */}\n        <Typography variant=\"h6\" gutterBottom>Système de Reconnaissance Faciale</Typography>\n        <Card>\n          <CardContent>\n            <Stack spacing={2}>\n              <Typography>\n                Statistiques du système de reconnaissance faciale\n              </Typography>\n              {/* Ici on afficherait les stats du service de reconnaissance */}\n              <Button\n                variant=\"outlined\"\n                onClick={() => setFaceRegistrationOpen(true)}\n              >\n                Gérer les Encodages Faciaux\n              </Button>\n            </Stack>\n          </CardContent>\n        </Card>\n      </TabPanel>\n\n      {/* Menu utilisateur */}\n      <Menu\n        anchorEl={userMenuAnchor}\n        open={Boolean(userMenuAnchor)}\n        onClose={() => setUserMenuAnchor(null)}\n      >\n        <MenuItem onClick={() => setLoginAsDialogOpen(true)}>\n          <LoginIcon sx={{ mr: 1 }} />\n          Se connecter en tant que\n        </MenuItem>\n        <MenuItem onClick={() => {/* Éditer utilisateur */}}>\n          <SettingsIcon sx={{ mr: 1 }} />\n          Modifier\n        </MenuItem>\n      </Menu>\n\n      {/* Dialog connexion en tant qu'utilisateur */}\n      <Dialog open={loginAsDialogOpen} onClose={() => setLoginAsDialogOpen(false)}>\n        <DialogTitle>Se connecter en tant qu'utilisateur</DialogTitle>\n        <DialogContent>\n          {selectedUser && (\n            <Typography>\n              Voulez-vous vous connecter en tant que{' '}\n              <strong>{selectedUser.firstName} {selectedUser.lastName}</strong> ?\n            </Typography>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setLoginAsDialogOpen(false)}>Annuler</Button>\n          <Button \n            onClick={() => selectedUser && loginAsUser(selectedUser)}\n            variant=\"contained\"\n          >\n            Se connecter\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QAKR,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,QAIE,UAAU;AACjB,SAASC,eAAe,QAAQ,6BAA6B;AAE7D,SAAyDC,QAAQ,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQpF,SAASC,QAAQA,CAACC,KAAoB,EAAE;EACtC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAClD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,kBAAkBJ,KAAK,EAAG;IAC9B,mBAAiB,aAAaA,KAAK,EAAG;IAAA,GAClCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAAChD,GAAG;MAAC0D,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAbQf,QAAQ;AAejB,MAAMgB,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACyE,KAAK,EAAEC,QAAQ,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAqB,EAAE,CAAC;EAClF,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiF,KAAK,EAAEC,QAAQ,CAAC,GAAGlF,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAM,CAACuF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC2F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5F,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAQ,EAAE,CAAC;;EAEjE;AACF;AACA;EACE,MAAM+F,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM,CAACc,SAAS,EAAEC,WAAW,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjErD,eAAe,CAACsD,QAAQ,CAAC,CAAC,EAC1BtD,eAAe,CAACuD,UAAU,CAAC,CAAC,EAC5BvD,eAAe,CAACwD,oBAAoB,CAAC,CAAC,CACvC,CAAC;MAEF7B,QAAQ,CAACsB,SAAS,CAAC;MACnBpB,UAAU,CAACqB,WAAW,CAAC;MACvBnB,oBAAoB,CAACoB,cAAc,CAAC;;MAEpC;MACA,MAAMM,cAA8B,GAAG;QACrCC,aAAa,EAAET,SAAS,CAACU,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClD,IAAI,KAAKT,QAAQ,CAAC4D,OAAO,CAAC,CAACC,MAAM;QACxEC,aAAa,EAAEd,SAAS,CAACU,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClD,IAAI,KAAKT,QAAQ,CAAC+D,OAAO,CAAC,CAACF,MAAM;QACxEG,YAAY,EAAEf,WAAW,CAACY,MAAM;QAChCI,aAAa,EAAE,CAAC;QAAE;QAClBC,qBAAqB,EAAEC,8BAA8B,CAACjB,cAAc,CAAC;QACrEkB,cAAc,EAAE,EAAE,CAAC;MACrB,CAAC;MAED5C,QAAQ,CAACgC,cAAc,CAAC;;MAExB;MACAa,gBAAgB,CAACnB,cAAc,EAAED,WAAW,CAAC;IAE/C,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAACtC,KAAK,CAAC,8BAA8B,EAAEqC,GAAG,CAAC;MAClDpC,QAAQ,CAAC,gDAAgD,CAAC;IAC5D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMmC,8BAA8B,GAAIK,OAA2B,IAAa;IAC9E,IAAIA,OAAO,CAACX,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAElC,MAAMY,cAAc,GAAGD,OAAO,CAACd,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,IAAID,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC;IACzF,OAAOC,IAAI,CAACC,KAAK,CAAEJ,cAAc,CAACZ,MAAM,GAAGW,OAAO,CAACX,MAAM,GAAI,GAAG,CAAC;EACnE,CAAC;;EAED;AACF;AACA;EACE,MAAMQ,gBAAgB,GAAGA,CAACS,UAA8B,EAAEnD,OAAiB,KAAK;IAC9E;IACA,MAAMoD,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEpB,MAAM,EAAE;IAAE,CAAC,EAAE,CAACqB,CAAC,EAAEC,CAAC,KAAK;MACpD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC;MACvBD,IAAI,CAACE,OAAO,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,GAAGJ,CAAC,CAAC;MAChC,OAAOC,IAAI,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEZ,MAAMC,eAAe,GAAGZ,SAAS,CAACa,GAAG,CAACR,IAAI,IAAI;MAC5C,MAAMS,UAAU,GAAGf,UAAU,CAACpB,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACU,IAAI,KAAKA,IAAI,CAAC;MAC1D,MAAMU,OAAO,GAAGD,UAAU,CAACnC,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACd,MAAM;MACrE,MAAMkC,IAAI,GAAGF,UAAU,CAACnC,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC,CAACd,MAAM;MAC/D,MAAMmC,MAAM,GAAGH,UAAU,CAACnC,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACd,MAAM;MAEnE,OAAO;QACLuB,IAAI,EAAE,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACa,kBAAkB,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAU,CAAC,CAAC;QACtFL,OAAO;QACPC,IAAI;QACJC,MAAM;QACNI,KAAK,EAAEP,UAAU,CAAChC,MAAM;QACxBwC,IAAI,EAAER,UAAU,CAAChC,MAAM,GAAG,CAAC,GAAGe,IAAI,CAACC,KAAK,CAAE,CAACiB,OAAO,GAAGC,IAAI,IAAIF,UAAU,CAAChC,MAAM,GAAI,GAAG,CAAC,GAAG;MAC3F,CAAC;IACH,CAAC,CAAC;IAEFjB,sBAAsB,CAAC+C,eAAe,CAAC;;IAEvC;IACA,MAAMW,WAAW,GAAG3E,OAAO,CAACiE,GAAG,CAACW,MAAM,IAAI;MACxC,MAAMC,aAAa,GAAG1B,UAAU,CAACpB,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAAC6B,MAAM,CAAC5F,EAAE,KAAK4F,MAAM,CAAC5F,EAAE,CAAC;MACvE,MAAMmF,OAAO,GAAGU,aAAa,CAAC9C,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACd,MAAM;MACxE,MAAMkC,IAAI,GAAGS,aAAa,CAAC9C,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,MAAM,CAAC,CAACd,MAAM;MAClE,MAAMmC,MAAM,GAAGQ,aAAa,CAAC9C,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACd,MAAM;MAEtE,OAAO;QACL4C,IAAI,EAAEF,MAAM,CAACE,IAAI,CAAC5C,MAAM,GAAG,EAAE,GAAG0C,MAAM,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGH,MAAM,CAACE,IAAI;QAClFX,OAAO;QACPC,IAAI;QACJC,MAAM;QACNI,KAAK,EAAEI,aAAa,CAAC3C,MAAM;QAC3BwC,IAAI,EAAEG,aAAa,CAAC3C,MAAM,GAAG,CAAC,GAAGe,IAAI,CAACC,KAAK,CAAE,CAACiB,OAAO,GAAGC,IAAI,IAAIS,aAAa,CAAC3C,MAAM,GAAI,GAAG,CAAC,GAAG;MACjG,CAAC;IACH,CAAC,CAAC;IAEFf,kBAAkB,CAACwD,WAAW,CAAC;EACjC,CAAC;;EAED;AACF;AACA;EACE,MAAMK,WAAW,GAAG,MAAOC,IAAU,IAAK;IACxC,IAAI;MACF;MACA;MACArC,OAAO,CAACsC,GAAG,CAAC,yBAAyBD,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACG,QAAQ,EAAE,CAAC;MACvEvE,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA;IAEF,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZC,OAAO,CAACtC,KAAK,CAAC,+BAA+B,EAAEqC,GAAG,CAAC;MACnDpC,QAAQ,CAAC,wDAAwD,CAAC;IACpE;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM8E,mBAAmB,GAAGA,CAACC,KAAoC,EAAEL,IAAU,KAAK;IAChFtE,iBAAiB,CAAC2E,KAAK,CAACC,aAAa,CAAC;IACtC9E,eAAe,CAACwE,IAAI,CAAC;EACvB,CAAC;;EAED;EACA3J,SAAS,CAAC,MAAM;IACd8F,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoE,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAE3D,oBACEjH,OAAA,CAAC/C,SAAS;IAACiK,QAAQ,EAAC,IAAI;IAACxG,EAAE,EAAE;MAAEyG,EAAE,EAAE;IAAE,CAAE;IAAAhH,QAAA,gBACrCH,OAAA,CAAC9C,UAAU;MAACkK,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAlH,QAAA,EAAC;IAEtC;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZgB,KAAK,iBACJ/B,OAAA,CAACxC,KAAK;MAAC8J,QAAQ,EAAC,OAAO;MAAC5G,EAAE,EAAE;QAAE6G,EAAE,EAAE;MAAE,CAAE;MAACC,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,IAAI,CAAE;MAAA7B,QAAA,EAClE4B;IAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGAM,KAAK,iBACJrB,OAAA,CAAC3C,KAAK;MAACoK,SAAS,EAAE;QAAEC,EAAE,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAM,CAAE;MAACC,OAAO,EAAE,CAAE;MAAClH,EAAE,EAAE;QAAE6G,EAAE,EAAE;MAAE,CAAE;MAAApH,QAAA,gBACvEH,OAAA,CAAC7C,IAAI;QAACuD,EAAE,EAAE;UAAEmH,IAAI,EAAE;QAAE,CAAE;QAAA1H,QAAA,eACpBH,OAAA,CAAC5C,WAAW;UAAA+C,QAAA,eACVH,OAAA,CAAC3C,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACK,UAAU,EAAC,QAAQ;YAACF,OAAO,EAAE,CAAE;YAAAzH,QAAA,gBACpDH,OAAA,CAAC3B,UAAU;cAAC0J,KAAK,EAAC,SAAS;cAACC,QAAQ,EAAC;YAAO;cAAApH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/Cf,OAAA,CAAChD,GAAG;cAAAmD,QAAA,gBACFH,OAAA,CAAC9C,UAAU;gBAACkK,OAAO,EAAC,IAAI;gBAAAjH,QAAA,EAAEkB,KAAK,CAACkC;cAAa;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3Df,OAAA,CAAC9C,UAAU;gBAAC6K,KAAK,EAAC,gBAAgB;gBAAA5H,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAAC7C,IAAI;QAACuD,EAAE,EAAE;UAAEmH,IAAI,EAAE;QAAE,CAAE;QAAA1H,QAAA,eACpBH,OAAA,CAAC5C,WAAW;UAAA+C,QAAA,eACVH,OAAA,CAAC3C,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACK,UAAU,EAAC,QAAQ;YAACF,OAAO,EAAE,CAAE;YAAAzH,QAAA,gBACpDH,OAAA,CAACzB,UAAU;cAACwJ,KAAK,EAAC,WAAW;cAACC,QAAQ,EAAC;YAAO;cAAApH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDf,OAAA,CAAChD,GAAG;cAAAmD,QAAA,gBACFH,OAAA,CAAC9C,UAAU;gBAACkK,OAAO,EAAC,IAAI;gBAAAjH,QAAA,EAAEkB,KAAK,CAACuC;cAAa;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3Df,OAAA,CAAC9C,UAAU;gBAAC6K,KAAK,EAAC,gBAAgB;gBAAA5H,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAAC7C,IAAI;QAACuD,EAAE,EAAE;UAAEmH,IAAI,EAAE;QAAE,CAAE;QAAA1H,QAAA,eACpBH,OAAA,CAAC5C,WAAW;UAAA+C,QAAA,eACVH,OAAA,CAAC3C,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACK,UAAU,EAAC,QAAQ;YAACF,OAAO,EAAE,CAAE;YAAAzH,QAAA,gBACpDH,OAAA,CAAC7B,aAAa;cAAC4J,KAAK,EAAC,SAAS;cAACC,QAAQ,EAAC;YAAO;cAAApH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDf,OAAA,CAAChD,GAAG;cAAAmD,QAAA,gBACFH,OAAA,CAAC9C,UAAU;gBAACkK,OAAO,EAAC,IAAI;gBAAAjH,QAAA,EAAEkB,KAAK,CAACyC;cAAY;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1Df,OAAA,CAAC9C,UAAU;gBAAC6K,KAAK,EAAC,gBAAgB;gBAAA5H,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAAC7C,IAAI;QAACuD,EAAE,EAAE;UAAEmH,IAAI,EAAE;QAAE,CAAE;QAAA1H,QAAA,eACpBH,OAAA,CAAC5C,WAAW;UAAA+C,QAAA,eACVH,OAAA,CAAC3C,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACK,UAAU,EAAC,QAAQ;YAACF,OAAO,EAAE,CAAE;YAAAzH,QAAA,gBACpDH,OAAA,CAACvB,aAAa;cAACsJ,KAAK,EAAC,SAAS;cAACC,QAAQ,EAAC;YAAO;cAAApH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDf,OAAA,CAAChD,GAAG;cAAAmD,QAAA,gBACFH,OAAA,CAAC9C,UAAU;gBAACkK,OAAO,EAAC,IAAI;gBAAAjH,QAAA,GAAEkB,KAAK,CAAC2C,qBAAqB,EAAC,GAAC;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpEf,OAAA,CAAC9C,UAAU;gBAAC6K,KAAK,EAAC,gBAAgB;gBAAA5H,QAAA,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACR,eAGDf,OAAA,CAAChD,GAAG;MAAC0D,EAAE,EAAE;QAAEuH,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEX,EAAE,EAAE;MAAE,CAAE;MAAApH,QAAA,eAC1DH,OAAA,CAACvC,IAAI;QAAC2C,KAAK,EAAEe,QAAS;QAACgH,QAAQ,EAAEA,CAACnD,CAAC,EAAEoD,QAAQ,KAAKhH,WAAW,CAACgH,QAAQ,CAAE;QAAAjI,QAAA,gBACtEH,OAAA,CAACtC,GAAG;UAAC2K,KAAK,EAAC,gBAAgB;UAACC,IAAI,eAAEtI,OAAA,CAAC7B,aAAa;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDf,OAAA,CAACtC,GAAG;UAAC2K,KAAK,EAAC,cAAc;UAACC,IAAI,eAAEtI,OAAA,CAAC3B,UAAU;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDf,OAAA,CAACtC,GAAG;UAAC2K,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEtI,OAAA,CAACzB,UAAU;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3Cf,OAAA,CAACtC,GAAG;UAAC2K,KAAK,EAAC,wBAAwB;UAACC,IAAI,eAAEtI,OAAA,CAACb,QAAQ;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAAC3C,KAAK;QAACuK,OAAO,EAAE,CAAE;QAAAzH,QAAA,gBAChBH,OAAA,CAAC7C,IAAI;UAAAgD,QAAA,eACHH,OAAA,CAAC5C,WAAW;YAAA+C,QAAA,gBACVH,OAAA,CAAC9C,UAAU;cAACkK,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAlH,QAAA,EAAC;YAEtC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbf,OAAA,CAACN,mBAAmB;cAAC6I,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAArI,QAAA,eAC5CH,OAAA,CAACZ,SAAS;gBAACqJ,IAAI,EAAEhG,mBAAoB;gBAAAtC,QAAA,gBACnCH,OAAA,CAACR,aAAa;kBAACkJ,eAAe,EAAC;gBAAK;kBAAA9H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCf,OAAA,CAACV,KAAK;kBAACqJ,OAAO,EAAC;gBAAM;kBAAA/H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBf,OAAA,CAACT,KAAK;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTf,OAAA,CAACP,OAAO;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXf,OAAA,CAACX,IAAI;kBAACuJ,IAAI,EAAC,UAAU;kBAACD,OAAO,EAAC,MAAM;kBAACE,MAAM,EAAC,SAAS;kBAACtC,IAAI,EAAC;gBAAQ;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPf,OAAA,CAAC7C,IAAI;UAAAgD,QAAA,eACHH,OAAA,CAAC5C,WAAW;YAAA+C,QAAA,gBACVH,OAAA,CAAC9C,UAAU;cAACkK,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAlH,QAAA,EAAC;YAEtC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbf,OAAA,CAACN,mBAAmB;cAAC6I,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAArI,QAAA,eAC5CH,OAAA,CAACL,QAAQ;gBAAC8I,IAAI,EAAE9F,eAAgB;gBAAAxC,QAAA,gBAC9BH,OAAA,CAACR,aAAa;kBAACkJ,eAAe,EAAC;gBAAK;kBAAA9H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCf,OAAA,CAACV,KAAK;kBAACqJ,OAAO,EAAC;gBAAM;kBAAA/H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBf,OAAA,CAACT,KAAK;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTf,OAAA,CAACP,OAAO;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXf,OAAA,CAACJ,GAAG;kBAAC+I,OAAO,EAAC,SAAS;kBAACG,OAAO,EAAC,GAAG;kBAACC,IAAI,EAAC,SAAS;kBAACxC,IAAI,EAAC;gBAAS;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnEf,OAAA,CAACJ,GAAG;kBAAC+I,OAAO,EAAC,MAAM;kBAACG,OAAO,EAAC,GAAG;kBAACC,IAAI,EAAC,SAAS;kBAACxC,IAAI,EAAC;gBAAQ;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/Df,OAAA,CAACJ,GAAG;kBAAC+I,OAAO,EAAC,QAAQ;kBAACG,OAAO,EAAC,GAAG;kBAACC,IAAI,EAAC,SAAS;kBAACxC,IAAI,EAAC;gBAAQ;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,eAElCH,OAAA,CAAC3C,KAAK;QAACuK,OAAO,EAAE,CAAE;QAAAzH,QAAA,gBAChBH,OAAA,CAAChD,GAAG;UAACgM,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACnB,UAAU,EAAC,QAAQ;UAAA3H,QAAA,gBACpEH,OAAA,CAAC9C,UAAU;YAACkK,OAAO,EAAC,IAAI;YAAAjH,QAAA,EAAC;UAAwB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9Df,OAAA,CAACzC,MAAM;YACL6J,OAAO,EAAC,WAAW;YACnB8B,SAAS,eAAElJ,OAAA,CAACnB,aAAa;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BoI,OAAO,EAAEA,CAAA,KAAM,CAAC,yCAA0C;YAAAhJ,QAAA,EAC3D;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENf,OAAA,CAAC3C,KAAK;UAACuK,OAAO,EAAE,CAAE;UAAAzH,QAAA,EACfoB,KAAK,CAACmE,GAAG,CAAEgB,IAAI,iBACd1G,OAAA,CAAC7C,IAAI;YAAAgD,QAAA,eACHH,OAAA,CAAC5C,WAAW;cAAA+C,QAAA,eACVH,OAAA,CAAChD,GAAG;gBAACgM,OAAO,EAAC,MAAM;gBAACC,cAAc,EAAC,eAAe;gBAACnB,UAAU,EAAC,QAAQ;gBAAA3H,QAAA,gBACpEH,OAAA,CAAC3C,KAAK;kBAACoK,SAAS,EAAC,KAAK;kBAACG,OAAO,EAAE,CAAE;kBAACE,UAAU,EAAC,QAAQ;kBAAA3H,QAAA,gBACpDH,OAAA,CAAChD,GAAG;oBAAAmD,QAAA,gBACFH,OAAA,CAAC9C,UAAU;sBAACkK,OAAO,EAAC,IAAI;sBAAAjH,QAAA,GACrBuG,IAAI,CAACE,SAAS,EAAC,GAAC,EAACF,IAAI,CAACG,QAAQ;oBAAA;sBAAAjG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACbf,OAAA,CAAC9C,UAAU;sBAAC6K,KAAK,EAAC,gBAAgB;sBAAA5H,QAAA,GAC/BuG,IAAI,CAAC0C,KAAK,EAAC,UAAG,EAAC1C,IAAI,CAAC2C,WAAW;oBAAA;sBAAAzI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNf,OAAA,CAAC1C,IAAI;oBACH+K,KAAK,EAAE3B,IAAI,CAACnG,IAAK;oBACjBwH,KAAK,EAAErB,IAAI,CAACnG,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGmG,IAAI,CAACnG,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,WAAY;oBAC3F+I,IAAI,EAAC;kBAAO;oBAAA1I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eAERf,OAAA,CAACrC,UAAU;kBAACwL,OAAO,EAAGI,CAAC,IAAKzC,mBAAmB,CAACyC,CAAC,EAAE7C,IAAI,CAAE;kBAAAvG,QAAA,eACvDH,OAAA,CAACf,YAAY;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC,GAvBL2F,IAAI,CAACjG,EAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBZ,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAAC9C,UAAU;QAACkK,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAlH,QAAA,EAAC;MAAiB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpEf,OAAA,CAAC3C,KAAK;QAACuK,OAAO,EAAE,CAAE;QAAAzH,QAAA,EACfsB,OAAO,CAACiE,GAAG,CAAEW,MAAM,iBAClBrG,OAAA,CAAC7C,IAAI;UAAAgD,QAAA,eACHH,OAAA,CAAC5C,WAAW;YAAA+C,QAAA,gBACVH,OAAA,CAAC9C,UAAU;cAACkK,OAAO,EAAC,IAAI;cAAAjH,QAAA,EAAEkG,MAAM,CAACE;YAAI;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnDf,OAAA,CAAC9C,UAAU;cAAC6K,KAAK,EAAC,gBAAgB;cAAA5H,QAAA,EAC/BkG,MAAM,CAACmD;YAAW;cAAA5I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACbf,OAAA,CAAC3C,KAAK;cAACoK,SAAS,EAAC,KAAK;cAACG,OAAO,EAAE,CAAE;cAAClH,EAAE,EAAE;gBAAE+I,EAAE,EAAE;cAAE,CAAE;cAAAtJ,QAAA,gBAC/CH,OAAA,CAAC1C,IAAI;gBAAC+K,KAAK,EAAE,GAAGhC,MAAM,CAACqD,OAAO,UAAW;gBAACJ,IAAI,EAAC;cAAO;gBAAA1I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDf,OAAA,CAAC1C,IAAI;gBAAC+K,KAAK,EAAEhC,MAAM,CAACsD,QAAS;gBAACL,IAAI,EAAC;cAAO;gBAAA1I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC,GAVLsF,MAAM,CAAC5F,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWd,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEe,QAAS;MAACd,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAAC9C,UAAU;QAACkK,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAlH,QAAA,EAAC;MAAiC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpFf,OAAA,CAAC7C,IAAI;QAAAgD,QAAA,eACHH,OAAA,CAAC5C,WAAW;UAAA+C,QAAA,eACVH,OAAA,CAAC3C,KAAK;YAACuK,OAAO,EAAE,CAAE;YAAAzH,QAAA,gBAChBH,OAAA,CAAC9C,UAAU;cAAAiD,QAAA,EAAC;YAEZ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbf,OAAA,CAACzC,MAAM;cACL6J,OAAO,EAAC,UAAU;cAClB+B,OAAO,EAAEA,CAAA,KAAM3G,uBAAuB,CAAC,IAAI,CAAE;cAAArC,QAAA,EAC9C;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGXf,OAAA,CAACpC,IAAI;MACHgM,QAAQ,EAAEzH,cAAe;MACzB0H,IAAI,EAAEC,OAAO,CAAC3H,cAAc,CAAE;MAC9BqF,OAAO,EAAEA,CAAA,KAAMpF,iBAAiB,CAAC,IAAI,CAAE;MAAAjC,QAAA,gBAEvCH,OAAA,CAACnC,QAAQ;QAACsL,OAAO,EAAEA,CAAA,KAAM7G,oBAAoB,CAAC,IAAI,CAAE;QAAAnC,QAAA,gBAClDH,OAAA,CAACjB,SAAS;UAAC2B,EAAE,EAAE;YAAEqJ,EAAE,EAAE;UAAE;QAAE;UAAAnJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXf,OAAA,CAACnC,QAAQ;QAACsL,OAAO,EAAEA,CAAA,KAAM,CAAC,yBAA0B;QAAAhJ,QAAA,gBAClDH,OAAA,CAACrB,YAAY;UAAC+B,EAAE,EAAE;YAAEqJ,EAAE,EAAE;UAAE;QAAE;UAAAnJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,YAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPf,OAAA,CAAClC,MAAM;MAAC+L,IAAI,EAAExH,iBAAkB;MAACmF,OAAO,EAAEA,CAAA,KAAMlF,oBAAoB,CAAC,KAAK,CAAE;MAAAnC,QAAA,gBAC1EH,OAAA,CAACjC,WAAW;QAAAoC,QAAA,EAAC;MAAmC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9Df,OAAA,CAAChC,aAAa;QAAAmC,QAAA,EACX8B,YAAY,iBACXjC,OAAA,CAAC9C,UAAU;UAAAiD,QAAA,GAAC,wCAC4B,EAAC,GAAG,eAC1CH,OAAA;YAAAG,QAAA,GAAS8B,YAAY,CAAC2E,SAAS,EAAC,GAAC,EAAC3E,YAAY,CAAC4E,QAAQ;UAAA;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,MACnE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBf,OAAA,CAAC/B,aAAa;QAAAkC,QAAA,gBACZH,OAAA,CAACzC,MAAM;UAAC4L,OAAO,EAAEA,CAAA,KAAM7G,oBAAoB,CAAC,KAAK,CAAE;UAAAnC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpEf,OAAA,CAACzC,MAAM;UACL4L,OAAO,EAAEA,CAAA,KAAMlH,YAAY,IAAIwE,WAAW,CAACxE,YAAY,CAAE;UACzDmF,OAAO,EAAC,WAAW;UAAAjH,QAAA,EACpB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACG,EAAA,CA/YID,cAAwB;AAAA+I,GAAA,GAAxB/I,cAAwB;AAiZ9B,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAgJ,GAAA;AAAAC,YAAA,CAAAjJ,EAAA;AAAAiJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}