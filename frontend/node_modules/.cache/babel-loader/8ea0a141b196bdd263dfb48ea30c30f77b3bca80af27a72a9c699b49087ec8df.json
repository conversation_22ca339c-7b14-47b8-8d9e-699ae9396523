{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { isValidNumber } from '../utils';\nimport { Box } from './Box';\nvar LabeledBox = /** @class */function (_super) {\n  __extends(LabeledBox, _super);\n  function LabeledBox(box, label) {\n    var _this = _super.call(this, box) || this;\n    _this._label = label;\n    return _this;\n  }\n  LabeledBox.assertIsValidLabeledBox = function (box, callee) {\n    Box.assertIsValidBox(box, callee);\n    if (!isValidNumber(box.label)) {\n      throw new Error(callee + \" - expected property label (\" + box.label + \") to be a number\");\n    }\n  };\n  Object.defineProperty(LabeledBox.prototype, \"label\", {\n    get: function () {\n      return this._label;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  return LabeledBox;\n}(Box);\nexport { LabeledBox };", "map": {"version": 3, "names": ["isValidNumber", "Box", "LabeledBox", "_super", "__extends", "box", "label", "_this", "call", "_label", "assertIsValidLabeledBox", "callee", "assertIsValidBox", "Error", "Object", "defineProperty", "prototype", "get"], "sources": ["../../../src/classes/LabeledBox.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,aAAa,QAAQ,UAAU;AAExC,SAASC,GAAG,QAAQ,OAAO;AAG3B,IAAAC,UAAA,0BAAAC,MAAA;EAAgCC,SAAA,CAAAF,UAAA,EAAAC,MAAA;EAY9B,SAAAD,WAAYG,GAA+B,EAAEC,KAAa;IAA1D,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,GAAG,CAAC;IACVE,KAAI,CAACE,MAAM,GAAGH,KAAK;;EACrB;EAbcJ,UAAA,CAAAQ,uBAAuB,GAArC,UAAsCL,GAAQ,EAAEM,MAAc;IAC5DV,GAAG,CAACW,gBAAgB,CAACP,GAAG,EAAEM,MAAM,CAAC;IAEjC,IAAI,CAACX,aAAa,CAACK,GAAG,CAACC,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAIO,KAAK,CAAIF,MAAM,oCAA+BN,GAAG,CAACC,KAAK,qBAAkB,CAAC;;EAExF,CAAC;EASDQ,MAAA,CAAAC,cAAA,CAAWb,UAAA,CAAAc,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACR,MAAM;IAAC,CAAC;;;;EAEnD,OAAAP,UAAC;AAAD,CAAC,CAnB+BD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}