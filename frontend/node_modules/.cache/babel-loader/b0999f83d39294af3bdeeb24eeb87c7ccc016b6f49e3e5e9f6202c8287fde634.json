{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/pages/TeacherDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Card, CardContent, Stack, Box, Alert, Chip, Button, Tabs, Tab, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, LinearProgress } from '@mui/material';\nimport { School as SchoolIcon, People as PeopleIcon, Analytics as AnalyticsIcon, Face as FaceIcon, PlayArrow as PlayIcon, CheckCircle as CheckCircleIcon, Cancel as CancelIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { supabaseService } from '../services/supabaseService';\nimport AutoAttendanceSystem from '../components/Attendance/AutoAttendanceSystem';\nimport { AttendanceStatus } from '../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `teacher-tabpanel-${index}`,\n    \"aria-labelledby\": `teacher-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst TeacherDashboard = () => {\n  _s();\n  const {\n    user,\n    signOut\n  } = useAuth();\n  const [tabValue, setTabValue] = useState(0);\n  const [courses, setCourses] = useState([]);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n  const [attendanceRecords, setAttendanceRecords] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [attendanceSessionOpen, setAttendanceSessionOpen] = useState(false);\n\n  /**\n   * Charge les données du professeur\n   */\n  const loadTeacherData = async () => {\n    if (!user) return;\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les cours du professeur\n      const allCourses = await supabaseService.getCourses();\n      const teacherCourses = allCourses.filter(course => {\n        var _course$teacher;\n        return ((_course$teacher = course.teacher) === null || _course$teacher === void 0 ? void 0 : _course$teacher.id) === user.id;\n      });\n      setCourses(teacherCourses);\n\n      // Charger tous les étudiants\n      const allUsers = await supabaseService.getUsers();\n      const studentUsers = allUsers.filter(u => u.role === 'student');\n      setStudents(studentUsers);\n\n      // Charger les présences pour les cours du professeur\n      if (teacherCourses.length > 0) {\n        const allAttendance = await Promise.all(teacherCourses.map(course => supabaseService.getAttendanceByCourse(course.id)));\n        const flatAttendance = allAttendance.flat();\n        setAttendanceRecords(flatAttendance);\n      }\n    } catch (err) {\n      console.error('Erreur chargement données professeur:', err);\n      setError('Impossible de charger vos données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadTeacherData();\n  }, [user]);\n\n  /**\n   * Calcule les statistiques générales\n   */\n  const getGeneralStats = () => {\n    const totalSessions = attendanceRecords.length;\n    const present = attendanceRecords.filter(r => r.status === AttendanceStatus.PRESENT).length;\n    const late = attendanceRecords.filter(r => r.status === AttendanceStatus.LATE).length;\n    const absent = attendanceRecords.filter(r => r.status === AttendanceStatus.ABSENT).length;\n    const attendanceRate = totalSessions > 0 ? Math.round((present + late) / totalSessions * 100) : 0;\n    return {\n      totalCourses: courses.length,\n      totalStudents: students.length,\n      totalSessions,\n      attendanceRate\n    };\n  };\n\n  /**\n   * Obtient l'icône pour un statut de présence\n   */\n  const getStatusIcon = status => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 16\n        }, this);\n      case AttendanceStatus.LATE:\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 16\n        }, this);\n      case AttendanceStatus.ABSENT:\n        return /*#__PURE__*/_jsxDEV(CancelIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n\n  /**\n   * Obtient la couleur pour un statut de présence\n   */\n  const getStatusColor = status => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return 'success';\n      case AttendanceStatus.LATE:\n        return 'warning';\n      case AttendanceStatus.ABSENT:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const stats = getGeneralStats();\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Utilisateur non connect\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          gutterBottom: true,\n          children: \"Dashboard Professeur\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: [\"Bienvenue, Prof. \", user.firstName, \" \", user.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 24\n          }, this),\n          onClick: () => setAttendanceSessionOpen(true),\n          disabled: courses.length === 0,\n          children: \"Session de Pr\\xE9sence\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"error\",\n          onClick: signOut,\n          children: \"D\\xE9connexion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      direction: {\n        xs: 'column',\n        md: 'row'\n      },\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(SchoolIcon, {\n              color: \"primary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalCourses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Mes Cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(PeopleIcon, {\n              color: \"secondary\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalStudents\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"\\xC9tudiants\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n              color: \"success\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: stats.totalSessions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              color: \"warning\",\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                children: [stats.attendanceRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Taux Pr\\xE9sence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: (_, newValue) => setTabValue(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Mes Cours\",\n          icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Pr\\xE9sences R\\xE9centes\",\n          icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"\\xC9tudiants\",\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 0,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Mes Cours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), courses.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Aucun cours assign\\xE9. Contactez l'administrateur pour vous assigner des cours.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: courses.map(course => /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: course.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: course.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: course.code,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${course.credits} crédits`,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: course.semester,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 34\n                }, this),\n                onClick: () => {\n                  setSelectedCourse(course);\n                  setAttendanceSessionOpen(true);\n                },\n                children: \"D\\xE9marrer Session\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 1,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Pr\\xE9sences R\\xE9centes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), attendanceRecords.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Aucun enregistrement de pr\\xE9sence trouv\\xE9.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"\\xC9tudiant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"M\\xE9thode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: attendanceRecords.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 20) // Afficher les 20 derniers\n            .map(record => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(record.date).toLocaleDateString('fr-FR')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [record.student.firstName, \" \", record.student.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: record.course.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 1,\n                  alignItems: \"center\",\n                  children: [getStatusIcon(record.status), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: record.status,\n                    color: getStatusColor(record.status),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: record.method,\n                  variant: \"outlined\",\n                  size: \"small\",\n                  icon: record.method === 'face_recognition' ? /*#__PURE__*/_jsxDEV(FaceIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 70\n                  }, this) : undefined\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this)]\n            }, record.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Liste des \\xC9tudiants\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: students.map(student => /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [student.firstName, \" \", student.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: [student.email, \" \\u2022 \", student.username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: student.isActive ? 'Actif' : 'Inactif',\n                color: student.isActive ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)\n        }, student.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: attendanceSessionOpen,\n      onClose: () => setAttendanceSessionOpen(false),\n      maxWidth: \"lg\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Session de Pr\\xE9sence Automatique\", selectedCourse && ` - ${selectedCourse.name}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCourse ? /*#__PURE__*/_jsxDEV(AutoAttendanceSystem, {\n          course: selectedCourse,\n          onAttendanceUpdate: records => {\n            // Mettre à jour les enregistrements locaux\n            setAttendanceRecords(prev => [...records, ...prev]);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this) : courses.length > 0 ? /*#__PURE__*/_jsxDEV(AutoAttendanceSystem, {\n          course: courses[0],\n          onAttendanceUpdate: records => {\n            setAttendanceRecords(prev => [...records, ...prev]);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          children: \"Aucun cours disponible pour d\\xE9marrer une session.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setAttendanceSessionOpen(false);\n            setSelectedCourse(null);\n          },\n          children: \"Fermer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(TeacherDashboard, \"Lh1+Ia4vRK47NBXK/rL4FtS4G1E=\", false, function () {\n  return [useAuth];\n});\n_c2 = TeacherDashboard;\nexport default TeacherDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"TeacherDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Box", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "Tabs", "Tab", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "LinearProgress", "School", "SchoolIcon", "People", "PeopleIcon", "Analytics", "AnalyticsIcon", "Face", "FaceIcon", "PlayArrow", "PlayIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "useAuth", "supabaseService", "AutoAttendanceSystem", "AttendanceStatus", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TeacherDashboard", "_s", "user", "signOut", "tabValue", "setTabValue", "courses", "setCourses", "selectedCourse", "setSelectedCourse", "attendanceRecords", "setAttendanceRecords", "students", "setStudents", "loading", "setLoading", "error", "setError", "attendanceSessionOpen", "setAttendanceSessionOpen", "loadTeacherData", "allCourses", "getCourses", "teacherCourses", "filter", "course", "_course$teacher", "teacher", "allUsers", "getUsers", "studentUsers", "u", "length", "allAttendance", "Promise", "all", "map", "getAttendanceByCourse", "flatAttendance", "flat", "err", "console", "getGeneralStats", "totalSessions", "present", "r", "status", "PRESENT", "late", "LATE", "absent", "ABSENT", "attendanceRate", "Math", "round", "totalCourses", "totalStudents", "getStatusIcon", "color", "getStatusColor", "stats", "severity", "max<PERSON><PERSON><PERSON>", "py", "display", "justifyContent", "alignItems", "mb", "variant", "gutterBottom", "firstName", "lastName", "direction", "spacing", "startIcon", "onClick", "disabled", "onClose", "xs", "md", "flex", "fontSize", "borderBottom", "borderColor", "onChange", "_", "newValue", "label", "icon", "name", "description", "code", "size", "credits", "semester", "component", "sort", "a", "b", "Date", "date", "getTime", "slice", "record", "toLocaleDateString", "student", "fontWeight", "method", "undefined", "email", "username", "isActive", "open", "fullWidth", "onAttendanceUpdate", "records", "prev", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/pages/TeacherDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Container,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  <PERSON>ack,\n  Box,\n  Alert,\n  Chip,\n  Button,\n  Tabs,\n  Tab,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  LinearProgress\n} from '@mui/material';\nimport {\n  School as SchoolIcon,\n  People as PeopleIcon,\n  Analytics as AnalyticsIcon,\n  Face as FaceIcon,\n  PlayArrow as PlayIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { supabaseService } from '../services/supabaseService';\nimport AutoAttendanceSystem from '../components/Attendance/AutoAttendanceSystem';\nimport { Course, AttendanceRecord, User, AttendanceStatus } from '../types';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`teacher-tabpanel-${index}`}\n      aria-labelledby={`teacher-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst TeacherDashboard: React.FC = () => {\n  const { user, signOut } = useAuth();\n  const [tabValue, setTabValue] = useState(0);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [students, setStudents] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [attendanceSessionOpen, setAttendanceSessionOpen] = useState(false);\n\n  /**\n   * Charge les données du professeur\n   */\n  const loadTeacherData = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Charger les cours du professeur\n      const allCourses = await supabaseService.getCourses();\n      const teacherCourses = allCourses.filter(course => \n        course.teacher?.id === user.id\n      );\n      setCourses(teacherCourses);\n\n      // Charger tous les étudiants\n      const allUsers = await supabaseService.getUsers();\n      const studentUsers = allUsers.filter(u => u.role === 'student');\n      setStudents(studentUsers);\n\n      // Charger les présences pour les cours du professeur\n      if (teacherCourses.length > 0) {\n        const allAttendance = await Promise.all(\n          teacherCourses.map(course => \n            supabaseService.getAttendanceByCourse(course.id)\n          )\n        );\n        const flatAttendance = allAttendance.flat();\n        setAttendanceRecords(flatAttendance);\n      }\n\n    } catch (err) {\n      console.error('Erreur chargement données professeur:', err);\n      setError('Impossible de charger vos données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les données au montage\n  useEffect(() => {\n    loadTeacherData();\n  }, [user]);\n\n  /**\n   * Calcule les statistiques générales\n   */\n  const getGeneralStats = () => {\n    const totalSessions = attendanceRecords.length;\n    const present = attendanceRecords.filter(r => r.status === AttendanceStatus.PRESENT).length;\n    const late = attendanceRecords.filter(r => r.status === AttendanceStatus.LATE).length;\n    const absent = attendanceRecords.filter(r => r.status === AttendanceStatus.ABSENT).length;\n    const attendanceRate = totalSessions > 0 ? Math.round(((present + late) / totalSessions) * 100) : 0;\n\n    return {\n      totalCourses: courses.length,\n      totalStudents: students.length,\n      totalSessions,\n      attendanceRate\n    };\n  };\n\n  /**\n   * Obtient l'icône pour un statut de présence\n   */\n  const getStatusIcon = (status: AttendanceStatus) => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return <CheckCircleIcon color=\"success\" />;\n      case AttendanceStatus.LATE:\n        return <WarningIcon color=\"warning\" />;\n      case AttendanceStatus.ABSENT:\n        return <CancelIcon color=\"error\" />;\n      default:\n        return null;\n    }\n  };\n\n  /**\n   * Obtient la couleur pour un statut de présence\n   */\n  const getStatusColor = (status: AttendanceStatus) => {\n    switch (status) {\n      case AttendanceStatus.PRESENT:\n        return 'success';\n      case AttendanceStatus.LATE:\n        return 'warning';\n      case AttendanceStatus.ABSENT:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const stats = getGeneralStats();\n\n  if (!user) {\n    return (\n      <Container>\n        <Alert severity=\"error\">\n          Utilisateur non connecté\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 4 }}>\n      {/* En-tête */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={4}>\n        <Box>\n          <Typography variant=\"h3\" gutterBottom>\n            Dashboard Professeur\n          </Typography>\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            Bienvenue, Prof. {user.firstName} {user.lastName}\n          </Typography>\n        </Box>\n        \n        <Stack direction=\"row\" spacing={2}>\n          <Button\n            variant=\"contained\"\n            startIcon={<FaceIcon />}\n            onClick={() => setAttendanceSessionOpen(true)}\n            disabled={courses.length === 0}\n          >\n            Session de Présence\n          </Button>\n          <Button\n            variant=\"outlined\"\n            color=\"error\"\n            onClick={signOut}\n          >\n            Déconnexion\n          </Button>\n        </Stack>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {loading && <LinearProgress sx={{ mb: 3 }} />}\n\n      {/* Statistiques générales */}\n      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <SchoolIcon color=\"primary\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.totalCourses}</Typography>\n                <Typography color=\"text.secondary\">Mes Cours</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <PeopleIcon color=\"secondary\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.totalStudents}</Typography>\n                <Typography color=\"text.secondary\">Étudiants</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <AnalyticsIcon color=\"success\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.totalSessions}</Typography>\n                <Typography color=\"text.secondary\">Sessions</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n\n        <Card sx={{ flex: 1 }}>\n          <CardContent>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"large\" />\n              <Box>\n                <Typography variant=\"h4\">{stats.attendanceRate}%</Typography>\n                <Typography color=\"text.secondary\">Taux Présence</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Stack>\n\n      {/* Onglets */}\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>\n        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>\n          <Tab label=\"Mes Cours\" icon={<SchoolIcon />} />\n          <Tab label=\"Présences Récentes\" icon={<AnalyticsIcon />} />\n          <Tab label=\"Étudiants\" icon={<PeopleIcon />} />\n        </Tabs>\n      </Box>\n\n      {/* Contenu des onglets */}\n      <TabPanel value={tabValue} index={0}>\n        {/* Mes cours */}\n        <Typography variant=\"h6\" gutterBottom>\n          Mes Cours\n        </Typography>\n        \n        {courses.length === 0 ? (\n          <Alert severity=\"info\">\n            Aucun cours assigné. Contactez l'administrateur pour vous assigner des cours.\n          </Alert>\n        ) : (\n          <Stack spacing={2}>\n            {courses.map((course) => (\n              <Card key={course.id} variant=\"outlined\">\n                <CardContent>\n                  <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                    <Box>\n                      <Typography variant=\"h6\">{course.name}</Typography>\n                      <Typography color=\"text.secondary\" gutterBottom>\n                        {course.description}\n                      </Typography>\n                      <Stack direction=\"row\" spacing={1}>\n                        <Chip label={course.code} size=\"small\" />\n                        <Chip label={`${course.credits} crédits`} size=\"small\" />\n                        <Chip label={course.semester} size=\"small\" />\n                      </Stack>\n                    </Box>\n                    \n                    <Button\n                      variant=\"outlined\"\n                      startIcon={<PlayIcon />}\n                      onClick={() => {\n                        setSelectedCourse(course);\n                        setAttendanceSessionOpen(true);\n                      }}\n                    >\n                      Démarrer Session\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            ))}\n          </Stack>\n        )}\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={1}>\n        {/* Présences récentes */}\n        <Typography variant=\"h6\" gutterBottom>\n          Présences Récentes\n        </Typography>\n        \n        {attendanceRecords.length === 0 ? (\n          <Alert severity=\"info\">\n            Aucun enregistrement de présence trouvé.\n          </Alert>\n        ) : (\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Date</TableCell>\n                  <TableCell>Étudiant</TableCell>\n                  <TableCell>Cours</TableCell>\n                  <TableCell>Statut</TableCell>\n                  <TableCell>Méthode</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {attendanceRecords\n                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n                  .slice(0, 20) // Afficher les 20 derniers\n                  .map((record) => (\n                  <TableRow key={record.id}>\n                    <TableCell>\n                      {new Date(record.date).toLocaleDateString('fr-FR')}\n                    </TableCell>\n                    <TableCell>\n                      {record.student.firstName} {record.student.lastName}\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {record.course.name}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>\n                      <Stack direction=\"row\" spacing={1} alignItems=\"center\">\n                        {getStatusIcon(record.status)}\n                        <Chip\n                          label={record.status}\n                          color={getStatusColor(record.status) as any}\n                          size=\"small\"\n                        />\n                      </Stack>\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={record.method}\n                        variant=\"outlined\"\n                        size=\"small\"\n                        icon={record.method === 'face_recognition' ? <FaceIcon /> : undefined}\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n      </TabPanel>\n\n      <TabPanel value={tabValue} index={2}>\n        {/* Liste des étudiants */}\n        <Typography variant=\"h6\" gutterBottom>\n          Liste des Étudiants\n        </Typography>\n        \n        <Stack spacing={2}>\n          {students.map((student) => (\n            <Card key={student.id} variant=\"outlined\">\n              <CardContent>\n                <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                  <Box>\n                    <Typography variant=\"h6\">\n                      {student.firstName} {student.lastName}\n                    </Typography>\n                    <Typography color=\"text.secondary\">\n                      {student.email} • {student.username}\n                    </Typography>\n                  </Box>\n                  \n                  <Chip \n                    label={student.isActive ? 'Actif' : 'Inactif'} \n                    color={student.isActive ? 'success' : 'default'}\n                    size=\"small\"\n                  />\n                </Box>\n              </CardContent>\n            </Card>\n          ))}\n        </Stack>\n      </TabPanel>\n\n      {/* Dialog de session de présence */}\n      <Dialog \n        open={attendanceSessionOpen} \n        onClose={() => setAttendanceSessionOpen(false)}\n        maxWidth=\"lg\"\n        fullWidth\n      >\n        <DialogTitle>\n          Session de Présence Automatique\n          {selectedCourse && ` - ${selectedCourse.name}`}\n        </DialogTitle>\n        <DialogContent>\n          {selectedCourse ? (\n            <AutoAttendanceSystem\n              course={selectedCourse}\n              onAttendanceUpdate={(records) => {\n                // Mettre à jour les enregistrements locaux\n                setAttendanceRecords(prev => [...records, ...prev]);\n              }}\n            />\n          ) : courses.length > 0 ? (\n            <AutoAttendanceSystem\n              course={courses[0]}\n              onAttendanceUpdate={(records) => {\n                setAttendanceRecords(prev => [...records, ...prev]);\n              }}\n            />\n          ) : (\n            <Alert severity=\"warning\">\n              Aucun cours disponible pour démarrer une session.\n            </Alert>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => {\n            setAttendanceSessionOpen(false);\n            setSelectedCourse(null);\n          }}>\n            Fermer\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default TeacherDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,QAAQ,EACrBC,WAAW,IAAIC,eAAe,EAC9BC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,SAAyCC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5E,SAASC,QAAQA,CAACC,KAAoB,EAAE;EACtC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAClD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,oBAAoBJ,KAAK,EAAG;IAChC,mBAAiB,eAAeA,KAAK,EAAG;IAAA,GACpCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAACvC,GAAG;MAACiD,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAbQf,QAAQ;AAejB,MAAMgB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGzB,OAAO,CAAC,CAAC;EACnC,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAqB,EAAE,CAAC;EAClF,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+E,KAAK,EAAEC,QAAQ,CAAC,GAAGhF,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACiF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAEzE;AACF;AACA;EACE,MAAMmF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAClB,IAAI,EAAE;IAEX,IAAI;MACFa,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMI,UAAU,GAAG,MAAM1C,eAAe,CAAC2C,UAAU,CAAC,CAAC;MACrD,MAAMC,cAAc,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM;QAAA,IAAAC,eAAA;QAAA,OAC7C,EAAAA,eAAA,GAAAD,MAAM,CAACE,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBlC,EAAE,MAAKU,IAAI,CAACV,EAAE;MAAA,CAChC,CAAC;MACDe,UAAU,CAACgB,cAAc,CAAC;;MAE1B;MACA,MAAMK,QAAQ,GAAG,MAAMjD,eAAe,CAACkD,QAAQ,CAAC,CAAC;MACjD,MAAMC,YAAY,GAAGF,QAAQ,CAACJ,MAAM,CAACO,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAK,SAAS,CAAC;MAC/DuB,WAAW,CAACiB,YAAY,CAAC;;MAEzB;MACA,IAAIP,cAAc,CAACS,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,aAAa,GAAG,MAAMC,OAAO,CAACC,GAAG,CACrCZ,cAAc,CAACa,GAAG,CAACX,MAAM,IACvB9C,eAAe,CAAC0D,qBAAqB,CAACZ,MAAM,CAACjC,EAAE,CACjD,CACF,CAAC;QACD,MAAM8C,cAAc,GAAGL,aAAa,CAACM,IAAI,CAAC,CAAC;QAC3C5B,oBAAoB,CAAC2B,cAAc,CAAC;MACtC;IAEF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,uCAAuC,EAAEwB,GAAG,CAAC;MAC3DvB,QAAQ,CAAC,mCAAmC,CAAC;IAC/C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA7E,SAAS,CAAC,MAAM;IACdkF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;;EAEV;AACF;AACA;EACE,MAAMwC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,aAAa,GAAGjC,iBAAiB,CAACsB,MAAM;IAC9C,MAAMY,OAAO,GAAGlC,iBAAiB,CAACc,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKjE,gBAAgB,CAACkE,OAAO,CAAC,CAACf,MAAM;IAC3F,MAAMgB,IAAI,GAAGtC,iBAAiB,CAACc,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKjE,gBAAgB,CAACoE,IAAI,CAAC,CAACjB,MAAM;IACrF,MAAMkB,MAAM,GAAGxC,iBAAiB,CAACc,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKjE,gBAAgB,CAACsE,MAAM,CAAC,CAACnB,MAAM;IACzF,MAAMoB,cAAc,GAAGT,aAAa,GAAG,CAAC,GAAGU,IAAI,CAACC,KAAK,CAAE,CAACV,OAAO,GAAGI,IAAI,IAAIL,aAAa,GAAI,GAAG,CAAC,GAAG,CAAC;IAEnG,OAAO;MACLY,YAAY,EAAEjD,OAAO,CAAC0B,MAAM;MAC5BwB,aAAa,EAAE5C,QAAQ,CAACoB,MAAM;MAC9BW,aAAa;MACbS;IACF,CAAC;EACH,CAAC;;EAED;AACF;AACA;EACE,MAAMK,aAAa,GAAIX,MAAwB,IAAK;IAClD,QAAQA,MAAM;MACZ,KAAKjE,gBAAgB,CAACkE,OAAO;QAC3B,oBAAOhE,OAAA,CAACV,eAAe;UAACqF,KAAK,EAAC;QAAS;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5C,KAAKjB,gBAAgB,CAACoE,IAAI;QACxB,oBAAOlE,OAAA,CAACN,WAAW;UAACiF,KAAK,EAAC;QAAS;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAKjB,gBAAgB,CAACsE,MAAM;QAC1B,oBAAOpE,OAAA,CAACR,UAAU;UAACmF,KAAK,EAAC;QAAO;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM6D,cAAc,GAAIb,MAAwB,IAAK;IACnD,QAAQA,MAAM;MACZ,KAAKjE,gBAAgB,CAACkE,OAAO;QAC3B,OAAO,SAAS;MAClB,KAAKlE,gBAAgB,CAACoE,IAAI;QACxB,OAAO,SAAS;MAClB,KAAKpE,gBAAgB,CAACsE,MAAM;QAC1B,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMS,KAAK,GAAGlB,eAAe,CAAC,CAAC;EAE/B,IAAI,CAACxC,IAAI,EAAE;IACT,oBACEnB,OAAA,CAAC5C,SAAS;MAAA+C,QAAA,eACRH,OAAA,CAACtC,KAAK;QAACoH,QAAQ,EAAC,OAAO;QAAA3E,QAAA,EAAC;MAExB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACEf,OAAA,CAAC5C,SAAS;IAAC2H,QAAQ,EAAC,IAAI;IAACrE,EAAE,EAAE;MAAEsE,EAAE,EAAE;IAAE,CAAE;IAAA7E,QAAA,gBAErCH,OAAA,CAACvC,GAAG;MAACwH,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAjF,QAAA,gBAC3EH,OAAA,CAACvC,GAAG;QAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;UAACgI,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAnF,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC3C,UAAU;UAACgI,OAAO,EAAC,IAAI;UAACV,KAAK,EAAC,gBAAgB;UAAAxE,QAAA,GAAC,mBAC7B,EAACgB,IAAI,CAACoE,SAAS,EAAC,GAAC,EAACpE,IAAI,CAACqE,QAAQ;QAAA;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENf,OAAA,CAACxC,KAAK;QAACiI,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAAvF,QAAA,gBAChCH,OAAA,CAACpC,MAAM;UACLyH,OAAO,EAAC,WAAW;UACnBM,SAAS,eAAE3F,OAAA,CAACd,QAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB6E,OAAO,EAAEA,CAAA,KAAMxD,wBAAwB,CAAC,IAAI,CAAE;UAC9CyD,QAAQ,EAAEtE,OAAO,CAAC0B,MAAM,KAAK,CAAE;UAAA9C,QAAA,EAChC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTf,OAAA,CAACpC,MAAM;UACLyH,OAAO,EAAC,UAAU;UAClBV,KAAK,EAAC,OAAO;UACbiB,OAAO,EAAExE,OAAQ;UAAAjB,QAAA,EAClB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELkB,KAAK,iBACJjC,OAAA,CAACtC,KAAK;MAACoH,QAAQ,EAAC,OAAO;MAACpE,EAAE,EAAE;QAAE0E,EAAE,EAAE;MAAE,CAAE;MAACU,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,IAAI,CAAE;MAAA/B,QAAA,EAClE8B;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAgB,OAAO,iBAAI/B,OAAA,CAACtB,cAAc;MAACgC,EAAE,EAAE;QAAE0E,EAAE,EAAE;MAAE;IAAE;MAAAxE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7Cf,OAAA,CAACxC,KAAK;MAACiI,SAAS,EAAE;QAAEM,EAAE,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAM,CAAE;MAACN,OAAO,EAAE,CAAE;MAAChF,EAAE,EAAE;QAAE0E,EAAE,EAAE;MAAE,CAAE;MAAAjF,QAAA,gBACvEH,OAAA,CAAC1C,IAAI;QAACoD,EAAE,EAAE;UAAEuF,IAAI,EAAE;QAAE,CAAE;QAAA9F,QAAA,eACpBH,OAAA,CAACzC,WAAW;UAAA4C,QAAA,eACVH,OAAA,CAACxC,KAAK;YAACiI,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAvF,QAAA,gBACpDH,OAAA,CAACpB,UAAU;cAAC+F,KAAK,EAAC,SAAS;cAACuB,QAAQ,EAAC;YAAO;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/Cf,OAAA,CAACvC,GAAG;cAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAAAlF,QAAA,EAAE0E,KAAK,CAACL;cAAY;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1Df,OAAA,CAAC3C,UAAU;gBAACsH,KAAK,EAAC,gBAAgB;gBAAAxE,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAAC1C,IAAI;QAACoD,EAAE,EAAE;UAAEuF,IAAI,EAAE;QAAE,CAAE;QAAA9F,QAAA,eACpBH,OAAA,CAACzC,WAAW;UAAA4C,QAAA,eACVH,OAAA,CAACxC,KAAK;YAACiI,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAvF,QAAA,gBACpDH,OAAA,CAAClB,UAAU;cAAC6F,KAAK,EAAC,WAAW;cAACuB,QAAQ,EAAC;YAAO;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDf,OAAA,CAACvC,GAAG;cAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAAAlF,QAAA,EAAE0E,KAAK,CAACJ;cAAa;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3Df,OAAA,CAAC3C,UAAU;gBAACsH,KAAK,EAAC,gBAAgB;gBAAAxE,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAAC1C,IAAI;QAACoD,EAAE,EAAE;UAAEuF,IAAI,EAAE;QAAE,CAAE;QAAA9F,QAAA,eACpBH,OAAA,CAACzC,WAAW;UAAA4C,QAAA,eACVH,OAAA,CAACxC,KAAK;YAACiI,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAvF,QAAA,gBACpDH,OAAA,CAAChB,aAAa;cAAC2F,KAAK,EAAC,SAAS;cAACuB,QAAQ,EAAC;YAAO;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDf,OAAA,CAACvC,GAAG;cAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAAAlF,QAAA,EAAE0E,KAAK,CAACjB;cAAa;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3Df,OAAA,CAAC3C,UAAU;gBAACsH,KAAK,EAAC,gBAAgB;gBAAAxE,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPf,OAAA,CAAC1C,IAAI;QAACoD,EAAE,EAAE;UAAEuF,IAAI,EAAE;QAAE,CAAE;QAAA9F,QAAA,eACpBH,OAAA,CAACzC,WAAW;UAAA4C,QAAA,eACVH,OAAA,CAACxC,KAAK;YAACiI,SAAS,EAAC,KAAK;YAACN,UAAU,EAAC,QAAQ;YAACO,OAAO,EAAE,CAAE;YAAAvF,QAAA,gBACpDH,OAAA,CAACV,eAAe;cAACqF,KAAK,EAAC,SAAS;cAACuB,QAAQ,EAAC;YAAO;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDf,OAAA,CAACvC,GAAG;cAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAAAlF,QAAA,GAAE0E,KAAK,CAACR,cAAc,EAAC,GAAC;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7Df,OAAA,CAAC3C,UAAU;gBAACsH,KAAK,EAAC,gBAAgB;gBAAAxE,QAAA,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRf,OAAA,CAACvC,GAAG;MAACiD,EAAE,EAAE;QAAEyF,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEhB,EAAE,EAAE;MAAE,CAAE;MAAAjF,QAAA,eAC1DH,OAAA,CAACnC,IAAI;QAACuC,KAAK,EAAEiB,QAAS;QAACgF,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAKjF,WAAW,CAACiF,QAAQ,CAAE;QAAApG,QAAA,gBACtEH,OAAA,CAAClC,GAAG;UAAC0I,KAAK,EAAC,WAAW;UAACC,IAAI,eAAEzG,OAAA,CAACpB,UAAU;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/Cf,OAAA,CAAClC,GAAG;UAAC0I,KAAK,EAAC,0BAAoB;UAACC,IAAI,eAAEzG,OAAA,CAAChB,aAAa;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3Df,OAAA,CAAClC,GAAG;UAAC0I,KAAK,EAAC,cAAW;UAACC,IAAI,eAAEzG,OAAA,CAAClB,UAAU;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEiB,QAAS;MAAChB,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAAC3C,UAAU;QAACgI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAnF,QAAA,EAAC;MAEtC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZQ,OAAO,CAAC0B,MAAM,KAAK,CAAC,gBACnBjD,OAAA,CAACtC,KAAK;QAACoH,QAAQ,EAAC,MAAM;QAAA3E,QAAA,EAAC;MAEvB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAERf,OAAA,CAACxC,KAAK;QAACkI,OAAO,EAAE,CAAE;QAAAvF,QAAA,EACfoB,OAAO,CAAC8B,GAAG,CAAEX,MAAM,iBAClB1C,OAAA,CAAC1C,IAAI;UAAiB+H,OAAO,EAAC,UAAU;UAAAlF,QAAA,eACtCH,OAAA,CAACzC,WAAW;YAAA4C,QAAA,eACVH,OAAA,CAACvC,GAAG;cAACwH,OAAO,EAAC,MAAM;cAACC,cAAc,EAAC,eAAe;cAACC,UAAU,EAAC,QAAQ;cAAAhF,QAAA,gBACpEH,OAAA,CAACvC,GAAG;gBAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;kBAACgI,OAAO,EAAC,IAAI;kBAAAlF,QAAA,EAAEuC,MAAM,CAACgE;gBAAI;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnDf,OAAA,CAAC3C,UAAU;kBAACsH,KAAK,EAAC,gBAAgB;kBAACW,YAAY;kBAAAnF,QAAA,EAC5CuC,MAAM,CAACiE;gBAAW;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACbf,OAAA,CAACxC,KAAK;kBAACiI,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,CAAE;kBAAAvF,QAAA,gBAChCH,OAAA,CAACrC,IAAI;oBAAC6I,KAAK,EAAE9D,MAAM,CAACkE,IAAK;oBAACC,IAAI,EAAC;kBAAO;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzCf,OAAA,CAACrC,IAAI;oBAAC6I,KAAK,EAAE,GAAG9D,MAAM,CAACoE,OAAO,UAAW;oBAACD,IAAI,EAAC;kBAAO;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzDf,OAAA,CAACrC,IAAI;oBAAC6I,KAAK,EAAE9D,MAAM,CAACqE,QAAS;oBAACF,IAAI,EAAC;kBAAO;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENf,OAAA,CAACpC,MAAM;gBACLyH,OAAO,EAAC,UAAU;gBAClBM,SAAS,eAAE3F,OAAA,CAACZ,QAAQ;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxB6E,OAAO,EAAEA,CAAA,KAAM;kBACblE,iBAAiB,CAACgB,MAAM,CAAC;kBACzBN,wBAAwB,CAAC,IAAI,CAAC;gBAChC,CAAE;gBAAAjC,QAAA,EACH;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GA1BL2B,MAAM,CAACjC,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Bd,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEiB,QAAS;MAAChB,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAAC3C,UAAU;QAACgI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAnF,QAAA,EAAC;MAEtC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZY,iBAAiB,CAACsB,MAAM,KAAK,CAAC,gBAC7BjD,OAAA,CAACtC,KAAK;QAACoH,QAAQ,EAAC,MAAM;QAAA3E,QAAA,EAAC;MAEvB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAERf,OAAA,CAAC9B,cAAc;QAAC8I,SAAS,EAAE3I,KAAM;QAAA8B,QAAA,eAC/BH,OAAA,CAACjC,KAAK;UAAAoC,QAAA,gBACJH,OAAA,CAAC7B,SAAS;YAAAgC,QAAA,eACRH,OAAA,CAAC5B,QAAQ;cAAA+B,QAAA,gBACPH,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,EAAC;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3Bf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/Bf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5Bf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7Bf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZf,OAAA,CAAChC,SAAS;YAAAmC,QAAA,EACPwB,iBAAiB,CACfsF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CACvEC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAAA,CACblE,GAAG,CAAEmE,MAAM,iBACZxH,OAAA,CAAC5B,QAAQ;cAAA+B,QAAA,gBACPH,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,EACP,IAAIiH,IAAI,CAACI,MAAM,CAACH,IAAI,CAAC,CAACI,kBAAkB,CAAC,OAAO;cAAC;gBAAA7G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACZf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,GACPqH,MAAM,CAACE,OAAO,CAACnC,SAAS,EAAC,GAAC,EAACiC,MAAM,CAACE,OAAO,CAAClC,QAAQ;cAAA;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACZf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,eACRH,OAAA,CAAC3C,UAAU;kBAACgI,OAAO,EAAC,OAAO;kBAACsC,UAAU,EAAC,QAAQ;kBAAAxH,QAAA,EAC5CqH,MAAM,CAAC9E,MAAM,CAACgE;gBAAI;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,eACRH,OAAA,CAACxC,KAAK;kBAACiI,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,CAAE;kBAACP,UAAU,EAAC,QAAQ;kBAAAhF,QAAA,GACnDuE,aAAa,CAAC8C,MAAM,CAACzD,MAAM,CAAC,eAC7B/D,OAAA,CAACrC,IAAI;oBACH6I,KAAK,EAAEgB,MAAM,CAACzD,MAAO;oBACrBY,KAAK,EAAEC,cAAc,CAAC4C,MAAM,CAACzD,MAAM,CAAS;oBAC5C8C,IAAI,EAAC;kBAAO;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZf,OAAA,CAAC/B,SAAS;gBAAAkC,QAAA,eACRH,OAAA,CAACrC,IAAI;kBACH6I,KAAK,EAAEgB,MAAM,CAACI,MAAO;kBACrBvC,OAAO,EAAC,UAAU;kBAClBwB,IAAI,EAAC,OAAO;kBACZJ,IAAI,EAAEe,MAAM,CAACI,MAAM,KAAK,kBAAkB,gBAAG5H,OAAA,CAACd,QAAQ;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GAAG8G;gBAAU;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA,GA7BCyG,MAAM,CAAC/G,EAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8Bd,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEXf,OAAA,CAACC,QAAQ;MAACG,KAAK,EAAEiB,QAAS;MAAChB,KAAK,EAAE,CAAE;MAAAF,QAAA,gBAElCH,OAAA,CAAC3C,UAAU;QAACgI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAnF,QAAA,EAAC;MAEtC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbf,OAAA,CAACxC,KAAK;QAACkI,OAAO,EAAE,CAAE;QAAAvF,QAAA,EACf0B,QAAQ,CAACwB,GAAG,CAAEqE,OAAO,iBACpB1H,OAAA,CAAC1C,IAAI;UAAkB+H,OAAO,EAAC,UAAU;UAAAlF,QAAA,eACvCH,OAAA,CAACzC,WAAW;YAAA4C,QAAA,eACVH,OAAA,CAACvC,GAAG;cAACwH,OAAO,EAAC,MAAM;cAACC,cAAc,EAAC,eAAe;cAACC,UAAU,EAAC,QAAQ;cAAAhF,QAAA,gBACpEH,OAAA,CAACvC,GAAG;gBAAA0C,QAAA,gBACFH,OAAA,CAAC3C,UAAU;kBAACgI,OAAO,EAAC,IAAI;kBAAAlF,QAAA,GACrBuH,OAAO,CAACnC,SAAS,EAAC,GAAC,EAACmC,OAAO,CAAClC,QAAQ;gBAAA;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACbf,OAAA,CAAC3C,UAAU;kBAACsH,KAAK,EAAC,gBAAgB;kBAAAxE,QAAA,GAC/BuH,OAAO,CAACI,KAAK,EAAC,UAAG,EAACJ,OAAO,CAACK,QAAQ;gBAAA;kBAAAnH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENf,OAAA,CAACrC,IAAI;gBACH6I,KAAK,EAAEkB,OAAO,CAACM,QAAQ,GAAG,OAAO,GAAG,SAAU;gBAC9CrD,KAAK,EAAE+C,OAAO,CAACM,QAAQ,GAAG,SAAS,GAAG,SAAU;gBAChDnB,IAAI,EAAC;cAAO;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GAlBL2G,OAAO,CAACjH,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBf,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXf,OAAA,CAAC1B,MAAM;MACL2J,IAAI,EAAE9F,qBAAsB;MAC5B2D,OAAO,EAAEA,CAAA,KAAM1D,wBAAwB,CAAC,KAAK,CAAE;MAC/C2C,QAAQ,EAAC,IAAI;MACbmD,SAAS;MAAA/H,QAAA,gBAETH,OAAA,CAACzB,WAAW;QAAA4B,QAAA,GAAC,oCAEX,EAACsB,cAAc,IAAI,MAAMA,cAAc,CAACiF,IAAI,EAAE;MAAA;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACdf,OAAA,CAACxB,aAAa;QAAA2B,QAAA,EACXsB,cAAc,gBACbzB,OAAA,CAACH,oBAAoB;UACnB6C,MAAM,EAAEjB,cAAe;UACvB0G,kBAAkB,EAAGC,OAAO,IAAK;YAC/B;YACAxG,oBAAoB,CAACyG,IAAI,IAAI,CAAC,GAAGD,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;UACrD;QAAE;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACAQ,OAAO,CAAC0B,MAAM,GAAG,CAAC,gBACpBjD,OAAA,CAACH,oBAAoB;UACnB6C,MAAM,EAAEnB,OAAO,CAAC,CAAC,CAAE;UACnB4G,kBAAkB,EAAGC,OAAO,IAAK;YAC/BxG,oBAAoB,CAACyG,IAAI,IAAI,CAAC,GAAGD,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;UACrD;QAAE;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFf,OAAA,CAACtC,KAAK;UAACoH,QAAQ,EAAC,SAAS;UAAA3E,QAAA,EAAC;QAE1B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBf,OAAA,CAACvB,aAAa;QAAA0B,QAAA,eACZH,OAAA,CAACpC,MAAM;UAACgI,OAAO,EAAEA,CAAA,KAAM;YACrBxD,wBAAwB,CAAC,KAAK,CAAC;YAC/BV,iBAAiB,CAAC,IAAI,CAAC;UACzB,CAAE;UAAAvB,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACG,EAAA,CAvZID,gBAA0B;EAAA,QACJtB,OAAO;AAAA;AAAA2I,GAAA,GAD7BrH,gBAA0B;AAyZhC,eAAeA,gBAAgB;AAAC,IAAAD,EAAA,EAAAsH,GAAA;AAAAC,YAAA,CAAAvH,EAAA;AAAAuH,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}