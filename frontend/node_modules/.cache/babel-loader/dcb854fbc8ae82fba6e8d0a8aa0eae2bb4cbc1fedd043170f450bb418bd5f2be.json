{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { extractWeightsFactory } from '../common';\nimport { isFloat } from '../utils';\nfunction extractorsFactory(extractWeights, paramMappings) {\n  function extractFilterValues(numFilterValues, numFilters, filterSize) {\n    var weights = extractWeights(numFilterValues);\n    var depth = weights.length / (numFilters * filterSize * filterSize);\n    if (isFloat(depth)) {\n      throw new Error(\"depth has to be an integer: \" + depth + \", weights.length: \" + weights.length + \", numFilters: \" + numFilters + \", filterSize: \" + filterSize);\n    }\n    return tf.tidy(function () {\n      return tf.transpose(tf.tensor4d(weights, [numFilters, depth, filterSize, filterSize]), [2, 3, 1, 0]);\n    });\n  }\n  function extractConvParams(numFilterValues, numFilters, filterSize, mappedPrefix) {\n    var filters = extractFilterValues(numFilterValues, numFilters, filterSize);\n    var bias = tf.tensor1d(extractWeights(numFilters));\n    paramMappings.push({\n      paramPath: mappedPrefix + \"/filters\"\n    }, {\n      paramPath: mappedPrefix + \"/bias\"\n    });\n    return {\n      filters: filters,\n      bias: bias\n    };\n  }\n  function extractScaleLayerParams(numWeights, mappedPrefix) {\n    var weights = tf.tensor1d(extractWeights(numWeights));\n    var biases = tf.tensor1d(extractWeights(numWeights));\n    paramMappings.push({\n      paramPath: mappedPrefix + \"/weights\"\n    }, {\n      paramPath: mappedPrefix + \"/biases\"\n    });\n    return {\n      weights: weights,\n      biases: biases\n    };\n  }\n  function extractConvLayerParams(numFilterValues, numFilters, filterSize, mappedPrefix) {\n    var conv = extractConvParams(numFilterValues, numFilters, filterSize, mappedPrefix + \"/conv\");\n    var scale = extractScaleLayerParams(numFilters, mappedPrefix + \"/scale\");\n    return {\n      conv: conv,\n      scale: scale\n    };\n  }\n  function extractResidualLayerParams(numFilterValues, numFilters, filterSize, mappedPrefix, isDown) {\n    if (isDown === void 0) {\n      isDown = false;\n    }\n    var conv1 = extractConvLayerParams((isDown ? 0.5 : 1) * numFilterValues, numFilters, filterSize, mappedPrefix + \"/conv1\");\n    var conv2 = extractConvLayerParams(numFilterValues, numFilters, filterSize, mappedPrefix + \"/conv2\");\n    return {\n      conv1: conv1,\n      conv2: conv2\n    };\n  }\n  return {\n    extractConvLayerParams: extractConvLayerParams,\n    extractResidualLayerParams: extractResidualLayerParams\n  };\n}\nexport function extractParams(weights) {\n  var _a = extractWeightsFactory(weights),\n    extractWeights = _a.extractWeights,\n    getRemainingWeights = _a.getRemainingWeights;\n  var paramMappings = [];\n  var _b = extractorsFactory(extractWeights, paramMappings),\n    extractConvLayerParams = _b.extractConvLayerParams,\n    extractResidualLayerParams = _b.extractResidualLayerParams;\n  var conv32_down = extractConvLayerParams(4704, 32, 7, 'conv32_down');\n  var conv32_1 = extractResidualLayerParams(9216, 32, 3, 'conv32_1');\n  var conv32_2 = extractResidualLayerParams(9216, 32, 3, 'conv32_2');\n  var conv32_3 = extractResidualLayerParams(9216, 32, 3, 'conv32_3');\n  var conv64_down = extractResidualLayerParams(36864, 64, 3, 'conv64_down', true);\n  var conv64_1 = extractResidualLayerParams(36864, 64, 3, 'conv64_1');\n  var conv64_2 = extractResidualLayerParams(36864, 64, 3, 'conv64_2');\n  var conv64_3 = extractResidualLayerParams(36864, 64, 3, 'conv64_3');\n  var conv128_down = extractResidualLayerParams(147456, 128, 3, 'conv128_down', true);\n  var conv128_1 = extractResidualLayerParams(147456, 128, 3, 'conv128_1');\n  var conv128_2 = extractResidualLayerParams(147456, 128, 3, 'conv128_2');\n  var conv256_down = extractResidualLayerParams(589824, 256, 3, 'conv256_down', true);\n  var conv256_1 = extractResidualLayerParams(589824, 256, 3, 'conv256_1');\n  var conv256_2 = extractResidualLayerParams(589824, 256, 3, 'conv256_2');\n  var conv256_down_out = extractResidualLayerParams(589824, 256, 3, 'conv256_down_out');\n  var fc = tf.tidy(function () {\n    return tf.transpose(tf.tensor2d(extractWeights(256 * 128), [128, 256]), [1, 0]);\n  });\n  paramMappings.push({\n    paramPath: \"fc\"\n  });\n  if (getRemainingWeights().length !== 0) {\n    throw new Error(\"weights remaing after extract: \" + getRemainingWeights().length);\n  }\n  var params = {\n    conv32_down: conv32_down,\n    conv32_1: conv32_1,\n    conv32_2: conv32_2,\n    conv32_3: conv32_3,\n    conv64_down: conv64_down,\n    conv64_1: conv64_1,\n    conv64_2: conv64_2,\n    conv64_3: conv64_3,\n    conv128_down: conv128_down,\n    conv128_1: conv128_1,\n    conv128_2: conv128_2,\n    conv256_down: conv256_down,\n    conv256_1: conv256_1,\n    conv256_2: conv256_2,\n    conv256_down_out: conv256_down_out,\n    fc: fc\n  };\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["tf", "extractWeightsFactory", "isFloat", "extractorsFactory", "extractWeights", "paramMappings", "extractFilterValues", "numFilter<PERSON><PERSON><PERSON>", "numFilters", "filterSize", "weights", "depth", "length", "Error", "tidy", "transpose", "tensor4d", "extractConvParams", "mappedPrefix", "filters", "bias", "tensor1d", "push", "<PERSON><PERSON><PERSON><PERSON>", "extractScaleLayerParams", "numWeights", "biases", "extractConvLayerParams", "conv", "scale", "extractResidualLayerParams", "isDown", "conv1", "conv2", "extractParams", "_a", "getRemainingWeights", "_b", "conv32_down", "conv32_1", "conv32_2", "conv32_3", "conv64_down", "conv64_1", "conv64_2", "conv64_3", "conv128_down", "conv128_1", "conv128_2", "conv256_down", "conv256_1", "conv256_2", "conv256_down_out", "fc", "tensor2d", "params"], "sources": ["../../../src/faceRecognitionNet/extractParams.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAAqBC,qBAAqB,QAA8C,WAAW;AACnG,SAASC,OAAO,QAAQ,UAAU;AAGlC,SAASC,iBAAiBA,CAACC,cAAsC,EAAEC,aAA6B;EAE9F,SAASC,mBAAmBA,CAACC,eAAuB,EAAEC,UAAkB,EAAEC,UAAkB;IAC1F,IAAMC,OAAO,GAAGN,cAAc,CAACG,eAAe,CAAC;IAC/C,IAAMI,KAAK,GAAGD,OAAO,CAACE,MAAM,IAAIJ,UAAU,GAAGC,UAAU,GAAGA,UAAU,CAAC;IAErE,IAAIP,OAAO,CAACS,KAAK,CAAC,EAAE;MAClB,MAAM,IAAIE,KAAK,CAAC,iCAA+BF,KAAK,0BAAqBD,OAAO,CAACE,MAAM,sBAAiBJ,UAAU,sBAAiBC,UAAY,CAAC;;IAGlJ,OAAOT,EAAE,CAACc,IAAI,CACZ;MAAM,OAAAd,EAAE,CAACe,SAAS,CAChBf,EAAE,CAACgB,QAAQ,CAACN,OAAO,EAAE,CAACF,UAAU,EAAEG,KAAK,EAAEF,UAAU,EAAEA,UAAU,CAAC,CAAC,EACjE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb;IAHK,CAGL,CACF;EACH;EAEA,SAASQ,iBAAiBA,CACxBV,eAAuB,EACvBC,UAAkB,EAClBC,UAAkB,EAClBS,YAAoB;IAGpB,IAAMC,OAAO,GAAGb,mBAAmB,CAACC,eAAe,EAAEC,UAAU,EAAEC,UAAU,CAAC;IAC5E,IAAMW,IAAI,GAAGpB,EAAE,CAACqB,QAAQ,CAACjB,cAAc,CAACI,UAAU,CAAC,CAAC;IAEpDH,aAAa,CAACiB,IAAI,CAChB;MAAEC,SAAS,EAAKL,YAAY;IAAU,CAAE,EACxC;MAAEK,SAAS,EAAKL,YAAY;IAAO,CAAE,CACtC;IAED,OAAO;MAAEC,OAAO,EAAAA,OAAA;MAAEC,IAAI,EAAAA;IAAA,CAAE;EAC1B;EAEA,SAASI,uBAAuBA,CAACC,UAAkB,EAAEP,YAAoB;IAEvE,IAAMR,OAAO,GAAGV,EAAE,CAACqB,QAAQ,CAACjB,cAAc,CAACqB,UAAU,CAAC,CAAC;IACvD,IAAMC,MAAM,GAAG1B,EAAE,CAACqB,QAAQ,CAACjB,cAAc,CAACqB,UAAU,CAAC,CAAC;IAEtDpB,aAAa,CAACiB,IAAI,CAChB;MAAEC,SAAS,EAAKL,YAAY;IAAU,CAAE,EACxC;MAAEK,SAAS,EAAKL,YAAY;IAAS,CAAE,CACxC;IAED,OAAO;MACLR,OAAO,EAAAA,OAAA;MACPgB,MAAM,EAAAA;KACP;EACH;EAEA,SAASC,sBAAsBA,CAC7BpB,eAAuB,EACvBC,UAAkB,EAClBC,UAAkB,EAClBS,YAAoB;IAGpB,IAAMU,IAAI,GAAGX,iBAAiB,CAACV,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAKS,YAAY,UAAO,CAAC;IAC/F,IAAMW,KAAK,GAAGL,uBAAuB,CAAChB,UAAU,EAAKU,YAAY,WAAQ,CAAC;IAE1E,OAAO;MAAEU,IAAI,EAAAA,IAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;EACxB;EAEA,SAASC,0BAA0BA,CACjCvB,eAAuB,EACvBC,UAAkB,EAClBC,UAAkB,EAClBS,YAAoB,EACpBa,MAAuB;IAAvB,IAAAA,MAAA;MAAAA,MAAA,QAAuB;IAAA;IAGvB,IAAMC,KAAK,GAAGL,sBAAsB,CAAC,CAACI,MAAM,GAAG,GAAG,GAAG,CAAC,IAAIxB,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAKS,YAAY,WAAQ,CAAC;IAC3H,IAAMe,KAAK,GAAGN,sBAAsB,CAACpB,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAKS,YAAY,WAAQ,CAAC;IAEtG,OAAO;MAAEc,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;EACzB;EAEA,OAAO;IACLN,sBAAsB,EAAAA,sBAAA;IACtBG,0BAA0B,EAAAA;GAC3B;AAEH;AAEA,OAAM,SAAUI,aAAaA,CAACxB,OAAqB;EAE3C,IAAAyB,EAAA,GAAAlC,qBAAA,CAAAS,OAAA,CAG4B;IAFhCN,cAAA,GAAA+B,EAAA,CAAA/B,cAAc;IACdgC,mBAAA,GAAAD,EAAA,CAAAC,mBACgC;EAElC,IAAM/B,aAAa,GAAmB,EAAE;EAElC,IAAAgC,EAAA,GAAAlC,iBAAA,CAAAC,cAAA,EAAAC,aAAA,CAG8C;IAFlDsB,sBAAA,GAAAU,EAAA,CAAAV,sBAAsB;IACtBG,0BAAA,GAAAO,EAAA,CAAAP,0BACkD;EAEpD,IAAMQ,WAAW,GAAGX,sBAAsB,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,aAAa,CAAC;EACtE,IAAMY,QAAQ,GAAGT,0BAA0B,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;EACpE,IAAMU,QAAQ,GAAGV,0BAA0B,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;EACpE,IAAMW,QAAQ,GAAGX,0BAA0B,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;EAEpE,IAAMY,WAAW,GAAGZ,0BAA0B,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC;EACjF,IAAMa,QAAQ,GAAGb,0BAA0B,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;EACrE,IAAMc,QAAQ,GAAGd,0BAA0B,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;EACrE,IAAMe,QAAQ,GAAGf,0BAA0B,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;EAErE,IAAMgB,YAAY,GAAGhB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC;EACrF,IAAMiB,SAAS,GAAGjB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC;EACzE,IAAMkB,SAAS,GAAGlB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC;EAEzE,IAAMmB,YAAY,GAAGnB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC;EACrF,IAAMoB,SAAS,GAAGpB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC;EACzE,IAAMqB,SAAS,GAAGrB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC;EACzE,IAAMsB,gBAAgB,GAAGtB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC;EAEvF,IAAMuB,EAAE,GAAGrD,EAAE,CAACc,IAAI,CAChB;IAAM,OAAAd,EAAE,CAACe,SAAS,CAACf,EAAE,CAACsD,QAAQ,CAAClD,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAxE,CAAwE,CAC/E;EACDC,aAAa,CAACiB,IAAI,CAAC;IAAEC,SAAS,EAAE;EAAI,CAAE,CAAC;EAEvC,IAAIa,mBAAmB,EAAE,CAACxB,MAAM,KAAK,CAAC,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,oCAAkCuB,mBAAmB,EAAE,CAACxB,MAAQ,CAAC;;EAGnF,IAAM2C,MAAM,GAAG;IACbjB,WAAW,EAAAA,WAAA;IACXC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,WAAW,EAAAA,WAAA;IACXC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,QAAQ,EAAAA,QAAA;IACRC,YAAY,EAAAA,YAAA;IACZC,SAAS,EAAAA,SAAA;IACTC,SAAS,EAAAA,SAAA;IACTC,YAAY,EAAAA,YAAA;IACZC,SAAS,EAAAA,SAAA;IACTC,SAAS,EAAAA,SAAA;IACTC,gBAAgB,EAAAA,gBAAA;IAChBC,EAAE,EAAAA;GACH;EAED,OAAO;IAAEE,MAAM,EAAAA,MAAA;IAAElD,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}