{"ast": null, "code": "var baseRange = require('./_baseRange'),\n  isIterateeCall = require('./_isIterateeCall'),\n  toFinite = require('./toFinite');\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function (start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? start < end ? 1 : -1 : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\nmodule.exports = createRange;", "map": {"version": 3, "names": ["baseRange", "require", "isIterateeCall", "toFinite", "createRange", "fromRight", "start", "end", "step", "undefined", "module", "exports"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/lodash/_createRange.js"], "sourcesContent": ["var baseRange = require('./_baseRange'),\n    isIterateeCall = require('./_isIterateeCall'),\n    toFinite = require('./toFinite');\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function(start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nmodule.exports = createRange;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;EAC7CE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACC,SAAS,EAAE;EAC9B,OAAO,UAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;IAChC,IAAIA,IAAI,IAAI,OAAOA,IAAI,IAAI,QAAQ,IAAIN,cAAc,CAACI,KAAK,EAAEC,GAAG,EAAEC,IAAI,CAAC,EAAE;MACvED,GAAG,GAAGC,IAAI,GAAGC,SAAS;IACxB;IACA;IACAH,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC;IACvB,IAAIC,GAAG,KAAKE,SAAS,EAAE;MACrBF,GAAG,GAAGD,KAAK;MACXA,KAAK,GAAG,CAAC;IACX,CAAC,MAAM;MACLC,GAAG,GAAGJ,QAAQ,CAACI,GAAG,CAAC;IACrB;IACAC,IAAI,GAAGA,IAAI,KAAKC,SAAS,GAAIH,KAAK,GAAGC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAIJ,QAAQ,CAACK,IAAI,CAAC;IACnE,OAAOR,SAAS,CAACM,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEH,SAAS,CAAC;EAC/C,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}