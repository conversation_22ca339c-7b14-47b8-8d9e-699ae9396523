{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Contexte d'authentification pour PresencePro avec Supabase\n */\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../config/supabase';\nimport { UserRole } from '../types';\nimport { supabaseService } from '../services/supabaseService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * Crée un utilisateur temporaire basé sur l'email\n * Utilisé quand l'utilisateur existe dans Supabase Auth mais pas dans notre table\n */\nfunction createTempUserFromEmail(email) {\n  // Déterminer le rôle basé sur l'email\n  let role;\n  let firstName;\n  let lastName;\n  if (email.includes('admin')) {\n    role = UserRole.ADMIN;\n    firstName = 'Admin';\n    lastName = 'PresencePro';\n  } else if (email.includes('martin') || email.includes('teacher')) {\n    role = UserRole.TEACHER;\n    firstName = 'Jean';\n    lastName = 'Martin';\n  } else {\n    role = UserRole.STUDENT;\n    firstName = 'Étudiant';\n    lastName = 'Temporaire';\n  }\n  return {\n    id: email,\n    // Utiliser l'email comme ID temporaire\n    username: email.split('@')[0],\n    email,\n    firstName,\n    lastName,\n    fullName: `${firstName} ${lastName}`,\n    role,\n    roleDisplay: role === UserRole.ADMIN ? 'Administrateur' : role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n    isActive: true,\n    dateJoined: new Date().toISOString()\n  };\n}\n\n// Provider\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [supabaseUser, setSupabaseUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Fonction pour obtenir la route basée sur le rôle\n  const getRoleBasedRoute = () => {\n    if (!user) return '/login';\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return '/admin-dashboard';\n      case UserRole.TEACHER:\n        return '/teacher-dashboard';\n      case UserRole.STUDENT:\n        return '/student-dashboard';\n      default:\n        return '/login';\n    }\n  };\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        // Obtenir la session actuelle\n        const {\n          data: {\n            session\n          }\n        } = await supabase.auth.getSession();\n        if (session !== null && session !== void 0 && session.user) {\n          setSupabaseUser(session.user);\n\n          // Récupérer les données utilisateur depuis notre base\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email);\n            setUser(userData);\n          } catch (error) {\n            console.error('Erreur lors de la récupération des données utilisateur:', error);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur lors de l\\'initialisation de l\\'authentification:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    initializeAuth();\n\n    // Écouter les changements d'authentification\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      if (event === 'SIGNED_IN' && session !== null && session !== void 0 && session.user) {\n        setSupabaseUser(session.user);\n        try {\n          const userData = await supabaseService.getUserByEmail(session.user.email);\n          setUser(userData);\n        } catch (error) {\n          console.error('Erreur lors de la récupération des données utilisateur:', error);\n        }\n      } else if (event === 'SIGNED_OUT') {\n        setSupabaseUser(null);\n        setUser(null);\n      }\n      setLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // Fonction de connexion\n  const signIn = async (email, password) => {\n    try {\n      setLoading(true);\n      console.log('🔐 Tentative de connexion pour:', email);\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) {\n        console.error('❌ Erreur d\\'authentification Supabase:', error);\n        return {\n          user: null,\n          error: error.message\n        };\n      }\n      if (data.user) {\n        console.log('✅ Authentification Supabase réussie pour:', data.user.email);\n        setSupabaseUser(data.user);\n        try {\n          console.log('🔍 Recherche du profil utilisateur dans la base...');\n          const userData = await supabaseService.getUserByEmail(data.user.email);\n          console.log('✅ Profil utilisateur trouvé:', userData);\n          setUser(userData);\n          return {\n            user: userData,\n            error: null\n          };\n        } catch (userError) {\n          console.error('❌ Erreur lors de la récupération du profil utilisateur:', userError);\n\n          // Créer un utilisateur temporaire basé sur l'email pour permettre la connexion\n          console.log('🔧 Création d\\'un profil utilisateur temporaire...');\n          const tempUser = createTempUserFromEmail(data.user.email);\n          setUser(tempUser);\n          return {\n            user: tempUser,\n            error: null\n          };\n        }\n      }\n      return {\n        user: null,\n        error: 'Erreur de connexion'\n      };\n    } catch (error) {\n      console.error('❌ Erreur générale de connexion:', error);\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur inconnue'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction d'inscription\n  const signUp = async (email, password, userData) => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password\n      });\n      if (error) {\n        return {\n          user: null,\n          error: error.message\n        };\n      }\n      if (data.user) {\n        // Créer l'utilisateur dans notre base de données\n        try {\n          const newUser = {\n            username: userData.username || email.split('@')[0],\n            email,\n            firstName: userData.firstName || '',\n            lastName: userData.lastName || '',\n            role: userData.role || UserRole.STUDENT,\n            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' : userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n            isActive: true,\n            dateJoined: new Date().toISOString()\n          };\n          const createdUser = await supabaseService.createUser(newUser);\n          setUser(createdUser);\n          return {\n            user: createdUser,\n            error: null\n          };\n        } catch (userError) {\n          return {\n            user: null,\n            error: 'Erreur lors de la création du profil utilisateur'\n          };\n        }\n      }\n      return {\n        user: null,\n        error: 'Erreur lors de l\\'inscription'\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur inconnue'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction de déconnexion\n  const signOut = async () => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSupabaseUser(null);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  };\n\n  // Fonction de mise à jour du profil\n  const updateProfile = async userData => {\n    if (!user) {\n      return {\n        user: null,\n        error: 'Aucun utilisateur connecté'\n      };\n    }\n    try {\n      const updatedUser = await supabaseService.updateUser(user.id, userData);\n      setUser(updatedUser);\n      return {\n        user: updatedUser,\n        error: null\n      };\n    } catch (error) {\n      return {\n        user: null,\n        error: error instanceof Error ? error.message : 'Erreur de mise à jour'\n      };\n    }\n  };\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n    getRoleBasedRoute\n  };\n\n  // Afficher un loader pendant l'initialisation\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      },\n      children: \"Chargement...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 285,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\n_s(AuthProvider, \"D2vDCBuOQTUoDpzCc/919H0GjjI=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "supabase", "UserRole", "supabaseService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "createTempUserFromEmail", "email", "role", "firstName", "lastName", "includes", "ADMIN", "TEACHER", "STUDENT", "id", "username", "split", "fullName", "roleDisplay", "isActive", "dateJoined", "Date", "toISOString", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "supabaseUser", "setSupabaseUser", "loading", "setLoading", "getRoleBasedRoute", "initializeAuth", "data", "session", "auth", "getSession", "userData", "getUserByEmail", "error", "console", "subscription", "onAuthStateChange", "event", "unsubscribe", "signIn", "password", "log", "signInWithPassword", "message", "userError", "tempUser", "Error", "signUp", "newUser", "trim", "created<PERSON>ser", "createUser", "signOut", "updateProfile", "updatedUser", "updateUser", "value", "isAuthenticated", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Provider", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["/**\n * Contexte d'authentification pour PresencePro avec Supabase\n */\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { supabase } from '../config/supabase';\nimport { User as SupabaseUser } from '@supabase/supabase-js';\nimport { User, UserRole } from '../types';\nimport { supabaseService } from '../services/supabaseService';\n\ninterface AuthContextType {\n  user: User | null;\n  supabaseUser: SupabaseUser | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;\n  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;\n  signOut: () => Promise<void>;\n  updateProfile: (userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;\n  getRoleBasedRoute: () => string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n/**\n * Crée un utilisateur temporaire basé sur l'email\n * Utilisé quand l'utilisateur existe dans Supabase Auth mais pas dans notre table\n */\nfunction createTempUserFromEmail(email: string): User {\n  // Déterminer le rôle basé sur l'email\n  let role: UserRole;\n  let firstName: string;\n  let lastName: string;\n\n  if (email.includes('admin')) {\n    role = UserRole.ADMIN;\n    firstName = 'Admin';\n    lastName = 'PresencePro';\n  } else if (email.includes('martin') || email.includes('teacher')) {\n    role = UserRole.TEACHER;\n    firstName = 'Jean';\n    lastName = 'Martin';\n  } else {\n    role = UserRole.STUDENT;\n    firstName = 'Étudiant';\n    lastName = 'Temporaire';\n  }\n\n  return {\n    id: email, // Utiliser l'email comme ID temporaire\n    username: email.split('@')[0],\n    email,\n    firstName,\n    lastName,\n    fullName: `${firstName} ${lastName}`,\n    role,\n    roleDisplay: role === UserRole.ADMIN ? 'Administrateur' :\n                role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n    isActive: true,\n    dateJoined: new Date().toISOString()\n  };\n}\n\n// Provider\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Fonction pour obtenir la route basée sur le rôle\n  const getRoleBasedRoute = (): string => {\n    if (!user) return '/login';\n\n    switch (user.role) {\n      case UserRole.ADMIN:\n        return '/admin-dashboard';\n      case UserRole.TEACHER:\n        return '/teacher-dashboard';\n      case UserRole.STUDENT:\n        return '/student-dashboard';\n      default:\n        return '/login';\n    }\n  };\n\n  // Initialisation - vérifier si l'utilisateur est déjà connecté\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        // Obtenir la session actuelle\n        const { data: { session } } = await supabase.auth.getSession();\n\n        if (session?.user) {\n          setSupabaseUser(session.user);\n\n          // Récupérer les données utilisateur depuis notre base\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email!);\n            setUser(userData);\n          } catch (error) {\n            console.error('Erreur lors de la récupération des données utilisateur:', error);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur lors de l\\'initialisation de l\\'authentification:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initializeAuth();\n\n    // Écouter les changements d'authentification\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          setSupabaseUser(session.user);\n          try {\n            const userData = await supabaseService.getUserByEmail(session.user.email!);\n            setUser(userData);\n          } catch (error) {\n            console.error('Erreur lors de la récupération des données utilisateur:', error);\n          }\n        } else if (event === 'SIGNED_OUT') {\n          setSupabaseUser(null);\n          setUser(null);\n        }\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // Fonction de connexion\n  const signIn = async (email: string, password: string): Promise<{ user: User | null; error: string | null }> => {\n    try {\n      setLoading(true);\n      console.log('🔐 Tentative de connexion pour:', email);\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        console.error('❌ Erreur d\\'authentification Supabase:', error);\n        return { user: null, error: error.message };\n      }\n\n      if (data.user) {\n        console.log('✅ Authentification Supabase réussie pour:', data.user.email);\n        setSupabaseUser(data.user);\n\n        try {\n          console.log('🔍 Recherche du profil utilisateur dans la base...');\n          const userData = await supabaseService.getUserByEmail(data.user.email!);\n          console.log('✅ Profil utilisateur trouvé:', userData);\n          setUser(userData);\n          return { user: userData, error: null };\n        } catch (userError) {\n          console.error('❌ Erreur lors de la récupération du profil utilisateur:', userError);\n\n          // Créer un utilisateur temporaire basé sur l'email pour permettre la connexion\n          console.log('🔧 Création d\\'un profil utilisateur temporaire...');\n          const tempUser = createTempUserFromEmail(data.user.email!);\n          setUser(tempUser);\n          return { user: tempUser, error: null };\n        }\n      }\n\n      return { user: null, error: 'Erreur de connexion' };\n    } catch (error) {\n      console.error('❌ Erreur générale de connexion:', error);\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction d'inscription\n  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {\n    try {\n      setLoading(true);\n\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { user: null, error: error.message };\n      }\n\n      if (data.user) {\n        // Créer l'utilisateur dans notre base de données\n        try {\n          const newUser: Omit<User, 'id'> = {\n            username: userData.username || email.split('@')[0],\n            email,\n            firstName: userData.firstName || '',\n            lastName: userData.lastName || '',\n            role: userData.role || UserRole.STUDENT,\n            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),\n            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' :\n                        userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',\n            isActive: true,\n            dateJoined: new Date().toISOString(),\n          };\n\n          const createdUser = await supabaseService.createUser(newUser);\n          setUser(createdUser);\n          return { user: createdUser, error: null };\n        } catch (userError) {\n          return { user: null, error: 'Erreur lors de la création du profil utilisateur' };\n        }\n      }\n\n      return { user: null, error: 'Erreur lors de l\\'inscription' };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction de déconnexion\n  const signOut = async (): Promise<void> => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSupabaseUser(null);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  };\n\n  // Fonction de mise à jour du profil\n  const updateProfile = async (userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {\n    if (!user) {\n      return { user: null, error: 'Aucun utilisateur connecté' };\n    }\n\n    try {\n      const updatedUser = await supabaseService.updateUser(user.id, userData);\n      setUser(updatedUser);\n      return { user: updatedUser, error: null };\n    } catch (error) {\n      return { user: null, error: error instanceof Error ? error.message : 'Erreur de mise à jour' };\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    supabaseUser,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n    getRoleBasedRoute,\n  };\n\n  // Afficher un loader pendant l'initialisation\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      }}>\n        Chargement...\n      </div>\n    );\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth doit être utilisé dans un AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAAeC,QAAQ,QAAQ,UAAU;AACzC,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc9D,MAAMC,WAAW,gBAAGT,aAAa,CAA8BU,SAAS,CAAC;;AAEzE;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACC,KAAa,EAAQ;EACpD;EACA,IAAIC,IAAc;EAClB,IAAIC,SAAiB;EACrB,IAAIC,QAAgB;EAEpB,IAAIH,KAAK,CAACI,QAAQ,CAAC,OAAO,CAAC,EAAE;IAC3BH,IAAI,GAAGR,QAAQ,CAACY,KAAK;IACrBH,SAAS,GAAG,OAAO;IACnBC,QAAQ,GAAG,aAAa;EAC1B,CAAC,MAAM,IAAIH,KAAK,CAACI,QAAQ,CAAC,QAAQ,CAAC,IAAIJ,KAAK,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE;IAChEH,IAAI,GAAGR,QAAQ,CAACa,OAAO;IACvBJ,SAAS,GAAG,MAAM;IAClBC,QAAQ,GAAG,QAAQ;EACrB,CAAC,MAAM;IACLF,IAAI,GAAGR,QAAQ,CAACc,OAAO;IACvBL,SAAS,GAAG,UAAU;IACtBC,QAAQ,GAAG,YAAY;EACzB;EAEA,OAAO;IACLK,EAAE,EAAER,KAAK;IAAE;IACXS,QAAQ,EAAET,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7BV,KAAK;IACLE,SAAS;IACTC,QAAQ;IACRQ,QAAQ,EAAE,GAAGT,SAAS,IAAIC,QAAQ,EAAE;IACpCF,IAAI;IACJW,WAAW,EAAEX,IAAI,KAAKR,QAAQ,CAACY,KAAK,GAAG,gBAAgB,GAC3CJ,IAAI,KAAKR,QAAQ,CAACa,OAAO,GAAG,YAAY,GAAG,UAAU;IACjEO,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACrC,CAAC;AACH;;AAEA;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMoC,iBAAiB,GAAGA,CAAA,KAAc;IACtC,IAAI,CAACN,IAAI,EAAE,OAAO,QAAQ;IAE1B,QAAQA,IAAI,CAACnB,IAAI;MACf,KAAKR,QAAQ,CAACY,KAAK;QACjB,OAAO,kBAAkB;MAC3B,KAAKZ,QAAQ,CAACa,OAAO;QACnB,OAAO,oBAAoB;MAC7B,KAAKb,QAAQ,CAACc,OAAO;QACnB,OAAO,oBAAoB;MAC7B;QACE,OAAO,QAAQ;IACnB;EACF,CAAC;;EAED;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMoC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF;QACA,MAAM;UAAEC,IAAI,EAAE;YAAEC;UAAQ;QAAE,CAAC,GAAG,MAAMrC,QAAQ,CAACsC,IAAI,CAACC,UAAU,CAAC,CAAC;QAE9D,IAAIF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAET,IAAI,EAAE;UACjBG,eAAe,CAACM,OAAO,CAACT,IAAI,CAAC;;UAE7B;UACA,IAAI;YACF,MAAMY,QAAQ,GAAG,MAAMtC,eAAe,CAACuC,cAAc,CAACJ,OAAO,CAACT,IAAI,CAACpB,KAAM,CAAC;YAC1EqB,OAAO,CAACW,QAAQ,CAAC;UACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;UACjF;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0DAA0D,EAAEA,KAAK,CAAC;MAClF,CAAC,SAAS;QACRT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAM;MAAEC,IAAI,EAAE;QAAEQ;MAAa;IAAE,CAAC,GAAG5C,QAAQ,CAACsC,IAAI,CAACO,iBAAiB,CAChE,OAAOC,KAAK,EAAET,OAAO,KAAK;MACxB,IAAIS,KAAK,KAAK,WAAW,IAAIT,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAET,IAAI,EAAE;QAC1CG,eAAe,CAACM,OAAO,CAACT,IAAI,CAAC;QAC7B,IAAI;UACF,MAAMY,QAAQ,GAAG,MAAMtC,eAAe,CAACuC,cAAc,CAACJ,OAAO,CAACT,IAAI,CAACpB,KAAM,CAAC;UAC1EqB,OAAO,CAACW,QAAQ,CAAC;QACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;QACjF;MACF,CAAC,MAAM,IAAII,KAAK,KAAK,YAAY,EAAE;QACjCf,eAAe,CAAC,IAAI,CAAC;QACrBF,OAAO,CAAC,IAAI,CAAC;MACf;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMW,YAAY,CAACG,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,MAAM,GAAG,MAAAA,CAAOxC,KAAa,EAAEyC,QAAgB,KAA2D;IAC9G,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBU,OAAO,CAACO,GAAG,CAAC,iCAAiC,EAAE1C,KAAK,CAAC;MAErD,MAAM;QAAE4B,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAM1C,QAAQ,CAACsC,IAAI,CAACa,kBAAkB,CAAC;QAC7D3C,KAAK;QACLyC;MACF,CAAC,CAAC;MAEF,IAAIP,KAAK,EAAE;QACTC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,OAAO;UAAEd,IAAI,EAAE,IAAI;UAAEc,KAAK,EAAEA,KAAK,CAACU;QAAQ,CAAC;MAC7C;MAEA,IAAIhB,IAAI,CAACR,IAAI,EAAE;QACbe,OAAO,CAACO,GAAG,CAAC,2CAA2C,EAAEd,IAAI,CAACR,IAAI,CAACpB,KAAK,CAAC;QACzEuB,eAAe,CAACK,IAAI,CAACR,IAAI,CAAC;QAE1B,IAAI;UACFe,OAAO,CAACO,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAMV,QAAQ,GAAG,MAAMtC,eAAe,CAACuC,cAAc,CAACL,IAAI,CAACR,IAAI,CAACpB,KAAM,CAAC;UACvEmC,OAAO,CAACO,GAAG,CAAC,8BAA8B,EAAEV,QAAQ,CAAC;UACrDX,OAAO,CAACW,QAAQ,CAAC;UACjB,OAAO;YAAEZ,IAAI,EAAEY,QAAQ;YAAEE,KAAK,EAAE;UAAK,CAAC;QACxC,CAAC,CAAC,OAAOW,SAAS,EAAE;UAClBV,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEW,SAAS,CAAC;;UAEnF;UACAV,OAAO,CAACO,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAMI,QAAQ,GAAG/C,uBAAuB,CAAC6B,IAAI,CAACR,IAAI,CAACpB,KAAM,CAAC;UAC1DqB,OAAO,CAACyB,QAAQ,CAAC;UACjB,OAAO;YAAE1B,IAAI,EAAE0B,QAAQ;YAAEZ,KAAK,EAAE;UAAK,CAAC;QACxC;MACF;MAEA,OAAO;QAAEd,IAAI,EAAE,IAAI;QAAEc,KAAK,EAAE;MAAsB,CAAC;IACrD,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO;QAAEd,IAAI,EAAE,IAAI;QAAEc,KAAK,EAAEA,KAAK,YAAYa,KAAK,GAAGb,KAAK,CAACU,OAAO,GAAG;MAAkB,CAAC;IAC1F,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuB,MAAM,GAAG,MAAAA,CAAOhD,KAAa,EAAEyC,QAAgB,EAAET,QAAuB,KAA2D;IACvI,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM;QAAEG,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAM1C,QAAQ,CAACsC,IAAI,CAACkB,MAAM,CAAC;QACjDhD,KAAK;QACLyC;MACF,CAAC,CAAC;MAEF,IAAIP,KAAK,EAAE;QACT,OAAO;UAAEd,IAAI,EAAE,IAAI;UAAEc,KAAK,EAAEA,KAAK,CAACU;QAAQ,CAAC;MAC7C;MAEA,IAAIhB,IAAI,CAACR,IAAI,EAAE;QACb;QACA,IAAI;UACF,MAAM6B,OAAyB,GAAG;YAChCxC,QAAQ,EAAEuB,QAAQ,CAACvB,QAAQ,IAAIT,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClDV,KAAK;YACLE,SAAS,EAAE8B,QAAQ,CAAC9B,SAAS,IAAI,EAAE;YACnCC,QAAQ,EAAE6B,QAAQ,CAAC7B,QAAQ,IAAI,EAAE;YACjCF,IAAI,EAAE+B,QAAQ,CAAC/B,IAAI,IAAIR,QAAQ,CAACc,OAAO;YACvCI,QAAQ,EAAE,GAAGqB,QAAQ,CAAC9B,SAAS,IAAI,EAAE,IAAI8B,QAAQ,CAAC7B,QAAQ,IAAI,EAAE,EAAE,CAAC+C,IAAI,CAAC,CAAC;YACzEtC,WAAW,EAAEoB,QAAQ,CAAC/B,IAAI,KAAKR,QAAQ,CAACY,KAAK,GAAG,gBAAgB,GACpD2B,QAAQ,CAAC/B,IAAI,KAAKR,QAAQ,CAACa,OAAO,GAAG,YAAY,GAAG,UAAU;YAC1EO,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACrC,CAAC;UAED,MAAMmC,WAAW,GAAG,MAAMzD,eAAe,CAAC0D,UAAU,CAACH,OAAO,CAAC;UAC7D5B,OAAO,CAAC8B,WAAW,CAAC;UACpB,OAAO;YAAE/B,IAAI,EAAE+B,WAAW;YAAEjB,KAAK,EAAE;UAAK,CAAC;QAC3C,CAAC,CAAC,OAAOW,SAAS,EAAE;UAClB,OAAO;YAAEzB,IAAI,EAAE,IAAI;YAAEc,KAAK,EAAE;UAAmD,CAAC;QAClF;MACF;MAEA,OAAO;QAAEd,IAAI,EAAE,IAAI;QAAEc,KAAK,EAAE;MAAgC,CAAC;IAC/D,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEd,IAAI,EAAE,IAAI;QAAEc,KAAK,EAAEA,KAAK,YAAYa,KAAK,GAAGb,KAAK,CAACU,OAAO,GAAG;MAAkB,CAAC;IAC1F,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,OAAO,GAAG,MAAAA,CAAA,KAA2B;IACzC,IAAI;MACF,MAAM7D,QAAQ,CAACsC,IAAI,CAACuB,OAAO,CAAC,CAAC;MAC7BhC,OAAO,CAAC,IAAI,CAAC;MACbE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMoB,aAAa,GAAG,MAAOtB,QAAuB,IAA2D;IAC7G,IAAI,CAACZ,IAAI,EAAE;MACT,OAAO;QAAEA,IAAI,EAAE,IAAI;QAAEc,KAAK,EAAE;MAA6B,CAAC;IAC5D;IAEA,IAAI;MACF,MAAMqB,WAAW,GAAG,MAAM7D,eAAe,CAAC8D,UAAU,CAACpC,IAAI,CAACZ,EAAE,EAAEwB,QAAQ,CAAC;MACvEX,OAAO,CAACkC,WAAW,CAAC;MACpB,OAAO;QAAEnC,IAAI,EAAEmC,WAAW;QAAErB,KAAK,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEd,IAAI,EAAE,IAAI;QAAEc,KAAK,EAAEA,KAAK,YAAYa,KAAK,GAAGb,KAAK,CAACU,OAAO,GAAG;MAAwB,CAAC;IAChG;EACF,CAAC;EAED,MAAMa,KAAsB,GAAG;IAC7BrC,IAAI;IACJE,YAAY;IACZE,OAAO;IACPkC,eAAe,EAAE,CAAC,CAACtC,IAAI;IACvBoB,MAAM;IACNQ,MAAM;IACNK,OAAO;IACPC,aAAa;IACb5B;EACF,CAAC;;EAED;EACA,IAAIF,OAAO,EAAE;IACX,oBACE5B,OAAA;MAAK+D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAA9C,QAAA,EAAC;IAEH;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACExE,OAAA,CAACC,WAAW,CAACwE,QAAQ;IAACZ,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EAChCA;EAAQ;IAAA+C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAjD,EAAA,CA9NaF,YAAyC;AAAAqD,EAAA,GAAzCrD,YAAyC;AA+NtD,OAAO,MAAMsD,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGpF,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI4E,OAAO,KAAK3E,SAAS,EAAE;IACzB,MAAM,IAAIiD,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAO0B,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe1E,WAAW;AAAC,IAAAyE,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}