{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { scale } from './scaleLayer';\nfunction convLayer(x, params, strides, withRelu, padding) {\n  if (padding === void 0) {\n    padding = 'same';\n  }\n  var _a = params.conv,\n    filters = _a.filters,\n    bias = _a.bias;\n  var out = tf.conv2d(x, filters, strides, padding);\n  out = tf.add(out, bias);\n  out = scale(out, params.scale);\n  return withRelu ? tf.relu(out) : out;\n}\nexport function conv(x, params) {\n  return convLayer(x, params, [1, 1], true);\n}\nexport function convNoRelu(x, params) {\n  return convLayer(x, params, [1, 1], false);\n}\nexport function convDown(x, params) {\n  return convLayer(x, params, [2, 2], true, 'valid');\n}", "map": {"version": 3, "names": ["tf", "scale", "convLayer", "x", "params", "strides", "withRelu", "padding", "_a", "conv", "filters", "bias", "out", "conv2d", "add", "relu", "convNoRelu", "convDown"], "sources": ["../../../src/faceRecognitionNet/convLayer.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,KAAK,QAAQ,cAAc;AAIpC,SAASC,SAASA,CAChBC,CAAc,EACdC,MAAuB,EACvBC,OAAyB,EACzBC,QAAiB,EACjBC,OAAkC;EAAlC,IAAAA,OAAA;IAAAA,OAAA,SAAkC;EAAA;EAE5B,IAAAC,EAAA,GAAAJ,MAAA,CAAAK,IAA+B;IAA7BC,OAAA,GAAAF,EAAA,CAAAE,OAAO;IAAEC,IAAA,GAAAH,EAAA,CAAAG,IAAoB;EAErC,IAAIC,GAAG,GAAGZ,EAAE,CAACa,MAAM,CAACV,CAAC,EAAEO,OAAO,EAAEL,OAAO,EAAEE,OAAO,CAAC;EACjDK,GAAG,GAAGZ,EAAE,CAACc,GAAG,CAACF,GAAG,EAAED,IAAI,CAAC;EACvBC,GAAG,GAAGX,KAAK,CAACW,GAAG,EAAER,MAAM,CAACH,KAAK,CAAC;EAC9B,OAAOK,QAAQ,GAAGN,EAAE,CAACe,IAAI,CAACH,GAAG,CAAC,GAAGA,GAAG;AACtC;AAEA,OAAM,SAAUH,IAAIA,CAACN,CAAc,EAAEC,MAAuB;EAC1D,OAAOF,SAAS,CAACC,CAAC,EAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;AAC3C;AAEA,OAAM,SAAUY,UAAUA,CAACb,CAAc,EAAEC,MAAuB;EAChE,OAAOF,SAAS,CAACC,CAAC,EAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AAC5C;AAEA,OAAM,SAAUa,QAAQA,CAACd,CAAc,EAAEC,MAAuB;EAC9D,OAAOF,SAAS,CAACC,CAAC,EAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}