{"ast": null, "code": "import { __spreadArrays } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nexport function normalize(x, meanRgb) {\n  return tf.tidy(function () {\n    var r = meanRgb[0],\n      g = meanRgb[1],\n      b = meanRgb[2];\n    var avg_r = tf.fill(__spreadArrays(x.shape.slice(0, 3), [1]), r);\n    var avg_g = tf.fill(__spreadArrays(x.shape.slice(0, 3), [1]), g);\n    var avg_b = tf.fill(__spreadArrays(x.shape.slice(0, 3), [1]), b);\n    var avg_rgb = tf.concat([avg_r, avg_g, avg_b], 3);\n    return tf.sub(x, avg_rgb);\n  });\n}", "map": {"version": 3, "names": ["tf", "normalize", "x", "meanRgb", "tidy", "r", "g", "b", "avg_r", "fill", "__spreadA<PERSON>ys", "shape", "slice", "avg_g", "avg_b", "avg_rgb", "concat", "sub"], "sources": ["../../../src/ops/normalize.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,OAAM,SAAUC,SAASA,CAACC,CAAc,EAAEC,OAAiB;EACzD,OAAOH,EAAE,CAACI,IAAI,CAAC;IACN,IAAAC,CAAA,GAAAF,OAAA,GAAC;MAAEG,CAAA,GAAAH,OAAA,GAAC;MAAEI,CAAA,GAAAJ,OAAA,GAAC;IACd,IAAMK,KAAK,GAAGR,EAAE,CAACS,IAAI,CAAAC,cAAA,CAAKR,CAAC,CAACS,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAE,CAAC,IAAGP,CAAC,CAAC;IACrD,IAAMQ,KAAK,GAAGb,EAAE,CAACS,IAAI,CAAAC,cAAA,CAAKR,CAAC,CAACS,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAE,CAAC,IAAGN,CAAC,CAAC;IACrD,IAAMQ,KAAK,GAAGd,EAAE,CAACS,IAAI,CAAAC,cAAA,CAAKR,CAAC,CAACS,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAE,CAAC,IAAGL,CAAC,CAAC;IACrD,IAAMQ,OAAO,GAAGf,EAAE,CAACgB,MAAM,CAAC,CAACR,KAAK,EAAEK,KAAK,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC;IAEnD,OAAOd,EAAE,CAACiB,GAAG,CAACf,CAAC,EAAEa,OAAO,CAAC;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}