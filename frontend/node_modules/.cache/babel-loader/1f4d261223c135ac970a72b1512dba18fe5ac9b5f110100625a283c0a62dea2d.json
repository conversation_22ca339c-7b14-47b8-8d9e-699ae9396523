{"ast": null, "code": "import { FaceRecognitionNet } from './FaceRecognitionNet';\nexport * from './FaceRecognitionNet';\nexport function createFaceRecognitionNet(weights) {\n  var net = new FaceRecognitionNet();\n  net.extractWeights(weights);\n  return net;\n}", "map": {"version": 3, "names": ["FaceRecognitionNet", "createFaceRecognitionNet", "weights", "net", "extractWeights"], "sources": ["../../../src/faceRecognitionNet/index.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AAEzD,cAAc,sBAAsB;AAEpC,OAAM,SAAUC,wBAAwBA,CAACC,OAAqB;EAC5D,IAAMC,GAAG,GAAG,IAAIH,kBAAkB,EAAE;EACpCG,GAAG,CAACC,cAAc,CAACF,OAAO,CAAC;EAC3B,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}