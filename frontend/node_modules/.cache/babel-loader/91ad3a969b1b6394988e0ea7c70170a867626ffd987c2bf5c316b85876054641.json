{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx\",\n  _s = $RefreshSig$();\n/**\n * Menu latéral pour PresencePro\n */\n\nimport React from 'react';\nimport { Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Toolbar, Box, Collapse } from '@mui/material';\nimport { Dashboard, People, School, EventNote, Assessment, Settings, Face, PersonAdd, GroupAdd, BookOnline, CalendarToday, BarChart, PieChart, ExpandLess, ExpandMore } from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { UserRole } from '../../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst menuItems = [{\n  id: 'dashboard',\n  label: 'Tableau de bord',\n  icon: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 11\n  }, this),\n  path: '/dashboard',\n  roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT]\n}, {\n  id: 'users',\n  label: 'Gestion des utilisateurs',\n  icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 11\n  }, this),\n  path: '/users',\n  roles: [UserRole.ADMIN],\n  children: [{\n    id: 'users-list',\n    label: 'Liste des utilisateurs',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 15\n    }, this),\n    path: '/users',\n    roles: [UserRole.ADMIN]\n  }, {\n    id: 'users-create',\n    label: 'Créer un utilisateur',\n    icon: /*#__PURE__*/_jsxDEV(PersonAdd, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 15\n    }, this),\n    path: '/users/create',\n    roles: [UserRole.ADMIN]\n  }, {\n    id: 'groups',\n    label: 'Groupes d\\'étudiants',\n    icon: /*#__PURE__*/_jsxDEV(GroupAdd, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 15\n    }, this),\n    path: '/groups',\n    roles: [UserRole.ADMIN]\n  }]\n}, {\n  id: 'courses',\n  label: 'Gestion des cours',\n  icon: /*#__PURE__*/_jsxDEV(School, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 11\n  }, this),\n  path: '/courses',\n  roles: [UserRole.ADMIN, UserRole.TEACHER],\n  children: [{\n    id: 'courses-list',\n    label: 'Liste des cours',\n    icon: /*#__PURE__*/_jsxDEV(BookOnline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 15\n    }, this),\n    path: '/courses',\n    roles: [UserRole.ADMIN, UserRole.TEACHER]\n  }, {\n    id: 'courses-create',\n    label: 'Créer un cours',\n    icon: /*#__PURE__*/_jsxDEV(School, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 15\n    }, this),\n    path: '/courses/create',\n    roles: [UserRole.ADMIN]\n  }, {\n    id: 'schedule',\n    label: 'Planning',\n    icon: /*#__PURE__*/_jsxDEV(CalendarToday, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 15\n    }, this),\n    path: '/schedule',\n    roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT]\n  }]\n}, {\n  id: 'attendance',\n  label: 'Gestion des présences',\n  icon: /*#__PURE__*/_jsxDEV(EventNote, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 11\n  }, this),\n  path: '/attendance',\n  roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],\n  children: [{\n    id: 'attendance-take',\n    label: 'Prendre les présences',\n    icon: /*#__PURE__*/_jsxDEV(Face, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 15\n    }, this),\n    path: '/attendance/take',\n    roles: [UserRole.TEACHER]\n  }, {\n    id: 'attendance-view',\n    label: 'Consulter les présences',\n    icon: /*#__PURE__*/_jsxDEV(EventNote, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 15\n    }, this),\n    path: '/attendance',\n    roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT]\n  }]\n}, {\n  id: 'reports',\n  label: 'Rapports et statistiques',\n  icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 11\n  }, this),\n  path: '/reports',\n  roles: [UserRole.ADMIN, UserRole.TEACHER],\n  children: [{\n    id: 'reports-attendance',\n    label: 'Rapports de présence',\n    icon: /*#__PURE__*/_jsxDEV(BarChart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 15\n    }, this),\n    path: '/reports/attendance',\n    roles: [UserRole.ADMIN, UserRole.TEACHER]\n  }, {\n    id: 'reports-stats',\n    label: 'Statistiques globales',\n    icon: /*#__PURE__*/_jsxDEV(PieChart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 15\n    }, this),\n    path: '/reports/statistics',\n    roles: [UserRole.ADMIN]\n  }]\n}, {\n  id: 'settings',\n  label: 'Paramètres',\n  icon: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 11\n  }, this),\n  path: '/settings',\n  roles: [UserRole.ADMIN]\n}, {\n  id: 'supabase-test',\n  label: 'Test Supabase',\n  icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 11\n  }, this),\n  path: '/supabase-test',\n  roles: [UserRole.ADMIN]\n}];\nconst Sidebar = ({\n  open,\n  onClose,\n  width\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [expandedItems, setExpandedItems] = React.useState([]);\n  const handleExpandClick = itemId => {\n    setExpandedItems(prev => prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]);\n  };\n  const hasAccess = item => {\n    if (!user) return false;\n    return item.roles.includes(user.role);\n  };\n  const renderMenuItem = (item, level = 0) => {\n    var _item$children;\n    if (!hasAccess(item)) return null;\n    const hasChildren = item.children && item.children.length > 0;\n    const isExpanded = expandedItems.includes(item.id);\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(ListItem, {\n        disablePadding: true,\n        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n          onClick: () => {\n            if (hasChildren) {\n              handleExpandClick(item.id);\n            } else {\n              // Navigation vers la page\n              console.log(`Navigation vers: ${item.path}`);\n              onClose();\n            }\n          },\n          sx: {\n            pl: 2 + level * 2,\n            minHeight: 48\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            sx: {\n              minWidth: 40,\n              color: 'inherit'\n            },\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.label,\n            primaryTypographyProps: {\n              fontSize: level > 0 ? '0.875rem' : '1rem',\n              fontWeight: level === 0 ? 'medium' : 'normal'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), hasChildren && (isExpanded ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 28\n          }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 45\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), hasChildren && /*#__PURE__*/_jsxDEV(Collapse, {\n        in: isExpanded,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: (_item$children = item.children) === null || _item$children === void 0 ? void 0 : _item$children.map(child => renderMenuItem(child, level + 1))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, item.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this);\n  };\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      overflow: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      children: menuItems.map(item => renderMenuItem(item))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"nav\",\n    sx: {\n      width: {\n        sm: width\n      },\n      flexShrink: {\n        sm: 0\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"temporary\",\n      open: open,\n      onClose: onClose,\n      ModalProps: {\n        keepMounted: true // Meilleure performance sur mobile\n      },\n      sx: {\n        display: {\n          xs: 'block',\n          sm: 'none'\n        },\n        '& .MuiDrawer-paper': {\n          boxSizing: 'border-box',\n          width: width\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"permanent\",\n      sx: {\n        display: {\n          xs: 'none',\n          sm: 'block'\n        },\n        '& .MuiDrawer-paper': {\n          boxSizing: 'border-box',\n          width: width\n        }\n      },\n      open: true,\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"RfiZTL3TZH31CS0VlZHvECRHrw4=\", false, function () {\n  return [useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON><PERSON>", "Box", "Collapse", "Dashboard", "People", "School", "EventNote", "Assessment", "Settings", "Face", "PersonAdd", "GroupAdd", "BookOnline", "CalendarToday", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ExpandLess", "ExpandMore", "useAuth", "UserRole", "jsxDEV", "_jsxDEV", "menuItems", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "roles", "ADMIN", "TEACHER", "STUDENT", "children", "Sidebar", "open", "onClose", "width", "_s", "user", "expandedItems", "setExpandedItems", "useState", "handleExpandClick", "itemId", "prev", "includes", "filter", "hasAccess", "item", "role", "renderMenuItem", "level", "_item$children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "isExpanded", "Fragment", "disablePadding", "onClick", "console", "log", "sx", "pl", "minHeight", "min<PERSON><PERSON><PERSON>", "color", "primary", "primaryTypographyProps", "fontSize", "fontWeight", "in", "timeout", "unmountOnExit", "component", "map", "child", "drawerContent", "overflow", "sm", "flexShrink", "variant", "ModalProps", "keepMounted", "display", "xs", "boxSizing", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["/**\n * <PERSON>u latéral pour PresencePro\n */\n\nimport React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Toolbar,\n  Box,\n  Collapse,\n} from '@mui/material';\nimport {\n  Dashboard,\n  People,\n  School,\n  EventNote,\n  Assessment,\n  Settings,\n  Face,\n  PersonAdd,\n  GroupAdd,\n  BookOnline,\n  CalendarToday,\n  BarChart,\n  PieChart,\n  ExpandLess,\n  ExpandMore,\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { UserRole } from '../../types';\n\ninterface SidebarProps {\n  open: boolean;\n  onClose: () => void;\n  width: number;\n}\n\ninterface MenuItem {\n  id: string;\n  label: string;\n  icon: React.ReactElement;\n  path: string;\n  roles: UserRole[];\n  children?: MenuItem[];\n}\n\nconst menuItems: MenuItem[] = [\n  {\n    id: 'dashboard',\n    label: 'Tableau de bord',\n    icon: <Dashboard />,\n    path: '/dashboard',\n    roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],\n  },\n  {\n    id: 'users',\n    label: 'Gestion des utilisateurs',\n    icon: <People />,\n    path: '/users',\n    roles: [UserRole.ADMIN],\n    children: [\n      {\n        id: 'users-list',\n        label: 'Liste des utilisateurs',\n        icon: <People />,\n        path: '/users',\n        roles: [UserRole.ADMIN],\n      },\n      {\n        id: 'users-create',\n        label: 'Créer un utilisateur',\n        icon: <PersonAdd />,\n        path: '/users/create',\n        roles: [UserRole.ADMIN],\n      },\n      {\n        id: 'groups',\n        label: 'Groupes d\\'étudiants',\n        icon: <GroupAdd />,\n        path: '/groups',\n        roles: [UserRole.ADMIN],\n      },\n    ],\n  },\n  {\n    id: 'courses',\n    label: 'Gestion des cours',\n    icon: <School />,\n    path: '/courses',\n    roles: [UserRole.ADMIN, UserRole.TEACHER],\n    children: [\n      {\n        id: 'courses-list',\n        label: 'Liste des cours',\n        icon: <BookOnline />,\n        path: '/courses',\n        roles: [UserRole.ADMIN, UserRole.TEACHER],\n      },\n      {\n        id: 'courses-create',\n        label: 'Créer un cours',\n        icon: <School />,\n        path: '/courses/create',\n        roles: [UserRole.ADMIN],\n      },\n      {\n        id: 'schedule',\n        label: 'Planning',\n        icon: <CalendarToday />,\n        path: '/schedule',\n        roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],\n      },\n    ],\n  },\n  {\n    id: 'attendance',\n    label: 'Gestion des présences',\n    icon: <EventNote />,\n    path: '/attendance',\n    roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],\n    children: [\n      {\n        id: 'attendance-take',\n        label: 'Prendre les présences',\n        icon: <Face />,\n        path: '/attendance/take',\n        roles: [UserRole.TEACHER],\n      },\n      {\n        id: 'attendance-view',\n        label: 'Consulter les présences',\n        icon: <EventNote />,\n        path: '/attendance',\n        roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],\n      },\n    ],\n  },\n  {\n    id: 'reports',\n    label: 'Rapports et statistiques',\n    icon: <Assessment />,\n    path: '/reports',\n    roles: [UserRole.ADMIN, UserRole.TEACHER],\n    children: [\n      {\n        id: 'reports-attendance',\n        label: 'Rapports de présence',\n        icon: <BarChart />,\n        path: '/reports/attendance',\n        roles: [UserRole.ADMIN, UserRole.TEACHER],\n      },\n      {\n        id: 'reports-stats',\n        label: 'Statistiques globales',\n        icon: <PieChart />,\n        path: '/reports/statistics',\n        roles: [UserRole.ADMIN],\n      },\n    ],\n  },\n  {\n    id: 'settings',\n    label: 'Paramètres',\n    icon: <Settings />,\n    path: '/settings',\n    roles: [UserRole.ADMIN],\n  },\n  {\n    id: 'supabase-test',\n    label: 'Test Supabase',\n    icon: <Assessment />,\n    path: '/supabase-test',\n    roles: [UserRole.ADMIN],\n  },\n];\n\nconst Sidebar: React.FC<SidebarProps> = ({ open, onClose, width }) => {\n  const { user } = useAuth();\n  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);\n\n  const handleExpandClick = (itemId: string) => {\n    setExpandedItems(prev =>\n      prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId]\n    );\n  };\n\n  const hasAccess = (item: MenuItem): boolean => {\n    if (!user) return false;\n    return item.roles.includes(user.role);\n  };\n\n  const renderMenuItem = (item: MenuItem, level: number = 0) => {\n    if (!hasAccess(item)) return null;\n\n    const hasChildren = item.children && item.children.length > 0;\n    const isExpanded = expandedItems.includes(item.id);\n\n    return (\n      <React.Fragment key={item.id}>\n        <ListItem disablePadding>\n          <ListItemButton\n            onClick={() => {\n              if (hasChildren) {\n                handleExpandClick(item.id);\n              } else {\n                // Navigation vers la page\n                console.log(`Navigation vers: ${item.path}`);\n                onClose();\n              }\n            }}\n            sx={{\n              pl: 2 + level * 2,\n              minHeight: 48,\n            }}\n          >\n            <ListItemIcon\n              sx={{\n                minWidth: 40,\n                color: 'inherit',\n              }}\n            >\n              {item.icon}\n            </ListItemIcon>\n            <ListItemText\n              primary={item.label}\n              primaryTypographyProps={{\n                fontSize: level > 0 ? '0.875rem' : '1rem',\n                fontWeight: level === 0 ? 'medium' : 'normal',\n              }}\n            />\n            {hasChildren && (\n              isExpanded ? <ExpandLess /> : <ExpandMore />\n            )}\n          </ListItemButton>\n        </ListItem>\n\n        {hasChildren && (\n          <Collapse in={isExpanded} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              {item.children?.map(child => renderMenuItem(child, level + 1))}\n            </List>\n          </Collapse>\n        )}\n      </React.Fragment>\n    );\n  };\n\n  const drawerContent = (\n    <Box sx={{ overflow: 'auto' }}>\n      <Toolbar />\n      <List>\n        {menuItems.map(item => renderMenuItem(item))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <Box\n      component=\"nav\"\n      sx={{ width: { sm: width }, flexShrink: { sm: 0 } }}\n    >\n      {/* Drawer temporaire pour mobile */}\n      <Drawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Meilleure performance sur mobile\n        }}\n        sx={{\n          display: { xs: 'block', sm: 'none' },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: width,\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n\n      {/* Drawer permanent pour desktop */}\n      <Drawer\n        variant=\"permanent\"\n        sx={{\n          display: { xs: 'none', sm: 'block' },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: width,\n          },\n        }}\n        open\n      >\n        {drawerContent}\n      </Drawer>\n    </Box>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,EACTC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBvC,MAAMC,SAAqB,GAAG,CAC5B;EACEC,EAAE,EAAE,WAAW;EACfC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,eAAEJ,OAAA,CAAClB,SAAS;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnBC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO,EAAEd,QAAQ,CAACe,OAAO;AAC5D,CAAC,EACD;EACEX,EAAE,EAAE,OAAO;EACXC,KAAK,EAAE,0BAA0B;EACjCC,IAAI,eAAEJ,OAAA,CAACjB,MAAM;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAChBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,CAAC;EACvBG,QAAQ,EAAE,CACR;IACEZ,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,eAAEJ,OAAA,CAACjB,MAAM;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;EACxB,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,eAAEJ,OAAA,CAACX,SAAS;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;EACxB,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,eAAEJ,OAAA,CAACV,QAAQ;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;EACxB,CAAC;AAEL,CAAC,EACD;EACET,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,mBAAmB;EAC1BC,IAAI,eAAEJ,OAAA,CAAChB,MAAM;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAChBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO,CAAC;EACzCE,QAAQ,EAAE,CACR;IACEZ,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eAAEJ,OAAA,CAACT,UAAU;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO;EAC1C,CAAC,EACD;IACEV,EAAE,EAAE,gBAAgB;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,eAAEJ,OAAA,CAAChB,MAAM;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;EACxB,CAAC,EACD;IACET,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,UAAU;IACjBC,IAAI,eAAEJ,OAAA,CAACR,aAAa;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO,EAAEd,QAAQ,CAACe,OAAO;EAC5D,CAAC;AAEL,CAAC,EACD;EACEX,EAAE,EAAE,YAAY;EAChBC,KAAK,EAAE,uBAAuB;EAC9BC,IAAI,eAAEJ,OAAA,CAACf,SAAS;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnBC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO,EAAEd,QAAQ,CAACe,OAAO,CAAC;EAC3DC,QAAQ,EAAE,CACR;IACEZ,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,eAAEJ,OAAA,CAACZ,IAAI;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,CAACZ,QAAQ,CAACc,OAAO;EAC1B,CAAC,EACD;IACEV,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,yBAAyB;IAChCC,IAAI,eAAEJ,OAAA,CAACf,SAAS;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO,EAAEd,QAAQ,CAACe,OAAO;EAC5D,CAAC;AAEL,CAAC,EACD;EACEX,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,0BAA0B;EACjCC,IAAI,eAAEJ,OAAA,CAACd,UAAU;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACpBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO,CAAC;EACzCE,QAAQ,EAAE,CACR;IACEZ,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,eAAEJ,OAAA,CAACP,QAAQ;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACc,OAAO;EAC1C,CAAC,EACD;IACEV,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,eAAEJ,OAAA,CAACN,QAAQ;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;EACxB,CAAC;AAEL,CAAC,EACD;EACET,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,YAAY;EACnBC,IAAI,eAAEJ,OAAA,CAACb,QAAQ;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAClBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;AACxB,CAAC,EACD;EACET,EAAE,EAAE,eAAe;EACnBC,KAAK,EAAE,eAAe;EACtBC,IAAI,eAAEJ,OAAA,CAACd,UAAU;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACpBC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,CAACZ,QAAQ,CAACa,KAAK;AACxB,CAAC,CACF;AAED,MAAMI,OAA+B,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM;IAAEC;EAAK,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,KAAK,CAACmD,QAAQ,CAAW,EAAE,CAAC;EAEtE,MAAMC,iBAAiB,GAAIC,MAAc,IAAK;IAC5CH,gBAAgB,CAACI,IAAI,IACnBA,IAAI,CAACC,QAAQ,CAACF,MAAM,CAAC,GACjBC,IAAI,CAACE,MAAM,CAAC1B,EAAE,IAAIA,EAAE,KAAKuB,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CACtB,CAAC;EACH,CAAC;EAED,MAAMI,SAAS,GAAIC,IAAc,IAAc;IAC7C,IAAI,CAACV,IAAI,EAAE,OAAO,KAAK;IACvB,OAAOU,IAAI,CAACpB,KAAK,CAACiB,QAAQ,CAACP,IAAI,CAACW,IAAI,CAAC;EACvC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACF,IAAc,EAAEG,KAAa,GAAG,CAAC,KAAK;IAAA,IAAAC,cAAA;IAC5D,IAAI,CAACL,SAAS,CAACC,IAAI,CAAC,EAAE,OAAO,IAAI;IAEjC,MAAMK,WAAW,GAAGL,IAAI,CAAChB,QAAQ,IAAIgB,IAAI,CAAChB,QAAQ,CAACsB,MAAM,GAAG,CAAC;IAC7D,MAAMC,UAAU,GAAGhB,aAAa,CAACM,QAAQ,CAACG,IAAI,CAAC5B,EAAE,CAAC;IAElD,oBACEF,OAAA,CAAC5B,KAAK,CAACkE,QAAQ;MAAAxB,QAAA,gBACbd,OAAA,CAACzB,QAAQ;QAACgE,cAAc;QAAAzB,QAAA,eACtBd,OAAA,CAACxB,cAAc;UACbgE,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIL,WAAW,EAAE;cACfX,iBAAiB,CAACM,IAAI,CAAC5B,EAAE,CAAC;YAC5B,CAAC,MAAM;cACL;cACAuC,OAAO,CAACC,GAAG,CAAC,oBAAoBZ,IAAI,CAACrB,IAAI,EAAE,CAAC;cAC5CQ,OAAO,CAAC,CAAC;YACX;UACF,CAAE;UACF0B,EAAE,EAAE;YACFC,EAAE,EAAE,CAAC,GAAGX,KAAK,GAAG,CAAC;YACjBY,SAAS,EAAE;UACb,CAAE;UAAA/B,QAAA,gBAEFd,OAAA,CAACvB,YAAY;YACXkE,EAAE,EAAE;cACFG,QAAQ,EAAE,EAAE;cACZC,KAAK,EAAE;YACT,CAAE;YAAAjC,QAAA,EAEDgB,IAAI,CAAC1B;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACfR,OAAA,CAACtB,YAAY;YACXsE,OAAO,EAAElB,IAAI,CAAC3B,KAAM;YACpB8C,sBAAsB,EAAE;cACtBC,QAAQ,EAAEjB,KAAK,GAAG,CAAC,GAAG,UAAU,GAAG,MAAM;cACzCkB,UAAU,EAAElB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG;YACvC;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD2B,WAAW,KACVE,UAAU,gBAAGrC,OAAA,CAACL,UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGR,OAAA,CAACJ,UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,CAC7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EAEV2B,WAAW,iBACVnC,OAAA,CAACnB,QAAQ;QAACuE,EAAE,EAAEf,UAAW;QAACgB,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAxC,QAAA,eACpDd,OAAA,CAAC1B,IAAI;UAACiF,SAAS,EAAC,KAAK;UAAChB,cAAc;UAAAzB,QAAA,GAAAoB,cAAA,GACjCJ,IAAI,CAAChB,QAAQ,cAAAoB,cAAA,uBAAbA,cAAA,CAAesB,GAAG,CAACC,KAAK,IAAIzB,cAAc,CAACyB,KAAK,EAAExB,KAAK,GAAG,CAAC,CAAC;QAAC;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACX;IAAA,GA5CkBsB,IAAI,CAAC5B,EAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA6CZ,CAAC;EAErB,CAAC;EAED,MAAMkD,aAAa,gBACjB1D,OAAA,CAACpB,GAAG;IAAC+D,EAAE,EAAE;MAAEgB,QAAQ,EAAE;IAAO,CAAE;IAAA7C,QAAA,gBAC5Bd,OAAA,CAACrB,OAAO;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXR,OAAA,CAAC1B,IAAI;MAAAwC,QAAA,EACFb,SAAS,CAACuD,GAAG,CAAC1B,IAAI,IAAIE,cAAc,CAACF,IAAI,CAAC;IAAC;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACER,OAAA,CAACpB,GAAG;IACF2E,SAAS,EAAC,KAAK;IACfZ,EAAE,EAAE;MAAEzB,KAAK,EAAE;QAAE0C,EAAE,EAAE1C;MAAM,CAAC;MAAE2C,UAAU,EAAE;QAAED,EAAE,EAAE;MAAE;IAAE,CAAE;IAAA9C,QAAA,gBAGpDd,OAAA,CAAC3B,MAAM;MACLyF,OAAO,EAAC,WAAW;MACnB9C,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjB8C,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;MACFrB,EAAE,EAAE;QACFsB,OAAO,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEN,EAAE,EAAE;QAAO,CAAC;QACpC,oBAAoB,EAAE;UACpBO,SAAS,EAAE,YAAY;UACvBjD,KAAK,EAAEA;QACT;MACF,CAAE;MAAAJ,QAAA,EAED4C;IAAa;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGTR,OAAA,CAAC3B,MAAM;MACLyF,OAAO,EAAC,WAAW;MACnBnB,EAAE,EAAE;QACFsB,OAAO,EAAE;UAAEC,EAAE,EAAE,MAAM;UAAEN,EAAE,EAAE;QAAQ,CAAC;QACpC,oBAAoB,EAAE;UACpBO,SAAS,EAAE,YAAY;UACvBjD,KAAK,EAAEA;QACT;MACF,CAAE;MACFF,IAAI;MAAAF,QAAA,EAEH4C;IAAa;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACW,EAAA,CA1HIJ,OAA+B;EAAA,QAClBlB,OAAO;AAAA;AAAAuE,EAAA,GADpBrD,OAA+B;AA4HrC,eAAeA,OAAO;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}