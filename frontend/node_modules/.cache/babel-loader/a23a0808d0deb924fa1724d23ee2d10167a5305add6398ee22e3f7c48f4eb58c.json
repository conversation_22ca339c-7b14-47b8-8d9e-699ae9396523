{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport * as tf from '@tensorflow/tfjs-core';\nimport { nonMaxSuppression } from '../ops';\nimport { extractImagePatches } from './extractImagePatches';\nimport { MtcnnBox } from './MtcnnBox';\nimport { RNet } from './RNet';\nexport function stage2(img, inputBoxes, scoreThreshold, params, stats) {\n  return __awaiter(this, void 0, void 0, function () {\n    var ts, rnetInputs, rnetOuts, scoresTensor, scores, _a, _b, indices, filteredBoxes, filteredScores, finalBoxes, finalScores, indicesNms, regions_1;\n    return __generator(this, function (_c) {\n      switch (_c.label) {\n        case 0:\n          ts = Date.now();\n          return [4 /*yield*/, extractImagePatches(img, inputBoxes, {\n            width: 24,\n            height: 24\n          })];\n        case 1:\n          rnetInputs = _c.sent();\n          stats.stage2_extractImagePatches = Date.now() - ts;\n          ts = Date.now();\n          rnetOuts = rnetInputs.map(function (rnetInput) {\n            var out = RNet(rnetInput, params);\n            rnetInput.dispose();\n            return out;\n          });\n          stats.stage2_rnet = Date.now() - ts;\n          scoresTensor = rnetOuts.length > 1 ? tf.concat(rnetOuts.map(function (out) {\n            return out.scores;\n          })) : rnetOuts[0].scores;\n          _b = (_a = Array).from;\n          return [4 /*yield*/, scoresTensor.data()];\n        case 2:\n          scores = _b.apply(_a, [_c.sent()]);\n          scoresTensor.dispose();\n          indices = scores.map(function (score, idx) {\n            return {\n              score: score,\n              idx: idx\n            };\n          }).filter(function (c) {\n            return c.score > scoreThreshold;\n          }).map(function (_a) {\n            var idx = _a.idx;\n            return idx;\n          });\n          filteredBoxes = indices.map(function (idx) {\n            return inputBoxes[idx];\n          });\n          filteredScores = indices.map(function (idx) {\n            return scores[idx];\n          });\n          finalBoxes = [];\n          finalScores = [];\n          if (filteredBoxes.length > 0) {\n            ts = Date.now();\n            indicesNms = nonMaxSuppression(filteredBoxes, filteredScores, 0.7);\n            stats.stage2_nms = Date.now() - ts;\n            regions_1 = indicesNms.map(function (idx) {\n              var regionsData = rnetOuts[indices[idx]].regions.arraySync();\n              return new MtcnnBox(regionsData[0][0], regionsData[0][1], regionsData[0][2], regionsData[0][3]);\n            });\n            finalScores = indicesNms.map(function (idx) {\n              return filteredScores[idx];\n            });\n            finalBoxes = indicesNms.map(function (idx, i) {\n              return filteredBoxes[idx].calibrate(regions_1[i]);\n            });\n          }\n          rnetOuts.forEach(function (t) {\n            t.regions.dispose();\n            t.scores.dispose();\n          });\n          return [2 /*return*/, {\n            boxes: finalBoxes,\n            scores: finalScores\n          }];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["tf", "nonMaxSuppression", "extractImagePatches", "MtcnnBox", "RNet", "stage2", "img", "inputBoxes", "scoreThreshold", "params", "stats", "ts", "Date", "now", "width", "height", "rnetInputs", "_c", "sent", "stage2_extractImagePatches", "rnetOuts", "map", "rnetInput", "out", "dispose", "stage2_rnet", "scoresTensor", "length", "concat", "scores", "_b", "_a", "Array", "from", "data", "apply", "indices", "score", "idx", "filter", "c", "filteredBoxes", "filteredScores", "finalBoxes", "finalScores", "indicesNms", "stage2_nms", "regions_1", "regionsData", "regions", "arraySync", "i", "calibrate", "for<PERSON>ach", "t", "boxes"], "sources": ["../../../src/mtcnn/stage2.ts"], "sourcesContent": [null], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAG3C,SAASC,iBAAiB,QAAQ,QAAQ;AAC1C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAG7B,OAAM,SAAgBC,MAAMA,CAC1BC,GAAsB,EACtBC,UAAiB,EACjBC,cAAsB,EACtBC,MAAkB,EAClBC,KAAU;;;;;;UAGNC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;UACA,qBAAMX,mBAAmB,CAACI,GAAG,EAAEC,UAAU,EAAE;YAAEO,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAE,CAAE,CAAC;;UAAlFC,UAAU,GAAGC,EAAA,CAAAC,IAAA,EAAqE;UACxFR,KAAK,CAACS,0BAA0B,GAAGP,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;UAElDA,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;UACTO,QAAQ,GAAGJ,UAAU,CAACK,GAAG,CAC7B,UAAAC,SAAS;YACP,IAAMC,GAAG,GAAGnB,IAAI,CAACkB,SAAS,EAAEb,MAAM,CAAC;YACnCa,SAAS,CAACE,OAAO,EAAE;YACnB,OAAOD,GAAG;UACZ,CAAC,CACF;UACDb,KAAK,CAACe,WAAW,GAAGb,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;UAE7Be,YAAY,GAAGN,QAAQ,CAACO,MAAM,GAAG,CAAC,GACpC3B,EAAE,CAAC4B,MAAM,CAACR,QAAQ,CAACC,GAAG,CAAC,UAAAE,GAAG;YAAI,OAAAA,GAAG,CAACM,MAAM;UAAV,CAAU,CAAC,CAAC,GAC1CT,QAAQ,CAAC,CAAC,CAAC,CAACS,MAAM;UACPC,EAAA,IAAAC,EAAA,GAAAC,KAAK,EAACC,IAAI;UAAC,qBAAMP,YAAY,CAACQ,IAAI,EAAE;;UAA7CL,MAAM,GAAGC,EAAA,CAAAK,KAAA,CAAAJ,EAAA,GAAWd,EAAA,CAAAC,IAAA,EAAyB,EAAC;UACpDQ,YAAY,CAACF,OAAO,EAAE;UAEhBY,OAAO,GAAGP,MAAM,CACnBR,GAAG,CAAC,UAACgB,KAAK,EAAEC,GAAG;YAAK,OAAC;cAAED,KAAK,EAAAA,KAAA;cAAEC,GAAG,EAAAA;YAAA,CAAE;UAAf,CAAgB,CAAC,CACrCC,MAAM,CAAC,UAAAC,CAAC;YAAI,OAAAA,CAAC,CAACH,KAAK,GAAG7B,cAAc;UAAxB,CAAwB,CAAC,CACrCa,GAAG,CAAC,UAACU,EAAO;gBAALO,GAAA,GAAAP,EAAA,CAAAO,GAAG;YAAO,OAAAA,GAAG;UAAH,CAAG,CAAC;UAElBG,aAAa,GAAGL,OAAO,CAACf,GAAG,CAAC,UAAAiB,GAAG;YAAI,OAAA/B,UAAU,CAAC+B,GAAG,CAAC;UAAf,CAAe,CAAC;UACnDI,cAAc,GAAGN,OAAO,CAACf,GAAG,CAAC,UAAAiB,GAAG;YAAI,OAAAT,MAAM,CAACS,GAAG,CAAC;UAAX,CAAW,CAAC;UAElDK,UAAU,GAAU,EAAE;UACtBC,WAAW,GAAa,EAAE;UAE9B,IAAIH,aAAa,CAACd,MAAM,GAAG,CAAC,EAAE;YAC5BhB,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;YACTgC,UAAU,GAAG5C,iBAAiB,CAClCwC,aAAa,EACbC,cAAc,EACd,GAAG,CACJ;YACDhC,KAAK,CAACoC,UAAU,GAAGlC,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;YAE5BoC,SAAA,GAAUF,UAAU,CAACxB,GAAG,CAAC,UAAAiB,GAAG;cAC9B,IAAMU,WAAW,GAAG5B,QAAQ,CAACgB,OAAO,CAACE,GAAG,CAAC,CAAC,CAACW,OAAO,CAACC,SAAS,EAAE;cAC9D,OAAO,IAAI/C,QAAQ,CACjB6C,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjBA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjBA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjBA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAClB;YACH,CAAC,CACF;YAEDJ,WAAW,GAAGC,UAAU,CAACxB,GAAG,CAAC,UAAAiB,GAAG;cAAI,OAAAI,cAAc,CAACJ,GAAG,CAAC;YAAnB,CAAmB,CAAC;YACxDK,UAAU,GAAGE,UAAU,CAACxB,GAAG,CAAC,UAACiB,GAAG,EAAEa,CAAC;cAAK,OAAAV,aAAa,CAACH,GAAG,CAAC,CAACc,SAAS,CAACL,SAAO,CAACI,CAAC,CAAC,CAAC;YAAxC,CAAwC,CAAC;;UAGnF/B,QAAQ,CAACiC,OAAO,CAAC,UAAAC,CAAC;YAChBA,CAAC,CAACL,OAAO,CAACzB,OAAO,EAAE;YACnB8B,CAAC,CAACzB,MAAM,CAACL,OAAO,EAAE;UACpB,CAAC,CAAC;UAEF,sBAAO;YACL+B,KAAK,EAAEZ,UAAU;YACjBd,MAAM,EAAEe;WACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}