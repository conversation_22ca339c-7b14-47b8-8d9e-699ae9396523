{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/PresencePRO/frontend/src/components/TestConnection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { supabase } from '../config/supabase';\nimport { Box, Button, Typography, Alert, CircularProgress } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestConnection = () => {\n  _s();\n  const [status, setStatus] = useState('En attente...');\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState([]);\n  const addResult = message => {\n    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n  const testConnection = async () => {\n    setLoading(true);\n    setResults([]);\n    setStatus('Test en cours...');\n    try {\n      // Test 1: Connexion de base\n      addResult('🧪 Test de connexion de base...');\n      const {\n        data,\n        error\n      } = await supabase.from('users').select('count', {\n        count: 'exact',\n        head: true\n      });\n      if (error) {\n        addResult(`❌ Erreur de connexion: ${error.message}`);\n        setStatus('Échec de connexion');\n        return;\n      }\n      addResult(`✅ Connexion réussie! Nombre d'utilisateurs: ${data}`);\n\n      // Test 2: Session actuelle\n      addResult('🔍 Vérification de la session...');\n      const {\n        data: session\n      } = await supabase.auth.getSession();\n      addResult(`Session: ${session.session ? 'Connecté' : 'Non connecté'}`);\n\n      // Test 3: Tentative de connexion\n      addResult('🔐 Test de connexion admin...');\n      const {\n        data: authData,\n        error: authError\n      } = await supabase.auth.signInWithPassword({\n        email: '<EMAIL>',\n        password: 'admin123'\n      });\n      if (authError) {\n        addResult(`❌ Erreur de connexion: ${authError.message}`);\n      } else {\n        var _authData$user;\n        addResult(`✅ Connexion réussie pour: ${(_authData$user = authData.user) === null || _authData$user === void 0 ? void 0 : _authData$user.email}`);\n\n        // Test 4: Récupération du profil\n        addResult('👤 Récupération du profil...');\n        const {\n          data: userData,\n          error: userError\n        } = await supabase.from('users').select('*').eq('email', '<EMAIL>').single();\n        if (userError) {\n          addResult(`❌ Erreur profil: ${userError.message}`);\n        } else {\n          addResult(`✅ Profil trouvé: ${userData === null || userData === void 0 ? void 0 : userData.email} (${userData === null || userData === void 0 ? void 0 : userData.role})`);\n        }\n\n        // Déconnexion\n        await supabase.auth.signOut();\n        addResult('👋 Déconnexion effectuée');\n      }\n      setStatus('Test terminé');\n    } catch (error) {\n      addResult(`❌ Erreur générale: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);\n      setStatus('Erreur');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    // Test automatique au chargement\n    testConnection();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Test de Connexion Supabase\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Statut: \", status]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: testConnection,\n      disabled: loading,\n      sx: {\n        mb: 2\n      },\n      children: \"Relancer le test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"R\\xE9sultats:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), results.map((result, index) => /*#__PURE__*/_jsxDEV(Alert, {\n        severity: result.includes('❌') ? 'error' : result.includes('✅') ? 'success' : 'info',\n        sx: {\n          mb: 1\n        },\n        children: result\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(TestConnection, \"7Yn90Pgtr+Ob7j0H7SPT7bgo03I=\");\n_c = TestConnection;\nexport default TestConnection;\nvar _c;\n$RefreshReg$(_c, \"TestConnection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "supabase", "Box", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "jsxDEV", "_jsxDEV", "TestConnection", "_s", "status", "setStatus", "loading", "setLoading", "results", "setResults", "addResult", "message", "prev", "Date", "toLocaleTimeString", "testConnection", "data", "error", "from", "select", "count", "head", "session", "auth", "getSession", "authData", "authError", "signInWithPassword", "email", "password", "_authData$user", "user", "userData", "userError", "eq", "single", "role", "signOut", "Error", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "size", "onClick", "disabled", "sx", "map", "result", "index", "severity", "includes", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/components/TestConnection.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabase } from '../config/supabase';\nimport { Box, Button, Typography, Alert, CircularProgress } from '@mui/material';\n\nconst TestConnection: React.FC = () => {\n  const [status, setStatus] = useState<string>('En attente...');\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState<string[]>([]);\n\n  const addResult = (message: string) => {\n    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n\n  const testConnection = async () => {\n    setLoading(true);\n    setResults([]);\n    setStatus('Test en cours...');\n\n    try {\n      // Test 1: Connexion de base\n      addResult('🧪 Test de connexion de base...');\n      const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true });\n      \n      if (error) {\n        addResult(`❌ Erreur de connexion: ${error.message}`);\n        setStatus('Échec de connexion');\n        return;\n      }\n      \n      addResult(`✅ Connexion réussie! Nombre d'utilisateurs: ${data}`);\n\n      // Test 2: Session actuelle\n      addResult('🔍 Vérification de la session...');\n      const { data: session } = await supabase.auth.getSession();\n      addResult(`Session: ${session.session ? 'Connecté' : 'Non connecté'}`);\n\n      // Test 3: Tentative de connexion\n      addResult('🔐 Test de connexion admin...');\n      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({\n        email: '<EMAIL>',\n        password: 'admin123'\n      });\n\n      if (authError) {\n        addResult(`❌ Erreur de connexion: ${authError.message}`);\n      } else {\n        addResult(`✅ Connexion réussie pour: ${authData.user?.email}`);\n        \n        // Test 4: Récupération du profil\n        addResult('👤 Récupération du profil...');\n        const { data: userData, error: userError } = await supabase\n          .from('users')\n          .select('*')\n          .eq('email', '<EMAIL>')\n          .single();\n\n        if (userError) {\n          addResult(`❌ Erreur profil: ${userError.message}`);\n        } else {\n          addResult(`✅ Profil trouvé: ${userData?.email} (${userData?.role})`);\n        }\n\n        // Déconnexion\n        await supabase.auth.signOut();\n        addResult('👋 Déconnexion effectuée');\n      }\n\n      setStatus('Test terminé');\n    } catch (error) {\n      addResult(`❌ Erreur générale: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);\n      setStatus('Erreur');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // Test automatique au chargement\n    testConnection();\n  }, []);\n\n  return (\n    <Box p={3}>\n      <Typography variant=\"h4\" gutterBottom>\n        Test de Connexion Supabase\n      </Typography>\n      \n      <Box mb={2}>\n        <Typography variant=\"h6\">\n          Statut: {status}\n        </Typography>\n        {loading && <CircularProgress size={20} />}\n      </Box>\n\n      <Button \n        variant=\"contained\" \n        onClick={testConnection} \n        disabled={loading}\n        sx={{ mb: 2 }}\n      >\n        Relancer le test\n      </Button>\n\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Résultats:\n        </Typography>\n        {results.map((result, index) => (\n          <Alert \n            key={index} \n            severity={result.includes('❌') ? 'error' : result.includes('✅') ? 'success' : 'info'}\n            sx={{ mb: 1 }}\n          >\n            {result}\n          </Alert>\n        ))}\n      </Box>\n    </Box>\n  );\n};\n\nexport default TestConnection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,gBAAgB,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjF,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAS,eAAe,CAAC;EAC7D,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAW,EAAE,CAAC;EAEpD,MAAMkB,SAAS,GAAIC,OAAe,IAAK;IACrCF,UAAU,CAACG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,KAAKH,OAAO,EAAE,CAAC,CAAC;EACjF,CAAC;EAED,MAAMI,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCR,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdJ,SAAS,CAAC,kBAAkB,CAAC;IAE7B,IAAI;MACF;MACAK,SAAS,CAAC,iCAAiC,CAAC;MAC5C,MAAM;QAAEM,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMvB,QAAQ,CAACwB,IAAI,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MAEpG,IAAIJ,KAAK,EAAE;QACTP,SAAS,CAAC,0BAA0BO,KAAK,CAACN,OAAO,EAAE,CAAC;QACpDN,SAAS,CAAC,oBAAoB,CAAC;QAC/B;MACF;MAEAK,SAAS,CAAC,+CAA+CM,IAAI,EAAE,CAAC;;MAEhE;MACAN,SAAS,CAAC,kCAAkC,CAAC;MAC7C,MAAM;QAAEM,IAAI,EAAEM;MAAQ,CAAC,GAAG,MAAM5B,QAAQ,CAAC6B,IAAI,CAACC,UAAU,CAAC,CAAC;MAC1Dd,SAAS,CAAC,YAAYY,OAAO,CAACA,OAAO,GAAG,UAAU,GAAG,cAAc,EAAE,CAAC;;MAEtE;MACAZ,SAAS,CAAC,+BAA+B,CAAC;MAC1C,MAAM;QAAEM,IAAI,EAAES,QAAQ;QAAER,KAAK,EAAES;MAAU,CAAC,GAAG,MAAMhC,QAAQ,CAAC6B,IAAI,CAACI,kBAAkB,CAAC;QAClFC,KAAK,EAAE,uBAAuB;QAC9BC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIH,SAAS,EAAE;QACbhB,SAAS,CAAC,0BAA0BgB,SAAS,CAACf,OAAO,EAAE,CAAC;MAC1D,CAAC,MAAM;QAAA,IAAAmB,cAAA;QACLpB,SAAS,CAAC,8BAAAoB,cAAA,GAA6BL,QAAQ,CAACM,IAAI,cAAAD,cAAA,uBAAbA,cAAA,CAAeF,KAAK,EAAE,CAAC;;QAE9D;QACAlB,SAAS,CAAC,8BAA8B,CAAC;QACzC,MAAM;UAAEM,IAAI,EAAEgB,QAAQ;UAAEf,KAAK,EAAEgB;QAAU,CAAC,GAAG,MAAMvC,QAAQ,CACxDwB,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXe,EAAE,CAAC,OAAO,EAAE,uBAAuB,CAAC,CACpCC,MAAM,CAAC,CAAC;QAEX,IAAIF,SAAS,EAAE;UACbvB,SAAS,CAAC,oBAAoBuB,SAAS,CAACtB,OAAO,EAAE,CAAC;QACpD,CAAC,MAAM;UACLD,SAAS,CAAC,oBAAoBsB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEJ,KAAK,KAAKI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,IAAI,GAAG,CAAC;QACtE;;QAEA;QACA,MAAM1C,QAAQ,CAAC6B,IAAI,CAACc,OAAO,CAAC,CAAC;QAC7B3B,SAAS,CAAC,0BAA0B,CAAC;MACvC;MAEAL,SAAS,CAAC,cAAc,CAAC;IAC3B,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdP,SAAS,CAAC,sBAAsBO,KAAK,YAAYqB,KAAK,GAAGrB,KAAK,CAACN,OAAO,GAAG,iBAAiB,EAAE,CAAC;MAC7FN,SAAS,CAAC,QAAQ,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDd,SAAS,CAAC,MAAM;IACd;IACAsB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA,CAACN,GAAG;IAAC4C,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRvC,OAAA,CAACJ,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7C,OAAA,CAACN,GAAG;MAACoD,EAAE,EAAE,CAAE;MAAAP,QAAA,gBACTvC,OAAA,CAACJ,UAAU;QAAC4C,OAAO,EAAC,IAAI;QAAAD,QAAA,GAAC,UACf,EAACpC,MAAM;MAAA;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EACZxC,OAAO,iBAAIL,OAAA,CAACF,gBAAgB;QAACiD,IAAI,EAAE;MAAG;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEN7C,OAAA,CAACL,MAAM;MACL6C,OAAO,EAAC,WAAW;MACnBQ,OAAO,EAAElC,cAAe;MACxBmC,QAAQ,EAAE5C,OAAQ;MAClB6C,EAAE,EAAE;QAAEJ,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACf;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET7C,OAAA,CAACN,GAAG;MAAA6C,QAAA,gBACFvC,OAAA,CAACJ,UAAU;QAAC4C,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZtC,OAAO,CAAC4C,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBrD,OAAA,CAACH,KAAK;QAEJyD,QAAQ,EAAEF,MAAM,CAACG,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAGH,MAAM,CAACG,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,MAAO;QACrFL,EAAE,EAAE;UAAEJ,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,EAEba;MAAM,GAJFC,KAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKL,CACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAnHID,cAAwB;AAAAuD,EAAA,GAAxBvD,cAAwB;AAqH9B,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}