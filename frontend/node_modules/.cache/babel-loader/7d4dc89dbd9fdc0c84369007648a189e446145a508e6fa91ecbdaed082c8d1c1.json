{"ast": null, "code": "import * as tf from '@tensorflow/tfjs-core';\nimport { pointwiseConvLayer } from './pointwiseConvLayer';\nvar epsilon = 0.0010000000474974513;\nfunction depthwiseConvLayer(x, params, strides) {\n  return tf.tidy(function () {\n    var out = tf.depthwiseConv2d(x, params.filters, strides, 'same');\n    out = tf.batchNorm(out, params.batch_norm_mean, params.batch_norm_variance, params.batch_norm_offset, params.batch_norm_scale, epsilon);\n    return tf.clipByValue(out, 0, 6);\n  });\n}\nfunction getStridesForLayerIdx(layerIdx) {\n  return [2, 4, 6, 12].some(function (idx) {\n    return idx === layerIdx;\n  }) ? [2, 2] : [1, 1];\n}\nexport function mobileNetV1(x, params) {\n  return tf.tidy(function () {\n    var conv11 = null;\n    var out = pointwiseConvLayer(x, params.conv_0, [2, 2]);\n    var convPairParams = [params.conv_1, params.conv_2, params.conv_3, params.conv_4, params.conv_5, params.conv_6, params.conv_7, params.conv_8, params.conv_9, params.conv_10, params.conv_11, params.conv_12, params.conv_13];\n    convPairParams.forEach(function (param, i) {\n      var layerIdx = i + 1;\n      var depthwiseConvStrides = getStridesForLayerIdx(layerIdx);\n      out = depthwiseConvLayer(out, param.depthwise_conv, depthwiseConvStrides);\n      out = pointwiseConvLayer(out, param.pointwise_conv, [1, 1]);\n      if (layerIdx === 11) {\n        conv11 = out;\n      }\n    });\n    if (conv11 === null) {\n      throw new Error('mobileNetV1 - output of conv layer 11 is null');\n    }\n    return {\n      out: out,\n      conv11: conv11\n    };\n  });\n}", "map": {"version": 3, "names": ["tf", "pointwiseConv<PERSON><PERSON>er", "epsilon", "depthwiseConvLayer", "x", "params", "strides", "tidy", "out", "depthwiseConv2d", "filters", "batchNorm", "batch_norm_mean", "batch_norm_variance", "batch_norm_offset", "batch_norm_scale", "clipByValue", "getStridesForLayerIdx", "layerIdx", "some", "idx", "mobileNetV1", "conv11", "conv_0", "convPairParams", "conv_1", "conv_2", "conv_3", "conv_4", "conv_5", "conv_6", "conv_7", "conv_8", "conv_9", "conv_10", "conv_11", "conv_12", "conv_13", "for<PERSON>ach", "param", "i", "depthwiseConvStrides", "depthwise_conv", "pointwise_conv", "Error"], "sources": ["../../../src/ssdMobilenetv1/mobileNetV1.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,SAASC,kBAAkB,QAAQ,sBAAsB;AAGzD,IAAMC,OAAO,GAAG,qBAAqB;AAErC,SAASC,kBAAkBA,CACzBC,CAAc,EACdC,MAAuC,EACvCC,OAAyB;EAEzB,OAAON,EAAE,CAACO,IAAI,CAAC;IAEb,IAAIC,GAAG,GAAGR,EAAE,CAACS,eAAe,CAACL,CAAC,EAAEC,MAAM,CAACK,OAAO,EAAEJ,OAAO,EAAE,MAAM,CAAC;IAChEE,GAAG,GAAGR,EAAE,CAACW,SAAS,CAChBH,GAAG,EACHH,MAAM,CAACO,eAAe,EACtBP,MAAM,CAACQ,mBAAmB,EAC1BR,MAAM,CAACS,iBAAiB,EACxBT,MAAM,CAACU,gBAAgB,EACvBb,OAAO,CACR;IACD,OAAOF,EAAE,CAACgB,WAAW,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAElC,CAAC,CAAC;AACJ;AAEA,SAASS,qBAAqBA,CAACC,QAAgB;EAC7C,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG;IAAI,OAAAA,GAAG,KAAKF,QAAQ;EAAhB,CAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACtE;AAEA,OAAM,SAAUG,WAAWA,CAACjB,CAAc,EAAEC,MAA0B;EACpE,OAAOL,EAAE,CAACO,IAAI,CAAC;IAEb,IAAIe,MAAM,GAAG,IAAI;IACjB,IAAId,GAAG,GAAGP,kBAAkB,CAACG,CAAC,EAAEC,MAAM,CAACkB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtD,IAAMC,cAAc,GAAG,CACrBnB,MAAM,CAACoB,MAAM,EACbpB,MAAM,CAACqB,MAAM,EACbrB,MAAM,CAACsB,MAAM,EACbtB,MAAM,CAACuB,MAAM,EACbvB,MAAM,CAACwB,MAAM,EACbxB,MAAM,CAACyB,MAAM,EACbzB,MAAM,CAAC0B,MAAM,EACb1B,MAAM,CAAC2B,MAAM,EACb3B,MAAM,CAAC4B,MAAM,EACb5B,MAAM,CAAC6B,OAAO,EACd7B,MAAM,CAAC8B,OAAO,EACd9B,MAAM,CAAC+B,OAAO,EACd/B,MAAM,CAACgC,OAAO,CACf;IAEDb,cAAc,CAACc,OAAO,CAAC,UAACC,KAAK,EAAEC,CAAC;MAC9B,IAAMtB,QAAQ,GAAGsB,CAAC,GAAG,CAAC;MACtB,IAAMC,oBAAoB,GAAGxB,qBAAqB,CAACC,QAAQ,CAAC;MAC5DV,GAAG,GAAGL,kBAAkB,CAACK,GAAG,EAAE+B,KAAK,CAACG,cAAc,EAAED,oBAAoB,CAAC;MACzEjC,GAAG,GAAGP,kBAAkB,CAACO,GAAG,EAAE+B,KAAK,CAACI,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3D,IAAIzB,QAAQ,KAAK,EAAE,EAAE;QACnBI,MAAM,GAAGd,GAAG;;IAEhB,CAAC,CAAC;IAEF,IAAIc,MAAM,KAAK,IAAI,EAAE;MACnB,MAAM,IAAIsB,KAAK,CAAC,+CAA+C,CAAC;;IAGlE,OAAO;MACLpC,GAAG,EAAAA,GAAA;MACHc,MAAM,EAAEA;KACT;EAEH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}