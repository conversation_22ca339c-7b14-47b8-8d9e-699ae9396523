{"ast": null, "code": "import { isDimensions, isValidNumber } from '../utils';\nimport { Point } from './Point';\nvar Box = /** @class */function () {\n  function Box(_box, allowNegativeDimensions) {\n    if (allowNegativeDimensions === void 0) {\n      allowNegativeDimensions = true;\n    }\n    var box = _box || {};\n    var isBbox = [box.left, box.top, box.right, box.bottom].every(isValidNumber);\n    var isRect = [box.x, box.y, box.width, box.height].every(isValidNumber);\n    if (!isRect && !isBbox) {\n      throw new Error(\"Box.constructor - expected box to be IBoundingBox | IRect, instead have \" + JSON.stringify(box));\n    }\n    var _a = isRect ? [box.x, box.y, box.width, box.height] : [box.left, box.top, box.right - box.left, box.bottom - box.top],\n      x = _a[0],\n      y = _a[1],\n      width = _a[2],\n      height = _a[3];\n    Box.assertIsValidBox({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }, 'Box.constructor', allowNegativeDimensions);\n    this._x = x;\n    this._y = y;\n    this._width = width;\n    this._height = height;\n  }\n  Box.isRect = function (rect) {\n    return !!rect && [rect.x, rect.y, rect.width, rect.height].every(isValidNumber);\n  };\n  Box.assertIsValidBox = function (box, callee, allowNegativeDimensions) {\n    if (allowNegativeDimensions === void 0) {\n      allowNegativeDimensions = false;\n    }\n    if (!Box.isRect(box)) {\n      throw new Error(callee + \" - invalid box: \" + JSON.stringify(box) + \", expected object with properties x, y, width, height\");\n    }\n    if (!allowNegativeDimensions && (box.width < 0 || box.height < 0)) {\n      throw new Error(callee + \" - width (\" + box.width + \") and height (\" + box.height + \") must be positive numbers\");\n    }\n  };\n  Object.defineProperty(Box.prototype, \"x\", {\n    get: function () {\n      return this._x;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"y\", {\n    get: function () {\n      return this._y;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"width\", {\n    get: function () {\n      return this._width;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"height\", {\n    get: function () {\n      return this._height;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"left\", {\n    get: function () {\n      return this.x;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"top\", {\n    get: function () {\n      return this.y;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"right\", {\n    get: function () {\n      return this.x + this.width;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"bottom\", {\n    get: function () {\n      return this.y + this.height;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"area\", {\n    get: function () {\n      return this.width * this.height;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"topLeft\", {\n    get: function () {\n      return new Point(this.left, this.top);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"topRight\", {\n    get: function () {\n      return new Point(this.right, this.top);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"bottomLeft\", {\n    get: function () {\n      return new Point(this.left, this.bottom);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(Box.prototype, \"bottomRight\", {\n    get: function () {\n      return new Point(this.right, this.bottom);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Box.prototype.round = function () {\n    var _a = [this.x, this.y, this.width, this.height].map(function (val) {\n        return Math.round(val);\n      }),\n      x = _a[0],\n      y = _a[1],\n      width = _a[2],\n      height = _a[3];\n    return new Box({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    });\n  };\n  Box.prototype.floor = function () {\n    var _a = [this.x, this.y, this.width, this.height].map(function (val) {\n        return Math.floor(val);\n      }),\n      x = _a[0],\n      y = _a[1],\n      width = _a[2],\n      height = _a[3];\n    return new Box({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    });\n  };\n  Box.prototype.toSquare = function () {\n    var _a = this,\n      x = _a.x,\n      y = _a.y,\n      width = _a.width,\n      height = _a.height;\n    var diff = Math.abs(width - height);\n    if (width < height) {\n      x -= diff / 2;\n      width += diff;\n    }\n    if (height < width) {\n      y -= diff / 2;\n      height += diff;\n    }\n    return new Box({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    });\n  };\n  Box.prototype.rescale = function (s) {\n    var scaleX = isDimensions(s) ? s.width : s;\n    var scaleY = isDimensions(s) ? s.height : s;\n    return new Box({\n      x: this.x * scaleX,\n      y: this.y * scaleY,\n      width: this.width * scaleX,\n      height: this.height * scaleY\n    });\n  };\n  Box.prototype.pad = function (padX, padY) {\n    var _a = [this.x - padX / 2, this.y - padY / 2, this.width + padX, this.height + padY],\n      x = _a[0],\n      y = _a[1],\n      width = _a[2],\n      height = _a[3];\n    return new Box({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    });\n  };\n  Box.prototype.clipAtImageBorders = function (imgWidth, imgHeight) {\n    var _a = this,\n      x = _a.x,\n      y = _a.y,\n      right = _a.right,\n      bottom = _a.bottom;\n    var clippedX = Math.max(x, 0);\n    var clippedY = Math.max(y, 0);\n    var newWidth = right - clippedX;\n    var newHeight = bottom - clippedY;\n    var clippedWidth = Math.min(newWidth, imgWidth - clippedX);\n    var clippedHeight = Math.min(newHeight, imgHeight - clippedY);\n    return new Box({\n      x: clippedX,\n      y: clippedY,\n      width: clippedWidth,\n      height: clippedHeight\n    }).floor();\n  };\n  Box.prototype.shift = function (sx, sy) {\n    var _a = this,\n      width = _a.width,\n      height = _a.height;\n    var x = this.x + sx;\n    var y = this.y + sy;\n    return new Box({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    });\n  };\n  Box.prototype.padAtBorders = function (imageHeight, imageWidth) {\n    var w = this.width + 1;\n    var h = this.height + 1;\n    var dx = 1;\n    var dy = 1;\n    var edx = w;\n    var edy = h;\n    var x = this.left;\n    var y = this.top;\n    var ex = this.right;\n    var ey = this.bottom;\n    if (ex > imageWidth) {\n      edx = -ex + imageWidth + w;\n      ex = imageWidth;\n    }\n    if (ey > imageHeight) {\n      edy = -ey + imageHeight + h;\n      ey = imageHeight;\n    }\n    if (x < 1) {\n      edy = 2 - x;\n      x = 1;\n    }\n    if (y < 1) {\n      edy = 2 - y;\n      y = 1;\n    }\n    return {\n      dy: dy,\n      edy: edy,\n      dx: dx,\n      edx: edx,\n      y: y,\n      ey: ey,\n      x: x,\n      ex: ex,\n      w: w,\n      h: h\n    };\n  };\n  Box.prototype.calibrate = function (region) {\n    return new Box({\n      left: this.left + region.left * this.width,\n      top: this.top + region.top * this.height,\n      right: this.right + region.right * this.width,\n      bottom: this.bottom + region.bottom * this.height\n    }).toSquare().round();\n  };\n  return Box;\n}();\nexport { Box };", "map": {"version": 3, "names": ["isDimensions", "isValidNumber", "Point", "Box", "_box", "allowNegativeDimensions", "box", "isBbox", "left", "top", "right", "bottom", "every", "isRect", "x", "y", "width", "height", "Error", "JSON", "stringify", "_a", "assertIsValidBox", "_x", "_y", "_width", "_height", "rect", "callee", "Object", "defineProperty", "prototype", "get", "round", "floor", "toSquare", "diff", "Math", "abs", "rescale", "s", "scaleX", "scaleY", "pad", "padX", "padY", "clipAtImageBorders", "imgWidth", "imgHeight", "clippedX", "max", "clippedY", "newWidth", "newHeight", "<PERSON><PERSON><PERSON><PERSON>", "min", "clippedHeight", "shift", "sx", "sy", "padAtBorders", "imageHeight", "imageWidth", "w", "h", "dx", "dy", "edx", "edy", "ex", "ey", "calibrate", "region"], "sources": ["../../../src/classes/Box.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAQ,UAAU;AAGtD,SAASC,KAAK,QAAQ,SAAS;AAG/B,IAAAC,GAAA;EAqBE,SAAAA,IAAYC,IAA0B,EAAEC,uBAAuC;IAAvC,IAAAA,uBAAA;MAAAA,uBAAA,OAAuC;IAAA;IAC7E,IAAMC,GAAG,GAAIF,IAAI,IAAI,EAAU;IAE/B,IAAMG,MAAM,GAAG,CAACD,GAAG,CAACE,IAAI,EAAEF,GAAG,CAACG,GAAG,EAAEH,GAAG,CAACI,KAAK,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAACC,KAAK,CAACX,aAAa,CAAC;IAC9E,IAAMY,MAAM,GAAG,CAACP,GAAG,CAACQ,CAAC,EAAER,GAAG,CAACS,CAAC,EAAET,GAAG,CAACU,KAAK,EAAEV,GAAG,CAACW,MAAM,CAAC,CAACL,KAAK,CAACX,aAAa,CAAC;IAEzE,IAAI,CAACY,MAAM,IAAI,CAACN,MAAM,EAAE;MACtB,MAAM,IAAIW,KAAK,CAAC,6EAA2EC,IAAI,CAACC,SAAS,CAACd,GAAG,CAAG,CAAC;;IAG7G,IAAAe,EAAA,GAAAR,MAAA,G,uGAE6D;MAF5DC,CAAA,GAAAO,EAAA,GAAC;MAAEN,CAAA,GAAAM,EAAA,GAAC;MAAEL,KAAA,GAAAK,EAAA,GAAK;MAAEJ,MAAA,GAAAI,EAAA,GAE+C;IAEnElB,GAAG,CAACmB,gBAAgB,CAAC;MAAER,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,EAAE,iBAAiB,EAAEZ,uBAAuB,CAAC;IAEzF,IAAI,CAACkB,EAAE,GAAGT,CAAC;IACX,IAAI,CAACU,EAAE,GAAGT,CAAC;IACX,IAAI,CAACU,MAAM,GAAGT,KAAK;IACnB,IAAI,CAACU,OAAO,GAAGT,MAAM;EACvB;EAvCcd,GAAA,CAAAU,MAAM,GAApB,UAAqBc,IAAS;IAC5B,OAAO,CAAC,CAACA,IAAI,IAAI,CAACA,IAAI,CAACb,CAAC,EAAEa,IAAI,CAACZ,CAAC,EAAEY,IAAI,CAACX,KAAK,EAAEW,IAAI,CAACV,MAAM,CAAC,CAACL,KAAK,CAACX,aAAa,CAAC;EACjF,CAAC;EAEaE,GAAA,CAAAmB,gBAAgB,GAA9B,UAA+BhB,GAAQ,EAAEsB,MAAc,EAAEvB,uBAAwC;IAAxC,IAAAA,uBAAA;MAAAA,uBAAA,QAAwC;IAAA;IAC/F,IAAI,CAACF,GAAG,CAACU,MAAM,CAACP,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIY,KAAK,CAAIU,MAAM,wBAAmBT,IAAI,CAACC,SAAS,CAACd,GAAG,CAAC,0DAAuD,CAAC;;IAGzH,IAAI,CAACD,uBAAuB,KAAKC,GAAG,CAACU,KAAK,GAAG,CAAC,IAAIV,GAAG,CAACW,MAAM,GAAG,CAAC,CAAC,EAAE;MACjE,MAAM,IAAIC,KAAK,CAAIU,MAAM,kBAAatB,GAAG,CAACU,KAAK,sBAAiBV,GAAG,CAACW,MAAM,+BAA4B,CAAC;;EAE3G,CAAC;EA6BDY,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,KAAC;SAAZ,SAAAC,CAAA;MAAyB,OAAO,IAAI,CAACT,EAAE;IAAC,CAAC;;;;EACzCM,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,KAAC;SAAZ,SAAAC,CAAA;MAAyB,OAAO,IAAI,CAACR,EAAE;IAAC,CAAC;;;;EACzCK,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAACP,MAAM;IAAC,CAAC;;;;EACjDI,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,UAAM;SAAjB,SAAAC,CAAA;MAA8B,OAAO,IAAI,CAACN,OAAO;IAAC,CAAC;;;;EACnDG,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,QAAI;SAAf,SAAAC,CAAA;MAA4B,OAAO,IAAI,CAAClB,CAAC;IAAC,CAAC;;;;EAC3Ce,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,OAAG;SAAd,SAAAC,CAAA;MAA2B,OAAO,IAAI,CAACjB,CAAC;IAAC,CAAC;;;;EAC1Cc,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,SAAK;SAAhB,SAAAC,CAAA;MAA6B,OAAO,IAAI,CAAClB,CAAC,GAAG,IAAI,CAACE,KAAK;IAAC,CAAC;;;;EACzDa,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,UAAM;SAAjB,SAAAC,CAAA;MAA8B,OAAO,IAAI,CAACjB,CAAC,GAAG,IAAI,CAACE,MAAM;IAAC,CAAC;;;;EAC3DY,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,QAAI;SAAf,SAAAC,CAAA;MAA4B,OAAO,IAAI,CAAChB,KAAK,GAAG,IAAI,CAACC,MAAM;IAAC,CAAC;;;;EAC7DY,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,WAAO;SAAlB,SAAAC,CAAA;MAA8B,OAAO,IAAI9B,KAAK,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACC,GAAG,CAAC;IAAC,CAAC;;;;EACrEoB,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,YAAQ;SAAnB,SAAAC,CAAA;MAA+B,OAAO,IAAI9B,KAAK,CAAC,IAAI,CAACQ,KAAK,EAAE,IAAI,CAACD,GAAG,CAAC;IAAC,CAAC;;;;EACvEoB,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,cAAU;SAArB,SAAAC,CAAA;MAAiC,OAAO,IAAI9B,KAAK,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACG,MAAM,CAAC;IAAC,CAAC;;;;EAC3EkB,MAAA,CAAAC,cAAA,CAAW3B,GAAA,CAAA4B,SAAA,eAAW;SAAtB,SAAAC,CAAA;MAAkC,OAAO,IAAI9B,KAAK,CAAC,IAAI,CAACQ,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;IAAC,CAAC;;;;EAEtER,GAAA,CAAA4B,SAAA,CAAAE,KAAK,GAAZ;IACQ,IAAAZ,EAAA,SAAAP,CAAA,OAAAC,CAAA,OAAAC,KAAA,OAAAC,MAAA,E;;QACwB;MADvBH,CAAA,GAAAO,EAAA,GAAC;MAAEN,CAAA,GAAAM,EAAA,GAAC;MAAEL,KAAA,GAAAK,EAAA,GAAK;MAAEJ,MAAA,GAAAI,EAAA,GACU;IAC9B,OAAO,IAAIlB,GAAG,CAAC;MAAEW,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAAC;EACzC,CAAC;EAEMd,GAAA,CAAA4B,SAAA,CAAAG,KAAK,GAAZ;IACQ,IAAAb,EAAA,SAAAP,CAAA,OAAAC,CAAA,OAAAC,KAAA,OAAAC,MAAA,E;;QACwB;MADvBH,CAAA,GAAAO,EAAA,GAAC;MAAEN,CAAA,GAAAM,EAAA,GAAC;MAAEL,KAAA,GAAAK,EAAA,GAAK;MAAEJ,MAAA,GAAAI,EAAA,GACU;IAC9B,OAAO,IAAIlB,GAAG,CAAC;MAAEW,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAAC;EACzC,CAAC;EAEMd,GAAA,CAAA4B,SAAA,CAAAI,QAAQ,GAAf;IACM,IAAAd,EAAA,OAA8B;MAA5BP,CAAA,GAAAO,EAAA,CAAAP,CAAC;MAAEC,CAAA,GAAAM,EAAA,CAAAN,CAAC;MAAEC,KAAA,GAAAK,EAAA,CAAAL,KAAK;MAAEC,MAAA,GAAAI,EAAA,CAAAJ,MAAe;IAClC,IAAMmB,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACtB,KAAK,GAAGC,MAAM,CAAC;IACrC,IAAID,KAAK,GAAGC,MAAM,EAAE;MAClBH,CAAC,IAAKsB,IAAI,GAAG,CAAE;MACfpB,KAAK,IAAIoB,IAAI;;IAEf,IAAInB,MAAM,GAAGD,KAAK,EAAE;MAClBD,CAAC,IAAKqB,IAAI,GAAG,CAAE;MACfnB,MAAM,IAAImB,IAAI;;IAGhB,OAAO,IAAIjC,GAAG,CAAC;MAAEW,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAAC;EACzC,CAAC;EAEMd,GAAA,CAAA4B,SAAA,CAAAQ,OAAO,GAAd,UAAeC,CAAuB;IACpC,IAAMC,MAAM,GAAGzC,YAAY,CAACwC,CAAC,CAAC,GAAIA,CAAiB,CAACxB,KAAK,GAAGwB,CAAW;IACvE,IAAME,MAAM,GAAG1C,YAAY,CAACwC,CAAC,CAAC,GAAIA,CAAiB,CAACvB,MAAM,GAAGuB,CAAW;IACxE,OAAO,IAAIrC,GAAG,CAAC;MACbW,CAAC,EAAE,IAAI,CAACA,CAAC,GAAG2B,MAAM;MAClB1B,CAAC,EAAE,IAAI,CAACA,CAAC,GAAG2B,MAAM;MAClB1B,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGyB,MAAM;MAC1BxB,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGyB;KACvB,CAAC;EACJ,CAAC;EAEMvC,GAAA,CAAA4B,SAAA,CAAAY,GAAG,GAAV,UAAWC,IAAY,EAAEC,IAAY;IAC/B,IAAAxB,EAAA,I,4EAKH;MALIP,CAAA,GAAAO,EAAA,GAAC;MAAEN,CAAA,GAAAM,EAAA,GAAC;MAAEL,KAAA,GAAAK,EAAA,GAAK;MAAEJ,MAAA,GAAAI,EAAA,GAKjB;IACD,OAAO,IAAIlB,GAAG,CAAC;MAAEW,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAAC;EACzC,CAAC;EAEMd,GAAA,CAAA4B,SAAA,CAAAe,kBAAkB,GAAzB,UAA0BC,QAAgB,EAAEC,SAAiB;IACrD,IAAA3B,EAAA,OAA8B;MAA5BP,CAAA,GAAAO,EAAA,CAAAP,CAAC;MAAEC,CAAA,GAAAM,EAAA,CAAAN,CAAC;MAAEL,KAAA,GAAAW,EAAA,CAAAX,KAAK;MAAEC,MAAA,GAAAU,EAAA,CAAAV,MAAe;IACpC,IAAMsC,QAAQ,GAAGZ,IAAI,CAACa,GAAG,CAACpC,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAMqC,QAAQ,GAAGd,IAAI,CAACa,GAAG,CAACnC,CAAC,EAAE,CAAC,CAAC;IAE/B,IAAMqC,QAAQ,GAAG1C,KAAK,GAAGuC,QAAQ;IACjC,IAAMI,SAAS,GAAG1C,MAAM,GAAGwC,QAAQ;IACnC,IAAMG,YAAY,GAAGjB,IAAI,CAACkB,GAAG,CAACH,QAAQ,EAAEL,QAAQ,GAAGE,QAAQ,CAAC;IAC5D,IAAMO,aAAa,GAAGnB,IAAI,CAACkB,GAAG,CAACF,SAAS,EAAEL,SAAS,GAAGG,QAAQ,CAAC;IAE/D,OAAQ,IAAIhD,GAAG,CAAC;MAAEW,CAAC,EAAEmC,QAAQ;MAAElC,CAAC,EAAEoC,QAAQ;MAAEnC,KAAK,EAAEsC,YAAY;MAAErC,MAAM,EAAEuC;IAAa,CAAC,CAAC,CAAEtB,KAAK,EAAE;EACnG,CAAC;EAEM/B,GAAA,CAAA4B,SAAA,CAAA0B,KAAK,GAAZ,UAAaC,EAAU,EAAEC,EAAU;IAC3B,IAAAtC,EAAA,OAAwB;MAAtBL,KAAA,GAAAK,EAAA,CAAAL,KAAK;MAAEC,MAAA,GAAAI,EAAA,CAAAJ,MAAe;IAC9B,IAAMH,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG4C,EAAE;IACrB,IAAM3C,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG4C,EAAE;IAErB,OAAO,IAAIxD,GAAG,CAAC;MAAEW,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,CAAE,CAAC;EACzC,CAAC;EAEMd,GAAA,CAAA4B,SAAA,CAAA6B,YAAY,GAAnB,UAAoBC,WAAmB,EAAEC,UAAkB;IACzD,IAAMC,CAAC,GAAG,IAAI,CAAC/C,KAAK,GAAG,CAAC;IACxB,IAAMgD,CAAC,GAAG,IAAI,CAAC/C,MAAM,GAAG,CAAC;IAEzB,IAAIgD,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,GAAG,GAAGJ,CAAC;IACX,IAAIK,GAAG,GAAGJ,CAAC;IAEX,IAAIlD,CAAC,GAAG,IAAI,CAACN,IAAI;IACjB,IAAIO,CAAC,GAAG,IAAI,CAACN,GAAG;IAChB,IAAI4D,EAAE,GAAG,IAAI,CAAC3D,KAAK;IACnB,IAAI4D,EAAE,GAAG,IAAI,CAAC3D,MAAM;IAEpB,IAAI0D,EAAE,GAAGP,UAAU,EAAE;MACnBK,GAAG,GAAG,CAACE,EAAE,GAAGP,UAAU,GAAGC,CAAC;MAC1BM,EAAE,GAAGP,UAAU;;IAEjB,IAAIQ,EAAE,GAAGT,WAAW,EAAE;MACpBO,GAAG,GAAG,CAACE,EAAE,GAAGT,WAAW,GAAGG,CAAC;MAC3BM,EAAE,GAAGT,WAAW;;IAElB,IAAI/C,CAAC,GAAG,CAAC,EAAE;MACTsD,GAAG,GAAG,CAAC,GAAGtD,CAAC;MACXA,CAAC,GAAG,CAAC;;IAEP,IAAIC,CAAC,GAAG,CAAC,EAAE;MACTqD,GAAG,GAAG,CAAC,GAAGrD,CAAC;MACXA,CAAC,GAAG,CAAC;;IAGP,OAAO;MAAEmD,EAAE,EAAAA,EAAA;MAAEE,GAAG,EAAAA,GAAA;MAAEH,EAAE,EAAAA,EAAA;MAAEE,GAAG,EAAAA,GAAA;MAAEpD,CAAC,EAAAA,CAAA;MAAEuD,EAAE,EAAAA,EAAA;MAAExD,CAAC,EAAAA,CAAA;MAAEuD,EAAE,EAAAA,EAAA;MAAEN,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA;IAAA,CAAE;EACjD,CAAC;EAEM7D,GAAA,CAAA4B,SAAA,CAAAwC,SAAS,GAAhB,UAAiBC,MAAW;IAC1B,OAAO,IAAIrE,GAAG,CAAC;MACbK,IAAI,EAAE,IAAI,CAACA,IAAI,GAAIgE,MAAM,CAAChE,IAAI,GAAG,IAAI,CAACQ,KAAM;MAC5CP,GAAG,EAAE,IAAI,CAACA,GAAG,GAAI+D,MAAM,CAAC/D,GAAG,GAAG,IAAI,CAACQ,MAAO;MAC1CP,KAAK,EAAE,IAAI,CAACA,KAAK,GAAI8D,MAAM,CAAC9D,KAAK,GAAG,IAAI,CAACM,KAAM;MAC/CL,MAAM,EAAE,IAAI,CAACA,MAAM,GAAI6D,MAAM,CAAC7D,MAAM,GAAG,IAAI,CAACM;KAC7C,CAAC,CAACkB,QAAQ,EAAE,CAACF,KAAK,EAAE;EACvB,CAAC;EACH,OAAA9B,GAAC;AAAD,CAAC,CAxKD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}