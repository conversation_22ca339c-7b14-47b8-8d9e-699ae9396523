{"ast": null, "code": "/**\n * Configuration Firebase pour PresencePro\n */\n\nimport { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\n// Configuration Firebase pour PresencePro\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || \"AIzaSyCMoA8Up7238cV0siW9-zOUvh4M9cz0_UE\",\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || \"presencepro-3f21f.firebaseapp.com\",\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || \"presencepro-3f21f\",\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || \"presencepro-3f21f.firebasestorage.app\",\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || \"691712514199\",\n  appId: process.env.REACT_APP_FIREBASE_APP_ID || \"1:691712514199:web:abb3656875005cb23831d2\"\n};\n\n// Initialiser Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialiser les services Firebase\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_FIREBASE_API_KEY", "authDomain", "REACT_APP_FIREBASE_AUTH_DOMAIN", "projectId", "REACT_APP_FIREBASE_PROJECT_ID", "storageBucket", "REACT_APP_FIREBASE_STORAGE_BUCKET", "messagingSenderId", "REACT_APP_FIREBASE_MESSAGING_SENDER_ID", "appId", "REACT_APP_FIREBASE_APP_ID", "app", "auth", "db", "storage"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/src/config/firebase.ts"], "sourcesContent": ["/**\n * Configuration Firebase pour PresencePro\n */\n\nimport { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\n// Configuration Firebase pour PresencePro\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || \"AIzaSyCMoA8Up7238cV0siW9-zOUvh4M9cz0_UE\",\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || \"presencepro-3f21f.firebaseapp.com\",\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || \"presencepro-3f21f\",\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || \"presencepro-3f21f.firebasestorage.app\",\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || \"691712514199\",\n  appId: process.env.REACT_APP_FIREBASE_APP_ID || \"1:691712514199:web:abb3656875005cb23831d2\"\n};\n\n// Initialiser Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialiser les services Firebase\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\nexport default app;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;;AAE7C;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAI,yCAAyC;EAC3FC,UAAU,EAAEH,OAAO,CAACC,GAAG,CAACG,8BAA8B,IAAI,mCAAmC;EAC7FC,SAAS,EAAEL,OAAO,CAACC,GAAG,CAACK,6BAA6B,IAAI,mBAAmB;EAC3EC,aAAa,EAAEP,OAAO,CAACC,GAAG,CAACO,iCAAiC,IAAI,uCAAuC;EACvGC,iBAAiB,EAAET,OAAO,CAACC,GAAG,CAACS,sCAAsC,IAAI,cAAc;EACvFC,KAAK,EAAEX,OAAO,CAACC,GAAG,CAACW,yBAAyB,IAAI;AAClD,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGnB,aAAa,CAACI,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMgB,IAAI,GAAGnB,OAAO,CAACkB,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGnB,YAAY,CAACiB,GAAG,CAAC;AACnC,OAAO,MAAMG,OAAO,GAAGnB,UAAU,CAACgB,GAAG,CAAC;AAEtC,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}