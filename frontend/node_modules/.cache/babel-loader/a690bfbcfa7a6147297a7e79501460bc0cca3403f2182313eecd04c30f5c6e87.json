{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { FaceFeatureExtractor } from '../faceFeatureExtractor/FaceFeatureExtractor';\nimport { FaceLandmark68NetBase } from './FaceLandmark68NetBase';\nvar FaceLandmark68Net = /** @class */function (_super) {\n  __extends(FaceLandmark68Net, _super);\n  function FaceLandmark68Net(faceFeatureExtractor) {\n    if (faceFeatureExtractor === void 0) {\n      faceFeatureExtractor = new FaceFeatureExtractor();\n    }\n    return _super.call(this, 'FaceLandmark68Net', faceFeatureExtractor) || this;\n  }\n  FaceLandmark68Net.prototype.getDefaultModelName = function () {\n    return 'face_landmark_68_model';\n  };\n  FaceLandmark68Net.prototype.getClassifierChannelsIn = function () {\n    return 256;\n  };\n  return FaceLandmark68Net;\n}(FaceLandmark68NetBase);\nexport { FaceLandmark68Net };", "map": {"version": 3, "names": ["FaceFeatureExtractor", "FaceLandmark68NetBase", "FaceLandmark68Net", "_super", "__extends", "faceFeatureExtractor", "call", "prototype", "getDefaultModelName", "getClassifierChannelsIn"], "sources": ["../../../src/faceLandmarkNet/FaceLandmark68Net.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,oBAAoB,QAAQ,8CAA8C;AAEnF,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,IAAAC,iBAAA,0BAAAC,MAAA;EAAuCC,SAAA,CAAAF,iBAAA,EAAAC,MAAA;EAErC,SAAAD,kBAAYG,oBAAuE;IAAvE,IAAAA,oBAAA;MAAAA,oBAAA,OAAiDL,oBAAoB,EAAE;IAAA;WACjFG,MAAA,CAAAG,IAAA,OAAM,mBAAmB,EAAED,oBAAoB,CAAC;EAClD;EAEUH,iBAAA,CAAAK,SAAA,CAAAC,mBAAmB,GAA7B;IACE,OAAO,wBAAwB;EACjC,CAAC;EAESN,iBAAA,CAAAK,SAAA,CAAAE,uBAAuB,GAAjC;IACE,OAAO,GAAG;EACZ,CAAC;EACH,OAAAP,iBAAC;AAAD,CAAC,CAbsCD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}