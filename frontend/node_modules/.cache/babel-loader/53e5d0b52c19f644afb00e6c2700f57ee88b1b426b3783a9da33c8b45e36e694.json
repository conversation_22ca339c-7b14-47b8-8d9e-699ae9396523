{"ast": null, "code": "import { disposeUnusedWeightTensors } from '../common';\nimport { loadParamsFactory } from './loadParamsFactory';\nexport function extractParamsFromWeigthMap(weightMap) {\n  var paramMappings = [];\n  var extractDenseBlock4Params = loadParamsFactory(weightMap, paramMappings).extractDenseBlock4Params;\n  var params = {\n    dense0: extractDenseBlock4Params('dense0', true),\n    dense1: extractDenseBlock4Params('dense1'),\n    dense2: extractDenseBlock4Params('dense2'),\n    dense3: extractDenseBlock4Params('dense3')\n  };\n  disposeUnusedWeightTensors(weightMap, paramMappings);\n  return {\n    params: params,\n    paramMappings: paramMappings\n  };\n}", "map": {"version": 3, "names": ["disposeUnusedWeightTensors", "loadParamsFactory", "extractParamsFromWeigthMap", "weightMap", "paramMappings", "extractDenseBlock4Params", "params", "dense0", "dense1", "dense2", "dense3"], "sources": ["../../../src/faceFeatureExtractor/extractParamsFromWeigthMap.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,0BAA0B,QAAsB,WAAW;AACpE,SAASC,iBAAiB,QAAQ,qBAAqB;AAGvD,OAAM,SAAUC,0BAA0BA,CACxCC,SAA4B;EAG5B,IAAMC,aAAa,GAAmB,EAAE;EAGtC,IAAAC,wBAAA,GAAAJ,iBAAA,CAAAE,SAAA,EAAAC,aAAA,EAAAC,wBAAwB;EAG1B,IAAMC,MAAM,GAAG;IACbC,MAAM,EAAEF,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChDG,MAAM,EAAEH,wBAAwB,CAAC,QAAQ,CAAC;IAC1CI,MAAM,EAAEJ,wBAAwB,CAAC,QAAQ,CAAC;IAC1CK,MAAM,EAAEL,wBAAwB,CAAC,QAAQ;GAC1C;EAEDL,0BAA0B,CAACG,SAAS,EAAEC,aAAa,CAAC;EAEpD,OAAO;IAAEE,MAAM,EAAAA,MAAA;IAAEF,aAAa,EAAAA;EAAA,CAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}