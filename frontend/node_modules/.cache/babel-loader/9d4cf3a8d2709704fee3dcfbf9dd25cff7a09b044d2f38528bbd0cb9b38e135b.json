{"ast": null, "code": "import noop from \"../noop.js\";\nimport { point } from \"./basis.js\";\nfunction BasisClosed(context) {\n  this._context = context;\n}\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 1:\n        {\n          this._context.moveTo(this._x2, this._y2);\n          this._context.closePath();\n          break;\n        }\n      case 2:\n        {\n          this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n          this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n          this._context.closePath();\n          break;\n        }\n      case 3:\n        {\n          this.point(this._x2, this._y2);\n          this.point(this._x3, this._y3);\n          this.point(this._x4, this._y4);\n          break;\n        }\n    }\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._x2 = x, this._y2 = y;\n        break;\n      case 1:\n        this._point = 2;\n        this._x3 = x, this._y3 = y;\n        break;\n      case 2:\n        this._point = 3;\n        this._x4 = x, this._y4 = y;\n        this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6);\n        break;\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\nexport default function (context) {\n  return new BasisClosed(context);\n}", "map": {"version": 3, "names": ["noop", "point", "BasisClosed", "context", "_context", "prototype", "areaStart", "areaEnd", "lineStart", "_x0", "_x1", "_x2", "_x3", "_x4", "_y0", "_y1", "_y2", "_y3", "_y4", "NaN", "_point", "lineEnd", "moveTo", "closePath", "lineTo", "x", "y"], "sources": ["/Users/<USER>/Documents/PresencePRO/frontend/node_modules/d3-shape/src/curve/basisClosed.js"], "sourcesContent": ["import noop from \"../noop.js\";\nimport {point} from \"./basis.js\";\n\nfunction BasisClosed(context) {\n  this._context = context;\n}\n\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x2, this._y2);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x2, this._y2);\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x2 = x, this._y2 = y; break;\n      case 1: this._point = 2; this._x3 = x, this._y3 = y; break;\n      case 2: this._point = 3; this._x4 = x, this._y4 = y; this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6); break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisClosed(context);\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,SAAQC,KAAK,QAAO,YAAY;AAEhC,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,WAAW,CAACG,SAAS,GAAG;EACtBC,SAAS,EAAEN,IAAI;EACfO,OAAO,EAAEP,IAAI;EACbQ,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GACpD,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGC,GAAG;IAC1D,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,QAAQ,IAAI,CAACD,MAAM;MACjB,KAAK,CAAC;QAAE;UACN,IAAI,CAAChB,QAAQ,CAACkB,MAAM,CAAC,IAAI,CAACX,GAAG,EAAE,IAAI,CAACK,GAAG,CAAC;UACxC,IAAI,CAACZ,QAAQ,CAACmB,SAAS,CAAC,CAAC;UACzB;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAACnB,QAAQ,CAACkB,MAAM,CAAC,CAAC,IAAI,CAACX,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAACI,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,GAAG,IAAI,CAAC,CAAC;UAClF,IAAI,CAACb,QAAQ,CAACoB,MAAM,CAAC,CAAC,IAAI,CAACZ,GAAG,GAAG,CAAC,GAAG,IAAI,CAACD,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAACM,GAAG,GAAG,CAAC,GAAG,IAAI,CAACD,GAAG,IAAI,CAAC,CAAC;UAClF,IAAI,CAACZ,QAAQ,CAACmB,SAAS,CAAC,CAAC;UACzB;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACU,GAAG,EAAE,IAAI,CAACK,GAAG,CAAC;UAC9B,IAAI,CAACf,KAAK,CAAC,IAAI,CAACW,GAAG,EAAE,IAAI,CAACK,GAAG,CAAC;UAC9B,IAAI,CAAChB,KAAK,CAAC,IAAI,CAACY,GAAG,EAAE,IAAI,CAACK,GAAG,CAAC;UAC9B;QACF;IACF;EACF,CAAC;EACDjB,KAAK,EAAE,SAAAA,CAASwB,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACN,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACT,GAAG,GAAGc,CAAC,EAAE,IAAI,CAACT,GAAG,GAAGU,CAAC;QAAE;MACrD,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;QAAE,IAAI,CAACR,GAAG,GAAGa,CAAC,EAAE,IAAI,CAACR,GAAG,GAAGS,CAAC;QAAE;MACrD,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;QAAE,IAAI,CAACP,GAAG,GAAGY,CAAC,EAAE,IAAI,CAACP,GAAG,GAAGQ,CAAC;QAAE,IAAI,CAACtB,QAAQ,CAACkB,MAAM,CAAC,CAAC,IAAI,CAACb,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,GAAG,GAAGe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAACX,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,GAAG,GAAGW,CAAC,IAAI,CAAC,CAAC;QAAE;MACjJ;QAASzB,KAAK,CAAC,IAAI,EAAEwB,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IACA,IAAI,CAACjB,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGe,CAAC;IACjC,IAAI,CAACX,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGW,CAAC;EACnC;AACF,CAAC;AAED,eAAe,UAASvB,OAAO,EAAE;EAC/B,OAAO,IAAID,WAAW,CAACC,OAAO,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}