{"ast": null, "code": "import { getMediaDimensions } from './getMediaDimensions';\nexport function matchDimensions(input, reference, useMediaDimensions) {\n  if (useMediaDimensions === void 0) {\n    useMediaDimensions = false;\n  }\n  var _a = useMediaDimensions ? getMediaDimensions(reference) : reference,\n    width = _a.width,\n    height = _a.height;\n  input.width = width;\n  input.height = height;\n  return {\n    width: width,\n    height: height\n  };\n}", "map": {"version": 3, "names": ["getMediaDimensions", "matchDimensions", "input", "reference", "useMediaDimensions", "_a", "width", "height"], "sources": ["../../../src/dom/matchDimensions.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,kBAAkB,QAAQ,sBAAsB;AAEzD,OAAM,SAAUC,eAAeA,CAACC,KAAkB,EAAEC,SAAsB,EAAEC,kBAAmC;EAAnC,IAAAA,kBAAA;IAAAA,kBAAA,QAAmC;EAAA;EACvG,IAAAC,EAAA,GAAAD,kBAAA,G,yCAEO;IAFLE,KAAA,GAAAD,EAAA,CAAAC,KAAK;IAAEC,MAAA,GAAAF,EAAA,CAAAE,MAEF;EACbL,KAAK,CAACI,KAAK,GAAGA,KAAK;EACnBJ,KAAK,CAACK,MAAM,GAAGA,MAAM;EACrB,OAAO;IAAED,KAAK,EAAAA,KAAA;IAAEC,MAAM,EAAAA;EAAA,CAAE;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}