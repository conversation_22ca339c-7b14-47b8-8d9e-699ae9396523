{"ast": null, "code": "import { FaceLandmarks } from '../classes/FaceLandmarks';\nimport { FaceLandmarks68 } from '../classes/FaceLandmarks68';\nimport { getContext2dOrThrow } from '../dom/getContext2dOrThrow';\nimport { isWithFaceLandmarks } from '../factories/WithFaceLandmarks';\nimport { drawContour } from './drawContour';\nvar DrawFaceLandmarksOptions = /** @class */function () {\n  function DrawFaceLandmarksOptions(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var _a = options.drawLines,\n      drawLines = _a === void 0 ? true : _a,\n      _b = options.drawPoints,\n      drawPoints = _b === void 0 ? true : _b,\n      lineWidth = options.lineWidth,\n      lineColor = options.lineColor,\n      pointSize = options.pointSize,\n      pointColor = options.pointColor;\n    this.drawLines = drawLines;\n    this.drawPoints = drawPoints;\n    this.lineWidth = lineWidth || 1;\n    this.pointSize = pointSize || 2;\n    this.lineColor = lineColor || 'rgba(0, 255, 255, 1)';\n    this.pointColor = pointColor || 'rgba(255, 0, 255, 1)';\n  }\n  return DrawFaceLandmarksOptions;\n}();\nexport { DrawFaceLandmarksOptions };\nvar DrawFaceLandmarks = /** @class */function () {\n  function DrawFaceLandmarks(faceLandmarks, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.faceLandmarks = faceLandmarks;\n    this.options = new DrawFaceLandmarksOptions(options);\n  }\n  DrawFaceLandmarks.prototype.draw = function (canvasArg) {\n    var ctx = getContext2dOrThrow(canvasArg);\n    var _a = this.options,\n      drawLines = _a.drawLines,\n      drawPoints = _a.drawPoints,\n      lineWidth = _a.lineWidth,\n      lineColor = _a.lineColor,\n      pointSize = _a.pointSize,\n      pointColor = _a.pointColor;\n    if (drawLines && this.faceLandmarks instanceof FaceLandmarks68) {\n      ctx.strokeStyle = lineColor;\n      ctx.lineWidth = lineWidth;\n      drawContour(ctx, this.faceLandmarks.getJawOutline());\n      drawContour(ctx, this.faceLandmarks.getLeftEyeBrow());\n      drawContour(ctx, this.faceLandmarks.getRightEyeBrow());\n      drawContour(ctx, this.faceLandmarks.getNose());\n      drawContour(ctx, this.faceLandmarks.getLeftEye(), true);\n      drawContour(ctx, this.faceLandmarks.getRightEye(), true);\n      drawContour(ctx, this.faceLandmarks.getMouth(), true);\n    }\n    if (drawPoints) {\n      ctx.strokeStyle = pointColor;\n      ctx.fillStyle = pointColor;\n      var drawPoint = function (pt) {\n        ctx.beginPath();\n        ctx.arc(pt.x, pt.y, pointSize, 0, 2 * Math.PI);\n        ctx.fill();\n      };\n      this.faceLandmarks.positions.forEach(drawPoint);\n    }\n  };\n  return DrawFaceLandmarks;\n}();\nexport { DrawFaceLandmarks };\nexport function drawFaceLandmarks(canvasArg, faceLandmarks) {\n  var faceLandmarksArray = Array.isArray(faceLandmarks) ? faceLandmarks : [faceLandmarks];\n  faceLandmarksArray.forEach(function (f) {\n    var landmarks = f instanceof FaceLandmarks ? f : isWithFaceLandmarks(f) ? f.landmarks : undefined;\n    if (!landmarks) {\n      throw new Error('drawFaceLandmarks - expected faceExpressions to be FaceLandmarks | WithFaceLandmarks<WithFaceDetection<{}>> or array thereof');\n    }\n    new DrawFaceLandmarks(landmarks).draw(canvasArg);\n  });\n}", "map": {"version": 3, "names": ["FaceLandmarks", "FaceLandmarks68", "getContext2dOrThrow", "isWithFaceLandmarks", "drawContour", "DrawFaceLandmarksOptions", "options", "_a", "drawLines", "_b", "drawPoints", "lineWidth", "lineColor", "pointSize", "pointColor", "DrawFaceLandmarks", "faceLandmarks", "prototype", "draw", "canvasArg", "ctx", "strokeStyle", "getJawOutline", "getLeftEyeBrow", "getRightEyeBrow", "getNose", "getLeftEye", "getRightEye", "getMouth", "fillStyle", "drawPoint", "pt", "beginPath", "arc", "x", "y", "Math", "PI", "fill", "positions", "for<PERSON>ach", "drawFaceLandmarks", "faceLandmarksArray", "Array", "isArray", "f", "landmarks", "undefined", "Error"], "sources": ["../../../src/draw/DrawFaceLandmarks.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,aAAa,QAAQ,0BAA0B;AACxD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,mBAAmB,QAAQ,4BAA4B;AAEhE,SAASC,mBAAmB,QAA2B,gCAAgC;AACvF,SAASC,WAAW,QAAQ,eAAe;AAW3C,IAAAC,wBAAA;EAQE,SAAAA,yBAAYC,OAAuC;IAAvC,IAAAA,OAAA;MAAAA,OAAA,KAAuC;IAAA;IACzC,IAAAC,EAAA,GAAAD,OAAA,CAAAE,SAAgB;MAAhBA,SAAA,GAAAD,EAAA,qBAAAA,EAAgB;MAAEE,EAAA,GAAAH,OAAA,CAAAI,UAAiB;MAAjBA,UAAA,GAAAD,EAAA,qBAAAA,EAAiB;MAAEE,SAAA,GAAAL,OAAA,CAAAK,SAAS;MAAEC,SAAA,GAAAN,OAAA,CAAAM,SAAS;MAAEC,SAAA,GAAAP,OAAA,CAAAO,SAAS;MAAEC,UAAA,GAAAR,OAAA,CAAAQ,UAAU;IACxF,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAGA,SAAS,IAAI,CAAC;IAC/B,IAAI,CAACD,SAAS,GAAGA,SAAS,IAAI,sBAAsB;IACpD,IAAI,CAACE,UAAU,GAAGA,UAAU,IAAI,sBAAsB;EACxD;EACF,OAAAT,wBAAC;AAAD,CAAC,CAjBD;;AAmBA,IAAAU,iBAAA;EAIE,SAAAA,kBACEC,aAA4B,EAC5BV,OAAuC;IAAvC,IAAAA,OAAA;MAAAA,OAAA,KAAuC;IAAA;IAEvC,IAAI,CAACU,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACV,OAAO,GAAG,IAAID,wBAAwB,CAACC,OAAO,CAAC;EACtD;EAEAS,iBAAA,CAAAE,SAAA,CAAAC,IAAI,GAAJ,UAAKC,SAAgE;IACnE,IAAMC,GAAG,GAAGlB,mBAAmB,CAACiB,SAAS,CAAC;IAEpC,IAAAZ,EAAA,QAAAD,OAAqF;MAAnFE,SAAA,GAAAD,EAAA,CAAAC,SAAS;MAAEE,UAAA,GAAAH,EAAA,CAAAG,UAAU;MAAEC,SAAA,GAAAJ,EAAA,CAAAI,SAAS;MAAEC,SAAA,GAAAL,EAAA,CAAAK,SAAS;MAAEC,SAAA,GAAAN,EAAA,CAAAM,SAAS;MAAEC,UAAA,GAAAP,EAAA,CAAAO,UAA2B;IAE3F,IAAIN,SAAS,IAAI,IAAI,CAACQ,aAAa,YAAYf,eAAe,EAAE;MAC9DmB,GAAG,CAACC,WAAW,GAAGT,SAAS;MAC3BQ,GAAG,CAACT,SAAS,GAAGA,SAAS;MACzBP,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACM,aAAa,EAAE,CAAC;MACpDlB,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACO,cAAc,EAAE,CAAC;MACrDnB,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACQ,eAAe,EAAE,CAAC;MACtDpB,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACS,OAAO,EAAE,CAAC;MAC9CrB,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACU,UAAU,EAAE,EAAE,IAAI,CAAC;MACvDtB,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACW,WAAW,EAAE,EAAE,IAAI,CAAC;MACxDvB,WAAW,CAACgB,GAAG,EAAE,IAAI,CAACJ,aAAa,CAACY,QAAQ,EAAE,EAAE,IAAI,CAAC;;IAGvD,IAAIlB,UAAU,EAAE;MACdU,GAAG,CAACC,WAAW,GAAGP,UAAU;MAC5BM,GAAG,CAACS,SAAS,GAAGf,UAAU;MAE1B,IAAMgB,SAAS,GAAG,SAAAA,CAACC,EAAU;QAC3BX,GAAG,CAACY,SAAS,EAAE;QACfZ,GAAG,CAACa,GAAG,CAACF,EAAE,CAACG,CAAC,EAAEH,EAAE,CAACI,CAAC,EAAEtB,SAAS,EAAE,CAAC,EAAE,CAAC,GAAGuB,IAAI,CAACC,EAAE,CAAC;QAC9CjB,GAAG,CAACkB,IAAI,EAAE;MACZ,CAAC;MACD,IAAI,CAACtB,aAAa,CAACuB,SAAS,CAACC,OAAO,CAACV,SAAS,CAAC;;EAEnD,CAAC;EACH,OAAAf,iBAAC;AAAD,CAAC,CAzCD;;AA6CA,OAAM,SAAU0B,iBAAiBA,CAC/BtB,SAAqC,EACrCH,aAAqE;EAErE,IAAM0B,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAAC5B,aAAa,CAAC,GAAGA,aAAa,GAAG,CAACA,aAAa,CAAC;EACzF0B,kBAAkB,CAACF,OAAO,CAAC,UAAAK,CAAC;IAC1B,IAAMC,SAAS,GAAGD,CAAC,YAAY7C,aAAa,GACxC6C,CAAC,GACA1C,mBAAmB,CAAC0C,CAAC,CAAC,GAAGA,CAAC,CAACC,SAAS,GAAGC,SAAU;IACtD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIE,KAAK,CAAC,8HAA8H,CAAC;;IAGjJ,IAAIjC,iBAAiB,CAAC+B,SAAS,CAAC,CAAC5B,IAAI,CAACC,SAAS,CAAC;EAClD,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}