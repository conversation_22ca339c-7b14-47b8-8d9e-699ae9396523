{"ast": null, "code": "import { TinyYolov2 } from './TinyYolov2';\nexport * from './TinyYolov2Options';\nexport * from './config';\nexport { TinyYolov2 };\nexport function createTinyYolov2(weights, withSeparableConvs) {\n  if (withSeparableConvs === void 0) {\n    withSeparableConvs = true;\n  }\n  var net = new TinyYolov2(withSeparableConvs);\n  net.extractWeights(weights);\n  return net;\n}", "map": {"version": 3, "names": ["TinyYolov2", "createTinyYolov2", "weights", "withSeparableConvs", "net", "extractWeights"], "sources": ["../../../src/tinyYolov2/index.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AAEzC,cAAc,qBAAqB;AAEnC,cAAc,UAAU;AAGxB,SAASA,UAAU;AAEnB,OAAM,SAAUC,gBAAgBA,CAACC,OAAqB,EAAEC,kBAAkC;EAAlC,IAAAA,kBAAA;IAAAA,kBAAA,OAAkC;EAAA;EACxF,IAAMC,GAAG,GAAG,IAAIJ,UAAU,CAACG,kBAAkB,CAAC;EAC9CC,GAAG,CAACC,cAAc,CAACH,OAAO,CAAC;EAC3B,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}