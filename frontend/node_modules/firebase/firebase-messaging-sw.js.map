{"version": 3, "file": "firebase-messaging-sw.js", "sources": ["../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../../node_modules/idb/build/index.js", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../messaging/src/util/constants.ts", "../messaging/src/interfaces/internal-message-payload.ts", "../messaging/src/helpers/array-base64-translator.ts", "../messaging/src/helpers/migrate-old-database.ts", "../messaging/src/internals/idb-manager.ts", "../messaging/src/util/errors.ts", "../messaging/src/internals/requests.ts", "../messaging/src/internals/token-manager.ts", "../messaging/src/helpers/logToFirelog.ts", "../messaging/src/listeners/sw-listeners.ts", "../messaging/src/helpers/externalizePayload.ts", "../messaging/src/helpers/is-console-message.ts", "../messaging/src/helpers/sleep.ts", "../messaging/src/helpers/extract-app-config.ts", "../messaging/src/messaging-service.ts", "../messaging/src/helpers/register.ts", "../messaging/src/api/isSupported.ts", "../util/src/environment.ts", "../messaging/src/api.ts", "../messaging/src/api/onBackgroundMessage.ts", "../messaging/src/api/setDeliveryMetricsExportedToBigQueryEnabled.ts", "../messaging/src/index.sw.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nexport const DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\n\nexport const DEFAULT_VAPID_KEY =\n  'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\n\nexport const ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n\n/** Key of FCM Payload in Notification's data field. */\nexport const FCM_MSG = 'FCM_MSG';\n\nexport const CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nexport const CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nexport const CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nexport const CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nexport const TAG = 'FirebaseMessaging: ';\nexport const MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST = 1000;\nexport const MAX_RETRIES = 3;\nexport const LOG_INTERVAL_IN_MS = 86400000; //24 hour\nexport const DEFAULT_BACKOFF_TIME_MS = 5000;\nexport const DEFAULT_REGISTRATION_TIMEOUT = 10000;\n\n// FCM log source name registered at Firelog: 'FCM_CLIENT_EVENT_LOGGING'. It uniquely identifies\n// FCM's logging configuration.\nexport const FCM_LOG_SOURCE = 1249;\n\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nexport const SDK_PLATFORM_WEB = 3;\nexport const EVENT_MESSAGE_DELIVERED = 1;\n\nexport enum MessageType {\n  DATA_MESSAGE = 1,\n  DISPLAY_NOTIFICATION = 3\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ANALYTICS_ENABLED,\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\n\nexport interface MessagePayloadInternal {\n  notification?: NotificationPayloadInternal;\n  data?: unknown;\n  fcmOptions?: FcmOptionsInternal;\n  messageType?: MessageType;\n  isFirebaseMessaging?: boolean;\n  from: string;\n  fcmMessageId: string;\n  productId: number;\n  // eslint-disable-next-line camelcase\n  collapse_key: string;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Notification/actions\ninterface NotificationAction {\n  action: string;\n  icon?: string;\n  title: string;\n}\n\n/**\n * This interface defines experimental properties of NotificationOptions, that are not part of\n * the interface in the generated DOM types at https://github.com/microsoft/TypeScript-DOM-lib-generator/blob/179bdd84a944933a3103f29c2274c9f5a857b693/baselines/dom.generated.d.ts#L1012\n * https://developer.mozilla.org/en-US/docs/Web/API/Notification\n */\ninterface NotificationOptionsExperimental extends NotificationOptions {\n  readonly maxActions?: number;\n  readonly actions?: NotificationAction[];\n  readonly image?: string;\n  readonly renotify?: boolean;\n  readonly timestamp?: EpochTimeStamp;\n  readonly vibrate?: VibratePattern;\n}\n\nexport interface NotificationPayloadInternal\n  extends NotificationOptionsExperimental {\n  title: string;\n  // Supported in the Legacy Send API.\n  // See:https://firebase.google.com/docs/cloud-messaging/xmpp-server-ref.\n  // eslint-disable-next-line camelcase\n  click_action?: string;\n  icon?: string;\n}\n\n// Defined in\n// https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#webpushfcmoptions. Note\n// that the keys are sent to the clients in snake cases which we need to convert to camel so it can\n// be exposed as a type to match the Firebase API convention.\nexport interface FcmOptionsInternal {\n  link?: string;\n\n  // eslint-disable-next-line camelcase\n  analytics_label?: string;\n}\n\nexport enum MessageType {\n  PUSH_RECEIVED = 'push-received',\n  NOTIFICATION_CLICKED = 'notification-clicked'\n}\n\n/** Additional data of a message sent from the FN Console. */\nexport interface ConsoleMessageData {\n  [CONSOLE_CAMPAIGN_ID]: string;\n  [CONSOLE_CAMPAIGN_TIME]: string;\n  [CONSOLE_CAMPAIGN_NAME]?: string;\n  [CONSOLE_CAMPAIGN_ANALYTICS_ENABLED]?: '1';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function arrayToBase64(array: Uint8Array | ArrayBuffer): string {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nexport function base64ToArray(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding)\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteDB, openDB } from 'idb';\n\nimport { TokenDetails } from '../interfaces/token-details';\nimport { arrayToBase64 } from './array-base64-translator';\n\n// https://github.com/firebase/firebase-js-sdk/blob/7857c212f944a2a9eb421fd4cb7370181bc034b5/packages/messaging/src/interfaces/token-details.ts\nexport interface V2TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: string | Uint8Array;\n  subscription: PushSubscription;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  createTime?: number;\n  endpoint?: string;\n  auth?: string;\n  p256dh?: string;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/6b5b15ce4ea3df5df5df8a8b33a4e41e249c7715/packages/messaging/src/interfaces/token-details.ts\nexport interface V3TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  endpoint: string;\n  auth: ArrayBuffer;\n  p256dh: ArrayBuffer;\n  createTime: number;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/9567dba664732f681fa7fe60f5b7032bb1daf4c9/packages/messaging/src/interfaces/token-details.ts\nexport interface V4TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  endpoint: string;\n  auth: ArrayBufferLike;\n  p256dh: ArrayBufferLike;\n  createTime: number;\n}\n\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\n\nexport async function migrateOldDatabase(\n  senderId: string\n): Promise<TokenDetails | null> {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await (\n      indexedDB as {\n        databases(): Promise<Array<{ name: string; version: number }>>;\n      }\n    ).databases();\n    const dbNames = databases.map(db => db.name);\n\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n\n  let tokenDetails: TokenDetails | null = null;\n\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n\n      if (oldVersion === 2) {\n        const oldDetails = value as V2TokenDetails;\n\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime ?? Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey:\n              typeof oldDetails.vapidKey === 'string'\n                ? oldDetails.vapidKey\n                : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value as V3TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value as V4TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\n\nfunction checkTokenDetails(\n  tokenDetails: TokenDetails | null\n): tokenDetails is TokenDetails {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const { subscriptionOptions } = tokenDetails;\n  return (\n    typeof tokenDetails.createTime === 'number' &&\n    tokenDetails.createTime > 0 &&\n    typeof tokenDetails.token === 'string' &&\n    tokenDetails.token.length > 0 &&\n    typeof subscriptionOptions.auth === 'string' &&\n    subscriptionOptions.auth.length > 0 &&\n    typeof subscriptionOptions.p256dh === 'string' &&\n    subscriptionOptions.p256dh.length > 0 &&\n    typeof subscriptionOptions.endpoint === 'string' &&\n    subscriptionOptions.endpoint.length > 0 &&\n    typeof subscriptionOptions.swScope === 'string' &&\n    subscriptionOptions.swScope.length > 0 &&\n    typeof subscriptionOptions.vapidKey === 'string' &&\n    subscriptionOptions.vapidKey.length > 0\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, deleteDB, openDB } from 'idb';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { TokenDetails } from '../interfaces/token-details';\nimport { migrateOldDatabase } from '../helpers/migrate-old-database';\n\n// Exported for tests.\nexport const DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\n\ninterface MessagingDB extends DBSchema {\n  'firebase-messaging-store': {\n    key: string;\n    value: TokenDetails;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<MessagingDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<MessagingDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function dbGet(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<TokenDetails | undefined> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = (await db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key)) as TokenDetails;\n\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(\n      firebaseDependencies.appConfig.senderId\n    );\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function dbSet(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<TokenDetails> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function dbRemove(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<void> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/** Deletes the DB. Useful for tests. */\nexport async function dbDelete(): Promise<void> {\n  if (dbPromise) {\n    (await dbPromise).close();\n    await deleteDB(DATABASE_NAME);\n    dbPromise = null;\n  }\n}\n\nfunction getKey({ appConfig }: FirebaseInternalDependencies): string {\n  return appConfig.appId;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  AVAILABLE_IN_WINDOW = 'only-available-in-window',\n  AVAILABLE_IN_SW = 'only-available-in-sw',\n  PERMISSION_DEFAULT = 'permission-default',\n  PERMISSION_BLOCKED = 'permission-blocked',\n  UNSUPPORTED_BROWSER = 'unsupported-browser',\n  INDEXED_DB_UNSUPPORTED = 'indexed-db-unsupported',\n  FAILED_DEFAULT_REGISTRATION = 'failed-service-worker-registration',\n  TOKEN_SUBSCRIBE_FAILED = 'token-subscribe-failed',\n  TOKEN_SUBSCRIBE_NO_TOKEN = 'token-subscribe-no-token',\n  TOKEN_UNSUBSCRIBE_FAILED = 'token-unsubscribe-failed',\n  TOKEN_UPDATE_FAILED = 'token-update-failed',\n  TOKEN_UPDATE_NO_TOKEN = 'token-update-no-token',\n  INVALID_BG_HANDLER = 'invalid-bg-handler',\n  USE_SW_AFTER_GET_TOKEN = 'use-sw-after-get-token',\n  INVALID_SW_REGISTRATION = 'invalid-sw-registration',\n  USE_VAPID_KEY_AFTER_GET_TOKEN = 'use-vapid-key-after-get-token',\n  INVALID_VAPID_KEY = 'invalid-vapid-key'\n}\n\nexport const ERROR_MAP: ErrorMap<ErrorCode> = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.AVAILABLE_IN_WINDOW]:\n    'This method is available in a Window context.',\n  [ErrorCode.AVAILABLE_IN_SW]:\n    'This method is available in a service worker context.',\n  [ErrorCode.PERMISSION_DEFAULT]:\n    'The notification permission was not granted and dismissed instead.',\n  [ErrorCode.PERMISSION_BLOCKED]:\n    'The notification permission was not granted and blocked instead.',\n  [ErrorCode.UNSUPPORTED_BROWSER]:\n    \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [ErrorCode.INDEXED_DB_UNSUPPORTED]:\n    \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]:\n    'We are unable to register the default service worker. {$browserErrorMessage}',\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]:\n    'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN]:\n    'FCM returned no token when subscribing the user to push.',\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]:\n    'A problem occurred while unsubscribing the ' +\n    'user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_FAILED]:\n    'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_NO_TOKEN]:\n    'FCM returned no token when updating the user to push.',\n  [ErrorCode.USE_SW_AFTER_GET_TOKEN]:\n    'The useServiceWorker() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your service worker is used.',\n  [ErrorCode.INVALID_SW_REGISTRATION]:\n    'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [ErrorCode.INVALID_BG_HANDLER]:\n    'The input to setBackgroundMessageHandler() must be a function.',\n  [ErrorCode.INVALID_VAPID_KEY]: 'The public VAPID key must be a string.',\n  [ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN]:\n    'The usePublicVapidKey() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your VAPID key is used.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]: { browserErrorMessage: string };\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UPDATE_FAILED]: { errorInfo: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'messaging',\n  'Messaging',\n  ERROR_MAP\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, ENDPOINT } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\n\nexport interface ApiResponse {\n  token?: string;\n  error?: { message: string };\n}\n\nexport interface ApiRequestBody {\n  web: {\n    endpoint: string;\n    p256dh: string;\n    auth: string;\n    applicationPubKey?: string;\n  };\n}\n\nexport async function requestGetToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      getEndpoint(firebaseDependencies.appConfig),\n      subscribeOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestUpdateToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions!);\n\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`,\n      updateOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestDeleteToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  token: string\n): Promise<void> {\n  const headers = await getHeaders(firebaseDependencies);\n\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${token}`,\n      unsubscribeOptions\n    );\n    const responseData: ApiResponse = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n}\n\nfunction getEndpoint({ projectId }: AppConfig): string {\n  return `${ENDPOINT}/projects/${projectId!}/registrations`;\n}\n\nasync function getHeaders({\n  appConfig,\n  installations\n}: FirebaseInternalDependencies): Promise<Headers> {\n  const authToken = await installations.getToken();\n\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey!,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\n\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}: SubscriptionOptions): ApiRequestBody {\n  const body: ApiRequestBody = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n\n  return body;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\nimport {\n  arrayToBase64,\n  base64ToArray\n} from '../helpers/array-base64-translator';\nimport { dbGet, dbRemove, dbSet } from './idb-manager';\nimport {\n  requestDeleteToken,\n  requestGetToken,\n  requestUpdateToken\n} from './requests';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { MessagingService } from '../messaging-service';\n\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport async function getTokenInternal(\n  messaging: MessagingService\n): Promise<string> {\n  const pushSubscription = await getPushSubscription(\n    messaging.swRegistration!,\n    messaging.vapidKey!\n  );\n\n  const subscriptionOptions: SubscriptionOptions = {\n    vapidKey: messaging.vapidKey!,\n    swScope: messaging.swRegistration!.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')!),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh')!)\n  };\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (\n    !isTokenValid(tokenDetails.subscriptionOptions!, subscriptionOptions)\n  ) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(\n        messaging.firebaseDependencies!,\n        tokenDetails.token\n      );\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n\n    return getNewToken(messaging.firebaseDependencies!, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nexport async function deleteTokenInternal(\n  messaging: MessagingService\n): Promise<boolean> {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(\n      messaging.firebaseDependencies,\n      tokenDetails.token\n    );\n    await dbRemove(messaging.firebaseDependencies);\n  }\n\n  // Unsubscribe from the push subscription.\n  const pushSubscription =\n    await messaging.swRegistration!.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n\n  // If there's no SW, consider it a success.\n  return true;\n}\n\nasync function updateToken(\n  messaging: MessagingService,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  try {\n    const updatedToken = await requestUpdateToken(\n      messaging.firebaseDependencies,\n      tokenDetails\n    );\n\n    const updatedTokenDetails: TokenDetails = {\n      ...tokenDetails,\n      token: updatedToken,\n      createTime: Date.now()\n    };\n\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\n\nasync function getNewToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const token = await requestGetToken(\n    firebaseDependencies,\n    subscriptionOptions\n  );\n  const tokenDetails: TokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(\n  swRegistration: ServiceWorkerRegistration,\n  vapidKey: string\n): Promise<PushSubscription> {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(\n  dbOptions: SubscriptionOptions,\n  currentOptions: SubscriptionOptions\n): boolean {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_BACKOFF_TIME_MS,\n  EVENT_MESSAGE_DELIVERED,\n  FCM_LOG_SOURCE,\n  LOG_INTERVAL_IN_MS,\n  MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST,\n  MAX_RETRIES,\n  MessageType,\n  SDK_PLATFORM_WEB\n} from '../util/constants';\nimport {\n  FcmEvent,\n  LogEvent,\n  LogRequest,\n  LogResponse,\n  ComplianceData\n} from '../interfaces/logging-types';\n\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\nimport { MessagingService } from '../messaging-service';\n\nconst LOG_ENDPOINT = 'https://play.google.com/log?format=json_proto3';\n\nconst FCM_TRANSPORT_KEY = _mergeStrings(\n  'AzSCbw63g1R0nCw85jG8',\n  'Iaya3yLKwmgvh7cF0q4'\n);\n\nexport function startLoggingService(messaging: MessagingService): void {\n  if (!messaging.isLogServiceStarted) {\n    _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    messaging.isLogServiceStarted = true;\n  }\n}\n\n/**\n *\n * @param messaging the messaging instance.\n * @param offsetInMs this method execute after `offsetInMs` elapsed .\n */\nexport function _processQueue(\n  messaging: MessagingService,\n  offsetInMs: number\n): void {\n  setTimeout(async () => {\n    if (!messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      // flush events and terminate logging service\n      messaging.logEvents = [];\n      messaging.isLogServiceStarted = false;\n\n      return;\n    }\n\n    if (!messaging.logEvents.length) {\n      return _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    }\n\n    await _dispatchLogEvents(messaging);\n  }, offsetInMs);\n}\n\nexport async function _dispatchLogEvents(\n  messaging: MessagingService\n): Promise<void> {\n  for (\n    let i = 0, n = messaging.logEvents.length;\n    i < n;\n    i += MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST\n  ) {\n    const logRequest = _createLogRequest(\n      messaging.logEvents.slice(i, i + MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST)\n    );\n\n    let retryCount = 0,\n      response = {} as Response;\n\n    do {\n      try {\n        response = await fetch(\n          LOG_ENDPOINT.concat('&key=', FCM_TRANSPORT_KEY),\n          {\n            method: 'POST',\n            body: JSON.stringify(logRequest)\n          }\n        );\n\n        // don't retry on 200s or non retriable errors\n        if (response.ok || (!response.ok && !isRetriableError(response))) {\n          break;\n        }\n\n        if (!response.ok && isRetriableError(response)) {\n          // rethrow to retry with quota\n          throw new Error(\n            'a retriable Non-200 code is returned in fetch to Firelog endpoint. Retry'\n          );\n        }\n      } catch (error) {\n        const isLastAttempt = retryCount === MAX_RETRIES;\n        if (isLastAttempt) {\n          // existing the do-while interactive retry logic because retry quota has reached.\n          break;\n        }\n      }\n\n      let delayInMs: number;\n      try {\n        delayInMs = Number(\n          ((await response.json()) as LogResponse).nextRequestWaitMillis\n        );\n      } catch (e) {\n        delayInMs = DEFAULT_BACKOFF_TIME_MS;\n      }\n\n      await new Promise(resolve => setTimeout(resolve, delayInMs));\n\n      retryCount++;\n    } while (retryCount < MAX_RETRIES);\n  }\n\n  messaging.logEvents = [];\n  // schedule for next logging\n  _processQueue(messaging, LOG_INTERVAL_IN_MS);\n}\n\nfunction isRetriableError(response: Response): boolean {\n  const httpStatus = response.status;\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\nexport async function stageLog(\n  messaging: MessagingService,\n  internalPayload: MessagePayloadInternal\n): Promise<void> {\n  const fcmEvent = createFcmEvent(\n    internalPayload,\n    await messaging.firebaseDependencies.installations.getId()\n  );\n\n  createAndEnqueueLogEvent(messaging, fcmEvent, internalPayload.productId);\n}\n\nfunction createFcmEvent(\n  internalPayload: MessagePayloadInternal,\n  fid: string\n): FcmEvent {\n  const fcmEvent = {} as FcmEvent;\n\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n\n  fcmEvent.instance_id = fid;\n\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType.DATA_MESSAGE.toString();\n  }\n\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n\n  if (!!internalPayload.fcmOptions?.analytics_label) {\n    fcmEvent.analytics_label = internalPayload.fcmOptions?.analytics_label;\n  }\n\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\n\nfunction createAndEnqueueLogEvent(\n  messaging: MessagingService,\n  fcmEvent: FcmEvent,\n  productId: number\n): void {\n  const logEvent = {} as LogEvent;\n\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify({\n    messaging_client_event: fcmEvent\n  });\n\n  if (!!productId) {\n    logEvent.compliance_data = buildComplianceData(productId);\n  }\n  // eslint-disable-next-line camelcase\n\n  messaging.logEvents.push(logEvent);\n}\n\nfunction buildComplianceData(productId: number): ComplianceData {\n  const complianceData: ComplianceData = {\n    privacy_context: {\n      prequest: {\n        origin_associated_product_id: productId\n      }\n    }\n  };\n\n  return complianceData;\n}\n\nexport function _createLogRequest(logEventQueue: LogEvent[]): LogRequest {\n  const logRequest = {} as LogRequest;\n\n  /* eslint-disable camelcase */\n  logRequest.log_source = FCM_LOG_SOURCE.toString();\n  logRequest.log_event = logEventQueue;\n  /* eslint-enable camelcase */\n\n  return logRequest;\n}\n\nexport function _mergeStrings(s1: string, s2: string): string {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, FCM_MSG } from '../util/constants';\nimport {\n  MessagePayloadInternal,\n  MessageType,\n  NotificationPayloadInternal\n} from '../interfaces/internal-message-payload';\nimport {\n  NotificationEvent,\n  PushEvent,\n  PushSubscriptionChangeEvent,\n  ServiceWorkerGlobalScope,\n  WindowClient\n} from '../util/sw-types';\nimport {\n  deleteTokenInternal,\n  getTokenInternal\n} from '../internals/token-manager';\n\nimport { MessagingService } from '../messaging-service';\nimport { dbGet } from '../internals/idb-manager';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { sleep } from '../helpers/sleep';\nimport { stageLog } from '../helpers/logToFirelog';\n\n// maxActions is an experimental property and not part of the official\n// TypeScript interface\n// https://developer.mozilla.org/en-US/docs/Web/API/Notification/maxActions\ninterface NotificationExperimental extends Notification {\n  maxActions?: number;\n}\n\n// Let TS know that this is a service worker\ndeclare const self: ServiceWorkerGlobalScope;\n\nexport async function onSubChange(\n  event: PushSubscriptionChangeEvent,\n  messaging: MessagingService\n): Promise<void> {\n  const { newSubscription } = event;\n  if (!newSubscription) {\n    // Subscription revoked, delete token\n    await deleteTokenInternal(messaging);\n    return;\n  }\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  await deleteTokenInternal(messaging);\n\n  messaging.vapidKey =\n    tokenDetails?.subscriptionOptions?.vapidKey ?? DEFAULT_VAPID_KEY;\n  await getTokenInternal(messaging);\n}\n\nexport async function onPush(\n  event: PushEvent,\n  messaging: MessagingService\n): Promise<void> {\n  const internalPayload = getMessagePayloadInternal(event);\n  if (!internalPayload) {\n    // Failed to get parsed MessagePayload from the PushEvent. Skip handling the push.\n    return;\n  }\n\n  // log to Firelog with user consent\n  if (messaging.deliveryMetricsExportedToBigQueryEnabled) {\n    await stageLog(messaging, internalPayload);\n  }\n\n  // foreground handling: eventually passed to onMessage hook\n  const clientList = await getClientList();\n  if (hasVisibleClients(clientList)) {\n    return sendMessagePayloadInternalToWindows(clientList, internalPayload);\n  }\n\n  // background handling: display if possible and pass to onBackgroundMessage hook\n  if (!!internalPayload.notification) {\n    await showNotification(wrapInternalPayload(internalPayload));\n  }\n\n  if (!messaging) {\n    return;\n  }\n\n  if (!!messaging.onBackgroundMessageHandler) {\n    const payload = externalizePayload(internalPayload);\n\n    if (typeof messaging.onBackgroundMessageHandler === 'function') {\n      await messaging.onBackgroundMessageHandler(payload);\n    } else {\n      messaging.onBackgroundMessageHandler.next(payload);\n    }\n  }\n}\n\nexport async function onNotificationClick(\n  event: NotificationEvent\n): Promise<void> {\n  const internalPayload: MessagePayloadInternal =\n    event.notification?.data?.[FCM_MSG];\n\n  if (!internalPayload) {\n    return;\n  } else if (event.action) {\n    // User clicked on an action button. This will allow developers to act on action button clicks\n    // by using a custom onNotificationClick listener that they define.\n    return;\n  }\n\n  // Prevent other listeners from receiving the event\n  event.stopImmediatePropagation();\n  event.notification.close();\n\n  // Note clicking on a notification with no link set will focus the Chrome's current tab.\n  const link = getLink(internalPayload);\n  if (!link) {\n    return;\n  }\n\n  // FM should only open/focus links from app's origin.\n  const url = new URL(link, self.location.href);\n  const originUrl = new URL(self.location.origin);\n\n  if (url.host !== originUrl.host) {\n    return;\n  }\n\n  let client = await getWindowClient(url);\n\n  if (!client) {\n    client = await self.clients.openWindow(link);\n\n    // Wait three seconds for the client to initialize and set up the message handler so that it\n    // can receive the message.\n    await sleep(3000);\n  } else {\n    client = await client.focus();\n  }\n\n  if (!client) {\n    // Window Client will not be returned if it's for a third party origin.\n    return;\n  }\n\n  internalPayload.messageType = MessageType.NOTIFICATION_CLICKED;\n  internalPayload.isFirebaseMessaging = true;\n  return client.postMessage(internalPayload);\n}\n\nfunction wrapInternalPayload(\n  internalPayload: MessagePayloadInternal\n): NotificationPayloadInternal {\n  const wrappedInternalPayload: NotificationPayloadInternal = {\n    ...(internalPayload.notification as unknown as NotificationPayloadInternal)\n  };\n\n  // Put the message payload under FCM_MSG name so we can identify the notification as being an FCM\n  // notification vs a notification from somewhere else (i.e. normal web push or developer generated\n  // notification).\n  wrappedInternalPayload.data = {\n    [FCM_MSG]: internalPayload\n  };\n\n  return wrappedInternalPayload;\n}\n\nfunction getMessagePayloadInternal({\n  data\n}: PushEvent): MessagePayloadInternal | null {\n  if (!data) {\n    return null;\n  }\n\n  try {\n    return data.json();\n  } catch (err) {\n    // Not JSON so not an FCM message.\n    return null;\n  }\n}\n\n/**\n * @param url The URL to look for when focusing a client.\n * @return Returns an existing window client or a newly opened WindowClient.\n */\nasync function getWindowClient(url: URL): Promise<WindowClient | null> {\n  const clientList = await getClientList();\n\n  for (const client of clientList) {\n    const clientUrl = new URL(client.url, self.location.href);\n\n    if (url.host === clientUrl.host) {\n      return client;\n    }\n  }\n\n  return null;\n}\n\n/**\n * @returns If there is currently a visible WindowClient, this method will resolve to true,\n * otherwise false.\n */\nfunction hasVisibleClients(clientList: WindowClient[]): boolean {\n  return clientList.some(\n    client =>\n      client.visibilityState === 'visible' &&\n      // Ignore chrome-extension clients as that matches the background pages of extensions, which\n      // are always considered visible for some reason.\n      !client.url.startsWith('chrome-extension://')\n  );\n}\n\nfunction sendMessagePayloadInternalToWindows(\n  clientList: WindowClient[],\n  internalPayload: MessagePayloadInternal\n): void {\n  internalPayload.isFirebaseMessaging = true;\n  internalPayload.messageType = MessageType.PUSH_RECEIVED;\n\n  for (const client of clientList) {\n    client.postMessage(internalPayload);\n  }\n}\n\nfunction getClientList(): Promise<WindowClient[]> {\n  return self.clients.matchAll({\n    type: 'window',\n    includeUncontrolled: true\n    // TS doesn't know that \"type: 'window'\" means it'll return WindowClient[]\n  }) as Promise<WindowClient[]>;\n}\n\nfunction showNotification(\n  notificationPayloadInternal: NotificationPayloadInternal\n): Promise<void> {\n  // Note: Firefox does not support the maxActions property.\n  // https://developer.mozilla.org/en-US/docs/Web/API/notification/maxActions\n  const { actions } = notificationPayloadInternal;\n  const { maxActions } = Notification as unknown as NotificationExperimental;\n  if (actions && maxActions && actions.length > maxActions) {\n    console.warn(\n      `This browser only supports ${maxActions} actions. The remaining actions will not be displayed.`\n    );\n  }\n\n  return self.registration.showNotification(\n    /* title= */ notificationPayloadInternal.title ?? '',\n    notificationPayloadInternal\n  );\n}\n\nfunction getLink(payload: MessagePayloadInternal): string | null {\n  // eslint-disable-next-line camelcase\n  const link = payload.fcmOptions?.link ?? payload.notification?.click_action;\n  if (link) {\n    return link;\n  }\n\n  if (isConsoleMessage(payload.data)) {\n    // Notification created in the Firebase Console. Redirect to origin.\n    return self.location.origin;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MessagePayload } from '../interfaces/public-types';\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\n\nexport function externalizePayload(\n  internalPayload: MessagePayloadInternal\n): MessagePayload {\n  const payload: MessagePayload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  } as MessagePayload;\n\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n\n  return payload;\n}\n\nfunction propagateNotificationPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n\n  payload.notification = {};\n\n  const title = messagePayloadInternal.notification!.title;\n  if (!!title) {\n    payload.notification!.title = title;\n  }\n\n  const body = messagePayloadInternal.notification!.body;\n  if (!!body) {\n    payload.notification!.body = body;\n  }\n\n  const image = messagePayloadInternal.notification!.image;\n  if (!!image) {\n    payload.notification!.image = image;\n  }\n\n  const icon = messagePayloadInternal.notification!.icon;\n  if (!!icon) {\n    payload.notification!.icon = icon;\n  }\n}\n\nfunction propagateDataPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n\n  payload.data = messagePayloadInternal.data as { [key: string]: string };\n}\n\nfunction propagateFcmOptions(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (\n    !messagePayloadInternal.fcmOptions &&\n    !messagePayloadInternal.notification?.click_action\n  ) {\n    return;\n  }\n\n  payload.fcmOptions = {};\n\n  const link =\n    messagePayloadInternal.fcmOptions?.link ??\n    messagePayloadInternal.notification?.click_action;\n\n  if (!!link) {\n    payload.fcmOptions!.link = link;\n  }\n\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = messagePayloadInternal.fcmOptions?.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions!.analyticsLabel = analyticsLabel;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSOLE_CAMPAIGN_ID } from '../util/constants';\nimport { ConsoleMessageData } from '../interfaces/internal-message-payload';\n\nexport function isConsoleMessage(data: unknown): data is ConsoleMessageData {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseError } from '@firebase/util';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: ReadonlyArray<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId',\n    'messagingSenderId'\n  ];\n\n  const { options } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: options.projectId!,\n    apiKey: options.apiKey!,\n    appId: options.appId!,\n    senderId: options.messagingSenderId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { MessagePayload, NextFn, Observer } from './interfaces/public-types';\n\nimport { FirebaseAnalyticsInternalName } from '@firebase/analytics-interop-types';\nimport { FirebaseInternalDependencies } from './interfaces/internal-dependencies';\nimport { LogEvent } from './interfaces/logging-types';\nimport { Provider } from '@firebase/component';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { extractAppConfig } from './helpers/extract-app-config';\n\nexport class MessagingService implements _FirebaseService {\n  readonly app!: FirebaseApp;\n  readonly firebaseDependencies!: FirebaseInternalDependencies;\n\n  swRegistration?: ServiceWorkerRegistration;\n  vapidKey?: string;\n  // logging is only done with end user consent. Default to false.\n  deliveryMetricsExportedToBigQueryEnabled: boolean = false;\n\n  onBackgroundMessageHandler:\n    | NextFn<MessagePayload>\n    | Observer<MessagePayload>\n    | null = null;\n\n  onMessageHandler: NextFn<MessagePayload> | Observer<MessagePayload> | null =\n    null;\n\n  logEvents: LogEvent[] = [];\n  isLogServiceStarted: boolean = false;\n\n  constructor(\n    app: FirebaseApp,\n    installations: _FirebaseInstallationsInternal,\n    analyticsProvider: Provider<FirebaseAnalyticsInternalName>\n  ) {\n    const appConfig = extractAppConfig(app);\n\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  onNotificationClick,\n  onPush,\n  onSubChange\n} from '../listeners/sw-listeners';\n\nimport { GetTokenOptions } from '../interfaces/public-types';\nimport { MessagingInternal } from '@firebase/messaging-interop-types';\nimport { MessagingService } from '../messaging-service';\nimport { ServiceWorkerGlobalScope } from '../util/sw-types';\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { getToken } from '../api/getToken';\nimport { messageEventListener } from '../listeners/window-listener';\n\nimport { name, version } from '../../package.json';\n\nconst WindowMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  navigator.serviceWorker.addEventListener('message', e =>\n    messageEventListener(messaging as MessagingService, e)\n  );\n\n  return messaging;\n};\n\nconst WindowMessagingInternalFactory: InstanceFactory<'messaging-internal'> = (\n  container: ComponentContainer\n) => {\n  const messaging = container\n    .getProvider('messaging')\n    .getImmediate() as MessagingService;\n\n  const messagingInternal: MessagingInternal = {\n    getToken: (options?: GetTokenOptions) => getToken(messaging, options)\n  };\n\n  return messagingInternal;\n};\n\ndeclare const self: ServiceWorkerGlobalScope;\nconst SwMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging as MessagingService));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging as MessagingService));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n\n  return messaging;\n};\n\nexport function registerMessagingInWindow(): void {\n  _registerComponent(\n    new Component('messaging', WindowMessagingFactory, ComponentType.PUBLIC)\n  );\n\n  _registerComponent(\n    new Component(\n      'messaging-internal',\n      WindowMessagingInternalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\n/**\n * The messaging instance registered in sw is named differently than that of in client. This is\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\n * `messaging-compat` and component with the same name can only be registered once.\n */\nexport function registerMessagingInSw(): void {\n  _registerComponent(\n    new Component('messaging-sw', SwMessagingFactory, ComponentType.PUBLIC)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isWindowSupported(): Promise<boolean> {\n  try {\n    // This throws if open() is unsupported, so adding it to the conditional\n    // statement below can cause an uncaught error.\n    await validateIndexedDBOpenable();\n  } catch (e) {\n    return false;\n  }\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks whether all required APIs exist within SW Context\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isSwSupported(): Promise<boolean> {\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    isIndexedDBAvailable() &&\n    (await validateIndexedDBOpenable()) &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './util/errors';\nimport { FirebaseApp, _getProvider, getApp } from '@firebase/app';\nimport {\n  GetTokenOptions,\n  MessagePayload,\n  Messaging\n} from './interfaces/public-types';\nimport {\n  NextFn,\n  Observer,\n  Unsubscribe,\n  getModularInstance\n} from '@firebase/util';\nimport { isSwSupported, isWindowSupported } from './api/isSupported';\n\nimport { MessagingService } from './messaging-service';\nimport { deleteToken as _deleteToken } from './api/deleteToken';\nimport { getToken as _getToken } from './api/getToken';\nimport { onBackgroundMessage as _onBackgroundMessage } from './api/onBackgroundMessage';\nimport { onMessage as _onMessage } from './api/onMessage';\nimport { _setDeliveryMetricsExportedToBigQueryEnabled } from './api/setDeliveryMetricsExportedToBigQueryEnabled';\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInWindow(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(\n    isSupported => {\n      // If `isWindowSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isWindowSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInSw(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(\n    isSupported => {\n      // If `isSwSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isSwSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nexport async function getToken(\n  messaging: Messaging,\n  options?: GetTokenOptions\n): Promise<string> {\n  messaging = getModularInstance(messaging);\n  return _getToken(messaging as MessagingService, options);\n}\n\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nexport function deleteToken(messaging: Messaging): Promise<boolean> {\n  messaging = getModularInstance(messaging);\n  return _deleteToken(messaging as MessagingService);\n}\n\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nexport function onMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Called when a message is received while the app is in the background. An app is considered to be\n * in the background if no active window is displayed.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\n * message is received and the app is currently in the background.\n *\n * @returns To stop listening for messages execute this returned function\n *\n * @public\n */\nexport function onBackgroundMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onBackgroundMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\n * disable the export at runtime.\n *\n * @param messaging - The `FirebaseMessaging` instance.\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\n * BigQuery.\n *\n * @public\n */\nexport function experimentalSetDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onBackgroundMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (self.document !== undefined) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_SW);\n  }\n\n  messaging.onBackgroundMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onBackgroundMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Messaging } from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function _setDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  (messaging as MessagingService).deliveryMetricsExportedToBigQueryEnabled =\n    enable;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport '@firebase/installations';\n\nimport { Messaging } from './interfaces/public-types';\nimport { registerMessagingInSw } from './helpers/register';\n\nexport * from './interfaces/public-types';\nexport {\n  onBackgroundMessage,\n  getMessagingInSw as getMessaging,\n  experimentalSetDeliveryMetricsExportedToBigQueryEnabled\n} from './api';\nexport { isSwSupported as isSupported } from './api/isSupported';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging-sw': Messaging;\n  }\n}\n\nregisterMessagingInSw();\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "set", "has", "wrapFunction", "func", "IDBDatabase", "transaction", "getCursorAdvanceMethods", "IDBCursor", "advance", "continue", "continuePrimaryKey", "includes", "args", "apply", "unwrap", "storeNames", "tx", "call", "sort", "transformCachableValue", "cacheDonePromiseForTransaction", "done", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "complete", "error", "DOMException", "addEventListener", "object", "getIdbProxyableTypes", "IDBObjectStore", "IDBIndex", "some", "c", "Proxy", "IDBRequest", "promisifyRequest", "request", "promise", "success", "result", "then", "catch", "newValue", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "useIndex", "isWrite", "method", "async", "storeName", "store", "index", "shift", "all", "replaceTraps", "oldTraps", "assign", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "TOKEN_EXPIRATION_BUFFER", "ERROR_FACTORY", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "responseExpiresIn", "Number", "creationTime", "Date", "now", "getErrorFromResponse", "requestName", "errorData", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "fn", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "generateFid", "fidByteArray", "Uint8Array", "self", "crypto", "msCrypto", "getRandomValues", "fid", "encode", "b64String", "bufferToBase64UrlSafe", "array", "btoa", "fromCharCode", "substr", "test", "_a", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "broadcastFidChange", "channel", "getBroadcastChannel", "broadcastChannel", "BroadcastChannel", "onmessage", "e", "postMessage", "closeBroadcastChannel", "size", "close", "callbacks", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "createObjectStore", "oldValue", "put", "remove", "delete", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "entry", "registrationStatus", "clearTimedOutRequest", "entryWithPromise", "triggerRegistrationIfNecessary", "navigator", "onLine", "inProgressEntry", "registrationTime", "registerInstallation", "registeredInstallationEntry", "createInstallationRequest", "heartbeatServiceProvider", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "waitUntilFidRegistration", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "isAuthTokenExpired", "waitUntilAuthTokenRequest", "updateAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "inProgressAuthToken", "requestTime", "fetchAuthTokenFromServer", "updatedInstallationEntry", "hasAuthTokenRequestTimedOut", "getToken", "installationsImpl", "completeInstallationRegistration", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "container", "app", "get<PERSON><PERSON><PERSON>", "extractAppConfig", "options", "config<PERSON><PERSON><PERSON>", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "console", "registerInstallations", "_registerComponent", "registerVersion", "DEFAULT_VAPID_KEY", "FCM_MSG", "MessageType", "arrayToBase64", "uint8Array", "base64ToArray", "base64String", "base64", "repeat", "length", "rawData", "atob", "outputArray", "i", "charCodeAt", "OLD_DB_NAME", "OLD_OBJECT_STORE_NAME", "upgradeDb", "dbGet", "firebaseDependencies", "tokenDetails", "oldTokenDetails", "migrateOldDatabase", "senderId", "dbNames", "databases", "map", "upgradeTransaction", "contains", "clear", "oldDetails", "auth", "p256dh", "fcmToken", "createTime", "subscriptionOptions", "swScope", "vapid<PERSON>ey", "checkTokenDetails", "dbSet", "requestDeleteToken", "unsubscribeOptions", "getEndpoint", "responseData", "errorInfo", "err", "toString", "getBody", "web", "applicationPubKey", "getTokenInternal", "messaging", "pushSubscription", "getPushSubscription", "swRegistration", "subscription", "pushManager", "getSubscription", "subscribe", "userVisibleOnly", "applicationServerKey", "scope", "isTokenValid", "dbOptions", "currentOptions", "isVapidKeyEqual", "isEndpointEqual", "isAuthEqual", "isP256dhEqual", "updateToken", "updatedToken", "requestUpdateToken", "updateOptions", "updatedTokenDetails", "warn", "getNewToken", "deleteTokenInternal", "db<PERSON><PERSON><PERSON>", "unsubscribe", "requestGetToken", "subscribeOptions", "stageLog", "internalPayload", "fcmEvent", "createFcmEvent", "from", "project_number", "fcmMessageId", "message_id", "instance_id", "notification", "message_type", "DISPLAY_NOTIFICATION", "DATA_MESSAGE", "sdk_platform", "package_name", "origin", "collapse_key", "fcmOptions", "analytics_label", "_b", "createAndEnqueueLogEvent", "productId", "logEvent", "event_time_ms", "Math", "floor", "source_extension_json_proto3", "messaging_client_event", "compliance_data", "buildComplianceData", "complianceData", "privacy_context", "prequest", "origin_associated_product_id", "logEvents", "push", "onPush", "getMessagePayloadInternal", "deliveryMetricsExportedToBigQueryEnabled", "clientList", "getClientList", "hasVisibleClients", "client", "visibilityState", "url", "startsWith", "sendMessagePayloadInternalToWindows", "isFirebaseMessaging", "messageType", "PUSH_RECEIVED", "showNotification", "notificationPayloadInternal", "actions", "maxActions", "Notification", "registration", "title", "wrapInternalPayload", "wrappedInternalPayload", "onBackgroundMessageHandler", "payload", "externalizePayload", "<PERSON><PERSON>ey", "messageId", "propagateNotificationPayload", "messagePayloadInternal", "image", "icon", "propagateDataPayload", "propagateFcmOptions", "click_action", "link", "_c", "_d", "analyticsLabel", "_e", "next", "onNotificationClick", "action", "stopImmediatePropagation", "getLink", "isConsoleMessage", "location", "URL", "href", "originUrl", "host", "getWindowClient", "clientUrl", "focus", "clients", "openWindow", "NOTIFICATION_CLICKED", "matchAll", "includeUncontrolled", "_mergeStrings", "s1", "s2", "resultArray", "char<PERSON>t", "join", "MessagingService", "analyticsProvider", "onMessageHandler", "isLogServiceStarted", "messagingSenderId", "SwMessagingFactory", "waitUntil", "onSubChange", "newSubscription", "isSwSupported", "isIndexedDBAvailable", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "onsuccess", "onupgradeneeded", "onerror", "ServiceWorkerRegistration", "hasOwnProperty", "PushSubscription", "getMessagingInSw", "getApp", "isSupported", "onBackgroundMessage", "nextOrObserver", "document", "_onBackgroundMessage", "experimentalSetDeliveryMetricsExportedToBigQueryEnabled", "enable", "_setDeliveryMetricsExportedToBigQueryEnabled", "registerMessagingInSw"], "mappings": "iGAyEM,MAAOA,sBAAsBC,MAIjC,WAAAC,CAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAX,CACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJ,MAAAH,CACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,KAAO,GAEtD,CAf+BJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,EAGxD,EAUH,MAAMkB,EAAU,gBClHV,SAAUM,mBACdd,GAEA,OAAIA,GAAYA,EAA+Be,UACrCf,EAA+Be,UAEhCf,CAEX,CCDa,MAAAgB,UAiBX,WAAA7B,CACWM,EACAwB,EACAC,GAFA1B,KAAIC,KAAJA,EACAD,KAAeyB,gBAAfA,EACAzB,KAAI0B,KAAJA,EAnBX1B,KAAiB2B,mBAAG,EAIpB3B,KAAY4B,aAAe,GAE3B5B,KAAA6B,kBAA2C,OAE3C7B,KAAiB8B,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADAhC,KAAK6B,kBAAoBG,EAClBhC,IACR,CAED,oBAAAiC,CAAqBN,GAEnB,OADA3B,KAAK2B,kBAAoBA,EAClB3B,IACR,CAED,eAAAkC,CAAgBC,GAEd,OADAnC,KAAK4B,aAAeO,EACbnC,IACR,CAED,0BAAAoC,CAA2BC,GAEzB,OADArC,KAAK8B,kBAAoBO,EAClBrC,IACR,ECnEH,IAAIsC,EACAC,EAqBJ,MAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlC,IAAIK,EAAgB,CAChB,GAAAC,CAAIC,EAAQC,EAAMC,GACd,GAAIF,aAAkBG,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOP,EAAmBK,IAAIC,GAElC,GAAa,qBAATC,EACA,OAAOD,EAAOI,kBAAoBT,EAAyBI,IAAIC,GAGnE,GAAa,UAATC,EACA,OAAOC,EAASE,iBAAiB,QAC3BC,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,GAE5D,CAED,OAAOG,KAAKP,EAAOC,GACtB,EACDO,IAAG,CAACR,EAAQC,EAAM9B,KACd6B,EAAOC,GAAQ9B,GACR,GAEXsC,IAAG,CAACT,EAAQC,IACJD,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,GAMvB,SAASU,aAAaC,GAIlB,OAAIA,IAASC,YAAYxD,UAAUyD,aAC7B,qBAAsBV,eAAe/C,UA9G/C,SAAS0D,0BACL,OAAQvB,IACHA,EAAuB,CACpBwB,UAAU3D,UAAU4D,QACpBD,UAAU3D,UAAU6D,SACpBF,UAAU3D,UAAU8D,oBAEhC,CAmHQJ,GAA0BK,SAASR,GAC5B,YAAaS,GAIhB,OADAT,EAAKU,MAAMC,OAAOtE,MAAOoE,GAClBb,KAAKf,EAAiBO,IAAI/C,MACrC,EAEG,YAAaoE,GAGhB,OAAOb,KAAKI,EAAKU,MAAMC,OAAOtE,MAAOoE,GACzC,EAvBW,SAAUG,KAAeH,GAC5B,MAAMI,EAAKb,EAAKc,KAAKH,OAAOtE,MAAOuE,KAAeH,GAElD,OADAzB,EAAyBa,IAAIgB,EAAID,EAAWG,KAAOH,EAAWG,OAAS,CAACH,IACjEhB,KAAKiB,EAChB,CAoBR,CACA,SAASG,uBAAuBxD,GAC5B,MAAqB,mBAAVA,EACAuC,aAAavC,IAGpBA,aAAiBgC,gBAhGzB,SAASyB,+BAA+BJ,GAEpC,GAAI9B,EAAmBe,IAAIe,GACvB,OACJ,MAAMK,EAAO,IAAIC,SAAQ,CAACC,EAASC,KAC/B,MAAMC,SAAW,KACbT,EAAGU,oBAAoB,WAAYC,UACnCX,EAAGU,oBAAoB,QAASE,OAChCZ,EAAGU,oBAAoB,QAASE,MAAM,EAEpCD,SAAW,KACbJ,IACAE,UAAU,EAERG,MAAQ,KACVJ,EAAOR,EAAGY,OAAS,IAAIC,aAAa,aAAc,eAClDJ,UAAU,EAEdT,EAAGc,iBAAiB,WAAYH,UAChCX,EAAGc,iBAAiB,QAASF,OAC7BZ,EAAGc,iBAAiB,QAASF,MAAM,IAGvC1C,EAAmBc,IAAIgB,EAAIK,EAC/B,CAyEQD,CAA+BzD,GA9JhBoE,EA+JDpE,EA1JtB,SAASqE,uBACL,OAAQlD,IACHA,EAAoB,CACjBsB,YACA6B,eACAC,SACA3B,UACAZ,gBAEZ,CAiJ6BqC,GA/JgCG,MAAMC,GAAML,aAAkBK,IAgK5E,IAAIC,MAAM1E,EAAO2B,GAErB3B,GAlKW,IAACoE,CAmKvB,CACA,SAAShC,KAAKpC,GAGV,GAAIA,aAAiB2E,WACjB,OA3IR,SAASC,iBAAiBC,GACtB,MAAMC,EAAU,IAAInB,SAAQ,CAACC,EAASC,KAClC,MAAMC,SAAW,KACbe,EAAQd,oBAAoB,UAAWgB,SACvCF,EAAQd,oBAAoB,QAASE,MAAM,EAEzCc,QAAU,KACZnB,EAAQxB,KAAKyC,EAAQG,SACrBlB,UAAU,EAERG,MAAQ,KACVJ,EAAOgB,EAAQZ,OACfH,UAAU,EAEde,EAAQV,iBAAiB,UAAWY,SACpCF,EAAQV,iBAAiB,QAASF,MAAM,IAe5C,OAbAa,EACKG,MAAMjF,IAGHA,aAAiB4C,WACjBvB,EAAiBgB,IAAIrC,EAAO6E,EAC/B,IAGAK,OAAM,SAGXxD,EAAsBW,IAAIyC,EAASD,GAC5BC,CACX,CA4GeF,CAAiB5E,GAG5B,GAAIyB,EAAea,IAAItC,GACnB,OAAOyB,EAAeG,IAAI5B,GAC9B,MAAMmF,EAAW3B,uBAAuBxD,GAOxC,OAJImF,IAAanF,IACbyB,EAAeY,IAAIrC,EAAOmF,GAC1BzD,EAAsBW,IAAI8C,EAAUnF,IAEjCmF,CACX,CACA,MAAMhC,OAAUnD,GAAU0B,EAAsBE,IAAI5B,GC5KpD,SAASoF,OAAOtG,EAAMuG,GAASC,QAAEA,EAAOC,QAAEA,EAAOC,SAAEA,EAAQC,WAAEA,GAAe,IACxE,MAAMZ,EAAUa,UAAUC,KAAK7G,EAAMuG,GAC/BO,EAAcxD,KAAKyC,GAoBzB,OAnBIU,GACAV,EAAQV,iBAAiB,iBAAkB0B,IACvCN,EAAQnD,KAAKyC,EAAQG,QAASa,EAAMC,WAAYD,EAAME,WAAY3D,KAAKyC,EAAQnC,aAAcmD,EAAM,IAGvGP,GACAT,EAAQV,iBAAiB,WAAY0B,GAAUP,EAE/CO,EAAMC,WAAYD,EAAME,WAAYF,KAExCD,EACKX,MAAMe,IACHP,GACAO,EAAG7B,iBAAiB,SAAS,IAAMsB,MACnCD,GACAQ,EAAG7B,iBAAiB,iBAAkB0B,GAAUL,EAASK,EAAMC,WAAYD,EAAME,WAAYF,IAChG,IAEAX,OAAM,SACJU,CACX,CAMA,SAASK,SAASnH,GAAMwG,QAAEA,GAAY,CAAA,GAClC,MAAMT,EAAUa,UAAUQ,eAAepH,GAMzC,OALIwG,GACAT,EAAQV,iBAAiB,WAAY0B,GAAUP,EAE/CO,EAAMC,WAAYD,KAEfzD,KAAKyC,GAASI,MAAK,KAAe,GAC7C,CAEA,MAAMkB,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,UAAU1E,EAAQC,GACvB,KAAMD,aAAkBY,cAClBX,KAAQD,GACM,iBAATC,EACP,OAEJ,GAAIuE,EAAczE,IAAIE,GAClB,OAAOuE,EAAczE,IAAIE,GAC7B,MAAM0E,EAAiB1E,EAAKlC,QAAQ,aAAc,IAC5C6G,EAAW3E,IAAS0E,EACpBE,EAAUN,EAAapD,SAASwD,GACtC,KAEEA,KAAmBC,EAAWlC,SAAWD,gBAAgBrF,aACrDyH,IAAWP,EAAYnD,SAASwD,GAClC,OAEJ,MAAMG,OAASC,eAAgBC,KAAc5D,GAEzC,MAAMI,EAAKxE,KAAK6D,YAAYmE,EAAWH,EAAU,YAAc,YAC/D,IAAI7E,EAASwB,EAAGyD,MAQhB,OAPIL,IACA5E,EAASA,EAAOkF,MAAM9D,EAAK+D,iBAMjBrD,QAAQsD,IAAI,CACtBpF,EAAO2E,MAAmBvD,GAC1ByD,GAAWrD,EAAGK,QACd,EACR,EAEA,OADA2C,EAAchE,IAAIP,EAAM6E,QACjBA,MACX,ED+BA,SAASO,aAAahG,GAClBS,EAAgBT,EAASS,EAC7B,CChCAuF,EAAcC,GACPpI,OAAAqI,OAAArI,OAAAqI,OAAA,CAAA,EAAAD,GACH,CAAAvF,IAAK,CAACC,EAAQC,EAAMC,IAAawE,UAAU1E,EAAQC,IAASqF,EAASvF,IAAIC,EAAQC,EAAMC,GACvFO,IAAK,CAACT,EAAQC,MAAWyE,UAAU1E,EAAQC,IAASqF,EAAS7E,IAAIT,EAAQC,oDCxEhEuF,EAAqB,IAErBC,EAAkB,KAAKjC,IACvBkC,EAAwB,SAKxBC,EAA0B,KCwB1BC,EAAgB,IAAItI,aDtBV,gBACK,gBCD2C,CACrE,4BACE,kDACF,iBAA4B,2CAC5B,yBAAoC,mCACpC,iBACE,6FACF,cAAyB,kDACzB,8BACE,6EA4BE,SAAUuI,cAAczD,GAC5B,OACEA,aAAiB3F,eACjB2F,EAAMxF,KAAKuE,SAAQ,iBAEvB,CCxCgB,SAAA2E,0BAAyBC,UAAEA,IACzC,MAAO,4DAAqCA,iBAC9C,CAEM,SAAUC,iCACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,WA8DuCC,EA9DMJ,EAASG,UAgEjDE,OAAOD,EAAkBtI,QAAQ,IAAK,SA/D3CwI,aAAcC,KAAKC,OA6DvB,IAA2CJ,CA3D3C,CAEOtB,eAAe2B,qBACpBC,EACAV,GAEA,MACMW,SADoCX,EAASY,QACpBzE,MAC/B,OAAOwD,EAAcrI,OAAiC,iBAAA,CACpDoJ,cACAG,WAAYF,EAAUhK,KACtBmK,cAAeH,EAAU/J,QACzBmK,aAAcJ,EAAUK,QAE5B,CAEgB,SAAAC,cAAWC,OAAEA,IAC3B,OAAO,IAAIC,QAAQ,CACjB,eAAgB,mBAChBC,OAAQ,mBACR,iBAAkBF,GAEtB,CAEgB,SAAAG,mBACdC,GACAC,aAAEA,IAEF,MAAMC,EAAUP,aAAWK,GAE3B,OADAE,EAAQC,OAAO,gBAmCjB,SAASC,uBAAuBH,GAC9B,MAAO,GAAG9B,KAAyB8B,GACrC,CArCkCG,CAAuBH,IAChDC,CACT,CAeO1C,eAAe6C,mBACpBC,GAEA,MAAM1E,QAAe0E,IAErB,OAAI1E,EAAO8D,QAAU,KAAO9D,EAAO8D,OAAS,IAEnCY,IAGF1E,CACT,CCnFM,SAAU2E,QAAMC,GACpB,OAAO,IAAIjG,SAAcC,IACvBiG,WAAWjG,EAASgG,EAAG,GAE3B,CCHO,MAAME,EAAoB,oBAOjB,SAAAC,cACd,IAGE,MAAMC,EAAe,IAAIC,WAAW,KAElCC,KAAKC,QAAWD,KAAyCE,UACpDC,gBAAgBL,GAGvBA,EAAa,GAAK,IAAcA,EAAa,GAAK,GAElD,MAAMM,EAUV,SAASC,OAAOP,GACd,MAAMQ,EChCF,SAAUC,sBAAsBC,GAEpC,OADYC,KAAK1K,OAAO2K,gBAAgBF,IAC7B9K,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IAChD,CD6BoB6K,CAAsBT,GAIxC,OAAOQ,EAAUK,OAAO,EAAG,GAC7B,CAhBgBN,CAAOP,GAEnB,OAAOF,EAAkBgB,KAAKR,GAAOA,EApBd,EAqBxB,CAAC,MAAAS,GAEA,MAvBuB,EAwBxB,CACH,CEzBM,SAAUC,SAAO5B,GACrB,MAAO,GAAGA,EAAU6B,WAAW7B,EAAU8B,OAC3C,CCDA,MAAMC,EAA2D,IAAI7E,IAMrD,SAAA8E,WAAWhC,EAAsBkB,GAC/C,MAAMvK,EAAMiL,SAAO5B,GAEnBiC,uBAAuBtL,EAAKuK,GAsD9B,SAASgB,mBAAmBvL,EAAauK,GACvC,MAAMiB,EASR,SAASC,uBACFC,GAAoB,qBAAsBvB,OAC7CuB,EAAmB,IAAIC,iBAAiB,yBACxCD,EAAiBE,UAAYC,IAC3BP,uBAAuBO,EAAEpM,KAAKO,IAAK6L,EAAEpM,KAAK8K,IAAI,GAGlD,OAAOmB,CACT,CAjBkBD,GACZD,GACFA,EAAQM,YAAY,CAAE9L,MAAKuK,SAiB/B,SAASwB,wBACyB,IAA5BX,EAAmBY,MAAcN,IACnCA,EAAiBO,QACjBP,EAAmB,KAEvB,CApBEK,EACF,CA3DER,CAAmBvL,EAAKuK,EAC1B,CAyCA,SAASe,uBAAuBtL,EAAauK,GAC3C,MAAM2B,EAAYd,EAAmBvJ,IAAI7B,GACzC,GAAKkM,EAIL,IAAK,MAAM/K,KAAY+K,EACrB/K,EAASoJ,EAEb,CAUA,IAAImB,EAA4C,KCrEhD,MAEMS,EAAoB,+BAS1B,IAAIC,EAA2D,KAC/D,SAASC,iBAgBP,OAfKD,IACHA,EAAY/G,OAdM,kCACG,EAa+B,CAClDG,QAAS,CAACS,EAAIF,KAMZ,GACO,IADCA,EAEJE,EAAGqG,kBAAkBH,EACxB,KAIAC,CACT,CAeOvF,eAAevE,IACpB+G,EACApJ,GAEA,MAAMD,EAAMiL,SAAO5B,GAEb/F,SADW+I,kBACH1J,YAAYwJ,EAAmB,aACvC/J,EAAckB,EAAGlB,YAAY+J,GAC7BI,QAAkBnK,EAAYP,IAAI7B,GAQxC,aAPMoC,EAAYoK,IAAIvM,EAAOD,SACvBsD,EAAGK,KAEJ4I,GAAYA,EAAShC,MAAQtK,EAAMsK,KACtCc,WAAWhC,EAAWpJ,EAAMsK,KAGvBtK,CACT,CAGO4G,eAAe4F,OAAOpD,GAC3B,MAAMrJ,EAAMiL,SAAO5B,GAEb/F,SADW+I,kBACH1J,YAAYwJ,EAAmB,mBACvC7I,EAAGlB,YAAY+J,GAAmBO,OAAO1M,SACzCsD,EAAGK,IACX,CAQOkD,eAAe8F,OACpBtD,EACAuD,GAEA,MAAM5M,EAAMiL,SAAO5B,GAEb/F,SADW+I,kBACH1J,YAAYwJ,EAAmB,aACvCpF,EAAQzD,EAAGlB,YAAY+J,GACvBI,QAAiDxF,EAAMlF,IAC3D7B,GAEIoF,EAAWwH,EAASL,GAa1B,YAXiBpK,IAAbiD,QACI2B,EAAM2F,OAAO1M,SAEb+G,EAAMyF,IAAIpH,EAAUpF,SAEtBsD,EAAGK,MAELyB,GAAcmH,GAAYA,EAAShC,MAAQnF,EAASmF,KACtDc,WAAWhC,EAAWjE,EAASmF,KAG1BnF,CACT,CClFOyB,eAAegG,qBACpBC,GAEA,IAAIC,EAEJ,MAAMC,QAA0BL,OAAOG,EAAczD,WAAW4D,IAC9D,MAAMD,EAwBV,SAASE,gCACPD,GAEA,MAAME,EAA2BF,GAAY,CAC3C1C,IAAKP,cACLoD,mBAA6C,GAG/C,OAAOC,qBAAqBF,EAC9B,CAjC8BD,CAAgCD,GACpDK,EAyCV,SAASC,+BACPT,EACAE,GAEA,GAAwC,IAApCA,EAAkBI,mBAAkD,CACtE,IAAKI,UAAUC,OAAQ,CAKrB,MAAO,CACLT,oBACAD,oBALmCnJ,QAAQE,OAC3C4D,EAAcrI,OAA6B,gBAM9C,CAGD,MAAMqO,EAA+C,CACnDnD,IAAKyC,EAAkBzC,IACvB6C,mBAA6C,EAC7CO,iBAAkBrF,KAAKC,OAEnBwE,EAkBVlG,eAAe+G,qBACbd,EACAE,GAEA,IACE,MAAMa,QCxGHhH,eAAeiH,2BACpBzE,UAAEA,EAAS0E,yBAAEA,IACbxD,IAAEA,IAEF,MAAMyD,EAAWpG,yBAAyByB,GAEpCE,EAAUP,aAAWK,GAGrB4E,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACF7E,EAAQC,OAAO,oBAAqB4E,EAEvC,CAED,MAAME,EAAO,CACX/D,MACAgE,YAAa/G,EACb2D,MAAO9B,EAAU8B,MACjBqD,WAAYjH,GAGRzC,EAAuB,CAC3B8B,OAAQ,OACR2C,UACA+E,KAAMG,KAAKC,UAAUJ,IAGjBvG,QAAiB2B,oBAAmB,IAAMiF,MAAMX,EAAUlJ,KAChE,GAAIiD,EAAS6G,GAAI,CACf,MAAMC,QAAkD9G,EAASY,OAOjE,MANiE,CAC/D4B,IAAKsE,EAActE,KAAOA,EAC1B6C,mBAA2C,EAC3C9D,aAAcuF,EAAcvF,aAC5BwF,UAAWhH,iCAAiC+G,EAAcC,WAG7D,CACC,YAAYtG,qBAAqB,sBAAuBT,EAE5D,CD2D8C+F,CACxChB,EACAE,GAEF,OAAO1K,IAAIwK,EAAczD,UAAWwE,EACrC,CAAC,MAAOhC,GAYP,MAXIlE,cAAckE,IAAkC,MAA5BA,EAAEjN,WAAWgK,iBAG7B6D,OAAOK,EAAczD,iBAGrB/G,IAAIwK,EAAczD,UAAW,CACjCkB,IAAKyC,EAAkBzC,IACvB6C,mBAA6C,IAG3CvB,CACP,CACH,CA1CgC+B,CAC1Bd,EACAY,GAEF,MAAO,CAAEV,kBAAmBU,EAAiBX,sBAC9C,CAAM,OAC+B,IAApCC,EAAkBI,mBAEX,CACLJ,oBACAD,oBAAqBgC,yBAAyBjC,IAGzC,CAAEE,oBAEb,CA9E6BO,CACvBT,EACAE,GAGF,OADAD,EAAsBO,EAAiBP,oBAChCO,EAAiBN,iBAAiB,IAG3C,MLvCyB,KKuCrBA,EAAkBzC,IAEb,CAAEyC,wBAAyBD,GAG7B,CACLC,oBACAD,sBAEJ,CA2FAlG,eAAekI,yBACbjC,GAMA,IAAIK,QAAiC6B,0BACnClC,EAAczD,WAEhB,KAA+B,IAAxB8D,EAAMC,0BAELxD,QAAM,KAEZuD,QAAc6B,0BAA0BlC,EAAczD,WAGxD,GAA4B,IAAxB8D,EAAMC,mBAAkD,CAE1D,MAAMJ,kBAAEA,EAAiBD,oBAAEA,SACnBF,qBAAqBC,GAE7B,OAAIC,GAIKC,CAEV,CAED,OAAOG,CACT,CAUA,SAAS6B,0BACP3F,GAEA,OAAOsD,OAAOtD,GAAW4D,IACvB,IAAKA,EACH,MAAMvF,EAAcrI,OAAM,0BAE5B,OAAOgO,qBAAqBJ,EAAS,GAEzC,CAEA,SAASI,qBAAqBF,GAC5B,OAUF,SAAS8B,+BACPjC,GAEA,OACoE,IAAlEA,EAAkBI,oBAClBJ,EAAkBW,iBAAmBrG,EAAqBgB,KAAKC,KAEnE,CAjBM0G,CAA+B9B,GAC1B,CACL5C,IAAK4C,EAAM5C,IACX6C,mBAA6C,GAI1CD,CACT,CEzLOtG,eAAeqI,0BACpB7F,UAAEA,EAAS0E,yBAAEA,GACbf,GAEA,MAAMgB,EAuCR,SAASmB,6BACP9F,GACAkB,IAAEA,IAEF,MAAO,GAAG3C,yBAAyByB,MAAckB,uBACnD,CA5CmB4E,CAA6B9F,EAAW2D,GAEnDzD,EAAUH,mBAAmBC,EAAW2D,GAGxCiB,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACF7E,EAAQC,OAAO,oBAAqB4E,EAEvC,CAED,MAAME,EAAO,CACXc,aAAc,CACZZ,WAAYjH,EACZ4D,MAAO9B,EAAU8B,QAIfrG,EAAuB,CAC3B8B,OAAQ,OACR2C,UACA+E,KAAMG,KAAKC,UAAUJ,IAGjBvG,QAAiB2B,oBAAmB,IAAMiF,MAAMX,EAAUlJ,KAChE,GAAIiD,EAAS6G,GAAI,CAIf,OADE9G,uCAFqDC,EAASY,OAIjE,CACC,YAAYH,qBAAqB,sBAAuBT,EAE5D,CCnCOlB,eAAewI,iBACpBvC,EACAwC,GAAe,GAEf,IAAIC,EACJ,MAAMpC,QAAcR,OAAOG,EAAczD,WAAW4D,IAClD,IAAKuC,kBAAkBvC,GACrB,MAAMvF,EAAcrI,OAAM,kBAG5B,MAAMoQ,EAAexC,EAAS6B,UAC9B,IAAKQ,GA+HT,SAASI,iBAAiBZ,GACxB,OACqD,IAAnDA,EAAU7G,gBAKd,SAAS0H,mBAAmBb,GAC1B,MAAMvG,EAAMD,KAAKC,MACjB,OACEA,EAAMuG,EAAUzG,cAChByG,EAAUzG,aAAeyG,EAAU5G,UAAYK,EAAMd,CAEzD,CAVKkI,CAAmBb,EAExB,CApIyBY,CAAiBD,GAEpC,OAAOxC,EACF,GAA8B,IAA1BwC,EAAaxH,cAGtB,OADAsH,EA0BN1I,eAAe+I,0BACb9C,EACAwC,GAMA,IAAInC,QAAc0C,uBAAuB/C,EAAczD,WACvD,KAAoC,IAA7B8D,EAAM2B,UAAU7G,qBAEf2B,QAAM,KAEZuD,QAAc0C,uBAAuB/C,EAAczD,WAGrD,MAAMyF,EAAY3B,EAAM2B,UACxB,OAA2B,IAAvBA,EAAU7G,cAELoH,iBAAiBvC,EAAewC,GAEhCR,CAEX,CAjDqBc,CAA0B9C,EAAewC,GACjDrC,EACF,CAEL,IAAKO,UAAUC,OACb,MAAM/F,EAAcrI,OAAM,eAG5B,MAAMqO,EAkIZ,SAASoC,oCACP7C,GAEA,MAAM8C,EAA2C,CAC/C9H,cAAwC,EACxC+H,YAAa1H,KAAKC,OAEpB,OAAAvJ,OAAAqI,OAAArI,OAAAqI,OAAA,CAAA,EACK4F,GAAQ,CACX6B,UAAWiB,GAEf,CA7I8BD,CAAoC7C,GAE5D,OADAsC,EAsEN1I,eAAeoJ,yBACbnD,EACAE,GAEA,IACE,MAAM8B,QAAkBI,yBACtBpC,EACAE,GAEIkD,EACDlR,OAAAqI,OAAArI,OAAAqI,OAAA,CAAA,EAAA2F,GACH,CAAA8B,cAGF,aADMxM,IAAIwK,EAAczD,UAAW6G,GAC5BpB,CACR,CAAC,MAAOjD,GACP,IACElE,cAAckE,IACe,MAA5BA,EAAEjN,WAAWgK,YAAkD,MAA5BiD,EAAEjN,WAAWgK,WAK5C,CACL,MAAMsH,EACDlR,OAAAqI,OAAArI,OAAAqI,OAAA,CAAA,EAAA2F,GACH,CAAA8B,UAAW,CAAE7G,cAAa,WAEtB3F,IAAIwK,EAAczD,UAAW6G,EACpC,YAPOzD,OAAOK,EAAczD,WAQ7B,MAAMwC,CACP,CACH,CAtGqBoE,CAAyBnD,EAAeY,GAChDA,CACR,KAMH,OAHkB6B,QACRA,EACLpC,EAAM2B,SAEb,CAyCA,SAASe,uBACPxG,GAEA,OAAOsD,OAAOtD,GAAW4D,IACvB,IAAKuC,kBAAkBvC,GACrB,MAAMvF,EAAcrI,OAAM,kBAI5B,OAmFJ,SAAS8Q,4BAA4BrB,GACnC,OACuD,IAArDA,EAAU7G,eACV6G,EAAUkB,YAAc1I,EAAqBgB,KAAKC,KAEtD,CAxFQ4H,CADiBlD,EAAS6B,WAGvB9P,OAAAqI,OAAArI,OAAAqI,OAAA,CAAA,EAAA4F,GACH,CAAA6B,UAAW,CAAE7G,cAAa,KAIvBgF,CAAQ,GAEnB,CAoCA,SAASuC,kBACPxC,GAEA,YACwB7K,IAAtB6K,GACgE,IAAhEA,EAAkBI,kBAEtB,CCnJOvG,eAAeuJ,SACpBtD,EACAwC,GAAe,GAEf,MAAMe,EAAoBvD,QAS5BjG,eAAeyJ,iCACbxD,GAEA,MAAMC,oBAAEA,SAA8BF,qBAAqBC,GAEvDC,SAEIA,CAEV,CAjBQuD,CAAiCD,GAKvC,aADwBhB,iBAAiBgB,EAAmBf,IAC3CtH,KACnB,CCWA,SAASuI,uBAAqBC,GAC5B,OAAO9I,EAAcrI,OAA4C,4BAAA,CAC/DmR,aAEJ,CC3BA,MAAMC,EAAqB,gBAGrBC,cACJC,IAEA,MAAMC,EAAMD,EAAUE,YAAY,OAAO3C,eAEnC7E,EDfF,SAAUyH,mBAAiBF,GAC/B,IAAKA,IAAQA,EAAIG,QACf,MAAMR,uBAAqB,qBAG7B,IAAKK,EAAI7R,KACP,MAAMwR,uBAAqB,YAI7B,MAAMS,EAA2C,CAC/C,YACA,SACA,SAGF,IAAK,MAAMC,KAAWD,EACpB,IAAKJ,EAAIG,QAAQE,GACf,MAAMV,uBAAqBU,GAI/B,MAAO,CACL/F,QAAS0F,EAAI7R,KACb8I,UAAW+I,EAAIG,QAAQlJ,UACvBoB,OAAQ2H,EAAIG,QAAQ9H,OACpBkC,MAAOyF,EAAIG,QAAQ5F,MAEvB,CCboB2F,CAAiBF,GASnC,MANqD,CACnDA,MACAvH,YACA0E,yBAL+BmD,aAAaN,EAAK,aAMjDO,QAAS,IAAMvN,QAAQC,UAED,EAGpBuN,gBACJT,IAEA,MAAMC,EAAMD,EAAUE,YAAY,OAAO3C,eAEnCpB,EAAgBoE,aAAaN,EAAKH,GAAoBvC,eAM5D,MAJ8D,CAC5DmD,MAAO,IC5BJxK,eAAewK,MAAMvE,GAC1B,MAAMuD,EAAoBvD,GACpBE,kBAAEA,EAAiBD,oBAAEA,SAA8BF,qBACvDwD,GAWF,OARItD,EACFA,EAAoB5H,MAAMmM,QAAQpN,OAIlCmL,iBAAiBgB,GAAmBlL,MAAMmM,QAAQpN,OAG7C8I,EAAkBzC,GAC3B,CDaiB8G,CAAMvE,GACnBsD,SAAWd,GAA2Bc,SAAStD,EAAewC,GAEpC,GAGd,SAAAiC,wBACdC,EACE,IAAIlR,UAAUmQ,EAAoBC,cAAoC,WAExEc,EACE,IAAIlR,UAtC4B,yBAwC9B8Q,gBAED,WAEL,CE3CAG,GACAE,EAAgB1S,EAAMuG,GAEtBmM,EAAgB1S,EAAMuG,EAAS,WCdxB,MAAMoM,EACX,0FAKWC,EAAU,UAsBvB,IAAYC,EC6BAA,EC5DN,SAAUC,cAAclH,GAC5B,MAAMmH,EAAa,IAAI5H,WAAWS,GAElC,OADqBC,KAAK1K,OAAO2K,gBAAgBiH,IAC7BjS,QAAQ,KAAM,IAAIA,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IAC3E,CAEM,SAAUkS,cAAcC,GAC5B,MACMC,GAAUD,EADA,IAAIE,QAAQ,EAAKF,EAAaG,OAAS,GAAM,IAE1DtS,QAAQ,MAAO,KACfA,QAAQ,KAAM,KAEXuS,EAAUC,KAAKJ,GACfK,EAAc,IAAIpI,WAAWkI,EAAQD,QAE3C,IAAK,IAAII,EAAI,EAAGA,EAAIH,EAAQD,SAAUI,EACpCD,EAAYC,GAAKH,EAAQI,WAAWD,GAEtC,OAAOD,CACT,EFYA,SAAYV,GACVA,EAAAA,EAAA,aAAA,GAAA,eACAA,EAAAA,EAAA,qBAAA,GAAA,sBACD,CAHD,CAAYA,IAAAA,EAGX,CAAA,IC0BD,SAAYA,GACVA,EAAA,cAAA,gBACAA,EAAA,qBAAA,sBACD,CAHD,CAAYA,IAAAA,EAGX,CAAA,IEnBD,MAAMa,EAAc,uBAMdC,EAAwB,yBC3CvB,MAEDvG,EAAoB,2BAS1B,IAAIC,EAAuD,KAC3D,SAASC,eAeP,OAdKD,IACHA,EAAY/G,OAda,8BACJ,EAa+B,CAClDG,QAAS,CAACmN,EAAW5M,KAKnB,GACO,IADCA,EAEJ4M,EAAUrG,kBAAkBH,EAC/B,KAIAC,CACT,CAGOvF,eAAe+L,MACpBC,GAEA,MAAM7S,EAAMiL,OAAO4H,GACb5M,QAAWoG,eACXyG,QAAsB7M,EACzBtD,YAAYwJ,GACZ/J,YAAY+J,GACZtK,IAAI7B,GAEP,GAAI8S,EACF,OAAOA,EACF,CAEL,MAAMC,QDAHlM,eAAemM,mBACpBC,GAEA,GAAI,cAAetN,UAAW,CAG5B,MAKMuN,SAJJvN,UAGAwN,aACwBC,KAAInN,GAAMA,EAAGlH,OAEvC,IAAKmU,EAAQjQ,SAASwP,GAEpB,OAAO,IAEV,CAED,IAAIK,EAAoC,KAkFxC,aAhFiBzN,OAAOoN,EAxBH,EAwBgC,CACnDjN,QAASqB,MAAOZ,EAAIF,EAAYC,EAAYqN,WAC1C,GAAItN,EAAa,EAEf,OAGF,IAAKE,EAAG/D,iBAAiBoR,SAASZ,GAEhC,OAGF,MAAMtQ,EAAciR,EAAmBjR,YAAYsQ,GAC7CzS,QAAcmC,EAAY4E,MAAM,eAAenF,IAAIoR,GAGzD,SAFM7Q,EAAYmR,QAEbtT,EAKL,GAAmB,IAAf8F,EAAkB,CACpB,MAAMyN,EAAavT,EAEnB,IAAKuT,EAAWC,OAASD,EAAWE,SAAWF,EAAWxF,SACxD,OAGF8E,EAAe,CACb9K,MAAOwL,EAAWG,SAClBC,WAAqC,QAAzB5I,EAAAwI,EAAWI,kBAAc,IAAA5I,EAAAA,EAAA1C,KAAKC,MAC1CsL,oBAAqB,CACnBJ,KAAMD,EAAWC,KACjBC,OAAQF,EAAWE,OACnB1F,SAAUwF,EAAWxF,SACrB8F,QAASN,EAAWM,QACpBC,SACiC,iBAAxBP,EAAWO,SACdP,EAAWO,SACXlC,cAAc2B,EAAWO,WAGpC,MAAM,GAAmB,IAAfhO,EAAkB,CAC3B,MAAMyN,EAAavT,EAEnB6S,EAAe,CACb9K,MAAOwL,EAAWG,SAClBC,WAAYJ,EAAWI,WACvBC,oBAAqB,CACnBJ,KAAM5B,cAAc2B,EAAWC,MAC/BC,OAAQ7B,cAAc2B,EAAWE,QACjC1F,SAAUwF,EAAWxF,SACrB8F,QAASN,EAAWM,QACpBC,SAAUlC,cAAc2B,EAAWO,WAGxC,MAAM,GAAmB,IAAfhO,EAAkB,CAC3B,MAAMyN,EAAavT,EAEnB6S,EAAe,CACb9K,MAAOwL,EAAWG,SAClBC,WAAYJ,EAAWI,WACvBC,oBAAqB,CACnBJ,KAAM5B,cAAc2B,EAAWC,MAC/BC,OAAQ7B,cAAc2B,EAAWE,QACjC1F,SAAUwF,EAAWxF,SACrB8F,QAASN,EAAWM,QACpBC,SAAUlC,cAAc2B,EAAWO,WAGxC,MAGF9H,cAGG/F,SAASuM,SACTvM,SAAS,8BACTA,SAAS,aAKjB,SAAS8N,kBACPlB,GAEA,IAAKA,IAAiBA,EAAae,oBACjC,OAAO,EAET,MAAMA,oBAAEA,GAAwBf,EAChC,MACqC,iBAA5BA,EAAac,YACpBd,EAAac,WAAa,GACI,iBAAvBd,EAAa9K,OACpB8K,EAAa9K,MAAMmK,OAAS,GACQ,iBAA7B0B,EAAoBJ,MAC3BI,EAAoBJ,KAAKtB,OAAS,GACI,iBAA/B0B,EAAoBH,QAC3BG,EAAoBH,OAAOvB,OAAS,GACI,iBAAjC0B,EAAoB7F,UAC3B6F,EAAoB7F,SAASmE,OAAS,GACC,iBAAhC0B,EAAoBC,SAC3BD,EAAoBC,QAAQ3B,OAAS,GACG,iBAAjC0B,EAAoBE,UAC3BF,EAAoBE,SAAS5B,OAAS,CAE1C,CA1BS6B,CAAkBlB,GAAgBA,EAAe,IAC1D,CCtGkCE,CAC5BH,EAAqBxJ,UAAU4J,UAEjC,GAAIF,EAEF,aADMkB,MAAMpB,EAAsBE,GAC3BA,CAEV,CACH,CAGOlM,eAAeoN,MACpBpB,EACAC,GAEA,MAAM9S,EAAMiL,OAAO4H,GAEbvP,SADW+I,gBACH1J,YAAYwJ,EAAmB,aAG7C,aAFM7I,EAAGlB,YAAY+J,GAAmBK,IAAIsG,EAAc9S,SACpDsD,EAAGK,KACFmP,CACT,CAsBA,SAAS7H,QAAO5B,UAAEA,IAChB,OAAOA,EAAU8B,KACnB,CC1EO,MAmDMzD,EAAgB,IAAItI,aAC/B,YACA,YArD4C,CAC5C,4BACE,kDACF,2BACE,gDACF,uBACE,wDACF,qBACE,qEACF,qBACE,mEACF,sBACE,2EACF,yBACE,mGACF,qCACE,+EACF,yBACE,qEACF,2BACE,2DACF,2BACE,yEAEF,sBACE,oEACF,wBACE,wDACF,yBACE,4IAEF,0BACE,uEACF,qBACE,iEACF,oBAA+B,yCAC/B,gCACE,0ICyCGyH,eAAeqN,mBACpBrB,EACA7K,GAEA,MAEMmM,EAAqB,CACzBvN,OAAQ,SACR2C,cAJoBP,WAAW6J,IAOjC,IACE,MAAM9K,QAAiB4G,MACrB,GAAGyF,YAAYvB,EAAqBxJ,cAAcrB,IAClDmM,GAEIE,QAAkCtM,EAASY,OACjD,GAAI0L,EAAanQ,MAAO,CACtB,MAAMvF,EAAU0V,EAAanQ,MAAMvF,QACnC,MAAM+I,EAAcrI,OAA2C,2BAAA,CAC7DiV,UAAW3V,GAEd,CACF,CAAC,MAAO4V,GACP,MAAM7M,EAAcrI,OAA2C,2BAAA,CAC7DiV,UAAYC,aAAA,EAAAA,EAAeC,YAE9B,CACH,CAEA,SAASJ,aAAYvM,UAAEA,IACrB,MAAO,uDAAwBA,iBACjC,CAEAhB,eAAemC,YAAWK,UACxBA,EAASyD,cACTA,IAEA,MAAMgC,QAAkBhC,EAAcsD,WAEtC,OAAO,IAAIlH,QAAQ,CACjB,eAAgB,mBAChBC,OAAQ,mBACR,iBAAkBE,EAAUJ,OAC5B,qCAAsC,OAAO6F,KAEjD,CAEA,SAAS2F,SAAQf,OACfA,EAAMD,KACNA,EAAIzF,SACJA,EAAQ+F,SACRA,IAEA,MAAMzF,EAAuB,CAC3BoG,IAAK,CACH1G,WACAyF,OACAC,WAQJ,OAJIK,IAAarC,IACfpD,EAAKoG,IAAIC,kBAAoBZ,GAGxBzF,CACT,CCtJOzH,eAAe+N,iBACpBC,GAEA,MAAMC,QAmHRjO,eAAekO,oBACbC,EACAjB,GAEA,MAAMkB,QAAqBD,EAAeE,YAAYC,kBACtD,GAAIF,EACF,OAAOA,EAGT,OAAOD,EAAeE,YAAYE,UAAU,CAC1CC,iBAAiB,EAGjBC,qBAAsBvD,cAAcgC,IAExC,CAlIiCgB,CAC7BF,EAAUG,eACVH,EAAUd,UAGNF,EAA2C,CAC/CE,SAAUc,EAAUd,SACpBD,QAASe,EAAUG,eAAgBO,MACnCvH,SAAU8G,EAAiB9G,SAC3ByF,KAAM5B,cAAciD,EAAiB7J,OAAO,SAC5CyI,OAAQ7B,cAAciD,EAAiB7J,OAAO,YAG1C6H,QAAqBF,MAAMiC,EAAUhC,sBAC3C,GAAKC,EAGE,IAsHT,SAAS0C,aACPC,EACAC,GAEA,MAAMC,EAAkBD,EAAe3B,WAAa0B,EAAU1B,SACxD6B,EAAkBF,EAAe1H,WAAayH,EAAUzH,SACxD6H,EAAcH,EAAejC,OAASgC,EAAUhC,KAChDqC,EAAgBJ,EAAehC,SAAW+B,EAAU/B,OAE1D,OAAOiC,GAAmBC,GAAmBC,GAAeC,CAC9D,CA/HKN,CAAa1C,EAAae,oBAAsBA,GAc5C,OAAIvL,KAAKC,OAASuK,EAAac,WArCZ,OA6E5B/M,eAAekP,YACblB,EACA/B,GAEA,IACE,MAAMkD,QDrCHnP,eAAeoP,mBACpBpD,EACAC,GAEA,MAAMvJ,QAAgBP,WAAW6J,GAC3BvE,EAAOmG,QAAQ3B,EAAae,qBAE5BqC,EAAgB,CACpBtP,OAAQ,QACR2C,UACA+E,KAAMG,KAAKC,UAAUJ,IAGvB,IAAI+F,EACJ,IACE,MAAMtM,QAAiB4G,MACrB,GAAGyF,YAAYvB,EAAqBxJ,cAAcyJ,EAAa9K,QAC/DkO,GAEF7B,QAAqBtM,EAASY,MAC/B,CAAC,MAAO4L,GACP,MAAM7M,EAAcrI,OAAsC,sBAAA,CACxDiV,UAAYC,aAAA,EAAAA,EAAeC,YAE9B,CAED,GAAIH,EAAanQ,MAAO,CACtB,MAAMvF,EAAU0V,EAAanQ,MAAMvF,QACnC,MAAM+I,EAAcrI,OAAsC,sBAAA,CACxDiV,UAAW3V,GAEd,CAED,IAAK0V,EAAarM,MAChB,MAAMN,EAAcrI,OAAM,yBAG5B,OAAOgV,EAAarM,KACtB,CCD+BiO,CACzBpB,EAAUhC,qBACVC,GAGIqD,EAAmBnX,OAAAqI,OAAArI,OAAAqI,OAAA,CAAA,EACpByL,GAAY,CACf9K,MAAOgO,EACPpC,WAAYtL,KAAKC,QAInB,aADM0L,MAAMY,EAAUhC,qBAAsBsD,GACrCH,CACR,CAAC,MAAOnK,GACP,MAAMA,CACP,CACH,CA3DWkK,CAAYlB,EAAW,CAC5B7M,MAAO8K,EAAa9K,MACpB4L,WAAYtL,KAAKC,MACjBsL,wBAIKf,EAAa9K,MApBpB,UACQkM,mBACJW,EAAUhC,qBACVC,EAAa9K,MAEhB,CAAC,MAAO6D,GAEPyF,QAAQ8E,KAAKvK,EACd,CAED,OAAOwK,YAAYxB,EAAUhC,qBAAuBgB,EAWrD,CA1BC,OAAOwC,YAAYxB,EAAUhC,qBAAsBgB,EA2BvD,CAMOhN,eAAeyP,oBACpBzB,GAEA,MAAM/B,QAAqBF,MAAMiC,EAAUhC,sBACvCC,UACIoB,mBACJW,EAAUhC,qBACVC,EAAa9K,aHDZnB,eAAe0P,SACpB1D,GAEA,MAAM7S,EAAMiL,OAAO4H,GAEbvP,SADW+I,gBACH1J,YAAYwJ,EAAmB,mBACvC7I,EAAGlB,YAAY+J,GAAmBO,OAAO1M,SACzCsD,EAAGK,IACX,CGLU4S,CAAS1B,EAAUhC,uBAI3B,MAAMiC,QACED,EAAUG,eAAgBE,YAAYC,kBAC9C,OAAIL,GACKA,EAAiB0B,aAK5B,CAyBA3P,eAAewP,YACbxD,EACAgB,GAEA,MAAM7L,QDnGDnB,eAAe4P,gBACpB5D,EACAgB,GAEA,MAAMtK,QAAgBP,WAAW6J,GAC3BvE,EAAOmG,QAAQZ,GAEf6C,EAAmB,CACvB9P,OAAQ,OACR2C,UACA+E,KAAMG,KAAKC,UAAUJ,IAGvB,IAAI+F,EACJ,IACE,MAAMtM,QAAiB4G,MACrByF,YAAYvB,EAAqBxJ,WACjCqN,GAEFrC,QAAqBtM,EAASY,MAC/B,CAAC,MAAO4L,GACP,MAAM7M,EAAcrI,OAAyC,yBAAA,CAC3DiV,UAAYC,aAAA,EAAAA,EAAeC,YAE9B,CAED,GAAIH,EAAanQ,MAAO,CACtB,MAAMvF,EAAU0V,EAAanQ,MAAMvF,QACnC,MAAM+I,EAAcrI,OAAyC,yBAAA,CAC3DiV,UAAW3V,GAEd,CAED,IAAK0V,EAAarM,MAChB,MAAMN,EAAcrI,OAAM,4BAG5B,OAAOgV,EAAarM,KACtB,CC6DsByO,CAClB5D,EACAgB,GAEIf,EAA6B,CACjC9K,QACA4L,WAAYtL,KAAKC,MACjBsL,uBAGF,aADMI,MAAMpB,EAAsBC,GAC3BA,EAAa9K,KACtB,CCKOnB,eAAe8P,SACpB9B,EACA+B,GAEA,MAAMC,EAQR,SAASC,eACPF,EACArM,WAEA,MAAMsM,EAAW,CAAA,EAIXD,EAAgBG,OACpBF,EAASG,eAAiBJ,EAAgBG,MAGtCH,EAAgBK,eACpBJ,EAASK,WAAaN,EAAgBK,cAGxCJ,EAASM,YAAc5M,EAEjBqM,EAAgBQ,aACpBP,EAASQ,aAAezF,EAAY0F,qBAAqB9C,WAEzDqC,EAASQ,aAAezF,EAAY2F,aAAa/C,WAGnDqC,EAASW,aRhJqB,GQgJWhD,WACzCqC,EAASY,aAAetN,KAAKuN,OAAO7X,QAAQ,gBAAiB,KAEvD+W,EAAgBe,eACpBd,EAASc,aAAef,EAAgBe,cAG1Cd,EAAS/Q,MRtJ4B,GQsJI0O,aAEP,QAA5BxJ,EAAA4L,EAAgBgB,kBAAY,IAAA5M,OAAA,EAAAA,EAAA6M,mBAChChB,EAASgB,gBAA8C,QAA5BC,EAAAlB,EAAgBgB,kBAAY,IAAAE,OAAA,EAAAA,EAAAD,iBAIzD,OAAOhB,CACT,CA/CmBC,CACfF,QACM/B,EAAUhC,qBAAqB/F,cAAcuE,UA+CvD,SAAS0G,yBACPlD,EACAgC,EACAmB,GAEA,MAAMC,EAAW,CAAA,EAGjBA,EAASC,cAAgBC,KAAKC,MAAM9P,KAAKC,OAAOiM,WAChDyD,EAASI,6BAA+B5J,KAAKC,UAAU,CACrD4J,uBAAwBzB,KAGpBmB,IACJC,EAASM,gBAOb,SAASC,oBAAoBR,GAC3B,MAAMS,EAAiC,CACrCC,gBAAiB,CACfC,SAAU,CACRC,6BAA8BZ,KAKpC,OAAOS,CACT,CAjB+BD,CAAoBR,IAIjDnD,EAAUgE,UAAUC,KAAKb,EAC3B,CA/DEF,CAAyBlD,EAAWgC,EAAUD,EAAgBoB,UAChE,CC5FOnR,eAAekS,OACpBjT,EACA+O,GAEA,MAAM+B,EA4GR,SAASoC,2BAA0BvZ,KACjCA,IAEA,IAAKA,EACH,OAAO,KAGT,IACE,OAAOA,EAAKkJ,MACb,CAAC,MAAO4L,GAEP,OAAO,IACR,CACH,CAzH0ByE,CAA0BlT,GAClD,IAAK8Q,EAEH,OAIE/B,EAAUoE,gDACNtC,SAAS9B,EAAW+B,GAI5B,MAAMsC,QAAmBC,gBACzB,GAoIF,SAASC,kBAAkBF,GACzB,OAAOA,EAAWzU,MAChB4U,GAC6B,YAA3BA,EAAOC,kBAGND,EAAOE,IAAIC,WAAW,wBAE7B,CA5IMJ,CAAkBF,GACpB,OA6IJ,SAASO,oCACPP,EACAtC,GAEAA,EAAgB8C,qBAAsB,EACtC9C,EAAgB+C,YAAc/H,EAAYgI,cAE1C,IAAK,MAAMP,KAAUH,EACnBG,EAAOvN,YAAY8K,EAEvB,CAvJW6C,CAAoCP,EAAYtC,GAQzD,GAJMA,EAAgBQ,oBA6JxB,SAASyC,iBACPC,SAIA,MAAMC,QAAEA,GAAYD,GACdE,WAAEA,GAAeC,aACnBF,GAAWC,GAAcD,EAAQ5H,OAAS6H,GAC5C1I,QAAQ8E,KACN,8BAA8B4D,2DAIlC,OAAO7P,KAAK+P,aAAaL,yBACV7O,EAAA8O,EAA4BK,qBAAS,GAClDL,EAEJ,CA7KUD,CAwEV,SAASO,oBACPxD,GAEA,MAAMyD,EACArb,OAAAqI,OAAA,CAAA,EAAAuP,EAAgBQ,cAUtB,OAJAiD,EAAuB5a,KAAO,CAC5BkS,CAACA,GAAUiF,GAGNyD,CACT,CAvF2BD,CAAoBxD,IAGxC/B,GAICA,EAAUyF,2BAA4B,CAC1C,MAAMC,EClFJ,SAAUC,mBACd5D,GAEA,MAAM2D,EAA0B,CAC9BxD,KAAMH,EAAgBG,KAEtB0D,YAAa7D,EAAgBe,aAE7B+C,UAAW9D,EAAgBK,cAO7B,OAGF,SAAS0D,6BACPJ,EACAK,GAEA,IAAKA,EAAuBxD,aAC1B,OAGFmD,EAAQnD,aAAe,GAEvB,MAAM+C,EAAQS,EAAuBxD,aAAc+C,MAC7CA,IACJI,EAAQnD,aAAc+C,MAAQA,GAGhC,MAAM7L,EAAOsM,EAAuBxD,aAAc9I,KAC5CA,IACJiM,EAAQnD,aAAc9I,KAAOA,GAG/B,MAAMuM,EAAQD,EAAuBxD,aAAcyD,MAC7CA,IACJN,EAAQnD,aAAcyD,MAAQA,GAGhC,MAAMC,EAAOF,EAAuBxD,aAAc0D,KAC5CA,IACJP,EAAQnD,aAAc0D,KAAOA,EAEjC,CApCEH,CAA6BJ,EAAS3D,GAsCxC,SAASmE,qBACPR,EACAK,GAEKA,EAAuBnb,OAI5B8a,EAAQ9a,KAAOmb,EAAuBnb,KACxC,CA9CEsb,CAAqBR,EAAS3D,GAgDhC,SAASoE,oBACPT,EACAK,iBAGA,IACGA,EAAuBhD,cACc,UAArCgD,EAAuBxD,oBAAc,IAAApM,OAAA,EAAAA,EAAAiQ,cAEtC,OAGFV,EAAQ3C,WAAa,GAErB,MAAMsD,EACmC,QAAvCC,EAAiC,QAAjCrD,EAAA8C,EAAuBhD,kBAAU,IAAAE,OAAA,EAAAA,EAAEoD,YAAI,IAAAC,EAAAA,EACJ,QAAnCC,EAAAR,EAAuBxD,oBAAY,IAAAgE,OAAA,EAAAA,EAAEH,aAEjCC,IACJX,EAAQ3C,WAAYsD,KAAOA,GAI7B,MAAMG,EAAkD,QAAjCC,EAAAV,EAAuBhD,kBAAU,IAAA0D,OAAA,EAAAA,EAAEzD,gBACpDwD,IACJd,EAAQ3C,WAAYyD,eAAiBA,EAEzC,CA1EEL,CAAoBT,EAAS3D,GAEtB2D,CACT,CDkEoBC,CAAmB5D,GAEiB,mBAAzC/B,EAAUyF,iCACbzF,EAAUyF,2BAA2BC,GAE3C1F,EAAUyF,2BAA2BiB,KAAKhB,EAE7C,CACH,CAEO1T,eAAe2U,oBACpB1V,WAEA,MAAM8Q,EACoB,QAAxBkB,EAAkB,QAAlB9M,EAAAlF,EAAMsR,oBAAY,IAAApM,OAAA,EAAAA,EAAEvL,YAAI,IAAAqY,OAAA,EAAAA,EAAGnG,GAE7B,IAAKiF,EACH,OACK,GAAI9Q,EAAM2V,OAGf,OAIF3V,EAAM4V,2BACN5V,EAAMsR,aAAanL,QAGnB,MAAMiP,EA0IR,SAASS,QAAQpB,aAEf,MAAMW,EAA+B,QAAxBpD,EAAkB,QAAlB9M,EAAAuP,EAAQ3C,kBAAU,IAAA5M,OAAA,EAAAA,EAAEkQ,YAAI,IAAApD,EAAAA,EAAwB,QAApBqD,EAAAZ,EAAQnD,oBAAY,IAAA+D,OAAA,EAAAA,EAAEF,aAC/D,GAAIC,EACF,OAAOA,EAGT,OEhQI,SAAUU,iBAAiBnc,GAE/B,MAAuB,iBAATA,KAAuBA,GXMJ,oBWNmCA,CACtE,CF6PMmc,CAAiBrB,EAAQ9a,MAEpB0K,KAAK0R,SAASnE,OAEd,IAEX,CAvJeiE,CAAQ/E,GACrB,IAAKsE,EACH,OAIF,MAAM3B,EAAM,IAAIuC,IAAIZ,EAAM/Q,KAAK0R,SAASE,MAClCC,EAAY,IAAIF,IAAI3R,KAAK0R,SAASnE,QAExC,GAAI6B,EAAI0C,OAASD,EAAUC,KACzB,OAGF,IAAI5C,QA0DNxS,eAAeqV,gBAAgB3C,GAC7B,MAAML,QAAmBC,gBAEzB,IAAK,MAAME,KAAUH,EAAY,CAC/B,MAAMiD,EAAY,IAAIL,IAAIzC,EAAOE,IAAKpP,KAAK0R,SAASE,MAEpD,GAAIxC,EAAI0C,OAASE,EAAUF,KACzB,OAAO5C,CAEV,CAED,OAAO,IACT,CAtEqB6C,CAAgB3C,GAYnC,OAVKF,EAOHA,QAAeA,EAAO+C,SANtB/C,QAAelP,KAAKkS,QAAQC,WAAWpB,SGjIrC,SAAUtR,MAAMC,GACpB,OAAO,IAAIjG,SAAcC,IACvBiG,WAAWjG,EAASgG,EAAG,GAE3B,CHiIUD,CAAM,MAKTyP,GAKLzC,EAAgB+C,YAAc/H,EAAY2K,qBAC1C3F,EAAgB8C,qBAAsB,EAC/BL,EAAOvN,YAAY8K,SAP1B,CAQF,CA8EA,SAASuC,gBACP,OAAOhP,KAAKkS,QAAQG,SAAS,CAC3Bhc,KAAM,SACNic,qBAAqB,GAGzB,CIhMA,SAASlM,qBAAqBC,GAC5B,OAAO9I,EAAcrI,OAA4C,4BAAA,CAC/DmR,aAEJ,EL8LgB,SAAAkM,cAAcC,EAAYC,GACxC,MAAMC,EAAc,GACpB,IAAK,IAAItK,EAAI,EAAGA,EAAIoK,EAAGxK,OAAQI,IAC7BsK,EAAY/D,KAAK6D,EAAGG,OAAOvK,IACvBA,EAAIqK,EAAGzK,QACT0K,EAAY/D,KAAK8D,EAAGE,OAAOvK,IAI/B,OAAOsK,EAAYE,KAAK,GAC1B,CA5N0BL,CACxB,uBACA,uBMfW,MAAAM,iBAoBX,WAAAve,CACEmS,EACA9D,EACAmQ,GAhBFne,KAAwCma,0CAAY,EAEpDna,KAA0Bwb,2BAGf,KAEXxb,KAAgBoe,iBACd,KAEFpe,KAAS+Z,UAAe,GACxB/Z,KAAmBqe,qBAAY,EAO7B,MAAM9T,ED7BJ,SAAUyH,iBAAiBF,GAC/B,IAAKA,IAAQA,EAAIG,QACf,MAAMR,qBAAqB,4BAG7B,IAAKK,EAAI7R,KACP,MAAMwR,qBAAqB,YAI7B,MAAMS,EAAmD,CACvD,YACA,SACA,QACA,sBAGID,QAAEA,GAAYH,EACpB,IAAK,MAAMK,KAAWD,EACpB,IAAKD,EAAQE,GACX,MAAMV,qBAAqBU,GAI/B,MAAO,CACL/F,QAAS0F,EAAI7R,KACb8I,UAAWkJ,EAAQlJ,UACnBoB,OAAQ8H,EAAQ9H,OAChBkC,MAAO4F,EAAQ5F,MACf8H,SAAUlC,EAAQqM,kBAEtB,CCFsBtM,CAAiBF,GAEnC9R,KAAK+T,qBAAuB,CAC1BjC,MACAvH,YACAyD,gBACAmQ,oBAEH,CAED,OAAA9L,GACE,OAAOvN,QAAQC,SAChB,ECMH,MAAMwZ,mBACJ1M,IAEA,MAAMkE,EAAY,IAAImI,iBACpBrM,EAAUE,YAAY,OAAO3C,eAC7ByC,EAAUE,YAAY,0BAA0B3C,eAChDyC,EAAUE,YAAY,uBAaxB,OAVA1G,KAAK/F,iBAAiB,QAAQyH,IAC5BA,EAAEyR,UAAUvE,OAAOlN,EAAGgJ,GAA+B,IAEvD1K,KAAK/F,iBAAiB,0BAA0ByH,IAC9CA,EAAEyR,UN/BCzW,eAAe0W,YACpBzX,EACA+O,WAEA,MAAM2I,gBAAEA,GAAoB1X,EAC5B,IAAK0X,EAGH,kBADMlH,oBAAoBzB,GAI5B,MAAM/B,QAAqBF,MAAMiC,EAAUhC,4BACrCyD,oBAAoBzB,GAE1BA,EAAUd,SACuC,QAA/C+D,EAAmC,QAAnC9M,EAAA8H,aAAA,EAAAA,EAAce,2BAAqB,IAAA7I,OAAA,EAAAA,EAAA+I,gBAAY,IAAA+D,EAAAA,EAAApG,QAC3CkD,iBAAiBC,EACzB,CMcgB0I,CAAY1R,EAAGgJ,GAA+B,IAE5D1K,KAAK/F,iBAAiB,qBAAqByH,IACzCA,EAAEyR,UAAU9B,oBAAoB3P,GAAG,IAG9BgJ,CAAS,EC9BXhO,eAAe4W,gBAIpB,gBC+HcC,uBACd,IACE,MAA4B,iBAAd/X,SACf,CAAC,MAAOkG,GACP,OAAO,CACR,CACH,CDpII6R,mBC6IYC,4BACd,OAAO,IAAI/Z,SAAQ,CAACC,EAASC,KAC3B,IACE,IAAI8Z,GAAoB,EACxB,MAAMC,EACJ,0DACI/Y,EAAUqF,KAAKxE,UAAUC,KAAKiY,GACpC/Y,EAAQgZ,UAAY,KAClBhZ,EAAQG,OAAOgH,QAEV2R,GACHzT,KAAKxE,UAAUQ,eAAe0X,GAEhCha,GAAQ,EAAK,EAEfiB,EAAQiZ,gBAAkB,KACxBH,GAAW,CAAK,EAGlB9Y,EAAQkZ,QAAU,WAChBla,GAAoB,QAAbkH,EAAAlG,EAAQZ,aAAK,IAAA8G,OAAA,EAAAA,EAAErM,UAAW,GAAG,CAEvC,CAAC,MAAOuF,GACPJ,EAAOI,EACR,IAEL,CDtKWyZ,IACP,gBAAiBxT,MACjB,iBAAkBA,MAClB8T,0BAA0B/e,UAAUgf,eAAe,qBACnDC,iBAAiBjf,UAAUgf,eAAe,SAE9C,CEEgB,SAAAE,iBAAiBxN,EAAmByN,KAiBlD,OAZAZ,gBAAgBvY,MACdoZ,IAEE,IAAKA,EACH,MAAM5W,EAAcrI,OAAM,sBAC3B,IAEHU,IAEE,MAAM2H,EAAcrI,OAAM,yBAAkC,IAGzD6R,aAAa9Q,mBAAmBwQ,GAAM,gBAAgB1C,cAC/D,CAyEgB,SAAAqQ,oBACd1J,EACA2J,GAGA,OC9Ic,SAAAD,sBACd1J,EACA2J,GAEA,QAAsBrc,IAAlBgI,KAAKsU,SACP,MAAM/W,EAAcrI,OAAM,wBAK5B,OAFAwV,EAAUyF,2BAA6BkE,EAEhC,KACL3J,EAAUyF,2BAA6B,IAAI,CAE/C,CDiISoE,CADP7J,EAAYzU,mBAAmByU,GAC4B2J,EAC7D,CAagB,SAAAG,wDACd9J,EACA+J,GAGA,OExKc,SAAAC,6CACdhK,EACA+J,GAEC/J,EAA+BoE,yCAC9B2F,CACJ,CFkKSC,CADPhK,EAAYzU,mBAAmByU,GACgC+J,EACjE,EH1EgB,SAAAE,wBACdtN,EACE,IAAIlR,UAAU,eAAgB+c,mBAAyC,UAE3E,CMnFAyB", "preExistingComment": "firebase-messaging-sw.js.map"}