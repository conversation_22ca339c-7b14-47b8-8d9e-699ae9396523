{"version": 3, "file": "firebase-performance-compat.js", "sources": ["../logger/src/logger.ts", "../util/src/errors.ts", "../component/src/component.ts", "../../node_modules/web-vitals/dist/web-vitals.attribution.js", "../../node_modules/idb/build/index.js", "../../node_modules/idb/build/wrap-idb-value.js", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../performance/src/constants.ts", "../performance/src/utils/attributes_utils.ts", "../performance-compat/src/index.ts", "../performance/src/utils/errors.ts", "../performance/src/utils/console_logger.ts", "../performance/src/services/api_service.ts", "../util/src/environment.ts", "../performance/src/services/iid_service.ts", "../performance/src/utils/string_merger.ts", "../performance/src/services/settings_service.ts", "../performance/src/utils/app_utils.ts", "../performance/src/services/remote_config_service.ts", "../performance/src/services/initialization_service.ts", "../performance/src/services/transport_service.ts", "../performance/src/services/perf_logger.ts", "../performance/src/resources/network_request.ts", "../performance/src/utils/metric_utils.ts", "../performance/src/resources/trace.ts", "../performance/src/services/oob_resources_service.ts", "../performance/src/controllers/perf.ts", "../performance/src/index.ts", "../performance-compat/src/performance.ts", "../util/src/compat.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "var t,e,n=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},r=function(t){if(\"loading\"===document.readyState)return\"loading\";var e=n();if(e){if(t<e.domInteractive)return\"loading\";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return\"dom-interactive\";if(0===e.domComplete||t<e.domComplete)return\"dom-content-loaded\"}return\"complete\"},i=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,\"\")},a=function(t,e){var n=\"\";try{for(;t&&9!==t.nodeType;){var r=t,a=r.id?\"#\"+r.id:i(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?\".\"+r.classList.value.trim().replace(/\\s+/g,\".\"):\"\");if(n.length+a.length>(e||100)-1)return n||a;if(n=n?a+\">\"+n:a,r.id)break;t=r.parentNode}}catch(t){}return n},o=-1,c=function(){return o},u=function(t){addEventListener(\"pageshow\",(function(e){e.persisted&&(o=e.timeStamp,t(e))}),!0)},s=function(){var t=n();return t&&t.activationStart||0},f=function(t,e){var r=n(),i=\"navigate\";c()>=0?i=\"back-forward-cache\":r&&(document.prerendering||s()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":r.type&&(i=r.type.replace(/_/g,\"-\")));return{name:t,value:void 0===e?-1:e,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},d=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},l=function(t,e,n,r){var i,a;return function(o){e.value>=0&&(o||r)&&((a=e.value-(i||0))||void 0===i)&&(i=e.value,e.delta=a,e.rating=function(t,e){return t>e[1]?\"poor\":t>e[0]?\"needs-improvement\":\"good\"}(e.value,n),t(e))}},m=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&t()}))},v=function(t){var e=!1;return function(){e||(t(),e=!0)}},g=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},T=function(t){\"hidden\"===document.visibilityState&&g>-1&&(g=\"visibilitychange\"===t.type?t.timeStamp:0,E())},y=function(){addEventListener(\"visibilitychange\",T,!0),addEventListener(\"prerenderingchange\",T,!0)},E=function(){removeEventListener(\"visibilitychange\",T,!0),removeEventListener(\"prerenderingchange\",T,!0)},S=function(){return g<0&&(g=h(),y(),u((function(){setTimeout((function(){g=h(),y()}),0)}))),{get firstHiddenTime(){return g}}},b=function(t){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return t()}),!0):t()},L=[1800,3e3],C=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FCP\"),a=d(\"paint\",(function(t){t.forEach((function(t){\"first-contentful-paint\"===t.name&&(a.disconnect(),t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries.push(t),n(!0)))}))}));a&&(n=l(t,i,L,e.reportAllChanges),u((function(r){i=f(\"FCP\"),n=l(t,i,L,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},M=[.1,.25],D=function(t,e){!function(t,e){e=e||{},C(v((function(){var n,r=f(\"CLS\",0),i=0,a=[],o=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=a[0],n=a[a.length-1];i&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(i+=t.value,a.push(t)):(i=t.value,a=[t])}})),i>r.value&&(r.value=i,r.entries=a,n())},c=d(\"layout-shift\",o);c&&(n=l(t,r,M,e.reportAllChanges),p((function(){o(c.takeRecords()),n(!0)})),u((function(){i=0,r=f(\"CLS\",0),n=l(t,r,M,e.reportAllChanges),m((function(){return n()}))})),setTimeout(n,0))})))}((function(e){var n=function(t){var e,n={};if(t.entries.length){var i=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(i&&i.sources&&i.sources.length){var o=(e=i.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];o&&(n={largestShiftTarget:a(o.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:o,largestShiftEntry:i,loadState:r(i.startTime)})}}return Object.assign(t,{attribution:n})}(e);t(n)}),e)},w=function(t,e){C((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:r(c())};if(t.entries.length){var i=n(),a=t.entries[t.entries.length-1];if(i){var o=i.activationStart||0,u=Math.max(0,i.responseStart-o);e={timeToFirstByte:u,firstByteToFCP:t.value-u,loadState:r(t.entries[0].startTime),navigationEntry:i,fcpEntry:a}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},x=0,I=1/0,k=0,A=function(t){t.forEach((function(t){t.interactionId&&(I=Math.min(I,t.interactionId),k=Math.max(k,t.interactionId),x=k?(k-I)/7+1:0)}))},F=function(){return t?x:performance.interactionCount||0},P=function(){\"interactionCount\"in performance||t||(t=d(\"event\",A,{type:\"event\",buffered:!0,durationThreshold:0}))},B=[],O=new Map,R=0,j=function(){var t=Math.min(B.length-1,Math.floor((F()-R)/50));return B[t]},q=[],H=function(t){if(q.forEach((function(e){return e(t)})),t.interactionId||\"first-input\"===t.entryType){var e=B[B.length-1],n=O.get(t.interactionId);if(n||B.length<10||t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===n.entries[0].startTime&&n.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};O.set(r.id,r),B.push(r)}B.sort((function(t,e){return e.latency-t.latency})),B.length>10&&B.splice(10).forEach((function(t){return O.delete(t.id)}))}}},N=function(t){var e=self.requestIdleCallback||self.setTimeout,n=-1;return t=v(t),\"hidden\"===document.visibilityState?t():(n=e(t),p(t)),n},W=[200,500],z=function(t,e){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(e=e||{},b((function(){var n;P();var r,i=f(\"INP\"),a=function(t){N((function(){t.forEach(H);var e=j();e&&e.latency!==i.value&&(i.value=e.latency,i.entries=e.entries,r())}))},o=d(\"event\",a,{durationThreshold:null!==(n=e.durationThreshold)&&void 0!==n?n:40});r=l(t,i,W,e.reportAllChanges),o&&(o.observe({type:\"first-input\",buffered:!0}),p((function(){a(o.takeRecords()),r(!0)})),u((function(){R=F(),B.length=0,O.clear(),i=f(\"INP\"),r=l(t,i,W,e.reportAllChanges)})))})))},U=[],V=[],_=0,G=new WeakMap,J=new Map,K=-1,Q=function(t){U=U.concat(t),X()},X=function(){K<0&&(K=N(Y))},Y=function(){J.size>10&&J.forEach((function(t,e){O.has(e)||J.delete(e)}));var t=B.map((function(t){return G.get(t.entries[0])})),e=V.length-50;V=V.filter((function(n,r){return r>=e||t.includes(n)}));for(var n=new Set,r=0;r<V.length;r++){var i=V[r];nt(i.startTime,i.processingEnd).forEach((function(t){n.add(t)}))}var a=U.length-1-50;U=U.filter((function(t,e){return t.startTime>_&&e>a||n.has(t)})),K=-1};q.push((function(t){t.interactionId&&t.target&&!J.has(t.interactionId)&&J.set(t.interactionId,t.target)}),(function(t){var e,n=t.startTime+t.duration;_=Math.max(_,t.processingEnd);for(var r=V.length-1;r>=0;r--){var i=V[r];if(Math.abs(n-i.renderTime)<=8){(e=i).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:n,entries:[t]},V.push(e)),(t.interactionId||\"first-input\"===t.entryType)&&G.set(t,e),X()}));var Z,$,tt,et,nt=function(t,e){for(var n,r=[],i=0;n=U[i];i++)if(!(n.startTime+n.duration<t)){if(n.startTime>e)break;r.push(n)}return r},rt=function(t,n){e||(e=d(\"long-animation-frame\",Q)),z((function(e){var n=function(t){var e=t.entries[0],n=G.get(e),i=e.processingStart,o=n.processingEnd,c=n.entries.sort((function(t,e){return t.processingStart-e.processingStart})),u=nt(e.startTime,o),s=t.entries.find((function(t){return t.target})),f=s&&s.target||J.get(e.interactionId),d=[e.startTime+e.duration,o].concat(u.map((function(t){return t.startTime+t.duration}))),l=Math.max.apply(Math,d),m={interactionTarget:a(f),interactionTargetElement:f,interactionType:e.name.startsWith(\"key\")?\"keyboard\":\"pointer\",interactionTime:e.startTime,nextPaintTime:l,processedEventEntries:c,longAnimationFrameEntries:u,inputDelay:i-e.startTime,processingDuration:o-i,presentationDelay:Math.max(l-o,0),loadState:r(e.startTime)};return Object.assign(t,{attribution:m})}(e);t(n)}),n)},it=[2500,4e3],at={},ot=function(t,e){!function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"LCP\"),a=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries=[t],n())}))},o=d(\"largest-contentful-paint\",a);if(o){n=l(t,i,it,e.reportAllChanges);var c=v((function(){at[i.id]||(a(o.takeRecords()),o.disconnect(),at[i.id]=!0,n(!0))}));[\"keydown\",\"click\"].forEach((function(t){addEventListener(t,(function(){return N(c)}),{once:!0,capture:!0})})),p(c),u((function(r){i=f(\"LCP\"),n=l(t,i,it,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,at[i.id]=!0,n(!0)}))}))}}))}((function(e){var r=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var r=n();if(r){var i=r.activationStart||0,o=t.entries[t.entries.length-1],c=o.url&&performance.getEntriesByType(\"resource\").filter((function(t){return t.name===o.url}))[0],u=Math.max(0,r.responseStart-i),s=Math.max(u,c?(c.requestStart||c.startTime)-i:0),f=Math.max(s,c?c.responseEnd-i:0),d=Math.max(f,o.startTime-i);e={element:a(o.element),timeToFirstByte:u,resourceLoadDelay:s-u,resourceLoadDuration:f-s,elementRenderDelay:d-f,navigationEntry:r,lcpEntry:o},o.url&&(e.url=o.url),c&&(e.lcpResourceEntry=c)}}return Object.assign(t,{attribution:e})}(e);t(r)}),e)},ct=[800,1800],ut=function t(e){document.prerendering?b((function(){return t(e)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return t(e)}),!0):setTimeout(e,0)},st=function(t,e){e=e||{};var r=f(\"TTFB\"),i=l(t,r,ct,e.reportAllChanges);ut((function(){var a=n();a&&(r.value=Math.max(a.responseStart-s(),0),r.entries=[a],i(!0),u((function(){r=f(\"TTFB\",0),(i=l(t,r,ct,e.reportAllChanges))(!0)})))}))},ft=function(t,e){st((function(e){var n=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var n=t.entries[0],r=n.activationStart||0,i=Math.max((n.workerStart||n.fetchStart)-r,0),a=Math.max(n.domainLookupStart-r,0),o=Math.max(n.connectStart-r,0),c=Math.max(n.connectEnd-r,0);e={waitingDuration:i,cacheDuration:a-i,dnsDuration:o-a,connectionDuration:c-o,requestDuration:t.value-c,navigationEntry:n}}return Object.assign(t,{attribution:e})}(e);t(n)}),e)},dt={passive:!0,capture:!0},lt=new Date,mt=function(t,e){Z||(Z=e,$=t,tt=new Date,gt(removeEventListener),pt())},pt=function(){if($>=0&&$<tt-lt){var t={entryType:\"first-input\",name:Z.type,target:Z.target,cancelable:Z.cancelable,startTime:Z.timeStamp,processingStart:Z.timeStamp+$};et.forEach((function(e){e(t)})),et=[]}},vt=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;\"pointerdown\"==t.type?function(t,e){var n=function(){mt(t,e),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",n,dt),removeEventListener(\"pointercancel\",r,dt)};addEventListener(\"pointerup\",n,dt),addEventListener(\"pointercancel\",r,dt)}(e,t):mt(e,t)}},gt=function(t){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(e){return t(e,vt,dt)}))},ht=[100,300],Tt=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FID\"),a=function(t){t.startTime<r.firstHiddenTime&&(i.value=t.processingStart-t.startTime,i.entries.push(t),n(!0))},o=function(t){t.forEach(a)},c=d(\"first-input\",o);n=l(t,i,ht,e.reportAllChanges),c&&(p(v((function(){o(c.takeRecords()),c.disconnect()}))),u((function(){var r;i=f(\"FID\"),n=l(t,i,ht,e.reportAllChanges),et=[],$=-1,Z=null,gt(addEventListener),r=a,et.push(r),pt()})))}))},yt=function(t,e){Tt((function(e){var n=function(t){var e=t.entries[0],n={eventTarget:a(e.target),eventType:e.name,eventTime:e.startTime,eventEntry:e,loadState:r(e.startTime)};return Object.assign(t,{attribution:n})}(e);t(n)}),e)};export{M as CLSThresholds,L as FCPThresholds,ht as FIDThresholds,W as INPThresholds,it as LCPThresholds,ct as TTFBThresholds,D as onCLS,w as onFCP,yt as onFID,rt as onINP,ot as onLCP,ft as onTTFB};\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const SDK_VERSION = version;\n/** The prefix for start User Timing marks used for creating Traces. */\nexport const TRACE_START_MARK_PREFIX = 'FB-PERF-TRACE-START';\n/** The prefix for stop User Timing marks used for creating Traces. */\nexport const TRACE_STOP_MARK_PREFIX = 'FB-PERF-TRACE-STOP';\n/** The prefix for User Timing measure used for creating Traces. */\nexport const TRACE_MEASURE_PREFIX = 'FB-PERF-TRACE-MEASURE';\n/** The prefix for out of the box page load Trace name. */\nexport const OOB_TRACE_PAGE_LOAD_PREFIX = '_wt_';\n\nexport const FIRST_PAINT_COUNTER_NAME = '_fp';\n\nexport const FIRST_CONTENTFUL_PAINT_COUNTER_NAME = '_fcp';\n\nexport const FIRST_INPUT_DELAY_COUNTER_NAME = '_fid';\n\nexport const LARGEST_CONTENTFUL_PAINT_METRIC_NAME = '_lcp';\nexport const LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME = 'lcp_element';\n\nexport const INTERACTION_TO_NEXT_PAINT_METRIC_NAME = '_inp';\nexport const INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME = 'inp_interactionTarget';\n\nexport const CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME = '_cls';\nexport const CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME = 'cls_largestShiftTarget';\n\nexport const CONFIG_LOCAL_STORAGE_KEY = '@firebase/performance/config';\n\nexport const CONFIG_EXPIRY_LOCAL_STORAGE_KEY =\n  '@firebase/performance/configexpire';\n\nexport const SERVICE = 'performance';\nexport const SERVICE_NAME = 'Performance';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\n\n// The values and orders of the following enums should not be changed.\nconst enum ServiceWorkerStatus {\n  UNKNOWN = 0,\n  UNSUPPORTED = 1,\n  CONTROLLED = 2,\n  UNCONTROLLED = 3\n}\n\nexport enum VisibilityState {\n  UNKNOWN = 0,\n  VISIBLE = 1,\n  HIDDEN = 2\n}\n\nconst enum EffectiveConnectionType {\n  UNKNOWN = 0,\n  CONNECTION_SLOW_2G = 1,\n  CONNECTION_2G = 2,\n  CONNECTION_3G = 3,\n  CONNECTION_4G = 4\n}\n\ntype ConnectionType =\n  | 'bluetooth'\n  | 'cellular'\n  | 'ethernet'\n  | 'mixed'\n  | 'none'\n  | 'other'\n  | 'unknown'\n  | 'wifi';\n\n/**\n * NetworkInformation\n * This API is not well supported in all major browsers, so TypeScript does not provide types for it.\n *\n * ref: https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation\n */\ninterface NetworkInformation extends EventTarget {\n  readonly type: ConnectionType;\n}\n\ninterface NetworkInformationWithEffectiveType extends NetworkInformation {\n  readonly effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';\n}\n\ninterface NavigatorWithConnection extends Navigator {\n  readonly connection: NetworkInformationWithEffectiveType;\n}\n\nconst RESERVED_ATTRIBUTE_PREFIXES = ['firebase_', 'google_', 'ga_'];\nconst ATTRIBUTE_FORMAT_REGEX = new RegExp('^[a-zA-Z]\\\\w*$');\nconst MAX_ATTRIBUTE_NAME_LENGTH = 40;\nconst MAX_ATTRIBUTE_VALUE_LENGTH = 100;\n\nexport function getServiceWorkerStatus(): ServiceWorkerStatus {\n  const navigator = Api.getInstance().navigator;\n  if (navigator?.serviceWorker) {\n    if (navigator.serviceWorker.controller) {\n      return ServiceWorkerStatus.CONTROLLED;\n    } else {\n      return ServiceWorkerStatus.UNCONTROLLED;\n    }\n  } else {\n    return ServiceWorkerStatus.UNSUPPORTED;\n  }\n}\n\nexport function getVisibilityState(): VisibilityState {\n  const document = Api.getInstance().document;\n  const visibilityState = document.visibilityState;\n  switch (visibilityState) {\n    case 'visible':\n      return VisibilityState.VISIBLE;\n    case 'hidden':\n      return VisibilityState.HIDDEN;\n    default:\n      return VisibilityState.UNKNOWN;\n  }\n}\n\nexport function getEffectiveConnectionType(): EffectiveConnectionType {\n  const navigator = Api.getInstance().navigator;\n  const navigatorConnection = (navigator as NavigatorWithConnection).connection;\n  const effectiveType =\n    navigatorConnection && navigatorConnection.effectiveType;\n  switch (effectiveType) {\n    case 'slow-2g':\n      return EffectiveConnectionType.CONNECTION_SLOW_2G;\n    case '2g':\n      return EffectiveConnectionType.CONNECTION_2G;\n    case '3g':\n      return EffectiveConnectionType.CONNECTION_3G;\n    case '4g':\n      return EffectiveConnectionType.CONNECTION_4G;\n    default:\n      return EffectiveConnectionType.UNKNOWN;\n  }\n}\n\nexport function isValidCustomAttributeName(name: string): boolean {\n  if (name.length === 0 || name.length > MAX_ATTRIBUTE_NAME_LENGTH) {\n    return false;\n  }\n  const matchesReservedPrefix = RESERVED_ATTRIBUTE_PREFIXES.some(prefix =>\n    name.startsWith(prefix)\n  );\n  return !matchesReservedPrefix && !!name.match(ATTRIBUTE_FORMAT_REGEX);\n}\n\nexport function isValidCustomAttributeValue(value: string): boolean {\n  return value.length !== 0 && value.length <= MAX_ATTRIBUTE_VALUE_LENGTH;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType\n} from '@firebase/component';\nimport { PerformanceCompatImpl } from './performance';\nimport { name as packageName, version } from '../package.json';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\n\nfunction registerPerformanceCompat(firebaseInstance: _FirebaseNamespace): void {\n  firebaseInstance.INTERNAL.registerComponent(\n    new Component(\n      'performance-compat',\n      performanceFactory,\n      ComponentType.PUBLIC\n    )\n  );\n\n  firebaseInstance.registerVersion(packageName, version);\n}\n\nfunction performanceFactory(\n  container: ComponentContainer\n): PerformanceCompatImpl {\n  const app = container.getProvider('app-compat').getImmediate();\n  // The following call will always succeed.\n  const performance = container.getProvider('performance').getImmediate();\n\n  return new PerformanceCompatImpl(app, performance);\n}\n\nregisterPerformanceCompat(firebase as _FirebaseNamespace);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    performance: {\n      (app?: FirebaseApp): FirebasePerformanceCompat;\n    };\n  }\n  interface FirebaseApp {\n    performance(): FirebasePerformanceCompat;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from '../constants';\n\nexport const enum ErrorCode {\n  TRACE_STARTED_BEFORE = 'trace started',\n  TRACE_STOPPED_BEFORE = 'trace stopped',\n  NONPOSITIVE_TRACE_START_TIME = 'nonpositive trace startTime',\n  NONPOSITIVE_TRACE_DURATION = 'nonpositive trace duration',\n  NO_WINDOW = 'no window',\n  NO_APP_ID = 'no app id',\n  NO_PROJECT_ID = 'no project id',\n  NO_API_KEY = 'no api key',\n  INVALID_CC_LOG = 'invalid cc log',\n  FB_NOT_DEFAULT = 'FB not default',\n  RC_NOT_OK = 'RC response not ok',\n  INVALID_ATTRIBUTE_NAME = 'invalid attribute name',\n  INVALID_ATTRIBUTE_VALUE = 'invalid attribute value',\n  INVALID_CUSTOM_METRIC_NAME = 'invalid custom metric name',\n  INVALID_STRING_MERGER_PARAMETER = 'invalid String merger input',\n  ALREADY_INITIALIZED = 'already initialized'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.TRACE_STARTED_BEFORE]: 'Trace {$traceName} was started before.',\n  [ErrorCode.TRACE_STOPPED_BEFORE]: 'Trace {$traceName} is not running.',\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]:\n    'Trace {$traceName} startTime should be positive.',\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]:\n    'Trace {$traceName} duration should be positive.',\n  [ErrorCode.NO_WINDOW]: 'Window is not available.',\n  [ErrorCode.NO_APP_ID]: 'App id is not available.',\n  [ErrorCode.NO_PROJECT_ID]: 'Project id is not available.',\n  [ErrorCode.NO_API_KEY]: 'Api key is not available.',\n  [ErrorCode.INVALID_CC_LOG]: 'Attempted to queue invalid cc event',\n  [ErrorCode.FB_NOT_DEFAULT]:\n    'Performance can only start when Firebase app instance is the default one.',\n  [ErrorCode.RC_NOT_OK]: 'RC response is not ok',\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]:\n    'Attribute name {$attributeName} is invalid.',\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]:\n    'Attribute value {$attributeValue} is invalid.',\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]:\n    'Custom metric name {$customMetricName} is invalid',\n  [ErrorCode.INVALID_STRING_MERGER_PARAMETER]:\n    'Input for String merger is invalid, contact support team to resolve.',\n  [ErrorCode.ALREADY_INITIALIZED]:\n    'initializePerformance() has already been called with ' +\n    'different options. To avoid this error, call initializePerformance() with the ' +\n    'same options as when it was originally called, or call getPerformance() to return the' +\n    ' already initialized instance.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.TRACE_STARTED_BEFORE]: { traceName: string };\n  [ErrorCode.TRACE_STOPPED_BEFORE]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]: { traceName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]: { attributeName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]: { attributeValue: string };\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]: { customMetricName: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { SERVICE_NAME } from '../constants';\n\nexport const consoleLogger = new Logger(SERVICE_NAME);\nconsoleLogger.logLevel = LogLevel.INFO;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { isIndexedDBAvailable, areCookiesEnabled } from '@firebase/util';\nimport { consoleLogger } from '../utils/console_logger';\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution,\n  onCLS as vitalsOnCLS,\n  onINP as vitalsOnINP,\n  onLCP as vitalsOnLCP\n} from 'web-vitals/attribution';\n\ndeclare global {\n  interface Window {\n    PerformanceObserver: typeof PerformanceObserver;\n    perfMetrics?: { onFirstInputDelay(fn: (fid: number) => void): void };\n  }\n}\n\nlet apiInstance: Api | undefined;\nlet windowInstance: Window | undefined;\n\nexport type EntryType =\n  | 'mark'\n  | 'measure'\n  | 'paint'\n  | 'resource'\n  | 'frame'\n  | 'navigation';\n\n/**\n * This class holds a reference to various browser related objects injected by\n * set methods.\n */\nexport class Api {\n  private readonly performance: Performance;\n  /** PerformanceObserver constructor function. */\n  private readonly PerformanceObserver: typeof PerformanceObserver;\n  private readonly windowLocation: Location;\n  readonly onFirstInputDelay?: (fn: (fid: number) => void) => void;\n  readonly onLCP: (fn: (metric: LCPMetricWithAttribution) => void) => void;\n  readonly onINP: (fn: (metric: INPMetricWithAttribution) => void) => void;\n  readonly onCLS: (fn: (metric: CLSMetricWithAttribution) => void) => void;\n  readonly localStorage?: Storage;\n  readonly document: Document;\n  readonly navigator: Navigator;\n\n  constructor(readonly window?: Window) {\n    if (!window) {\n      throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n    }\n    this.performance = window.performance;\n    this.PerformanceObserver = window.PerformanceObserver;\n    this.windowLocation = window.location;\n    this.navigator = window.navigator;\n    this.document = window.document;\n    if (this.navigator && this.navigator.cookieEnabled) {\n      // If user blocks cookies on the browser, accessing localStorage will\n      // throw an exception.\n      this.localStorage = window.localStorage;\n    }\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      this.onFirstInputDelay = window.perfMetrics.onFirstInputDelay;\n    }\n    this.onLCP = vitalsOnLCP;\n    this.onINP = vitalsOnINP;\n    this.onCLS = vitalsOnCLS;\n  }\n\n  getUrl(): string {\n    // Do not capture the string query part of url.\n    return this.windowLocation.href.split('?')[0];\n  }\n\n  mark(name: string): void {\n    if (!this.performance || !this.performance.mark) {\n      return;\n    }\n    this.performance.mark(name);\n  }\n\n  measure(measureName: string, mark1: string, mark2: string): void {\n    if (!this.performance || !this.performance.measure) {\n      return;\n    }\n    this.performance.measure(measureName, mark1, mark2);\n  }\n\n  getEntriesByType(type: EntryType): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByType) {\n      return [];\n    }\n    return this.performance.getEntriesByType(type);\n  }\n\n  getEntriesByName(name: string): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByName) {\n      return [];\n    }\n    return this.performance.getEntriesByName(name);\n  }\n\n  getTimeOrigin(): number {\n    // Polyfill the time origin with performance.timing.navigationStart.\n    return (\n      this.performance &&\n      (this.performance.timeOrigin || this.performance.timing.navigationStart)\n    );\n  }\n\n  requiredApisAvailable(): boolean {\n    if (!fetch || !Promise || !areCookiesEnabled()) {\n      consoleLogger.info(\n        'Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled.'\n      );\n      return false;\n    }\n\n    if (!isIndexedDBAvailable()) {\n      consoleLogger.info('IndexedDB is not supported by current browser');\n      return false;\n    }\n    return true;\n  }\n\n  setupObserver(\n    entryType: EntryType,\n    callback: (entry: PerformanceEntry) => void\n  ): void {\n    if (!this.PerformanceObserver) {\n      return;\n    }\n    const observer = new this.PerformanceObserver(list => {\n      for (const entry of list.getEntries()) {\n        // `entry` is a PerformanceEntry instance.\n        callback(entry);\n      }\n    });\n\n    // Start observing the entry types you care about.\n    observer.observe({ entryTypes: [entryType] });\n  }\n\n  static getInstance(): Api {\n    if (apiInstance === undefined) {\n      apiInstance = new Api(windowInstance);\n    }\n    return apiInstance;\n  }\n}\n\nexport function setupApi(window: Window): void {\n  windowInstance = window;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\nlet iid: string | undefined;\nlet authToken: string | undefined;\n\nexport function getIidPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const iidPromise = installationsService.getId();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  iidPromise.then((iidVal: string) => {\n    iid = iidVal;\n  });\n  return iidPromise;\n}\n\n// This method should be used after the iid is retrieved by getIidPromise method.\nexport function getIid(): string | undefined {\n  return iid;\n}\n\nexport function getAuthTokenPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const authTokenPromise = installationsService.getToken();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  authTokenPromise.then((authTokenVal: string) => {\n    authToken = authTokenVal;\n  });\n  return authTokenPromise;\n}\n\nexport function getAuthenticationToken(): string | undefined {\n  return authToken;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\n\nexport function mergeStrings(part1: string, part2: string): string {\n  const sizeDiff = part1.length - part2.length;\n  if (sizeDiff < 0 || sizeDiff > 1) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_STRING_MERGER_PARAMETER);\n  }\n\n  const resultArray = [];\n  for (let i = 0; i < part1.length; i++) {\n    resultArray.push(part1.charAt(i));\n    if (part2.length > i) {\n      resultArray.push(part2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { mergeStrings } from '../utils/string_merger';\n\nlet settingsServiceInstance: SettingsService | undefined;\n\nexport class SettingsService {\n  // The variable which controls logging of automatic traces and HTTP/S network monitoring.\n  instrumentationEnabled = true;\n\n  // The variable which controls logging of custom traces.\n  dataCollectionEnabled = true;\n\n  // Configuration flags set through remote config.\n  loggingEnabled = false;\n  // Sampling rate between 0 and 1.\n  tracesSamplingRate = 1;\n  networkRequestsSamplingRate = 1;\n\n  // Address of logging service.\n  logEndPointUrl =\n    'https://firebaselogging.googleapis.com/v0cc/log?format=json_proto';\n  // Performance event transport endpoint URL which should be compatible with proto3.\n  // New Address for transport service, not configurable via Remote Config.\n  flTransportEndpointUrl = mergeStrings(\n    'hts/frbslgigp.ogepscmv/ieo/eaylg',\n    'tp:/ieaeogn-agolai.o/1frlglgc/o'\n  );\n\n  transportKey = mergeStrings('AzSC8r6ReiGqFMyfvgow', 'Iayx0u-XT3vksVM-pIV');\n\n  // Source type for performance event logs.\n  logSource = 462;\n\n  // Flags which control per session logging of traces and network requests.\n  logTraceAfterSampling = false;\n  logNetworkAfterSampling = false;\n\n  // TTL of config retrieved from remote config in hours.\n  configTimeToLive = 12;\n\n  getFlTransportFullUrl(): string {\n    return this.flTransportEndpointUrl.concat('?key=', this.transportKey);\n  }\n\n  static getInstance(): SettingsService {\n    if (settingsServiceInstance === undefined) {\n      settingsServiceInstance = new SettingsService();\n    }\n    return settingsServiceInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getAppId(firebaseApp: FirebaseApp): string {\n  const appId = firebaseApp.options?.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_APP_ID);\n  }\n  return appId;\n}\n\nexport function getProjectId(firebaseApp: FirebaseApp): string {\n  const projectId = firebaseApp.options?.projectId;\n  if (!projectId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_PROJECT_ID);\n  }\n  return projectId;\n}\n\nexport function getApiKey(firebaseApp: FirebaseApp): string {\n  const apiKey = firebaseApp.options?.apiKey;\n  if (!apiKey) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_API_KEY);\n  }\n  return apiKey;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n  CONFIG_LOCAL_STORAGE_KEY,\n  SDK_VERSION\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\n\nimport { Api } from './api_service';\nimport { getAuthTokenPromise } from './iid_service';\nimport { SettingsService } from './settings_service';\nimport { Performance<PERSON>ontroller } from '../controllers/perf';\nimport { getProjectId, getApiKey, getAppId } from '../utils/app_utils';\n\nconst REMOTE_CONFIG_SDK_VERSION = '0.0.1';\n\ninterface SecondaryConfig {\n  loggingEnabled?: boolean;\n  logSource?: number;\n  logEndPointUrl?: string;\n  transportKey?: string;\n  tracesSamplingRate?: number;\n  networkRequestsSamplingRate?: number;\n}\n\n// These values will be used if the remote config object is successfully\n// retrieved, but the template does not have these fields.\nconst DEFAULT_CONFIGS: SecondaryConfig = {\n  loggingEnabled: true\n};\n\n/* eslint-disable camelcase */\ninterface RemoteConfigTemplate {\n  fpr_enabled?: string;\n  fpr_log_source?: string;\n  fpr_log_endpoint_url?: string;\n  fpr_log_transport_key?: string;\n  fpr_log_transport_web_percent?: string;\n  fpr_vc_network_request_sampling_rate?: string;\n  fpr_vc_trace_sampling_rate?: string;\n  fpr_vc_session_sampling_rate?: string;\n}\n/* eslint-enable camelcase */\n\ninterface RemoteConfigResponse {\n  entries?: RemoteConfigTemplate;\n  state?: string;\n}\n\nconst FIS_AUTH_PREFIX = 'FIREBASE_INSTALLATIONS_AUTH';\n\nexport function getConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<void> {\n  const config = getStoredConfig();\n  if (config) {\n    processConfig(config);\n    return Promise.resolve();\n  }\n\n  return getRemoteConfig(performanceController, iid)\n    .then(processConfig)\n    .then(\n      config => storeConfig(config),\n      /** Do nothing for error, use defaults set in settings service. */\n      () => {}\n    );\n}\n\nfunction getStoredConfig(): RemoteConfigResponse | undefined {\n  const localStorage = Api.getInstance().localStorage;\n  if (!localStorage) {\n    return;\n  }\n  const expiryString = localStorage.getItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY);\n  if (!expiryString || !configValid(expiryString)) {\n    return;\n  }\n\n  const configStringified = localStorage.getItem(CONFIG_LOCAL_STORAGE_KEY);\n  if (!configStringified) {\n    return;\n  }\n  try {\n    const configResponse: RemoteConfigResponse = JSON.parse(configStringified);\n    return configResponse;\n  } catch {\n    return;\n  }\n}\n\nfunction storeConfig(config: RemoteConfigResponse | undefined): void {\n  const localStorage = Api.getInstance().localStorage;\n  if (!config || !localStorage) {\n    return;\n  }\n\n  localStorage.setItem(CONFIG_LOCAL_STORAGE_KEY, JSON.stringify(config));\n  localStorage.setItem(\n    CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n    String(\n      Date.now() +\n        SettingsService.getInstance().configTimeToLive * 60 * 60 * 1000\n    )\n  );\n}\n\nconst COULD_NOT_GET_CONFIG_MSG =\n  'Could not fetch config, will use default configs';\n\nfunction getRemoteConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<RemoteConfigResponse | undefined> {\n  // Perf needs auth token only to retrieve remote config.\n  return getAuthTokenPromise(performanceController.installations)\n    .then(authToken => {\n      const projectId = getProjectId(performanceController.app);\n      const apiKey = getApiKey(performanceController.app);\n      const configEndPoint = `https://firebaseremoteconfig.googleapis.com/v1/projects/${projectId}/namespaces/fireperf:fetch?key=${apiKey}`;\n      const request = new Request(configEndPoint, {\n        method: 'POST',\n        headers: { Authorization: `${FIS_AUTH_PREFIX} ${authToken}` },\n        /* eslint-disable camelcase */\n        body: JSON.stringify({\n          app_instance_id: iid,\n          app_instance_id_token: authToken,\n          app_id: getAppId(performanceController.app),\n          app_version: SDK_VERSION,\n          sdk_version: REMOTE_CONFIG_SDK_VERSION\n        })\n        /* eslint-enable camelcase */\n      });\n      return fetch(request).then(response => {\n        if (response.ok) {\n          return response.json() as RemoteConfigResponse;\n        }\n        // In case response is not ok. This will be caught by catch.\n        throw ERROR_FACTORY.create(ErrorCode.RC_NOT_OK);\n      });\n    })\n    .catch(() => {\n      consoleLogger.info(COULD_NOT_GET_CONFIG_MSG);\n      return undefined;\n    });\n}\n\n/**\n * Processes config coming either from calling RC or from local storage.\n * This method only runs if call is successful or config in storage\n * is valid.\n */\nfunction processConfig(\n  config?: RemoteConfigResponse\n): RemoteConfigResponse | undefined {\n  if (!config) {\n    return config;\n  }\n  const settingsServiceInstance = SettingsService.getInstance();\n  const entries = config.entries || {};\n  if (entries.fpr_enabled !== undefined) {\n    // TODO: Change the assignment of loggingEnabled once the received type is\n    // known.\n    settingsServiceInstance.loggingEnabled =\n      String(entries.fpr_enabled) === 'true';\n  } else if (DEFAULT_CONFIGS.loggingEnabled !== undefined) {\n    // Config retrieved successfully, but there is no fpr_enabled in template.\n    // Use secondary configs value.\n    settingsServiceInstance.loggingEnabled = DEFAULT_CONFIGS.loggingEnabled;\n  }\n  if (entries.fpr_log_source) {\n    settingsServiceInstance.logSource = Number(entries.fpr_log_source);\n  } else if (DEFAULT_CONFIGS.logSource) {\n    settingsServiceInstance.logSource = DEFAULT_CONFIGS.logSource;\n  }\n\n  if (entries.fpr_log_endpoint_url) {\n    settingsServiceInstance.logEndPointUrl = entries.fpr_log_endpoint_url;\n  } else if (DEFAULT_CONFIGS.logEndPointUrl) {\n    settingsServiceInstance.logEndPointUrl = DEFAULT_CONFIGS.logEndPointUrl;\n  }\n\n  // Key from Remote Config has to be non-empty string, otherwise use local value.\n  if (entries.fpr_log_transport_key) {\n    settingsServiceInstance.transportKey = entries.fpr_log_transport_key;\n  } else if (DEFAULT_CONFIGS.transportKey) {\n    settingsServiceInstance.transportKey = DEFAULT_CONFIGS.transportKey;\n  }\n\n  if (entries.fpr_vc_network_request_sampling_rate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate = Number(\n      entries.fpr_vc_network_request_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.networkRequestsSamplingRate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate =\n      DEFAULT_CONFIGS.networkRequestsSamplingRate;\n  }\n  if (entries.fpr_vc_trace_sampling_rate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate = Number(\n      entries.fpr_vc_trace_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.tracesSamplingRate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate =\n      DEFAULT_CONFIGS.tracesSamplingRate;\n  }\n  // Set the per session trace and network logging flags.\n  settingsServiceInstance.logTraceAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.tracesSamplingRate\n  );\n  settingsServiceInstance.logNetworkAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.networkRequestsSamplingRate\n  );\n  return config;\n}\n\nfunction configValid(expiry: string): boolean {\n  return Number(expiry) > Date.now();\n}\n\nfunction shouldLogAfterSampling(samplingRate: number): boolean {\n  return Math.random() <= samplingRate;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIidPromise } from './iid_service';\nimport { getConfig } from './remote_config_service';\nimport { Api } from './api_service';\nimport { PerformanceController } from '../controllers/perf';\n\nconst enum InitializationStatus {\n  notInitialized = 1,\n  initializationPending,\n  initialized\n}\n\nlet initializationStatus = InitializationStatus.notInitialized;\n\nlet initializationPromise: Promise<void> | undefined;\n\nexport function getInitializationPromise(\n  performanceController: PerformanceController\n): Promise<void> {\n  initializationStatus = InitializationStatus.initializationPending;\n\n  initializationPromise =\n    initializationPromise || initializePerf(performanceController);\n\n  return initializationPromise;\n}\n\nexport function isPerfInitialized(): boolean {\n  return initializationStatus === InitializationStatus.initialized;\n}\n\nfunction initializePerf(\n  performanceController: PerformanceController\n): Promise<void> {\n  return getDocumentReadyComplete()\n    .then(() => getIidPromise(performanceController.installations))\n    .then(iid => getConfig(performanceController, iid))\n    .then(\n      () => changeInitializationStatus(),\n      () => changeInitializationStatus()\n    );\n}\n\n/**\n * Returns a promise which resolves whenever the document readystate is complete or\n * immediately if it is called after page load complete.\n */\nfunction getDocumentReadyComplete(): Promise<void> {\n  const document = Api.getInstance().document;\n  return new Promise(resolve => {\n    if (document && document.readyState !== 'complete') {\n      const handler = (): void => {\n        if (document.readyState === 'complete') {\n          document.removeEventListener('readystatechange', handler);\n          resolve();\n        }\n      };\n      document.addEventListener('readystatechange', handler);\n    } else {\n      resolve();\n    }\n  });\n}\n\nfunction changeInitializationStatus(): void {\n  initializationStatus = InitializationStatus.initialized;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsService } from './settings_service';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst DEFAULT_SEND_INTERVAL_MS = 10 * 1000;\nconst INITIAL_SEND_TIME_DELAY_MS = 5.5 * 1000;\nconst MAX_EVENT_COUNT_PER_REQUEST = 1000;\nconst DEFAULT_REMAINING_TRIES = 3;\n\nlet remainingTries = DEFAULT_REMAINING_TRIES;\n\ninterface BatchEvent {\n  message: string;\n  eventTime: number;\n}\n\n/* eslint-disable camelcase */\n// CC/Fl accepted log format.\ninterface TransportBatchLogFormat {\n  request_time_ms: string;\n  client_info: ClientInfo;\n  log_source: number;\n  log_event: Log[];\n}\n\ninterface ClientInfo {\n  client_type: number;\n  js_client_info: {};\n}\n\ninterface Log {\n  source_extension_json_proto3: string;\n  event_time_ms: string;\n}\n/* eslint-enable camelcase */\n\nlet queue: BatchEvent[] = [];\n\nlet isTransportSetup: boolean = false;\n\nexport function setupTransportService(): void {\n  if (!isTransportSetup) {\n    processQueue(INITIAL_SEND_TIME_DELAY_MS);\n    isTransportSetup = true;\n  }\n}\n\n/**\n * Utilized by testing to clean up message queue and un-initialize transport service.\n */\nexport function resetTransportService(): void {\n  isTransportSetup = false;\n  queue = [];\n}\n\nfunction processQueue(timeOffset: number): void {\n  setTimeout(() => {\n    // If there is no remainingTries left, stop retrying.\n    if (remainingTries <= 0) {\n      return;\n    }\n\n    if (queue.length > 0) {\n      dispatchQueueEvents();\n    }\n    processQueue(DEFAULT_SEND_INTERVAL_MS);\n  }, timeOffset);\n}\n\nfunction dispatchQueueEvents(): void {\n  // Extract events up to the maximum cap of single logRequest from top of \"official queue\".\n  // The staged events will be used for current logRequest attempt, remaining events will be kept\n  // for next attempt.\n  const staged = queue.splice(0, MAX_EVENT_COUNT_PER_REQUEST);\n\n  /* eslint-disable camelcase */\n  // We will pass the JSON serialized event to the backend.\n  const log_event: Log[] = staged.map(evt => ({\n    source_extension_json_proto3: evt.message,\n    event_time_ms: String(evt.eventTime)\n  }));\n\n  const data: TransportBatchLogFormat = {\n    request_time_ms: String(Date.now()),\n    client_info: {\n      client_type: 1, // 1 is JS\n      js_client_info: {}\n    },\n    log_source: SettingsService.getInstance().logSource,\n    log_event\n  };\n  /* eslint-enable camelcase */\n\n  postToFlEndpoint(data)\n    .then(() => {\n      remainingTries = DEFAULT_REMAINING_TRIES;\n    })\n    .catch(() => {\n      // If the request fails for some reason, add the events that were attempted\n      // back to the primary queue to retry later.\n      queue = [...staged, ...queue];\n      remainingTries--;\n      consoleLogger.info(`Tries left: ${remainingTries}.`);\n      processQueue(DEFAULT_SEND_INTERVAL_MS);\n    });\n}\n\nfunction postToFlEndpoint(data: TransportBatchLogFormat): Promise<void> {\n  const flTransportFullUrl =\n    SettingsService.getInstance().getFlTransportFullUrl();\n  const body = JSON.stringify(data);\n\n  return navigator.sendBeacon && navigator.sendBeacon(flTransportFullUrl, body)\n    ? Promise.resolve()\n    : fetch(flTransportFullUrl, {\n        method: 'POST',\n        body,\n        keepalive: true\n      }).then();\n}\n\nfunction addToQueue(evt: BatchEvent): void {\n  if (!evt.eventTime || !evt.message) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_CC_LOG);\n  }\n  // Add the new event to the queue.\n  queue = [...queue, evt];\n}\n\n/** Log handler for cc service to send the performance logs to the server. */\nexport function transportHandler(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  serializer: (...args: any[]) => string\n): (...args: unknown[]) => void {\n  return (...args) => {\n    const message = serializer(...args);\n    addToQueue({\n      message,\n      eventTime: Date.now()\n    });\n  };\n}\n\n/**\n * Force flush the queued events. Useful at page unload time to ensure all\n * events are uploaded.\n */\nexport function flushQueuedEvents(): void {\n  while (queue.length > 0) {\n    dispatchQueueEvents();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIid } from './iid_service';\nimport { NetworkRequest } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { Api } from './api_service';\nimport { SettingsService } from './settings_service';\nimport {\n  getServiceWorkerStatus,\n  getVisibilityState,\n  getEffectiveConnectionType\n} from '../utils/attributes_utils';\nimport {\n  isPerfInitialized,\n  getInitializationPromise\n} from './initialization_service';\nimport { transport<PERSON><PERSON><PERSON>, flushQueuedEvents } from './transport_service';\nimport { SDK_VERSION } from '../constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { getAppId } from '../utils/app_utils';\n\nconst enum ResourceType {\n  NetworkRequest,\n  Trace\n}\n\n/* eslint-disable camelcase */\ninterface ApplicationInfo {\n  google_app_id: string;\n  app_instance_id?: string;\n  web_app_info: WebAppInfo;\n  application_process_state: number;\n}\n\ninterface WebAppInfo {\n  sdk_version: string;\n  page_url: string;\n  service_worker_status: number;\n  visibility_state: number;\n  effective_connection_type: number;\n}\n\ninterface PerfNetworkLog {\n  application_info: ApplicationInfo;\n  network_request_metric: NetworkRequestMetric;\n}\n\ninterface PerfTraceLog {\n  application_info: ApplicationInfo;\n  trace_metric: TraceMetric;\n}\n\ninterface NetworkRequestMetric {\n  url: string;\n  http_method: number;\n  http_response_code: number;\n  response_payload_bytes?: number;\n  client_start_time_us?: number;\n  time_to_response_initiated_us?: number;\n  time_to_response_completed_us?: number;\n}\n\ninterface TraceMetric {\n  name: string;\n  is_auto: boolean;\n  client_start_time_us: number;\n  duration_us: number;\n  counters?: { [key: string]: number };\n  custom_attributes?: { [key: string]: string };\n}\n\ninterface Logger {\n  send: (\n    resource: NetworkRequest | Trace,\n    resourceType: ResourceType\n  ) => void | undefined;\n  flush: () => void;\n}\n\nlet logger: Logger;\n//\n// This method is not called before initialization.\nfunction sendLog(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): void {\n  if (!logger) {\n    logger = {\n      send: transportHandler(serializer),\n      flush: flushQueuedEvents\n    };\n  }\n  logger.send(resource, resourceType);\n}\n\nexport function logTrace(trace: Trace): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log if trace is auto generated and instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled && trace.isAuto) {\n    return;\n  }\n  // Do not log if trace is custom and data collection is disabled.\n  if (!settingsService.dataCollectionEnabled && !trace.isAuto) {\n    return;\n  }\n  // Do not log if required apis are not available.\n  if (!Api.getInstance().requiredApisAvailable()) {\n    return;\n  }\n\n  if (isPerfInitialized()) {\n    sendTraceLog(trace);\n  } else {\n    // Custom traces can be used before the initialization but logging\n    // should wait until after.\n    getInitializationPromise(trace.performanceController).then(\n      () => sendTraceLog(trace),\n      () => sendTraceLog(trace)\n    );\n  }\n}\n\nexport function flushLogs(): void {\n  if (logger) {\n    logger.flush();\n  }\n}\n\nfunction sendTraceLog(trace: Trace): void {\n  if (!getIid()) {\n    return;\n  }\n\n  const settingsService = SettingsService.getInstance();\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logTraceAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(trace, ResourceType.Trace);\n}\n\nexport function logNetworkRequest(networkRequest: NetworkRequest): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log network requests if instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled) {\n    return;\n  }\n\n  // Do not log the js sdk's call to transport service domain to avoid unnecessary cycle.\n  // Need to blacklist both old and new endpoints to avoid migration gap.\n  const networkRequestUrl = networkRequest.url;\n\n  // Blacklist old log endpoint and new transport endpoint.\n  // Because Performance SDK doesn't instrument requests sent from SDK itself.\n  const logEndpointUrl = settingsService.logEndPointUrl.split('?')[0];\n  const flEndpointUrl = settingsService.flTransportEndpointUrl.split('?')[0];\n  if (\n    networkRequestUrl === logEndpointUrl ||\n    networkRequestUrl === flEndpointUrl\n  ) {\n    return;\n  }\n\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logNetworkAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(networkRequest, ResourceType.NetworkRequest);\n}\n\nfunction serializer(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): string {\n  if (resourceType === ResourceType.NetworkRequest) {\n    return serializeNetworkRequest(resource as NetworkRequest);\n  }\n  return serializeTrace(resource as Trace);\n}\n\nfunction serializeNetworkRequest(networkRequest: NetworkRequest): string {\n  const networkRequestMetric: NetworkRequestMetric = {\n    url: networkRequest.url,\n    http_method: networkRequest.httpMethod || 0,\n    http_response_code: 200,\n    response_payload_bytes: networkRequest.responsePayloadBytes,\n    client_start_time_us: networkRequest.startTimeUs,\n    time_to_response_initiated_us: networkRequest.timeToResponseInitiatedUs,\n    time_to_response_completed_us: networkRequest.timeToResponseCompletedUs\n  };\n  const perfMetric: PerfNetworkLog = {\n    application_info: getApplicationInfo(\n      networkRequest.performanceController.app\n    ),\n    network_request_metric: networkRequestMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction serializeTrace(trace: Trace): string {\n  const traceMetric: TraceMetric = {\n    name: trace.name,\n    is_auto: trace.isAuto,\n    client_start_time_us: trace.startTimeUs,\n    duration_us: trace.durationUs\n  };\n\n  if (Object.keys(trace.counters).length !== 0) {\n    traceMetric.counters = trace.counters;\n  }\n  const customAttributes = trace.getAttributes();\n  if (Object.keys(customAttributes).length !== 0) {\n    traceMetric.custom_attributes = customAttributes;\n  }\n\n  const perfMetric: PerfTraceLog = {\n    application_info: getApplicationInfo(trace.performanceController.app),\n    trace_metric: traceMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction getApplicationInfo(firebaseApp: FirebaseApp): ApplicationInfo {\n  return {\n    google_app_id: getAppId(firebaseApp),\n    app_instance_id: getIid(),\n    web_app_info: {\n      sdk_version: SDK_VERSION,\n      page_url: Api.getInstance().getUrl(),\n      service_worker_status: getServiceWorkerStatus(),\n      visibility_state: getVisibilityState(),\n      effective_connection_type: getEffectiveConnectionType()\n    },\n    application_process_state: 0\n  };\n}\n\n/* eslint-enable camelcase */\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\nimport { logNetworkRequest } from '../services/perf_logger';\nimport { PerformanceController } from '../controllers/perf';\n\n// The order of values of this enum should not be changed.\nexport const enum HttpMethod {\n  HTTP_METHOD_UNKNOWN = 0,\n  GET = 1,\n  PUT = 2,\n  POST = 3,\n  DELETE = 4,\n  HEAD = 5,\n  PATCH = 6,\n  OPTIONS = 7,\n  TRACE = 8,\n  CONNECT = 9\n}\n\n// Durations are in microseconds.\nexport interface NetworkRequest {\n  performanceController: PerformanceController;\n  url: string;\n  httpMethod?: HttpMethod;\n  requestPayloadBytes?: number;\n  responsePayloadBytes?: number;\n  httpResponseCode?: number;\n  responseContentType?: string;\n  startTimeUs?: number;\n  timeToRequestCompletedUs?: number;\n  timeToResponseInitiatedUs?: number;\n  timeToResponseCompletedUs?: number;\n}\n\nexport function createNetworkRequestEntry(\n  performanceController: PerformanceController,\n  entry: PerformanceEntry\n): void {\n  const performanceEntry = entry as PerformanceResourceTiming;\n  if (!performanceEntry || performanceEntry.responseStart === undefined) {\n    return;\n  }\n  const timeOrigin = Api.getInstance().getTimeOrigin();\n  const startTimeUs = Math.floor(\n    (performanceEntry.startTime + timeOrigin) * 1000\n  );\n  const timeToResponseInitiatedUs = performanceEntry.responseStart\n    ? Math.floor(\n        (performanceEntry.responseStart - performanceEntry.startTime) * 1000\n      )\n    : undefined;\n  const timeToResponseCompletedUs = Math.floor(\n    (performanceEntry.responseEnd - performanceEntry.startTime) * 1000\n  );\n  // Remove the query params from logged network request url.\n  const url = performanceEntry.name && performanceEntry.name.split('?')[0];\n  const networkRequest: NetworkRequest = {\n    performanceController,\n    url,\n    responsePayloadBytes: performanceEntry.transferSize,\n    startTimeUs,\n    timeToResponseInitiatedUs,\n    timeToResponseCompletedUs\n  };\n\n  logNetworkRequest(networkRequest);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst MAX_METRIC_NAME_LENGTH = 100;\nconst RESERVED_AUTO_PREFIX = '_';\nconst oobMetrics = [\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME\n];\n\n/**\n * Returns true if the metric is custom and does not start with reserved prefix, or if\n * the metric is one of out of the box page load trace metrics.\n */\nexport function isValidMetricName(name: string, traceName?: string): boolean {\n  if (name.length === 0 || name.length > MAX_METRIC_NAME_LENGTH) {\n    return false;\n  }\n  return (\n    (traceName &&\n      traceName.startsWith(OOB_TRACE_PAGE_LOAD_PREFIX) &&\n      oobMetrics.indexOf(name) > -1) ||\n    !name.startsWith(RESERVED_AUTO_PREFIX)\n  );\n}\n\n/**\n * Converts the provided value to an integer value to be used in case of a metric.\n * @param providedValue Provided number value of the metric that needs to be converted to an integer.\n *\n * @returns Converted integer number to be set for the metric.\n */\nexport function convertMetricValueToInteger(providedValue: number): number {\n  const valueAsInteger: number = Math.floor(providedValue);\n  if (valueAsInteger < providedValue) {\n    consoleLogger.info(\n      `Metric value should be an Integer, setting the value as : ${valueAsInteger}.`\n    );\n  }\n  return valueAsInteger;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  TRACE_START_MARK_PREFIX,\n  TRACE_STOP_MARK_PREFIX,\n  TRACE_MEASURE_PREFIX,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME\n} from '../constants';\nimport { Api } from '../services/api_service';\nimport { logTrace, flushLogs } from '../services/perf_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport {\n  isValidCustomAttributeName,\n  isValidCustomAttributeValue\n} from '../utils/attributes_utils';\nimport {\n  isValidMetricName,\n  convertMetricValueToInteger\n} from '../utils/metric_utils';\nimport { PerformanceTrace } from '../public_types';\nimport { PerformanceController } from '../controllers/perf';\nimport { CoreVitalMetric, WebVitalMetrics } from './web_vitals';\n\nconst enum TraceState {\n  UNINITIALIZED = 1,\n  RUNNING,\n  TERMINATED\n}\n\nexport class Trace implements PerformanceTrace {\n  private state: TraceState = TraceState.UNINITIALIZED;\n  startTimeUs!: number;\n  durationUs!: number;\n  private customAttributes: { [key: string]: string } = {};\n  counters: { [counterName: string]: number } = {};\n  private api = Api.getInstance();\n  private randomId = Math.floor(Math.random() * 1000000);\n  private traceStartMark!: string;\n  private traceStopMark!: string;\n  private traceMeasure!: string;\n\n  /**\n   * @param performanceController The performance controller running.\n   * @param name The name of the trace.\n   * @param isAuto If the trace is auto-instrumented.\n   * @param traceMeasureName The name of the measure marker in user timing specification. This field\n   * is only set when the trace is built for logging when the user directly uses the user timing\n   * api (performance.mark and performance.measure).\n   */\n  constructor(\n    readonly performanceController: PerformanceController,\n    readonly name: string,\n    readonly isAuto = false,\n    traceMeasureName?: string\n  ) {\n    if (!this.isAuto) {\n      this.traceStartMark = `${TRACE_START_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceStopMark = `${TRACE_STOP_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceMeasure =\n        traceMeasureName ||\n        `${TRACE_MEASURE_PREFIX}-${this.randomId}-${this.name}`;\n\n      if (traceMeasureName) {\n        // For the case of direct user timing traces, no start stop will happen. The measure object\n        // is already available.\n        this.calculateTraceMetrics();\n      }\n    }\n  }\n\n  /**\n   * Starts a trace. The measurement of the duration starts at this point.\n   */\n  start(): void {\n    if (this.state !== TraceState.UNINITIALIZED) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STARTED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.api.mark(this.traceStartMark);\n    this.state = TraceState.RUNNING;\n  }\n\n  /**\n   * Stops the trace. The measurement of the duration of the trace stops at this point and trace\n   * is logged.\n   */\n  stop(): void {\n    if (this.state !== TraceState.RUNNING) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STOPPED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.state = TraceState.TERMINATED;\n    this.api.mark(this.traceStopMark);\n    this.api.measure(\n      this.traceMeasure,\n      this.traceStartMark,\n      this.traceStopMark\n    );\n    this.calculateTraceMetrics();\n    logTrace(this);\n  }\n\n  /**\n   * Records a trace with predetermined values. If this method is used a trace is created and logged\n   * directly. No need to use start and stop methods.\n   * @param startTime Trace start time since epoch in millisec\n   * @param duration The duration of the trace in millisec\n   * @param options An object which can optionally hold maps of custom metrics and custom attributes\n   */\n  record(\n    startTime: number,\n    duration: number,\n    options?: {\n      metrics?: { [key: string]: number };\n      attributes?: { [key: string]: string };\n    }\n  ): void {\n    if (startTime <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_START_TIME, {\n        traceName: this.name\n      });\n    }\n    if (duration <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_DURATION, {\n        traceName: this.name\n      });\n    }\n\n    this.durationUs = Math.floor(duration * 1000);\n    this.startTimeUs = Math.floor(startTime * 1000);\n    if (options && options.attributes) {\n      this.customAttributes = { ...options.attributes };\n    }\n    if (options && options.metrics) {\n      for (const metricName of Object.keys(options.metrics)) {\n        if (!isNaN(Number(options.metrics[metricName]))) {\n          this.counters[metricName] = Math.floor(\n            Number(options.metrics[metricName])\n          );\n        }\n      }\n    }\n    logTrace(this);\n  }\n\n  /**\n   * Increments a custom metric by a certain number or 1 if number not specified. Will create a new\n   * custom metric if one with the given name does not exist. The value will be floored down to an\n   * integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Increment by value\n   */\n  incrementMetric(counter: string, numAsInteger = 1): void {\n    if (this.counters[counter] === undefined) {\n      this.putMetric(counter, numAsInteger);\n    } else {\n      this.putMetric(counter, this.counters[counter] + numAsInteger);\n    }\n  }\n\n  /**\n   * Sets a custom metric to a specified value. Will create a new custom metric if one with the\n   * given name does not exist. The value will be floored down to an integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Set custom metric to this value\n   */\n  putMetric(counter: string, numAsInteger: number): void {\n    if (isValidMetricName(counter, this.name)) {\n      this.counters[counter] = convertMetricValueToInteger(numAsInteger ?? 0);\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_CUSTOM_METRIC_NAME, {\n        customMetricName: counter\n      });\n    }\n  }\n\n  /**\n   * Returns the value of the custom metric by that name. If a custom metric with that name does\n   * not exist will return zero.\n   * @param counter\n   */\n  getMetric(counter: string): number {\n    return this.counters[counter] || 0;\n  }\n\n  /**\n   * Sets a custom attribute of a trace to a certain value.\n   * @param attr\n   * @param value\n   */\n  putAttribute(attr: string, value: string): void {\n    const isValidName = isValidCustomAttributeName(attr);\n    const isValidValue = isValidCustomAttributeValue(value);\n    if (isValidName && isValidValue) {\n      this.customAttributes[attr] = value;\n      return;\n    }\n    // Throw appropriate error when the attribute name or value is invalid.\n    if (!isValidName) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_NAME, {\n        attributeName: attr\n      });\n    }\n    if (!isValidValue) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_VALUE, {\n        attributeValue: value\n      });\n    }\n  }\n\n  /**\n   * Retrieves the value a custom attribute of a trace is set to.\n   * @param attr\n   */\n  getAttribute(attr: string): string | undefined {\n    return this.customAttributes[attr];\n  }\n\n  removeAttribute(attr: string): void {\n    if (this.customAttributes[attr] === undefined) {\n      return;\n    }\n    delete this.customAttributes[attr];\n  }\n\n  getAttributes(): { [key: string]: string } {\n    return { ...this.customAttributes };\n  }\n\n  private setStartTime(startTime: number): void {\n    this.startTimeUs = startTime;\n  }\n\n  private setDuration(duration: number): void {\n    this.durationUs = duration;\n  }\n\n  /**\n   * Calculates and assigns the duration and start time of the trace using the measure performance\n   * entry.\n   */\n  private calculateTraceMetrics(): void {\n    const perfMeasureEntries = this.api.getEntriesByName(this.traceMeasure);\n    const perfMeasureEntry = perfMeasureEntries && perfMeasureEntries[0];\n    if (perfMeasureEntry) {\n      this.durationUs = Math.floor(perfMeasureEntry.duration * 1000);\n      this.startTimeUs = Math.floor(\n        (perfMeasureEntry.startTime + this.api.getTimeOrigin()) * 1000\n      );\n    }\n  }\n\n  /**\n   * @param navigationTimings A single element array which contains the navigationTIming object of\n   * the page load\n   * @param paintTimings A array which contains paintTiming object of the page load\n   * @param firstInputDelay First input delay in millisec\n   */\n  static createOobTrace(\n    performanceController: PerformanceController,\n    navigationTimings: PerformanceNavigationTiming[],\n    paintTimings: PerformanceEntry[],\n    webVitalMetrics: WebVitalMetrics,\n    firstInputDelay?: number\n  ): void {\n    const route = Api.getInstance().getUrl();\n    if (!route) {\n      return;\n    }\n    const trace = new Trace(\n      performanceController,\n      OOB_TRACE_PAGE_LOAD_PREFIX + route,\n      true\n    );\n    const timeOriginUs = Math.floor(Api.getInstance().getTimeOrigin() * 1000);\n    trace.setStartTime(timeOriginUs);\n\n    // navigationTimings includes only one element.\n    if (navigationTimings && navigationTimings[0]) {\n      trace.setDuration(Math.floor(navigationTimings[0].duration * 1000));\n      trace.putMetric(\n        'domInteractive',\n        Math.floor(navigationTimings[0].domInteractive * 1000)\n      );\n      trace.putMetric(\n        'domContentLoadedEventEnd',\n        Math.floor(navigationTimings[0].domContentLoadedEventEnd * 1000)\n      );\n      trace.putMetric(\n        'loadEventEnd',\n        Math.floor(navigationTimings[0].loadEventEnd * 1000)\n      );\n    }\n\n    const FIRST_PAINT = 'first-paint';\n    const FIRST_CONTENTFUL_PAINT = 'first-contentful-paint';\n    if (paintTimings) {\n      const firstPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_PAINT\n      );\n      if (firstPaint && firstPaint.startTime) {\n        trace.putMetric(\n          FIRST_PAINT_COUNTER_NAME,\n          Math.floor(firstPaint.startTime * 1000)\n        );\n      }\n      const firstContentfulPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_CONTENTFUL_PAINT\n      );\n      if (firstContentfulPaint && firstContentfulPaint.startTime) {\n        trace.putMetric(\n          FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n          Math.floor(firstContentfulPaint.startTime * 1000)\n        );\n      }\n\n      if (firstInputDelay) {\n        trace.putMetric(\n          FIRST_INPUT_DELAY_COUNTER_NAME,\n          Math.floor(firstInputDelay * 1000)\n        );\n      }\n    }\n\n    this.addWebVitalMetric(\n      trace,\n      LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n      LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.lcp\n    );\n    this.addWebVitalMetric(\n      trace,\n      CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n      CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME,\n      webVitalMetrics.cls\n    );\n    this.addWebVitalMetric(\n      trace,\n      INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n      INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.inp\n    );\n\n    // Page load logs are sent at unload time and so should be logged and\n    // flushed immediately.\n    logTrace(trace);\n    flushLogs();\n  }\n\n  static addWebVitalMetric(\n    trace: Trace,\n    metricKey: string,\n    attributeKey: string,\n    metric?: CoreVitalMetric\n  ): void {\n    if (metric) {\n      trace.putMetric(metricKey, Math.floor(metric.value * 1000));\n      if (metric.elementAttribution) {\n        trace.putAttribute(attributeKey, metric.elementAttribution);\n      }\n    }\n  }\n\n  static createUserTimingTrace(\n    performanceController: PerformanceController,\n    measureName: string\n  ): void {\n    const trace = new Trace(\n      performanceController,\n      measureName,\n      false,\n      measureName\n    );\n    logTrace(trace);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution\n} from 'web-vitals/attribution';\n\nimport { TRACE_MEASURE_PREFIX } from '../constants';\nimport { PerformanceController } from '../controllers/perf';\nimport { createNetworkRequestEntry } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { WebVitalMetrics } from '../resources/web_vitals';\n\nimport { Api } from './api_service';\nimport { getIid } from './iid_service';\n\nlet webVitalMetrics: WebVitalMetrics = {};\nlet sentPageLoadTrace: boolean = false;\nlet firstInputDelay: number | undefined;\n\nexport function setupOobResources(\n  performanceController: PerformanceController\n): void {\n  // Do not initialize unless iid is available.\n  if (!getIid()) {\n    return;\n  }\n  // The load event might not have fired yet, and that means performance\n  // navigation timing object has a duration of 0. The setup should run after\n  // all current tasks in js queue.\n  setTimeout(() => setupOobTraces(performanceController), 0);\n  setTimeout(() => setupNetworkRequests(performanceController), 0);\n  setTimeout(() => setupUserTimingTraces(performanceController), 0);\n}\n\nfunction setupNetworkRequests(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  const resources = api.getEntriesByType('resource');\n  for (const resource of resources) {\n    createNetworkRequestEntry(performanceController, resource);\n  }\n  api.setupObserver('resource', entry =>\n    createNetworkRequestEntry(performanceController, entry)\n  );\n}\n\nfunction setupOobTraces(performanceController: PerformanceController): void {\n  const api = Api.getInstance();\n  // Better support for Safari\n  if ('onpagehide' in window) {\n    api.document.addEventListener('pagehide', () =>\n      sendOobTrace(performanceController)\n    );\n  } else {\n    api.document.addEventListener('unload', () =>\n      sendOobTrace(performanceController)\n    );\n  }\n  api.document.addEventListener('visibilitychange', () => {\n    if (api.document.visibilityState === 'hidden') {\n      sendOobTrace(performanceController);\n    }\n  });\n\n  if (api.onFirstInputDelay) {\n    api.onFirstInputDelay((fid: number) => {\n      firstInputDelay = fid;\n    });\n  }\n\n  api.onLCP((metric: LCPMetricWithAttribution) => {\n    webVitalMetrics.lcp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.element\n    };\n  });\n  api.onCLS((metric: CLSMetricWithAttribution) => {\n    webVitalMetrics.cls = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.largestShiftTarget\n    };\n  });\n  api.onINP((metric: INPMetricWithAttribution) => {\n    webVitalMetrics.inp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.interactionTarget\n    };\n  });\n}\n\nfunction setupUserTimingTraces(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  // Run through the measure performance entries collected up to this point.\n  const measures = api.getEntriesByType('measure');\n  for (const measure of measures) {\n    createUserTimingTrace(performanceController, measure);\n  }\n  // Setup an observer to capture the measures from this point on.\n  api.setupObserver('measure', entry =>\n    createUserTimingTrace(performanceController, entry)\n  );\n}\n\nfunction createUserTimingTrace(\n  performanceController: PerformanceController,\n  measure: PerformanceEntry\n): void {\n  const measureName = measure.name;\n  // Do not create a trace, if the user timing marks and measures are created by\n  // the sdk itself.\n  if (\n    measureName.substring(0, TRACE_MEASURE_PREFIX.length) ===\n    TRACE_MEASURE_PREFIX\n  ) {\n    return;\n  }\n  Trace.createUserTimingTrace(performanceController, measureName);\n}\n\nfunction sendOobTrace(performanceController: PerformanceController): void {\n  if (!sentPageLoadTrace) {\n    sentPageLoadTrace = true;\n    const api = Api.getInstance();\n    const navigationTimings = api.getEntriesByType(\n      'navigation'\n    ) as PerformanceNavigationTiming[];\n    const paintTimings = api.getEntriesByType('paint');\n\n    // On page unload web vitals may be updated so queue the oob trace creation\n    // so that these updates have time to be included.\n    setTimeout(() => {\n      Trace.createOobTrace(\n        performanceController,\n        navigationTimings,\n        paintTimings,\n        webVitalMetrics,\n        firstInputDelay\n      );\n    }, 0);\n  }\n}\n\n/**\n * This service will only export the page load trace once. This function allows\n * resetting it for unit tests\n */\nexport function resetForUnitTests(): void {\n  sentPageLoadTrace = false;\n  firstInputDelay = undefined;\n  webVitalMetrics = {};\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { setupOobResources } from '../services/oob_resources_service';\nimport { SettingsService } from '../services/settings_service';\nimport { getInitializationPromise } from '../services/initialization_service';\nimport { Api } from '../services/api_service';\nimport { FirebaseApp } from '@firebase/app';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { PerformanceSettings, FirebasePerformance } from '../public_types';\nimport { validateIndexedDBOpenable } from '@firebase/util';\nimport { setupTransportService } from '../services/transport_service';\nimport { consoleLogger } from '../utils/console_logger';\n\nexport class PerformanceController implements FirebasePerformance {\n  private initialized: boolean = false;\n\n  constructor(\n    readonly app: FirebaseApp,\n    readonly installations: _FirebaseInstallationsInternal\n  ) {}\n\n  /**\n   * This method *must* be called internally as part of creating a\n   * PerformanceController instance.\n   *\n   * Currently it's not possible to pass the settings object through the\n   * constructor using Components, so this method exists to be called with the\n   * desired settings, to ensure nothing is collected without the user's\n   * consent.\n   */\n  _init(settings?: PerformanceSettings): void {\n    if (this.initialized) {\n      return;\n    }\n\n    if (settings?.dataCollectionEnabled !== undefined) {\n      this.dataCollectionEnabled = settings.dataCollectionEnabled;\n    }\n    if (settings?.instrumentationEnabled !== undefined) {\n      this.instrumentationEnabled = settings.instrumentationEnabled;\n    }\n\n    if (Api.getInstance().requiredApisAvailable()) {\n      validateIndexedDBOpenable()\n        .then(isAvailable => {\n          if (isAvailable) {\n            setupTransportService();\n            getInitializationPromise(this).then(\n              () => setupOobResources(this),\n              () => setupOobResources(this)\n            );\n            this.initialized = true;\n          }\n        })\n        .catch(error => {\n          consoleLogger.info(`Environment doesn't support IndexedDB: ${error}`);\n        });\n    } else {\n      consoleLogger.info(\n        'Firebase Performance cannot start if the browser does not support ' +\n          '\"Fetch\" and \"Promise\", or cookies are disabled.'\n      );\n    }\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    SettingsService.getInstance().instrumentationEnabled = val;\n  }\n  get instrumentationEnabled(): boolean {\n    return SettingsService.getInstance().instrumentationEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    SettingsService.getInstance().dataCollectionEnabled = val;\n  }\n  get dataCollectionEnabled(): boolean {\n    return SettingsService.getInstance().dataCollectionEnabled;\n  }\n}\n", "/**\n * The Firebase Performance Monitoring Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebasePerformance,\n  PerformanceSettings,\n  PerformanceTrace\n} from './public_types';\nimport { ERROR_FACTORY, ErrorCode } from './utils/errors';\nimport { setupApi } from './services/api_service';\nimport { PerformanceController } from './controllers/perf';\nimport {\n  _registerComponent,\n  _getProvider,\n  registerVersion,\n  FirebaseApp,\n  getApp\n} from '@firebase/app';\nimport {\n  InstanceFactory,\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { Trace } from './resources/trace';\nimport '@firebase/installations';\nimport { deepEqual, getModularInstance } from '@firebase/util';\n\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @public\n */\nexport function getPerformance(\n  app: FirebaseApp = getApp()\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n  const perfInstance = provider.getImmediate() as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app. Can only be called once.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param settings - Optional settings for the {@link FirebasePerformance} instance.\n * @public\n */\nexport function initializePerformance(\n  app: FirebaseApp,\n  settings?: PerformanceSettings\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n\n  // throw if an instance was already created.\n  // It could happen if initializePerformance() is called more than once, or getPerformance() is called first.\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialSettings = provider.getOptions() as PerformanceSettings;\n    if (deepEqual(initialSettings, settings ?? {})) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n    }\n  }\n\n  const perfInstance = provider.initialize({\n    options: settings\n  }) as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a new `PerformanceTrace` instance.\n * @param performance - The {@link FirebasePerformance} instance to use.\n * @param name - The name of the trace.\n * @public\n */\nexport function trace(\n  performance: FirebasePerformance,\n  name: string\n): PerformanceTrace {\n  performance = getModularInstance(performance);\n  return new Trace(performance as PerformanceController, name);\n}\n\nconst factory: InstanceFactory<'performance'> = (\n  container: ComponentContainer,\n  { options: settings }: { options?: PerformanceSettings }\n) => {\n  // Dependencies\n  const app = container.getProvider('app').getImmediate();\n  const installations = container\n    .getProvider('installations-internal')\n    .getImmediate();\n\n  if (app.name !== DEFAULT_ENTRY_NAME) {\n    throw ERROR_FACTORY.create(ErrorCode.FB_NOT_DEFAULT);\n  }\n  if (typeof window === 'undefined') {\n    throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n  }\n  setupApi(window);\n  const perfInstance = new PerformanceController(app, installations);\n  perfInstance._init(settings);\n\n  return perfInstance;\n};\n\nfunction registerPerformance(): void {\n  _registerComponent(\n    new Component('performance', factory, ComponentType.PUBLIC)\n  );\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterPerformance();\n\nexport { FirebasePerformance, PerformanceSettings, PerformanceTrace };\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  FirebasePerformance,\n  // The PerformanceTrace type has not changed between modular and non-modular packages.\n  PerformanceTrace\n} from '@firebase/performance';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\n\nexport class PerformanceCompatImpl\n  implements FirebasePerformanceCompat, _FirebaseService\n{\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FirebasePerformance\n  ) {}\n\n  get instrumentationEnabled(): boolean {\n    return this._delegate.instrumentationEnabled;\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    this._delegate.instrumentationEnabled = val;\n  }\n\n  get dataCollectionEnabled(): boolean {\n    return this._delegate.dataCollectionEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    this._delegate.dataCollectionEnabled = val;\n  }\n\n  trace(traceName: string): PerformanceTrace {\n    return trace(this._delegate, traceName);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n"], "names": ["LogLevel", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "r", "t", "document", "readyState", "e", "n", "domInteractive", "domContentLoadedEventStart", "domComplete", "a", "nodeType", "id", "i", "classList", "trim", "length", "parentNode", "D", "entries", "reduce", "sources", "o", "find", "node", "largestShiftTarget", "largestShiftTime", "startTime", "largestShiftValue", "largestShiftSource", "largestShiftEntry", "loadState", "assign", "attribution", "v", "for<PERSON>ach", "hadRecentInput", "push", "f", "c", "d", "l", "M", "reportAllChanges", "p", "takeRecords", "u", "m", "setTimeout", "b", "S", "disconnect", "firstHiddenTime", "Math", "max", "s", "L", "performance", "timeStamp", "z", "self", "PerformanceEventTiming", "P", "N", "H", "j", "latency", "durationThreshold", "W", "observe", "buffered", "R", "F", "B", "O", "clear", "Q", "U", "concat", "X", "Y", "J", "size", "has", "delete", "map", "G", "get", "V", "filter", "includes", "Set", "nt", "processingEnd", "add", "K", "rt", "processingStart", "sort", "target", "interactionId", "duration", "apply", "interaction<PERSON>arget", "interactionTargetElement", "interactionType", "startsWith", "interactionTime", "nextPaintTime", "processedEventEntries", "longAnimationFrameEntries", "inputDelay", "processingDuration", "presentationDelay", "ot", "timeToFirstByte", "resourceLoadDelay", "resourceLoadDuration", "elementRenderDelay", "activationStart", "url", "getEntriesByType", "responseStart", "requestStart", "responseEnd", "element", "navigationEntry", "lcpEntry", "lcpResourceEntry", "slice", "it", "at", "addEventListener", "once", "capture", "nodeName", "toLowerCase", "toUpperCase", "persisted", "prerendering", "wasDiscarded", "rating", "delta", "floor", "random", "navigationType", "PerformanceObserver", "supportedEntryTypes", "Promise", "resolve", "then", "getEntries", "requestAnimationFrame", "visibilityState", "g", "h", "T", "E", "y", "removeEventListener", "x", "I", "k", "A", "min", "interactionCount", "Map", "q", "entryType", "set", "splice", "requestIdleCallback", "WeakMap", "abs", "renderTime", "instanceOfAny", "object", "constructors", "some", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "let", "idbProxyTraps", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "unwrap", "storeNames", "tx", "call", "transformCachableValue", "done", "reject", "unlisten", "complete", "DOMException", "IDBObjectStore", "IDBIndex", "Proxy", "request", "newValue", "IDBRequest", "promise", "success", "result", "catch", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "all", "oldTraps", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "version", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "ERROR_FACTORY", "missing-app-config-values", "not-registered", "installation-not-found", "request-failed", "app-offline", "delete-pending-registration", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "errorData", "await", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Content-Type", "Accept", "x-goog-api-key", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "retryIfServerError", "fn", "sleep", "ms", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "fid", "crypto", "msCrypto", "getRandomValues", "array", "btoa", "fromCharCode", "substr", "test", "_a", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "channel", "broadcastChannel", "BroadcastChannel", "onmessage", "postMessage", "close", "callbacks", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "oldValue", "put", "remove", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "clearTimedOutRequest", "registrationStatus", "entryWithPromise", "inProgressEntry", "navigator", "onLine", "registrationTime", "registeredInstallationEntry", "heartbeatServiceProvider", "endpoint", "body", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "registrationPromiseWithError", "entry", "updateInstallationRequest", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "updateAuthTokenRequest", "inProgressAuthToken", "requestTime", "updatedInstallationEntry", "getToken", "installationsImpl", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "app", "container", "get<PERSON><PERSON><PERSON>", "options", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "_registerComponent", "registerVersion", "SDK_VERSION", "TRACE_MEASURE_PREFIX", "CONFIG_LOCAL_STORAGE_KEY", "CONFIG_EXPIRY_LOCAL_STORAGE_KEY", "VisibilityState", "firebaseInstance", "SERVICE_NAME", "trace started", "trace stopped", "nonpositive trace startTime", "nonpositive trace duration", "no window", "no app id", "no project id", "no api key", "invalid cc log", "FB not default", "RC response not ok", "invalid attribute name", "invalid attribute value", "invalid custom metric name", "invalid String merger input", "already initialized", "consoleLogger", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "apiInstance", "windowInstance", "Api", "window", "windowLocation", "location", "cookieEnabled", "localStorage", "perfMetrics", "onFirstInputDelay", "onLCP", "vitalsOnLCP", "onINP", "vitalsOnINP", "onCLS", "vitalsOnCLS", "getUrl", "href", "split", "mark", "measure", "measureName", "mark1", "mark2", "getEntriesByName", "getTime<PERSON>rigin", "<PERSON><PERSON><PERSON><PERSON>", "timing", "navigationStart", "requiredApisAvailable", "isIndexedDBAvailable", "setupObserver", "list", "entryTypes", "getInstance", "iid", "mergeStrings", "part1", "part2", "sizeDiff", "resultArray", "char<PERSON>t", "join", "settingsServiceInstance", "SettingsService", "instrumentationEnabled", "dataCollectionEnabled", "loggingEnabled", "tracesSamplingRate", "networkRequestsSamplingRate", "logEndPointUrl", "flTransportEndpointUrl", "transportKey", "logSource", "logTraceAfterSampling", "logNetworkAfterSampling", "configTimeToLive", "getFlTransportFullUrl", "RESERVED_ATTRIBUTE_PREFIXES", "ATTRIBUTE_FORMAT_REGEX", "RegExp", "getAppId", "firebaseApp", "REMOTE_CONFIG_SDK_VERSION", "DEFAULT_CONFIGS", "FIS_AUTH_PREFIX", "getConfig", "performanceController", "config", "expiryString", "getItem", "expiry", "configStringified", "parse", "processConfig", "installationsService", "authTokenPromise", "Request", "Authorization", "app_instance_id", "app_instance_id_token", "app_id", "app_version", "sdk_version", "COULD_NOT_GET_CONFIG_MSG", "setItem", "fpr_enabled", "fpr_log_source", "fpr_log_endpoint_url", "fpr_log_transport_key", "fpr_vc_network_request_sampling_rate", "fpr_vc_trace_sampling_rate", "shouldLogAfterSampling", "samplingRate", "initializationStatus", "initializationPromise", "getInitializationPromise", "handler", "getIidPromise", "iidPromise", "iidVal", "changeInitializationStatus", "DEFAULT_SEND_INTERVAL_MS", "MAX_EVENT_COUNT_PER_REQUEST", "DEFAULT_REMAINING_TRIES", "remainingTries", "queue", "isTransportSetup", "processQueue", "timeOffset", "dispatchQueueEvents", "staged", "log_event", "evt", "source_extension_json_proto3", "event_time_ms", "eventTime", "request_time_ms", "client_info", "client_type", "js_client_info", "log_source", "flTransportFullUrl", "sendBeacon", "keepalive", "transportHandler", "serializer", "flushQueuedEvents", "logger", "sendLog", "resource", "resourceType", "send", "flush", "logTrace", "trace", "settingsService", "isAuto", "sendTraceLog", "traceMetric", "customAttributes", "networkRequestMetric", "http_method", "httpMethod", "http_response_code", "response_payload_bytes", "responsePayloadBytes", "client_start_time_us", "startTimeUs", "time_to_response_initiated_us", "timeToResponseInitiatedUs", "time_to_response_completed_us", "timeToResponseCompletedUs", "perfMetric", "application_info", "getApplicationInfo", "network_request_metric", "is_auto", "duration_us", "durationUs", "keys", "counters", "getAttributes", "custom_attributes", "trace_metric", "google_app_id", "web_app_info", "page_url", "service_worker_status", "serviceWorker", "controller", "visibility_state", "VISIBLE", "HIDDEN", "UNKNOWN", "effective_connection_type", "navigatorConnection", "connection", "effectiveType", "application_process_state", "createNetworkRequestEntry", "networkRequestUrl", "logEndpointUrl", "performanceEntry", "networkRequest", "transferSize", "flEndpointUrl", "oobMetrics", "Trace", "traceMeasureName", "state", "api", "randomId", "traceStartMark", "traceStopMark", "traceMeasure", "calculateTraceMetrics", "start", "traceName", "stop", "record", "attributes", "metrics", "metricName", "isNaN", "incrementMetric", "counter", "numAsInteger", "putMetric", "indexOf", "customMetricName", "valueAsInteger", "providedValue", "getMetric", "putAttribute", "attr", "isValidName", "prefix", "match", "isValidValue", "attributeName", "attributeValue", "getAttribute", "removeAttribute", "setStartTime", "setDuration", "perfMeasureEntries", "perfMeasureEntry", "createOobTrace", "navigationTimings", "paintTimings", "webVitalMetrics", "firstInputDelay", "route", "timeOriginUs", "domContentLoadedEventEnd", "loadEventEnd", "<PERSON><PERSON><PERSON><PERSON>", "paintObject", "firstContentful<PERSON><PERSON>t", "addWebVitalMetric", "lcp", "cls", "inp", "metricKey", "<PERSON><PERSON><PERSON>", "metric", "elementAttribution", "createUserTimingTrace", "sentPageLoadTrace", "setupOobResources", "setupOobTraces", "sendOobTrace", "setupNetworkRequests", "setupUserTimingTraces", "substring", "PerformanceController", "initialized", "_init", "settings", "preExist", "DB_CHECK_NAME", "onsuccess", "deleteDatabase", "onupgradeneeded", "onerror", "isAvailable", "DEFAULT_ENTRY_NAME", "factory", "perfInstance", "PerformanceCompatImpl", "_delegate", "performanceFactory", "firebase", "INTERNAL", "registerComponent"], "mappings": "yaAsDYA,gBCmBCC,UAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,EAAcW,SAAS,EAI/CV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJH,OACEX,KACGe,GAEH,IAcuCA,EAdjCb,EAAca,EAAK,IAAoB,GACvCC,EAAcZ,KAAKQ,QAAR,IAAmBZ,EAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,GAUuBF,EAVcb,EAAVe,EAW7BC,QAAQC,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQP,EAAKM,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,CAAK,MAAQD,KAC7C,CAAC,GAdoE,QAE7DG,EAAiBpB,KAAKS,iBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,EAAcmB,EAAUQ,EAAatB,CAAU,CAGlE,CACF,CASD,IAAMiB,EAAU,sBC3GHM,EAiBX1B,YACWM,EACAqB,EACAC,GAFAvB,KAAIC,KAAJA,EACAD,KAAesB,gBAAfA,EACAtB,KAAIuB,KAAJA,EAnBXvB,KAAiBwB,kBAAG,CAAA,EAIpBxB,KAAYyB,aAAe,GAE3BzB,KAAA0B,kBAA2C,OAE3C1B,KAAiB2B,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA7B,KAAK0B,kBAAoBG,EAClB7B,IACR,CAED8B,qBAAqBN,GAEnB,OADAxB,KAAKwB,kBAAoBA,EAClBxB,IACR,CAED+B,gBAAgBC,GAEd,OADAhC,KAAKyB,aAAeO,EACbhC,IACR,CAEDiC,2BAA2BC,GAEzB,OADAlC,KAAK2B,kBAAoBO,EAClBlC,IACR,CACF,EFhBWR,EAAAA,EAAAA,GAOX,IANCA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SAGF,IAAM2C,EAA2D,CAC/DC,MAAS5C,EAAS6C,MAClBC,QAAW9C,EAAS+C,QACpBC,KAAQhD,EAASiD,KACjBC,KAAQlD,EAASmD,KACjBC,MAASpD,EAASqD,MAClBC,OAAUtD,EAASuD,QAMfC,EAA4BxD,EAASiD,KAmBrCQ,EAAgB,EACnBzD,EAAS6C,OAAQ,OACjB7C,EAAS+C,SAAU,OACnB/C,EAASiD,MAAO,QAChBjD,EAASmD,MAAO,QAChBnD,EAASqD,OAAQ,SAQdK,EAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAAA,EAAUD,EAASG,UAAvB,CAGA,IAAMC,GAAM,IAAIC,MAAOC,YAAW,EAC5BC,EAAST,EAAcG,GAC7B,GAAIM,CAAAA,EAMF,MAAM,IAAIhE,oEACsD0D,IAAU,EAN1EO,QAAQD,OACFH,OAASJ,EAASlD,QACtB,GAAGoD,CAAI,CANV,CAaH,EG3HgM,SAAFO,EAAWC,GAAG,GAAG,YAAYC,SAASC,WAAW,MAAM,UAAU,IAAIC,EAAEC,IAAI,GAAGD,EAAE,CAAC,GAAGH,EAAEG,EAAEE,eAAe,MAAM,UAAU,GAAG,IAAIF,EAAEG,4BAA4BN,EAAEG,EAAEG,2BAA2B,MAAM,kBAAkB,GAAG,IAAIH,EAAEI,aAAaP,EAAEG,EAAEI,YAAY,MAAM,oBAAoB,CAAC,MAAM,UAAU,CAA2G,SAAFC,EAAWR,EAAEG,GAAG,IAAIC,EAAE,GAAG,IAAI,KAAKJ,GAAG,IAAIA,EAAES,UAAU,CAAC,IAAIV,EAAEC,EAAEQ,EAAET,EAAEW,GAAG,IAAIX,EAAEW,GAAGC,GAAEZ,CAAC,GAAGA,EAAEa,WAAWb,EAAEa,UAAUvD,OAAO0C,EAAEa,UAAUvD,MAAMwD,KAAM,GAAEd,EAAEa,UAAUvD,MAAMwD,KAAI,EAAGC,OAAO,IAAIf,EAAEa,UAAUvD,MAAMwD,KAAM,EAAC5D,QAAQ,OAAO,GAAG,EAAE,IAAI,GAAGmD,EAAEU,OAAON,EAAEM,QAAQX,GAAG,KAAK,EAAE,OAAOC,GAAGI,EAAE,GAAGJ,EAAEA,EAAEI,EAAE,IAAIJ,EAAEI,EAAET,EAAEW,GAAG,MAAMV,EAAED,EAAEgB,UAAU,EAAE,MAAMf,IAAI,OAAOI,CAAC,CAA60E,SAAFY,EAAWhB,EAAEG,GAAI,IAASH,EAAEG,EAA/bH,EAAEG,EAA2bH,WAA8fG,GAA2BC,EAAE,IAAXJ,EAAwaG,GAArZc,QAAQH,SAAYH,EAAEX,EAAEiB,QAAQC,OAAQ,SAASlB,EAAEG,GAAG,OAAOH,GAAGA,EAAE3C,MAAM8C,EAAE9C,MAAM2C,EAAEG,CAAE,CAAE,IAAOQ,EAAEQ,SAASR,EAAEQ,QAAQL,SAAYM,GAAGjB,EAAEQ,EAAEQ,SAASE,KAAI,SAAWrB,GAAG,OAAOA,EAAEsB,MAAM,IAAItB,EAAEsB,KAAKb,QAAS,CAAA,GAAIN,EAAE,MAAOC,EAAE,CAACmB,mBAAmBf,EAAEY,EAAEE,IAAI,EAAEE,iBAAiBb,EAAEc,UAAUC,kBAAkBf,EAAEtD,MAAMsE,mBAAmBP,EAAEQ,kBAAkBjB,EAAEkB,UAAU9B,EAAEY,EAAEc,SAAS,CAAC,GAA1Y,IAAsDd,EAA9BP,EAApBA,EAAiZ/D,OAAOyF,OAAO9B,EAAE,CAAC+B,YAAY3B,CAAC,CAAC,EAAMJ,EAAEI,CAAC,CAAE,EAA37BD,EAA67BA,GAAx7B,GAAvcH,EAA4cgC,cAA4C,SAAFZ,EAAWpB,GAAGA,EAAEiC,QAAO,SAAWjC,GAAG,IAA0BG,EAAOC,EAA7BJ,EAAEkC,iBAAoB/B,EAAEK,EAAE,GAAGJ,EAAEI,EAAEA,EAAEM,OAAO,GAAGH,GAAGX,EAAEyB,UAAUrB,EAAEqB,UAAU,KAAKzB,EAAEyB,UAAUtB,EAAEsB,UAAU,KAAKd,GAAGX,EAAE3C,MAAMmD,EAAE2B,KAAKnC,CAAC,IAAIW,EAAEX,EAAE3C,MAAMmD,EAAE,CAACR,IAAK,CAAA,EAAGW,EAAEZ,EAAE1C,QAAQ0C,EAAE1C,MAAMsD,EAAEZ,EAAEkB,QAAQT,EAAEJ,EAAC,EAAG,CAAjQ,IAAIA,EAAEL,EAAEqC,EAAE,MAAM,CAAC,EAAEzB,EAAE,EAAEH,EAAE,GAA0O6B,EAAEC,EAAE,eAAelB,CAAC,EAAEiB,IAAIjC,EAAEmC,EAAEvC,EAAED,EAAEyC,GAAErC,EAAEsC,gBAAgB,EAAEC,aAActB,EAAEiB,EAAEM,YAAW,CAAE,EAAEvC,EAAE,CAAA,CAAE,CAAE,CAAA,EAAGwC,EAAG,WAAe7C,EAAEqC,EAAE,MAARzB,EAAE,CAAa,EAAEP,EAAEmC,EAAEvC,EAAED,EAAEyC,GAAErC,EAAEsC,gBAAgB,EAAEI,cAAc,OAAOzC,EAAG,CAAC,EAAG,CAAA,EAAG0C,WAAW1C,EAAE,CAAC,EAAG,CAAA,EAAv6BD,EAAEA,GAAG,GAAG4C,GAAG,WAAW,IAAI3C,EAAEL,EAAEiD,KAAIrC,EAAEyB,EAAE,KAAK,EAAE5B,EAAE8B,EAAE,QAAO,SAAWtC,GAAGA,EAAEiC,QAAS,SAASjC,GAAG,2BAA2BA,EAAE5D,OAAOoE,EAAEyC,WAAU,EAAGjD,EAAEyB,UAAU1B,EAAEmD,mBAAkBvC,EAAEtD,MAAM8F,KAAKC,IAAIpD,EAAEyB,UAAU4B,KAAI,CAAC,EAAE1C,EAAEM,QAAQkB,KAAKnC,CAAC,EAAEI,EAAE,CAAA,CAAE,EAAI,CAAE,CAAC,CAAE,EAACI,IAAIJ,EAAEmC,EAAEvC,EAAEW,EAAE2C,GAAEnD,EAAEsC,gBAAgB,EAAEG,EAAC,SAAW7C,GAAGY,EAAEyB,EAAE,KAAK,EAAEhC,EAAEmC,EAAEvC,EAAEW,EAAE2C,GAAEnD,EAAEsC,gBAAgB,EAAEI,GAAC,WAAalC,EAAEtD,MAAMkG,YAAY7D,IAAK,EAACK,EAAEyD,UAAUpD,EAAE,CAAA,CAAE,CAAE,CAAA,CAAG,CAAA,EAAI,CAAE,CAA2+B,CAA2/C,SAAFqD,EAAWzD,EAAEG,GAAG,2BAA2BuD,MAAM,kBAAkBC,uBAAuBpH,YAAY4D,EAAEA,GAAG,GAAG4C,GAAC,WAAa,IAAI3C,EAAEwD,GAAC,EAAsB,SAAFpD,EAAWR,GAAG6D,GAAC,WAAa7D,EAAEiC,QAAQ6B,EAAC,EAAE,IAAI3D,EAAE4D,GAAG,EAAC5D,GAAGA,EAAE6D,UAAUrD,EAAEtD,QAAQsD,EAAEtD,MAAM8C,EAAE6D,QAAQrD,EAAEM,QAAQd,EAAEc,QAAQlB,EAAC,EAAI,EAAE,CAA1I,IAAMY,EAAEyB,EAAE,KAAK,EAA6HhB,EAAEkB,EAAE,QAAQ9B,EAAE,CAACyD,kBAAkB,OAAQ7D,EAAED,EAAE8D,mBAA+B7D,EAAE,EAAE,CAAC,EAAEL,EAAEwC,EAAEvC,EAAEW,EAAEuD,GAAE/D,EAAEsC,gBAAgB,EAAErB,IAAIA,EAAE+C,QAAQ,CAACzG,KAAK,cAAc0G,SAAS,CAAA,CAAE,CAAC,EAAE1B,EAAG,WAAWlC,EAAEY,EAAEuB,YAAW,CAAE,EAAE5C,EAAE,CAAA,CAAE,CAAE,CAAE,EAAC6C,EAAC,WAAayB,GAAEC,GAAG,EAACC,EAAEzD,OAAO,EAAE0D,EAAEC,MAAO,EAAC9D,EAAEyB,EAAE,KAAK,EAAErC,EAAEwC,EAAEvC,EAAEW,EAAEuD,GAAE/D,EAAEsC,gBAAgB,CAAE,CAAA,EAAI,CAAE,EAAC,CAA+C,SAAFiC,EAAW1E,GAAG2E,EAAEA,EAAEC,OAAO5E,CAAC,EAAE6E,GAAG,CAAA,CAAgC,SAAFC,IAAoB,GAAPC,EAAEC,MAASD,EAAE9C,QAAO,SAAWjC,EAAEG,GAAGqE,EAAES,IAAI9E,CAAC,GAAG4E,EAAEG,OAAO/E,CAAC,CAAE,CAAE,EAAC,IAAIH,EAAEuE,EAAEY,IAAG,SAAWnF,GAAG,OAAOoF,GAAEC,IAAIrF,EAAEiB,QAAQ,EAAE,CAAE,CAAE,EAACd,EAAEmF,EAAExE,OAAO,GAAGwE,EAAEA,EAAEC,OAAQ,SAASnF,EAAEL,GAAG,OAAUI,GAAHJ,GAAMC,EAAEwF,SAASpF,CAAC,CAAE,CAAA,EAAG,IAAI,IAAIA,EAAE,IAAIqF,IAAI1F,EAAE,EAAEA,EAAEuF,EAAExE,OAAOf,CAAC,GAAG,CAAC,IAAIY,EAAE2E,EAAEvF,GAAG2F,GAAG/E,EAAEc,UAAUd,EAAEgF,aAAa,EAAE1D,QAAO,SAAWjC,GAAGI,EAAEwF,IAAI5F,CAAC,CAAE,CAAE,CAAA,CAAC,IAAIQ,EAAEmE,EAAE7D,OAAO,EAAE,GAAG6D,EAAEA,EAAEY,OAAM,SAAWvF,EAAEG,GAAG,OAAOH,EAAEyB,UAAUtE,IAAKqD,EAAFL,GAAKC,EAAE6E,IAAIjF,CAAC,CAAE,CAAE,EAAC6F,GAAE,CAAC,CAAC,CAAoxB,SAAHC,EAAY9F,EAAEI,GAAGD,GAAAA,IAAMmC,EAAE,uBAAuBoC,CAAC,EAAGjB,WAAYtD,GAAyBA,GAAPH,EAA0tBG,GAA/sBc,QAAQ,GAAGb,EAAEgF,GAAEC,IAAIlF,CAAC,EAAEQ,EAAER,EAAE4F,gBAAgB3E,EAAEhB,EAAEuF,cAActD,EAAEjC,EAAEa,QAAQ+E,KAAI,SAAWhG,EAAEG,GAAG,OAAOH,EAAE+F,gBAAgB5F,EAAE4F,eAAgB,CAAE,EAACnD,EAAE8C,GAAGvF,EAAEsB,UAAUL,CAAC,EAAmDgB,GAAjDiB,EAAErD,EAAEiB,QAAQI,KAAI,SAAWrB,GAAG,OAAOA,EAAEiG,MAAO,CAAE,IAAM5C,EAAE4C,QAAQlB,EAAEM,IAAIlF,EAAE+F,aAAa,EAAE5D,EAAE,CAACnC,EAAEsB,UAAUtB,EAAEgG,SAAS/E,GAAGwD,OAAOhC,EAAEuC,aAAcnF,GAAG,OAAOA,EAAEyB,UAAUzB,EAAEmG,QAAS,CAAA,CAAE,EAAE5D,EAAEY,KAAKC,IAAIgD,MAAMjD,KAAKb,CAAC,EAAEO,EAAE,CAACwD,kBAAkB7F,EAAE4B,CAAC,EAAEkE,yBAAyBlE,EAAEmE,gBAAgBpG,EAAE/D,KAAKoK,WAAW,KAAK,EAAE,WAAW,UAAUC,gBAAgBtG,EAAEsB,UAAUiF,cAAcnE,EAAEoE,sBAAsBtE,EAAEuE,0BAA0BhE,EAAEiE,WAAWlG,EAAER,EAAEsB,UAAUqF,mBAAmB1F,EAAET,EAAEoG,kBAAkB5D,KAAKC,IAAIb,EAAEnB,EAAE,CAAC,EAAES,UAAU9B,EAAEI,EAAEsB,SAAS,CAAC,EAA9rB,IAAsBtB,EAA0BQ,EAAoBS,EAAgGwB,EAAoBS,EAAuFf,EAA3QlC,EAAmsB/D,OAAOyF,OAAO9B,EAAE,CAAC+B,YAAYc,CAAC,CAAC,EAAM7C,EAAEI,CAAC,CAAE,EAAEA,CAAC,CAAC,CAAyB,SAAH4G,EAAYhH,EAAEG,GAAI,IAASH,EAAEG,EAAFH,EAAinB,SAAWG,GAAyBA,EAAE,CAAC8G,gBAAgB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,oBAAvEpH,EAA4pBG,GAAhkB9C,KAAK,EAAK2C,EAAEiB,QAAQH,SAAYf,EAAEK,EAAG,KAAWO,EAAEZ,EAAEsH,iBAAiB,EAAkChF,GAAhCjB,EAAEpB,EAAEiB,QAAQjB,EAAEiB,QAAQH,OAAO,IAAOwG,KAAK/D,YAAYgE,iBAAiB,UAAU,EAAEhC,OAAM,SAAWvF,GAAG,OAAOA,EAAE5D,OAAOgF,EAAEkG,GAAI,CAAE,EAAC,GAAG1E,EAAEO,KAAKC,IAAI,EAAErD,EAAEyH,cAAc7G,CAAC,EAAE0C,EAAEF,KAAKC,IAAIR,EAAEP,GAAGA,EAAEoF,cAAcpF,EAAEZ,WAAWd,EAAE,CAAC,EAAEyB,EAAEe,KAAKC,IAAIC,EAAEhB,EAAEA,EAAEqF,YAAY/G,EAAE,CAAC,EAAE2B,EAAEa,KAAKC,IAAIhB,EAAEhB,EAAEK,UAAUd,CAAC,EAAER,EAAE,CAACwH,QAAQnH,EAAEY,EAAEuG,OAAO,EAAEV,gBAAgBrE,EAAEsE,kBAAkB7D,EAAET,EAAEuE,qBAAqB/E,EAAEiB,EAAE+D,mBAAmB9E,EAAEF,EAAEwF,gBAAgB7H,EAAE8H,SAASzG,CAAC,EAAEA,EAAEkG,MAAMnH,EAAEmH,IAAIlG,EAAEkG,KAAKjF,KAAIlC,EAAE2H,iBAAiBzF,GAA9nB,IAA2ItC,EAAuCqB,EAAgCiB,EAAkIgB,EAAkDjB,EAAkCE,EAAlZnC,EAAlBJ,EAAqoB1D,OAAOyF,OAAO9B,EAAE,CAAC+B,YAAY5B,CAAC,CAAC,EAAMH,EAAED,CAAC,CAAE,EAA7yCI,EAA+yCA,GAA1yC,GAAG4C,GAAG,WAAoC,SAAFvC,EAAWR,IAAwBA,EAArBG,EAAEsC,iBAAkCzC,EAAbA,EAAE+H,MAAM,CAAC,CAAC,GAAK9F,QAAO,SAAWjC,GAAGA,EAAEyB,UAAU1B,EAAEmD,kBAAkBvC,EAAEtD,MAAM8F,KAAKC,IAAIpD,EAAEyB,UAAU4B,GAAC,EAAG,CAAC,EAAE1C,EAAEM,QAAQ,CAACjB,GAAGI,EAAG,EAAE,CAAE,CAAA,CAAzL,IAAmOA,EAAmCiC,EAAhQtC,EAAEiD,GAAG,EAACrC,EAAEyB,EAAE,KAAK,EAAsKhB,EAAEkB,EAAE,2BAA2B9B,CAAC,EAAKY,IAAGhB,EAAEmC,EAAEvC,EAAEW,EAAEqH,GAAG7H,EAAEsC,gBAAgB,EAAMJ,EAAEL,GAAC,WAAaiG,GAAGtH,EAAED,MAAMF,EAAEY,EAAEuB,YAAW,CAAE,EAAEvB,EAAE6B,WAAY,EAACgF,GAAGtH,EAAED,IAAI,CAAA,EAAGN,EAAE,CAAA,CAAE,EAAG,CAAA,EAAG,CAAC,UAAU,SAAS6B,QAAO,SAAWjC,GAAGkI,iBAAiBlI,EAAC,WAAa,OAAO6D,GAAExB,CAAC,CAAE,EAAE,CAAC8F,KAAK,CAAA,EAAGC,QAAQ,CAAA,CAAE,CAAC,CAAE,CAAE,EAAC1F,EAAEL,CAAC,EAAEO,EAAC,SAAW7C,GAAGY,EAAEyB,EAAE,KAAK,EAAEhC,EAAEmC,EAAEvC,EAAEW,EAAEqH,GAAG7H,EAAEsC,gBAAgB,EAAEI,GAAC,WAAalC,EAAEtD,MAAMkG,YAAY7D,IAAK,EAACK,EAAEyD,UAAUyE,GAAGtH,EAAED,IAAI,CAAA,EAAGN,EAAE,CAAA,CAAE,CAAE,CAAA,CAAG,CAAE,EAAE,CAAA,CAAwsB,CAAxpT,IAAAJ,EAAEG,GCwFO,GDxFLC,EAAE,WAAW,IAAIJ,EAAE0D,KAAKH,aAAaA,YAAYgE,kBAAkBhE,YAAYgE,iBAAiB,YAAY,EAAE,GAAG,GAAGvH,GAAmB,EAAhBA,EAAEwH,eAAiBxH,EAAEwH,cAAcjE,YAAY7D,IAAK,EAAC,OAAOM,CAAC,EAAwSW,GAAE,SAASX,GAAG,IAAIG,EAAEH,EAAEqI,SAAS,OAAO,IAAIrI,EAAES,SAASN,EAAEmI,YAAa,EAACnI,EAAEoI,YAAW,EAAGtL,QAAQ,KAAK,EAAE,CAAC,EAAiVmE,GAAE,CAAC,EAAEiB,GAAE,WAAW,OAAOjB,EAAC,EAAEwB,EAAE,SAAS5C,GAAGkI,iBAAiB,WAAU,SAAW/H,GAAGA,EAAEqI,YAAYpH,GAAEjB,EAAEqD,UAAUxD,EAAEG,CAAC,EAAG,EAAE,CAAA,CAAE,CAAC,EAAEkD,GAAE,WAAW,IAAIrD,EAAEI,EAAG,EAAC,OAAOJ,GAAGA,EAAEqH,iBAAiB,CAAC,EAAEjF,EAAE,SAASpC,EAAEG,GAAG,IAAIJ,EAAEK,EAAC,EAAGO,EAAE,WAAgK,OAAhJ,GAAL0B,GAAC,EAAM1B,EAAE,qBAAqBZ,IAAIE,SAASwI,cAAkB,EAAJpF,GAAC,EAAK1C,EAAE,YAAYV,SAASyI,aAAa/H,EAAE,UAAUZ,EAAErC,OAAOiD,EAAEZ,EAAErC,KAAKT,QAAQ,KAAK,GAAG,IAAU,CAACb,KAAK4D,EAAE3C,MAAM,KAAA,IAAS8C,EAAE,CAAC,EAAEA,EAAEwI,OAAO,OAAOC,MAAM,EAAE3H,QAAQ,GAAGP,GAAG,MAAMkE,OAAOjF,KAAKD,IAAG,EAAG,GAAG,EAAEkF,OAAOzB,KAAK0F,MAAM,cAAc1F,KAAK2F,OAAQ,CAAA,EAAE,IAAI,EAAEC,eAAepI,CAAC,CAAC,EAAE2B,EAAE,SAAStC,EAAEG,EAAEC,GAAG,IAAI,IAA4DL,EAA5D,GAAGiJ,oBAAoBC,oBAAoBzD,SAASxF,CAAC,EAAyG,OAAlGD,EAAE,IAAIiJ,oBAAqB,SAAShJ,GAAGkJ,QAAQC,QAAS,EAACC,KAAI,WAAajJ,EAAEH,EAAEqJ,WAAY,CAAA,CAAE,CAAA,CAAG,CAAA,GAAYlF,QAAQ9H,OAAOyF,OAAO,CAACpE,KAAKsC,EAAEoE,SAAS,CAAA,CAAE,EAAEhE,GAAG,EAAE,CAAC,EAAEL,EAAG,MAAMC,IAAI,EAAEuC,EAAE,SAASvC,EAAEG,EAAEC,EAAEL,GAAG,IAAIY,EAAEH,EAAE,OAAO,SAASY,GAAY,GAATjB,EAAE9C,QAAW+D,GAAGrB,MAAMS,EAAEL,EAAE9C,OAAOsD,GAAG,KAAK,KAAA,IAASA,KAAKA,EAAER,EAAE9C,MAAM8C,EAAEyI,MAAMpI,EAAEL,EAAEwI,QAAgB3I,EAA6DG,EAAE9C,OAAM+C,EAArD,GAAG,OAAOJ,EAA2CI,EAAvC,GAAG,oBAAoB,OAAmBJ,EAAEG,CAAC,EAAE,CAAC,EAAE0C,GAAE,SAAS7C,GAAGsJ,sBAAuB,WAAW,OAAOA,sBAAqB,WAAa,OAAOtJ,EAAG,CAAC,CAAE,CAAC,EAAE,EAAE0C,EAAE,SAAS1C,GAAGC,SAASiI,iBAAiB,mBAAkB,WAAa,WAAWjI,SAASsJ,iBAAiBvJ,EAAG,CAAC,CAAE,CAAA,EAAEgC,GAAE,SAAShC,GAAG,IAAIG,EAAE,CAAA,EAAG,OAAO,WAAWA,IAAIH,EAAC,EAAGG,EAAE,CAAA,EAAG,CAAC,EAAEqJ,EAAE,CAAC,EAAEC,GAAE,WAAW,MAAM,WAAWxJ,SAASsJ,iBAAiBtJ,SAASwI,aAAa,EAAA,EAAI,CAAC,EAAEiB,EAAE,SAAS1J,GAAG,WAAWC,SAASsJ,iBAAmB,CAAC,EAAHC,IAAOA,EAAE,qBAAqBxJ,EAAEtC,KAAKsC,EAAEwD,UAAU,EAAEmG,GAAC,EAAG,EAAEC,GAAE,WAAW1B,iBAAiB,mBAAmBwB,EAAE,CAAA,CAAE,EAAExB,iBAAiB,qBAAqBwB,EAAE,CAAA,CAAE,CAAC,EAAEC,GAAE,WAAWE,oBAAoB,mBAAmBH,EAAE,CAAA,CAAE,EAAEG,oBAAoB,qBAAqBH,EAAE,CAAA,CAAE,CAAC,EAAE1G,GAAE,WAAW,OAAOwG,EAAE,IAAIA,EAAEC,GAAG,EAACG,GAAC,EAAGhH,EAAG,WAAWE,WAAY,WAAW0G,EAAEC,GAAC,EAAGG,GAAG,CAAC,EAAE,CAAC,CAAE,CAAE,GAAE,CAAC1G,sBAAsB,OAAOsG,CAAC,CAAC,CAAC,EAAEzG,GAAE,SAAS/C,GAAGC,SAASwI,aAAaP,iBAAiB,qBAAsB,WAAW,OAAOlI,EAAG,CAAC,EAAE,CAAA,CAAE,EAAEA,EAAG,CAAA,EAAEsD,GAAE,CAAC,KAAK,KAAwad,GAAE,CAAC,GAAG,KAAg4CsH,GAAE,EAAEC,GAAE,EAAA,EAAIC,EAAE,EAAEC,GAAE,SAASjK,GAAGA,EAAEiC,QAAS,SAASjC,GAAGA,EAAEkG,gBAAgB6D,GAAE5G,KAAK+G,IAAIH,GAAE/J,EAAEkG,aAAa,EAAE8D,EAAE7G,KAAKC,IAAI4G,EAAEhK,EAAEkG,aAAa,EAAE4D,GAAEE,GAAGA,EAAED,IAAG,EAAE,EAAE,EAAG,CAAE,CAAA,EAAEzF,GAAE,WAAW,OAAOtE,EAAE8J,GAAEvG,YAAY4G,kBAAkB,CAAC,EAAEvG,GAAE,WAAW,qBAAqBL,cAAavD,EAAAA,GAAMsC,EAAE,QAAQ2H,GAAE,CAACvM,KAAK,QAAQ0G,SAAS,CAAA,EAAGH,kBAAkB,CAAC,CAAC,EAAE,EAAEM,EAAE,GAAGC,EAAE,IAAI4F,IAAI/F,GAAE,EAAEN,GAAE,WAAW,IAAI/D,EAAEmD,KAAK+G,IAAI3F,EAAEzD,OAAO,EAAEqC,KAAK0F,OAAOvE,GAAG,EAACD,IAAG,EAAE,CAAC,EAAE,OAAOE,EAAEvE,EAAE,EAAEqK,GAAE,GAAGvG,GAAE,SAAS9D,GAAG,IAA2GI,EAA0NL,EAAlUsK,GAAEpI,QAAO,SAAW9B,GAAG,OAAOA,EAAEH,CAAC,CAAE,CAAA,EAAGA,CAAAA,EAAEkG,eAAe,gBAAgBlG,EAAEsK,YAAenK,EAAEoE,EAAEA,EAAEzD,OAAO,KAAGV,EAAEoE,EAAEa,IAAIrF,EAAEkG,aAAa,IAAQ3B,EAAEzD,OAAO,IAAId,EAAEmG,SAAShG,EAAE6D,WAAY5D,EAAEJ,EAAEmG,SAAS/F,EAAE4D,SAAS5D,EAAEa,QAAQ,CAACjB,GAAGI,EAAE4D,QAAQhE,EAAEmG,UAAUnG,EAAEmG,WAAW/F,EAAE4D,SAAShE,EAAEyB,YAAYrB,EAAEa,QAAQ,GAAGQ,WAAWrB,EAAEa,QAAQkB,KAAKnC,CAAC,GAAWD,EAAE,CAACW,GAAGV,EAAEkG,cAAclC,QAAQhE,EAAEmG,SAASlF,QAAQ,CAACjB,EAAE,EAAEwE,EAAE+F,IAAIxK,EAAEW,GAAGX,CAAC,EAAEwE,EAAEpC,KAAKpC,CAAC,GAAEwE,EAAEyB,KAAM,SAAShG,EAAEG,GAAG,OAAOA,EAAE6D,QAAQhE,EAAEgE,OAAQ,CAAA,EAAY,GAATO,EAAEzD,SAAWyD,EAAEiG,OAAO,EAAE,EAAEvI,QAAO,SAAWjC,GAAG,OAAOwE,EAAEU,OAAOlF,EAAEU,EAAE,CAAE,CAAA,EAAI,EAAEmD,GAAE,SAAS7D,GAAG,IAAIG,EAAEuD,KAAK+G,qBAAqB/G,KAAKZ,WAAW1C,EAAE,CAAC,EAAE,OAAOJ,EAAEgC,GAAEhC,CAAC,EAAE,WAAWC,SAASsJ,gBAAgBvJ,EAAG,GAAEI,EAAED,EAAEH,CAAC,EAAE0C,EAAE1C,CAAC,GAAGI,CAAC,EAAE8D,GAAE,CAAC,IAAI,KAA6jBS,EAAE,GAAGW,EAAE,GAAGnI,GAAE,EAAEiI,GAAE,IAAIsF,QAAQ3F,EAAE,IAAIqF,IAAIvE,GAAE,CAAC,EAAmChB,GAAE,WAAWgB,GAAE,IAAIA,GAAEhC,GAAEiB,CAAC,EAAE,EAA2iCY,IAArpB2E,GAAElI,cAAenC,GAAGA,EAAEkG,eAAelG,EAAEiG,QAAQ,CAAClB,EAAEE,IAAIjF,EAAEkG,aAAa,GAAGnB,EAAEwF,IAAIvK,EAAEkG,cAAclG,EAAEiG,MAAM,CAAE,EAAG,SAASjG,GAAG,IAAIG,EAAEC,EAAEJ,EAAEyB,UAAUzB,EAAEmG,SAAShJ,GAAEgG,KAAKC,IAAIjG,GAAE6C,EAAE2F,aAAa,EAAE,IAAI,IAAI5F,EAAEuF,EAAExE,OAAO,EAAK,GAAHf,EAAKA,CAAC,GAAG,CAAC,IAAIY,EAAE2E,EAAEvF,GAAG,GAAGoD,KAAKwH,IAAIvK,EAAEO,EAAEiK,UAAU,GAAG,EAAE,EAAEzK,EAAEQ,GAAGc,UAAU0B,KAAK+G,IAAIlK,EAAEyB,UAAUtB,EAAEsB,SAAS,EAAEtB,EAAE4F,gBAAgB5C,KAAK+G,IAAIlK,EAAE+F,gBAAgB5F,EAAE4F,eAAe,EAAE5F,EAAEwF,cAAcxC,KAAKC,IAAIpD,EAAE2F,cAAcxF,EAAEwF,aAAa,EAAExF,EAAEc,QAAQkB,KAAKnC,CAAC,EAAE,KAAK,CAAC,CAACG,IAAIA,EAAE,CAACsB,UAAUzB,EAAEyB,UAAUsE,gBAAgB/F,EAAE+F,gBAAgBJ,cAAc3F,EAAE2F,cAAciF,WAAWxK,EAAEa,QAAQ,CAACjB,EAAE,EAAEsF,EAAEnD,KAAKhC,CAAC,GAAIH,CAAAA,EAAEkG,eAAe,gBAAgBlG,EAAEsK,WAAYlF,GAAEmF,IAAIvK,EAAEG,CAAC,EAAE0E,GAAC,CAAG,CAAE,EAAkB,SAAS7E,EAAEG,GAAG,IAAI,IAAIC,EAAEL,EAAE,GAAGY,EAAE,EAAEP,EAAEuE,EAAEhE,GAAGA,CAAC,GAAG,GAAG,EAAEP,EAAEqB,UAAUrB,EAAE+F,SAASnG,GAAG,CAAC,GAAGI,EAAEqB,UAAUtB,EAAE,MAAMJ,EAAEoC,KAAK/B,CAAC,CAAC,CAAC,OAAOL,CAAC,GAA4zBiI,GAAG,CAAC,KAAK,KAAKC,GAAG,GEAx0Q,IAAM4C,GAAgB,CAACC,EAAQC,IAAiBA,EAAaC,KAAK,GAAOF,aAAkBzI,CAAC,EAExF4I,GACAC,GAqBJ,IAAMC,GAAmB,IAAIT,QACvBU,GAAqB,IAAIV,QACzBW,GAA2B,IAAIX,QAC/BY,GAAiB,IAAIZ,QACrBa,GAAwB,IAAIb,QA0DlCc,IAAIC,GAAgB,CAChBpG,IAAIY,EAAQyF,EAAMC,GACd,GAAI1F,aAAkB2F,eAAgB,CAElC,GAAa,SAATF,EACA,OAAON,GAAmB/F,IAAIY,CAAM,EAExC,GAAa,qBAATyF,EACA,OAAOzF,EAAO4F,kBAAoBR,GAAyBhG,IAAIY,CAAM,EAGzE,GAAa,UAATyF,EACA,OAAOC,EAASE,iBAAiB,GAC3BC,KAAAA,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,EAAE,CAE9D,CAED,OAAOG,EAAK/F,EAAOyF,EAAK,CAC3B,EACDnB,IAAItE,EAAQyF,EAAMrO,GAEd,OADA4I,EAAOyF,GAAQrO,EACR,CAAA,CACV,EACD4H,IAAIgB,EAAQyF,GACR,OAAIzF,aAAkB2F,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQzF,CAClB,CACL,EAIA,SAASgG,GAAaC,GAIlB,OAAIA,IAASC,YAAY5P,UAAU6P,aAC7B,qBAAsBR,eAAerP,WA7GnC2O,GAAAA,IACoB,CACpBmB,UAAU9P,UAAU+P,QACpBD,UAAU9P,UAAUgQ,SACpBF,UAAU9P,UAAUiQ,qBAqHEhH,SAAS0G,CAAI,EAChC,YAAa1M,GAIhB,OADA0M,EAAK9F,MAAMqG,GAAOtQ,IAAI,EAAGqD,CAAI,EACtBwM,EAAKb,GAAiB9F,IAAIlJ,IAAI,CAAC,CAClD,EAEW,YAAaqD,GAGhB,OAAOwM,EAAKE,EAAK9F,MAAMqG,GAAOtQ,IAAI,EAAGqD,CAAI,CAAC,CAClD,EAvBe,SAAUkN,KAAelN,GAC5B,IAAMmN,EAAKT,EAAKU,KAAKH,GAAOtQ,IAAI,EAAGuQ,EAAY,GAAGlN,CAAI,EAEtD,OADA6L,GAAyBd,IAAIoC,EAAID,EAAW1G,KAAO0G,EAAW1G,KAAM,EAAG,CAAC0G,EAAW,EAC5EV,EAAKW,CAAE,CAC1B,CAoBA,CACA,SAASE,GAAuBxP,GAC5B,IA5FoCsP,EAI9BG,EAwFN,MAAqB,YAAjB,OAAOzP,EACA4O,GAAa5O,CAAK,GAGzBA,aAAiBuO,iBAhGee,EAiGDtP,EA/F/B+N,GAAmBnG,IAAI0H,CAAE,IAEvBG,EAAO,IAAI5D,QAAQ,CAACC,EAAS4D,KAC/B,IAAMC,EAAW,KACbL,EAAG9C,oBAAoB,WAAYoD,CAAQ,EAC3CN,EAAG9C,oBAAoB,QAAS9K,CAAK,EACrC4N,EAAG9C,oBAAoB,QAAS9K,CAAK,CACjD,EACckO,EAAW,KACb9D,IACA6D,GACZ,EACcjO,EAAQ,KACVgO,EAAOJ,EAAG5N,OAAS,IAAImO,aAAa,aAAc,YAAY,CAAC,EAC/DF,GACZ,EACQL,EAAGzE,iBAAiB,WAAY+E,CAAQ,EACxCN,EAAGzE,iBAAiB,QAASnJ,CAAK,EAClC4N,EAAGzE,iBAAiB,QAASnJ,CAAK,CAC1C,CAAK,EAEDqM,GAAmBb,IAAIoC,EAAIG,CAAI,IA2E3BjC,GAAcxN,EAzJV4N,GAAAA,IACiB,CACjBkB,YACAgB,eACAC,SACAf,UACAT,eAmJuC,EACpC,IAAIyB,MAAMhQ,EAAOoO,EAAa,EAElCpO,EACX,CACA,SAAS2O,EAAK3O,GAGV,IA1IsBiQ,EAgJhBC,EANN,OAAIlQ,aAAiBmQ,YA1ICF,EA2IMjQ,GA1ItBoQ,EAAU,IAAIvE,QAAQ,CAACC,EAAS4D,KAClC,IAAMC,EAAW,KACbM,EAAQzD,oBAAoB,UAAW6D,CAAO,EAC9CJ,EAAQzD,oBAAoB,QAAS9K,CAAK,CACtD,EACc2O,EAAU,KACZvE,EAAQ6C,EAAKsB,EAAQK,MAAM,CAAC,EAC5BX,GACZ,EACcjO,EAAQ,KACVgO,EAAOO,EAAQvO,KAAK,EACpBiO,GACZ,EACQM,EAAQpF,iBAAiB,UAAWwF,CAAO,EAC3CJ,EAAQpF,iBAAiB,QAASnJ,CAAK,CAC/C,CAAK,GAEIqK,KAAK,IAGF/L,aAAiBgP,WACjBlB,GAAiBZ,IAAIlN,EAAOiQ,CAAO,CAG/C,CAAK,EACIM,MAAM,MAAS,EAGpBrC,GAAsBhB,IAAIkD,EAASH,CAAO,EACnCG,GAgHHnC,GAAerG,IAAI5H,CAAK,EACjBiO,GAAejG,IAAIhI,CAAK,IAC7BkQ,EAAWV,GAAuBxP,CAAK,KAG5BA,IACbiO,GAAef,IAAIlN,EAAOkQ,CAAQ,EAClChC,GAAsBhB,IAAIgD,EAAUlQ,CAAK,GAEtCkQ,EACX,CACA,IAAMd,GAAS,GAAWlB,GAAsBlG,IAAIhI,CAAK,EDrIzD,IAAMwQ,GAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,GAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,GAAgB,IAAI3D,IAC1B,SAAS4D,GAAU/H,EAAQyF,GACvB,GAAMzF,aAAkBkG,aAClB,EAAAT,KAAQzF,IACM,UAAhB,OAAOyF,EAFX,CAKA,GAAIqC,GAAc1I,IAAIqG,CAAI,EACtB,OAAOqC,GAAc1I,IAAIqG,CAAI,EACjC,IAAMuC,EAAiBvC,EAAKzO,QAAQ,aAAc,EAAE,EAC9CiR,EAAWxC,IAASuC,EACpBE,EAAUL,GAAatI,SAASyI,CAAc,EACpD,IAMMpO,EANN,OAEEoO,KAAmBC,EAAWd,SAAWD,gBAAgB5Q,YACrD4R,GAAWN,GAAYrI,SAASyI,CAAc,IAG9CpO,EAASuO,eAAgBC,KAAc7O,GAEzC,IAAMmN,EAAKxQ,KAAKiQ,YAAYiC,EAAWF,EAAU,YAAc,UAAU,EACzE3C,IAAIvF,EAAS0G,EAAG2B,MAQhB,OAPIJ,IACAjI,EAASA,EAAOsI,MAAM/O,EAAKgP,MAAO,CAAA,IAM/B,MAAOtF,QAAQuF,IAAI,CACtBxI,EAAOgI,GAAgB,GAAGzO,CAAI,EAC9B2O,GAAWxB,EAAGG,KACjB,GAAG,EACZ,EACIiB,GAAcxD,IAAImB,EAAM7L,CAAM,EACvBA,GAvBP,KAAA,CANC,CA8BL,CCgCI4L,GD/BwB,CACxB,GADS,GC+BgBA,GD7BzBpG,IAAK,CAACY,EAAQyF,EAAMC,IAAaqC,GAAU/H,EAAQyF,CAAI,GAAKgD,GAASrJ,IAAIY,EAAQyF,EAAMC,CAAQ,EAC/F1G,IAAK,CAACgB,EAAQyF,IAAS,CAAC,CAACsC,GAAU/H,EAAQyF,CAAI,GAAKgD,GAASzJ,IAAIgB,EAAQyF,CAAI,CAChF,8CEzEM,IAAMiD,GAAqB,IAErBC,GAAkB,KAAKC,GACvBC,GAAwB,SAExBC,GACX,kDAEWC,GAA0B,KCwBhC,IAAMC,EAAgB,IAAIxS,EDtBV,gBACK,gBCD2C,CACrEyS,4BACE,kDACFC,iBAA4B,2CAC5BC,yBAAoC,mCACpCC,iBACE,6FACFC,cAAyB,kDACzBC,8BACE,2EAgBmB,EAYjB,SAAUC,GAAczQ,GAC5B,OACEA,aAAiBnD,GACjBmD,EAAMhD,KAAKyJ,SAAQ,iBAEvB,CCxCgB,SAAAiK,GAAyB,CAAEC,UAAAA,IACzC,OAAUX,gBAAkCW,iBAC9C,CAEM,SAAUC,GACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,UAgEKC,OAhEwCJ,EAASG,UAgExB9S,QAAQ,IAAK,KAAK,CAAC,EA/DjDgT,aAActQ,KAAKD,IAAK,EAE5B,CAEO0O,eAAe8B,GACpBC,EACAP,GAEA,IACMQ,GAD8BC,MAAMT,EAASU,QACpBvR,MAC/B,OAAOkQ,EAAcvS,OAAiC,iBAAA,CACpDyT,YAAAA,EACAI,WAAYH,EAAUrU,KACtByU,cAAeJ,EAAUpU,QACzByU,aAAcL,EAAUM,MACzB,CAAA,CACH,CAEgB,SAAAC,GAAW,CAAEC,OAAAA,IAC3B,OAAO,IAAIC,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBJ,CACnB,CAAA,CACH,CAEgB,SAAAK,GACdC,EACA,CAAEC,aAAAA,IAEF,IAAMC,EAAUT,GAAWO,CAAS,EAEpC,OADAE,EAAQC,OAAO,iBAmCeF,EAnCyBA,EAoC7CrC,GAAH,IAA4BqC,EApCiC,EAC7DC,CACT,CAeOhD,eAAekD,GACpBC,GAEA,IAAM5D,EAAS0C,MAAMkB,IAErB,OAAqB,KAAjB5D,EAAO+C,QAAiB/C,EAAO+C,OAAS,IAEnCa,EAAE,EAGJ5D,CACT,CCnFM,SAAU6D,GAAMC,GACpB,OAAO,IAAIvI,QAAcC,IACvBrG,WAAWqG,EAASsI,CAAE,CACxB,CAAC,CACH,CCHO,IAAMC,GAAoB,oBACpBC,GAAc,GAMX,SAAAC,KACd,IAGE,IAAMC,EAAe,IAAIC,WAAW,EAAE,EAQhCC,IANJrO,KAAKsO,QAAWtO,KAAyCuO,UACpDC,gBAAgBL,CAAY,EAGnCA,EAAa,GAAK,IAAcA,EAAa,GAAK,ICnBhBM,GACxBC,KAAK9U,OAAO+U,aAAa,GAAGF,CAAK,CAAC,EACnClV,QAAQ,MAAO,GAAG,EAAEA,QAAQ,MAAO,GAAG,GDmB5B4U,CAW+B,EAInCS,OAAO,EAAG,EAAE,GAb3B,OAAOZ,GAAkBa,KAAKR,CAAG,EAAIA,EAAMJ,EAI5C,CAHC,MAAAa,GAEA,OAAOb,EACR,CACH,CEzBM,SAAUc,EAAOvB,GACrB,OAAUA,EAAUwB,QAAb,IAAwBxB,EAAUyB,KAC3C,CCDA,IAAMC,GAA2D,IAAIxI,IAMrD,SAAAyI,GAAW3B,EAAsBa,GAC/C,IAAM3U,EAAMqV,EAAOvB,CAAS,EAwDF9T,GAtD1B0V,GAAuB1V,EAAK2U,CAAG,EACZ3U,GAsDb2V,GASR,KACM,CAACC,GAAoB,qBAAsBtP,QAC7CsP,EAAmB,IAAIC,iBAAiB,uBAAuB,GAC9CC,UAAY/S,IAC3B2S,GAAuB3S,EAAErD,KAAKM,IAAK+C,EAAErD,KAAKiV,GAAG,CAC/C,GAEKiB,MAfHD,GACFA,EAAQI,YAAY,CAAE/V,IAAAA,EAAK2U,IAAAA,CAAK,CAAA,EAkBF,IAA5Ba,GAAmB5N,MAAcgO,IACnCA,EAAiBI,MAAK,EACtBJ,EAAmB,KA3EvB,CAyCA,SAASF,GAAuB1V,EAAa2U,GAC3C,IAAMsB,EAAYT,GAAmBvN,IAAIjI,CAAG,EAC5C,GAAKiW,EAIL,IAAK,IAAMhV,KAAYgV,EACrBhV,EAAS0T,CAAG,CAEhB,CAUAvG,IAAIwH,EAA4C,KCrEhD,IAAMM,GAAgB,kCAChBC,GAAmB,EACnBC,EAAoB,+BAStBC,GAA2D,KAC/D,SAASC,KAgBP,OAfKD,GAAAA,KV1BP,CAAgBrX,EAAMyS,EAAS,CAAE8E,QAAAA,EAASC,QAAAA,EAASC,SAAAA,EAAUC,WAAAA,CAAY,KACrE,IAAMxG,EAAUyG,UAAUC,KAAK5X,EAAMyS,CAAO,EAC5C,IAAMoF,EAAcjI,EAAKsB,CAAO,EAoBhC,OAnBIsG,GACAtG,EAAQpF,iBAAiB,gBAAiB,IACtC0L,EAAQ5H,EAAKsB,EAAQK,MAAM,EAAGuG,EAAMC,WAAYD,EAAME,WAAYpI,EAAKsB,EAAQlB,WAAW,EAAG8H,CAAK,CAC9G,CAAS,EAEDP,GACArG,EAAQpF,iBAAiB,UAAW,GAAWyL,EAE/CO,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,EAE9CD,EACK7K,KAAK,IACF0K,GACAO,EAAGnM,iBAAiB,QAAS,IAAM4L,EAAY,CAAA,EAC/CD,GACAQ,EAAGnM,iBAAiB,gBAAiB,GAAW2L,EAASK,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,CAE/G,CAAK,EACItG,MAAM,MAAS,EACbqG,CACX,GUIuBX,GAAeC,GAAkB,CAClDK,QAAS,CAACS,EAAIF,KAOL,IADCA,GAEJE,EAAGC,kBAAkBd,CAAiB,CAE3C,CACF,CAAA,CAGL,CAeOpF,eAAe7D,EACpB2G,EACA7T,GAEA,IAAMD,EAAMqV,EAAOvB,CAAS,EAEtBvE,GADK0D,MAAMqD,MACHtH,YAAYoH,EAAmB,WAAW,EAClDzH,EAAcY,EAAGZ,YAAYyH,CAAiB,EAC9Ce,EAAQ,MAAUxI,EAAY1G,IAAIjI,CAAG,EAQ3C,OAPAiT,MAAMtE,EAAYyI,IAAInX,EAAOD,CAAG,EAChCiT,MAAM1D,EAAGG,KAEJyH,GAAYA,EAASxC,MAAQ1U,EAAM0U,KACtCc,GAAW3B,EAAW7T,EAAM0U,GAAG,EAG1B1U,CACT,CAGO+Q,eAAeqG,GAAOvD,GAC3B,IAAM9T,EAAMqV,EAAOvB,CAAS,EAEtBvE,GADK0D,MAAMqD,MACHtH,YAAYoH,EAAmB,WAAW,EACxDnD,MAAM1D,EAAGZ,YAAYyH,CAAiB,EAAEtO,OAAO9H,CAAG,EAClDiT,MAAM1D,EAAGG,IACX,CAQOsB,eAAesG,EACpBxD,EACAyD,GAEA,IAAMvX,EAAMqV,EAAOvB,CAAS,EAEtBvE,GADK0D,MAAMqD,MACHtH,YAAYoH,EAAmB,WAAW,EAClDlF,EAAQ3B,EAAGZ,YAAYyH,CAAiB,EACxCe,EAAQ,MAAyCjG,EAAMjJ,IAC3DjI,CAAG,EAECmQ,EAAWoH,EAASJ,CAAQ,EAalC,OAXiBzI,KAAAA,IAAbyB,EACF8C,MAAM/B,EAAMpJ,OAAO9H,CAAG,EAEtBiT,MAAM/B,EAAMkG,IAAIjH,EAAUnQ,CAAG,EAE/BiT,MAAM1D,EAAGG,KAELS,CAAAA,GAAcgH,GAAYA,EAASxC,MAAQxE,EAASwE,KACtDc,GAAW3B,EAAW3D,EAASwE,GAAG,EAG7BxE,CACT,CClFOa,eAAewG,GACpBC,GAEArJ,IAAIsJ,EAEJ,IAAMC,EAAoB1E,MAAMqE,EAAOG,EAAc3D,UAAW8D,IAC9D,IAAMD,EAgCDE,GAhCqDD,GA2Bf,CAC3CjD,IAAKH,GAAa,EAClBsD,mBAA6C,EAGd,EA/BzBC,GAyCV,CACEN,EACAE,KAEA,IAaQK,EAKAN,EAlBR,OAAwC,IAApCC,EAAkBG,mBACfG,UAAUC,QAYTF,EAA+C,CACnDrD,IAAKgD,EAAkBhD,IACvBmD,mBAA6C,EAC7CK,iBAAkB5V,KAAKD,IAAK,GAExBoV,GAkBV1G,MACEyG,EACAE,KAEA,IACE,IAAMS,EAA8BnF,MCxGjCjC,MACL,CAAE8C,UAAAA,EAAWuE,yBAAAA,CAAwB,EACrC,CAAE1D,IAAAA,CAAG,KAEL,IAAM2D,EAAWjG,GAAyByB,CAAS,EAEnD,IAAME,EAAUT,GAAWO,CAAS,EAa9ByE,IAPFC,EAHqBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,KAEOC,EAAmB1F,MAAMuF,EAAiBI,wBAE9C5E,EAAQC,OAAO,oBAAqB0E,CAAgB,EAI3C,CACXhE,IAAAA,EACAkE,YAAanH,GACb6D,MAAOzB,EAAUyB,MACjBuD,WAAYtH,KAGd,IAAMtB,EAAuB,CAC3BzN,OAAQ,OACRuR,QAAAA,EACAuE,KAAMQ,KAAKC,UAAUT,CAAI,GAI3B,IADM/F,EAAWS,MAAMiB,GAAmB,IAAM+E,MAAMX,EAAUpI,CAAO,CAAC,GAC3DgJ,GAQX,MANiE,CAC/DvE,KAFIwE,EAA4ClG,MAAMT,EAASU,QAE5CyB,KAAOA,EAC1BmD,mBAA2C,EAC3C/D,aAAcoF,EAAcpF,aAC5BqF,UAAW7G,GAAiC4G,EAAcC,SAAS,GAIrE,MAAMnG,MAAMH,GAAqB,sBAAuBN,CAAQ,CAEpE,GD4DMiF,EACAE,CAAiB,EAEnB,OAAOxK,EAAIsK,EAAc3D,UAAWsE,CAA2B,CAchE,CAbC,MAAOrV,GAYP,MAXIqP,GAAcrP,CAAC,GAAiC,MAA5BA,EAAElE,WAAWsU,WAGnCF,MAAMoE,GAAOI,EAAc3D,SAAS,EAGpCb,MAAM9F,EAAIsK,EAAc3D,UAAW,CACjCa,IAAKgD,EAAkBhD,IACvBmD,mBAA6C,CAC9C,CAAA,EAEG/U,CACP,CACH,GAzCM0U,EACAO,CAAe,EAEV,CAAEL,kBAAmBK,EAAiBN,oBAAAA,KAnBrC2B,EAA+BvN,QAAQ6D,OAC3CkC,EAAcvS,OAA6B,aAAA,CAAA,EAEtC,CACLqY,kBAAAA,EACAD,oBAAqB2B,IAgBW,IAApC1B,EAAkBG,mBAEX,CACLH,kBAAAA,EACAD,qBAmCN1G,MACEyG,IAMArJ,IAAIkL,EAA2BrG,MAAMsG,GACnC9B,EAAc3D,SAAS,EAEzB,KAA+B,IAAxBwF,EAAMxB,oBAEX7E,MAAMmB,GAAM,GAAG,EAEfkF,EAAQrG,MAAMsG,GAA0B9B,EAAc3D,SAAS,EAGjE,IAEU6D,EAAmBD,EAF7B,OAA4B,IAAxB4B,EAAMxB,mBAaHwB,GAXC,CAAE3B,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAC5CzE,MAAMuE,GAAqBC,CAAa,EAEtCC,GAIKC,EAKb,GAlEoDF,CAAa,GAGtD,CAAEE,kBAAAA,CAAiB,CAE9B,GA7EMF,EACAE,CAAiB,EAGnB,OADAD,EAAsBK,EAAiBL,oBAChCK,EAAiBJ,iBAC1B,CAAC,EAED,OAAIA,EAAkBhD,MAAQJ,GAErB,CAAEoD,kBAAmB1E,MAAMyE,GAG7B,CACLC,kBAAAA,EACAD,oBAAAA,EAEJ,CAoIA,SAAS6B,GACPzF,GAEA,OAAOwD,EAAOxD,EAAW8D,IACvB,GAAKA,EAGL,OAAOC,GAAqBD,CAAQ,EAFlC,MAAM/F,EAAcvS,OAAM,yBAG9B,CAAC,CACH,CAEA,SAASuY,GAAqByB,GAC5B,IAWA3B,EAXA,OAcoE,KAHpEA,EAXmC2B,GAcfxB,oBAClBH,EAAkBQ,iBAAmB5G,GAAqBhP,KAAKD,IAAG,EAd3D,CACLqS,IAAK2E,EAAM3E,IACXmD,mBAA6C,GAI1CwB,CACT,CEzLOtI,eAAewI,GACpB,CAAE1F,UAAAA,EAAWuE,yBAAAA,CAAwB,EACrCV,GAEiB8B,CAwCjB3F,EACEa,GAzCe8E,CAA6B3F,EAAW6D,OAAzD,IAAMW,EA2CIjG,GAAyByB,CAAS,MAAKa,wBAJnD,IACEb,EACEa,EAvCIX,EAAUH,GAAmBC,EAAW6D,CAAiB,EAGzDa,EAAmBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,EAQKH,GAPFC,IACIG,EAAmB1F,MAAMuF,EAAiBI,wBAE9C5E,EAAQC,OAAO,oBAAqB0E,CAAgB,EAI3C,CACXe,aAAc,CACZZ,WAAYtH,GACZ+D,MAAOzB,EAAUyB,KAClB,IAGH,IAAMrF,EAAuB,CAC3BzN,OAAQ,OACRuR,QAAAA,EACAuE,KAAMQ,KAAKC,UAAUT,CAAI,GAGrB/F,EAAWS,MAAMiB,GAAmB,IAAM+E,MAAMX,EAAUpI,CAAO,CAAC,EACxE,GAAIsC,EAAS0G,GAIX,OADE3G,GAF+CU,MAAMT,EAASU,MAEhB,EAGhD,MAAMD,MAAMH,GAAqB,sBAAuBN,CAAQ,CAEpE,CCnCOxB,eAAe2I,GACpBlC,EACAmC,EAAe,CAAA,GAEfxL,IAAIyL,EACJ,IAAMP,EAAQrG,MAAMqE,EAAOG,EAAc3D,UAAW8D,IAClD,GAAI,CAACkC,GAAkBlC,CAAQ,EAC7B,MAAM/F,EAAcvS,OAAM,kBAG5B,IAgIsB8Z,EAhIhBW,EAAenC,EAASwB,UAC9B,GAAKQ,GAiI8C,KAF7BR,EA/HgBW,GAiI5BrH,gBAKc0G,IAC1B,IAAM9W,EAAMC,KAAKD,MACjB,OACEA,EAAM8W,EAAUvG,cAChBuG,EAAUvG,aAAeuG,EAAUzG,UAAYrQ,EAAMsP,EAEzD,GAVwBwH,CAAS,EA/HtB,CAAA,GAA8B,IAA1BW,EAAarH,cAGtB,OADAmH,GA0BN7I,MACEyG,EACAmC,KAMAxL,IAAIkL,EAAQrG,MAAM+G,GAAuBvC,EAAc3D,SAAS,EAChE,KAAoC,IAA7BwF,EAAMF,UAAU1G,eAErBO,MAAMmB,GAAM,GAAG,EAEfkF,EAAQrG,MAAM+G,GAAuBvC,EAAc3D,SAAS,EAG9D,IAAMsF,EAAYE,EAAMF,UACxB,OAA2B,IAAvBA,EAAU1G,cAELiH,GAAiBlC,EAAemC,CAAY,EAE5CR,CAEX,GAjD+C3B,EAAemC,CAAY,EAC7DhC,EAGP,GAAKK,UAAUC,OAMf,OAiIJN,EAnIgEA,EAqI1DqC,EAA2C,CAC/CvH,cAAwC,EACxCwH,YAAa3X,KAAKD,IAAK,GAvIf0V,EAyIV/Y,OAAAyF,OAAAzF,OAAAyF,OAAA,GACKkT,CAAQ,EAAA,CACXwB,UAAWa,CAAmB,CAC9B,EA3IEJ,GAsEN7I,MACEyG,EACAE,KAEA,IACE,IAAMyB,EAAYnG,MAAMuG,GACtB/B,EACAE,CAAiB,EAEbwC,EACDlb,OAAAyF,OAAAzF,OAAAyF,OAAA,GAAAiT,CAAiB,EACpB,CAAAyB,UAAAA,CAAS,GAGX,OADAnG,MAAM9F,EAAIsK,EAAc3D,UAAWqG,CAAwB,EACpDf,CAiBR,CAhBC,MAAOrW,GACP,IAQQoX,EAMR,KAbE/H,CAAAA,GAAcrP,CAAC,GACc,MAA5BA,EAAElE,WAAWsU,YAAkD,MAA5BpQ,EAAElE,WAAWsU,YAM3CgH,EACDlb,OAAAyF,OAAAzF,OAAAyF,OAAA,GAAAiT,CAAiB,EACpB,CAAAyB,UAAW,CAAE1G,cAAa,CAAA,CAA6B,CAAA,EAEzDO,MAAM9F,EAAIsK,EAAc3D,UAAWqG,CAAwB,GAN3DlH,MAAMoE,GAAOI,EAAc3D,SAAS,EAQhC/Q,CACP,CACH,GAtG8C0U,EAAeO,CAAe,EAC/DA,EALL,MAAMnG,EAAcvS,OAAM,cAM7B,CAdC,OAAOsY,CAeX,CAAC,EAKD,OAHkBiC,EACd5G,MAAM4G,EACLP,EAAMF,SAEb,CAyCA,SAASY,GACPlG,GAEA,OAAOwD,EAAOxD,EAAW8D,IACvB,IAIMmC,EAoF2BX,EAxFjC,GAAKU,GAAkBlC,CAAQ,EAK/B,OADMmC,EAAenC,EAASwB,UAsFuB,KAFpBA,EAnFDW,GAqFtBrH,eACV0G,EAAUc,YAAc3I,GAAqBhP,KAAKD,IAAG,EApF9CrD,OAAAyF,OAAAzF,OAAAyF,OAAA,GAAAkT,CAAQ,EACX,CAAAwB,UAAW,CAAE1G,cAAa,CAAA,CAC1B,CAAA,EAGGkF,EAXL,MAAM/F,EAAcvS,OAAM,iBAY9B,CAAC,CACH,CAoCA,SAASwa,GACPnC,GAEA,OACwBjJ,KAAAA,IAAtBiJ,GACgE,IAAhEA,EAAkBG,kBAEtB,CCnJO9G,eAAeoJ,GACpB3C,EACAmC,EAAe,CAAA,GAEf,IAAMS,EAAoB5C,EAKpB2B,GAJNnG,MAaIyE,EAFIA,GAAwBzE,MAAMuE,GAXC6C,CAWiC,GAA3C,sBAI3BpH,CAAAA,MAAMyE,GAXUzE,MAAM0G,GAAiBU,EAAmBT,CAAY,GACxE,OAAOR,EAAU3G,KACnB,CCWA,SAAS6H,GAAqBC,GAC5B,OAAO1I,EAAcvS,OAA4C,4BAAA,CAC/Dib,UAAAA,CACD,CAAA,CACH,CC3BA,IAAMC,GAAqB,gBAGrBC,GAAkD,IAGtD,IAAMC,EAAMC,EAAUC,YAAY,KAAK,EAAEnC,aAAY,EAWrD,MANqD,KACnDiC,EACA5G,WDpB6B4G,IAC/B,GAAI,CAACA,GAAO,CAACA,EAAIG,QACf,MAAMP,GAAqB,mBAAmB,EAGhD,GAAI,CAACI,EAAI1b,KACP,MAAMsb,GAAqB,UAAU,EAIvC,IAMWQ,EAAX,IAAWA,IANsC,CAC/C,YACA,SACA,SAIA,GAAI,CAACJ,EAAIG,QAAQC,GACf,MAAMR,GAAqBQ,CAAO,EAItC,MAAO,CACLxF,QAASoF,EAAI1b,KACbsT,UAAWoI,EAAIG,QAAQvI,UACvBkB,OAAQkH,EAAIG,QAAQrH,OACpB+B,MAAOmF,EAAIG,QAAQtF,MAEvB,GCbqCmF,CAAG,EAMpCrC,yBAL+B0C,GAAAA,aAAaL,EAAK,WAAW,EAM5DM,QAAS,IAAMlP,QAAQC,QAAS,EAGpC,EAEMkP,GAA6D,IAGjE,IAAMP,EAAMC,EAAUC,YAAY,KAAK,EAAEnC,aAAY,EAErD,IAAMhB,EAAgBsD,GAAAA,aAAaL,EAAKF,EAAkB,EAAE/B,aAAY,EAMxE,MAJ8D,CAC5DyC,MAAO,KC5BJlK,MAAqByG,IAC1B,IAAM4C,EAAoB5C,EACpB,CAAEE,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAAKzE,MAAMuE,GACvD6C,CAAiB,EAWnB,OARI3C,GAKFiC,GAAiBU,CAAiB,GAJd7J,MAAM9N,QAAQf,KAAK,EAOlCgW,EAAkBhD,GAC3B,GDauB8C,CAAa,EAChC2C,SAAU,GAA4BA,GAAS3C,EAAemC,CAAY,EAG9E,EAGEuB,GAAkBA,mBAChB,IAAI/a,EAAUoa,GAAoBC,GAAoC,QAAA,CAAA,EAExEU,GAAkBA,mBAChB,IAAI/a,EAtC4B,yBAwC9B6a,GAED,SAAA,CAAA,EExCLG,GAAAA,gBAAgBpc,EAAMyS,EAAO,EAE7B2J,GAAAA,gBAAgBpc,EAAMyS,GAAS,SAAkB,4CCfpC4J,GAAc5J,GAMd6J,GAAuB,wBAmBvBC,GAA2B,+BAE3BC,GACX,qCAEK,ICtBKC,ECAuBC,EFuBtBC,EAAe,cG6BrB,IAAM9J,EAAgB,IAAIxS,EH9BV,cGgCrBsc,EA1CqE,CACrEC,gBAAkC,yCAClCC,gBAAkC,qCAClCC,8BACE,mDACFC,6BACE,kDACFC,YAAuB,2BACvBC,YAAuB,2BACvBC,gBAA2B,+BAC3BC,aAAwB,4BACxBC,iBAA4B,sCAC5BC,iBACE,4EACFC,qBAAuB,wBACvBC,yBACE,8CACFC,0BACE,gDACFC,6BACE,oDACFC,8BACE,uEACFC,sBACE,yPAmBmB,EC9DVC,EAAgB,U5BgH3Ble,YAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAAS8d,UAAG9a,EAsBZhD,KAAW+d,YAAe7a,EAc1BlD,KAAege,gBAAsB,IAzC5C,CAOD1a,eACE,OAAOtD,KAAK8d,SACb,CAEDxa,aAAa2a,GACX,GAAI,EAAEA,KAAOze,GACX,MAAM,IAAI0e,4BAA4BD,6BAA+B,EAEvEje,KAAK8d,UAAYG,CAClB,CAGDE,YAAYF,GACVje,KAAK8d,UAA2B,UAAf,OAAOG,EAAmB9b,EAAkB8b,GAAOA,CACrE,CAODG,iBACE,OAAOpe,KAAK+d,WACb,CACDK,eAAeH,GACb,GAAmB,YAAf,OAAOA,EACT,MAAM,IAAIC,UAAU,mDAAmD,EAEzEle,KAAK+d,YAAcE,CACpB,CAMDI,qBACE,OAAOre,KAAKge,eACb,CACDK,mBAAmBJ,GACjBje,KAAKge,gBAAkBC,CACxB,CAMD7b,SAASiB,GACPrD,KAAKge,iBAAmBhe,KAAKge,gBAAgBhe,KAAMR,EAAS6C,MAAO,GAAGgB,CAAI,EAC1ErD,KAAK+d,YAAY/d,KAAMR,EAAS6C,MAAO,GAAGgB,CAAI,CAC/C,CACDib,OAAOjb,GACLrD,KAAKge,iBACHhe,KAAKge,gBAAgBhe,KAAMR,EAAS+C,QAAS,GAAGc,CAAI,EACtDrD,KAAK+d,YAAY/d,KAAMR,EAAS+C,QAAS,GAAGc,CAAI,CACjD,CACDb,QAAQa,GACNrD,KAAKge,iBAAmBhe,KAAKge,gBAAgBhe,KAAMR,EAASiD,KAAM,GAAGY,CAAI,EACzErD,KAAK+d,YAAY/d,KAAMR,EAASiD,KAAM,GAAGY,CAAI,CAC9C,CACDX,QAAQW,GACNrD,KAAKge,iBAAmBhe,KAAKge,gBAAgBhe,KAAMR,EAASmD,KAAM,GAAGU,CAAI,EACzErD,KAAK+d,YAAY/d,KAAMR,EAASmD,KAAM,GAAGU,CAAI,CAC9C,CACDT,SAASS,GACPrD,KAAKge,iBAAmBhe,KAAKge,gBAAgBhe,KAAMR,EAASqD,MAAO,GAAGQ,CAAI,EAC1ErD,KAAK+d,YAAY/d,KAAMR,EAASqD,MAAO,GAAGQ,CAAI,CAC/C,CACF,E4B/LuCuZ,CAAY,EACpDiB,EAAcva,SAAW9D,EAASiD,KCelC4M,IAAIkP,GACAC,SAcSC,EAaX9e,YAAqB+e,GACnB,GAAI,EADe1e,KAAM0e,OAANA,GAEjB,MAAM5L,EAAcvS,OAAM,aAE5BP,KAAKoH,YAAcsX,EAAOtX,YAC1BpH,KAAK6M,oBAAsB6R,EAAO7R,oBAClC7M,KAAK2e,eAAiBD,EAAOE,SAC7B5e,KAAKkZ,UAAYwF,EAAOxF,UACxBlZ,KAAK8D,SAAW4a,EAAO5a,SACnB9D,KAAKkZ,WAAalZ,KAAKkZ,UAAU2F,gBAGnC7e,KAAK8e,aAAeJ,EAAOI,cAEzBJ,EAAOK,aAAeL,EAAOK,YAAYC,oBAC3Chf,KAAKgf,kBAAoBN,EAAOK,YAAYC,mBAE9Chf,KAAKif,MAAQC,EACblf,KAAKmf,MAAQC,EACbpf,KAAKqf,MAAQC,CACd,CAEDC,SAEE,OAAOvf,KAAK2e,eAAea,KAAKC,MAAM,GAAG,EAAE,EAC5C,CAEDC,KAAKzf,GACED,KAAKoH,aAAgBpH,KAAKoH,YAAYsY,MAG3C1f,KAAKoH,YAAYsY,KAAKzf,CAAI,CAC3B,CAED0f,QAAQC,EAAqBC,EAAeC,GACrC9f,KAAKoH,aAAgBpH,KAAKoH,YAAYuY,SAG3C3f,KAAKoH,YAAYuY,QAAQC,EAAaC,EAAOC,CAAK,CACnD,CAED1U,iBAAiB7J,GACf,OAAKvB,KAAKoH,aAAgBpH,KAAKoH,YAAYgE,iBAGpCpL,KAAKoH,YAAYgE,iBAAiB7J,CAAI,EAFpC,EAGV,CAEDwe,iBAAiB9f,GACf,OAAKD,KAAKoH,aAAgBpH,KAAKoH,YAAY2Y,iBAGpC/f,KAAKoH,YAAY2Y,iBAAiB9f,CAAI,EAFpC,EAGV,CAED+f,gBAEE,OACEhgB,KAAKoH,cACJpH,KAAKoH,YAAY6Y,YAAcjgB,KAAKoH,YAAY8Y,OAAOC,gBAE3D,CAEDC,wBACE,OAAKlG,OAAUnN,SC+GQ,aAArB,OAAOmM,WAA8BA,UAAU2F,cDxG5CwB,CAAAA,OCwDP,IACE,MAA4B,UAArB,OAAOzI,SAGf,CAFC,MAAO5T,IAGX,GD7D6B,IACvB6Z,EAAcrb,KAAK,+CAA+C,EAC3D,CAAA,IARPqb,EAAcrb,KACZ,wGAAwG,EAEnG,CAAA,EAQV,CAED8d,cACEnS,EACAjM,GAEKlC,KAAK6M,qBAGO,IAAI7M,KAAK6M,oBAAoB0T,IAC5C,IAAK,IAAMhG,KAASgG,EAAKrT,aAEvBhL,EAASqY,CAAK,CAElB,CAAC,EAGQvS,QAAQ,CAAEwY,WAAY,CAACrS,EAAY,CAAA,CAC7C,CAEDsS,qBAIE,OAFElC,GADkB5O,KAAAA,IAAhB4O,GACY,IAAIE,EAAID,EAAc,EAE/BD,EACR,CACF,CEnJDlP,IAAIqR,ECAY,SAAAC,GAAaC,EAAeC,GAC1C,IAAMC,EAAWF,EAAMjc,OAASkc,EAAMlc,OACtC,GAAImc,EAAW,GAAgB,EAAXA,EAClB,MAAMhO,EAAcvS,OAAM,+BAG5B,IAAMwgB,EAAc,GACpB,IAAK1R,IAAI7K,EAAI,EAAGA,EAAIoc,EAAMjc,OAAQH,CAAC,GACjCuc,EAAY/a,KAAK4a,EAAMI,OAAOxc,CAAC,CAAC,EAC5Bqc,EAAMlc,OAASH,GACjBuc,EAAY/a,KAAK6a,EAAMG,OAAOxc,CAAC,CAAC,EAIpC,OAAOuc,EAAYE,KAAK,EAAE,CAC5B,CCfA5R,IAAI6R,SAESC,EAAbxhB,cAEEK,KAAsBohB,uBAAG,CAAA,EAGzBphB,KAAqBqhB,sBAAG,CAAA,EAGxBrhB,KAAcshB,eAAG,CAAA,EAEjBthB,KAAkBuhB,mBAAG,EACrBvhB,KAA2BwhB,4BAAG,EAG9BxhB,KAAcyhB,eACZ,oEAGFzhB,KAAA0hB,uBAAyBf,GACvB,mCACA,iCAAiC,EAGnC3gB,KAAA2hB,aAAehB,GAAa,uBAAwB,qBAAqB,EAGzE3gB,KAAS4hB,UAAG,IAGZ5hB,KAAqB6hB,sBAAG,CAAA,EACxB7hB,KAAuB8hB,wBAAG,CAAA,EAG1B9hB,KAAgB+hB,iBAAG,EAYpB,CAVCC,wBACE,OAAOhiB,KAAK0hB,uBAAuBjZ,OAAO,QAASzI,KAAK2hB,YAAY,CACrE,CAEDlB,qBAIE,OAFES,GAD8BvR,KAAAA,IAA5BuR,GACwB,IAAIC,EAEzBD,EACR,CACF,ERvCWxE,EAAAA,EAAAA,GAIX,IAHCA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,SAuCF,IAAMuF,GAA8B,CAAC,YAAa,UAAW,OACvDC,GAAyB,IAAIC,OAAO,gBAAgB,ESlDpD,SAAUC,GAASC,OACjB7L,EAAQ,OAAAH,EAAAgM,EAAYvG,SAAO,KAAA,EAAAzF,EAAEG,MACnC,GAAKA,EAGL,OAAOA,EAFL,MAAM1D,EAAcvS,OAAM,YAG9B,CCKA,IAAM+hB,GAA4B,QAa5BC,EAAmC,CACvCjB,eAAgB,CAAA,GAqBZkB,GAAkB,8BAER,SAAAC,GACdC,EACAhC,GAEA,IAyDAgC,EACAhC,EA1DMiC,GAeR,KACE,IAAM7D,EAAeL,EAAIgC,YAAW,EAAG3B,aACvC,GAAKA,EAAL,CAGA,IAAM8D,EAAe9D,EAAa+D,QAAQpG,EAA+B,EACzE,GAAKmG,IA4IcE,GACZjP,OAAOiP,CAAM,EAAItf,KAAKD,IAAG,GA7IEqf,CAAY,EAA9C,CAIMG,EAAoBjE,EAAa+D,QAAQrG,EAAwB,EACvE,GAAKuG,EAGL,IAEE,OAD6C/I,KAAKgJ,MAAMD,CAAiB,CAI1E,CAFC,MAAA1M,IATD,CAJA,CAgBH,KAlCE,OAAIsM,GACFM,GAAcN,CAAM,EACb5V,QAAQC,YAuDjB0T,EApD8CA,GJvC9CwC,IAEA,IAAMC,EAAmBD,EAAqB7H,WAK9C,OAHA8H,EAAiBlW,KAAK,KAErB,EACMkW,CACT,IIkFET,EAnDuBA,GAuD0BhK,aAAa,EAC3DzL,KAAKoN,IACJ,IAAM9G,GD3GiB8O,QACrB9O,EAAY,OAAA8C,EAAAgM,EAAYvG,SAAO,KAAA,EAAAzF,EAAE9C,UACvC,GAAKA,EAGL,OAAOA,EAFL,MAAMT,EAAcvS,OAAM,gBAG9B,GCqGqCmiB,EAAsB/G,GAAG,EAClDlH,GDpGc4N,QAClB5N,EAAS,OAAA4B,EAAAgM,EAAYvG,SAAO,KAAA,EAAAzF,EAAE5B,OACpC,GAAKA,EAGL,OAAOA,EAFL,MAAM3B,EAAcvS,OAAM,aAG9B,GC8F+BmiB,EAAsB/G,GAAG,EAE5CxK,EAAU,IAAIiS,mEAD8D7P,mCAA2CkB,EACjF,CAC1C/Q,OAAQ,OACRuR,QAAS,CAAEoO,cAAkBb,GAAH,IAAsBnI,CAAa,EAE7Db,KAAMQ,KAAKC,UAAU,CACnBqJ,gBAAiB5C,EACjB6C,sBAAuBlJ,EACvBmJ,OAAQpB,GAASM,EAAsB/G,GAAG,EAC1C8H,YAAanH,GACboH,YAAapB,GACd,CAEF,CAAA,EACD,OAAOpI,MAAM/I,CAAO,EAAElE,KAAKwG,IACzB,GAAIA,EAAS0G,GACX,OAAO1G,EAASU,OAGlB,MAAMrB,EAAcvS,OAAM,qBAC5B,CAAC,CACH,CAAC,EACAkR,MAAM,KACLoM,EAAcrb,KAAKmhB,EAAwB,CAE7C,CAAC,EAnFA1W,KAAKgW,EAAa,EAClBhW,KACC0V,IA4BN,IACQ7D,EADa6D,EA5BOA,EA6BpB7D,EAAeL,EAAIgC,YAAW,EAAG3B,aAClC6D,GAAW7D,IAIhBA,EAAa8E,QAAQpH,GAA0BxC,KAAKC,UAAU0I,CAAM,CAAC,EACrE7D,EAAa8E,QACXnH,GACAtb,OACEqC,KAAKD,IAAK,EACyC,GAAjD4d,EAAgBV,YAAW,EAAGsB,iBAAwB,GAAK,GAAI,CAClE,EAxC6B,EAE5B,MAAQ,EAEd,CAwCA,IAAM4B,GACJ,mDA4CF,SAASV,GACPN,GAEA,IAGMzB,EACApc,EAqDN,OAzDK6d,IAGCzB,EAA0BC,EAAgBV,cAEpB9Q,KAAAA,KADtB7K,EAAU6d,EAAO7d,SAAW,IACtB+e,YAGV3C,EAAwBI,eACU,SAAhCngB,OAAO2D,EAAQ+e,WAAW,EAI5B3C,EAAwBI,eAAiBiB,EAAgBjB,eAEvDxc,EAAQgf,eACV5C,EAAwBU,UAAY/N,OAAO/O,EAAQgf,cAAc,EACxDvB,EAAgBX,YACzBV,EAAwBU,UAAYW,EAAgBX,WAGlD9c,EAAQif,qBACV7C,EAAwBO,eAAiB3c,EAAQif,qBACxCxB,EAAgBd,iBACzBP,EAAwBO,eAAiBc,EAAgBd,gBAIvD3c,EAAQkf,sBACV9C,EAAwBS,aAAe7c,EAAQkf,sBACtCzB,EAAgBZ,eACzBT,EAAwBS,aAAeY,EAAgBZ,cAGJhS,KAAAA,IAAjD7K,EAAQmf,qCACV/C,EAAwBM,4BAA8B3N,OACpD/O,EAAQmf,oCAAoC,EAEWtU,KAAAA,IAAhD4S,EAAgBf,8BACzBN,EAAwBM,4BACtBe,EAAgBf,6BAEuB7R,KAAAA,IAAvC7K,EAAQof,2BACVhD,EAAwBK,mBAAqB1N,OAC3C/O,EAAQof,0BAA0B,EAEYvU,KAAAA,IAAvC4S,EAAgBhB,qBACzBL,EAAwBK,mBACtBgB,EAAgBhB,oBAGpBL,EAAwBW,sBAAwBsC,GAC9CjD,EAAwBK,kBAAkB,EAE5CL,EAAwBY,wBAA0BqC,GAChDjD,EAAwBM,2BAA2B,GAE9CmB,CACT,CAMA,SAASwB,GAAuBC,GAC9B,OAAOpd,KAAK2F,OAAQ,GAAIyX,CAC1B,CCnNA/U,IAAIgV,GAA2D,EAE3DC,GAEE,SAAUC,GACd7B,GAcF,IACEA,EARA,OALA2B,GAAkE,EAElEC,GACEA,KAUF5B,EAV0CA,GAyB5C,KACE,IAAM5e,EAAW2a,EAAIgC,YAAW,EAAG3c,SACnC,OAAO,IAAIiJ,QAAQC,IACjB,GAAIlJ,GAAoC,aAAxBA,EAASC,WAA2B,CAClD,IAAMygB,EAAU,KACc,aAAxB1gB,EAASC,aACXD,EAAS4J,oBAAoB,mBAAoB8W,CAAO,EACxDxX,IAEJ,EACAlJ,EAASiI,iBAAiB,mBAAoByY,CAAO,CACtD,MACCxX,GAEJ,CAAC,CACH,GA5BmC,EAC9BC,KAAK,KAAMwX,OL5BdvB,EK4B4BR,EAAsBhK,eL1B5CgM,EAAaxB,EAAqB/G,SAE7BlP,KAAK,IACdyT,EAAMiE,CACR,CAAC,EACMD,EARH,IACJxB,EAEMwB,CK0ByD,CAAC,EAC7DzX,KAAKyT,GAAO+B,GAAUC,EAAuBhC,CAAG,CAAC,EACjDzT,KACC,IAAM2X,GAA0B,EAChC,IAAMA,GAA4B,CAAA,EAdxC,CAuCA,SAASA,KACPP,GAAwD,CAC1D,CC7DA,IAAMQ,GAA2B,IAE3BC,GAA8B,IAC9BC,GAA0B,EAE5BC,EAAiBD,GA2BjBE,EAAsB,GAEtBC,GAA4B,CAAA,EAiBhC,SAASC,GAAaC,GACpBze,WAAW,KAELqe,GAAkB,IAIH,EAAfC,EAAMtgB,QACR0gB,KAEFF,GAAaN,EAAwB,EACtC,EAAEO,CAAU,CACf,CAEA,SAASC,KAIP,IAAMC,EAASL,EAAM5W,OAAO,EAAGyW,EAA2B,EAI1D,IA8BwBnkB,EAGlB6Y,EAjCA+L,EAAmBD,EAAOtc,IAAIwc,IAAQ,CAC1CC,6BAA8BD,EAAI3lB,QAClC6lB,cAAevkB,OAAOqkB,EAAIG,SAAS,CACpC,EAAC,EAEIhlB,EAAgC,CACpCilB,gBAAiBzkB,OAAOqC,KAAKD,KAAK,EAClCsiB,YAAa,CACXC,YAAa,EACbC,eAAgB,EACjB,EACDC,WAAY7E,EAAgBV,YAAW,EAAGmB,UAC1C2D,UAAAA,GAkBsB5kB,EAdPA,EAeXslB,EACJ9E,EAAgBV,YAAa,EAACuB,sBAAqB,EAC/CxI,EAAOQ,KAAKC,UAAUtZ,CAAI,GAEzBuY,UAAUgN,YAAchN,UAAUgN,WAAWD,EAAoBzM,CAAI,EACxEzM,QAAQC,QAAS,EACjBkN,MAAM+L,EAAoB,CACxBviB,OAAQ,OACR8V,KAAAA,EACA2M,UAAW,CAAA,EACZ,EAAElZ,KAAI,GAxBRA,KAAK,KACJ+X,EAAiBD,EACnB,CAAC,EACAtT,MAAM,KAGLwT,EAAQ,CAAC,GAAGK,EAAQ,GAAGL,GACvBD,CAAc,GACdnH,EAAcrb,oBAAoBwiB,IAAiB,EACnDG,GAAaN,EAAwB,CACvC,CAAC,CACL,UAyBgBuB,GAEdC,GAEA,MAAO,IAAIhjB,KAbOmiB,EAeL,CACT3lB,QAFcwmB,EAAW,GAAGhjB,CAAI,EAGhCsiB,UAAWniB,KAAKD,IAAK,CACtB,EAjBH,GAAI,CAACiiB,EAAIG,WAAa,CAACH,EAAI3lB,QACzB,MAAMiT,EAAcvS,OAAM,kBAG5B0kB,EAAQ,CAAC,GAAGA,EAAOO,EAcnB,CACF,CAMgB,SAAAc,KACd,KAAsB,EAAfrB,EAAMtgB,QACX0gB,IAEJ,CC1EAhW,IAAIkX,EAGJ,SAASC,GACPC,EACAC,IAEKH,EAAAA,GACM,CACPI,KAAMP,GAAiBC,EAAU,EACjCO,MAAON,KAGJK,KAAKF,EAAUC,CAAY,CACpC,CAEM,SAAUG,GAASC,GACvB,IAAMC,EAAkB5F,EAAgBV,cAEpC,CAACsG,EAAgB3F,wBAA0B0F,EAAME,SAIhDD,EAAgB1F,uBAA0ByF,EAAME,SAIhDvI,EAAIgC,YAAa,EAACL,sBAAqB,IF7EqB,IAA1DiE,GEkFL4C,GAAaH,CAAK,EAIlBvC,GAAyBuC,EAAMpE,qBAAqB,EAAEzV,KACpD,IAAMga,GAAaH,CAAK,EACxB,IAAMG,GAAaH,CAAK,CAAC,EAG/B,CAQA,SAASG,GAAaH,GACpB,IAIMC,EPjHCrG,IOiHDqG,EAAkB5F,EAAgBV,eAErBa,gBAChByF,EAAgBlF,uBAKnB2E,GAAQM,EAAK,EACf,CAkCA,SAAST,GACPI,EACAC,GAEA,IA0BMQ,EAUAC,EApCN,OAAgD,IAA5CT,GAOEU,EAA6C,CACjDjc,IAP+Bsb,EAOXtb,IACpBkc,YAR+BZ,EAQHa,YAAc,EAC1CC,mBAAoB,IACpBC,uBAV+Bf,EAUQgB,qBACvCC,qBAX+BjB,EAWMkB,YACrCC,8BAZ+BnB,EAYeoB,0BAC9CC,8BAb+BrB,EAaesB,2BAE1CC,EAA6B,CACjCC,iBAAkBC,GAhBazB,EAiBd/D,sBAAsB/G,GAAG,EAE1CwM,uBAAwBf,GAEnBpN,KAAKC,UAAU+N,CAAU,IAI1Bd,EAA2B,CAC/BjnB,MAFoB6mB,EAtBAL,GAwBRxmB,KACZmoB,QAAStB,EAAME,OACfU,qBAAsBZ,EAAMa,YAC5BU,YAAavB,EAAMwB,YAGsB,IAAvCpoB,OAAOqoB,KAAKzB,EAAM0B,QAAQ,EAAE7jB,SAC9BuiB,EAAYsB,SAAW1B,EAAM0B,UAEzBrB,EAAmBL,EAAM2B,gBACc,IAAzCvoB,OAAOqoB,KAAKpB,CAAgB,EAAExiB,SAChCuiB,EAAYwB,kBAAoBvB,GAG5Ba,EAA2B,CAC/BC,iBAAkBC,GAAmBpB,EAAMpE,sBAAsB/G,GAAG,EACpEgN,aAAczB,GAETlN,KAAKC,UAAU+N,CAAU,EAzClC,CA4CA,SAASE,GAAmB7F,GAC1B,MAAO,CACLuG,cAAexG,GAASC,CAAW,EACnCiB,gBPnNK5C,EOoNLmI,aAAc,CACZnF,YAAapH,GACbwM,SAAUrK,EAAIgC,YAAa,EAAClB,OAAQ,EACpCwJ,sBb9KA7P,OADEA,EAAYuF,EAAIgC,YAAW,EAAGvH,YAChCA,EAAW8P,cACT9P,EAAU8P,cAAcC,WACY,EAEE,EAGH,EawKrCC,kBbpKU,KAGd,OAFiBzK,EAAIgC,YAAW,EAAG3c,SACFsJ,iBAE/B,IAAK,UACH,OAAOsP,EAAgByM,QACzB,IAAK,SACH,OAAOzM,EAAgB0M,OACzB,QACE,OAAO1M,EAAgB2M,OAC1B,CACH,GayJ4C,EACtCC,2BbxJU,KACd,IACMC,EADY9K,EAAIgC,YAAW,EAAGvH,UAC+BsQ,WAGnE,OADED,GAAuBA,EAAoBE,eAE3C,IAAK,UACH,OAAkD,EACpD,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,QACE,OAAuC,CAC1C,CACH,GauI6D,CACxD,EACDC,0BAA2B,GbpLf,IACRxQ,CaqLR,CC9MgB,SAAAyQ,GACdjH,EACAnI,GAEA,IDkHMqP,EAIAC,EAZA9C,EC1GA+C,EAAmBvP,EACpBuP,GAAuDna,KAAAA,IAAnCma,EAAiBze,gBAGpC4U,EAAaxB,EAAIgC,YAAa,EAACT,cAAa,EAC5C2H,EAAc3gB,KAAK0F,MACqB,KAA3Cod,EAAiBxkB,UAAY2a,EAAkB,EAE5C4H,EAA4BiC,EAAiBze,cAC/CrE,KAAK0F,MAC6D,KAA/Dod,EAAiBze,cAAgBye,EAAiBxkB,UAAiB,EAEtEqK,KAAAA,EACEoY,EAA4B/gB,KAAK0F,MACyB,KAA7Dod,EAAiBve,YAAcue,EAAiBxkB,UAAiB,EAI9DykB,EAAiC,CACrCrH,sBAAAA,EACAvX,IAHU2e,EAAiB7pB,MAAQ6pB,EAAiB7pB,KAAKwf,MAAM,GAAG,EAAE,GAIpEgI,qBAAsBqC,EAAiBE,aACvCrC,YAAAA,EACAE,0BAAAA,EACAE,0BAAAA,GDiF8BgC,EC9EdA,GD+EZhD,EAAkB5F,EAAgBV,eAEnBW,0BAMfwI,EAAoBG,EAAe5e,IAInC0e,EAAiB9C,EAAgBtF,eAAehC,MAAM,GAAG,EAAE,GAC3DwK,EAAgBlD,EAAgBrF,uBAAuBjC,MAAM,GAAG,EAAE,GAEtEmK,IAAsBC,IACtBD,IAAsBK,GAMrBlD,EAAgBzF,gBAChByF,EAAgBjF,yBAKnB0E,GAAQuD,EAAc,EC1GxB,CCtDA,IAEMG,GAAa,ChBDqB,MAEW,OAEL,OAEM,OAMD,OAHE,ciBexCC,EAoBXxqB,YACW+iB,EACAziB,EACA+mB,EAAS,CAAA,EAClBoD,GAHSpqB,KAAqB0iB,sBAArBA,EACA1iB,KAAIC,KAAJA,EACAD,KAAMgnB,OAANA,EAtBHhnB,KAAAqqB,MAA6C,EAG7CrqB,KAAgBmnB,iBAA8B,GACtDnnB,KAAQwoB,SAAsC,GACtCxoB,KAAAsqB,IAAM7L,EAAIgC,cACVzgB,KAAAuqB,SAAWvjB,KAAK0F,MAAsB,IAAhB1F,KAAK2F,OAAM,CAAY,EAmB9C3M,KAAKgnB,SACRhnB,KAAKwqB,sCAA+CxqB,KAAKuqB,YAAYvqB,KAAKC,KAC1ED,KAAKyqB,oCAA6CzqB,KAAKuqB,YAAYvqB,KAAKC,KACxED,KAAK0qB,aACHN,MACG7N,MAAwBvc,KAAKuqB,YAAYvqB,KAAKC,KAE/CmqB,GAGFpqB,KAAK2qB,sBAAqB,EAG/B,CAKDC,QACE,GAAc,IAAV5qB,KAAKqqB,MACP,MAAMvX,EAAcvS,OAAuC,gBAAA,CACzDsqB,UAAW7qB,KAAKC,IACjB,CAAA,EAEHD,KAAKsqB,IAAI5K,KAAK1f,KAAKwqB,cAAc,EACjCxqB,KAAKqqB,MAAK,CACX,CAMDS,OACE,GAAc,IAAV9qB,KAAKqqB,MACP,MAAMvX,EAAcvS,OAAuC,gBAAA,CACzDsqB,UAAW7qB,KAAKC,IACjB,CAAA,EAEHD,KAAKqqB,MAAK,EACVrqB,KAAKsqB,IAAI5K,KAAK1f,KAAKyqB,aAAa,EAChCzqB,KAAKsqB,IAAI3K,QACP3f,KAAK0qB,aACL1qB,KAAKwqB,eACLxqB,KAAKyqB,aAAa,EAEpBzqB,KAAK2qB,sBAAqB,EAC1B9D,GAAS7mB,IAAI,CACd,CASD+qB,OACEzlB,EACA0E,EACA8R,GAKA,GAAIxW,GAAa,EACf,MAAMwN,EAAcvS,OAA+C,8BAAA,CACjEsqB,UAAW7qB,KAAKC,IACjB,CAAA,EAEH,GAAI+J,GAAY,EACd,MAAM8I,EAAcvS,OAA6C,6BAAA,CAC/DsqB,UAAW7qB,KAAKC,IACjB,CAAA,EAQH,GALAD,KAAKsoB,WAAathB,KAAK0F,MAAiB,IAAX1C,CAAe,EAC5ChK,KAAK2nB,YAAc3gB,KAAK0F,MAAkB,IAAZpH,CAAgB,EAC1CwW,GAAWA,EAAQkP,aACrBhrB,KAAKmnB,iBAAgBjnB,OAAAyF,OAAA,GAAQmW,EAAQkP,UAAU,GAE7ClP,GAAWA,EAAQmP,QACrB,IAAK,IAAMC,KAAchrB,OAAOqoB,KAAKzM,EAAQmP,OAAO,EAC7CE,MAAMtX,OAAOiI,EAAQmP,QAAQC,EAAW,CAAC,IAC5ClrB,KAAKwoB,SAAS0C,GAAclkB,KAAK0F,MAC/BmH,OAAOiI,EAAQmP,QAAQC,EAAW,CAAC,GAK3CrE,GAAS7mB,IAAI,CACd,CASDorB,gBAAgBC,EAAiBC,EAAe,GACf3b,KAAAA,IAA3B3P,KAAKwoB,SAAS6C,GAChBrrB,KAAKurB,UAAUF,EAASC,CAAY,EAEpCtrB,KAAKurB,UAAUF,EAASrrB,KAAKwoB,SAAS6C,GAAWC,CAAY,CAEhE,CAQDC,UAAUF,EAAiBC,GACzB,GDtJ8BrrB,ECsJRorB,EDtJsBR,ECsJb7qB,KAAKC,KDrJlB,IAAhBA,EAAK0E,QAhBoB,IAgBJ1E,EAAK0E,QAI5B,EAACkmB,GACCA,EAAUxgB,WhBtB0B,MgBsBW,GACpB,CAAC,EAA5B6f,GAAWsB,QAAQvrB,CAAI,IACxBA,EAAKoK,WAtBmB,GAsBY,ECiJnC,MAAMyI,EAAcvS,OAA6C,6BAAA,CAC/DkrB,iBAAkBJ,CACnB,CAAA,EDzID,IACEK,EAnB0BzrB,EAAc4qB,ECuJ1C7qB,KAAKwoB,SAAS6C,IDrIwBM,ECqIeL,MAAAA,EAAAA,EAAgB,GDpInEI,EAAyB1kB,KAAK0F,MAAMif,CAAa,GAClCA,GACnB9N,EAAcrb,kEACiDkpB,IAAiB,EAG3EA,ECoIN,CAODE,UAAUP,GACR,OAAOrrB,KAAKwoB,SAAS6C,IAAY,CAClC,CAODQ,aAAaC,EAAc5qB,GACzB,IhBlGuCjB,EAUCiB,EgBwFlC6qB,EhBjGR,EAAoB,KADqB9rB,EgBkGQ6rB,GhBjGxCnnB,QAjDuB,GAiDP1E,EAAK0E,QAGAsd,GAA4BpT,KAAKmd,GAC7D/rB,EAAKoK,WAAW2hB,CAAM,CAAC,GAES,CAAC/rB,EAAKgsB,MAAM/J,EAAsB,GgB4F5DgK,EhBxFgB,KADkBhrB,EgByFSA,GhBxFtCyD,QAAgBzD,EAAMyD,QA1DF,IgBmJ/B,GAAIonB,GAAeG,EACjBlsB,KAAKmnB,iBAAiB2E,GAAQ5qB,MADhC,CAKA,GAAI,CAAC6qB,EACH,MAAMjZ,EAAcvS,OAAyC,yBAAA,CAC3D4rB,cAAeL,CAChB,CAAA,EAEH,GAAI,CAACI,EACH,MAAMpZ,EAAcvS,OAA0C,0BAAA,CAC5D6rB,eAAgBlrB,CACjB,CAAA,CAVF,CAYF,CAMDmrB,aAAaP,GACX,OAAO9rB,KAAKmnB,iBAAiB2E,EAC9B,CAEDQ,gBAAgBR,GACsBnc,KAAAA,IAAhC3P,KAAKmnB,iBAAiB2E,IAG1B,OAAO9rB,KAAKmnB,iBAAiB2E,EAC9B,CAEDrD,gBACE,OAAYvoB,OAAAyF,OAAA,GAAA3F,KAAKmnB,gBAAgB,CAClC,CAEOoF,aAAajnB,GACnBtF,KAAK2nB,YAAcriB,CACpB,CAEOknB,YAAYxiB,GAClBhK,KAAKsoB,WAAate,CACnB,CAMO2gB,wBACN,IAAM8B,EAAqBzsB,KAAKsqB,IAAIvK,iBAAiB/f,KAAK0qB,YAAY,EAChEgC,EAAmBD,GAAsBA,EAAmB,GAC9DC,IACF1sB,KAAKsoB,WAAathB,KAAK0F,MAAkC,IAA5BggB,EAAiB1iB,QAAe,EAC7DhK,KAAK2nB,YAAc3gB,KAAK0F,MACoC,KAAzDggB,EAAiBpnB,UAAYtF,KAAKsqB,IAAItK,cAAe,EAAQ,EAGnE,CAQD2M,sBACEjK,EACAkK,EACAC,EACAC,EACAC,GAEA,IAAMC,EAAQvO,EAAIgC,YAAa,EAAClB,OAAM,EACtC,GAAKyN,EAAL,CAGA,IAAMlG,EAAQ,IAAIqD,EAChBzH,EjB7QoC,OiB8QPsK,EAC7B,CAAA,CAAI,EAEAC,EAAejmB,KAAK0F,MAA0C,IAApC+R,EAAIgC,cAAcT,eAAsB,EACxE8G,EAAMyF,aAAaU,CAAY,EAG3BL,GAAqBA,EAAkB,KACzC9F,EAAM0F,YAAYxlB,KAAK0F,MAAsC,IAAhCkgB,EAAkB,GAAG5iB,QAAe,CAAC,EAClE8c,EAAMyE,UACJ,iBACAvkB,KAAK0F,MAA4C,IAAtCkgB,EAAkB,GAAG1oB,cAAqB,CAAC,EAExD4iB,EAAMyE,UACJ,2BACAvkB,KAAK0F,MAAsD,IAAhDkgB,EAAkB,GAAGM,wBAA+B,CAAC,EAElEpG,EAAMyE,UACJ,eACAvkB,KAAK0F,MAA0C,IAApCkgB,EAAkB,GAAGO,YAAmB,CAAC,GAMpDN,KACIO,EAAaP,EAAa3nB,KAC9BmoB,GAJgB,gBAIDA,EAAYptB,IAAoB,IAE/BmtB,EAAW9nB,WAC3BwhB,EAAMyE,UjB1S0B,MiB4S9BvkB,KAAK0F,MAA6B,IAAvB0gB,EAAW9nB,SAAgB,CAAC,GAGrCgoB,EAAuBT,EAAa3nB,KACxCmoB,GAZ2B,2BAYZA,EAAYptB,IAA+B,IAEhCqtB,EAAqBhoB,WAC/CwhB,EAAMyE,UjBjTqC,OiBmTzCvkB,KAAK0F,MAAuC,IAAjC4gB,EAAqBhoB,SAAgB,CAAC,EAIjDynB,IACFjG,EAAMyE,UjBtTgC,OiBwTpCvkB,KAAK0F,MAAwB,IAAlBqgB,CAAsB,CAAC,EAKxC/sB,KAAKutB,kBACHzG,EjB5T8C,OACG,ciB8TjDgG,EAAgBU,GAAG,EAErBxtB,KAAKutB,kBACHzG,EjB5T6C,OACG,yBiB8ThDgG,EAAgBW,GAAG,EAErBztB,KAAKutB,kBACHzG,EjBrU+C,OACG,wBiBuUlDgG,EAAgBY,GAAG,EAKrB7G,GAASC,CAAK,EHzOZP,GACFA,EAAOK,MAAK,CG2JX,CA+EF,CAED2G,yBACEzG,EACA6G,EACAC,EACAC,GAEIA,IACF/G,EAAMyE,UAAUoC,EAAW3mB,KAAK0F,MAAqB,IAAfmhB,EAAO3sB,KAAY,CAAC,EACtD2sB,EAAOC,qBACThH,EAAM+E,aAAa+B,EAAcC,EAAOC,kBAAkB,CAG/D,CAEDC,6BACErL,EACA9C,GAQAiH,GANc,IAAIsD,EAChBzH,EACA9C,EACA,CAAA,EACAA,CAAW,CAEC,CACf,CACF,CCjXDvQ,IAAIyd,GAAmC,GACnCkB,GAA6B,CAAA,EAC7BjB,GAEE,SAAUkB,GACdvL,GXFOhC,IWWP/Z,WAAW,KAAMunB,CAAAA,IAkBKxL,EAlBUA,EAmBhC,IAAM4H,EAAM7L,EAAIgC,cAEZ,eAAgB/B,OAClB4L,EAAIxmB,SAASiI,iBAAiB,WAAY,IACxCoiB,GAAazL,CAAqB,CAAC,EAGrC4H,EAAIxmB,SAASiI,iBAAiB,SAAU,IACtCoiB,GAAazL,CAAqB,CAAC,EAGvC4H,EAAIxmB,SAASiI,iBAAiB,mBAAoB,KACX,WAAjCue,EAAIxmB,SAASsJ,iBACf+gB,GAAazL,CAAqB,CAEtC,CAAC,EAEG4H,EAAItL,mBACNsL,EAAItL,kBAAkB,IACpB+N,GAAkBnX,CACpB,CAAC,EAGH0U,EAAIrL,MAAM,UACR6N,GAAgBU,IAAM,CACpBtsB,MAAO2sB,EAAO3sB,MACd4sB,mBAAoB,OAAAzX,EAAAwX,EAAOjoB,aAAW,KAAA,EAAAyQ,EAAE7K,QAE5C,CAAC,EACD8e,EAAIjL,MAAM,UACRyN,GAAgBW,IAAM,CACpBvsB,MAAO2sB,EAAO3sB,MACd4sB,mBAAoB,OAAAzX,EAAAwX,EAAOjoB,aAAW,KAAA,EAAAyQ,EAAEjR,mBAE5C,CAAC,EACDklB,EAAInL,MAAM,UACR2N,GAAgBY,IAAM,CACpBxsB,MAAO2sB,EAAO3sB,MACd4sB,mBAAoB,OAAAzX,EAAAwX,EAAOjoB,aAAW,KAAA,EAAAyQ,EAAEnM,kBAE5C,CAAC,CA3DoD,CAAA,EAAG,CAAC,EACzDvD,WAAW,KAAMynB,IASN3H,EAJX/D,EALsCA,EAOhC4H,EAAM7L,EAAIgC,cAEhB,IAAWgG,KADO6D,EAAIlf,iBAAiB,UAAU,EAE/Cue,GAA0BjH,EAAuB+D,CAAQ,EAE3D6D,EAAIhK,cAAc,WAAY/F,GAC5BoP,GAA0BjH,EAAuBnI,CAAK,CAAC,CAbE,EAAG,CAAC,EAC/D5T,WAAW,KAAM0nB,IAkEN1O,EALX+C,EA7DuCA,EA+DjC4H,EAAM7L,EAAIgC,cAGhB,IAAWd,KADM2K,EAAIlf,iBAAiB,SAAS,EAE7C2iB,GAAsBrL,EAAuB/C,CAAO,EAGtD2K,EAAIhK,cAAc,UAAW/F,GAC3BwT,GAAsBrL,EAAuBnI,CAAK,CAAC,CAvEO,EAAG,CAAC,EAClE,CA0EA,SAASwT,GACPrL,EACA/C,GAEA,IAAMC,EAAcD,EAAQ1f,KAI1B2f,EAAY0O,UAAU,EAAG/R,GAAqB5X,MAAM,IACpD4X,IAIF4N,EAAM4D,sBAAsBrL,EAAuB9C,CAAW,CAChE,CAEA,SAASuO,GAAazL,GACpB,GAAI,CAACsL,GAAmB,CACtBA,GAAoB,CAAA,EACpB,IAAM1D,EAAM7L,EAAIgC,cAChB,IAAMmM,EAAoBtC,EAAIlf,iBAC5B,YAAY,EAERyhB,EAAevC,EAAIlf,iBAAiB,OAAO,EAIjDzE,WAAW,KACTwjB,EAAMwC,eACJjK,EACAkK,EACAC,EACAC,GACAC,EAAe,CAElB,EAAE,CAAC,CACL,CACH,OCpIawB,GAGX5uB,YACWgc,EACAjD,GADA1Y,KAAG2b,IAAHA,EACA3b,KAAa0Y,cAAbA,EAJH1Y,KAAWwuB,YAAY,CAAA,CAK3B,CAWJC,MAAMC,GACA1uB,KAAKwuB,cAI+B7e,KAAAA,KAApC+e,MAAAA,EAAQ,KAAA,EAARA,EAAUrN,yBACZrhB,KAAKqhB,sBAAwBqN,EAASrN,uBAEC1R,KAAAA,KAArC+e,MAAAA,EAAQ,KAAA,EAARA,EAAUtN,0BACZphB,KAAKohB,uBAAyBsN,EAAStN,wBAGrC3C,EAAIgC,cAAcL,wBbqJjB,IAAIrT,QAAQ,CAACC,EAAS4D,KAC3B,IACEvB,IAAIsf,EAAoB,CAAA,EAClBC,EACJ,0DACIzd,EAAU5J,KAAKqQ,UAAUC,KAAK+W,CAAa,EACjDzd,EAAQ0d,UAAY,KAClB1d,EAAQK,OAAOyF,QAEV0X,GACHpnB,KAAKqQ,UAAUkX,eAAeF,CAAa,EAE7C5hB,EAAQ,CAAA,CAAI,CACd,EACAmE,EAAQ4d,gBAAkB,KACxBJ,EAAW,CAAA,CACb,EAEAxd,EAAQ6d,QAAU,WAChBpe,GAAO,OAAAyF,EAAAlF,EAAQvO,OAAK,KAAA,EAAAyT,EAAExW,UAAW,EAAE,CACrC,CAGD,CAFC,MAAO+C,GACPgO,EAAOhO,CAAK,CACb,CACH,CAAC,Ea3KMqK,KAAKgiB,IACAA,INFP/J,KACHC,GArC+B,IAqCQ,EACvCD,GAAmB,CAAA,GMEXX,GAAyBvkB,IAAI,EAAEiN,KAC7B,IAAMghB,GAAkBjuB,IAAI,EAC5B,IAAMiuB,GAAkBjuB,IAAI,CAAC,EAE/BA,KAAKwuB,YAAc,CAAA,EAEvB,CAAC,EACA/c,MAAM7O,IACLib,EAAcrb,KAAK,0CAA0CI,CAAO,CACtE,CAAC,EAEHib,EAAcrb,KACZ,mHACmD,EAGxD,CAED4e,2BAA2BnD,GACzBkD,EAAgBV,YAAW,EAAGW,uBAAyBnD,CACxD,CACDmD,6BACE,OAAOD,EAAgBV,YAAa,EAACW,sBACtC,CAEDC,0BAA0BpD,GACxBkD,EAAgBV,YAAW,EAAGY,sBAAwBpD,CACvD,CACDoD,4BACE,OAAOF,EAAgBV,YAAa,EAACY,qBACtC,CACF,CC3CD,IAAM6N,GAAqB,YA6D3B,IAAMC,GAA0C,CAC9CvT,EACA,CAAEE,QAAS4S,CAAQ,KAGnB,IAAM/S,EAAMC,EAAUC,YAAY,KAAK,EAAEnC,aAAY,EAC/ChB,EAAgBkD,EACnBC,YAAY,wBAAwB,EACpCnC,eAEH,GAAIiC,EAAI1b,OAASivB,GACf,MAAMpc,EAAcvS,OAAM,kBAE5B,GAAsB,aAAlB,OAAOme,OACT,MAAM5L,EAAcvS,OAAM,af2CLme,EezCdA,Of0CTF,GAAiBE,EezCX0Q,EAAe,IAAIb,GAAsB5S,EAAKjD,CAAa,EAGjE,OAFA0W,EAAaX,MAAMC,CAAQ,EAEpBU,CACT,EAGEhT,GAAkBA,mBAChB,IAAI/a,EAAU,cAAe8tB,GAA8B,QAAA,CAAA,EAE7D9S,mBAAgBpc,GAAMyS,EAAO,EAE7B2J,GAAAA,gBAAgBpc,GAAMyS,GAAS,SAAkB,QClHtC2c,GAGX1vB,YACSgc,EACE2T,GADFtvB,KAAG2b,IAAHA,EACE3b,KAASsvB,UAATA,CACP,CAEJlO,6BACE,OAAOphB,KAAKsvB,UAAUlO,sBACvB,CAEDA,2BAA2BnD,GACzBje,KAAKsvB,UAAUlO,uBAAyBnD,CACzC,CAEDoD,4BACE,OAAOrhB,KAAKsvB,UAAUjO,qBACvB,CAEDA,0BAA0BpD,GACxBje,KAAKsvB,UAAUjO,sBAAwBpD,CACxC,CAED6I,MAAM+D,GACJ,ODqDFzjB,ECrDepH,KAAKsvB,UDsDpBrvB,ECtD+B4qB,EDwD/BzjB,GErFA5G,EFqFiC4G,IEnFjB5G,EAA+B8uB,UACrC9uB,EAA+B8uB,UAEhC9uB,EFiFF,IAAI2pB,EAAM/iB,EAAsCnH,CAAI,EAL7C,IACdmH,EElFA5G,CD8BC,CACF,CnBdD,SAAS+uB,GACP3T,GAEA,IAAMD,EAAMC,EAAUC,YAAY,YAAY,EAAEnC,aAAY,EAEtDtS,EAAcwU,EAAUC,YAAY,aAAa,EAAEnC,aAAY,EAErE,OAAO,IAAI2V,GAAsB1T,EAAKvU,CAAW,CACnD,EApBmCuV,EAsBT6S,WArBPC,SAASC,kBACxB,IAAIruB,EACF,qBACAkuB,GAAkB,QAAA,CAEnB,EAGH5S,EAAiBN,uDAAoC"}