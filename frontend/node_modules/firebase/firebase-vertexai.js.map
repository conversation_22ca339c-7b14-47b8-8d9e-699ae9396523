{"version": 3, "file": "firebase-vertexai.js", "sources": ["../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../logger/src/logger.ts", "../ai/src/constants.ts", "../ai/src/types/enums.ts", "../ai/src/types/schema.ts", "../ai/src/types/imagen/requests.ts", "../ai/src/public-types.ts", "../ai/src/backend.ts", "../ai/src/service.ts", "../ai/src/errors.ts", "../ai/src/helpers.ts", "../ai/src/models/ai-model.ts", "../ai/src/logger.ts", "../ai/src/requests/request.ts", "../ai/src/requests/response-helpers.ts", "../ai/src/googleai-mappers.ts", "../ai/src/requests/stream-reader.ts", "../ai/src/methods/generate-content.ts", "../ai/src/requests/request-helpers.ts", "../ai/src/methods/chat-session-helpers.ts", "../ai/src/methods/chat-session.ts", "../ai/src/methods/count-tokens.ts", "../ai/src/models/generative-model.ts", "../ai/src/models/imagen-model.ts", "../ai/src/requests/schema-builder.ts", "../ai/src/requests/imagen-image-format.ts", "../ai/src/api.ts", "../ai/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const AI_TYPE = 'AI';\n\nexport const DEFAULT_LOCATION = 'us-central1';\n\nexport const DEFAULT_BASE_URL = 'https://firebasevertexai.googleapis.com';\n\nexport const DEFAULT_API_VERSION = 'v1beta';\n\nexport const PACKAGE_VERSION = version;\n\nexport const LANGUAGE_TAG = 'gl-js';\n\nexport const DEFAULT_FETCH_TIMEOUT_MS = 180 * 1000;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Role is the producer of the content.\n * @public\n */\nexport type Role = (typeof POSSIBLE_ROLES)[number];\n\n/**\n * Possible roles.\n * @public\n */\nexport const POSSIBLE_ROLES = ['user', 'model', 'function', 'system'] as const;\n\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nexport enum HarmCategory {\n  HARM_CATEGORY_HATE_SPEECH = 'HARM_CATEGORY_HATE_SPEECH',\n  HARM_CATEGORY_SEXUALLY_EXPLICIT = 'HARM_CATEGORY_SEXUALLY_EXPLICIT',\n  HARM_CATEGORY_HARASSMENT = 'HARM_CATEGORY_HARASSMENT',\n  HARM_CATEGORY_DANGEROUS_CONTENT = 'HARM_CATEGORY_DANGEROUS_CONTENT'\n}\n\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nexport enum HarmBlockThreshold {\n  /**\n   * Content with `NEGLIGIBLE` will be allowed.\n   */\n  BLOCK_LOW_AND_ABOVE = 'BLOCK_LOW_AND_ABOVE',\n  /**\n   * Content with `NEGLIGIBLE` and `LOW` will be allowed.\n   */\n  BLOCK_MEDIUM_AND_ABOVE = 'BLOCK_MEDIUM_AND_ABOVE',\n  /**\n   * Content with `NEGLIGIBLE`, `LOW`, and `MEDIUM` will be allowed.\n   */\n  BLOCK_ONLY_HIGH = 'BLOCK_ONLY_HIGH',\n  /**\n   * All content will be allowed.\n   */\n  BLOCK_NONE = 'BLOCK_NONE',\n  /**\n   * All content will be allowed. This is the same as `BLOCK_NONE`, but the metadata corresponding\n   * to the {@link HarmCategory} will not be present in the response.\n   */\n  OFF = 'OFF'\n}\n\n/**\n * This property is not supported in the Gemini Developer API ({@link GoogleAIBackend}).\n *\n * @public\n */\nexport enum HarmBlockMethod {\n  /**\n   * The harm block method uses both probability and severity scores.\n   */\n  SEVERITY = 'SEVERITY',\n  /**\n   * The harm block method uses the probability score.\n   */\n  PROBABILITY = 'PROBABILITY'\n}\n\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nexport enum HarmProbability {\n  /**\n   * Content has a negligible chance of being unsafe.\n   */\n  NEGLIGIBLE = 'NEGLIGIBLE',\n  /**\n   * Content has a low chance of being unsafe.\n   */\n  LOW = 'LOW',\n  /**\n   * Content has a medium chance of being unsafe.\n   */\n  MEDIUM = 'MEDIUM',\n  /**\n   * Content has a high chance of being unsafe.\n   */\n  HIGH = 'HIGH'\n}\n\n/**\n * Harm severity levels.\n * @public\n */\nexport enum HarmSeverity {\n  /**\n   * Negligible level of harm severity.\n   */\n  HARM_SEVERITY_NEGLIGIBLE = 'HARM_SEVERITY_NEGLIGIBLE',\n  /**\n   * Low level of harm severity.\n   */\n  HARM_SEVERITY_LOW = 'HARM_SEVERITY_LOW',\n  /**\n   * Medium level of harm severity.\n   */\n  HARM_SEVERITY_MEDIUM = 'HARM_SEVERITY_MEDIUM',\n  /**\n   * High level of harm severity.\n   */\n  HARM_SEVERITY_HIGH = 'HARM_SEVERITY_HIGH',\n  /**\n   * Harm severity is not supported.\n   *\n   * @remarks\n   * The GoogleAI backend does not support `HarmSeverity`, so this value is used as a fallback.\n   */\n  HARM_SEVERITY_UNSUPPORTED = 'HARM_SEVERITY_UNSUPPORTED'\n}\n\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nexport enum BlockReason {\n  /**\n   * Content was blocked by safety settings.\n   */\n  SAFETY = 'SAFETY',\n  /**\n   * Content was blocked, but the reason is uncategorized.\n   */\n  OTHER = 'OTHER',\n  /**\n   * Content was blocked because it contained terms from the terminology blocklist.\n   */\n  BLOCKLIST = 'BLOCKLIST',\n  /**\n   * Content was blocked due to prohibited content.\n   */\n  PROHIBITED_CONTENT = 'PROHIBITED_CONTENT'\n}\n\n/**\n * Reason that a candidate finished.\n * @public\n */\nexport enum FinishReason {\n  /**\n   * Natural stop point of the model or provided stop sequence.\n   */\n  STOP = 'STOP',\n  /**\n   * The maximum number of tokens as specified in the request was reached.\n   */\n  MAX_TOKENS = 'MAX_TOKENS',\n  /**\n   * The candidate content was flagged for safety reasons.\n   */\n  SAFETY = 'SAFETY',\n  /**\n   * The candidate content was flagged for recitation reasons.\n   */\n  RECITATION = 'RECITATION',\n  /**\n   * Unknown reason.\n   */\n  OTHER = 'OTHER',\n  /**\n   * The candidate content contained forbidden terms.\n   */\n  BLOCKLIST = 'BLOCKLIST',\n  /**\n   * The candidate content potentially contained prohibited content.\n   */\n  PROHIBITED_CONTENT = 'PROHIBITED_CONTENT',\n  /**\n   * The candidate content potentially contained Sensitive Personally Identifiable Information (SPII).\n   */\n  SPII = 'SPII',\n  /**\n   * The function call generated by the model was invalid.\n   */\n  MALFORMED_FUNCTION_CALL = 'MALFORMED_FUNCTION_CALL'\n}\n\n/**\n * @public\n */\nexport enum FunctionCallingMode {\n  /**\n   * Default model behavior; model decides to predict either a function call\n   * or a natural language response.\n   */\n  AUTO = 'AUTO',\n  /**\n   * Model is constrained to always predicting a function call only.\n   * If `allowed_function_names` is set, the predicted function call will be\n   * limited to any one of `allowed_function_names`, else the predicted\n   * function call will be any one of the provided `function_declarations`.\n   */\n  ANY = 'ANY',\n  /**\n   * Model will not predict any function call. Model behavior is same as when\n   * not passing any function declarations.\n   */\n  NONE = 'NONE'\n}\n\n/**\n * Content part modality.\n * @public\n */\nexport enum Modality {\n  /**\n   * Unspecified modality.\n   */\n  MODALITY_UNSPECIFIED = 'MODALITY_UNSPECIFIED',\n  /**\n   * Plain text.\n   */\n  TEXT = 'TEXT',\n  /**\n   * Image.\n   */\n  IMAGE = 'IMAGE',\n  /**\n   * Video.\n   */\n  VIDEO = 'VIDEO',\n  /**\n   * Audio.\n   */\n  AUDIO = 'AUDIO',\n  /**\n   * Document (for example, PDF).\n   */\n  DOCUMENT = 'DOCUMENT'\n}\n\n/**\n * Generation modalities to be returned in generation responses.\n *\n * @beta\n */\nexport const ResponseModality = {\n  /**\n   * Text.\n   * @beta\n   */\n  TEXT: 'TEXT',\n  /**\n   * Image.\n   * @beta\n   */\n  IMAGE: 'IMAGE'\n} as const;\n\n/**\n * Generation modalities to be returned in generation responses.\n *\n * @beta\n */\nexport type ResponseModality =\n  (typeof ResponseModality)[keyof typeof ResponseModality];\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Contains the list of OpenAPI data types\n * as defined by the\n * {@link https://swagger.io/docs/specification/data-models/data-types/ | OpenAPI specification}\n * @public\n */\nexport enum SchemaType {\n  /** String type. */\n  STRING = 'string',\n  /** Number type. */\n  NUMBER = 'number',\n  /** Integer type. */\n  INTEGER = 'integer',\n  /** Boolean type. */\n  BOOLEAN = 'boolean',\n  /** Array type. */\n  ARRAY = 'array',\n  /** Object type. */\n  OBJECT = 'object'\n}\n\n/**\n * Basic {@link Schema} properties shared across several Schema-related\n * types.\n * @public\n */\nexport interface SchemaShared<T> {\n  /** Optional. The format of the property.\n   * When using the Gemini Developer API ({@link GoogleAIBackend}), this must be either `'enum'` or\n   * `'date-time'`, otherwise requests will fail.\n   */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /**\n   * The title of the property. This helps document the schema's purpose but does not typically\n   * constrain the generated value. It can subtly guide the model by clarifying the intent of a\n   * field.\n   */\n  title?: string;\n  /** Optional. The items of the property. */\n  items?: T;\n  /** The minimum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  minItems?: number;\n  /** The maximum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  maxItems?: number;\n  /** Optional. Map of `Schema` objects. */\n  properties?: {\n    [k: string]: T;\n  };\n  /** A hint suggesting the order in which the keys should appear in the generated JSON string. */\n  propertyOrdering?: string[];\n  /** Optional. The enum of the property. */\n  enum?: string[];\n  /** Optional. The example of the property. */\n  example?: unknown;\n  /** Optional. Whether the property is nullable. */\n  nullable?: boolean;\n  /** The minimum value of a numeric type. */\n  minimum?: number;\n  /** The maximum value of a numeric type. */\n  maximum?: number;\n  [key: string]: unknown;\n}\n\n/**\n * Params passed to {@link Schema} static methods to create specific\n * {@link Schema} classes.\n * @public\n */\nexport interface SchemaParams extends SchemaShared<SchemaInterface> {}\n\n/**\n * Final format for {@link Schema} params passed to backend requests.\n * @public\n */\nexport interface SchemaRequest extends SchemaShared<SchemaRequest> {\n  /**\n   * The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n  /** Optional. Array of required property. */\n  required?: string[];\n}\n\n/**\n * Interface for {@link Schema} class.\n * @public\n */\nexport interface SchemaInterface extends SchemaShared<SchemaInterface> {\n  /**\n   * The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n}\n\n/**\n * Interface for {@link ObjectSchema} class.\n * @public\n */\nexport interface ObjectSchemaInterface extends SchemaInterface {\n  type: SchemaType.OBJECT;\n  optionalProperties?: string[];\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ImagenImageFormat } from '../../requests/imagen-image-format';\n\n/**\n * Parameters for configuring an {@link ImagenModel}.\n *\n * @beta\n */\nexport interface ImagenModelParams {\n  /**\n   * The Imagen model to use for generating images.\n   * For example: `imagen-3.0-generate-002`.\n   *\n   * Only Imagen 3 models (named `imagen-3.0-*`) are supported.\n   *\n   * See {@link https://firebase.google.com/docs/vertex-ai/models | model versions}\n   * for a full list of supported Imagen 3 models.\n   */\n  model: string;\n  /**\n   * Configuration options for generating images with Imagen.\n   */\n  generationConfig?: ImagenGenerationConfig;\n  /**\n   * Safety settings for filtering potentially inappropriate content.\n   */\n  safetySettings?: ImagenSafetySettings;\n}\n\n/**\n * Configuration options for generating images with Imagen.\n *\n * See the {@link http://firebase.google.com/docs/vertex-ai/generate-images-imagen | documentation} for\n * more details.\n *\n * @beta\n */\nexport interface ImagenGenerationConfig {\n  /**\n   * A description of what should be omitted from the generated images.\n   *\n   * Support for negative prompts depends on the Imagen model.\n   *\n   * See the {@link http://firebase.google.com/docs/vertex-ai/model-parameters#imagen | documentation} for more details.\n   *\n   * This is no longer supported in the Gemini Developer API ({@link GoogleAIBackend}) in versions\n   * greater than `imagen-3.0-generate-002`.\n   */\n  negativePrompt?: string;\n  /**\n   * The number of images to generate. The default value is 1.\n   *\n   * The number of sample images that may be generated in each request depends on the model\n   * (typically up to 4); see the <a href=\"http://firebase.google.com/docs/vertex-ai/model-parameters#imagen\">sampleCount</a>\n   * documentation for more details.\n   */\n  numberOfImages?: number;\n  /**\n   * The aspect ratio of the generated images. The default value is square 1:1.\n   * Supported aspect ratios depend on the Imagen model, see {@link ImagenAspectRatio}\n   * for more details.\n   */\n  aspectRatio?: ImagenAspectRatio;\n  /**\n   * The image format of the generated images. The default is PNG.\n   *\n   * See {@link ImagenImageFormat} for more details.\n   */\n  imageFormat?: ImagenImageFormat;\n  /**\n   * Whether to add an invisible watermark to generated images.\n   *\n   * If set to `true`, an invisible SynthID watermark is embedded in generated images to indicate\n   * that they are AI generated. If set to `false`, watermarking will be disabled.\n   *\n   * For Imagen 3 models, the default value is `true`; see the <a href=\"http://firebase.google.com/docs/vertex-ai/model-parameters#imagen\">addWatermark</a>\n   * documentation for more details.\n   *\n   * When using the Gemini Developer API ({@link GoogleAIBackend}), this will default to true,\n   * and cannot be turned off.\n   */\n  addWatermark?: boolean;\n}\n\n/**\n * A filter level controlling how aggressively to filter sensitive content.\n *\n * Text prompts provided as inputs and images (generated or uploaded) through Imagen on Vertex AI\n * are assessed against a list of safety filters, which include 'harmful categories' (for example,\n * `violence`, `sexual`, `derogatory`, and `toxic`). This filter level controls how aggressively to\n * filter out potentially harmful content from responses. See the {@link http://firebase.google.com/docs/vertex-ai/generate-images | documentation }\n * and the {@link https://cloud.google.com/vertex-ai/generative-ai/docs/image/responsible-ai-imagen#safety-filters | Responsible AI and usage guidelines}\n * for more details.\n *\n * @beta\n */\nexport enum ImagenSafetyFilterLevel {\n  /**\n   * The most aggressive filtering level; most strict blocking.\n   */\n  BLOCK_LOW_AND_ABOVE = 'block_low_and_above',\n  /**\n   * Blocks some sensitive prompts and responses.\n   */\n  BLOCK_MEDIUM_AND_ABOVE = 'block_medium_and_above',\n  /**\n   * Blocks few sensitive prompts and responses.\n   */\n  BLOCK_ONLY_HIGH = 'block_only_high',\n  /**\n   * The least aggressive filtering level; blocks very few sensitive prompts and responses.\n   *\n   * Access to this feature is restricted and may require your case to be reviewed and approved by\n   * Cloud support.\n   */\n  BLOCK_NONE = 'block_none'\n}\n\n/**\n * A filter level controlling whether generation of images containing people or faces is allowed.\n *\n * See the <a href=\"http://firebase.google.com/docs/vertex-ai/generate-images\">personGeneration</a>\n * documentation for more details.\n *\n * @beta\n */\nexport enum ImagenPersonFilterLevel {\n  /**\n   * Disallow generation of images containing people or faces; images of people are filtered out.\n   */\n  BLOCK_ALL = 'dont_allow',\n  /**\n   * Allow generation of images containing adults only; images of children are filtered out.\n   *\n   * Generation of images containing people or faces may require your use case to be\n   * reviewed and approved by Cloud support; see the {@link https://cloud.google.com/vertex-ai/generative-ai/docs/image/responsible-ai-imagen#person-face-gen | Responsible AI and usage guidelines}\n   * for more details.\n   */\n  ALLOW_ADULT = 'allow_adult',\n  /**\n   * Allow generation of images containing adults only; images of children are filtered out.\n   *\n   * Generation of images containing people or faces may require your use case to be\n   * reviewed and approved by Cloud support; see the {@link https://cloud.google.com/vertex-ai/generative-ai/docs/image/responsible-ai-imagen#person-face-gen | Responsible AI and usage guidelines}\n   * for more details.\n   */\n  ALLOW_ALL = 'allow_all'\n}\n\n/**\n * Settings for controlling the aggressiveness of filtering out sensitive content.\n *\n * See the {@link http://firebase.google.com/docs/vertex-ai/generate-images | documentation }\n * for more details.\n *\n * @beta\n */\nexport interface ImagenSafetySettings {\n  /**\n   * A filter level controlling how aggressive to filter out sensitive content from generated\n   * images.\n   */\n  safetyFilterLevel?: ImagenSafetyFilterLevel;\n  /**\n   * A filter level controlling whether generation of images containing people or faces is allowed.\n   */\n  personFilterLevel?: ImagenPersonFilterLevel;\n}\n\n/**\n * Aspect ratios for Imagen images.\n *\n * To specify an aspect ratio for generated images, set the `aspectRatio` property in your\n * {@link ImagenGenerationConfig}.\n *\n * See the the {@link http://firebase.google.com/docs/vertex-ai/generate-images | documentation }\n * for more details and examples of the supported aspect ratios.\n *\n * @beta\n */\nexport enum ImagenAspectRatio {\n  /**\n   * Square (1:1) aspect ratio.\n   */\n  SQUARE = '1:1',\n  /**\n   * Landscape (3:4) aspect ratio.\n   */\n  LANDSCAPE_3x4 = '3:4',\n  /**\n   * Portrait (4:3) aspect ratio.\n   */\n  PORTRAIT_4x3 = '4:3',\n  /**\n   * Landscape (16:9) aspect ratio.\n   */\n  LANDSCAPE_16x9 = '16:9',\n  /**\n   * Portrait (9:16) aspect ratio.\n   */\n  PORTRAIT_9x16 = '9:16'\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { Backend } from './backend';\n\nexport * from './types';\n\n/**\n * @deprecated Use the new {@link AI | AI} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * An instance of the Firebase AI SDK.\n *\n * @public\n */\nexport type VertexAI = AI;\n\n/**\n * Options when initializing the Firebase AI SDK.\n *\n * @public\n */\nexport interface VertexAIOptions {\n  location?: string;\n}\n\n/**\n * An instance of the Firebase AI SDK.\n *\n * Do not create this instance directly. Instead, use {@link getAI | getAI()}.\n *\n * @public\n */\nexport interface AI {\n  /**\n   * The {@link @firebase/app#FirebaseApp} this {@link AI} instance is associated with.\n   */\n  app: FirebaseApp;\n  /**\n   * A {@link Backend} instance that specifies the configuration for the target backend,\n   * either the Gemini Developer API (using {@link GoogleAIBackend}) or the\n   * Vertex AI Gemini API (using {@link VertexAIBackend}).\n   */\n  backend: Backend;\n  /**\n   * @deprecated use `AI.backend.location` instead.\n   *\n   * The location configured for this AI service instance, relevant for Vertex AI backends.\n   */\n  location: string;\n}\n\n/**\n * An enum-like object containing constants that represent the supported backends\n * for the Firebase AI SDK.\n * This determines which backend service (Vertex AI Gemini API or Gemini Developer API)\n * the SDK will communicate with.\n *\n * These values are assigned to the `backendType` property within the specific backend\n * configuration objects ({@link GoogleAIBackend} or {@link VertexAIBackend}) to identify\n * which service to target.\n *\n * @public\n */\nexport const BackendType = {\n  /**\n   * Identifies the backend service for the Vertex AI Gemini API provided through Google Cloud.\n   * Use this constant when creating a {@link VertexAIBackend} configuration.\n   */\n  VERTEX_AI: 'VERTEX_AI',\n\n  /**\n   * Identifies the backend service for the Gemini Developer API ({@link https://ai.google/ | Google AI}).\n   * Use this constant when creating a {@link GoogleAIBackend} configuration.\n   */\n  GOOGLE_AI: 'GOOGLE_AI'\n} as const; // Using 'as const' makes the string values literal types\n\n/**\n * Type alias representing valid backend types.\n * It can be either `'VERTEX_AI'` or `'GOOGLE_AI'`.\n *\n * @public\n */\nexport type BackendType = (typeof BackendType)[keyof typeof BackendType];\n\n/**\n * Options for initializing the AI service using {@link getAI | getAI()}.\n * This allows specifying which backend to use (Vertex AI Gemini API or Gemini Developer API)\n * and configuring its specific options (like location for Vertex AI).\n *\n * @public\n */\nexport interface AIOptions {\n  /**\n   * The backend configuration to use for the AI service instance.\n   */\n  backend: Backend;\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_LOCATION } from './constants';\nimport { BackendType } from './public-types';\n\n/**\n * Abstract base class representing the configuration for an AI service backend.\n * This class should not be instantiated directly. Use its subclasses; {@link GoogleAIBackend} for\n * the Gemini Developer API (via {@link https://ai.google/ | Google AI}), and\n * {@link VertexAIBackend} for the Vertex AI Gemini API.\n *\n * @public\n */\nexport abstract class Backend {\n  /**\n   * Specifies the backend type.\n   */\n  readonly backendType: BackendType;\n\n  /**\n   * Protected constructor for use by subclasses.\n   * @param type - The backend type.\n   */\n  protected constructor(type: BackendType) {\n    this.backendType = type;\n  }\n}\n\n/**\n * Configuration class for the Gemini Developer API.\n *\n * Use this with {@link AIOptions} when initializing the AI service via\n * {@link getAI | getAI()} to specify the Gemini Developer API as the backend.\n *\n * @public\n */\nexport class GoogleAIBackend extends Backend {\n  /**\n   * Creates a configuration object for the Gemini Developer API backend.\n   */\n  constructor() {\n    super(BackendType.GOOGLE_AI);\n  }\n}\n\n/**\n * Configuration class for the Vertex AI Gemini API.\n *\n * Use this with {@link AIOptions} when initializing the AI service via\n * {@link getAI | getAI()} to specify the Vertex AI Gemini API as the backend.\n *\n * @public\n */\nexport class VertexAIBackend extends Backend {\n  /**\n   * The region identifier.\n   * See {@link https://firebase.google.com/docs/vertex-ai/locations#available-locations | Vertex AI locations}\n   * for a list of supported locations.\n   */\n  readonly location: string;\n\n  /**\n   * Creates a configuration object for the Vertex AI backend.\n   *\n   * @param location - The region identifier, defaulting to `us-central1`;\n   * see {@link https://firebase.google.com/docs/vertex-ai/locations#available-locations | Vertex AI locations}\n   * for a list of supported locations.\n   */\n  constructor(location: string = DEFAULT_LOCATION) {\n    super(BackendType.VERTEX_AI);\n    if (!location) {\n      this.location = DEFAULT_LOCATION;\n    } else {\n      this.location = location;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { AI } from './public-types';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\nimport { Backend, VertexAIBackend } from './backend';\n\nexport class AIService implements AI, _FirebaseService {\n  auth: FirebaseAuthInternal | null;\n  appCheck: FirebaseAppCheckInternal | null;\n  location: string; // This is here for backwards-compatibility\n\n  constructor(\n    public app: FirebaseApp,\n    public backend: Backend,\n    authProvider?: Provider<FirebaseAuthInternalName>,\n    appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    const appCheck = appCheckProvider?.getImmediate({ optional: true });\n    const auth = authProvider?.getImmediate({ optional: true });\n    this.auth = auth || null;\n    this.appCheck = appCheck || null;\n\n    if (backend instanceof VertexAIBackend) {\n      this.location = backend.location;\n    } else {\n      this.location = '';\n    }\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { AIErrorCode, CustomErrorData } from './types';\nimport { AI_TYPE } from './constants';\n\n/**\n * Error class for the Firebase AI SDK.\n *\n * @public\n */\nexport class AIError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `AIError` class.\n   *\n   * @param code - The error code from {@link AIErrorCode}.\n   * @param message - A human-readable message describing the error.\n   * @param customErrorData - Optional error data.\n   */\n  constructor(\n    readonly code: <PERSON><PERSON><PERSON>rC<PERSON>,\n    message: string,\n    readonly customErrorData?: CustomErrorData\n  ) {\n    // Match error format used by FirebaseError from ErrorFactory\n    const service = AI_TYPE;\n    const fullCode = `${service}/${code}`;\n    const fullMessage = `${service}: ${message} (${fullCode})`;\n    super(code, fullMessage);\n\n    // FirebaseError initializes a stack trace, but it assumes the error is created from the error\n    // factory. Since we break this assumption, we set the stack trace to be originating from this\n    // constructor.\n    // This is only supported in V8.\n    if (Error.captureStackTrace) {\n      // Allows us to initialize the stack trace without including the constructor itself at the\n      // top level of the stack trace.\n      Error.captureStackTrace(this, AIError);\n    }\n\n    // Allows instanceof AIError in ES5/ES6\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, AIError.prototype);\n\n    // Since Error is an interface, we don't inherit toString and so we define it ourselves.\n    this.toString = () => fullMessage;\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AI_TYPE } from './constants';\nimport { AIError } from './errors';\nimport { AIErrorCode } from './types';\nimport { Backend, GoogleAIBackend, VertexAIBackend } from './backend';\n\n/**\n * Encodes a {@link Backend} into a string that will be used to uniquely identify {@link AI}\n * instances by backend type.\n *\n * @internal\n */\nexport function encodeInstanceIdentifier(backend: Backend): string {\n  if (backend instanceof GoogleAIBackend) {\n    return `${AI_TYPE}/googleai`;\n  } else if (backend instanceof VertexAIBackend) {\n    return `${AI_TYPE}/vertexai/${backend.location}`;\n  } else {\n    throw new AIError(\n      AIErrorCode.ERROR,\n      `Invalid backend: ${JSON.stringify(backend.backendType)}`\n    );\n  }\n}\n\n/**\n * Decodes an instance identifier string into a {@link Backend}.\n *\n * @internal\n */\nexport function decodeInstanceIdentifier(instanceIdentifier: string): Backend {\n  const identifierParts = instanceIdentifier.split('/');\n  if (identifierParts[0] !== AI_TYPE) {\n    throw new AIError(\n      AIErrorCode.ERROR,\n      `Invalid instance identifier, unknown prefix '${identifierParts[0]}'`\n    );\n  }\n  const backendType = identifierParts[1];\n  switch (backendType) {\n    case 'vertexai':\n      const location: string | undefined = identifierParts[2];\n      if (!location) {\n        throw new AIError(\n          AIErrorCode.ERROR,\n          `Invalid instance identifier, unknown location '${instanceIdentifier}'`\n        );\n      }\n      return new VertexAIBackend(location);\n    case 'googleai':\n      return new GoogleAIBackend();\n    default:\n      throw new AIError(\n        AIErrorCode.ERROR,\n        `Invalid instance identifier string: '${instanceIdentifier}'`\n      );\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AIError } from '../errors';\nimport { AIErrorCode, AI, BackendType } from '../public-types';\nimport { AIService } from '../service';\nimport { ApiSettings } from '../types/internal';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/**\n * Base class for Firebase AI model APIs.\n *\n * Instances of this class are associated with a specific Firebase AI {@link Backend}\n * and provide methods for interacting with the configured generative model.\n *\n * @public\n */\nexport abstract class AIModel {\n  /**\n   * The fully qualified model resource name to use for generating images\n   * (for example, `publishers/google/models/imagen-3.0-generate-002`).\n   */\n  readonly model: string;\n\n  /**\n   * @internal\n   */\n  protected _apiSettings: ApiSettings;\n\n  /**\n   * Constructs a new instance of the {@link AIModel} class.\n   *\n   * This constructor should only be called from subclasses that provide\n   * a model API.\n   *\n   * @param ai - an {@link AI} instance.\n   * @param modelName - The name of the model being used. It can be in one of the following formats:\n   * - `my-model` (short name, will resolve to `publishers/google/models/my-model`)\n   * - `models/my-model` (will resolve to `publishers/google/models/my-model`)\n   * - `publishers/my-publisher/models/my-model` (fully qualified model name)\n   *\n   * @throws If the `apiKey` or `projectId` fields are missing in your\n   * Firebase config.\n   *\n   * @internal\n   */\n  protected constructor(ai: AI, modelName: string) {\n    if (!ai.app?.options?.apiKey) {\n      throw new AIError(\n        AIErrorCode.NO_API_KEY,\n        `The \"apiKey\" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid API key.`\n      );\n    } else if (!ai.app?.options?.projectId) {\n      throw new AIError(\n        AIErrorCode.NO_PROJECT_ID,\n        `The \"projectId\" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid project ID.`\n      );\n    } else if (!ai.app?.options?.appId) {\n      throw new AIError(\n        AIErrorCode.NO_APP_ID,\n        `The \"appId\" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid app ID.`\n      );\n    } else {\n      this._apiSettings = {\n        apiKey: ai.app.options.apiKey,\n        project: ai.app.options.projectId,\n        appId: ai.app.options.appId,\n        automaticDataCollectionEnabled: ai.app.automaticDataCollectionEnabled,\n        location: ai.location,\n        backend: ai.backend\n      };\n\n      if (_isFirebaseServerApp(ai.app) && ai.app.settings.appCheckToken) {\n        const token = ai.app.settings.appCheckToken;\n        this._apiSettings.getAppCheckToken = () => {\n          return Promise.resolve({ token });\n        };\n      } else if ((ai as AIService).appCheck) {\n        this._apiSettings.getAppCheckToken = () =>\n          (ai as AIService).appCheck!.getToken();\n      }\n\n      if ((ai as AIService).auth) {\n        this._apiSettings.getAuthToken = () =>\n          (ai as AIService).auth!.getToken();\n      }\n\n      this.model = AIModel.normalizeModelName(\n        modelName,\n        this._apiSettings.backend.backendType\n      );\n    }\n  }\n\n  /**\n   * Normalizes the given model name to a fully qualified model resource name.\n   *\n   * @param modelName - The model name to normalize.\n   * @returns The fully qualified model resource name.\n   *\n   * @internal\n   */\n  static normalizeModelName(\n    modelName: string,\n    backendType: BackendType\n  ): string {\n    if (backendType === BackendType.GOOGLE_AI) {\n      return AIModel.normalizeGoogleAIModelName(modelName);\n    } else {\n      return AIModel.normalizeVertexAIModelName(modelName);\n    }\n  }\n\n  /**\n   * @internal\n   */\n  private static normalizeGoogleAIModelName(modelName: string): string {\n    return `models/${modelName}`;\n  }\n\n  /**\n   * @internal\n   */\n  private static normalizeVertexAIModelName(modelName: string): string {\n    let model: string;\n    if (modelName.includes('/')) {\n      if (modelName.startsWith('models/')) {\n        // Add 'publishers/google' if the user is only passing in 'models/model-name'.\n        model = `publishers/google/${modelName}`;\n      } else {\n        // Any other custom format (e.g. tuned models) must be passed in correctly.\n        model = modelName;\n      }\n    } else {\n      // If path is not included, assume it's a non-tuned model.\n      model = `publishers/google/models/${modelName}`;\n    }\n\n    return model;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/vertexai');\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorDetails, RequestOptions, AIErrorCode } from '../types';\nimport { AIError } from '../errors';\nimport { ApiSettings } from '../types/internal';\nimport {\n  DEFAULT_API_VERSION,\n  DEFAULT_BASE_URL,\n  DEFAULT_FETCH_TIMEOUT_MS,\n  LANGUAGE_TAG,\n  PACKAGE_VERSION\n} from '../constants';\nimport { logger } from '../logger';\nimport { GoogleAIBackend, VertexAIBackend } from '../backend';\n\nexport enum Task {\n  GENERATE_CONTENT = 'generateContent',\n  STREAM_GENERATE_CONTENT = 'streamGenerateContent',\n  COUNT_TOKENS = 'countTokens',\n  PREDICT = 'predict'\n}\n\nexport class RequestUrl {\n  constructor(\n    public model: string,\n    public task: Task,\n    public apiSettings: ApiSettings,\n    public stream: boolean,\n    public requestOptions?: RequestOptions\n  ) {}\n  toString(): string {\n    const url = new URL(this.baseUrl); // Throws if the URL is invalid\n    url.pathname = `/${this.apiVersion}/${this.modelPath}:${this.task}`;\n    url.search = this.queryParams.toString();\n    return url.toString();\n  }\n\n  private get baseUrl(): string {\n    return this.requestOptions?.baseUrl || DEFAULT_BASE_URL;\n  }\n\n  private get apiVersion(): string {\n    return DEFAULT_API_VERSION; // TODO: allow user-set options if that feature becomes available\n  }\n\n  private get modelPath(): string {\n    if (this.apiSettings.backend instanceof GoogleAIBackend) {\n      return `projects/${this.apiSettings.project}/${this.model}`;\n    } else if (this.apiSettings.backend instanceof VertexAIBackend) {\n      return `projects/${this.apiSettings.project}/locations/${this.apiSettings.backend.location}/${this.model}`;\n    } else {\n      throw new AIError(\n        AIErrorCode.ERROR,\n        `Invalid backend: ${JSON.stringify(this.apiSettings.backend)}`\n      );\n    }\n  }\n\n  private get queryParams(): URLSearchParams {\n    const params = new URLSearchParams();\n    if (this.stream) {\n      params.set('alt', 'sse');\n    }\n\n    return params;\n  }\n}\n\n/**\n * Log language and \"fire/version\" to x-goog-api-client\n */\nfunction getClientHeaders(): string {\n  const loggingTags = [];\n  loggingTags.push(`${LANGUAGE_TAG}/${PACKAGE_VERSION}`);\n  loggingTags.push(`fire/${PACKAGE_VERSION}`);\n  return loggingTags.join(' ');\n}\n\nexport async function getHeaders(url: RequestUrl): Promise<Headers> {\n  const headers = new Headers();\n  headers.append('Content-Type', 'application/json');\n  headers.append('x-goog-api-client', getClientHeaders());\n  headers.append('x-goog-api-key', url.apiSettings.apiKey);\n  if (url.apiSettings.automaticDataCollectionEnabled) {\n    headers.append('X-Firebase-Appid', url.apiSettings.appId);\n  }\n  if (url.apiSettings.getAppCheckToken) {\n    const appCheckToken = await url.apiSettings.getAppCheckToken();\n    if (appCheckToken) {\n      headers.append('X-Firebase-AppCheck', appCheckToken.token);\n      if (appCheckToken.error) {\n        logger.warn(\n          `Unable to obtain a valid App Check token: ${appCheckToken.error.message}`\n        );\n      }\n    }\n  }\n\n  if (url.apiSettings.getAuthToken) {\n    const authToken = await url.apiSettings.getAuthToken();\n    if (authToken) {\n      headers.append('Authorization', `Firebase ${authToken.accessToken}`);\n    }\n  }\n\n  return headers;\n}\n\nexport async function constructRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<{ url: string; fetchOptions: RequestInit }> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  return {\n    url: url.toString(),\n    fetchOptions: {\n      method: 'POST',\n      headers: await getHeaders(url),\n      body\n    }\n  };\n}\n\nexport async function makeRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<Response> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  let response;\n  let fetchTimeoutId: string | number | NodeJS.Timeout | undefined;\n  try {\n    const request = await constructRequest(\n      model,\n      task,\n      apiSettings,\n      stream,\n      body,\n      requestOptions\n    );\n    // Timeout is 180s by default\n    const timeoutMillis =\n      requestOptions?.timeout != null && requestOptions.timeout >= 0\n        ? requestOptions.timeout\n        : DEFAULT_FETCH_TIMEOUT_MS;\n    const abortController = new AbortController();\n    fetchTimeoutId = setTimeout(() => abortController.abort(), timeoutMillis);\n    request.fetchOptions.signal = abortController.signal;\n\n    response = await fetch(request.url, request.fetchOptions);\n    if (!response.ok) {\n      let message = '';\n      let errorDetails;\n      try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n          message += ` ${JSON.stringify(json.error.details)}`;\n          errorDetails = json.error.details;\n        }\n      } catch (e) {\n        // ignored\n      }\n      if (\n        response.status === 403 &&\n        errorDetails.some(\n          (detail: ErrorDetails) => detail.reason === 'SERVICE_DISABLED'\n        ) &&\n        errorDetails.some((detail: ErrorDetails) =>\n          (\n            detail.links as Array<Record<string, string>>\n          )?.[0]?.description.includes(\n            'Google developers console API activation'\n          )\n        )\n      ) {\n        throw new AIError(\n          AIErrorCode.API_NOT_ENABLED,\n          `The Firebase AI SDK requires the Firebase AI ` +\n            `API ('firebasevertexai.googleapis.com') to be enabled in your ` +\n            `Firebase project. Enable this API by visiting the Firebase Console ` +\n            `at https://console.firebase.google.com/project/${url.apiSettings.project}/genai/ ` +\n            `and clicking \"Get started\". If you enabled this API recently, ` +\n            `wait a few minutes for the action to propagate to our systems and ` +\n            `then retry.`,\n          {\n            status: response.status,\n            statusText: response.statusText,\n            errorDetails\n          }\n        );\n      }\n      throw new AIError(\n        AIErrorCode.FETCH_ERROR,\n        `Error fetching from ${url}: [${response.status} ${response.statusText}] ${message}`,\n        {\n          status: response.status,\n          statusText: response.statusText,\n          errorDetails\n        }\n      );\n    }\n  } catch (e) {\n    let err = e as Error;\n    if (\n      (e as AIError).code !== AIErrorCode.FETCH_ERROR &&\n      (e as AIError).code !== AIErrorCode.API_NOT_ENABLED &&\n      e instanceof Error\n    ) {\n      err = new AIError(\n        AIErrorCode.ERROR,\n        `Error fetching from ${url.toString()}: ${e.message}`\n      );\n      err.stack = e.stack;\n    }\n\n    throw err;\n  } finally {\n    if (fetchTimeoutId) {\n      clearTimeout(fetchTimeoutId);\n    }\n  }\n  return response;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  FinishReason,\n  FunctionCall,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  ImagenGCSImage,\n  ImagenInlineImage,\n  AIErrorCode,\n  InlineDataPart\n} from '../types';\nimport { AIError } from '../errors';\nimport { logger } from '../logger';\nimport { ImagenResponseInternal } from '../types/internal';\n\n/**\n * Creates an EnhancedGenerateContentResponse object that has helper functions and\n * other modifications that improve usability.\n */\nexport function createEnhancedContentResponse(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  /**\n   * The Vertex AI backend omits default values.\n   * This causes the `index` property to be omitted from the first candidate in the\n   * response, since it has index 0, and 0 is a default value.\n   * See: https://github.com/firebase/firebase-js-sdk/issues/8566\n   */\n  if (response.candidates && !response.candidates[0].hasOwnProperty('index')) {\n    response.candidates[0].index = 0;\n  }\n\n  const responseWithHelpers = addHelpers(response);\n  return responseWithHelpers;\n}\n\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nexport function addHelpers(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  (response as EnhancedGenerateContentResponse).text = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning text from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new AIError(\n          AIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getText(response);\n    } else if (response.promptFeedback) {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Text not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return '';\n  };\n  (response as EnhancedGenerateContentResponse).inlineDataParts = ():\n    | InlineDataPart[]\n    | undefined => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning data from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new AIError(\n          AIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getInlineDataParts(response);\n    } else if (response.promptFeedback) {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Data not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return undefined;\n  };\n  (response as EnhancedGenerateContentResponse).functionCalls = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning function calls from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new AIError(\n          AIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getFunctionCalls(response);\n    } else if (response.promptFeedback) {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Function call not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return undefined;\n  };\n  return response as EnhancedGenerateContentResponse;\n}\n\n/**\n * Returns all text found in all parts of first candidate.\n */\nexport function getText(response: GenerateContentResponse): string {\n  const textStrings = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.text) {\n        textStrings.push(part.text);\n      }\n    }\n  }\n  if (textStrings.length > 0) {\n    return textStrings.join('');\n  } else {\n    return '';\n  }\n}\n\n/**\n * Returns {@link FunctionCall}s associated with first candidate.\n */\nexport function getFunctionCalls(\n  response: GenerateContentResponse\n): FunctionCall[] | undefined {\n  const functionCalls: FunctionCall[] = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.functionCall) {\n        functionCalls.push(part.functionCall);\n      }\n    }\n  }\n  if (functionCalls.length > 0) {\n    return functionCalls;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Returns {@link InlineDataPart}s in the first candidate if present.\n *\n * @internal\n */\nexport function getInlineDataParts(\n  response: GenerateContentResponse\n): InlineDataPart[] | undefined {\n  const data: InlineDataPart[] = [];\n\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.inlineData) {\n        data.push(part);\n      }\n    }\n  }\n\n  if (data.length > 0) {\n    return data;\n  } else {\n    return undefined;\n  }\n}\n\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY];\n\nfunction hadBadFinishReason(candidate: GenerateContentCandidate): boolean {\n  return (\n    !!candidate.finishReason &&\n    badFinishReasons.includes(candidate.finishReason)\n  );\n}\n\nexport function formatBlockErrorMessage(\n  response: GenerateContentResponse\n): string {\n  let message = '';\n  if (\n    (!response.candidates || response.candidates.length === 0) &&\n    response.promptFeedback\n  ) {\n    message += 'Response was blocked';\n    if (response.promptFeedback?.blockReason) {\n      message += ` due to ${response.promptFeedback.blockReason}`;\n    }\n    if (response.promptFeedback?.blockReasonMessage) {\n      message += `: ${response.promptFeedback.blockReasonMessage}`;\n    }\n  } else if (response.candidates?.[0]) {\n    const firstCandidate = response.candidates[0];\n    if (hadBadFinishReason(firstCandidate)) {\n      message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n      if (firstCandidate.finishMessage) {\n        message += `: ${firstCandidate.finishMessage}`;\n      }\n    }\n  }\n  return message;\n}\n\n/**\n * Convert a generic successful fetch response body to an Imagen response object\n * that can be returned to the user. This converts the REST APIs response format to our\n * APIs representation of a response.\n *\n * @internal\n */\nexport async function handlePredictResponse<\n  T extends ImagenInlineImage | ImagenGCSImage\n>(response: Response): Promise<{ images: T[]; filteredReason?: string }> {\n  const responseJson: ImagenResponseInternal = await response.json();\n\n  const images: T[] = [];\n  let filteredReason: string | undefined = undefined;\n\n  // The backend should always send a non-empty array of predictions if the response was successful.\n  if (!responseJson.predictions || responseJson.predictions?.length === 0) {\n    throw new AIError(\n      AIErrorCode.RESPONSE_ERROR,\n      'No predictions or filtered reason received from Vertex AI. Please report this issue with the full error details at https://github.com/firebase/firebase-js-sdk/issues.'\n    );\n  }\n\n  for (const prediction of responseJson.predictions) {\n    if (prediction.raiFilteredReason) {\n      filteredReason = prediction.raiFilteredReason;\n    } else if (prediction.mimeType && prediction.bytesBase64Encoded) {\n      images.push({\n        mimeType: prediction.mimeType,\n        bytesBase64Encoded: prediction.bytesBase64Encoded\n      } as T);\n    } else if (prediction.mimeType && prediction.gcsUri) {\n      images.push({\n        mimeType: prediction.mimeType,\n        gcsURI: prediction.gcsUri\n      } as T);\n    } else {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Predictions array in response has missing properties. Response: ${JSON.stringify(\n          responseJson\n        )}`\n      );\n    }\n  }\n\n  return { images, filteredReason };\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AIError } from './errors';\nimport { logger } from './logger';\nimport {\n  CitationMetadata,\n  CountTokensRequest,\n  GenerateContentCandidate,\n  GenerateContentRequest,\n  GenerateContentResponse,\n  HarmSeverity,\n  InlineDataPart,\n  PromptFeedback,\n  SafetyRating,\n  AIErrorCode\n} from './types';\nimport {\n  GoogleAIGenerateContentResponse,\n  GoogleAIGenerateContentCandidate,\n  GoogleAICountTokensRequest\n} from './types/googleai';\n\n/**\n * This SDK supports both the Vertex AI Gemini API and the Gemini Developer API (using Google AI).\n * The public API prioritizes the format used by the Vertex AI Gemini API.\n * We avoid having two sets of types by translating requests and responses between the two API formats.\n * This translation allows developers to switch between the Vertex AI Gemini API and the Gemini Developer API\n * with minimal code changes.\n *\n * In here are functions that map requests and responses between the two API formats.\n * Requests in the Vertex AI format are mapped to the Google AI format before being sent.\n * Responses from the Google AI backend are mapped back to the Vertex AI format before being returned to the user.\n */\n\n/**\n * Maps a Vertex AI {@link GenerateContentRequest} to a format that can be sent to Google AI.\n *\n * @param generateContentRequest The {@link GenerateContentRequest} to map.\n * @returns A {@link GenerateContentResponse} that conforms to the Google AI format.\n *\n * @throws If the request contains properties that are unsupported by Google AI.\n *\n * @internal\n */\nexport function mapGenerateContentRequest(\n  generateContentRequest: GenerateContentRequest\n): GenerateContentRequest {\n  generateContentRequest.safetySettings?.forEach(safetySetting => {\n    if (safetySetting.method) {\n      throw new AIError(\n        AIErrorCode.UNSUPPORTED,\n        'SafetySetting.method is not supported in the the Gemini Developer API. Please remove this property.'\n      );\n    }\n  });\n\n  if (generateContentRequest.generationConfig?.topK) {\n    const roundedTopK = Math.round(\n      generateContentRequest.generationConfig.topK\n    );\n\n    if (roundedTopK !== generateContentRequest.generationConfig.topK) {\n      logger.warn(\n        'topK in GenerationConfig has been rounded to the nearest integer to match the format for requests to the Gemini Developer API.'\n      );\n      generateContentRequest.generationConfig.topK = roundedTopK;\n    }\n  }\n\n  return generateContentRequest;\n}\n\n/**\n * Maps a {@link GenerateContentResponse} from Google AI to the format of the\n * {@link GenerateContentResponse} that we get from VertexAI that is exposed in the public API.\n *\n * @param googleAIResponse The {@link GenerateContentResponse} from Google AI.\n * @returns A {@link GenerateContentResponse} that conforms to the public API's format.\n *\n * @internal\n */\nexport function mapGenerateContentResponse(\n  googleAIResponse: GoogleAIGenerateContentResponse\n): GenerateContentResponse {\n  const generateContentResponse = {\n    candidates: googleAIResponse.candidates\n      ? mapGenerateContentCandidates(googleAIResponse.candidates)\n      : undefined,\n    prompt: googleAIResponse.promptFeedback\n      ? mapPromptFeedback(googleAIResponse.promptFeedback)\n      : undefined,\n    usageMetadata: googleAIResponse.usageMetadata\n  };\n\n  return generateContentResponse;\n}\n\n/**\n * Maps a Vertex AI {@link CountTokensRequest} to a format that can be sent to Google AI.\n *\n * @param countTokensRequest The {@link CountTokensRequest} to map.\n * @param model The model to count tokens with.\n * @returns A {@link CountTokensRequest} that conforms to the Google AI format.\n *\n * @internal\n */\nexport function mapCountTokensRequest(\n  countTokensRequest: CountTokensRequest,\n  model: string\n): GoogleAICountTokensRequest {\n  const mappedCountTokensRequest: GoogleAICountTokensRequest = {\n    generateContentRequest: {\n      model,\n      ...countTokensRequest\n    }\n  };\n\n  return mappedCountTokensRequest;\n}\n\n/**\n * Maps a Google AI {@link GoogleAIGenerateContentCandidate} to a format that conforms\n * to the Vertex AI API format.\n *\n * @param candidates The {@link GoogleAIGenerateContentCandidate} to map.\n * @returns A {@link GenerateContentCandidate} that conforms to the Vertex AI format.\n *\n * @throws If any {@link Part} in the candidates has a `videoMetadata` property.\n *\n * @internal\n */\nexport function mapGenerateContentCandidates(\n  candidates: GoogleAIGenerateContentCandidate[]\n): GenerateContentCandidate[] {\n  const mappedCandidates: GenerateContentCandidate[] = [];\n  let mappedSafetyRatings: SafetyRating[];\n  if (mappedCandidates) {\n    candidates.forEach(candidate => {\n      // Map citationSources to citations.\n      let citationMetadata: CitationMetadata | undefined;\n      if (candidate.citationMetadata) {\n        citationMetadata = {\n          citations: candidate.citationMetadata.citationSources\n        };\n      }\n\n      // Assign missing candidate SafetyRatings properties to their defaults if undefined.\n      if (candidate.safetyRatings) {\n        mappedSafetyRatings = candidate.safetyRatings.map(safetyRating => {\n          return {\n            ...safetyRating,\n            severity:\n              safetyRating.severity ?? HarmSeverity.HARM_SEVERITY_UNSUPPORTED,\n            probabilityScore: safetyRating.probabilityScore ?? 0,\n            severityScore: safetyRating.severityScore ?? 0\n          };\n        });\n      }\n\n      // videoMetadata is not supported.\n      // Throw early since developers may send a long video as input and only expect to pay\n      // for inference on a small portion of the video.\n      if (\n        candidate.content?.parts.some(\n          part => (part as InlineDataPart)?.videoMetadata\n        )\n      ) {\n        throw new AIError(\n          AIErrorCode.UNSUPPORTED,\n          'Part.videoMetadata is not supported in the Gemini Developer API. Please remove this property.'\n        );\n      }\n\n      const mappedCandidate = {\n        index: candidate.index,\n        content: candidate.content,\n        finishReason: candidate.finishReason,\n        finishMessage: candidate.finishMessage,\n        safetyRatings: mappedSafetyRatings,\n        citationMetadata,\n        groundingMetadata: candidate.groundingMetadata\n      };\n      mappedCandidates.push(mappedCandidate);\n    });\n  }\n\n  return mappedCandidates;\n}\n\nexport function mapPromptFeedback(\n  promptFeedback: PromptFeedback\n): PromptFeedback {\n  // Assign missing SafetyRating properties to their defaults if undefined.\n  const mappedSafetyRatings: SafetyRating[] = [];\n  promptFeedback.safetyRatings.forEach(safetyRating => {\n    mappedSafetyRatings.push({\n      category: safetyRating.category,\n      probability: safetyRating.probability,\n      severity: safetyRating.severity ?? HarmSeverity.HARM_SEVERITY_UNSUPPORTED,\n      probabilityScore: safetyRating.probabilityScore ?? 0,\n      severityScore: safetyRating.severityScore ?? 0,\n      blocked: safetyRating.blocked\n    });\n  });\n\n  const mappedPromptFeedback: PromptFeedback = {\n    blockReason: promptFeedback.blockReason,\n    safetyRatings: mappedSafetyRatings,\n    blockReasonMessage: promptFeedback.blockReasonMessage\n  };\n  return mappedPromptFeedback;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  GenerateContentStreamResult,\n  Part,\n  AIErrorCode\n} from '../types';\nimport { AIError } from '../errors';\nimport { createEnhancedContentResponse } from './response-helpers';\nimport * as GoogleAIMapper from '../googleai-mappers';\nimport { GoogleAIGenerateContentResponse } from '../types/googleai';\nimport { ApiSettings } from '../types/internal';\nimport { BackendType } from '../public-types';\n\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nexport function processStream(\n  response: Response,\n  apiSettings: ApiSettings\n): GenerateContentStreamResult {\n  const inputStream = response.body!.pipeThrough(\n    new TextDecoderStream('utf8', { fatal: true })\n  );\n  const responseStream =\n    getResponseStream<GenerateContentResponse>(inputStream);\n  const [stream1, stream2] = responseStream.tee();\n  return {\n    stream: generateResponseSequence(stream1, apiSettings),\n    response: getResponsePromise(stream2, apiSettings)\n  };\n}\n\nasync function getResponsePromise(\n  stream: ReadableStream<GenerateContentResponse>,\n  apiSettings: ApiSettings\n): Promise<EnhancedGenerateContentResponse> {\n  const allResponses: GenerateContentResponse[] = [];\n  const reader = stream.getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      let generateContentResponse = aggregateResponses(allResponses);\n      if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n        generateContentResponse = GoogleAIMapper.mapGenerateContentResponse(\n          generateContentResponse as GoogleAIGenerateContentResponse\n        );\n      }\n      return createEnhancedContentResponse(generateContentResponse);\n    }\n\n    allResponses.push(value);\n  }\n}\n\nasync function* generateResponseSequence(\n  stream: ReadableStream<GenerateContentResponse>,\n  apiSettings: ApiSettings\n): AsyncGenerator<EnhancedGenerateContentResponse> {\n  const reader = stream.getReader();\n  while (true) {\n    const { value, done } = await reader.read();\n    if (done) {\n      break;\n    }\n\n    let enhancedResponse: EnhancedGenerateContentResponse;\n    if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n      enhancedResponse = createEnhancedContentResponse(\n        GoogleAIMapper.mapGenerateContentResponse(\n          value as GoogleAIGenerateContentResponse\n        )\n      );\n    } else {\n      enhancedResponse = createEnhancedContentResponse(value);\n    }\n\n    yield enhancedResponse;\n  }\n}\n\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nexport function getResponseStream<T>(\n  inputStream: ReadableStream<string>\n): ReadableStream<T> {\n  const reader = inputStream.getReader();\n  const stream = new ReadableStream<T>({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      function pump(): Promise<(() => Promise<void>) | undefined> {\n        return reader.read().then(({ value, done }) => {\n          if (done) {\n            if (currentText.trim()) {\n              controller.error(\n                new AIError(AIErrorCode.PARSE_FAILED, 'Failed to parse stream')\n              );\n              return;\n            }\n            controller.close();\n            return;\n          }\n\n          currentText += value;\n          let match = currentText.match(responseLineRE);\n          let parsedResponse: T;\n          while (match) {\n            try {\n              parsedResponse = JSON.parse(match[1]);\n            } catch (e) {\n              controller.error(\n                new AIError(\n                  AIErrorCode.PARSE_FAILED,\n                  `Error parsing JSON response: \"${match[1]}`\n                )\n              );\n              return;\n            }\n            controller.enqueue(parsedResponse);\n            currentText = currentText.substring(match[0].length);\n            match = currentText.match(responseLineRE);\n          }\n          return pump();\n        });\n      }\n    }\n  });\n  return stream;\n}\n\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nexport function aggregateResponses(\n  responses: GenerateContentResponse[]\n): GenerateContentResponse {\n  const lastResponse = responses[responses.length - 1];\n  const aggregatedResponse: GenerateContentResponse = {\n    promptFeedback: lastResponse?.promptFeedback\n  };\n  for (const response of responses) {\n    if (response.candidates) {\n      for (const candidate of response.candidates) {\n        // Index will be undefined if it's the first index (0), so we should use 0 if it's undefined.\n        // See: https://github.com/firebase/firebase-js-sdk/issues/8566\n        const i = candidate.index || 0;\n        if (!aggregatedResponse.candidates) {\n          aggregatedResponse.candidates = [];\n        }\n        if (!aggregatedResponse.candidates[i]) {\n          aggregatedResponse.candidates[i] = {\n            index: candidate.index\n          } as GenerateContentCandidate;\n        }\n        // Keep overwriting, the last one will be final\n        aggregatedResponse.candidates[i].citationMetadata =\n          candidate.citationMetadata;\n        aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n        aggregatedResponse.candidates[i].finishMessage =\n          candidate.finishMessage;\n        aggregatedResponse.candidates[i].safetyRatings =\n          candidate.safetyRatings;\n\n        /**\n         * Candidates should always have content and parts, but this handles\n         * possible malformed responses.\n         */\n        if (candidate.content && candidate.content.parts) {\n          if (!aggregatedResponse.candidates[i].content) {\n            aggregatedResponse.candidates[i].content = {\n              role: candidate.content.role || 'user',\n              parts: []\n            };\n          }\n          const newPart: Partial<Part> = {};\n          for (const part of candidate.content.parts) {\n            if (part.text !== undefined) {\n              // The backend can send empty text parts. If these are sent back\n              // (e.g. in chat history), the backend will respond with an error.\n              // To prevent this, ignore empty text parts.\n              if (part.text === '') {\n                continue;\n              }\n              newPart.text = part.text;\n            }\n            if (part.functionCall) {\n              newPart.functionCall = part.functionCall;\n            }\n            if (Object.keys(newPart).length === 0) {\n              throw new AIError(\n                AIErrorCode.INVALID_CONTENT,\n                'Part should have at least one property, but there are none. This is likely caused ' +\n                  'by a malformed response from the backend.'\n              );\n            }\n            aggregatedResponse.candidates[i].content.parts.push(\n              newPart as Part\n            );\n          }\n        }\n      }\n    }\n  }\n  return aggregatedResponse;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenerateContentRequest,\n  GenerateContentResponse,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { createEnhancedContentResponse } from '../requests/response-helpers';\nimport { processStream } from '../requests/stream-reader';\nimport { ApiSettings } from '../types/internal';\nimport * as GoogleAIMapper from '../googleai-mappers';\nimport { BackendType } from '../public-types';\n\nexport async function generateContentStream(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentStreamResult> {\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    params = GoogleAIMapper.mapGenerateContentRequest(params);\n  }\n  const response = await makeRequest(\n    model,\n    Task.STREAM_GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ true,\n    JSON.stringify(params),\n    requestOptions\n  );\n  return processStream(response, apiSettings); // TODO: Map streaming responses\n}\n\nexport async function generateContent(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentResult> {\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    params = GoogleAIMapper.mapGenerateContentRequest(params);\n  }\n  const response = await makeRequest(\n    model,\n    Task.GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ false,\n    JSON.stringify(params),\n    requestOptions\n  );\n  const generateContentResponse = await processGenerateContentResponse(\n    response,\n    apiSettings\n  );\n  const enhancedResponse = createEnhancedContentResponse(\n    generateContentResponse\n  );\n  return {\n    response: enhancedResponse\n  };\n}\n\nasync function processGenerateContentResponse(\n  response: Response,\n  apiSettings: ApiSettings\n): Promise<GenerateContentResponse> {\n  const responseJson = await response.json();\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    return GoogleAIMapper.mapGenerateContentResponse(responseJson);\n  } else {\n    return responseJson;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Content, GenerateContentRequest, Part, AIErrorCode } from '../types';\nimport { AIError } from '../errors';\nimport { ImagenGenerationParams, PredictRequestBody } from '../types/internal';\n\nexport function formatSystemInstruction(\n  input?: string | Part | Content\n): Content | undefined {\n  // null or undefined\n  if (input == null) {\n    return undefined;\n  } else if (typeof input === 'string') {\n    return { role: 'system', parts: [{ text: input }] } as Content;\n  } else if ((input as Part).text) {\n    return { role: 'system', parts: [input as Part] };\n  } else if ((input as Content).parts) {\n    if (!(input as Content).role) {\n      return { role: 'system', parts: (input as Content).parts };\n    } else {\n      return input as Content;\n    }\n  }\n}\n\nexport function formatNewContent(\n  request: string | Array<string | Part>\n): Content {\n  let newParts: Part[] = [];\n  if (typeof request === 'string') {\n    newParts = [{ text: request }];\n  } else {\n    for (const partOrString of request) {\n      if (typeof partOrString === 'string') {\n        newParts.push({ text: partOrString });\n      } else {\n        newParts.push(partOrString);\n      }\n    }\n  }\n  return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(\n  parts: Part[]\n): Content {\n  const userContent: Content = { role: 'user', parts: [] };\n  const functionContent: Content = { role: 'function', parts: [] };\n  let hasUserContent = false;\n  let hasFunctionContent = false;\n  for (const part of parts) {\n    if ('functionResponse' in part) {\n      functionContent.parts.push(part);\n      hasFunctionContent = true;\n    } else {\n      userContent.parts.push(part);\n      hasUserContent = true;\n    }\n  }\n\n  if (hasUserContent && hasFunctionContent) {\n    throw new AIError(\n      AIErrorCode.INVALID_CONTENT,\n      'Within a single message, FunctionResponse cannot be mixed with other type of Part in the request for sending chat message.'\n    );\n  }\n\n  if (!hasUserContent && !hasFunctionContent) {\n    throw new AIError(\n      AIErrorCode.INVALID_CONTENT,\n      'No Content is provided for sending chat message.'\n    );\n  }\n\n  if (hasUserContent) {\n    return userContent;\n  }\n\n  return functionContent;\n}\n\nexport function formatGenerateContentInput(\n  params: GenerateContentRequest | string | Array<string | Part>\n): GenerateContentRequest {\n  let formattedRequest: GenerateContentRequest;\n  if ((params as GenerateContentRequest).contents) {\n    formattedRequest = params as GenerateContentRequest;\n  } else {\n    // Array or string\n    const content = formatNewContent(params as string | Array<string | Part>);\n    formattedRequest = { contents: [content] };\n  }\n  if ((params as GenerateContentRequest).systemInstruction) {\n    formattedRequest.systemInstruction = formatSystemInstruction(\n      (params as GenerateContentRequest).systemInstruction\n    );\n  }\n  return formattedRequest;\n}\n\n/**\n * Convert the user-defined parameters in {@link ImagenGenerationParams} to the format\n * that is expected from the REST API.\n *\n * @internal\n */\nexport function createPredictRequestBody(\n  prompt: string,\n  {\n    gcsURI,\n    imageFormat,\n    addWatermark,\n    numberOfImages = 1,\n    negativePrompt,\n    aspectRatio,\n    safetyFilterLevel,\n    personFilterLevel\n  }: ImagenGenerationParams\n): PredictRequestBody {\n  // Properties that are undefined will be omitted from the JSON string that is sent in the request.\n  const body: PredictRequestBody = {\n    instances: [\n      {\n        prompt\n      }\n    ],\n    parameters: {\n      storageUri: gcsURI,\n      negativePrompt,\n      sampleCount: numberOfImages,\n      aspectRatio,\n      outputOptions: imageFormat,\n      addWatermark,\n      safetyFilterLevel,\n      personGeneration: personFilterLevel,\n      includeRaiReason: true\n    }\n  };\n  return body;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Content, POSSIBLE_ROLES, Part, Role, AIErrorCode } from '../types';\nimport { AIError } from '../errors';\n\n// https://ai.google.dev/api/rest/v1beta/Content#part\n\nconst VALID_PART_FIELDS: Array<keyof Part> = [\n  'text',\n  'inlineData',\n  'functionCall',\n  'functionResponse'\n];\n\nconst VALID_PARTS_PER_ROLE: { [key in Role]: Array<keyof Part> } = {\n  user: ['text', 'inlineData'],\n  function: ['functionResponse'],\n  model: ['text', 'functionCall'],\n  // System instructions shouldn't be in history anyway.\n  system: ['text']\n};\n\nconst VALID_PREVIOUS_CONTENT_ROLES: { [key in Role]: Role[] } = {\n  user: ['model'],\n  function: ['model'],\n  model: ['user', 'function'],\n  // System instructions shouldn't be in history.\n  system: []\n};\n\nexport function validateChatHistory(history: Content[]): void {\n  let prevContent: Content | null = null;\n  for (const currContent of history) {\n    const { role, parts } = currContent;\n    if (!prevContent && role !== 'user') {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `First Content should be with role 'user', got ${role}`\n      );\n    }\n    if (!POSSIBLE_ROLES.includes(role)) {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(\n          POSSIBLE_ROLES\n        )}`\n      );\n    }\n\n    if (!Array.isArray(parts)) {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `Content should have 'parts' but property with an array of Parts`\n      );\n    }\n\n    if (parts.length === 0) {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `Each Content should have at least one part`\n      );\n    }\n\n    const countFields: Record<keyof Part, number> = {\n      text: 0,\n      inlineData: 0,\n      functionCall: 0,\n      functionResponse: 0\n    };\n\n    for (const part of parts) {\n      for (const key of VALID_PART_FIELDS) {\n        if (key in part) {\n          countFields[key] += 1;\n        }\n      }\n    }\n    const validParts = VALID_PARTS_PER_ROLE[role];\n    for (const key of VALID_PART_FIELDS) {\n      if (!validParts.includes(key) && countFields[key] > 0) {\n        throw new AIError(\n          AIErrorCode.INVALID_CONTENT,\n          `Content with role '${role}' can't contain '${key}' part`\n        );\n      }\n    }\n\n    if (prevContent) {\n      const validPreviousContentRoles = VALID_PREVIOUS_CONTENT_ROLES[role];\n      if (!validPreviousContentRoles.includes(prevContent.role)) {\n        throw new AIError(\n          AIErrorCode.INVALID_CONTENT,\n          `Content with role '${role}' can't follow '${\n            prevContent.role\n          }'. Valid previous roles: ${JSON.stringify(\n            VALID_PREVIOUS_CONTENT_ROLES\n          )}`\n        );\n      }\n    }\n    prevContent = currContent;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  Part,\n  RequestOptions,\n  StartChatParams\n} from '../types';\nimport { formatNewContent } from '../requests/request-helpers';\nimport { formatBlockErrorMessage } from '../requests/response-helpers';\nimport { validateChatHistory } from './chat-session-helpers';\nimport { generateContent, generateContentStream } from './generate-content';\nimport { ApiSettings } from '../types/internal';\nimport { logger } from '../logger';\n\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = 'SILENT_ERROR';\n\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nexport class ChatSession {\n  private _apiSettings: ApiSettings;\n  private _history: Content[] = [];\n  private _sendPromise: Promise<void> = Promise.resolve();\n\n  constructor(\n    apiSettings: ApiSettings,\n    public model: string,\n    public params?: StartChatParams,\n    public requestOptions?: RequestOptions\n  ) {\n    this._apiSettings = apiSettings;\n    if (params?.history) {\n      validateChatHistory(params.history);\n      this._history = params.history;\n    }\n  }\n\n  /**\n   * Gets the chat history so far. Blocked prompts are not added to history.\n   * Neither blocked candidates nor the prompts that generated them are added\n   * to history.\n   */\n  async getHistory(): Promise<Content[]> {\n    await this._sendPromise;\n    return this._history;\n  }\n\n  /**\n   * Sends a chat message and receives a non-streaming\n   * {@link GenerateContentResult}\n   */\n  async sendMessage(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    let finalResult = {} as GenerateContentResult;\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() =>\n        generateContent(\n          this._apiSettings,\n          this.model,\n          generateContentRequest,\n          this.requestOptions\n        )\n      )\n      .then(result => {\n        if (\n          result.response.candidates &&\n          result.response.candidates.length > 0\n        ) {\n          this._history.push(newContent);\n          const responseContent: Content = {\n            parts: result.response.candidates?.[0].content.parts || [],\n            // Response seems to come back without a role set.\n            role: result.response.candidates?.[0].content.role || 'model'\n          };\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(result.response);\n          if (blockErrorMessage) {\n            logger.warn(\n              `sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n        finalResult = result;\n      });\n    await this._sendPromise;\n    return finalResult;\n  }\n\n  /**\n   * Sends a chat message and receives the response as a\n   * {@link GenerateContentStreamResult} containing an iterable stream\n   * and a response promise.\n   */\n  async sendMessageStream(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    const streamPromise = generateContentStream(\n      this._apiSettings,\n      this.model,\n      generateContentRequest,\n      this.requestOptions\n    );\n\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() => streamPromise)\n      // This must be handled to avoid unhandled rejection, but jump\n      // to the final catch block with a label to not log this error.\n      .catch(_ignored => {\n        throw new Error(SILENT_ERROR);\n      })\n      .then(streamResult => streamResult.response)\n      .then(response => {\n        if (response.candidates && response.candidates.length > 0) {\n          this._history.push(newContent);\n          const responseContent = { ...response.candidates[0].content };\n          // Response seems to come back without a role set.\n          if (!responseContent.role) {\n            responseContent.role = 'model';\n          }\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(response);\n          if (blockErrorMessage) {\n            logger.warn(\n              `sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n      })\n      .catch(e => {\n        // Errors in streamPromise are already catchable by the user as\n        // streamPromise is returned.\n        // Avoid duplicating the error message in logs.\n        if (e.message !== SILENT_ERROR) {\n          // Users do not have access to _sendPromise to catch errors\n          // downstream from streamPromise, so they should not throw.\n          logger.error(e);\n        }\n      });\n    return streamPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CountTokensRequest,\n  CountTokensResponse,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { ApiSettings } from '../types/internal';\nimport * as GoogleAIMapper from '../googleai-mappers';\nimport { BackendType } from '../public-types';\n\nexport async function countTokens(\n  apiSettings: ApiSettings,\n  model: string,\n  params: CountTokensRequest,\n  requestOptions?: RequestOptions\n): Promise<CountTokensResponse> {\n  let body: string = '';\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    const mappedParams = GoogleAIMapper.mapCountTokensRequest(params, model);\n    body = JSON.stringify(mappedParams);\n  } else {\n    body = JSON.stringify(params);\n  }\n  const response = await makeRequest(\n    model,\n    Task.COUNT_TOKENS,\n    apiSettings,\n    false,\n    body,\n    requestOptions\n  );\n  return response.json();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  generateContent,\n  generateContentStream\n} from '../methods/generate-content';\nimport {\n  Content,\n  CountTokensRequest,\n  CountTokensResponse,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  GenerationConfig,\n  ModelParams,\n  Part,\n  RequestOptions,\n  SafetySetting,\n  StartChatParams,\n  Tool,\n  ToolConfig\n} from '../types';\nimport { ChatSession } from '../methods/chat-session';\nimport { countTokens } from '../methods/count-tokens';\nimport {\n  formatGenerateContentInput,\n  formatSystemInstruction\n} from '../requests/request-helpers';\nimport { AI } from '../public-types';\nimport { AIModel } from './ai-model';\n\n/**\n * Class for generative model APIs.\n * @public\n */\nexport class GenerativeModel extends AIModel {\n  generationConfig: GenerationConfig;\n  safetySettings: SafetySetting[];\n  requestOptions?: RequestOptions;\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: Content;\n\n  constructor(\n    ai: AI,\n    modelParams: ModelParams,\n    requestOptions?: RequestOptions\n  ) {\n    super(ai, modelParams.model);\n    this.generationConfig = modelParams.generationConfig || {};\n    this.safetySettings = modelParams.safetySettings || [];\n    this.tools = modelParams.tools;\n    this.toolConfig = modelParams.toolConfig;\n    this.systemInstruction = formatSystemInstruction(\n      modelParams.systemInstruction\n    );\n    this.requestOptions = requestOptions || {};\n  }\n\n  /**\n   * Makes a single non-streaming call to the model\n   * and returns an object containing a single {@link GenerateContentResponse}.\n   */\n  async generateContent(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContent(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Makes a single streaming call to the model\n   * and returns an object containing an iterable stream that iterates\n   * over all chunks in the streaming response as well as\n   * a promise that returns the final aggregated response.\n   */\n  async generateContentStream(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContentStream(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Gets a new {@link ChatSession} instance which can be used for\n   * multi-turn chats.\n   */\n  startChat(startChatParams?: StartChatParams): ChatSession {\n    return new ChatSession(\n      this._apiSettings,\n      this.model,\n      {\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        /**\n         * Overrides params inherited from GenerativeModel with those explicitly set in the\n         * StartChatParams. For example, if startChatParams.generationConfig is set, it'll override\n         * this.generationConfig.\n         */\n        ...startChatParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Counts the tokens in the provided request.\n   */\n  async countTokens(\n    request: CountTokensRequest | string | Array<string | Part>\n  ): Promise<CountTokensResponse> {\n    const formattedParams = formatGenerateContentInput(request);\n    return countTokens(this._apiSettings, this.model, formattedParams);\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AI } from '../public-types';\nimport { Task, makeRequest } from '../requests/request';\nimport { createPredictRequestBody } from '../requests/request-helpers';\nimport { handlePredictResponse } from '../requests/response-helpers';\nimport {\n  ImagenGCSImage,\n  ImagenGenerationConfig,\n  ImagenInlineImage,\n  RequestOptions,\n  ImagenModelParams,\n  ImagenGenerationResponse,\n  ImagenSafetySettings\n} from '../types';\nimport { AIModel } from './ai-model';\n\n/**\n * Class for Imagen model APIs.\n *\n * This class provides methods for generating images using the Imagen model.\n *\n * @example\n * ```javascript\n * const imagen = new ImagenModel(\n *   ai,\n *   {\n *     model: 'imagen-3.0-generate-002'\n *   }\n * );\n *\n * const response = await imagen.generateImages('A photo of a cat');\n * if (response.images.length > 0) {\n *   console.log(response.images[0].bytesBase64Encoded);\n * }\n * ```\n *\n * @beta\n */\nexport class ImagenModel extends AIModel {\n  /**\n   * The Imagen generation configuration.\n   */\n  generationConfig?: ImagenGenerationConfig;\n  /**\n   * Safety settings for filtering inappropriate content.\n   */\n  safetySettings?: ImagenSafetySettings;\n\n  /**\n   * Constructs a new instance of the {@link ImagenModel} class.\n   *\n   * @param ai - an {@link AI} instance.\n   * @param modelParams - Parameters to use when making requests to Imagen.\n   * @param requestOptions - Additional options to use when making requests.\n   *\n   * @throws If the `apiKey` or `projectId` fields are missing in your\n   * Firebase config.\n   */\n  constructor(\n    ai: AI,\n    modelParams: ImagenModelParams,\n    public requestOptions?: RequestOptions\n  ) {\n    const { model, generationConfig, safetySettings } = modelParams;\n    super(ai, model);\n    this.generationConfig = generationConfig;\n    this.safetySettings = safetySettings;\n  }\n\n  /**\n   * Generates images using the Imagen model and returns them as\n   * base64-encoded strings.\n   *\n   * @param prompt - A text prompt describing the image(s) to generate.\n   * @returns A promise that resolves to an {@link ImagenGenerationResponse}\n   * object containing the generated images.\n   *\n   * @throws If the request to generate images fails. This happens if the\n   * prompt is blocked.\n   *\n   * @remarks\n   * If the prompt was not blocked, but one or more of the generated images were filtered, the\n   * returned object will have a `filteredReason` property.\n   * If all images are filtered, the `images` array will be empty.\n   *\n   * @beta\n   */\n  async generateImages(\n    prompt: string\n  ): Promise<ImagenGenerationResponse<ImagenInlineImage>> {\n    const body = createPredictRequestBody(prompt, {\n      ...this.generationConfig,\n      ...this.safetySettings\n    });\n    const response = await makeRequest(\n      this.model,\n      Task.PREDICT,\n      this._apiSettings,\n      /* stream */ false,\n      JSON.stringify(body),\n      this.requestOptions\n    );\n    return handlePredictResponse<ImagenInlineImage>(response);\n  }\n\n  /**\n   * Generates images to Cloud Storage for Firebase using the Imagen model.\n   *\n   * @internal This method is temporarily internal.\n   *\n   * @param prompt - A text prompt describing the image(s) to generate.\n   * @param gcsURI - The URI of file stored in a Cloud Storage for Firebase bucket.\n   * This should be a directory. For example, `gs://my-bucket/my-directory/`.\n   * @returns A promise that resolves to an {@link ImagenGenerationResponse}\n   * object containing the URLs of the generated images.\n   *\n   * @throws If the request fails to generate images fails. This happens if\n   * the prompt is blocked.\n   *\n   * @remarks\n   * If the prompt was not blocked, but one or more of the generated images were filtered, the\n   * returned object will have a `filteredReason` property.\n   * If all images are filtered, the `images` array will be empty.\n   */\n  async generateImagesGCS(\n    prompt: string,\n    gcsURI: string\n  ): Promise<ImagenGenerationResponse<ImagenGCSImage>> {\n    const body = createPredictRequestBody(prompt, {\n      gcsURI,\n      ...this.generationConfig,\n      ...this.safetySettings\n    });\n    const response = await makeRequest(\n      this.model,\n      Task.PREDICT,\n      this._apiSettings,\n      /* stream */ false,\n      JSON.stringify(body),\n      this.requestOptions\n    );\n    return handlePredictResponse<ImagenGCSImage>(response);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AIError } from '../errors';\nimport { AIErrorCode } from '../types';\nimport {\n  SchemaInterface,\n  SchemaType,\n  SchemaParams,\n  SchemaRequest,\n  ObjectSchemaInterface\n} from '../types/schema';\n\n/**\n * Parent class encompassing all Schema types, with static methods that\n * allow building specific Schema types. This class can be converted with\n * `JSON.stringify()` into a JSON string accepted by Vertex AI REST endpoints.\n * (This string conversion is automatically done when calling SDK methods.)\n * @public\n */\nexport abstract class Schema implements SchemaInterface {\n  /**\n   * Optional. The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n  /** Optional. The format of the property.\n   * Supported formats:<br/>\n   * <ul>\n   *  <li>for NUMBER type: \"float\", \"double\"</li>\n   *  <li>for INTEGER type: \"int32\", \"int64\"</li>\n   *  <li>for STRING type: \"email\", \"byte\", etc</li>\n   * </ul>\n   */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /** Optional. The items of the property. */\n  items?: SchemaInterface;\n  /** The minimum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  minItems?: number;\n  /** The maximum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  maxItems?: number;\n  /** Optional. Whether the property is nullable. Defaults to false. */\n  nullable: boolean;\n  /** Optional. The example of the property. */\n  example?: unknown;\n  /**\n   * Allows user to add other schema properties that have not yet\n   * been officially added to the SDK.\n   */\n  [key: string]: unknown;\n\n  constructor(schemaParams: SchemaInterface) {\n    // eslint-disable-next-line guard-for-in\n    for (const paramKey in schemaParams) {\n      this[paramKey] = schemaParams[paramKey];\n    }\n    // Ensure these are explicitly set to avoid TS errors.\n    this.type = schemaParams.type;\n    this.nullable = schemaParams.hasOwnProperty('nullable')\n      ? !!schemaParams.nullable\n      : false;\n  }\n\n  /**\n   * Defines how this Schema should be serialized as JSON.\n   * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#tojson_behavior\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj: { type: SchemaType; [key: string]: unknown } = {\n      type: this.type\n    };\n    for (const prop in this) {\n      if (this.hasOwnProperty(prop) && this[prop] !== undefined) {\n        if (prop !== 'required' || this.type === SchemaType.OBJECT) {\n          obj[prop] = this[prop];\n        }\n      }\n    }\n    return obj as SchemaRequest;\n  }\n\n  static array(arrayParams: SchemaParams & { items: Schema }): ArraySchema {\n    return new ArraySchema(arrayParams, arrayParams.items);\n  }\n\n  static object(\n    objectParams: SchemaParams & {\n      properties: {\n        [k: string]: Schema;\n      };\n      optionalProperties?: string[];\n    }\n  ): ObjectSchema {\n    return new ObjectSchema(\n      objectParams,\n      objectParams.properties,\n      objectParams.optionalProperties\n    );\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static string(stringParams?: SchemaParams): StringSchema {\n    return new StringSchema(stringParams);\n  }\n\n  static enumString(\n    stringParams: SchemaParams & { enum: string[] }\n  ): StringSchema {\n    return new StringSchema(stringParams, stringParams.enum);\n  }\n\n  static integer(integerParams?: SchemaParams): IntegerSchema {\n    return new IntegerSchema(integerParams);\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static number(numberParams?: SchemaParams): NumberSchema {\n    return new NumberSchema(numberParams);\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static boolean(booleanParams?: SchemaParams): BooleanSchema {\n    return new BooleanSchema(booleanParams);\n  }\n}\n\n/**\n * A type that includes all specific Schema types.\n * @public\n */\nexport type TypedSchema =\n  | IntegerSchema\n  | NumberSchema\n  | StringSchema\n  | BooleanSchema\n  | ObjectSchema\n  | ArraySchema;\n\n/**\n * Schema class for \"integer\" types.\n * @public\n */\nexport class IntegerSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.INTEGER,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"number\" types.\n * @public\n */\nexport class NumberSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.NUMBER,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"boolean\" types.\n * @public\n */\nexport class BooleanSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.BOOLEAN,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"string\" types. Can be used with or without\n * enum values.\n * @public\n */\nexport class StringSchema extends Schema {\n  enum?: string[];\n  constructor(schemaParams?: SchemaParams, enumValues?: string[]) {\n    super({\n      type: SchemaType.STRING,\n      ...schemaParams\n    });\n    this.enum = enumValues;\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    if (this.enum) {\n      obj['enum'] = this.enum;\n    }\n    return obj as SchemaRequest;\n  }\n}\n\n/**\n * Schema class for \"array\" types.\n * The `items` param should refer to the type of item that can be a member\n * of the array.\n * @public\n */\nexport class ArraySchema extends Schema {\n  constructor(schemaParams: SchemaParams, public items: TypedSchema) {\n    super({\n      type: SchemaType.ARRAY,\n      ...schemaParams\n    });\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    obj.items = this.items.toJSON();\n    return obj;\n  }\n}\n\n/**\n * Schema class for \"object\" types.\n * The `properties` param must be a map of `Schema` objects.\n * @public\n */\nexport class ObjectSchema extends Schema {\n  constructor(\n    schemaParams: SchemaParams,\n    public properties: {\n      [k: string]: TypedSchema;\n    },\n    public optionalProperties: string[] = []\n  ) {\n    super({\n      type: SchemaType.OBJECT,\n      ...schemaParams\n    });\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    obj.properties = { ...this.properties };\n    const required = [];\n    if (this.optionalProperties) {\n      for (const propertyKey of this.optionalProperties) {\n        if (!this.properties.hasOwnProperty(propertyKey)) {\n          throw new AIError(\n            AIErrorCode.INVALID_SCHEMA,\n            `Property \"${propertyKey}\" specified in \"optionalProperties\" does not exist.`\n          );\n        }\n      }\n    }\n    for (const propertyKey in this.properties) {\n      if (this.properties.hasOwnProperty(propertyKey)) {\n        obj.properties[propertyKey] = this.properties[\n          propertyKey\n        ].toJSON() as SchemaRequest;\n        if (!this.optionalProperties.includes(propertyKey)) {\n          required.push(propertyKey);\n        }\n      }\n    }\n    if (required.length > 0) {\n      obj.required = required;\n    }\n    delete (obj as ObjectSchemaInterface).optionalProperties;\n    return obj as SchemaRequest;\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { logger } from '../logger';\n\n/**\n * Defines the image format for images generated by Imagen.\n *\n * Use this class to specify the desired format (JPEG or PNG) and compression quality\n * for images generated by Imagen. This is typically included as part of\n * {@link ImagenModelParams}.\n *\n * @example\n * ```javascript\n * const imagenModelParams = {\n *   // ... other ImagenModelParams\n *   imageFormat: ImagenImageFormat.jpeg(75) // JPEG with a compression level of 75.\n * }\n * ```\n *\n * @beta\n */\nexport class ImagenImageFormat {\n  /**\n   * The MIME type.\n   */\n  mimeType: string;\n  /**\n   * The level of compression (a number between 0 and 100).\n   */\n  compressionQuality?: number;\n\n  private constructor() {\n    this.mimeType = 'image/png';\n  }\n\n  /**\n   * Creates an {@link ImagenImageFormat} for a JPEG image.\n   *\n   * @param compressionQuality - The level of compression (a number between 0 and 100).\n   * @returns An {@link ImagenImageFormat} object for a JPEG image.\n   *\n   * @beta\n   */\n  static jpeg(compressionQuality?: number): ImagenImageFormat {\n    if (\n      compressionQuality &&\n      (compressionQuality < 0 || compressionQuality > 100)\n    ) {\n      logger.warn(\n        `Invalid JPEG compression quality of ${compressionQuality} specified; the supported range is [0, 100].`\n      );\n    }\n    return { mimeType: 'image/jpeg', compressionQuality };\n  }\n\n  /**\n   * Creates an {@link ImagenImageFormat} for a PNG image.\n   *\n   * @returns An {@link ImagenImageFormat} object for a PNG image.\n   *\n   * @beta\n   */\n  static png(): ImagenImageFormat {\n    return { mimeType: 'image/png' };\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Provider } from '@firebase/component';\nimport { getModularInstance } from '@firebase/util';\nimport { AI_TYPE } from './constants';\nimport { AIService } from './service';\nimport { AI, AIOptions, VertexAI, VertexAIOptions } from './public-types';\nimport {\n  ImagenModelParams,\n  ModelParams,\n  RequestOptions,\n  AIErrorCode\n} from './types';\nimport { AIError } from './errors';\nimport { AIModel, GenerativeModel, ImagenModel } from './models';\nimport { encodeInstanceIdentifier } from './helpers';\nimport { GoogleAIBackend, VertexAIBackend } from './backend';\n\nexport { ChatSession } from './methods/chat-session';\nexport * from './requests/schema-builder';\nexport { ImagenImageFormat } from './requests/imagen-image-format';\nexport { AIModel, GenerativeModel, ImagenModel, AIError };\nexport { Backend, VertexAIBackend, GoogleAIBackend } from './backend';\n\nexport { AIErrorCode as VertexAIErrorCode };\n\n/**\n * @deprecated Use the new {@link AIModel} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * Base class for Firebase AI model APIs.\n *\n * @public\n */\nexport const VertexAIModel = AIModel;\n\n/**\n * @deprecated Use the new {@link AIError} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * Error class for the Firebase AI SDK.\n *\n * @public\n */\nexport const VertexAIError = AIError;\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [AI_TYPE]: AIService;\n  }\n}\n\n/**\n * @deprecated Use the new {@link getAI | getAI()} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * Returns a {@link VertexAI} instance for the given app, configured to use the\n * Vertex AI Gemini API. This instance will be\n * configured to use the Vertex AI Gemini API.\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param options - Options to configure the Vertex AI instance, including the location.\n *\n * @public\n */\nexport function getVertexAI(\n  app: FirebaseApp = getApp(),\n  options?: VertexAIOptions\n): VertexAI {\n  app = getModularInstance(app);\n  // Dependencies\n  const AIProvider: Provider<'AI'> = _getProvider(app, AI_TYPE);\n\n  const backend = new VertexAIBackend(options?.location);\n  const identifier = encodeInstanceIdentifier(backend);\n  return AIProvider.getImmediate({\n    identifier\n  });\n}\n\n/**\n * Returns the default {@link AI} instance that is associated with the provided\n * {@link @firebase/app#FirebaseApp}. If no instance exists, initializes a new instance with the\n * default settings.\n *\n * @example\n * ```javascript\n * const ai = getAI(app);\n * ```\n *\n * @example\n * ```javascript\n * // Get an AI instance configured to use the Gemini Developer API (via Google AI).\n * const ai = getAI(app, { backend: new GoogleAIBackend() });\n * ```\n *\n * @example\n * ```javascript\n * // Get an AI instance configured to use the Vertex AI Gemini API.\n * const ai = getAI(app, { backend: new VertexAIBackend() });\n * ```\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param options - {@link AIOptions} that configure the AI instance.\n * @returns The default {@link AI} instance for the given {@link @firebase/app#FirebaseApp}.\n *\n * @public\n */\nexport function getAI(\n  app: FirebaseApp = getApp(),\n  options: AIOptions = { backend: new GoogleAIBackend() }\n): AI {\n  app = getModularInstance(app);\n  // Dependencies\n  const AIProvider: Provider<'AI'> = _getProvider(app, AI_TYPE);\n\n  const identifier = encodeInstanceIdentifier(options.backend);\n  return AIProvider.getImmediate({\n    identifier\n  });\n}\n\n/**\n * Returns a {@link GenerativeModel} class with methods for inference\n * and other functionality.\n *\n * @public\n */\nexport function getGenerativeModel(\n  ai: AI,\n  modelParams: ModelParams,\n  requestOptions?: RequestOptions\n): GenerativeModel {\n  if (!modelParams.model) {\n    throw new AIError(\n      AIErrorCode.NO_MODEL,\n      `Must provide a model name. Example: getGenerativeModel({ model: 'my-model-name' })`\n    );\n  }\n  return new GenerativeModel(ai, modelParams, requestOptions);\n}\n\n/**\n * Returns an {@link ImagenModel} class with methods for using Imagen.\n *\n * Only Imagen 3 models (named `imagen-3.0-*`) are supported.\n *\n * @param ai - An {@link AI} instance.\n * @param modelParams - Parameters to use when making Imagen requests.\n * @param requestOptions - Additional options to use when making requests.\n *\n * @throws If the `apiKey` or `projectId` fields are missing in your\n * Firebase config.\n *\n * @beta\n */\nexport function getImagenModel(\n  ai: AI,\n  modelParams: ImagenModelParams,\n  requestOptions?: RequestOptions\n): ImagenModel {\n  if (!modelParams.model) {\n    throw new AIError(\n      AIErrorCode.NO_MODEL,\n      `Must provide a model name. Example: getImagenModel({ model: 'my-model-name' })`\n    );\n  }\n  return new ImagenModel(ai, modelParams, requestOptions);\n}\n", "/**\n * The Firebase AI Web SDK.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { AIService } from './service';\nimport { AI_TYPE } from './constants';\nimport { Component, ComponentType } from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { decodeInstanceIdentifier } from './helpers';\nimport { AIError } from './api';\nimport { AIErrorCode } from './types';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerAI(): void {\n  _registerComponent(\n    new Component(\n      AI_TYPE,\n      (container, { instanceIdentifier }) => {\n        if (!instanceIdentifier) {\n          throw new AIError(\n            AIErrorCode.ERROR,\n            'AIService instance identifier is undefined.'\n          );\n        }\n\n        const backend = decodeInstanceIdentifier(instanceIdentifier);\n\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const auth = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        return new AIService(app, backend, auth, appCheckProvider);\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterAI();\n\nexport * from './api';\nexport * from './public-types';\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "AI_TYPE", "DEFAULT_LOCATION", "PACKAGE_VERSION", "version", "POSSIBLE_ROLES", "HarmCategory", "HarmBlockThreshold", "HarmBlockMethod", "HarmProbability", "HarmSeverity", "BlockReason", "FinishReason", "FunctionCallingMode", "Modality", "ResponseModality", "TEXT", "IMAGE", "SchemaType", "ImagenSafetyFilterLevel", "ImagenPersonFilterLevel", "ImagenAspectRatio", "BackendType", "VERTEX_AI", "GOOGLE_AI", "Backend", "backendType", "GoogleAIBackend", "VertexAIBackend", "location", "AIService", "app", "backend", "authProvider", "appCheckProvider", "appCheck", "getImmediate", "optional", "auth", "_delete", "Promise", "resolve", "AIError", "customErrorData", "toString", "encodeInstanceIdentifier", "JSON", "stringify", "AIModel", "ai", "modelName", "_b", "_a", "options", "<PERSON><PERSON><PERSON><PERSON>", "_d", "_c", "projectId", "_f", "_e", "appId", "_apiSettings", "project", "automaticDataCollectionEnabled", "_isFirebaseServerApp", "settings", "appCheckToken", "token", "getAppCheckToken", "getToken", "getAuthToken", "model", "normalizeModelName", "normalizeGoogleAIModelName", "normalizeVertexAIModelName", "includes", "startsWith", "logger", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "Task", "RequestUrl", "task", "apiSettings", "stream", "requestOptions", "url", "URL", "baseUrl", "pathname", "apiVersion", "modelPath", "search", "queryParams", "params", "URLSearchParams", "set", "async", "getHeaders", "headers", "Headers", "append", "getClientHeaders", "loggingTags", "push", "join", "authToken", "accessToken", "makeRequest", "body", "response", "fetchTimeoutId", "request", "constructRequest", "fetchOptions", "timeout<PERSON><PERSON><PERSON>", "timeout", "abortController", "AbortController", "setTimeout", "abort", "signal", "fetch", "ok", "errorDetails", "json", "details", "e", "status", "some", "detail", "reason", "links", "description", "statusText", "err", "stack", "clearTimeout", "createEnhancedContentResponse", "candidates", "hasOwnProperty", "index", "responseWithHelpers", "addHelpers", "text", "length", "hadBadFinishReason", "formatBlockErrorMessage", "getText", "textStrings", "content", "parts", "part", "promptFeedback", "inlineDataParts", "getInlineDataParts", "inlineData", "functionCalls", "getFunctionCalls", "functionCall", "badFinishReasons", "RECITATION", "SAFETY", "candidate", "finishReason", "firstCandidate", "finishMessage", "blockReason", "blockReasonMessage", "handlePredictResponse", "responseJson", "images", "filteredReason", "predictions", "prediction", "raiFilteredReason", "mimeType", "bytesBase64Encoded", "gcsUri", "gcsURI", "mapGenerateContentRequest", "generateContentRequest", "safetySettings", "for<PERSON>ach", "safetySetting", "generationConfig", "topK", "roundedTopK", "Math", "round", "mapGenerateContentResponse", "googleAIResponse", "mapGenerateContentCandidates", "undefined", "prompt", "mapPromptFeedback", "usageMetadata", "mappedCandidates", "mappedSafetyRatings", "citationMetadata", "citations", "citationSources", "safetyRatings", "map", "safetyRating", "assign", "severity", "HARM_SEVERITY_UNSUPPORTED", "probabilityScore", "severityScore", "videoMetadata", "mappedCandidate", "groundingMetadata", "category", "probability", "blocked", "responseLineRE", "processStream", "responseStream", "getResponseStream", "inputStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "start", "controller", "currentText", "pump", "read", "then", "done", "trim", "close", "parsedResponse", "match", "parse", "enqueue", "substring", "pipeThrough", "TextDecoderStream", "fatal", "stream1", "stream2", "tee", "generateResponseSequence", "getResponsePromise", "allResponses", "generateContentResponse", "aggregateResponses", "GoogleAIMapper.mapGenerateContentResponse", "__await", "enhancedResponse", "responses", "lastResponse", "aggregatedResponse", "i", "role", "newPart", "keys", "generateContentStream", "GoogleAIMapper.mapGenerateContentRequest", "STREAM_GENERATE_CONTENT", "generateContent", "GENERATE_CONTENT", "processGenerateContentResponse", "formatSystemInstruction", "input", "formatNewContent", "newParts", "partOrString", "assignRoleToPartsAndValidateSendMessageRequest", "userContent", "functionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasFunctionContent", "formatGenerateContentInput", "formattedRequest", "contents", "systemInstruction", "createPredictRequestBody", "imageFormat", "addWatermark", "numberOfImages", "negativePrompt", "aspectRatio", "safetyFilterLevel", "personFilterLevel", "instances", "parameters", "storageUri", "sampleCount", "outputOptions", "personGeneration", "includeRaiReason", "VALID_PART_FIELDS", "VALID_PARTS_PER_ROLE", "user", "function", "system", "VALID_PREVIOUS_CONTENT_ROLES", "SILENT_ERROR", "ChatSession", "_history", "_sendPromise", "history", "validateChatHistory", "prevContent", "currContent", "Array", "isArray", "countFields", "functionResponse", "validParts", "getHistory", "sendMessage", "newContent", "tools", "toolConfig", "finalResult", "result", "responseContent", "blockErrorMessage", "sendMessageStream", "streamPromise", "catch", "_ignored", "streamResult", "countTokens", "mappedParams", "mapCountTokensRequest", "countTokensRequest", "GoogleAIMapper.mapCountTokensRequest", "COUNT_TOKENS", "GenerativeModel", "modelParams", "formattedParams", "startChat", "startChatParams", "ImagenModel", "generateImages", "PREDICT", "generateImagesGCS", "<PERSON><PERSON><PERSON>", "schemaParams", "<PERSON><PERSON><PERSON><PERSON>", "nullable", "toJSON", "obj", "prop", "OBJECT", "array", "arrayParams", "ArraySchema", "items", "object", "objectParams", "ObjectSchema", "properties", "optionalProperties", "string", "stringParams", "StringSchema", "enumString", "enum", "integer", "integerParams", "IntegerSchema", "number", "numberParams", "NumberSchema", "boolean", "booleanParams", "BooleanSchema", "INTEGER", "NUMBER", "BOOLEAN", "enum<PERSON><PERSON><PERSON>", "STRING", "ARRAY", "required", "propertyKey", "ImagenImageFormat", "jpeg", "compressionQuality", "png", "VertexAIModel", "VertexAIError", "getVertexAI", "getApp", "AIProvider", "_get<PERSON><PERSON><PERSON>", "identifier", "getAI", "getGenerativeModel", "getImagenModel", "registerAI", "_registerComponent", "container", "instanceIdentifier", "decodeInstanceIdentifier", "identifierParts", "split", "get<PERSON><PERSON><PERSON>", "registerVersion"], "mappings": "2HAyEM,MAAOA,sBAAsBC,MAIjC,WAAAC,CAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAX,CACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJ,MAAAH,CACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,KAAO,GAEtD,CAf+BJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,EAGxD,EAUH,MAAMkB,EAAU,gBClHV,SAAUM,mBACdd,GAEA,OAAIA,GAAYA,EAA+Be,UACrCf,EAA+Be,UAEhCf,CAEX,CCDa,MAAAgB,UAiBX,WAAA7B,CACWM,EACAwB,EACAC,GAFA1B,KAAIC,KAAJA,EACAD,KAAeyB,gBAAfA,EACAzB,KAAI0B,KAAJA,EAnBX1B,KAAiB2B,mBAAG,EAIpB3B,KAAY4B,aAAe,GAE3B5B,KAAA6B,kBAA2C,OAE3C7B,KAAiB8B,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADAhC,KAAK6B,kBAAoBG,EAClBhC,IACR,CAED,oBAAAiC,CAAqBN,GAEnB,OADA3B,KAAK2B,kBAAoBA,EAClB3B,IACR,CAED,eAAAkC,CAAgBC,GAEd,OADAnC,KAAK4B,aAAeO,EACbnC,IACR,CAED,0BAAAoC,CAA2BC,GAEzB,OADArC,KAAK8B,kBAAoBO,EAClBrC,IACR,MCfSsC,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAOX,CAAA,IAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAIpE,MACR,8DAA8D8D,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAAStD,WACnBwD,EAMN,6gCCvGI,MAAMO,EAAU,KAEVC,EAAmB,cAMnBC,EAAkBC,ECAlBC,EAAiB,CAAC,OAAQ,QAAS,WAAY,cAMhDC,EAWAC,EA6BAC,EAeAC,EAuBAC,EA8BAC,EAuBAC,EA0CAC,EAwBAC,GArMZ,SAAYR,GACVA,EAAA,0BAAA,4BACAA,EAAA,gCAAA,kCACAA,EAAA,yBAAA,2BACAA,EAAA,gCAAA,iCACD,CALD,CAAYA,IAAAA,EAKX,CAAA,IAMD,SAAYC,GAIVA,EAAA,oBAAA,sBAIAA,EAAA,uBAAA,yBAIAA,EAAA,gBAAA,kBAIAA,EAAA,WAAA,aAKAA,EAAA,IAAA,KACD,CAtBD,CAAYA,IAAAA,EAsBX,CAAA,IAOD,SAAYC,GAIVA,EAAA,SAAA,WAIAA,EAAA,YAAA,aACD,CATD,CAAYA,IAAAA,EASX,CAAA,IAMD,SAAYC,GAIVA,EAAA,WAAA,aAIAA,EAAA,IAAA,MAIAA,EAAA,OAAA,SAIAA,EAAA,KAAA,MACD,CAjBD,CAAYA,IAAAA,EAiBX,CAAA,IAMD,SAAYC,GAIVA,EAAA,yBAAA,2BAIAA,EAAA,kBAAA,oBAIAA,EAAA,qBAAA,uBAIAA,EAAA,mBAAA,qBAOAA,EAAA,0BAAA,2BACD,CAxBD,CAAYA,IAAAA,EAwBX,CAAA,IAMD,SAAYC,GAIVA,EAAA,OAAA,SAIAA,EAAA,MAAA,QAIAA,EAAA,UAAA,YAIAA,EAAA,mBAAA,oBACD,CAjBD,CAAYA,IAAAA,EAiBX,CAAA,IAMD,SAAYC,GAIVA,EAAA,KAAA,OAIAA,EAAA,WAAA,aAIAA,EAAA,OAAA,SAIAA,EAAA,WAAA,aAIAA,EAAA,MAAA,QAIAA,EAAA,UAAA,YAIAA,EAAA,mBAAA,qBAIAA,EAAA,KAAA,OAIAA,EAAA,wBAAA,yBACD,CArCD,CAAYA,IAAAA,EAqCX,CAAA,IAKD,SAAYC,GAKVA,EAAA,KAAA,OAOAA,EAAA,IAAA,MAKAA,EAAA,KAAA,MACD,CAlBD,CAAYA,IAAAA,EAkBX,CAAA,IAMD,SAAYC,GAIVA,EAAA,qBAAA,uBAIAA,EAAA,KAAA,OAIAA,EAAA,MAAA,QAIAA,EAAA,MAAA,QAIAA,EAAA,MAAA,QAIAA,EAAA,SAAA,UACD,CAzBD,CAAYA,IAAAA,EAyBX,CAAA,IAOY,MAAAC,EAAmB,CAK9BC,KAAM,OAKNC,MAAO,aCzPGC,ECyFAC,EA8BAC,EAsDAC,GD7KZ,SAAYH,GAEVA,EAAA,OAAA,SAEAA,EAAA,OAAA,SAEAA,EAAA,QAAA,UAEAA,EAAA,QAAA,UAEAA,EAAA,MAAA,QAEAA,EAAA,OAAA,QACD,CAbD,CAAYA,IAAAA,EAaX,CAAA,IC4ED,SAAYC,GAIVA,EAAA,oBAAA,sBAIAA,EAAA,uBAAA,yBAIAA,EAAA,gBAAA,kBAOAA,EAAA,WAAA,YACD,CApBD,CAAYA,IAAAA,EAoBX,CAAA,IAUD,SAAYC,GAIVA,EAAA,UAAA,aAQAA,EAAA,YAAA,cAQAA,EAAA,UAAA,WACD,CArBD,CAAYA,IAAAA,EAqBX,CAAA,IAiCD,SAAYC,GAIVA,EAAA,OAAA,MAIAA,EAAA,cAAA,MAIAA,EAAA,aAAA,MAIAA,EAAA,eAAA,OAIAA,EAAA,cAAA,MACD,CArBD,CAAYA,IAAAA,EAqBX,CAAA,ICzIY,MAAAC,EAAc,CAKzBC,UAAW,YAMXC,UAAW,aC/DS,MAAAC,QAUpB,WAAA7F,CAAsB+B,GACpB1B,KAAKyF,YAAc/D,CACpB,EAWG,MAAOgE,wBAAwBF,QAInC,WAAA7F,GACEI,MAAMsF,EAAYE,UACnB,EAWG,MAAOI,wBAAwBH,QAenC,WAAA7F,CAAYiG,EAAmB3B,GAC7BlE,MAAMsF,EAAYC,WAIhBtF,KAAK4F,SAHFA,GACa3B,CAInB,EC5DU,MAAA4B,UAKX,WAAAlG,CACSmG,EACAC,EACPC,EACAC,GAHOjG,KAAG8F,IAAHA,EACA9F,KAAO+F,QAAPA,EAIP,MAAMG,EAAWD,aAAgB,EAAhBA,EAAkBE,aAAa,CAAEC,UAAU,IACtDC,EAAOL,aAAY,EAAZA,EAAcG,aAAa,CAAEC,UAAU,IACpDpG,KAAKqG,KAAOA,GAAQ,KACpBrG,KAAKkG,SAAWA,GAAY,KAG1BlG,KAAK4F,SADHG,aAAmBJ,gBACLI,EAAQH,SAER,EAEnB,CAED,OAAAU,GACE,OAAOC,QAAQC,SAChB,EC7BG,MAAOC,gBAAgBhH,cAQ3B,WAAAE,CACWC,EACTC,EACS6G,GAGT,MAEMrF,EAAc,GAFJ2C,MAEmBnE,MADlB,GADDmE,KACepE,OAE/BG,MAAMH,EAAMyB,GARHrB,KAAIJ,KAAJA,EAEAI,KAAe0G,gBAAfA,EAYLhH,MAAMW,mBAGRX,MAAMW,kBAAkBL,KAAMyG,SAOhCvG,OAAOC,eAAeH,KAAMyG,QAAQrG,WAGpCJ,KAAK2G,SAAW,IAAMtF,CACvB,ECnCG,SAAUuF,yBAAyBb,GACvC,GAAIA,aAAmBL,gBACrB,MAAO,GAAG1B,aACL,GAAI+B,aAAmBJ,gBAC5B,MAAO,GAAG3B,cAAoB+B,EAAQH,WAEtC,MAAM,IAAIa,QAER,QAAA,oBAAoBI,KAAKC,UAAUf,EAAQN,eAGjD,CCRsB,MAAAsB,QA6BpB,WAAApH,CAAsBqH,EAAQC,mBAC5B,KAAoB,QAAfC,EAAM,QAANC,EAAAH,EAAGlB,WAAG,IAAAqB,OAAA,EAAAA,EAAEC,eAAO,IAAAF,OAAA,EAAAA,EAAEG,QACpB,MAAM,IAAIZ,QAER,aAAA,yHAEG,KAAoB,QAAfa,EAAM,QAANC,EAAAP,EAAGlB,WAAG,IAAAyB,OAAA,EAAAA,EAAEH,eAAO,IAAAE,OAAA,EAAAA,EAAEE,WAC3B,MAAM,IAAIf,QAER,gBAAA,+HAEG,KAAoB,QAAfgB,EAAM,QAANC,EAAAV,EAAGlB,WAAG,IAAA4B,OAAA,EAAAA,EAAEN,eAAO,IAAAK,OAAA,EAAAA,EAAEE,OAC3B,MAAM,IAAIlB,QAER,YAAA,uHAYF,GATAzG,KAAK4H,aAAe,CAClBP,OAAQL,EAAGlB,IAAIsB,QAAQC,OACvBQ,QAASb,EAAGlB,IAAIsB,QAAQI,UACxBG,MAAOX,EAAGlB,IAAIsB,QAAQO,MACtBG,+BAAgCd,EAAGlB,IAAIgC,+BACvClC,SAAUoB,EAAGpB,SACbG,QAASiB,EAAGjB,SAGVgC,EAAqBf,EAAGlB,MAAQkB,EAAGlB,IAAIkC,SAASC,cAAe,CACjE,MAAMC,EAAQlB,EAAGlB,IAAIkC,SAASC,cAC9BjI,KAAK4H,aAAaO,iBAAmB,IAC5B5B,QAAQC,QAAQ,CAAE0B,SAE5B,MAAWlB,EAAiBd,WAC3BlG,KAAK4H,aAAaO,iBAAmB,IAClCnB,EAAiBd,SAAUkC,YAG3BpB,EAAiBX,OACpBrG,KAAK4H,aAAaS,aAAe,IAC9BrB,EAAiBX,KAAM+B,YAG5BpI,KAAKsI,MAAQvB,QAAQwB,mBACnBtB,EACAjH,KAAK4H,aAAa7B,QAAQN,YAG/B,CAUD,yBAAO8C,CACLtB,EACAxB,GAEA,OAAIA,IAAgBJ,EAAYE,UACvBwB,QAAQyB,2BAA2BvB,GAEnCF,QAAQ0B,2BAA2BxB,EAE7C,CAKO,iCAAOuB,CAA2BvB,GACxC,MAAO,UAAUA,GAClB,CAKO,iCAAOwB,CAA2BxB,GACxC,IAAIqB,EAcJ,OAVIA,EAHArB,EAAUyB,SAAS,KACjBzB,EAAU0B,WAAW,WAEf,qBAAqB1B,IAGrBA,EAIF,4BAA4BA,IAG/BqB,CACR,ECtII,MAAMM,EAAS,IX0GT,MAAAC,OAOX,WAAAlJ,CAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAAS8I,UAAG1F,EAsBZpD,KAAW+I,YAAezF,kBAc1BtD,KAAegJ,gBAAsB,IAzC5C,CAOD,YAAItF,GACF,OAAO1D,KAAK8I,SACb,CAED,YAAIpF,CAASuF,GACX,KAAMA,KAAO3G,GACX,MAAM,IAAI4G,UAAU,kBAAkBD,+BAExCjJ,KAAK8I,UAAYG,CAClB,CAGD,WAAAE,CAAYF,GACVjJ,KAAK8I,UAA2B,iBAARG,EAAmB1G,EAAkB0G,GAAOA,CACrE,CAOD,cAAIG,GACF,OAAOpJ,KAAK+I,WACb,CACD,cAAIK,CAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtBlJ,KAAK+I,YAAcE,CACpB,CAMD,kBAAII,GACF,OAAOrJ,KAAKgJ,eACb,CACD,kBAAIK,CAAeJ,GACjBjJ,KAAKgJ,gBAAkBC,CACxB,CAMD,KAAAzG,IAASiB,GACPzD,KAAKgJ,iBAAmBhJ,KAAKgJ,gBAAgBhJ,KAAMsC,EAASG,SAAUgB,GACtEzD,KAAK+I,YAAY/I,KAAMsC,EAASG,SAAUgB,EAC3C,CACD,GAAA6F,IAAO7F,GACLzD,KAAKgJ,iBACHhJ,KAAKgJ,gBAAgBhJ,KAAMsC,EAASK,WAAYc,GAClDzD,KAAK+I,YAAY/I,KAAMsC,EAASK,WAAYc,EAC7C,CACD,IAAAb,IAAQa,GACNzD,KAAKgJ,iBAAmBhJ,KAAKgJ,gBAAgBhJ,KAAMsC,EAASO,QAASY,GACrEzD,KAAK+I,YAAY/I,KAAMsC,EAASO,QAASY,EAC1C,CACD,IAAAX,IAAQW,GACNzD,KAAKgJ,iBAAmBhJ,KAAKgJ,gBAAgBhJ,KAAMsC,EAASS,QAASU,GACrEzD,KAAK+I,YAAY/I,KAAMsC,EAASS,QAASU,EAC1C,CACD,KAAAT,IAASS,GACPzD,KAAKgJ,iBAAmBhJ,KAAKgJ,gBAAgBhJ,KAAMsC,EAASW,SAAUQ,GACtEzD,KAAK+I,YAAY/I,KAAMsC,EAASW,SAAUQ,EAC3C,GW/L8B,sBCWjC,IAAY8F,GAAZ,SAAYA,GACVA,EAAA,iBAAA,kBACAA,EAAA,wBAAA,wBACAA,EAAA,aAAA,cACAA,EAAA,QAAA,SACD,CALD,CAAYA,IAAAA,EAKX,CAAA,IAEY,MAAAC,WACX,WAAA7J,CACS2I,EACAmB,EACAC,EACAC,EACAC,GAJA5J,KAAKsI,MAALA,EACAtI,KAAIyJ,KAAJA,EACAzJ,KAAW0J,YAAXA,EACA1J,KAAM2J,OAANA,EACA3J,KAAc4J,eAAdA,CACL,CACJ,QAAAjD,GACE,MAAMkD,EAAM,IAAIC,IAAI9J,KAAK+J,SAGzB,OAFAF,EAAIG,SAAW,IAAIhK,KAAKiK,cAAcjK,KAAKkK,aAAalK,KAAKyJ,OAC7DI,EAAIM,OAASnK,KAAKoK,YAAYzD,WACvBkD,EAAIlD,UACZ,CAED,WAAYoD,SACV,OAA4B,UAArB/J,KAAK4J,sBAAgB,IAAAzC,OAAA,EAAAA,EAAA4C,UX9BA,yCW+B7B,CAED,cAAYE,GACV,MXhC+B,QWiChC,CAED,aAAYC,GACV,GAAIlK,KAAK0J,YAAY3D,mBAAmBL,gBACtC,MAAO,YAAY1F,KAAK0J,YAAY7B,WAAW7H,KAAKsI,QAC/C,GAAItI,KAAK0J,YAAY3D,mBAAmBJ,gBAC7C,MAAO,YAAY3F,KAAK0J,YAAY7B,qBAAqB7H,KAAK0J,YAAY3D,QAAQH,YAAY5F,KAAKsI,QAEnG,MAAM,IAAI7B,QAAO,QAEf,oBAAoBI,KAAKC,UAAU9G,KAAK0J,YAAY3D,WAGzD,CAED,eAAYqE,GACV,MAAMC,EAAS,IAAIC,gBAKnB,OAJItK,KAAK2J,QACPU,EAAOE,IAAI,MAAO,OAGbF,CACR,EAaIG,eAAeC,WAAWZ,GAC/B,MAAMa,EAAU,IAAIC,QAOpB,GANAD,EAAQE,OAAO,eAAgB,oBAC/BF,EAAQE,OAAO,oBAVjB,SAASC,mBACP,MAAMC,EAAc,GAGpB,OAFAA,EAAYC,KAAK,SAAmB7G,KACpC4G,EAAYC,KAAK,QAAQ7G,KAClB4G,EAAYE,KAAK,IAC1B,CAKsCH,IACpCH,EAAQE,OAAO,iBAAkBf,EAAIH,YAAYrC,QAC7CwC,EAAIH,YAAY5B,gCAClB4C,EAAQE,OAAO,mBAAoBf,EAAIH,YAAY/B,OAEjDkC,EAAIH,YAAYvB,iBAAkB,CACpC,MAAMF,QAAsB4B,EAAIH,YAAYvB,mBACxCF,IACFyC,EAAQE,OAAO,sBAAuB3C,EAAcC,OAChDD,EAAcjF,OAChB4F,EAAO9F,KACL,6CAA6CmF,EAAcjF,MAAMnD,WAIxE,CAED,GAAIgK,EAAIH,YAAYrB,aAAc,CAChC,MAAM4C,QAAkBpB,EAAIH,YAAYrB,eACpC4C,GACFP,EAAQE,OAAO,gBAAiB,YAAYK,EAAUC,cAEzD,CAED,OAAOR,CACT,CAqBOF,eAAeW,YACpB7C,EACAmB,EACAC,EACAC,EACAyB,EACAxB,GAEA,MAAMC,EAAM,IAAIL,WAAWlB,EAAOmB,EAAMC,EAAaC,EAAQC,GAC7D,IAAIyB,EACAC,EACJ,IACE,MAAMC,QA/BHf,eAAegB,iBACpBlD,EACAmB,EACAC,EACAC,EACAyB,EACAxB,GAEA,MAAMC,EAAM,IAAIL,WAAWlB,EAAOmB,EAAMC,EAAaC,EAAQC,GAC7D,MAAO,CACLC,IAAKA,EAAIlD,WACT8E,aAAc,CACZ3H,OAAQ,OACR4G,cAAeD,WAAWZ,GAC1BuB,QAGN,CAc0BI,CACpBlD,EACAmB,EACAC,EACAC,EACAyB,EACAxB,GAGI8B,EACuB,OAA3B9B,aAAA,EAAAA,EAAgB+B,UAAmB/B,EAAe+B,SAAW,EACzD/B,EAAe+B,QXtIe,KWwI9BC,EAAkB,IAAIC,gBAK5B,GAJAP,EAAiBQ,YAAW,IAAMF,EAAgBG,SAASL,GAC3DH,EAAQE,aAAaO,OAASJ,EAAgBI,OAE9CX,QAAiBY,MAAMV,EAAQ1B,IAAK0B,EAAQE,eACvCJ,EAASa,GAAI,CAChB,IACIC,EADAtM,EAAU,GAEd,IACE,MAAMuM,QAAaf,EAASe,OAC5BvM,EAAUuM,EAAKpJ,MAAMnD,QACjBuM,EAAKpJ,MAAMqJ,UACbxM,GAAW,IAAIgH,KAAKC,UAAUsF,EAAKpJ,MAAMqJ,WACzCF,EAAeC,EAAKpJ,MAAMqJ,QAE7B,CAAC,MAAOC,GAER,CACD,GACsB,MAApBjB,EAASkB,QACTJ,EAAaK,MACVC,GAA2C,qBAAlBA,EAAOC,UAEnCP,EAAaK,MAAMC,YACjB,OAEM,QAFNvF,EAEI,QAFJC,EACEsF,EAAOE,aACL,IAAAxF,OAAA,EAAAA,EAAA,UAAE,IAAAD,OAAA,EAAAA,EAAE0F,YAAYlE,SAClB,2CACD,IAGH,MAAM,IAAIjC,QAAO,kBAKb,gOAAkDoD,EAAIH,YAAY7B,6JAIpE,CACE0E,OAAQlB,EAASkB,OACjBM,WAAYxB,EAASwB,WACrBV,iBAIN,MAAM,IAAI1F,QAAO,cAEf,uBAAuBoD,OAASwB,EAASkB,UAAUlB,EAASwB,eAAehN,IAC3E,CACE0M,OAAQlB,EAASkB,OACjBM,WAAYxB,EAASwB,WACrBV,gBAGL,CACF,CAAC,MAAOG,GACP,IAAIQ,EAAMR,EAaV,KAXiD,gBAA9CA,EAAc1M,MACoC,oBAAlD0M,EAAc1M,MACf0M,aAAa5M,QAEboN,EAAM,IAAIrG,QAAO,QAEf,uBAAuBoD,EAAIlD,eAAe2F,EAAEzM,WAE9CiN,EAAIC,MAAQT,EAAES,OAGVD,CACP,CAAS,QACJxB,GACF0B,aAAa1B,EAEhB,CACD,OAAOD,CACT,CCjNM,SAAU4B,8BACd5B,GAQIA,EAAS6B,aAAe7B,EAAS6B,WAAW,GAAGC,eAAe,WAChE9B,EAAS6B,WAAW,GAAGE,MAAQ,GAGjC,MAAMC,EAQF,SAAUC,WACdjC,GAoGA,OAlGCA,EAA6CkC,KAAO,KACnD,GAAIlC,EAAS6B,YAAc7B,EAAS6B,WAAWM,OAAS,EAAG,CAQzD,GAPInC,EAAS6B,WAAWM,OAAS,GAC/B5E,EAAO9F,KACL,qBAAqBuI,EAAS6B,WAAWM,qIAKzCC,mBAAmBpC,EAAS6B,WAAW,IACzC,MAAM,IAAIzG,QAER,iBAAA,mBAAmBiH,wBACjBrC,6CAEF,CACEA,aAIN,OAoFA,SAAUsC,QAAQtC,eACtB,MAAMuC,EAAc,GACpB,GAAoC,QAAhC1G,EAAmB,QAAnBC,EAAAkE,EAAS6B,kBAAU,IAAA/F,OAAA,EAAAA,EAAG,GAAG0G,eAAO,IAAA3G,OAAA,EAAAA,EAAE4G,MACpC,IAAK,MAAMC,KAA0C,QAAlCzG,EAAmB,QAAnBC,EAAA8D,EAAS6B,kBAAU,IAAA3F,OAAA,EAAAA,EAAG,GAAGsG,eAAS,IAAAvG,OAAA,EAAAA,EAAAwG,MAC/CC,EAAKR,MACPK,EAAY7C,KAAKgD,EAAKR,MAI5B,OAAIK,EAAYJ,OAAS,EAChBI,EAAY5C,KAAK,IAEjB,EAEX,CAlGa2C,CAAQtC,EAChB,CAAM,GAAIA,EAAS2C,eAClB,MAAM,IAAIvH,QAER,iBAAA,uBAAuBiH,wBAAwBrC,KAC/C,CACEA,aAIN,MAAO,EAAE,EAEVA,EAA6C4C,gBAAkB,KAG9D,GAAI5C,EAAS6B,YAAc7B,EAAS6B,WAAWM,OAAS,EAAG,CAQzD,GAPInC,EAAS6B,WAAWM,OAAS,GAC/B5E,EAAO9F,KACL,qBAAqBuI,EAAS6B,WAAWM,qIAKzCC,mBAAmBpC,EAAS6B,WAAW,IACzC,MAAM,IAAIzG,QAER,iBAAA,mBAAmBiH,wBACjBrC,6CAEF,CACEA,aAIN,OA4FA,SAAU6C,mBACd7C,eAEA,MAAM1K,EAAyB,GAE/B,GAAoC,QAAhCuG,EAAmB,QAAnBC,EAAAkE,EAAS6B,kBAAU,IAAA/F,OAAA,EAAAA,EAAG,GAAG0G,eAAO,IAAA3G,OAAA,EAAAA,EAAE4G,MACpC,IAAK,MAAMC,KAA0C,QAAlCzG,EAAmB,QAAnBC,EAAA8D,EAAS6B,kBAAU,IAAA3F,OAAA,EAAAA,EAAG,GAAGsG,eAAS,IAAAvG,OAAA,EAAAA,EAAAwG,MAC/CC,EAAKI,YACPxN,EAAKoK,KAAKgD,GAKhB,OAAIpN,EAAK6M,OAAS,EACT7M,OAEP,CAEJ,CA9GauN,CAAmB7C,EAC3B,CAAM,GAAIA,EAAS2C,eAClB,MAAM,IAAIvH,QAER,iBAAA,uBAAuBiH,wBAAwBrC,KAC/C,CACEA,YAIU,EAEjBA,EAA6C+C,cAAgB,KAC5D,GAAI/C,EAAS6B,YAAc7B,EAAS6B,WAAWM,OAAS,EAAG,CAQzD,GAPInC,EAAS6B,WAAWM,OAAS,GAC/B5E,EAAO9F,KACL,qBAAqBuI,EAAS6B,WAAWM,+IAKzCC,mBAAmBpC,EAAS6B,WAAW,IACzC,MAAM,IAAIzG,QAER,iBAAA,mBAAmBiH,wBACjBrC,6CAEF,CACEA,aAIN,OAqCA,SAAUgD,iBACdhD,eAEA,MAAM+C,EAAgC,GACtC,GAAoC,QAAhClH,EAAmB,QAAnBC,EAAAkE,EAAS6B,kBAAU,IAAA/F,OAAA,EAAAA,EAAG,GAAG0G,eAAO,IAAA3G,OAAA,EAAAA,EAAE4G,MACpC,IAAK,MAAMC,KAA0C,QAAlCzG,EAAmB,QAAnBC,EAAA8D,EAAS6B,kBAAU,IAAA3F,OAAA,EAAAA,EAAG,GAAGsG,eAAS,IAAAvG,OAAA,EAAAA,EAAAwG,MAC/CC,EAAKO,cACPF,EAAcrD,KAAKgD,EAAKO,cAI9B,OAAIF,EAAcZ,OAAS,EAClBY,OAEP,CAEJ,CArDaC,CAAiBhD,EACzB,CAAM,GAAIA,EAAS2C,eAClB,MAAM,IAAIvH,QAER,iBAAA,gCAAgCiH,wBAAwBrC,KACxD,CACEA,YAIU,EAEXA,CACT,CA9G8BiC,CAAWjC,GACvC,OAAOgC,CACT,CA+KA,MAAMkB,EAAmB,CAAC5J,EAAa6J,WAAY7J,EAAa8J,QAEhE,SAAShB,mBAAmBiB,GAC1B,QACIA,EAAUC,cACZJ,EAAiB7F,SAASgG,EAAUC,aAExC,CAEM,SAAUjB,wBACdrC,aAEA,IAAIxL,EAAU,GACd,GACIwL,EAAS6B,YAA6C,IAA/B7B,EAAS6B,WAAWM,SAC7CnC,EAAS2C,gBASJ,GAAuB,QAAnBzG,EAAA8D,EAAS6B,kBAAU,IAAA3F,OAAA,EAAAA,EAAG,GAAI,CACnC,MAAMqH,EAAiBvD,EAAS6B,WAAW,GACvCO,mBAAmBmB,KACrB/O,GAAW,gCAAgC+O,EAAeD,eACtDC,EAAeC,gBACjBhP,GAAW,KAAK+O,EAAeC,iBAGpC,OAfChP,GAAW,wBACkB,QAAzBsH,EAAAkE,EAAS2C,sBAAgB,IAAA7G,OAAA,EAAAA,EAAA2H,eAC3BjP,GAAW,WAAWwL,EAAS2C,eAAec,gBAEnB,QAAzB5H,EAAAmE,EAAS2C,sBAAgB,IAAA9G,OAAA,EAAAA,EAAA6H,sBAC3BlP,GAAW,KAAKwL,EAAS2C,eAAee,sBAW5C,OAAOlP,CACT,CASO2K,eAAewE,sBAEpB3D,SACA,MAAM4D,QAA6C5D,EAASe,OAEtD8C,EAAc,GACpB,IAAIC,EAGJ,IAAKF,EAAaG,aAAoD,KAAX,QAA1BjI,EAAA8H,EAAaG,mBAAa,IAAAjI,OAAA,EAAAA,EAAAqG,QACzD,MAAM,IAAI/G,QAER,iBAAA,0KAIJ,IAAK,MAAM4I,KAAcJ,EAAaG,YACpC,GAAIC,EAAWC,kBACbH,EAAiBE,EAAWC,uBACvB,GAAID,EAAWE,UAAYF,EAAWG,mBAC3CN,EAAOnE,KAAK,CACVwE,SAAUF,EAAWE,SACrBC,mBAAoBH,EAAWG,yBAE5B,KAAIH,EAAWE,WAAYF,EAAWI,OAM3C,MAAM,IAAIhJ,QAER,iBAAA,mEAAmEI,KAAKC,UACtEmI,MARJC,EAAOnE,KAAK,CACVwE,SAAUF,EAAWE,SACrBG,OAAQL,EAAWI,QAStB,CAGH,MAAO,CAAEP,SAAQC,iBACnB,CC1PM,SAAUQ,0BACdC,WAWA,GATqC,QAArCzI,EAAAyI,EAAuBC,sBAAc,IAAA1I,GAAAA,EAAE2I,SAAQC,IAC7C,GAAIA,EAAcjM,OAChB,MAAM,IAAI2C,QAER,cAAA,sGAEH,IAG0C,QAAzCS,EAAA0I,EAAuBI,wBAAkB,IAAA9I,OAAA,EAAAA,EAAA+I,KAAM,CACjD,MAAMC,EAAcC,KAAKC,MACvBR,EAAuBI,iBAAiBC,MAGtCC,IAAgBN,EAAuBI,iBAAiBC,OAC1DrH,EAAO9F,KACL,kIAEF8M,EAAuBI,iBAAiBC,KAAOC,EAElD,CAED,OAAON,CACT,CAWM,SAAUS,2BACdC,GAYA,MAVgC,CAC9BpD,WAAYoD,EAAiBpD,WACzBqD,6BAA6BD,EAAiBpD,iBAC9CsD,EACJC,OAAQH,EAAiBtC,eACrB0C,kBAAkBJ,EAAiBtC,qBACnCwC,EACJG,cAAeL,EAAiBK,cAIpC,CAoCM,SAAUJ,6BACdrD,GAEA,MAAM0D,EAA+C,GACrD,IAAIC,EAmDJ,OAlDID,GACF1D,EAAW4C,SAAQpB,UAEjB,IAAIoC,EAuBJ,GAtBIpC,EAAUoC,mBACZA,EAAmB,CACjBC,UAAWrC,EAAUoC,iBAAiBE,kBAKtCtC,EAAUuC,gBACZJ,EAAsBnC,EAAUuC,cAAcC,KAAIC,cAChD,OAAAjR,OAAAkR,OAAAlR,OAAAkR,OAAA,CAAA,EACKD,GAAY,CACfE,SAC2B,QAAzBlK,EAAAgK,EAAaE,gBAAY,IAAAlK,EAAAA,EAAA1C,EAAa6M,0BACxCC,iBAAmD,QAAjCrK,EAAAiK,EAAaI,wBAAoB,IAAArK,EAAAA,EAAA,EACnDsK,cAAyC,QAA1BjK,EAAA4J,EAAaK,qBAAa,IAAAjK,EAAAA,EAAI,GAC7C,KAQa,QAAjBJ,EAAAuH,EAAUb,eAAO,IAAA1G,OAAA,EAAAA,EAAE2G,MAAMtB,MACvBuB,GAASA,aAAI,EAAJA,EAAyB0D,gBAGpC,MAAM,IAAIhL,QAER,cAAA,iGAIJ,MAAMiL,EAAkB,CACtBtE,MAAOsB,EAAUtB,MACjBS,QAASa,EAAUb,QACnBc,aAAcD,EAAUC,aACxBE,cAAeH,EAAUG,cACzBoC,cAAeJ,EACfC,mBACAa,kBAAmBjD,EAAUiD,mBAE/Bf,EAAiB7F,KAAK2G,EAAgB,IAInCd,CACT,CAEM,SAAUF,kBACd1C,GAGA,MAAM6C,EAAsC,GAC5C7C,EAAeiD,cAAcnB,SAAQqB,cACnCN,EAAoB9F,KAAK,CACvB6G,SAAUT,EAAaS,SACvBC,YAAaV,EAAaU,YAC1BR,SAAmC,QAAzBlK,EAAAgK,EAAaE,gBAAY,IAAAlK,EAAAA,EAAA1C,EAAa6M,0BAChDC,iBAA+C,QAA7BrK,EAAAiK,EAAaI,wBAAgB,IAAArK,EAAAA,EAAI,EACnDsK,cAAyC,QAA1BjK,EAAA4J,EAAaK,qBAAa,IAAAjK,EAAAA,EAAI,EAC7CuK,QAASX,EAAaW,SACtB,IAQJ,MAL6C,CAC3ChD,YAAad,EAAec,YAC5BmC,cAAeJ,EACf9B,mBAAoBf,EAAee,mBAGvC,CClMA,MAAMgD,EAAiB,qCAUP,SAAAC,cACd3G,EACA3B,GAEA,MAGMuI,EA8DF,SAAUC,kBACdC,GAEA,MAAMC,EAASD,EAAYE,YA0C3B,OAzCe,IAAIC,eAAkB,CACnC,KAAAC,CAAMC,GACJ,IAAIC,EAAc,GAClB,OAAOC,OACP,SAASA,OACP,OAAON,EAAOO,OAAOC,MAAK,EAAGzR,QAAO0R,WAClC,GAAIA,EACF,OAAIJ,EAAYK,YACdN,EAAWxP,MACT,IAAIyD,QAAkC,eAAA,gCAI1C+L,EAAWO,QAIbN,GAAetR,EACf,IACI6R,EADAC,EAAQR,EAAYQ,MAAMlB,GAE9B,KAAOkB,GAAO,CACZ,IACED,EAAiBnM,KAAKqM,MAAMD,EAAM,GACnC,CAAC,MAAO3G,GAOP,YANAkG,EAAWxP,MACT,IAAIyD,QAEF,eAAA,iCAAiCwM,EAAM,MAI5C,CACDT,EAAWW,QAAQH,GACnBP,EAAcA,EAAYW,UAAUH,EAAM,GAAGzF,QAC7CyF,EAAQR,EAAYQ,MAAMlB,EAC3B,CACD,OAAOW,MAAM,GAEhB,CACF,GAGL,CA3GIR,CAJkB7G,EAASD,KAAMiI,YACjC,IAAIC,kBAAkB,OAAQ,CAAEC,OAAO,OAIlCC,EAASC,GAAWxB,EAAeyB,MAC1C,MAAO,CACL/J,OAAQgK,yBAAyBH,EAAS9J,GAC1C2B,SAAUuI,mBAAmBH,EAAS/J,GAE1C,CAEAc,eAAeoJ,mBACbjK,EACAD,GAEA,MAAMmK,EAA0C,GAC1CzB,EAASzI,EAAO0I,YACtB,OAAa,CACX,MAAMQ,KAAEA,EAAI1R,MAAEA,SAAgBiR,EAAOO,OACrC,GAAIE,EAAM,CACR,IAAIiB,EAA0BC,mBAAmBF,GAMjD,OALInK,EAAY3D,QAAQN,cAAgBJ,EAAYE,YAClDuO,EAA0BE,2BACxBF,IAGG7G,8BAA8B6G,EACtC,CAEDD,EAAa9I,KAAK5J,EACnB,CACH,CAEA,SAAgBwS,yBACdhK,EACAD,iFAEA,MAAM0I,EAASzI,EAAO0I,YACtB,OAAa,CACX,MAAMlR,MAAEA,EAAK0R,KAAEA,SAAeoB,QAAA7B,EAAOO,QACrC,GAAIE,EACF,MAGF,IAAIqB,EAEFA,EADExK,EAAY3D,QAAQN,cAAgBJ,EAAYE,UAC/B0H,8BACjB+G,2BACE7S,IAIe8L,8BAA8B9L,eAG7C8S,QAAAC,EACP,CACF,GAAA,CA2DK,SAAUH,mBACdI,GAEA,MAAMC,EAAeD,EAAUA,EAAU3G,OAAS,GAC5C6G,EAA8C,CAClDrG,eAAgBoG,aAAA,EAAAA,EAAcpG,gBAEhC,IAAK,MAAM3C,KAAY8I,EACrB,GAAI9I,EAAS6B,WACX,IAAK,MAAMwB,KAAarD,EAAS6B,WAAY,CAG3C,MAAMoH,EAAI5F,EAAUtB,OAAS,EAsB7B,GArBKiH,EAAmBnH,aACtBmH,EAAmBnH,WAAa,IAE7BmH,EAAmBnH,WAAWoH,KACjCD,EAAmBnH,WAAWoH,GAAK,CACjClH,MAAOsB,EAAUtB,QAIrBiH,EAAmBnH,WAAWoH,GAAGxD,iBAC/BpC,EAAUoC,iBACZuD,EAAmBnH,WAAWoH,GAAG3F,aAAeD,EAAUC,aAC1D0F,EAAmBnH,WAAWoH,GAAGzF,cAC/BH,EAAUG,cACZwF,EAAmBnH,WAAWoH,GAAGrD,cAC/BvC,EAAUuC,cAMRvC,EAAUb,SAAWa,EAAUb,QAAQC,MAAO,CAC3CuG,EAAmBnH,WAAWoH,GAAGzG,UACpCwG,EAAmBnH,WAAWoH,GAAGzG,QAAU,CACzC0G,KAAM7F,EAAUb,QAAQ0G,MAAQ,OAChCzG,MAAO,KAGX,MAAM0G,EAAyB,CAAA,EAC/B,IAAK,MAAMzG,KAAQW,EAAUb,QAAQC,MAAO,CAC1C,QAAkB0C,IAAdzC,EAAKR,KAAoB,CAI3B,GAAkB,KAAdQ,EAAKR,KACP,SAEFiH,EAAQjH,KAAOQ,EAAKR,IACrB,CAID,GAHIQ,EAAKO,eACPkG,EAAQlG,aAAeP,EAAKO,cAEM,IAAhCpO,OAAOuU,KAAKD,GAAShH,OACvB,MAAM,IAAI/G,QAAO,kBAEf,+HAIJ4N,EAAmBnH,WAAWoH,GAAGzG,QAAQC,MAAM/C,KAC7CyJ,EAEH,CACF,CACF,CAGL,OAAOH,CACT,CC3MO7J,eAAekK,sBACpBhL,EACApB,EACA+B,EACAT,GAEIF,EAAY3D,QAAQN,cAAgBJ,EAAYE,YAClD8E,EAASsK,0BAAyCtK,IAUpD,OAAO2H,oBARgB7G,YACrB7C,EACAiB,EAAKqL,wBACLlL,GACa,EACb7C,KAAKC,UAAUuD,GACfT,GAE6BF,EACjC,CAEOc,eAAeqK,gBACpBnL,EACApB,EACA+B,EACAT,GAEIF,EAAY3D,QAAQN,cAAgBJ,EAAYE,YAClD8E,EAASsK,0BAAyCtK,IAEpD,MAAMgB,QAAiBF,YACrB7C,EACAiB,EAAKuL,iBACLpL,GACa,EACb7C,KAAKC,UAAUuD,GACfT,GAEIkK,QAYRtJ,eAAeuK,+BACb1J,EACA3B,GAEA,MAAMuF,QAAqB5D,EAASe,OACpC,OAAI1C,EAAY3D,QAAQN,cAAgBJ,EAAYE,UAC3CyO,2BAA0C/E,GAE1CA,CAEX,CAtBwC8F,CACpC1J,EACA3B,GAKF,MAAO,CACL2B,SAJuB4B,8BACvB6G,GAKJ,CCzDM,SAAUkB,wBACdC,GAGA,GAAa,MAATA,EAEG,MAAqB,iBAAVA,EACT,CAAEV,KAAM,SAAUzG,MAAO,CAAC,CAAEP,KAAM0H,KAC/BA,EAAe1H,KAClB,CAAEgH,KAAM,SAAUzG,MAAO,CAACmH,IACvBA,EAAkBnH,MACtBmH,EAAkBV,KAGfU,EAFA,CAAEV,KAAM,SAAUzG,MAAQmH,EAAkBnH,YAFhD,CAOT,CAEM,SAAUoH,iBACd3J,GAEA,IAAI4J,EAAmB,GACvB,GAAuB,iBAAZ5J,EACT4J,EAAW,CAAC,CAAE5H,KAAMhC,SAEpB,IAAK,MAAM6J,KAAgB7J,EACG,iBAAjB6J,EACTD,EAASpK,KAAK,CAAEwC,KAAM6H,IAEtBD,EAASpK,KAAKqK,GAIpB,OAWF,SAASC,+CACPvH,GAEA,MAAMwH,EAAuB,CAAEf,KAAM,OAAQzG,MAAO,IAC9CyH,EAA2B,CAAEhB,KAAM,WAAYzG,MAAO,IAC5D,IAAI0H,GAAiB,EACjBC,GAAqB,EACzB,IAAK,MAAM1H,KAAQD,EACb,qBAAsBC,GACxBwH,EAAgBzH,MAAM/C,KAAKgD,GAC3B0H,GAAqB,IAErBH,EAAYxH,MAAM/C,KAAKgD,GACvByH,GAAiB,GAIrB,GAAIA,GAAkBC,EACpB,MAAM,IAAIhP,QAER,kBAAA,8HAIJ,IAAK+O,IAAmBC,EACtB,MAAM,IAAIhP,QAER,kBAAA,oDAIJ,GAAI+O,EACF,OAAOF,EAGT,OAAOC,CACT,CA/CSF,CAA+CF,EACxD,CAgDM,SAAUO,2BACdrL,GAEA,IAAIsL,EACJ,GAAKtL,EAAkCuL,SACrCD,EAAmBtL,MACd,CAGLsL,EAAmB,CAAEC,SAAU,CADfV,iBAAiB7K,IAElC,CAMD,OALKA,EAAkCwL,oBACrCF,EAAiBE,kBAAoBb,wBAClC3K,EAAkCwL,oBAGhCF,CACT,CAQM,SAAUG,yBACdrF,GACAf,OACEA,EAAMqG,YACNA,EAAWC,aACXA,EAAYC,eACZA,EAAiB,EAACC,eAClBA,EAAcC,YACdA,EAAWC,kBACXA,EAAiBC,kBACjBA,IAsBF,MAlBiC,CAC/BC,UAAW,CACT,CACE7F,WAGJ8F,WAAY,CACVC,WAAY9G,EACZwG,iBACAO,YAAaR,EACbE,cACAO,cAAeX,EACfC,eACAI,oBACAO,iBAAkBN,EAClBO,kBAAkB,GAIxB,CC5IA,MAAMC,EAAuC,CAC3C,OACA,aACA,eACA,oBAGIC,EAA6D,CACjEC,KAAM,CAAC,OAAQ,cACfC,SAAU,CAAC,oBACX1O,MAAO,CAAC,OAAQ,gBAEhB2O,OAAQ,CAAC,SAGLC,EAA0D,CAC9DH,KAAM,CAAC,SACPC,SAAU,CAAC,SACX1O,MAAO,CAAC,OAAQ,YAEhB2O,OAAQ,ICNV,MAAME,EAAe,eAQR,MAAAC,YAKX,WAAAzX,CACE+J,EACOpB,EACA+B,EACAT,GAFA5J,KAAKsI,MAALA,EACAtI,KAAMqK,OAANA,EACArK,KAAc4J,eAAdA,EAPD5J,KAAQqX,SAAc,GACtBrX,KAAAsX,aAA8B/Q,QAAQC,UAQ5CxG,KAAK4H,aAAe8B,GAChBW,aAAM,EAANA,EAAQkN,YDXV,SAAUC,oBAAoBD,GAClC,IAAIE,EAA8B,KAClC,IAAK,MAAMC,KAAeH,EAAS,CACjC,MAAMhD,KAAEA,EAAIzG,MAAEA,GAAU4J,EACxB,IAAKD,GAAwB,SAATlD,EAClB,MAAM,IAAI9N,QAAO,kBAEf,iDAAiD8N,KAGrD,IAAKnQ,EAAesE,SAAS6L,GAC3B,MAAM,IAAI9N,QAER,kBAAA,4CAA4C8N,0BAA6B1N,KAAKC,UAC5E1C,MAKN,IAAKuT,MAAMC,QAAQ9J,GACjB,MAAM,IAAIrH,QAER,kBAAA,mEAIJ,GAAqB,IAAjBqH,EAAMN,OACR,MAAM,IAAI/G,QAER,kBAAA,8CAIJ,MAAMoR,EAA0C,CAC9CtK,KAAM,EACNY,WAAY,EACZG,aAAc,EACdwJ,iBAAkB,GAGpB,IAAK,MAAM/J,KAAQD,EACjB,IAAK,MAAM5M,KAAO2V,EACZ3V,KAAO6M,IACT8J,EAAY3W,IAAQ,GAI1B,MAAM6W,EAAajB,EAAqBvC,GACxC,IAAK,MAAMrT,KAAO2V,EAChB,IAAKkB,EAAWrP,SAASxH,IAAQ2W,EAAY3W,GAAO,EAClD,MAAM,IAAIuF,QAER,kBAAA,sBAAsB8N,qBAAwBrT,WAKpD,GAAIuW,IACgCP,EAA6B3C,GAChC7L,SAAS+O,EAAYlD,MAClD,MAAM,IAAI9N,QAAO,kBAEf,sBAAsB8N,oBACpBkD,EAAYlD,gCACc1N,KAAKC,UAC/BoQ,MAKRO,EAAcC,CACf,CACH,CC5DMF,CAAoBnN,EAAOkN,SAC3BvX,KAAKqX,SAAWhN,EAAOkN,QAE1B,CAOD,gBAAMS,GAEJ,aADMhY,KAAKsX,aACJtX,KAAKqX,QACb,CAMD,iBAAMY,CACJ1M,uBAEMvL,KAAKsX,aACX,MAAMY,EAAahD,iBAAiB3J,GAC9BqE,EAAiD,CACrDC,eAA2B,QAAX1I,EAAAnH,KAAKqK,cAAM,IAAAlD,OAAA,EAAAA,EAAE0I,eAC7BG,iBAA6B,QAAX9I,EAAAlH,KAAKqK,cAAM,IAAAnD,OAAA,EAAAA,EAAE8I,iBAC/BmI,MAAkB,QAAX5Q,EAAAvH,KAAKqK,cAAM,IAAA9C,OAAA,EAAAA,EAAE4Q,MACpBC,WAAuB,QAAX9Q,EAAAtH,KAAKqK,cAAM,IAAA/C,OAAA,EAAAA,EAAE8Q,WACzBvC,kBAA8B,QAAXnO,EAAA1H,KAAKqK,cAAM,IAAA3C,OAAA,EAAAA,EAAEmO,kBAChCD,SAAU,IAAI5V,KAAKqX,SAAUa,IAE/B,IAAIG,EAAc,CAAA,EAkClB,OAhCArY,KAAKsX,aAAetX,KAAKsX,aACtB1E,MAAK,IACJiC,gBACE7U,KAAK4H,aACL5H,KAAKsI,MACLsH,EACA5P,KAAK4J,kBAGRgJ,MAAK0F,YACJ,GACEA,EAAOjN,SAAS6B,YAChBoL,EAAOjN,SAAS6B,WAAWM,OAAS,EACpC,CACAxN,KAAKqX,SAAStM,KAAKmN,GACnB,MAAMK,EAA2B,CAC/BzK,OAAiC,QAA1B3G,EAAAmR,EAAOjN,SAAS6B,kBAAU,IAAA/F,OAAA,EAAAA,EAAG,GAAG0G,QAAQC,QAAS,GAExDyG,MAAgC,QAA1BrN,EAAAoR,EAAOjN,SAAS6B,kBAAU,IAAAhG,OAAA,EAAAA,EAAG,GAAG2G,QAAQ0G,OAAQ,SAExDvU,KAAKqX,SAAStM,KAAKwN,EACpB,KAAM,CACL,MAAMC,EAAoB9K,wBAAwB4K,EAAOjN,UACrDmN,GACF5P,EAAO9F,KACL,mCAAmC0V,0CAGxC,CACDH,EAAcC,CAAM,UAElBtY,KAAKsX,aACJe,CACR,CAOD,uBAAMI,CACJlN,uBAEMvL,KAAKsX,aACX,MAAMY,EAAahD,iBAAiB3J,GAC9BqE,EAAiD,CACrDC,eAA2B,QAAX1I,EAAAnH,KAAKqK,cAAM,IAAAlD,OAAA,EAAAA,EAAE0I,eAC7BG,iBAA6B,QAAX9I,EAAAlH,KAAKqK,cAAM,IAAAnD,OAAA,EAAAA,EAAE8I,iBAC/BmI,MAAkB,QAAX5Q,EAAAvH,KAAKqK,cAAM,IAAA9C,OAAA,EAAAA,EAAE4Q,MACpBC,WAAuB,QAAX9Q,EAAAtH,KAAKqK,cAAM,IAAA/C,OAAA,EAAAA,EAAE8Q,WACzBvC,kBAA8B,QAAXnO,EAAA1H,KAAKqK,cAAM,IAAA3C,OAAA,EAAAA,EAAEmO,kBAChCD,SAAU,IAAI5V,KAAKqX,SAAUa,IAEzBQ,EAAgBhE,sBACpB1U,KAAK4H,aACL5H,KAAKsI,MACLsH,EACA5P,KAAK4J,gBAwCP,OApCA5J,KAAKsX,aAAetX,KAAKsX,aACtB1E,MAAK,IAAM8F,IAGXC,OAAMC,IACL,MAAM,IAAIlZ,MAAMyX,EAAa,IAE9BvE,MAAKiG,GAAgBA,EAAaxN,WAClCuH,MAAKvH,IACJ,GAAIA,EAAS6B,YAAc7B,EAAS6B,WAAWM,OAAS,EAAG,CACzDxN,KAAKqX,SAAStM,KAAKmN,GACnB,MAAMK,EAAuBrY,OAAAkR,OAAA,CAAA,EAAA/F,EAAS6B,WAAW,GAAGW,SAE/C0K,EAAgBhE,OACnBgE,EAAgBhE,KAAO,SAEzBvU,KAAKqX,SAAStM,KAAKwN,EACpB,KAAM,CACL,MAAMC,EAAoB9K,wBAAwBrC,GAC9CmN,GACF5P,EAAO9F,KACL,yCAAyC0V,0CAG9C,KAEFG,OAAMrM,IAIDA,EAAEzM,UAAYsX,GAGhBvO,EAAO5F,MAAMsJ,EACd,IAEEoM,CACR,EClKIlO,eAAesO,YACpBpP,EACApB,EACA+B,EACAT,GAEA,IAAIwB,EAAe,GACnB,GAAI1B,EAAY3D,QAAQN,cAAgBJ,EAAYE,UAAW,CAC7D,MAAMwT,ENsFM,SAAAC,sBACdC,EACA3Q,GASA,MAP6D,CAC3DsH,uBACE1P,OAAAkR,OAAA,CAAA9I,SACG2Q,GAKT,CMlGyBC,CAAqC7O,EAAQ/B,GAClE8C,EAAOvE,KAAKC,UAAUiS,EACvB,MACC3N,EAAOvE,KAAKC,UAAUuD,GAUxB,aARuBc,YACrB7C,EACAiB,EAAK4P,aACLzP,GACA,EACA0B,EACAxB,IAEcwC,MAClB,CCCM,MAAOgN,wBAAwBrS,QAQnC,WAAApH,CACEqH,EACAqS,EACAzP,GAEA7J,MAAMiH,EAAIqS,EAAY/Q,OACtBtI,KAAKgQ,iBAAmBqJ,EAAYrJ,kBAAoB,CAAA,EACxDhQ,KAAK6P,eAAiBwJ,EAAYxJ,gBAAkB,GACpD7P,KAAKmY,MAAQkB,EAAYlB,MACzBnY,KAAKoY,WAAaiB,EAAYjB,WAC9BpY,KAAK6V,kBAAoBb,wBACvBqE,EAAYxD,mBAEd7V,KAAK4J,eAAiBA,GAAkB,EACzC,CAMD,qBAAMiL,CACJtJ,GAEA,MAAM+N,EAAkB5D,2BAA2BnK,GACnD,OAAOsJ,gBACL7U,KAAK4H,aACL5H,KAAKsI,MAAKpI,OAAAkR,OAAA,CAERpB,iBAAkBhQ,KAAKgQ,iBACvBH,eAAgB7P,KAAK6P,eACrBsI,MAAOnY,KAAKmY,MACZC,WAAYpY,KAAKoY,WACjBvC,kBAAmB7V,KAAK6V,mBACrByD,GAELtZ,KAAK4J,eAER,CAQD,2BAAM8K,CACJnJ,GAEA,MAAM+N,EAAkB5D,2BAA2BnK,GACnD,OAAOmJ,sBACL1U,KAAK4H,aACL5H,KAAKsI,MAAKpI,OAAAkR,OAAA,CAERpB,iBAAkBhQ,KAAKgQ,iBACvBH,eAAgB7P,KAAK6P,eACrBsI,MAAOnY,KAAKmY,MACZC,WAAYpY,KAAKoY,WACjBvC,kBAAmB7V,KAAK6V,mBACrByD,GAELtZ,KAAK4J,eAER,CAMD,SAAA2P,CAAUC,GACR,OAAO,IAAIpC,YACTpX,KAAK4H,aACL5H,KAAKsI,MAAKpI,OAAAkR,OAAA,CAER+G,MAAOnY,KAAKmY,MACZC,WAAYpY,KAAKoY,WACjBvC,kBAAmB7V,KAAK6V,kBACxB7F,iBAAkBhQ,KAAKgQ,iBACvBH,eAAgB7P,KAAK6P,gBAMlB2J,GAELxZ,KAAK4J,eAER,CAKD,iBAAMkP,CACJvN,GAEA,MAAM+N,EAAkB5D,2BAA2BnK,GACnD,OAAOuN,YAAY9Y,KAAK4H,aAAc5H,KAAKsI,MAAOgR,EACnD,ECrGG,MAAOG,oBAAoB1S,QAoB/B,WAAApH,CACEqH,EACAqS,EACOzP,GAEP,MAAMtB,MAAEA,EAAK0H,iBAAEA,EAAgBH,eAAEA,GAAmBwJ,EACpDtZ,MAAMiH,EAAIsB,GAHHtI,KAAc4J,eAAdA,EAIP5J,KAAKgQ,iBAAmBA,EACxBhQ,KAAK6P,eAAiBA,CACvB,CAoBD,oBAAM6J,CACJjJ,GAEA,MAAMrF,EAAO0K,yBAAyBrF,EACjCvQ,OAAAkR,OAAAlR,OAAAkR,OAAA,CAAA,EAAApR,KAAKgQ,kBACLhQ,KAAK6P,iBAUV,OAAOb,4BARgB7D,YACrBnL,KAAKsI,MACLiB,EAAKoQ,QACL3Z,KAAK4H,cACQ,EACbf,KAAKC,UAAUsE,GACfpL,KAAK4J,gBAGR,CAqBD,uBAAMgQ,CACJnJ,EACAf,GAEA,MAAMtE,EAAO0K,yBAAyBrF,+BACpCf,UACG1P,KAAKgQ,kBACLhQ,KAAK6P,iBAUV,OAAOb,4BARgB7D,YACrBnL,KAAKsI,MACLiB,EAAKoQ,QACL3Z,KAAK4H,cACQ,EACbf,KAAKC,UAAUsE,GACfpL,KAAK4J,gBAGR,EC5HmB,MAAAiQ,OAiCpB,WAAAla,CAAYma,GAEV,IAAK,MAAMC,KAAYD,EACrB9Z,KAAK+Z,GAAYD,EAAaC,GAGhC/Z,KAAK0B,KAAOoY,EAAapY,KACzB1B,KAAKga,WAAWF,EAAa3M,eAAe,eACtC2M,EAAaE,QAEpB,CAOD,MAAAC,GACE,MAAMC,EAAoD,CACxDxY,KAAM1B,KAAK0B,MAEb,IAAK,MAAMyY,KAAQna,KACbA,KAAKmN,eAAegN,SAAwB3J,IAAfxQ,KAAKma,KACvB,aAATA,GAAuBna,KAAK0B,OAASuD,EAAWmV,SAClDF,EAAIC,GAAQna,KAAKma,KAIvB,OAAOD,CACR,CAED,YAAOG,CAAMC,GACX,OAAO,IAAIC,YAAYD,EAAaA,EAAYE,MACjD,CAED,aAAOC,CACLC,GAOA,OAAO,IAAIC,aACTD,EACAA,EAAaE,WACbF,EAAaG,mBAEhB,CAGD,aAAOC,CAAOC,GACZ,OAAO,IAAIC,aAAaD,EACzB,CAED,iBAAOE,CACLF,GAEA,OAAO,IAAIC,aAAaD,EAAcA,EAAaG,KACpD,CAED,cAAOC,CAAQC,GACb,OAAO,IAAIC,cAAcD,EAC1B,CAGD,aAAOE,CAAOC,GACZ,OAAO,IAAIC,aAAaD,EACzB,CAGD,cAAOE,CAAQC,GACb,OAAO,IAAIC,cAAcD,EAC1B,EAmBG,MAAOL,sBAAsBxB,OACjC,WAAAla,CAAYma,GACV/Z,MAAKG,OAAAkR,OAAA,CACH1P,KAAMuD,EAAW2W,SACd9B,GAEN,EAOG,MAAO0B,qBAAqB3B,OAChC,WAAAla,CAAYma,GACV/Z,MAAKG,OAAAkR,OAAA,CACH1P,KAAMuD,EAAW4W,QACd/B,GAEN,EAOG,MAAO6B,sBAAsB9B,OACjC,WAAAla,CAAYma,GACV/Z,MAAKG,OAAAkR,OAAA,CACH1P,KAAMuD,EAAW6W,SACdhC,GAEN,EAQG,MAAOkB,qBAAqBnB,OAEhC,WAAAla,CAAYma,EAA6BiC,GACvChc,MAAKG,OAAAkR,OAAA,CACH1P,KAAMuD,EAAW+W,QACdlC,IAEL9Z,KAAKkb,KAAOa,CACb,CAKD,MAAA9B,GACE,MAAMC,EAAMna,MAAMka,SAIlB,OAHIja,KAAKkb,OACPhB,EAAU,KAAIla,KAAKkb,MAEdhB,CACR,EASG,MAAOK,oBAAoBV,OAC/B,WAAAla,CAAYma,EAAmCU,GAC7Cza,MAAKG,OAAAkR,OAAA,CACH1P,KAAMuD,EAAWgX,OACdnC,IAHwC9Z,KAAKwa,MAALA,CAK9C,CAKD,MAAAP,GACE,MAAMC,EAAMna,MAAMka,SAElB,OADAC,EAAIM,MAAQxa,KAAKwa,MAAMP,SAChBC,CACR,EAQG,MAAOS,qBAAqBd,OAChC,WAAAla,CACEma,EACOc,EAGAC,EAA+B,IAEtC9a,MAAKG,OAAAkR,OAAA,CACH1P,KAAMuD,EAAWmV,QACdN,IAPE9Z,KAAU4a,WAAVA,EAGA5a,KAAkB6a,mBAAlBA,CAMR,CAKD,MAAAZ,GACE,MAAMC,EAAMna,MAAMka,SAClBC,EAAIU,WAAU1a,OAAAkR,OAAA,CAAA,EAAQpR,KAAK4a,YAC3B,MAAMsB,EAAW,GACjB,GAAIlc,KAAK6a,mBACP,IAAK,MAAMsB,KAAenc,KAAK6a,mBAC7B,IAAK7a,KAAK4a,WAAWzN,eAAegP,GAClC,MAAM,IAAI1V,QAAO,iBAEf,aAAa0V,wDAKrB,IAAK,MAAMA,KAAenc,KAAK4a,WACzB5a,KAAK4a,WAAWzN,eAAegP,KACjCjC,EAAIU,WAAWuB,GAAenc,KAAK4a,WACjCuB,GACAlC,SACGja,KAAK6a,mBAAmBnS,SAASyT,IACpCD,EAASnR,KAAKoR,IAQpB,OAJID,EAAS1O,OAAS,IACpB0M,EAAIgC,SAAWA,UAEThC,EAA8BW,mBAC/BX,CACR,ECpQU,MAAAkC,kBAUX,WAAAzc,GACEK,KAAKuP,SAAW,WACjB,CAUD,WAAO8M,CAAKC,GASV,OAPEA,IACCA,EAAqB,GAAKA,EAAqB,MAEhD1T,EAAO9F,KACL,uCAAuCwZ,iDAGpC,CAAE/M,SAAU,aAAc+M,qBAClC,CASD,UAAOC,GACL,MAAO,CAAEhN,SAAU,YACpB,EC5BU,MAAAiN,EAAgBzV,QAWhB0V,EAAgBhW,QAsBb,SAAAiW,YACd5W,EAAmB6W,IACnBvV,GAEAtB,EAAMxE,mBAAmBwE,GAEzB,MAAM8W,EAA6BC,aAAa/W,EAAK9B,GAG/C8Y,EAAalW,yBADH,IAAIjB,gBAAgByB,aAAA,EAAAA,EAASxB,WAE7C,OAAOgX,EAAWzW,aAAa,CAC7B2W,cAEJ,CA8BgB,SAAAC,MACdjX,EAAmB6W,IACnBvV,EAAqB,CAAErB,QAAS,IAAIL,kBAEpCI,EAAMxE,mBAAmBwE,GAEzB,MAAM8W,EAA6BC,aAAa/W,EAAK9B,GAE/C8Y,EAAalW,yBAAyBQ,EAAQrB,SACpD,OAAO6W,EAAWzW,aAAa,CAC7B2W,cAEJ,CAQgB,SAAAE,mBACdhW,EACAqS,EACAzP,GAEA,IAAKyP,EAAY/Q,MACf,MAAM,IAAI7B,QAER,WAAA,sFAGJ,OAAO,IAAI2S,gBAAgBpS,EAAIqS,EAAazP,EAC9C,CAgBgB,SAAAqT,eACdjW,EACAqS,EACAzP,GAEA,IAAKyP,EAAY/Q,MACf,MAAM,IAAI7B,QAER,WAAA,kFAGJ,OAAO,IAAIgT,YAAYzS,EAAIqS,EAAazP,EAC1C,ECrJA,SAASsT,aACPC,EACE,IAAI3b,UACFwC,GACA,CAACoZ,GAAaC,yBACZ,IAAKA,EACH,MAAM,IAAI5W,QAER,QAAA,+CAIJ,MAAMV,EjBJR,SAAUuX,yBAAyBD,GACvC,MAAME,EAAkBF,EAAmBG,MAAM,KACjD,GAAID,EAAgB,KAAOvZ,EACzB,MAAM,IAAIyC,QAAO,QAEf,gDAAgD8W,EAAgB,OAIpE,OADoBA,EAAgB,IAElC,IAAK,WACH,MAAM3X,EAA+B2X,EAAgB,GACrD,IAAK3X,EACH,MAAM,IAAIa,QAAO,QAEf,kDAAkD4W,MAGtD,OAAO,IAAI1X,gBAAgBC,GAC7B,IAAK,WACH,OAAO,IAAIF,gBACb,QACE,MAAM,IAAIe,QAAO,QAEf,wCAAwC4W,MAGhD,CiBvBwBC,CAAyBD,GAGnCvX,EAAMsX,EAAUK,YAAY,OAAOtX,eACnCE,EAAO+W,EAAUK,YAAY,iBAC7BxX,EAAmBmX,EAAUK,YAAY,sBAC/C,OAAO,IAAI5X,UAAUC,EAAKC,EAASM,EAAMJ,EAAiB,aAG5DhE,sBAAqB,IAGzByb,EAAgBzd,EAAMkE,GAEtBuZ,EAAgBzd,EAAMkE,EAAS,UACjC,CAEA+Y", "preExistingComment": "firebase-vertexai.js.map"}