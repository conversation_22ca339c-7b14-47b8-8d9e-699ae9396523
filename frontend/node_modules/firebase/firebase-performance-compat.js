((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Mn,Ln){try{!(function(){function B(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t,j=B(Mn);class F extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,F.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,U.prototype.create)}}class U{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){var r,n=t[0]||{},i=this.service+"/"+e,a=this.errors[e],a=a?(r=n,a.replace(q,(e,t)=>{var n=r[t];return null!=n?String(n):`<${t}?>`})):"Error",a=this.serviceName+`: ${a} (${i}).`;return new F(i,a,n)}}let q=/\{\$([^}]+)}/g;class e{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}(I=t=t||{})[I.DEBUG=0]="DEBUG",I[I.VERBOSE=1]="VERBOSE",I[I.INFO=2]="INFO",I[I.WARN=3]="WARN",I[I.ERROR=4]="ERROR",I[I.SILENT=5]="SILENT";let x={debug:t.DEBUG,verbose:t.VERBOSE,info:t.INFO,warn:t.WARN,error:t.ERROR,silent:t.SILENT},H=t.INFO,V={[t.DEBUG]:"log",[t.VERBOSE]:"log",[t.INFO]:"info",[t.WARN]:"warn",[t.ERROR]:"error"},$=(e,t,...n)=>{if(!(t<e.logLevel)){var r=(new Date).toISOString(),i=V[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${r}]  ${e.name}:`,...n)}};function W(e){if("loading"===document.readyState)return"loading";var t=u();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"}function K(e,t){var n="";try{for(;e&&9!==e.nodeType;){var r=e,i=r.id?"#"+r.id:ne(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+i.length>(t||100)-1)return n||i;if(n=n?i+">"+n:i,r.id)break;e=r.parentNode}}catch(e){}return n}function z(i,e){var o,s,a,c;o=function(e){n={},(e=e).entries.length&&(t=e.entries.reduce(function(e,t){return e&&e.value>t.value?e:t}))&&t.sources&&t.sources.length&&(r=(r=t.sources).find(function(e){return e.node&&1===e.node.nodeType})||r[0])&&(n={largestShiftTarget:K(r.node),largestShiftTime:t.startTime,largestShiftValue:t.value,largestShiftSource:r,largestShiftEntry:t,loadState:W(t.startTime)});var t,n,r=Object.assign(e,{attribution:n});i(r)},s=e||{},a=se(function(){function e(e){e.forEach(function(e){var t,n;e.hadRecentInput||(t=i[0],n=i[i.length-1],r&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,i.push(e)):(r=e.value,i=[e]))}),r>n.value&&(n.value=r,n.entries=i,t())}var t,n=p("CLS",0),r=0,i=[],a=f("layout-shift",e);a&&(t=g(o,n,ge,s.reportAllChanges),m(function(){e(a.takeRecords()),t(!0)}),d(function(){n=p("CLS",r=0),t=g(o,n,ge,s.reportAllChanges),oe(function(){return t()})}),setTimeout(t,0))}),c=c||{},pe(function(){var t,n=de(),r=p("FCP"),i=f("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(i.disconnect(),e.startTime<n.firstHiddenTime)&&(r.value=Math.max(e.startTime-ae(),0),r.entries.push(e),t(!0))})});i&&(t=g(a,r,fe,c.reportAllChanges),d(function(e){r=p("FCP"),t=g(a,r,fe,c.reportAllChanges),oe(function(){r.value=performance.now()-e.timeStamp,t(!0)})}))})}function G(a,o){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(o=o||{},pe(function(){var e;ye();function t(t){Se(function(){t.forEach(_e);var e=Ee();e&&e.latency!==n.value&&(n.value=e.latency,n.entries=e.entries,i())})}var n=p("INP"),r=f("event",t,{durationThreshold:null!=(e=o.durationThreshold)?e:40}),i=g(a,n,Ie,o.reportAllChanges);r&&(r.observe({type:"first-input",buffered:!0}),m(function(){t(r.takeRecords()),i(!0)}),d(function(){we=be(),s.length=0,c.clear(),n=p("INP"),i=g(a,n,Ie,o.reportAllChanges)}))}))}function J(e){o=o.concat(e),Le()}function Z(){10<h.size&&h.forEach(function(e,t){c.has(t)||h.delete(t)});var n=s.map(function(e){return ke.get(e.entries[0])}),r=l.length-50;l=l.filter(function(e,t){return r<=t||n.includes(e)});for(var i=new Set,e=0;e<l.length;e++){var t=l[e];Ae(t.startTime,t.processingEnd).forEach(function(e){i.add(e)})}var a=o.length-1-50;o=o.filter(function(e,t){return e.startTime>Ce&&a<t||i.has(e)}),Me=-1}function X(c,e){ee=ee||f("long-animation-frame",J),G(function(e){t=(e=e).entries[0],s=ke.get(t),n=t.processingStart,r=s.processingEnd,s=s.entries.sort(function(e,t){return e.processingStart-t.processingStart}),i=Ae(t.startTime,r),a=(a=e.entries.find(function(e){return e.target}))&&a.target||h.get(t.interactionId),o=[t.startTime+t.duration,r].concat(i.map(function(e){return e.startTime+e.duration})),o=Math.max.apply(Math,o),a={interactionTarget:K(a),interactionTargetElement:a,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:o,processedEventEntries:s,longAnimationFrameEntries:i,inputDelay:n-t.startTime,processingDuration:r-n,presentationDelay:Math.max(o-r,0),loadState:W(t.startTime)};var t,n,r,i,a,o,s=Object.assign(e,{attribution:a});c(s)},e)}function Y(l,e){var o,s;o=function(e){s={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:(e=e).value},e.entries.length&&(t=u())&&(o=t.activationStart||0,r=(n=e.entries[e.entries.length-1]).url&&performance.getEntriesByType("resource").filter(function(e){return e.name===n.url})[0],c=Math.max(0,t.responseStart-o),i=Math.max(c,r?(r.requestStart||r.startTime)-o:0),a=Math.max(i,r?r.responseEnd-o:0),o=Math.max(a,n.startTime-o),s={element:K(n.element),timeToFirstByte:c,resourceLoadDelay:i-c,resourceLoadDuration:a-i,elementRenderDelay:o-a,navigationEntry:t,lcpEntry:n},n.url&&(s.url=n.url),r)&&(s.lcpResourceEntry=r);var t,n,r,i,a,o,s,c=Object.assign(e,{attribution:s});l(c)},s=e||{},pe(function(){function e(e){(e=s.reportAllChanges?e:e.slice(-1)).forEach(function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-ae(),0),i.entries=[e],t())})}var t,n,r=de(),i=p("LCP"),a=f("largest-contentful-paint",e);a&&(t=g(o,i,Ne,s.reportAllChanges),n=se(function(){Pe[i.id]||(e(a.takeRecords()),a.disconnect(),Pe[i.id]=!0,t(!0))}),["keydown","click"].forEach(function(e){addEventListener(e,function(){return Se(n)},{once:!0,capture:!0})}),m(n),d(function(e){i=p("LCP"),t=g(o,i,Ne,s.reportAllChanges),oe(function(){i.value=performance.now()-e.timeStamp,Pe[i.id]=!0,t(!0)})}))})}var Q,ee,te,u=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&0<e.responseStart&&e.responseStart<performance.now())return e},ne=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},re=-1,ie=function(){return re},d=function(t){addEventListener("pageshow",function(e){e.persisted&&(re=e.timeStamp,t(e))},!0)},ae=function(){var e=u();return e&&e.activationStart||0},p=function(e,t){var n=u(),r="navigate";return 0<=ie()?r="back-forward-cache":n&&(document.prerendering||0<ae()?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},f=function(e,t,n){try{var r;if(PerformanceObserver.supportedEntryTypes.includes(e))return(r=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})})).observe(Object.assign({type:e,buffered:!0},n||{})),r}catch(e){}},g=function(t,n,r,i){var a,o;return function(e){0<=n.value&&(e||i)&&((o=n.value-(a||0))||void 0===a)&&(a=n.value,n.delta=o,n.rating=(e=n.value)>r[1]?"poor":e>r[0]?"needs-improvement":"good",t(n))}},oe=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},m=function(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})},se=function(e){var t=!1;return function(){t||(e(),t=!0)}},n=-1,ce=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},r=function(e){"hidden"===document.visibilityState&&-1<n&&(n="visibilitychange"===e.type?e.timeStamp:0,ue())},le=function(){addEventListener("visibilitychange",r,!0),addEventListener("prerenderingchange",r,!0)},ue=function(){removeEventListener("visibilitychange",r,!0),removeEventListener("prerenderingchange",r,!0)},de=function(){return n<0&&(n=ce(),le(),d(function(){setTimeout(function(){n=ce(),le()},0)})),{get firstHiddenTime(){return n}}},pe=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},fe=[1800,3e3],ge=[.1,.25],me=0,he=1/0,i=0,ve=function(e){e.forEach(function(e){e.interactionId&&(he=Math.min(he,e.interactionId),i=Math.max(i,e.interactionId),me=i?(i-he)/7+1:0)})},be=function(){return Q?me:performance.interactionCount||0},ye=function(){"interactionCount"in performance||(Q=Q||f("event",ve,{type:"event",buffered:!0,durationThreshold:0}))},s=[],c=new Map,we=0,Ee=function(){var e=Math.min(s.length-1,Math.floor((be()-we)/50));return s[e]},Te=[],_e=function(t){var e,n;Te.forEach(function(e){return e(t)}),!t.interactionId&&"first-input"!==t.entryType||(n=s[s.length-1],((e=c.get(t.interactionId))||s.length<10||t.duration>n.latency)&&(e?t.duration>e.latency?(e.entries=[t],e.latency=t.duration):t.duration===e.latency&&t.startTime===e.entries[0].startTime&&e.entries.push(t):(n={id:t.interactionId,latency:t.duration,entries:[t]},c.set(n.id,n),s.push(n)),s.sort(function(e,t){return t.latency-e.latency}),10<s.length)&&s.splice(10).forEach(function(e){return c.delete(e.id)}))},Se=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=se(e),"hidden"===document.visibilityState?e():(n=t(e),m(e)),n},Ie=[200,500],o=[],l=[],Ce=0,ke=new WeakMap,h=new Map,Me=-1,Le=function(){Me<0&&(Me=Se(Z))},Ae=(Te.push(function(e){e.interactionId&&e.target&&!h.has(e.interactionId)&&h.set(e.interactionId,e.target)},function(e){var t,n=e.startTime+e.duration;Ce=Math.max(Ce,e.processingEnd);for(var r=l.length-1;0<=r;r--){var i=l[r];if(Math.abs(n-i.renderTime)<=8){(t=i).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:n,entries:[e]},l.push(t)),!e.interactionId&&"first-input"!==e.entryType||ke.set(e,t),Le()}),function(e,t){for(var n,r=[],i=0;n=o[i];i++)if(!(n.startTime+n.duration<e)){if(n.startTime>t)break;r.push(n)}return r}),Ne=[2500,4e3],Pe={};let Re=(t,e)=>e.some(e=>t instanceof e),Oe,De;let Be=new WeakMap,je=new WeakMap,Fe=new WeakMap,Ue=new WeakMap,qe=new WeakMap;let xe={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return je.get(e);if("objectStoreNames"===t)return e.objectStoreNames||Fe.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return v(e[t])},set(e,t,n){return e[t]=n,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function He(r){return r!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(De=De||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(r)?function(...e){return r.apply($e(this),e),v(Be.get(this))}:function(...e){return v(r.apply($e(this),e))}:function(e,...t){var n=r.call($e(this),e,...t);return Fe.set(n,e.sort?e.sort():[e]),v(n)}}function Ve(e){var a,t;return"function"==typeof e?He(e):(e instanceof IDBTransaction&&(a=e,je.has(a)||(t=new Promise((e,t)=>{let n=()=>{a.removeEventListener("complete",r),a.removeEventListener("error",i),a.removeEventListener("abort",i)},r=()=>{e(),n()},i=()=>{t(a.error||new DOMException("AbortError","AbortError")),n()};a.addEventListener("complete",r),a.addEventListener("error",i),a.addEventListener("abort",i)}),je.set(a,t))),Re(e,Oe=Oe||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,xe):e)}function v(e){var a,t;return e instanceof IDBRequest?(a=e,(t=new Promise((e,t)=>{let n=()=>{a.removeEventListener("success",r),a.removeEventListener("error",i)},r=()=>{e(v(a.result)),n()},i=()=>{t(a.error),n()};a.addEventListener("success",r),a.addEventListener("error",i)})).then(e=>{e instanceof IDBCursor&&Be.set(e,a)}).catch(()=>{}),qe.set(t,a),t):Ue.has(e)?Ue.get(e):((t=Ve(e))!==e&&(Ue.set(e,t),qe.set(t,e)),t)}let $e=e=>qe.get(e);let We=["get","getKey","getAll","getAllKeys","count"],Ke=["put","add","delete","clear"],ze=new Map;function Ge(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(ze.get(t))return ze.get(t);let i=t.replace(/FromIndex$/,""),a=t!==i,o=Ke.includes(i);var n;return i in(a?IDBIndex:IDBObjectStore).prototype&&(o||We.includes(i))?(n=async function(e,...t){var n=this.transaction(e,o?"readwrite":"readonly");let r=n.store;return a&&(r=r.index(t.shift())),(await Promise.all([r[i](...t),o&&n.done]))[0]},ze.set(t,n),n):void 0}}xe={...te=xe,get:(e,t,n)=>Ge(e,t)||te.get(e,t,n),has:(e,t)=>!!Ge(e,t)||te.has(e,t)};var a="@firebase/installations",Je="0.6.17";let Ze=1e4,Xe="w:"+Je,Ye="FIS_v2",Qe="https://firebaseinstallations.googleapis.com/v1",et=36e5;let b=new U("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function tt(e){return e instanceof F&&e.code.includes("request-failed")}function nt({projectId:e}){return Qe+`/projects/${e}/installations`}function rt(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function it(e,t){var n=(await t.json()).error;return b.create("request-failed",{requestName:e,serverCode:n.code,serverMessage:n.message,serverStatus:n.status})}function at({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function ot(e,{refreshToken:t}){var n=at(e);return n.append("Authorization",(e=t,Ye+" "+e)),n}async function st(e){var t=await e();return 500<=t.status&&t.status<600?e():t}function ct(t){return new Promise(e=>{setTimeout(e,t)})}let lt=/^[cdef][\w-]{21}$/,ut="";function dt(){try{var e=new Uint8Array(17),t=((self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16,(e=>btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_"))(e).substr(0,22));return lt.test(t)?t:ut}catch(e){return ut}}function y(e){return e.appName+"!"+e.appId}let pt=new Map;function ft(e,t){var n=y(e),e=(gt(n,t),n),n=(()=>(!w&&"BroadcastChannel"in self&&((w=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{gt(e.data.key,e.data.fid)}),w))();n&&n.postMessage({key:e,fid:t}),0===pt.size&&w&&(w.close(),w=null)}function gt(e,t){var n=pt.get(e);if(n)for(var r of n)r(t)}let w=null;let mt="firebase-installations-database",ht=1,E="firebase-installations-store",vt=null;function bt(){return vt=vt||((e,t,{blocked:n,upgrade:r,blocking:i,terminated:a})=>{let o=indexedDB.open(e,t);var s=v(o);return r&&o.addEventListener("upgradeneeded",e=>{r(v(o.result),e.oldVersion,e.newVersion,v(o.transaction),e)}),n&&o.addEventListener("blocked",e=>n(e.oldVersion,e.newVersion,e)),s.then(e=>{a&&e.addEventListener("close",()=>a()),i&&e.addEventListener("versionchange",e=>i(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s})(mt,ht,{upgrade:(e,t)=>{0===t&&e.createObjectStore(E)}})}async function T(e,t){var n=y(e),r=(await bt()).transaction(E,"readwrite"),i=r.objectStore(E),a=await i.get(n);return await i.put(t,n),await r.done,a&&a.fid===t.fid||ft(e,t.fid),t}async function yt(e){var t=y(e),n=(await bt()).transaction(E,"readwrite");await n.objectStore(E).delete(t),await n.done}async function _(e,t){var n=y(e),r=(await bt()).transaction(E,"readwrite"),i=r.objectStore(E),a=await i.get(n),o=t(a);return void 0===o?await i.delete(n):await i.put(o,n),await r.done,!o||a&&a.fid===o.fid||ft(e,o.fid),o}async function wt(n){let r;var e=await _(n.appConfig,e=>{var t=Tt(e||{fid:dt(),registrationStatus:0}),t=((e,t)=>{var n,r;return 0===t.registrationStatus?navigator.onLine?(n={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=(async(t,n)=>{try{var e=await(async({appConfig:e,heartbeatServiceProvider:t},{fid:n})=>{let r=nt(e);var i=at(e),a=((a=t.getImmediate({optional:!0}))&&(a=await a.getHeartbeatsHeader())&&i.append("x-firebase-client",a),{fid:n,authVersion:Ye,appId:e.appId,sdkVersion:Xe});let o={method:"POST",headers:i,body:JSON.stringify(a)};if((i=await st(()=>fetch(r,o))).ok)return{fid:(a=await i.json()).fid||n,registrationStatus:2,refreshToken:a.refreshToken,authToken:rt(a.authToken)};throw await it("Create Installation",i)})(t,n);return T(t.appConfig,e)}catch(e){throw tt(e)&&409===e.customData.serverCode?await yt(t.appConfig):await T(t.appConfig,{fid:n.fid,registrationStatus:0}),e}})(e,n),{installationEntry:n,registrationPromise:r}):(n=Promise.reject(b.create("app-offline")),{installationEntry:t,registrationPromise:n}):1===t.registrationStatus?{installationEntry:t,registrationPromise:(async e=>{let t=await Et(e.appConfig);for(;1===t.registrationStatus;)await ct(100),t=await Et(e.appConfig);var n,r;return 0!==t.registrationStatus?t:({installationEntry:n,registrationPromise:r}=await wt(e),r||n)})(e)}:{installationEntry:t}})(n,t);return r=t.registrationPromise,t.installationEntry});return e.fid===ut?{installationEntry:await r}:{installationEntry:e,registrationPromise:r}}function Et(e){return _(e,e=>{if(e)return Tt(e);throw b.create("installation-not-found")})}function Tt(e){var t;return 1===(t=e).registrationStatus&&t.registrationTime+Ze<Date.now()?{fid:e.fid,registrationStatus:0}:e}async function _t({appConfig:e,heartbeatServiceProvider:t},n){[i,a]=[e,n.fid];let r=nt(i)+`/${a}/authTokens:generate`;var i,a,o=ot(e,n),s=t.getImmediate({optional:!0}),s=(s&&(s=await s.getHeartbeatsHeader())&&o.append("x-firebase-client",s),{installation:{sdkVersion:Xe,appId:e.appId}});let c={method:"POST",headers:o,body:JSON.stringify(s)};o=await st(()=>fetch(r,c));if(o.ok)return rt(await o.json());throw await it("Generate Auth Token",o)}async function St(r,i=!1){let a;var e=await _(r.appConfig,e=>{if(!Ct(e))throw b.create("not-registered");var t,n=e.authToken;if(i||2!==(t=n).requestStatus||(e=>{var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+et})(t)){if(1===n.requestStatus)return a=(async(e,t)=>{let n=await It(e.appConfig);for(;1===n.authToken.requestStatus;)await ct(100),n=await It(e.appConfig);var r=n.authToken;return 0===r.requestStatus?St(e,t):r})(r,i),e;if(navigator.onLine)return t=e,n={requestStatus:1,requestTime:Date.now()},n=Object.assign(Object.assign({},t),{authToken:n}),a=(async(t,n)=>{try{var e=await _t(t,n),r=Object.assign(Object.assign({},n),{authToken:e});return await T(t.appConfig,r),e}catch(e){var i;throw!tt(e)||401!==e.customData.serverCode&&404!==e.customData.serverCode?(i=Object.assign(Object.assign({},n),{authToken:{requestStatus:0}}),await T(t.appConfig,i)):await yt(t.appConfig),e}})(r,n),n;throw b.create("app-offline")}return e});return a?await a:e.authToken}function It(e){return _(e,e=>{var t,n;if(Ct(e))return t=e.authToken,1===(n=t).requestStatus&&n.requestTime+Ze<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e;throw b.create("not-registered")})}function Ct(e){return void 0!==e&&2===e.registrationStatus}async function kt(e,t=!1){var n=e,r=(await(!(r=(await wt(n)).registrationPromise)||!await r),await St(n,t));return r.token}function Mt(e){return b.create("missing-app-config-values",{valueName:e})}let Lt="installations",At=e=>{var t=e.getProvider("app").getImmediate();return{app:t,appConfig:(e=>{if(!e||!e.options)throw Mt("App Configuration");if(!e.name)throw Mt("App Name");var t;for(t of["projectId","apiKey","appId"])if(!e.options[t])throw Mt(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}})(t),heartbeatServiceProvider:Ln._getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},Nt=e=>{var t=e.getProvider("app").getImmediate();let n=Ln._getProvider(t,Lt).getImmediate();return{getId:()=>(async e=>{var t=e,{installationEntry:n,registrationPromise:r}=await wt(t);return(r||St(t)).catch(console.error),n.fid})(n),getToken:e=>kt(n,e)}};Ln._registerComponent(new e(Lt,At,"PUBLIC")),Ln._registerComponent(new e("installations-internal",Nt,"PRIVATE")),Ln.registerVersion(a,Je),Ln.registerVersion(a,Je,"esm2017");let Pt="@firebase/performance",Rt="0.7.6",Ot=Rt,Dt="FB-PERF-TRACE-MEASURE",Bt="@firebase/performance/config",jt="@firebase/performance/configexpire";var S,I,a="Performance";let C=new U("performance",a,{"trace started":"Trace {$traceName} was started before.","trace stopped":"Trace {$traceName} is not running.","nonpositive trace startTime":"Trace {$traceName} startTime should be positive.","nonpositive trace duration":"Trace {$traceName} duration should be positive.","no window":"Window is not available.","no app id":"App id is not available.","no project id":"Project id is not available.","no api key":"Api key is not available.","invalid cc log":"Attempted to queue invalid cc event","FB not default":"Performance can only start when Firebase app instance is the default one.","RC response not ok":"RC response is not ok","invalid attribute name":"Attribute name {$attributeName} is invalid.","invalid attribute value":"Attribute value {$attributeValue} is invalid.","invalid custom metric name":"Custom metric name {$customMetricName} is invalid","invalid String merger input":"Input for String merger is invalid, contact support team to resolve.","already initialized":"initializePerformance() has already been called with different options. To avoid this error, call initializePerformance() with the same options as when it was originally called, or call getPerformance() to return the already initialized instance."}),k=new class{constructor(e){this.name=e,this._logLevel=H,this._logHandler=$,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in t))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?x[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,t.DEBUG,...e),this._logHandler(this,t.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,t.VERBOSE,...e),this._logHandler(this,t.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,t.INFO,...e),this._logHandler(this,t.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,t.WARN,...e),this._logHandler(this,t.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,t.ERROR,...e),this._logHandler(this,t.ERROR,...e)}}(a);k.logLevel=t.INFO;let Ft,Ut;class M{constructor(e){if(!(this.window=e))throw C.create("no window");this.performance=e.performance,this.PerformanceObserver=e.PerformanceObserver,this.windowLocation=e.location,this.navigator=e.navigator,this.document=e.document,this.navigator&&this.navigator.cookieEnabled&&(this.localStorage=e.localStorage),e.perfMetrics&&e.perfMetrics.onFirstInputDelay&&(this.onFirstInputDelay=e.perfMetrics.onFirstInputDelay),this.onLCP=Y,this.onINP=X,this.onCLS=z}getUrl(){return this.windowLocation.href.split("?")[0]}mark(e){this.performance&&this.performance.mark&&this.performance.mark(e)}measure(e,t,n){this.performance&&this.performance.measure&&this.performance.measure(e,t,n)}getEntriesByType(e){return this.performance&&this.performance.getEntriesByType?this.performance.getEntriesByType(e):[]}getEntriesByName(e){return this.performance&&this.performance.getEntriesByName?this.performance.getEntriesByName(e):[]}getTimeOrigin(){return this.performance&&(this.performance.timeOrigin||this.performance.timing.navigationStart)}requiredApisAvailable(){return fetch&&Promise&&"undefined"!=typeof navigator&&navigator.cookieEnabled?!!(()=>{try{return"object"==typeof indexedDB}catch(e){}})()||(k.info("IndexedDB is not supported by current browser"),!1):(k.info("Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled."),!1)}setupObserver(e,n){this.PerformanceObserver&&new this.PerformanceObserver(e=>{for(var t of e.getEntries())n(t)}).observe({entryTypes:[e]})}static getInstance(){return Ft=void 0===Ft?new M(Ut):Ft}}let L;function qt(e,t){var n=e.length-t.length;if(n<0||1<n)throw C.create("invalid String merger input");var r=[];for(let i=0;i<e.length;i++)r.push(e.charAt(i)),t.length>i&&r.push(t.charAt(i));return r.join("")}let xt;class A{constructor(){this.instrumentationEnabled=!0,this.dataCollectionEnabled=!0,this.loggingEnabled=!1,this.tracesSamplingRate=1,this.networkRequestsSamplingRate=1,this.logEndPointUrl="https://firebaselogging.googleapis.com/v0cc/log?format=json_proto",this.flTransportEndpointUrl=qt("hts/frbslgigp.ogepscmv/ieo/eaylg","tp:/ieaeogn-agolai.o/1frlglgc/o"),this.transportKey=qt("AzSC8r6ReiGqFMyfvgow","Iayx0u-XT3vksVM-pIV"),this.logSource=462,this.logTraceAfterSampling=!1,this.logNetworkAfterSampling=!1,this.configTimeToLive=12}getFlTransportFullUrl(){return this.flTransportEndpointUrl.concat("?key=",this.transportKey)}static getInstance(){return xt=void 0===xt?new A:xt}}(I=S=S||{})[I.UNKNOWN=0]="UNKNOWN",I[I.VISIBLE=1]="VISIBLE",I[I.HIDDEN=2]="HIDDEN";let Ht=["firebase_","google_","ga_"],Vt=new RegExp("^[a-zA-Z]\\w*$");function $t(e){var t=null==(t=e.options)?void 0:t.appId;if(t)return t;throw C.create("no app id")}let Wt="0.0.1",N={loggingEnabled:!0},Kt="FIREBASE_INSTALLATIONS_AUTH";function zt(e,t){var r,i,n=(()=>{var e=M.getInstance().localStorage;if(e){var t=e.getItem(jt);if(t&&(e=>Number(e)>Date.now())(t)){t=e.getItem(Bt);if(t)try{return JSON.parse(t)}catch(e){}}}})();return n?(Jt(n),Promise.resolve()):(i=t,(e=>{var t=e.getToken();return t.then(e=>{}),t})((r=e).installations).then(e=>{var t=(e=>{var t=null==(t=e.options)?void 0:t.projectId;if(t)return t;throw C.create("no project id")})(r.app),n=(e=>{var t=null==(t=e.options)?void 0:t.apiKey;if(t)return t;throw C.create("no api key")})(r.app),t=new Request(`https://firebaseremoteconfig.googleapis.com/v1/projects/${t}/namespaces/fireperf:fetch?key=`+n,{method:"POST",headers:{Authorization:Kt+" "+e},body:JSON.stringify({app_instance_id:i,app_instance_id_token:e,app_id:$t(r.app),app_version:Ot,sdk_version:Wt})});return fetch(t).then(e=>{if(e.ok)return e.json();throw C.create("RC response not ok")})}).catch(()=>{k.info(Gt)}).then(Jt).then(e=>{var t;e=e,t=M.getInstance().localStorage,e&&t&&(t.setItem(Bt,JSON.stringify(e)),t.setItem(jt,String(Date.now()+60*A.getInstance().configTimeToLive*60*1e3)))},()=>{}))}let Gt="Could not fetch config, will use default configs";function Jt(e){var t,n;return e&&(t=A.getInstance(),void 0!==(n=e.entries||{}).fpr_enabled?t.loggingEnabled="true"===String(n.fpr_enabled):t.loggingEnabled=N.loggingEnabled,n.fpr_log_source?t.logSource=Number(n.fpr_log_source):N.logSource&&(t.logSource=N.logSource),n.fpr_log_endpoint_url?t.logEndPointUrl=n.fpr_log_endpoint_url:N.logEndPointUrl&&(t.logEndPointUrl=N.logEndPointUrl),n.fpr_log_transport_key?t.transportKey=n.fpr_log_transport_key:N.transportKey&&(t.transportKey=N.transportKey),void 0!==n.fpr_vc_network_request_sampling_rate?t.networkRequestsSamplingRate=Number(n.fpr_vc_network_request_sampling_rate):void 0!==N.networkRequestsSamplingRate&&(t.networkRequestsSamplingRate=N.networkRequestsSamplingRate),void 0!==n.fpr_vc_trace_sampling_rate?t.tracesSamplingRate=Number(n.fpr_vc_trace_sampling_rate):void 0!==N.tracesSamplingRate&&(t.tracesSamplingRate=N.tracesSamplingRate),t.logTraceAfterSampling=Zt(t.tracesSamplingRate),t.logNetworkAfterSampling=Zt(t.networkRequestsSamplingRate)),e}function Zt(e){return Math.random()<=e}let Xt=1,Yt;function Qt(e){var n;return Xt=2,Yt=Yt||(n=e,(()=>{let n=M.getInstance().document;return new Promise(t=>{if(n&&"complete"!==n.readyState){let e=()=>{"complete"===n.readyState&&(n.removeEventListener("readystatechange",e),t())};n.addEventListener("readystatechange",e)}else t()})})().then(()=>{return e=n.installations,(t=e.getId()).then(e=>{L=e}),t;var e,t}).then(e=>zt(n,e)).then(()=>en(),()=>en()))}function en(){Xt=3}let tn=1e4,nn=1e3,rn=3,P=rn,R=[],an=!1;function on(e){setTimeout(()=>{P<=0||(0<R.length&&sn(),on(tn))},e)}function sn(){let e=R.splice(0,nn);var t,n,r=e.map(e=>({source_extension_json_proto3:e.message,event_time_ms:String(e.eventTime)})),r={request_time_ms:String(Date.now()),client_info:{client_type:1,js_client_info:{}},log_source:A.getInstance().logSource,log_event:r};t=r,r=A.getInstance().getFlTransportFullUrl(),n=JSON.stringify(t),(navigator.sendBeacon&&navigator.sendBeacon(r,n)?Promise.resolve():fetch(r,{method:"POST",body:n,keepalive:!0}).then()).then(()=>{P=rn}).catch(()=>{R=[...e,...R],P--,k.info(`Tries left: ${P}.`),on(tn)})}function cn(t){return(...e)=>{e={message:t(...e),eventTime:Date.now()};if(!e.eventTime||!e.message)throw C.create("invalid cc log");R=[...R,e]}}function ln(){for(;0<R.length;)sn()}let O;function un(e,t){(O=O||{send:cn(fn),flush:ln}).send(e,t)}function dn(e){var t=A.getInstance();!t.instrumentationEnabled&&e.isAuto||(t.dataCollectionEnabled||e.isAuto)&&M.getInstance().requiredApisAvailable()&&(3===Xt?pn(e):Qt(e.performanceController).then(()=>pn(e),()=>pn(e)))}function pn(e){var t;L&&(t=A.getInstance()).loggingEnabled&&t.logTraceAfterSampling&&un(e,1)}function fn(e,t){var n,r;return 0===t?(n={url:e.url,http_method:e.httpMethod||0,http_response_code:200,response_payload_bytes:e.responsePayloadBytes,client_start_time_us:e.startTimeUs,time_to_response_initiated_us:e.timeToResponseInitiatedUs,time_to_response_completed_us:e.timeToResponseCompletedUs},n={application_info:gn(e.performanceController.app),network_request_metric:n},JSON.stringify(n)):(n={name:(t=e).name,is_auto:t.isAuto,client_start_time_us:t.startTimeUs,duration_us:t.durationUs},0!==Object.keys(t.counters).length&&(n.counters=t.counters),r=t.getAttributes(),0!==Object.keys(r).length&&(n.custom_attributes=r),r={application_info:gn(t.performanceController.app),trace_metric:n},JSON.stringify(r))}function gn(e){return{google_app_id:$t(e),app_instance_id:L,web_app_info:{sdk_version:Ot,page_url:M.getInstance().getUrl(),service_worker_status:null!=(t=M.getInstance().navigator)&&t.serviceWorker?t.serviceWorker.controller?2:3:1,visibility_state:(()=>{switch(M.getInstance().document.visibilityState){case"visible":return S.VISIBLE;case"hidden":return S.HIDDEN;default:return S.UNKNOWN}})(),effective_connection_type:(()=>{var e=M.getInstance().navigator.connection;switch(e&&e.effectiveType){case"slow-2g":return 1;case"2g":return 2;case"3g":return 3;case"4g":return 4;default:return 0}})()},application_process_state:0};var t}function mn(e,t){var n,r,i,a=t;a&&void 0!==a.responseStart&&(i=M.getInstance().getTimeOrigin(),i=Math.floor(1e3*(a.startTime+i)),n=a.responseStart?Math.floor(1e3*(a.responseStart-a.startTime)):void 0,r=Math.floor(1e3*(a.responseEnd-a.startTime)),a={performanceController:e,url:a.name&&a.name.split("?")[0],responsePayloadBytes:a.transferSize,startTimeUs:i,timeToResponseInitiatedUs:n,timeToResponseCompletedUs:r},t=a,(i=A.getInstance()).instrumentationEnabled)&&(n=t.url,r=i.logEndPointUrl.split("?")[0],a=i.flTransportEndpointUrl.split("?")[0],n!==r)&&n!==a&&i.loggingEnabled&&i.logNetworkAfterSampling&&un(t,0)}let hn=["_fp","_fcp","_fid","_lcp","_cls","_inp"];class D{constructor(e,t,n=!1,r){this.performanceController=e,this.name=t,this.isAuto=n,this.state=1,this.customAttributes={},this.counters={},this.api=M.getInstance(),this.randomId=Math.floor(1e6*Math.random()),this.isAuto||(this.traceStartMark=`FB-PERF-TRACE-START-${this.randomId}-`+this.name,this.traceStopMark=`FB-PERF-TRACE-STOP-${this.randomId}-`+this.name,this.traceMeasure=r||`${Dt}-${this.randomId}-`+this.name,r&&this.calculateTraceMetrics())}start(){if(1!==this.state)throw C.create("trace started",{traceName:this.name});this.api.mark(this.traceStartMark),this.state=2}stop(){if(2!==this.state)throw C.create("trace stopped",{traceName:this.name});this.state=3,this.api.mark(this.traceStopMark),this.api.measure(this.traceMeasure,this.traceStartMark,this.traceStopMark),this.calculateTraceMetrics(),dn(this)}record(e,t,n){if(e<=0)throw C.create("nonpositive trace startTime",{traceName:this.name});if(t<=0)throw C.create("nonpositive trace duration",{traceName:this.name});if(this.durationUs=Math.floor(1e3*t),this.startTimeUs=Math.floor(1e3*e),n&&n.attributes&&(this.customAttributes=Object.assign({},n.attributes)),n&&n.metrics)for(var r of Object.keys(n.metrics))isNaN(Number(n.metrics[r]))||(this.counters[r]=Math.floor(Number(n.metrics[r])));dn(this)}incrementMetric(e,t=1){void 0===this.counters[e]?this.putMetric(e,t):this.putMetric(e,this.counters[e]+t)}putMetric(e,t){if(r=e,i=this.name,0===r.length||100<r.length||!(i&&i.startsWith("_wt_")&&-1<hn.indexOf(r))&&r.startsWith("_"))throw C.create("invalid custom metric name",{customMetricName:e});var n,r,i;this.counters[e]=(i=null!=t?t:0,(n=Math.floor(i))<i&&k.info(`Metric value should be an Integer, setting the value as : ${n}.`),n)}getMetric(e){return this.counters[e]||0}putAttribute(e,t){var n,r,i=!(0===(n=e).length||40<n.length||Ht.some(e=>n.startsWith(e))||!n.match(Vt)),a=0!==(r=t).length&&r.length<=100;if(i&&a)this.customAttributes[e]=t;else{if(!i)throw C.create("invalid attribute name",{attributeName:e});if(!a)throw C.create("invalid attribute value",{attributeValue:t})}}getAttribute(e){return this.customAttributes[e]}removeAttribute(e){void 0!==this.customAttributes[e]&&delete this.customAttributes[e]}getAttributes(){return Object.assign({},this.customAttributes)}setStartTime(e){this.startTimeUs=e}setDuration(e){this.durationUs=e}calculateTraceMetrics(){var e=this.api.getEntriesByName(this.traceMeasure),e=e&&e[0];e&&(this.durationUs=Math.floor(1e3*e.duration),this.startTimeUs=Math.floor(1e3*(e.startTime+this.api.getTimeOrigin())))}static createOobTrace(e,t,n,r,i){var a=M.getInstance().getUrl();if(a){var a=new D(e,"_wt_"+a,!0),o=Math.floor(1e3*M.getInstance().getTimeOrigin());a.setStartTime(o),t&&t[0]&&(a.setDuration(Math.floor(1e3*t[0].duration)),a.putMetric("domInteractive",Math.floor(1e3*t[0].domInteractive)),a.putMetric("domContentLoadedEventEnd",Math.floor(1e3*t[0].domContentLoadedEventEnd)),a.putMetric("loadEventEnd",Math.floor(1e3*t[0].loadEventEnd)));n&&((o=n.find(e=>"first-paint"===e.name))&&o.startTime&&a.putMetric("_fp",Math.floor(1e3*o.startTime)),(o=n.find(e=>"first-contentful-paint"===e.name))&&o.startTime&&a.putMetric("_fcp",Math.floor(1e3*o.startTime)),i)&&a.putMetric("_fid",Math.floor(1e3*i)),this.addWebVitalMetric(a,"_lcp","lcp_element",r.lcp),this.addWebVitalMetric(a,"_cls","cls_largestShiftTarget",r.cls),this.addWebVitalMetric(a,"_inp","inp_interactionTarget",r.inp),dn(a),O&&O.flush()}}static addWebVitalMetric(e,t,n,r){r&&(e.putMetric(t,Math.floor(1e3*r.value)),r.elementAttribution)&&e.putAttribute(n,r.elementAttribution)}static createUserTimingTrace(e,t){dn(new D(e,t,!1,t))}}let vn={},bn=!1,yn;function wn(r){L&&(setTimeout(()=>{{var t=r;let e=M.getInstance();"onpagehide"in window?e.document.addEventListener("pagehide",()=>Tn(t)):e.document.addEventListener("unload",()=>Tn(t)),e.document.addEventListener("visibilitychange",()=>{"hidden"===e.document.visibilityState&&Tn(t)}),e.onFirstInputDelay&&e.onFirstInputDelay(e=>{yn=e}),e.onLCP(e=>{var t;vn.lcp={value:e.value,elementAttribution:null==(t=e.attribution)?void 0:t.element}}),e.onCLS(e=>{var t;vn.cls={value:e.value,elementAttribution:null==(t=e.attribution)?void 0:t.largestShiftTarget}}),e.onINP(e=>{var t;vn.inp={value:e.value,elementAttribution:null==(t=e.attribution)?void 0:t.interactionTarget}})}},0),setTimeout(()=>{var e,t=r,n=M.getInstance();for(e of n.getEntriesByType("resource"))mn(t,e);n.setupObserver("resource",e=>mn(t,e))},0),setTimeout(()=>{var e,t=r,n=M.getInstance();for(e of n.getEntriesByType("measure"))En(t,e);n.setupObserver("measure",e=>En(t,e))},0))}function En(e,t){var n=t.name;n.substring(0,Dt.length)!==Dt&&D.createUserTimingTrace(e,n)}function Tn(n){if(!bn){bn=!0;var r=M.getInstance();let e=r.getEntriesByType("navigation"),t=r.getEntriesByType("paint");setTimeout(()=>{D.createOobTrace(n,e,t,vn,yn)},0)}}class _n{constructor(e,t){this.app=e,this.installations=t,this.initialized=!1}_init(e){this.initialized||(void 0!==(null==e?void 0:e.dataCollectionEnabled)&&(this.dataCollectionEnabled=e.dataCollectionEnabled),void 0!==(null==e?void 0:e.instrumentationEnabled)&&(this.instrumentationEnabled=e.instrumentationEnabled),M.getInstance().requiredApisAvailable()?new Promise((r,i)=>{try{let e=!0,t="validate-browser-context-for-indexeddb-analytics-module",n=self.indexedDB.open(t);n.onsuccess=()=>{n.result.close(),e||self.indexedDB.deleteDatabase(t),r(!0)},n.onupgradeneeded=()=>{e=!1},n.onerror=()=>{var e;i((null==(e=n.error)?void 0:e.message)||"")}}catch(e){i(e)}}).then(e=>{e&&(an||(on(5500),an=!0),Qt(this).then(()=>wn(this),()=>wn(this)),this.initialized=!0)}).catch(e=>{k.info("Environment doesn't support IndexedDB: "+e)}):k.info('Firebase Performance cannot start if the browser does not support "Fetch" and "Promise", or cookies are disabled.'))}set instrumentationEnabled(e){A.getInstance().instrumentationEnabled=e}get instrumentationEnabled(){return A.getInstance().instrumentationEnabled}set dataCollectionEnabled(e){A.getInstance().dataCollectionEnabled=e}get dataCollectionEnabled(){return A.getInstance().dataCollectionEnabled}}let Sn="[DEFAULT]";let In=(e,{options:t})=>{var n=e.getProvider("app").getImmediate(),r=e.getProvider("installations-internal").getImmediate();if(n.name!==Sn)throw C.create("FB not default");if("undefined"==typeof window)throw C.create("no window");e=window,Ut=e;n=new _n(n,r);return n._init(t),n};Ln._registerComponent(new e("performance",In,"PUBLIC")),Ln.registerVersion(Pt,Rt),Ln.registerVersion(Pt,Rt,"esm2017");class Cn{constructor(e,t){this.app=e,this._delegate=t}get instrumentationEnabled(){return this._delegate.instrumentationEnabled}set instrumentationEnabled(e){this._delegate.instrumentationEnabled=e}get dataCollectionEnabled(){return this._delegate.dataCollectionEnabled}set dataCollectionEnabled(e){this._delegate.dataCollectionEnabled=e}trace(e){return t=this._delegate,e=e,t=(n=t)&&n._delegate?n._delegate:n,new D(t,e);var t,n}}function kn(e){var t=e.getProvider("app-compat").getImmediate(),n=e.getProvider("performance").getImmediate();return new Cn(t,n)}(I=j.default).INTERNAL.registerComponent(new e("performance-compat",kn,"PUBLIC")),I.registerVersion("@firebase/performance-compat","0.2.19")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-performance-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-performance-compat.js.map
