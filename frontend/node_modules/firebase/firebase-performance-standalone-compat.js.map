{"version": 3, "file": "firebase-performance-standalone-compat.js", "sources": ["../util/dist/postinstall.mjs", "../util/src/crypt.ts", "../util/src/deepCopy.ts", "../util/src/defaults.ts", "../util/src/global.ts", "../util/src/deferred.ts", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/obj.ts", "../component/src/component.ts", "../component/src/constants.ts", "../component/src/provider.ts", "../component/src/component_container.ts", "../logger/src/logger.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../../node_modules/idb/build/index.js", "../app/src/platformLoggerService.ts", "../app/src/logger.ts", "../app/src/constants.ts", "../app/src/internal.ts", "../app/src/errors.ts", "../app/src/firebaseApp.ts", "../app/src/firebaseServerApp.ts", "../app/src/api.ts", "../app/src/indexeddb.ts", "../app/src/heartbeatService.ts", "../app/src/registerCoreComponents.ts", "../app/src/index.ts", "../app-compat/src/lite/firebaseAppLite.ts", "../app-compat/src/errors.ts", "../app-compat/src/firebaseNamespaceCore.ts", "../app-compat/src/index.lite.ts", "../app-compat/src/lite/firebaseNamespaceLite.ts", "../app-compat/src/registerCoreComponents.ts", "compat/app/index.ts", "../../node_modules/web-vitals/dist/web-vitals.attribution.js", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../performance/src/constants.ts", "../performance/src/utils/errors.ts", "../performance/src/utils/console_logger.ts", "../performance/src/services/api_service.ts", "../performance/src/services/iid_service.ts", "../performance/src/services/settings_service.ts", "../performance/src/utils/string_merger.ts", "../performance/src/utils/attributes_utils.ts", "../performance/src/utils/app_utils.ts", "../performance/src/services/remote_config_service.ts", "../performance/src/services/initialization_service.ts", "../performance/src/services/transport_service.ts", "../performance/src/services/perf_logger.ts", "../performance/src/resources/network_request.ts", "../performance/src/utils/metric_utils.ts", "../performance/src/resources/trace.ts", "../performance/src/services/oob_resources_service.ts", "../performance/src/controllers/perf.ts", "../performance/src/index.ts", "../performance-compat/src/performance.ts", "../util/src/compat.ts", "../performance-compat/src/index.ts", "compat/index.perf.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// This value is retrieved and hardcoded by the NPM postinstall script\nconst getDefaultsFromPostinstall = () => undefined;\n\nexport { getDefaultsFromPostinstall };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nexport function deepCopy<T>(value: T): T {\n  return deepExtend(undefined, value) as T;\n}\n\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nexport function deepExtend(target: unknown, source: unknown): unknown {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source as Date;\n      return new Date(dateValue.getTime());\n\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    (target as Record<string, unknown>)[prop] = deepExtend(\n      (target as Record<string, unknown>)[prop],\n      (source as Record<string, unknown>)[prop]\n    );\n  }\n\n  return target;\n}\n\nfunction isValidKey(key: string): boolean {\n  return key !== '__proto__';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\nimport { getDefaultsFromPostinstall } from './postinstall';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromPostinstall() ||\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance!);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance!, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance!\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as aiName } from '../../../packages/ai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [aiName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp | null | undefined\n): obj is FirebaseServerApp {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\nimport { base64Decode } from '@firebase/util';\n\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token: string, tokenName: string): void {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`\n    );\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`\n    );\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: the token has expired.`\n    );\n  }\n}\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : true;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: true,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - `FirebaseServerApp` configuration.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config: FirebaseServerAppSettings\n): FirebaseServerApp;\n\nexport function initializeServerApp(\n  _options: FirebaseOptions | FirebaseApp,\n  _serverAppConfig: FirebaseServerAppSettings\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = true;\n  }\n\n  let appOptions: FirebaseOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ..._serverAppConfig,\n    ...appOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      _serverAppConfig.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    appOptions,\n    _serverAppConfig,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\nexport const MAX_NUM_STORED_HEARTBEATS = 30;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n\n        // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n        // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n        if (\n          this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS\n        ) {\n          const earliestHeartbeatIdx = getEarliestHeartbeatIdx(\n            this._heartbeatsCache.heartbeats\n          );\n          this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n        }\n      }\n\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nexport function getEarliestHeartbeatIdx(\n  heartbeats: SingleDateHeartbeat[]\n): number {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n\n  return earliestHeartbeatIdx;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '../public-types';\nimport { _FirebaseNamespace, _FirebaseService } from '../types';\nimport {\n  deleteApp,\n  _addComponent,\n  _DEFAULT_ENTRY_NAME,\n  _FirebaseAppInternal as FirebaseAppExp\n} from '@firebase/app';\nimport { Component, ComponentType, Name } from '@firebase/component';\nimport { Compat } from '@firebase/util';\n\n/**\n * Global context object for a collection of services using\n * a shared authentication state.\n */\nexport class FirebaseAppLiteImpl\n  implements FirebaseApp, Compat<FirebaseAppExp>\n{\n  constructor(\n    readonly _delegate: FirebaseAppExp,\n    private readonly firebase: _FirebaseNamespace\n  ) {\n    // add itself to container\n    _addComponent(\n      _delegate,\n      new Component('app-compat', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    return this._delegate.automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val) {\n    this.automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get options(): FirebaseOptions {\n    return this._delegate.options;\n  }\n\n  delete(): Promise<void> {\n    this.firebase.INTERNAL.removeApp(this.name);\n    return deleteApp(this._delegate);\n  }\n\n  /**\n   * Return a service instance associated with this app (creating it\n   * on demand), identified by the passed instanceIdentifier.\n   *\n   * NOTE: Currently storage is the only one that is leveraging this\n   * functionality. They invoke it by calling:\n   *\n   * ```javascript\n   * firebase.app().storage('STORAGE BUCKET ID')\n   * ```\n   *\n   * The service name is passed to this already\n   * @internal\n   */\n  _getService(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): _FirebaseService {\n    this._delegate.checkDestroyed();\n\n    // getImmediate will always succeed because _getService is only called for registered components.\n    return this._delegate.container.getProvider(name as Name).getImmediate({\n      identifier: instanceIdentifier\n    }) as unknown as _FirebaseService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call Firebase App.initializeApp()',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.'\n};\n\ntype ErrorParams = { [key in AppError]: { appName: string } };\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app-compat',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from './public-types';\nimport {\n  _FirebaseNamespace,\n  _FirebaseService,\n  FirebaseServiceNamespace\n} from './types';\nimport * as modularAPIs from '@firebase/app';\nimport { _FirebaseAppInternal as _FirebaseAppExp } from '@firebase/app';\nimport { Component, ComponentType, Name } from '@firebase/component';\n\nimport { deepExtend, contains } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { FirebaseAppLiteImpl } from './lite/firebaseAppLite';\n\n/**\n * Because auth can't share code with other components, we attach the utility functions\n * in an internal namespace to share code.\n * This function return a firebase namespace object without\n * any utility functions, so it can be shared between the regular firebaseNamespace and\n * the lite version.\n */\nexport function createFirebaseNamespaceCore(\n  firebaseAppImpl: typeof FirebaseAppImpl | typeof FirebaseAppLiteImpl\n): _FirebaseNamespace {\n  const apps: { [name: string]: FirebaseApp } = {};\n  // // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  // const components = new Map<string, Component<any>>();\n\n  // A namespace is a plain JavaScript Object.\n  const namespace: _FirebaseNamespace = {\n    // Hack to prevent Babel from modifying the object returned\n    // as the firebase namespace.\n    // @ts-ignore\n    __esModule: true,\n    initializeApp: initializeAppCompat,\n    // @ts-ignore\n    app,\n    registerVersion: modularAPIs.registerVersion,\n    setLogLevel: modularAPIs.setLogLevel,\n    onLog: modularAPIs.onLog,\n    // @ts-ignore\n    apps: null,\n    SDK_VERSION: modularAPIs.SDK_VERSION,\n    INTERNAL: {\n      registerComponent: registerComponentCompat,\n      removeApp,\n      useAsService,\n      modularAPIs\n    }\n  };\n\n  // Inject a circular default export to allow Babel users who were previously\n  // using:\n  //\n  //   import firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase').default;\n  //\n  // instead of\n  //\n  //   import * as firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase');\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (namespace as any)['default'] = namespace;\n\n  // firebase.apps is a read-only getter.\n  Object.defineProperty(namespace, 'apps', {\n    get: getApps\n  });\n\n  /**\n   * Called by App.delete() - but before any services associated with the App\n   * are deleted.\n   */\n  function removeApp(name: string): void {\n    delete apps[name];\n  }\n\n  /**\n   * Get the App object for a given name (or DEFAULT).\n   */\n  function app(name?: string): FirebaseApp {\n    name = name || modularAPIs._DEFAULT_ENTRY_NAME;\n    if (!contains(apps, name)) {\n      throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n    }\n    return apps[name];\n  }\n\n  // @ts-ignore\n  app['App'] = firebaseAppImpl;\n\n  /**\n   * Create a new App instance (name must be unique).\n   *\n   * This function is idempotent. It can be called more than once and return the same instance using the same options and config.\n   */\n  function initializeAppCompat(\n    options: FirebaseOptions,\n    rawConfig = {}\n  ): FirebaseApp {\n    const app = modularAPIs.initializeApp(\n      options,\n      rawConfig\n    ) as _FirebaseAppExp;\n\n    if (contains(apps, app.name)) {\n      return apps[app.name];\n    }\n\n    const appCompat = new firebaseAppImpl(app, namespace);\n    apps[app.name] = appCompat;\n    return appCompat;\n  }\n\n  /*\n   * Return an array of all the non-deleted FirebaseApps.\n   */\n  function getApps(): FirebaseApp[] {\n    // Make a copy so caller cannot mutate the apps list.\n    return Object.keys(apps).map(name => apps[name]);\n  }\n\n  function registerComponentCompat<T extends Name>(\n    component: Component<T>\n  ): FirebaseServiceNamespace<_FirebaseService> | null {\n    const componentName = component.name;\n    const componentNameWithoutCompat = componentName.replace('-compat', '');\n    if (\n      modularAPIs._registerComponent(component) &&\n      component.type === ComponentType.PUBLIC\n    ) {\n      // create service namespace for public components\n      // The Service namespace is an accessor function ...\n      const serviceNamespace = (\n        appArg: FirebaseApp = app()\n      ): _FirebaseService => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (typeof (appArg as any)[componentNameWithoutCompat] !== 'function') {\n          // Invalid argument.\n          // This happens in the following case: firebase.storage('gs:/')\n          throw ERROR_FACTORY.create(AppError.INVALID_APP_ARGUMENT, {\n            appName: componentName\n          });\n        }\n\n        // Forward service instance lookup to the FirebaseApp.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return (appArg as any)[componentNameWithoutCompat]();\n      };\n\n      // ... and a container for service-level properties.\n      if (component.serviceProps !== undefined) {\n        deepExtend(serviceNamespace, component.serviceProps);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (namespace as any)[componentNameWithoutCompat] = serviceNamespace;\n\n      // Patch the FirebaseAppImpl prototype\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (firebaseAppImpl.prototype as any)[componentNameWithoutCompat] =\n        // TODO: The eslint disable can be removed and the 'ignoreRestArgs'\n        // option added to the no-explicit-any rule when ESlint releases it.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        function (...args: any) {\n          const serviceFxn = this._getService.bind(this, componentName);\n          return serviceFxn.apply(\n            this,\n            component.multipleInstances ? args : []\n          );\n        };\n    }\n\n    return component.type === ComponentType.PUBLIC\n      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (namespace as any)[componentNameWithoutCompat]\n      : null;\n  }\n\n  // Map the requested service to a registered service name\n  // (used to map auth to serverAuth service when needed).\n  function useAsService(app: FirebaseApp, name: string): string | null {\n    if (name === 'serverAuth') {\n      return null;\n    }\n\n    const useService = name;\n\n    return useService;\n  }\n\n  return namespace;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createFirebaseNamespaceLite } from './lite/firebaseNamespaceLite';\nimport { registerCoreComponents } from './registerCoreComponents';\n\nconst firebase = createFirebaseNamespaceLite();\n\nregisterCoreComponents('lite');\n\n// eslint-disable-next-line import/no-default-export\nexport default firebase;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from '../public-types';\nimport { FirebaseServiceNamespace, _FirebaseService } from '../types';\nimport { FirebaseAppLiteImpl } from './firebaseAppLite';\nimport { createFirebaseNamespaceCore } from '../firebaseNamespaceCore';\nimport { Component, ComponentType } from '@firebase/component';\n\nexport function createFirebaseNamespaceLite(): FirebaseNamespace {\n  const namespace = createFirebaseNamespaceCore(FirebaseAppLiteImpl);\n\n  namespace.SDK_VERSION = `${namespace.SDK_VERSION}_LITE`;\n\n  const registerComponent = namespace.INTERNAL.registerComponent;\n  namespace.INTERNAL.registerComponent = registerComponentForLite;\n\n  /**\n   * This is a special implementation, so it only works with performance.\n   * only allow performance SDK to register.\n   */\n  function registerComponentForLite(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    component: Component<any>\n  ): FirebaseServiceNamespace<_FirebaseService> | null {\n    // only allow performance to register with firebase lite\n    if (\n      component.type === ComponentType.PUBLIC &&\n      !component.name.includes('performance') &&\n      !component.name.includes('installations')\n    ) {\n      throw Error(`${name} cannot register with the standalone perf instance`);\n    }\n\n    return registerComponent(component);\n  }\n\n  return namespace;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion } from '@firebase/app';\n\nimport { name, version } from '../package.json';\n\nexport function registerCoreComponents(variant?: string): void {\n  // Register `app` package.\n  registerVersion(name, version, variant);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../../package.json';\n\nfirebase.registerVersion(name, version, 'app-compat');\n\nexport default firebase;\n", "var t,e,n=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},r=function(t){if(\"loading\"===document.readyState)return\"loading\";var e=n();if(e){if(t<e.domInteractive)return\"loading\";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return\"dom-interactive\";if(0===e.domComplete||t<e.domComplete)return\"dom-content-loaded\"}return\"complete\"},i=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,\"\")},a=function(t,e){var n=\"\";try{for(;t&&9!==t.nodeType;){var r=t,a=r.id?\"#\"+r.id:i(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?\".\"+r.classList.value.trim().replace(/\\s+/g,\".\"):\"\");if(n.length+a.length>(e||100)-1)return n||a;if(n=n?a+\">\"+n:a,r.id)break;t=r.parentNode}}catch(t){}return n},o=-1,c=function(){return o},u=function(t){addEventListener(\"pageshow\",(function(e){e.persisted&&(o=e.timeStamp,t(e))}),!0)},s=function(){var t=n();return t&&t.activationStart||0},f=function(t,e){var r=n(),i=\"navigate\";c()>=0?i=\"back-forward-cache\":r&&(document.prerendering||s()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":r.type&&(i=r.type.replace(/_/g,\"-\")));return{name:t,value:void 0===e?-1:e,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},d=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},l=function(t,e,n,r){var i,a;return function(o){e.value>=0&&(o||r)&&((a=e.value-(i||0))||void 0===i)&&(i=e.value,e.delta=a,e.rating=function(t,e){return t>e[1]?\"poor\":t>e[0]?\"needs-improvement\":\"good\"}(e.value,n),t(e))}},m=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&t()}))},v=function(t){var e=!1;return function(){e||(t(),e=!0)}},g=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},T=function(t){\"hidden\"===document.visibilityState&&g>-1&&(g=\"visibilitychange\"===t.type?t.timeStamp:0,E())},y=function(){addEventListener(\"visibilitychange\",T,!0),addEventListener(\"prerenderingchange\",T,!0)},E=function(){removeEventListener(\"visibilitychange\",T,!0),removeEventListener(\"prerenderingchange\",T,!0)},S=function(){return g<0&&(g=h(),y(),u((function(){setTimeout((function(){g=h(),y()}),0)}))),{get firstHiddenTime(){return g}}},b=function(t){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return t()}),!0):t()},L=[1800,3e3],C=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FCP\"),a=d(\"paint\",(function(t){t.forEach((function(t){\"first-contentful-paint\"===t.name&&(a.disconnect(),t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries.push(t),n(!0)))}))}));a&&(n=l(t,i,L,e.reportAllChanges),u((function(r){i=f(\"FCP\"),n=l(t,i,L,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},M=[.1,.25],D=function(t,e){!function(t,e){e=e||{},C(v((function(){var n,r=f(\"CLS\",0),i=0,a=[],o=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=a[0],n=a[a.length-1];i&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(i+=t.value,a.push(t)):(i=t.value,a=[t])}})),i>r.value&&(r.value=i,r.entries=a,n())},c=d(\"layout-shift\",o);c&&(n=l(t,r,M,e.reportAllChanges),p((function(){o(c.takeRecords()),n(!0)})),u((function(){i=0,r=f(\"CLS\",0),n=l(t,r,M,e.reportAllChanges),m((function(){return n()}))})),setTimeout(n,0))})))}((function(e){var n=function(t){var e,n={};if(t.entries.length){var i=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(i&&i.sources&&i.sources.length){var o=(e=i.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];o&&(n={largestShiftTarget:a(o.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:o,largestShiftEntry:i,loadState:r(i.startTime)})}}return Object.assign(t,{attribution:n})}(e);t(n)}),e)},w=function(t,e){C((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:r(c())};if(t.entries.length){var i=n(),a=t.entries[t.entries.length-1];if(i){var o=i.activationStart||0,u=Math.max(0,i.responseStart-o);e={timeToFirstByte:u,firstByteToFCP:t.value-u,loadState:r(t.entries[0].startTime),navigationEntry:i,fcpEntry:a}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},x=0,I=1/0,k=0,A=function(t){t.forEach((function(t){t.interactionId&&(I=Math.min(I,t.interactionId),k=Math.max(k,t.interactionId),x=k?(k-I)/7+1:0)}))},F=function(){return t?x:performance.interactionCount||0},P=function(){\"interactionCount\"in performance||t||(t=d(\"event\",A,{type:\"event\",buffered:!0,durationThreshold:0}))},B=[],O=new Map,R=0,j=function(){var t=Math.min(B.length-1,Math.floor((F()-R)/50));return B[t]},q=[],H=function(t){if(q.forEach((function(e){return e(t)})),t.interactionId||\"first-input\"===t.entryType){var e=B[B.length-1],n=O.get(t.interactionId);if(n||B.length<10||t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===n.entries[0].startTime&&n.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};O.set(r.id,r),B.push(r)}B.sort((function(t,e){return e.latency-t.latency})),B.length>10&&B.splice(10).forEach((function(t){return O.delete(t.id)}))}}},N=function(t){var e=self.requestIdleCallback||self.setTimeout,n=-1;return t=v(t),\"hidden\"===document.visibilityState?t():(n=e(t),p(t)),n},W=[200,500],z=function(t,e){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(e=e||{},b((function(){var n;P();var r,i=f(\"INP\"),a=function(t){N((function(){t.forEach(H);var e=j();e&&e.latency!==i.value&&(i.value=e.latency,i.entries=e.entries,r())}))},o=d(\"event\",a,{durationThreshold:null!==(n=e.durationThreshold)&&void 0!==n?n:40});r=l(t,i,W,e.reportAllChanges),o&&(o.observe({type:\"first-input\",buffered:!0}),p((function(){a(o.takeRecords()),r(!0)})),u((function(){R=F(),B.length=0,O.clear(),i=f(\"INP\"),r=l(t,i,W,e.reportAllChanges)})))})))},U=[],V=[],_=0,G=new WeakMap,J=new Map,K=-1,Q=function(t){U=U.concat(t),X()},X=function(){K<0&&(K=N(Y))},Y=function(){J.size>10&&J.forEach((function(t,e){O.has(e)||J.delete(e)}));var t=B.map((function(t){return G.get(t.entries[0])})),e=V.length-50;V=V.filter((function(n,r){return r>=e||t.includes(n)}));for(var n=new Set,r=0;r<V.length;r++){var i=V[r];nt(i.startTime,i.processingEnd).forEach((function(t){n.add(t)}))}var a=U.length-1-50;U=U.filter((function(t,e){return t.startTime>_&&e>a||n.has(t)})),K=-1};q.push((function(t){t.interactionId&&t.target&&!J.has(t.interactionId)&&J.set(t.interactionId,t.target)}),(function(t){var e,n=t.startTime+t.duration;_=Math.max(_,t.processingEnd);for(var r=V.length-1;r>=0;r--){var i=V[r];if(Math.abs(n-i.renderTime)<=8){(e=i).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:n,entries:[t]},V.push(e)),(t.interactionId||\"first-input\"===t.entryType)&&G.set(t,e),X()}));var Z,$,tt,et,nt=function(t,e){for(var n,r=[],i=0;n=U[i];i++)if(!(n.startTime+n.duration<t)){if(n.startTime>e)break;r.push(n)}return r},rt=function(t,n){e||(e=d(\"long-animation-frame\",Q)),z((function(e){var n=function(t){var e=t.entries[0],n=G.get(e),i=e.processingStart,o=n.processingEnd,c=n.entries.sort((function(t,e){return t.processingStart-e.processingStart})),u=nt(e.startTime,o),s=t.entries.find((function(t){return t.target})),f=s&&s.target||J.get(e.interactionId),d=[e.startTime+e.duration,o].concat(u.map((function(t){return t.startTime+t.duration}))),l=Math.max.apply(Math,d),m={interactionTarget:a(f),interactionTargetElement:f,interactionType:e.name.startsWith(\"key\")?\"keyboard\":\"pointer\",interactionTime:e.startTime,nextPaintTime:l,processedEventEntries:c,longAnimationFrameEntries:u,inputDelay:i-e.startTime,processingDuration:o-i,presentationDelay:Math.max(l-o,0),loadState:r(e.startTime)};return Object.assign(t,{attribution:m})}(e);t(n)}),n)},it=[2500,4e3],at={},ot=function(t,e){!function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"LCP\"),a=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries=[t],n())}))},o=d(\"largest-contentful-paint\",a);if(o){n=l(t,i,it,e.reportAllChanges);var c=v((function(){at[i.id]||(a(o.takeRecords()),o.disconnect(),at[i.id]=!0,n(!0))}));[\"keydown\",\"click\"].forEach((function(t){addEventListener(t,(function(){return N(c)}),{once:!0,capture:!0})})),p(c),u((function(r){i=f(\"LCP\"),n=l(t,i,it,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,at[i.id]=!0,n(!0)}))}))}}))}((function(e){var r=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var r=n();if(r){var i=r.activationStart||0,o=t.entries[t.entries.length-1],c=o.url&&performance.getEntriesByType(\"resource\").filter((function(t){return t.name===o.url}))[0],u=Math.max(0,r.responseStart-i),s=Math.max(u,c?(c.requestStart||c.startTime)-i:0),f=Math.max(s,c?c.responseEnd-i:0),d=Math.max(f,o.startTime-i);e={element:a(o.element),timeToFirstByte:u,resourceLoadDelay:s-u,resourceLoadDuration:f-s,elementRenderDelay:d-f,navigationEntry:r,lcpEntry:o},o.url&&(e.url=o.url),c&&(e.lcpResourceEntry=c)}}return Object.assign(t,{attribution:e})}(e);t(r)}),e)},ct=[800,1800],ut=function t(e){document.prerendering?b((function(){return t(e)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return t(e)}),!0):setTimeout(e,0)},st=function(t,e){e=e||{};var r=f(\"TTFB\"),i=l(t,r,ct,e.reportAllChanges);ut((function(){var a=n();a&&(r.value=Math.max(a.responseStart-s(),0),r.entries=[a],i(!0),u((function(){r=f(\"TTFB\",0),(i=l(t,r,ct,e.reportAllChanges))(!0)})))}))},ft=function(t,e){st((function(e){var n=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var n=t.entries[0],r=n.activationStart||0,i=Math.max((n.workerStart||n.fetchStart)-r,0),a=Math.max(n.domainLookupStart-r,0),o=Math.max(n.connectStart-r,0),c=Math.max(n.connectEnd-r,0);e={waitingDuration:i,cacheDuration:a-i,dnsDuration:o-a,connectionDuration:c-o,requestDuration:t.value-c,navigationEntry:n}}return Object.assign(t,{attribution:e})}(e);t(n)}),e)},dt={passive:!0,capture:!0},lt=new Date,mt=function(t,e){Z||(Z=e,$=t,tt=new Date,gt(removeEventListener),pt())},pt=function(){if($>=0&&$<tt-lt){var t={entryType:\"first-input\",name:Z.type,target:Z.target,cancelable:Z.cancelable,startTime:Z.timeStamp,processingStart:Z.timeStamp+$};et.forEach((function(e){e(t)})),et=[]}},vt=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;\"pointerdown\"==t.type?function(t,e){var n=function(){mt(t,e),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",n,dt),removeEventListener(\"pointercancel\",r,dt)};addEventListener(\"pointerup\",n,dt),addEventListener(\"pointercancel\",r,dt)}(e,t):mt(e,t)}},gt=function(t){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(e){return t(e,vt,dt)}))},ht=[100,300],Tt=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FID\"),a=function(t){t.startTime<r.firstHiddenTime&&(i.value=t.processingStart-t.startTime,i.entries.push(t),n(!0))},o=function(t){t.forEach(a)},c=d(\"first-input\",o);n=l(t,i,ht,e.reportAllChanges),c&&(p(v((function(){o(c.takeRecords()),c.disconnect()}))),u((function(){var r;i=f(\"FID\"),n=l(t,i,ht,e.reportAllChanges),et=[],$=-1,Z=null,gt(addEventListener),r=a,et.push(r),pt()})))}))},yt=function(t,e){Tt((function(e){var n=function(t){var e=t.entries[0],n={eventTarget:a(e.target),eventType:e.name,eventTime:e.startTime,eventEntry:e,loadState:r(e.startTime)};return Object.assign(t,{attribution:n})}(e);t(n)}),e)};export{M as CLSThresholds,L as FCPThresholds,ht as FIDThresholds,W as INPThresholds,it as LCPThresholds,ct as TTFBThresholds,D as onCLS,w as onFCP,yt as onFID,rt as onINP,ot as onLCP,ft as onTTFB};\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const SDK_VERSION = version;\n/** The prefix for start User Timing marks used for creating Traces. */\nexport const TRACE_START_MARK_PREFIX = 'FB-PERF-TRACE-START';\n/** The prefix for stop User Timing marks used for creating Traces. */\nexport const TRACE_STOP_MARK_PREFIX = 'FB-PERF-TRACE-STOP';\n/** The prefix for User Timing measure used for creating Traces. */\nexport const TRACE_MEASURE_PREFIX = 'FB-PERF-TRACE-MEASURE';\n/** The prefix for out of the box page load Trace name. */\nexport const OOB_TRACE_PAGE_LOAD_PREFIX = '_wt_';\n\nexport const FIRST_PAINT_COUNTER_NAME = '_fp';\n\nexport const FIRST_CONTENTFUL_PAINT_COUNTER_NAME = '_fcp';\n\nexport const FIRST_INPUT_DELAY_COUNTER_NAME = '_fid';\n\nexport const LARGEST_CONTENTFUL_PAINT_METRIC_NAME = '_lcp';\nexport const LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME = 'lcp_element';\n\nexport const INTERACTION_TO_NEXT_PAINT_METRIC_NAME = '_inp';\nexport const INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME = 'inp_interactionTarget';\n\nexport const CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME = '_cls';\nexport const CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME = 'cls_largestShiftTarget';\n\nexport const CONFIG_LOCAL_STORAGE_KEY = '@firebase/performance/config';\n\nexport const CONFIG_EXPIRY_LOCAL_STORAGE_KEY =\n  '@firebase/performance/configexpire';\n\nexport const SERVICE = 'performance';\nexport const SERVICE_NAME = 'Performance';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from '../constants';\n\nexport const enum ErrorCode {\n  TRACE_STARTED_BEFORE = 'trace started',\n  TRACE_STOPPED_BEFORE = 'trace stopped',\n  NONPOSITIVE_TRACE_START_TIME = 'nonpositive trace startTime',\n  NONPOSITIVE_TRACE_DURATION = 'nonpositive trace duration',\n  NO_WINDOW = 'no window',\n  NO_APP_ID = 'no app id',\n  NO_PROJECT_ID = 'no project id',\n  NO_API_KEY = 'no api key',\n  INVALID_CC_LOG = 'invalid cc log',\n  FB_NOT_DEFAULT = 'FB not default',\n  RC_NOT_OK = 'RC response not ok',\n  INVALID_ATTRIBUTE_NAME = 'invalid attribute name',\n  INVALID_ATTRIBUTE_VALUE = 'invalid attribute value',\n  INVALID_CUSTOM_METRIC_NAME = 'invalid custom metric name',\n  INVALID_STRING_MERGER_PARAMETER = 'invalid String merger input',\n  ALREADY_INITIALIZED = 'already initialized'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.TRACE_STARTED_BEFORE]: 'Trace {$traceName} was started before.',\n  [ErrorCode.TRACE_STOPPED_BEFORE]: 'Trace {$traceName} is not running.',\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]:\n    'Trace {$traceName} startTime should be positive.',\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]:\n    'Trace {$traceName} duration should be positive.',\n  [ErrorCode.NO_WINDOW]: 'Window is not available.',\n  [ErrorCode.NO_APP_ID]: 'App id is not available.',\n  [ErrorCode.NO_PROJECT_ID]: 'Project id is not available.',\n  [ErrorCode.NO_API_KEY]: 'Api key is not available.',\n  [ErrorCode.INVALID_CC_LOG]: 'Attempted to queue invalid cc event',\n  [ErrorCode.FB_NOT_DEFAULT]:\n    'Performance can only start when Firebase app instance is the default one.',\n  [ErrorCode.RC_NOT_OK]: 'RC response is not ok',\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]:\n    'Attribute name {$attributeName} is invalid.',\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]:\n    'Attribute value {$attributeValue} is invalid.',\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]:\n    'Custom metric name {$customMetricName} is invalid',\n  [ErrorCode.INVALID_STRING_MERGER_PARAMETER]:\n    'Input for String merger is invalid, contact support team to resolve.',\n  [ErrorCode.ALREADY_INITIALIZED]:\n    'initializePerformance() has already been called with ' +\n    'different options. To avoid this error, call initializePerformance() with the ' +\n    'same options as when it was originally called, or call getPerformance() to return the' +\n    ' already initialized instance.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.TRACE_STARTED_BEFORE]: { traceName: string };\n  [ErrorCode.TRACE_STOPPED_BEFORE]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]: { traceName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]: { attributeName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]: { attributeValue: string };\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]: { customMetricName: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { SERVICE_NAME } from '../constants';\n\nexport const consoleLogger = new Logger(SERVICE_NAME);\nconsoleLogger.logLevel = LogLevel.INFO;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { isIndexedDBAvailable, areCookiesEnabled } from '@firebase/util';\nimport { consoleLogger } from '../utils/console_logger';\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution,\n  onCLS as vitalsOnCLS,\n  onINP as vitalsOnINP,\n  onLCP as vitalsOnLCP\n} from 'web-vitals/attribution';\n\ndeclare global {\n  interface Window {\n    PerformanceObserver: typeof PerformanceObserver;\n    perfMetrics?: { onFirstInputDelay(fn: (fid: number) => void): void };\n  }\n}\n\nlet apiInstance: Api | undefined;\nlet windowInstance: Window | undefined;\n\nexport type EntryType =\n  | 'mark'\n  | 'measure'\n  | 'paint'\n  | 'resource'\n  | 'frame'\n  | 'navigation';\n\n/**\n * This class holds a reference to various browser related objects injected by\n * set methods.\n */\nexport class Api {\n  private readonly performance: Performance;\n  /** PerformanceObserver constructor function. */\n  private readonly PerformanceObserver: typeof PerformanceObserver;\n  private readonly windowLocation: Location;\n  readonly onFirstInputDelay?: (fn: (fid: number) => void) => void;\n  readonly onLCP: (fn: (metric: LCPMetricWithAttribution) => void) => void;\n  readonly onINP: (fn: (metric: INPMetricWithAttribution) => void) => void;\n  readonly onCLS: (fn: (metric: CLSMetricWithAttribution) => void) => void;\n  readonly localStorage?: Storage;\n  readonly document: Document;\n  readonly navigator: Navigator;\n\n  constructor(readonly window?: Window) {\n    if (!window) {\n      throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n    }\n    this.performance = window.performance;\n    this.PerformanceObserver = window.PerformanceObserver;\n    this.windowLocation = window.location;\n    this.navigator = window.navigator;\n    this.document = window.document;\n    if (this.navigator && this.navigator.cookieEnabled) {\n      // If user blocks cookies on the browser, accessing localStorage will\n      // throw an exception.\n      this.localStorage = window.localStorage;\n    }\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      this.onFirstInputDelay = window.perfMetrics.onFirstInputDelay;\n    }\n    this.onLCP = vitalsOnLCP;\n    this.onINP = vitalsOnINP;\n    this.onCLS = vitalsOnCLS;\n  }\n\n  getUrl(): string {\n    // Do not capture the string query part of url.\n    return this.windowLocation.href.split('?')[0];\n  }\n\n  mark(name: string): void {\n    if (!this.performance || !this.performance.mark) {\n      return;\n    }\n    this.performance.mark(name);\n  }\n\n  measure(measureName: string, mark1: string, mark2: string): void {\n    if (!this.performance || !this.performance.measure) {\n      return;\n    }\n    this.performance.measure(measureName, mark1, mark2);\n  }\n\n  getEntriesByType(type: EntryType): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByType) {\n      return [];\n    }\n    return this.performance.getEntriesByType(type);\n  }\n\n  getEntriesByName(name: string): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByName) {\n      return [];\n    }\n    return this.performance.getEntriesByName(name);\n  }\n\n  getTimeOrigin(): number {\n    // Polyfill the time origin with performance.timing.navigationStart.\n    return (\n      this.performance &&\n      (this.performance.timeOrigin || this.performance.timing.navigationStart)\n    );\n  }\n\n  requiredApisAvailable(): boolean {\n    if (!fetch || !Promise || !areCookiesEnabled()) {\n      consoleLogger.info(\n        'Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled.'\n      );\n      return false;\n    }\n\n    if (!isIndexedDBAvailable()) {\n      consoleLogger.info('IndexedDB is not supported by current browser');\n      return false;\n    }\n    return true;\n  }\n\n  setupObserver(\n    entryType: EntryType,\n    callback: (entry: PerformanceEntry) => void\n  ): void {\n    if (!this.PerformanceObserver) {\n      return;\n    }\n    const observer = new this.PerformanceObserver(list => {\n      for (const entry of list.getEntries()) {\n        // `entry` is a PerformanceEntry instance.\n        callback(entry);\n      }\n    });\n\n    // Start observing the entry types you care about.\n    observer.observe({ entryTypes: [entryType] });\n  }\n\n  static getInstance(): Api {\n    if (apiInstance === undefined) {\n      apiInstance = new Api(windowInstance);\n    }\n    return apiInstance;\n  }\n}\n\nexport function setupApi(window: Window): void {\n  windowInstance = window;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\nlet iid: string | undefined;\nlet authToken: string | undefined;\n\nexport function getIidPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const iidPromise = installationsService.getId();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  iidPromise.then((iidVal: string) => {\n    iid = iidVal;\n  });\n  return iidPromise;\n}\n\n// This method should be used after the iid is retrieved by getIidPromise method.\nexport function getIid(): string | undefined {\n  return iid;\n}\n\nexport function getAuthTokenPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const authTokenPromise = installationsService.getToken();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  authTokenPromise.then((authTokenVal: string) => {\n    authToken = authTokenVal;\n  });\n  return authTokenPromise;\n}\n\nexport function getAuthenticationToken(): string | undefined {\n  return authToken;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { mergeStrings } from '../utils/string_merger';\n\nlet settingsServiceInstance: SettingsService | undefined;\n\nexport class SettingsService {\n  // The variable which controls logging of automatic traces and HTTP/S network monitoring.\n  instrumentationEnabled = true;\n\n  // The variable which controls logging of custom traces.\n  dataCollectionEnabled = true;\n\n  // Configuration flags set through remote config.\n  loggingEnabled = false;\n  // Sampling rate between 0 and 1.\n  tracesSamplingRate = 1;\n  networkRequestsSamplingRate = 1;\n\n  // Address of logging service.\n  logEndPointUrl =\n    'https://firebaselogging.googleapis.com/v0cc/log?format=json_proto';\n  // Performance event transport endpoint URL which should be compatible with proto3.\n  // New Address for transport service, not configurable via Remote Config.\n  flTransportEndpointUrl = mergeStrings(\n    'hts/frbslgigp.ogepscmv/ieo/eaylg',\n    'tp:/ieaeogn-agolai.o/1frlglgc/o'\n  );\n\n  transportKey = mergeStrings('AzSC8r6ReiGqFMyfvgow', 'Iayx0u-XT3vksVM-pIV');\n\n  // Source type for performance event logs.\n  logSource = 462;\n\n  // Flags which control per session logging of traces and network requests.\n  logTraceAfterSampling = false;\n  logNetworkAfterSampling = false;\n\n  // TTL of config retrieved from remote config in hours.\n  configTimeToLive = 12;\n\n  getFlTransportFullUrl(): string {\n    return this.flTransportEndpointUrl.concat('?key=', this.transportKey);\n  }\n\n  static getInstance(): SettingsService {\n    if (settingsServiceInstance === undefined) {\n      settingsServiceInstance = new SettingsService();\n    }\n    return settingsServiceInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\n\nexport function mergeStrings(part1: string, part2: string): string {\n  const sizeDiff = part1.length - part2.length;\n  if (sizeDiff < 0 || sizeDiff > 1) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_STRING_MERGER_PARAMETER);\n  }\n\n  const resultArray = [];\n  for (let i = 0; i < part1.length; i++) {\n    resultArray.push(part1.charAt(i));\n    if (part2.length > i) {\n      resultArray.push(part2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\n\n// The values and orders of the following enums should not be changed.\nconst enum ServiceWorkerStatus {\n  UNKNOWN = 0,\n  UNSUPPORTED = 1,\n  CONTROLLED = 2,\n  UNCONTROLLED = 3\n}\n\nexport enum VisibilityState {\n  UNKNOWN = 0,\n  VISIBLE = 1,\n  HIDDEN = 2\n}\n\nconst enum EffectiveConnectionType {\n  UNKNOWN = 0,\n  CONNECTION_SLOW_2G = 1,\n  CONNECTION_2G = 2,\n  CONNECTION_3G = 3,\n  CONNECTION_4G = 4\n}\n\ntype ConnectionType =\n  | 'bluetooth'\n  | 'cellular'\n  | 'ethernet'\n  | 'mixed'\n  | 'none'\n  | 'other'\n  | 'unknown'\n  | 'wifi';\n\n/**\n * NetworkInformation\n * This API is not well supported in all major browsers, so TypeScript does not provide types for it.\n *\n * ref: https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation\n */\ninterface NetworkInformation extends EventTarget {\n  readonly type: ConnectionType;\n}\n\ninterface NetworkInformationWithEffectiveType extends NetworkInformation {\n  readonly effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';\n}\n\ninterface NavigatorWithConnection extends Navigator {\n  readonly connection: NetworkInformationWithEffectiveType;\n}\n\nconst RESERVED_ATTRIBUTE_PREFIXES = ['firebase_', 'google_', 'ga_'];\nconst ATTRIBUTE_FORMAT_REGEX = new RegExp('^[a-zA-Z]\\\\w*$');\nconst MAX_ATTRIBUTE_NAME_LENGTH = 40;\nconst MAX_ATTRIBUTE_VALUE_LENGTH = 100;\n\nexport function getServiceWorkerStatus(): ServiceWorkerStatus {\n  const navigator = Api.getInstance().navigator;\n  if (navigator?.serviceWorker) {\n    if (navigator.serviceWorker.controller) {\n      return ServiceWorkerStatus.CONTROLLED;\n    } else {\n      return ServiceWorkerStatus.UNCONTROLLED;\n    }\n  } else {\n    return ServiceWorkerStatus.UNSUPPORTED;\n  }\n}\n\nexport function getVisibilityState(): VisibilityState {\n  const document = Api.getInstance().document;\n  const visibilityState = document.visibilityState;\n  switch (visibilityState) {\n    case 'visible':\n      return VisibilityState.VISIBLE;\n    case 'hidden':\n      return VisibilityState.HIDDEN;\n    default:\n      return VisibilityState.UNKNOWN;\n  }\n}\n\nexport function getEffectiveConnectionType(): EffectiveConnectionType {\n  const navigator = Api.getInstance().navigator;\n  const navigatorConnection = (navigator as NavigatorWithConnection).connection;\n  const effectiveType =\n    navigatorConnection && navigatorConnection.effectiveType;\n  switch (effectiveType) {\n    case 'slow-2g':\n      return EffectiveConnectionType.CONNECTION_SLOW_2G;\n    case '2g':\n      return EffectiveConnectionType.CONNECTION_2G;\n    case '3g':\n      return EffectiveConnectionType.CONNECTION_3G;\n    case '4g':\n      return EffectiveConnectionType.CONNECTION_4G;\n    default:\n      return EffectiveConnectionType.UNKNOWN;\n  }\n}\n\nexport function isValidCustomAttributeName(name: string): boolean {\n  if (name.length === 0 || name.length > MAX_ATTRIBUTE_NAME_LENGTH) {\n    return false;\n  }\n  const matchesReservedPrefix = RESERVED_ATTRIBUTE_PREFIXES.some(prefix =>\n    name.startsWith(prefix)\n  );\n  return !matchesReservedPrefix && !!name.match(ATTRIBUTE_FORMAT_REGEX);\n}\n\nexport function isValidCustomAttributeValue(value: string): boolean {\n  return value.length !== 0 && value.length <= MAX_ATTRIBUTE_VALUE_LENGTH;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getAppId(firebaseApp: FirebaseApp): string {\n  const appId = firebaseApp.options?.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_APP_ID);\n  }\n  return appId;\n}\n\nexport function getProjectId(firebaseApp: FirebaseApp): string {\n  const projectId = firebaseApp.options?.projectId;\n  if (!projectId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_PROJECT_ID);\n  }\n  return projectId;\n}\n\nexport function getApiKey(firebaseApp: FirebaseApp): string {\n  const apiKey = firebaseApp.options?.apiKey;\n  if (!apiKey) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_API_KEY);\n  }\n  return apiKey;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n  CONFIG_LOCAL_STORAGE_KEY,\n  SDK_VERSION\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\n\nimport { Api } from './api_service';\nimport { getAuthTokenPromise } from './iid_service';\nimport { SettingsService } from './settings_service';\nimport { Performance<PERSON>ontroller } from '../controllers/perf';\nimport { getProjectId, getApiKey, getAppId } from '../utils/app_utils';\n\nconst REMOTE_CONFIG_SDK_VERSION = '0.0.1';\n\ninterface SecondaryConfig {\n  loggingEnabled?: boolean;\n  logSource?: number;\n  logEndPointUrl?: string;\n  transportKey?: string;\n  tracesSamplingRate?: number;\n  networkRequestsSamplingRate?: number;\n}\n\n// These values will be used if the remote config object is successfully\n// retrieved, but the template does not have these fields.\nconst DEFAULT_CONFIGS: SecondaryConfig = {\n  loggingEnabled: true\n};\n\n/* eslint-disable camelcase */\ninterface RemoteConfigTemplate {\n  fpr_enabled?: string;\n  fpr_log_source?: string;\n  fpr_log_endpoint_url?: string;\n  fpr_log_transport_key?: string;\n  fpr_log_transport_web_percent?: string;\n  fpr_vc_network_request_sampling_rate?: string;\n  fpr_vc_trace_sampling_rate?: string;\n  fpr_vc_session_sampling_rate?: string;\n}\n/* eslint-enable camelcase */\n\ninterface RemoteConfigResponse {\n  entries?: RemoteConfigTemplate;\n  state?: string;\n}\n\nconst FIS_AUTH_PREFIX = 'FIREBASE_INSTALLATIONS_AUTH';\n\nexport function getConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<void> {\n  const config = getStoredConfig();\n  if (config) {\n    processConfig(config);\n    return Promise.resolve();\n  }\n\n  return getRemoteConfig(performanceController, iid)\n    .then(processConfig)\n    .then(\n      config => storeConfig(config),\n      /** Do nothing for error, use defaults set in settings service. */\n      () => {}\n    );\n}\n\nfunction getStoredConfig(): RemoteConfigResponse | undefined {\n  const localStorage = Api.getInstance().localStorage;\n  if (!localStorage) {\n    return;\n  }\n  const expiryString = localStorage.getItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY);\n  if (!expiryString || !configValid(expiryString)) {\n    return;\n  }\n\n  const configStringified = localStorage.getItem(CONFIG_LOCAL_STORAGE_KEY);\n  if (!configStringified) {\n    return;\n  }\n  try {\n    const configResponse: RemoteConfigResponse = JSON.parse(configStringified);\n    return configResponse;\n  } catch {\n    return;\n  }\n}\n\nfunction storeConfig(config: RemoteConfigResponse | undefined): void {\n  const localStorage = Api.getInstance().localStorage;\n  if (!config || !localStorage) {\n    return;\n  }\n\n  localStorage.setItem(CONFIG_LOCAL_STORAGE_KEY, JSON.stringify(config));\n  localStorage.setItem(\n    CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n    String(\n      Date.now() +\n        SettingsService.getInstance().configTimeToLive * 60 * 60 * 1000\n    )\n  );\n}\n\nconst COULD_NOT_GET_CONFIG_MSG =\n  'Could not fetch config, will use default configs';\n\nfunction getRemoteConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<RemoteConfigResponse | undefined> {\n  // Perf needs auth token only to retrieve remote config.\n  return getAuthTokenPromise(performanceController.installations)\n    .then(authToken => {\n      const projectId = getProjectId(performanceController.app);\n      const apiKey = getApiKey(performanceController.app);\n      const configEndPoint = `https://firebaseremoteconfig.googleapis.com/v1/projects/${projectId}/namespaces/fireperf:fetch?key=${apiKey}`;\n      const request = new Request(configEndPoint, {\n        method: 'POST',\n        headers: { Authorization: `${FIS_AUTH_PREFIX} ${authToken}` },\n        /* eslint-disable camelcase */\n        body: JSON.stringify({\n          app_instance_id: iid,\n          app_instance_id_token: authToken,\n          app_id: getAppId(performanceController.app),\n          app_version: SDK_VERSION,\n          sdk_version: REMOTE_CONFIG_SDK_VERSION\n        })\n        /* eslint-enable camelcase */\n      });\n      return fetch(request).then(response => {\n        if (response.ok) {\n          return response.json() as RemoteConfigResponse;\n        }\n        // In case response is not ok. This will be caught by catch.\n        throw ERROR_FACTORY.create(ErrorCode.RC_NOT_OK);\n      });\n    })\n    .catch(() => {\n      consoleLogger.info(COULD_NOT_GET_CONFIG_MSG);\n      return undefined;\n    });\n}\n\n/**\n * Processes config coming either from calling RC or from local storage.\n * This method only runs if call is successful or config in storage\n * is valid.\n */\nfunction processConfig(\n  config?: RemoteConfigResponse\n): RemoteConfigResponse | undefined {\n  if (!config) {\n    return config;\n  }\n  const settingsServiceInstance = SettingsService.getInstance();\n  const entries = config.entries || {};\n  if (entries.fpr_enabled !== undefined) {\n    // TODO: Change the assignment of loggingEnabled once the received type is\n    // known.\n    settingsServiceInstance.loggingEnabled =\n      String(entries.fpr_enabled) === 'true';\n  } else if (DEFAULT_CONFIGS.loggingEnabled !== undefined) {\n    // Config retrieved successfully, but there is no fpr_enabled in template.\n    // Use secondary configs value.\n    settingsServiceInstance.loggingEnabled = DEFAULT_CONFIGS.loggingEnabled;\n  }\n  if (entries.fpr_log_source) {\n    settingsServiceInstance.logSource = Number(entries.fpr_log_source);\n  } else if (DEFAULT_CONFIGS.logSource) {\n    settingsServiceInstance.logSource = DEFAULT_CONFIGS.logSource;\n  }\n\n  if (entries.fpr_log_endpoint_url) {\n    settingsServiceInstance.logEndPointUrl = entries.fpr_log_endpoint_url;\n  } else if (DEFAULT_CONFIGS.logEndPointUrl) {\n    settingsServiceInstance.logEndPointUrl = DEFAULT_CONFIGS.logEndPointUrl;\n  }\n\n  // Key from Remote Config has to be non-empty string, otherwise use local value.\n  if (entries.fpr_log_transport_key) {\n    settingsServiceInstance.transportKey = entries.fpr_log_transport_key;\n  } else if (DEFAULT_CONFIGS.transportKey) {\n    settingsServiceInstance.transportKey = DEFAULT_CONFIGS.transportKey;\n  }\n\n  if (entries.fpr_vc_network_request_sampling_rate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate = Number(\n      entries.fpr_vc_network_request_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.networkRequestsSamplingRate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate =\n      DEFAULT_CONFIGS.networkRequestsSamplingRate;\n  }\n  if (entries.fpr_vc_trace_sampling_rate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate = Number(\n      entries.fpr_vc_trace_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.tracesSamplingRate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate =\n      DEFAULT_CONFIGS.tracesSamplingRate;\n  }\n  // Set the per session trace and network logging flags.\n  settingsServiceInstance.logTraceAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.tracesSamplingRate\n  );\n  settingsServiceInstance.logNetworkAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.networkRequestsSamplingRate\n  );\n  return config;\n}\n\nfunction configValid(expiry: string): boolean {\n  return Number(expiry) > Date.now();\n}\n\nfunction shouldLogAfterSampling(samplingRate: number): boolean {\n  return Math.random() <= samplingRate;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIidPromise } from './iid_service';\nimport { getConfig } from './remote_config_service';\nimport { Api } from './api_service';\nimport { PerformanceController } from '../controllers/perf';\n\nconst enum InitializationStatus {\n  notInitialized = 1,\n  initializationPending,\n  initialized\n}\n\nlet initializationStatus = InitializationStatus.notInitialized;\n\nlet initializationPromise: Promise<void> | undefined;\n\nexport function getInitializationPromise(\n  performanceController: PerformanceController\n): Promise<void> {\n  initializationStatus = InitializationStatus.initializationPending;\n\n  initializationPromise =\n    initializationPromise || initializePerf(performanceController);\n\n  return initializationPromise;\n}\n\nexport function isPerfInitialized(): boolean {\n  return initializationStatus === InitializationStatus.initialized;\n}\n\nfunction initializePerf(\n  performanceController: PerformanceController\n): Promise<void> {\n  return getDocumentReadyComplete()\n    .then(() => getIidPromise(performanceController.installations))\n    .then(iid => getConfig(performanceController, iid))\n    .then(\n      () => changeInitializationStatus(),\n      () => changeInitializationStatus()\n    );\n}\n\n/**\n * Returns a promise which resolves whenever the document readystate is complete or\n * immediately if it is called after page load complete.\n */\nfunction getDocumentReadyComplete(): Promise<void> {\n  const document = Api.getInstance().document;\n  return new Promise(resolve => {\n    if (document && document.readyState !== 'complete') {\n      const handler = (): void => {\n        if (document.readyState === 'complete') {\n          document.removeEventListener('readystatechange', handler);\n          resolve();\n        }\n      };\n      document.addEventListener('readystatechange', handler);\n    } else {\n      resolve();\n    }\n  });\n}\n\nfunction changeInitializationStatus(): void {\n  initializationStatus = InitializationStatus.initialized;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsService } from './settings_service';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst DEFAULT_SEND_INTERVAL_MS = 10 * 1000;\nconst INITIAL_SEND_TIME_DELAY_MS = 5.5 * 1000;\nconst MAX_EVENT_COUNT_PER_REQUEST = 1000;\nconst DEFAULT_REMAINING_TRIES = 3;\n\nlet remainingTries = DEFAULT_REMAINING_TRIES;\n\ninterface BatchEvent {\n  message: string;\n  eventTime: number;\n}\n\n/* eslint-disable camelcase */\n// CC/Fl accepted log format.\ninterface TransportBatchLogFormat {\n  request_time_ms: string;\n  client_info: ClientInfo;\n  log_source: number;\n  log_event: Log[];\n}\n\ninterface ClientInfo {\n  client_type: number;\n  js_client_info: {};\n}\n\ninterface Log {\n  source_extension_json_proto3: string;\n  event_time_ms: string;\n}\n/* eslint-enable camelcase */\n\nlet queue: BatchEvent[] = [];\n\nlet isTransportSetup: boolean = false;\n\nexport function setupTransportService(): void {\n  if (!isTransportSetup) {\n    processQueue(INITIAL_SEND_TIME_DELAY_MS);\n    isTransportSetup = true;\n  }\n}\n\n/**\n * Utilized by testing to clean up message queue and un-initialize transport service.\n */\nexport function resetTransportService(): void {\n  isTransportSetup = false;\n  queue = [];\n}\n\nfunction processQueue(timeOffset: number): void {\n  setTimeout(() => {\n    // If there is no remainingTries left, stop retrying.\n    if (remainingTries <= 0) {\n      return;\n    }\n\n    if (queue.length > 0) {\n      dispatchQueueEvents();\n    }\n    processQueue(DEFAULT_SEND_INTERVAL_MS);\n  }, timeOffset);\n}\n\nfunction dispatchQueueEvents(): void {\n  // Extract events up to the maximum cap of single logRequest from top of \"official queue\".\n  // The staged events will be used for current logRequest attempt, remaining events will be kept\n  // for next attempt.\n  const staged = queue.splice(0, MAX_EVENT_COUNT_PER_REQUEST);\n\n  /* eslint-disable camelcase */\n  // We will pass the JSON serialized event to the backend.\n  const log_event: Log[] = staged.map(evt => ({\n    source_extension_json_proto3: evt.message,\n    event_time_ms: String(evt.eventTime)\n  }));\n\n  const data: TransportBatchLogFormat = {\n    request_time_ms: String(Date.now()),\n    client_info: {\n      client_type: 1, // 1 is JS\n      js_client_info: {}\n    },\n    log_source: SettingsService.getInstance().logSource,\n    log_event\n  };\n  /* eslint-enable camelcase */\n\n  postToFlEndpoint(data)\n    .then(() => {\n      remainingTries = DEFAULT_REMAINING_TRIES;\n    })\n    .catch(() => {\n      // If the request fails for some reason, add the events that were attempted\n      // back to the primary queue to retry later.\n      queue = [...staged, ...queue];\n      remainingTries--;\n      consoleLogger.info(`Tries left: ${remainingTries}.`);\n      processQueue(DEFAULT_SEND_INTERVAL_MS);\n    });\n}\n\nfunction postToFlEndpoint(data: TransportBatchLogFormat): Promise<void> {\n  const flTransportFullUrl =\n    SettingsService.getInstance().getFlTransportFullUrl();\n  const body = JSON.stringify(data);\n\n  return navigator.sendBeacon && navigator.sendBeacon(flTransportFullUrl, body)\n    ? Promise.resolve()\n    : fetch(flTransportFullUrl, {\n        method: 'POST',\n        body,\n        keepalive: true\n      }).then();\n}\n\nfunction addToQueue(evt: BatchEvent): void {\n  if (!evt.eventTime || !evt.message) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_CC_LOG);\n  }\n  // Add the new event to the queue.\n  queue = [...queue, evt];\n}\n\n/** Log handler for cc service to send the performance logs to the server. */\nexport function transportHandler(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  serializer: (...args: any[]) => string\n): (...args: unknown[]) => void {\n  return (...args) => {\n    const message = serializer(...args);\n    addToQueue({\n      message,\n      eventTime: Date.now()\n    });\n  };\n}\n\n/**\n * Force flush the queued events. Useful at page unload time to ensure all\n * events are uploaded.\n */\nexport function flushQueuedEvents(): void {\n  while (queue.length > 0) {\n    dispatchQueueEvents();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIid } from './iid_service';\nimport { NetworkRequest } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { Api } from './api_service';\nimport { SettingsService } from './settings_service';\nimport {\n  getServiceWorkerStatus,\n  getVisibilityState,\n  getEffectiveConnectionType\n} from '../utils/attributes_utils';\nimport {\n  isPerfInitialized,\n  getInitializationPromise\n} from './initialization_service';\nimport { transport<PERSON><PERSON><PERSON>, flushQueuedEvents } from './transport_service';\nimport { SDK_VERSION } from '../constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { getAppId } from '../utils/app_utils';\n\nconst enum ResourceType {\n  NetworkRequest,\n  Trace\n}\n\n/* eslint-disable camelcase */\ninterface ApplicationInfo {\n  google_app_id: string;\n  app_instance_id?: string;\n  web_app_info: WebAppInfo;\n  application_process_state: number;\n}\n\ninterface WebAppInfo {\n  sdk_version: string;\n  page_url: string;\n  service_worker_status: number;\n  visibility_state: number;\n  effective_connection_type: number;\n}\n\ninterface PerfNetworkLog {\n  application_info: ApplicationInfo;\n  network_request_metric: NetworkRequestMetric;\n}\n\ninterface PerfTraceLog {\n  application_info: ApplicationInfo;\n  trace_metric: TraceMetric;\n}\n\ninterface NetworkRequestMetric {\n  url: string;\n  http_method: number;\n  http_response_code: number;\n  response_payload_bytes?: number;\n  client_start_time_us?: number;\n  time_to_response_initiated_us?: number;\n  time_to_response_completed_us?: number;\n}\n\ninterface TraceMetric {\n  name: string;\n  is_auto: boolean;\n  client_start_time_us: number;\n  duration_us: number;\n  counters?: { [key: string]: number };\n  custom_attributes?: { [key: string]: string };\n}\n\ninterface Logger {\n  send: (\n    resource: NetworkRequest | Trace,\n    resourceType: ResourceType\n  ) => void | undefined;\n  flush: () => void;\n}\n\nlet logger: Logger;\n//\n// This method is not called before initialization.\nfunction sendLog(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): void {\n  if (!logger) {\n    logger = {\n      send: transportHandler(serializer),\n      flush: flushQueuedEvents\n    };\n  }\n  logger.send(resource, resourceType);\n}\n\nexport function logTrace(trace: Trace): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log if trace is auto generated and instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled && trace.isAuto) {\n    return;\n  }\n  // Do not log if trace is custom and data collection is disabled.\n  if (!settingsService.dataCollectionEnabled && !trace.isAuto) {\n    return;\n  }\n  // Do not log if required apis are not available.\n  if (!Api.getInstance().requiredApisAvailable()) {\n    return;\n  }\n\n  if (isPerfInitialized()) {\n    sendTraceLog(trace);\n  } else {\n    // Custom traces can be used before the initialization but logging\n    // should wait until after.\n    getInitializationPromise(trace.performanceController).then(\n      () => sendTraceLog(trace),\n      () => sendTraceLog(trace)\n    );\n  }\n}\n\nexport function flushLogs(): void {\n  if (logger) {\n    logger.flush();\n  }\n}\n\nfunction sendTraceLog(trace: Trace): void {\n  if (!getIid()) {\n    return;\n  }\n\n  const settingsService = SettingsService.getInstance();\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logTraceAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(trace, ResourceType.Trace);\n}\n\nexport function logNetworkRequest(networkRequest: NetworkRequest): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log network requests if instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled) {\n    return;\n  }\n\n  // Do not log the js sdk's call to transport service domain to avoid unnecessary cycle.\n  // Need to blacklist both old and new endpoints to avoid migration gap.\n  const networkRequestUrl = networkRequest.url;\n\n  // Blacklist old log endpoint and new transport endpoint.\n  // Because Performance SDK doesn't instrument requests sent from SDK itself.\n  const logEndpointUrl = settingsService.logEndPointUrl.split('?')[0];\n  const flEndpointUrl = settingsService.flTransportEndpointUrl.split('?')[0];\n  if (\n    networkRequestUrl === logEndpointUrl ||\n    networkRequestUrl === flEndpointUrl\n  ) {\n    return;\n  }\n\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logNetworkAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(networkRequest, ResourceType.NetworkRequest);\n}\n\nfunction serializer(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): string {\n  if (resourceType === ResourceType.NetworkRequest) {\n    return serializeNetworkRequest(resource as NetworkRequest);\n  }\n  return serializeTrace(resource as Trace);\n}\n\nfunction serializeNetworkRequest(networkRequest: NetworkRequest): string {\n  const networkRequestMetric: NetworkRequestMetric = {\n    url: networkRequest.url,\n    http_method: networkRequest.httpMethod || 0,\n    http_response_code: 200,\n    response_payload_bytes: networkRequest.responsePayloadBytes,\n    client_start_time_us: networkRequest.startTimeUs,\n    time_to_response_initiated_us: networkRequest.timeToResponseInitiatedUs,\n    time_to_response_completed_us: networkRequest.timeToResponseCompletedUs\n  };\n  const perfMetric: PerfNetworkLog = {\n    application_info: getApplicationInfo(\n      networkRequest.performanceController.app\n    ),\n    network_request_metric: networkRequestMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction serializeTrace(trace: Trace): string {\n  const traceMetric: TraceMetric = {\n    name: trace.name,\n    is_auto: trace.isAuto,\n    client_start_time_us: trace.startTimeUs,\n    duration_us: trace.durationUs\n  };\n\n  if (Object.keys(trace.counters).length !== 0) {\n    traceMetric.counters = trace.counters;\n  }\n  const customAttributes = trace.getAttributes();\n  if (Object.keys(customAttributes).length !== 0) {\n    traceMetric.custom_attributes = customAttributes;\n  }\n\n  const perfMetric: PerfTraceLog = {\n    application_info: getApplicationInfo(trace.performanceController.app),\n    trace_metric: traceMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction getApplicationInfo(firebaseApp: FirebaseApp): ApplicationInfo {\n  return {\n    google_app_id: getAppId(firebaseApp),\n    app_instance_id: getIid(),\n    web_app_info: {\n      sdk_version: SDK_VERSION,\n      page_url: Api.getInstance().getUrl(),\n      service_worker_status: getServiceWorkerStatus(),\n      visibility_state: getVisibilityState(),\n      effective_connection_type: getEffectiveConnectionType()\n    },\n    application_process_state: 0\n  };\n}\n\n/* eslint-enable camelcase */\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\nimport { logNetworkRequest } from '../services/perf_logger';\nimport { PerformanceController } from '../controllers/perf';\n\n// The order of values of this enum should not be changed.\nexport const enum HttpMethod {\n  HTTP_METHOD_UNKNOWN = 0,\n  GET = 1,\n  PUT = 2,\n  POST = 3,\n  DELETE = 4,\n  HEAD = 5,\n  PATCH = 6,\n  OPTIONS = 7,\n  TRACE = 8,\n  CONNECT = 9\n}\n\n// Durations are in microseconds.\nexport interface NetworkRequest {\n  performanceController: PerformanceController;\n  url: string;\n  httpMethod?: HttpMethod;\n  requestPayloadBytes?: number;\n  responsePayloadBytes?: number;\n  httpResponseCode?: number;\n  responseContentType?: string;\n  startTimeUs?: number;\n  timeToRequestCompletedUs?: number;\n  timeToResponseInitiatedUs?: number;\n  timeToResponseCompletedUs?: number;\n}\n\nexport function createNetworkRequestEntry(\n  performanceController: PerformanceController,\n  entry: PerformanceEntry\n): void {\n  const performanceEntry = entry as PerformanceResourceTiming;\n  if (!performanceEntry || performanceEntry.responseStart === undefined) {\n    return;\n  }\n  const timeOrigin = Api.getInstance().getTimeOrigin();\n  const startTimeUs = Math.floor(\n    (performanceEntry.startTime + timeOrigin) * 1000\n  );\n  const timeToResponseInitiatedUs = performanceEntry.responseStart\n    ? Math.floor(\n        (performanceEntry.responseStart - performanceEntry.startTime) * 1000\n      )\n    : undefined;\n  const timeToResponseCompletedUs = Math.floor(\n    (performanceEntry.responseEnd - performanceEntry.startTime) * 1000\n  );\n  // Remove the query params from logged network request url.\n  const url = performanceEntry.name && performanceEntry.name.split('?')[0];\n  const networkRequest: NetworkRequest = {\n    performanceController,\n    url,\n    responsePayloadBytes: performanceEntry.transferSize,\n    startTimeUs,\n    timeToResponseInitiatedUs,\n    timeToResponseCompletedUs\n  };\n\n  logNetworkRequest(networkRequest);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst MAX_METRIC_NAME_LENGTH = 100;\nconst RESERVED_AUTO_PREFIX = '_';\nconst oobMetrics = [\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME\n];\n\n/**\n * Returns true if the metric is custom and does not start with reserved prefix, or if\n * the metric is one of out of the box page load trace metrics.\n */\nexport function isValidMetricName(name: string, traceName?: string): boolean {\n  if (name.length === 0 || name.length > MAX_METRIC_NAME_LENGTH) {\n    return false;\n  }\n  return (\n    (traceName &&\n      traceName.startsWith(OOB_TRACE_PAGE_LOAD_PREFIX) &&\n      oobMetrics.indexOf(name) > -1) ||\n    !name.startsWith(RESERVED_AUTO_PREFIX)\n  );\n}\n\n/**\n * Converts the provided value to an integer value to be used in case of a metric.\n * @param providedValue Provided number value of the metric that needs to be converted to an integer.\n *\n * @returns Converted integer number to be set for the metric.\n */\nexport function convertMetricValueToInteger(providedValue: number): number {\n  const valueAsInteger: number = Math.floor(providedValue);\n  if (valueAsInteger < providedValue) {\n    consoleLogger.info(\n      `Metric value should be an Integer, setting the value as : ${valueAsInteger}.`\n    );\n  }\n  return valueAsInteger;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  TRACE_START_MARK_PREFIX,\n  TRACE_STOP_MARK_PREFIX,\n  TRACE_MEASURE_PREFIX,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME\n} from '../constants';\nimport { Api } from '../services/api_service';\nimport { logTrace, flushLogs } from '../services/perf_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport {\n  isValidCustomAttributeName,\n  isValidCustomAttributeValue\n} from '../utils/attributes_utils';\nimport {\n  isValidMetricName,\n  convertMetricValueToInteger\n} from '../utils/metric_utils';\nimport { PerformanceTrace } from '../public_types';\nimport { PerformanceController } from '../controllers/perf';\nimport { CoreVitalMetric, WebVitalMetrics } from './web_vitals';\n\nconst enum TraceState {\n  UNINITIALIZED = 1,\n  RUNNING,\n  TERMINATED\n}\n\nexport class Trace implements PerformanceTrace {\n  private state: TraceState = TraceState.UNINITIALIZED;\n  startTimeUs!: number;\n  durationUs!: number;\n  private customAttributes: { [key: string]: string } = {};\n  counters: { [counterName: string]: number } = {};\n  private api = Api.getInstance();\n  private randomId = Math.floor(Math.random() * 1000000);\n  private traceStartMark!: string;\n  private traceStopMark!: string;\n  private traceMeasure!: string;\n\n  /**\n   * @param performanceController The performance controller running.\n   * @param name The name of the trace.\n   * @param isAuto If the trace is auto-instrumented.\n   * @param traceMeasureName The name of the measure marker in user timing specification. This field\n   * is only set when the trace is built for logging when the user directly uses the user timing\n   * api (performance.mark and performance.measure).\n   */\n  constructor(\n    readonly performanceController: PerformanceController,\n    readonly name: string,\n    readonly isAuto = false,\n    traceMeasureName?: string\n  ) {\n    if (!this.isAuto) {\n      this.traceStartMark = `${TRACE_START_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceStopMark = `${TRACE_STOP_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceMeasure =\n        traceMeasureName ||\n        `${TRACE_MEASURE_PREFIX}-${this.randomId}-${this.name}`;\n\n      if (traceMeasureName) {\n        // For the case of direct user timing traces, no start stop will happen. The measure object\n        // is already available.\n        this.calculateTraceMetrics();\n      }\n    }\n  }\n\n  /**\n   * Starts a trace. The measurement of the duration starts at this point.\n   */\n  start(): void {\n    if (this.state !== TraceState.UNINITIALIZED) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STARTED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.api.mark(this.traceStartMark);\n    this.state = TraceState.RUNNING;\n  }\n\n  /**\n   * Stops the trace. The measurement of the duration of the trace stops at this point and trace\n   * is logged.\n   */\n  stop(): void {\n    if (this.state !== TraceState.RUNNING) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STOPPED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.state = TraceState.TERMINATED;\n    this.api.mark(this.traceStopMark);\n    this.api.measure(\n      this.traceMeasure,\n      this.traceStartMark,\n      this.traceStopMark\n    );\n    this.calculateTraceMetrics();\n    logTrace(this);\n  }\n\n  /**\n   * Records a trace with predetermined values. If this method is used a trace is created and logged\n   * directly. No need to use start and stop methods.\n   * @param startTime Trace start time since epoch in millisec\n   * @param duration The duration of the trace in millisec\n   * @param options An object which can optionally hold maps of custom metrics and custom attributes\n   */\n  record(\n    startTime: number,\n    duration: number,\n    options?: {\n      metrics?: { [key: string]: number };\n      attributes?: { [key: string]: string };\n    }\n  ): void {\n    if (startTime <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_START_TIME, {\n        traceName: this.name\n      });\n    }\n    if (duration <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_DURATION, {\n        traceName: this.name\n      });\n    }\n\n    this.durationUs = Math.floor(duration * 1000);\n    this.startTimeUs = Math.floor(startTime * 1000);\n    if (options && options.attributes) {\n      this.customAttributes = { ...options.attributes };\n    }\n    if (options && options.metrics) {\n      for (const metricName of Object.keys(options.metrics)) {\n        if (!isNaN(Number(options.metrics[metricName]))) {\n          this.counters[metricName] = Math.floor(\n            Number(options.metrics[metricName])\n          );\n        }\n      }\n    }\n    logTrace(this);\n  }\n\n  /**\n   * Increments a custom metric by a certain number or 1 if number not specified. Will create a new\n   * custom metric if one with the given name does not exist. The value will be floored down to an\n   * integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Increment by value\n   */\n  incrementMetric(counter: string, numAsInteger = 1): void {\n    if (this.counters[counter] === undefined) {\n      this.putMetric(counter, numAsInteger);\n    } else {\n      this.putMetric(counter, this.counters[counter] + numAsInteger);\n    }\n  }\n\n  /**\n   * Sets a custom metric to a specified value. Will create a new custom metric if one with the\n   * given name does not exist. The value will be floored down to an integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Set custom metric to this value\n   */\n  putMetric(counter: string, numAsInteger: number): void {\n    if (isValidMetricName(counter, this.name)) {\n      this.counters[counter] = convertMetricValueToInteger(numAsInteger ?? 0);\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_CUSTOM_METRIC_NAME, {\n        customMetricName: counter\n      });\n    }\n  }\n\n  /**\n   * Returns the value of the custom metric by that name. If a custom metric with that name does\n   * not exist will return zero.\n   * @param counter\n   */\n  getMetric(counter: string): number {\n    return this.counters[counter] || 0;\n  }\n\n  /**\n   * Sets a custom attribute of a trace to a certain value.\n   * @param attr\n   * @param value\n   */\n  putAttribute(attr: string, value: string): void {\n    const isValidName = isValidCustomAttributeName(attr);\n    const isValidValue = isValidCustomAttributeValue(value);\n    if (isValidName && isValidValue) {\n      this.customAttributes[attr] = value;\n      return;\n    }\n    // Throw appropriate error when the attribute name or value is invalid.\n    if (!isValidName) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_NAME, {\n        attributeName: attr\n      });\n    }\n    if (!isValidValue) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_VALUE, {\n        attributeValue: value\n      });\n    }\n  }\n\n  /**\n   * Retrieves the value a custom attribute of a trace is set to.\n   * @param attr\n   */\n  getAttribute(attr: string): string | undefined {\n    return this.customAttributes[attr];\n  }\n\n  removeAttribute(attr: string): void {\n    if (this.customAttributes[attr] === undefined) {\n      return;\n    }\n    delete this.customAttributes[attr];\n  }\n\n  getAttributes(): { [key: string]: string } {\n    return { ...this.customAttributes };\n  }\n\n  private setStartTime(startTime: number): void {\n    this.startTimeUs = startTime;\n  }\n\n  private setDuration(duration: number): void {\n    this.durationUs = duration;\n  }\n\n  /**\n   * Calculates and assigns the duration and start time of the trace using the measure performance\n   * entry.\n   */\n  private calculateTraceMetrics(): void {\n    const perfMeasureEntries = this.api.getEntriesByName(this.traceMeasure);\n    const perfMeasureEntry = perfMeasureEntries && perfMeasureEntries[0];\n    if (perfMeasureEntry) {\n      this.durationUs = Math.floor(perfMeasureEntry.duration * 1000);\n      this.startTimeUs = Math.floor(\n        (perfMeasureEntry.startTime + this.api.getTimeOrigin()) * 1000\n      );\n    }\n  }\n\n  /**\n   * @param navigationTimings A single element array which contains the navigationTIming object of\n   * the page load\n   * @param paintTimings A array which contains paintTiming object of the page load\n   * @param firstInputDelay First input delay in millisec\n   */\n  static createOobTrace(\n    performanceController: PerformanceController,\n    navigationTimings: PerformanceNavigationTiming[],\n    paintTimings: PerformanceEntry[],\n    webVitalMetrics: WebVitalMetrics,\n    firstInputDelay?: number\n  ): void {\n    const route = Api.getInstance().getUrl();\n    if (!route) {\n      return;\n    }\n    const trace = new Trace(\n      performanceController,\n      OOB_TRACE_PAGE_LOAD_PREFIX + route,\n      true\n    );\n    const timeOriginUs = Math.floor(Api.getInstance().getTimeOrigin() * 1000);\n    trace.setStartTime(timeOriginUs);\n\n    // navigationTimings includes only one element.\n    if (navigationTimings && navigationTimings[0]) {\n      trace.setDuration(Math.floor(navigationTimings[0].duration * 1000));\n      trace.putMetric(\n        'domInteractive',\n        Math.floor(navigationTimings[0].domInteractive * 1000)\n      );\n      trace.putMetric(\n        'domContentLoadedEventEnd',\n        Math.floor(navigationTimings[0].domContentLoadedEventEnd * 1000)\n      );\n      trace.putMetric(\n        'loadEventEnd',\n        Math.floor(navigationTimings[0].loadEventEnd * 1000)\n      );\n    }\n\n    const FIRST_PAINT = 'first-paint';\n    const FIRST_CONTENTFUL_PAINT = 'first-contentful-paint';\n    if (paintTimings) {\n      const firstPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_PAINT\n      );\n      if (firstPaint && firstPaint.startTime) {\n        trace.putMetric(\n          FIRST_PAINT_COUNTER_NAME,\n          Math.floor(firstPaint.startTime * 1000)\n        );\n      }\n      const firstContentfulPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_CONTENTFUL_PAINT\n      );\n      if (firstContentfulPaint && firstContentfulPaint.startTime) {\n        trace.putMetric(\n          FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n          Math.floor(firstContentfulPaint.startTime * 1000)\n        );\n      }\n\n      if (firstInputDelay) {\n        trace.putMetric(\n          FIRST_INPUT_DELAY_COUNTER_NAME,\n          Math.floor(firstInputDelay * 1000)\n        );\n      }\n    }\n\n    this.addWebVitalMetric(\n      trace,\n      LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n      LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.lcp\n    );\n    this.addWebVitalMetric(\n      trace,\n      CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n      CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME,\n      webVitalMetrics.cls\n    );\n    this.addWebVitalMetric(\n      trace,\n      INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n      INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.inp\n    );\n\n    // Page load logs are sent at unload time and so should be logged and\n    // flushed immediately.\n    logTrace(trace);\n    flushLogs();\n  }\n\n  static addWebVitalMetric(\n    trace: Trace,\n    metricKey: string,\n    attributeKey: string,\n    metric?: CoreVitalMetric\n  ): void {\n    if (metric) {\n      trace.putMetric(metricKey, Math.floor(metric.value * 1000));\n      if (metric.elementAttribution) {\n        trace.putAttribute(attributeKey, metric.elementAttribution);\n      }\n    }\n  }\n\n  static createUserTimingTrace(\n    performanceController: PerformanceController,\n    measureName: string\n  ): void {\n    const trace = new Trace(\n      performanceController,\n      measureName,\n      false,\n      measureName\n    );\n    logTrace(trace);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution\n} from 'web-vitals/attribution';\n\nimport { TRACE_MEASURE_PREFIX } from '../constants';\nimport { PerformanceController } from '../controllers/perf';\nimport { createNetworkRequestEntry } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { WebVitalMetrics } from '../resources/web_vitals';\n\nimport { Api } from './api_service';\nimport { getIid } from './iid_service';\n\nlet webVitalMetrics: WebVitalMetrics = {};\nlet sentPageLoadTrace: boolean = false;\nlet firstInputDelay: number | undefined;\n\nexport function setupOobResources(\n  performanceController: PerformanceController\n): void {\n  // Do not initialize unless iid is available.\n  if (!getIid()) {\n    return;\n  }\n  // The load event might not have fired yet, and that means performance\n  // navigation timing object has a duration of 0. The setup should run after\n  // all current tasks in js queue.\n  setTimeout(() => setupOobTraces(performanceController), 0);\n  setTimeout(() => setupNetworkRequests(performanceController), 0);\n  setTimeout(() => setupUserTimingTraces(performanceController), 0);\n}\n\nfunction setupNetworkRequests(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  const resources = api.getEntriesByType('resource');\n  for (const resource of resources) {\n    createNetworkRequestEntry(performanceController, resource);\n  }\n  api.setupObserver('resource', entry =>\n    createNetworkRequestEntry(performanceController, entry)\n  );\n}\n\nfunction setupOobTraces(performanceController: PerformanceController): void {\n  const api = Api.getInstance();\n  // Better support for Safari\n  if ('onpagehide' in window) {\n    api.document.addEventListener('pagehide', () =>\n      sendOobTrace(performanceController)\n    );\n  } else {\n    api.document.addEventListener('unload', () =>\n      sendOobTrace(performanceController)\n    );\n  }\n  api.document.addEventListener('visibilitychange', () => {\n    if (api.document.visibilityState === 'hidden') {\n      sendOobTrace(performanceController);\n    }\n  });\n\n  if (api.onFirstInputDelay) {\n    api.onFirstInputDelay((fid: number) => {\n      firstInputDelay = fid;\n    });\n  }\n\n  api.onLCP((metric: LCPMetricWithAttribution) => {\n    webVitalMetrics.lcp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.element\n    };\n  });\n  api.onCLS((metric: CLSMetricWithAttribution) => {\n    webVitalMetrics.cls = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.largestShiftTarget\n    };\n  });\n  api.onINP((metric: INPMetricWithAttribution) => {\n    webVitalMetrics.inp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.interactionTarget\n    };\n  });\n}\n\nfunction setupUserTimingTraces(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  // Run through the measure performance entries collected up to this point.\n  const measures = api.getEntriesByType('measure');\n  for (const measure of measures) {\n    createUserTimingTrace(performanceController, measure);\n  }\n  // Setup an observer to capture the measures from this point on.\n  api.setupObserver('measure', entry =>\n    createUserTimingTrace(performanceController, entry)\n  );\n}\n\nfunction createUserTimingTrace(\n  performanceController: PerformanceController,\n  measure: PerformanceEntry\n): void {\n  const measureName = measure.name;\n  // Do not create a trace, if the user timing marks and measures are created by\n  // the sdk itself.\n  if (\n    measureName.substring(0, TRACE_MEASURE_PREFIX.length) ===\n    TRACE_MEASURE_PREFIX\n  ) {\n    return;\n  }\n  Trace.createUserTimingTrace(performanceController, measureName);\n}\n\nfunction sendOobTrace(performanceController: PerformanceController): void {\n  if (!sentPageLoadTrace) {\n    sentPageLoadTrace = true;\n    const api = Api.getInstance();\n    const navigationTimings = api.getEntriesByType(\n      'navigation'\n    ) as PerformanceNavigationTiming[];\n    const paintTimings = api.getEntriesByType('paint');\n\n    // On page unload web vitals may be updated so queue the oob trace creation\n    // so that these updates have time to be included.\n    setTimeout(() => {\n      Trace.createOobTrace(\n        performanceController,\n        navigationTimings,\n        paintTimings,\n        webVitalMetrics,\n        firstInputDelay\n      );\n    }, 0);\n  }\n}\n\n/**\n * This service will only export the page load trace once. This function allows\n * resetting it for unit tests\n */\nexport function resetForUnitTests(): void {\n  sentPageLoadTrace = false;\n  firstInputDelay = undefined;\n  webVitalMetrics = {};\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { setupOobResources } from '../services/oob_resources_service';\nimport { SettingsService } from '../services/settings_service';\nimport { getInitializationPromise } from '../services/initialization_service';\nimport { Api } from '../services/api_service';\nimport { FirebaseApp } from '@firebase/app';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { PerformanceSettings, FirebasePerformance } from '../public_types';\nimport { validateIndexedDBOpenable } from '@firebase/util';\nimport { setupTransportService } from '../services/transport_service';\nimport { consoleLogger } from '../utils/console_logger';\n\nexport class PerformanceController implements FirebasePerformance {\n  private initialized: boolean = false;\n\n  constructor(\n    readonly app: FirebaseApp,\n    readonly installations: _FirebaseInstallationsInternal\n  ) {}\n\n  /**\n   * This method *must* be called internally as part of creating a\n   * PerformanceController instance.\n   *\n   * Currently it's not possible to pass the settings object through the\n   * constructor using Components, so this method exists to be called with the\n   * desired settings, to ensure nothing is collected without the user's\n   * consent.\n   */\n  _init(settings?: PerformanceSettings): void {\n    if (this.initialized) {\n      return;\n    }\n\n    if (settings?.dataCollectionEnabled !== undefined) {\n      this.dataCollectionEnabled = settings.dataCollectionEnabled;\n    }\n    if (settings?.instrumentationEnabled !== undefined) {\n      this.instrumentationEnabled = settings.instrumentationEnabled;\n    }\n\n    if (Api.getInstance().requiredApisAvailable()) {\n      validateIndexedDBOpenable()\n        .then(isAvailable => {\n          if (isAvailable) {\n            setupTransportService();\n            getInitializationPromise(this).then(\n              () => setupOobResources(this),\n              () => setupOobResources(this)\n            );\n            this.initialized = true;\n          }\n        })\n        .catch(error => {\n          consoleLogger.info(`Environment doesn't support IndexedDB: ${error}`);\n        });\n    } else {\n      consoleLogger.info(\n        'Firebase Performance cannot start if the browser does not support ' +\n          '\"Fetch\" and \"Promise\", or cookies are disabled.'\n      );\n    }\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    SettingsService.getInstance().instrumentationEnabled = val;\n  }\n  get instrumentationEnabled(): boolean {\n    return SettingsService.getInstance().instrumentationEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    SettingsService.getInstance().dataCollectionEnabled = val;\n  }\n  get dataCollectionEnabled(): boolean {\n    return SettingsService.getInstance().dataCollectionEnabled;\n  }\n}\n", "/**\n * The Firebase Performance Monitoring Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebasePerformance,\n  PerformanceSettings,\n  PerformanceTrace\n} from './public_types';\nimport { ERROR_FACTORY, ErrorCode } from './utils/errors';\nimport { setupApi } from './services/api_service';\nimport { PerformanceController } from './controllers/perf';\nimport {\n  _registerComponent,\n  _getProvider,\n  registerVersion,\n  FirebaseApp,\n  getApp\n} from '@firebase/app';\nimport {\n  InstanceFactory,\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { Trace } from './resources/trace';\nimport '@firebase/installations';\nimport { deepEqual, getModularInstance } from '@firebase/util';\n\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @public\n */\nexport function getPerformance(\n  app: FirebaseApp = getApp()\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n  const perfInstance = provider.getImmediate() as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app. Can only be called once.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param settings - Optional settings for the {@link FirebasePerformance} instance.\n * @public\n */\nexport function initializePerformance(\n  app: FirebaseApp,\n  settings?: PerformanceSettings\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n\n  // throw if an instance was already created.\n  // It could happen if initializePerformance() is called more than once, or getPerformance() is called first.\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialSettings = provider.getOptions() as PerformanceSettings;\n    if (deepEqual(initialSettings, settings ?? {})) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n    }\n  }\n\n  const perfInstance = provider.initialize({\n    options: settings\n  }) as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a new `PerformanceTrace` instance.\n * @param performance - The {@link FirebasePerformance} instance to use.\n * @param name - The name of the trace.\n * @public\n */\nexport function trace(\n  performance: FirebasePerformance,\n  name: string\n): PerformanceTrace {\n  performance = getModularInstance(performance);\n  return new Trace(performance as PerformanceController, name);\n}\n\nconst factory: InstanceFactory<'performance'> = (\n  container: ComponentContainer,\n  { options: settings }: { options?: PerformanceSettings }\n) => {\n  // Dependencies\n  const app = container.getProvider('app').getImmediate();\n  const installations = container\n    .getProvider('installations-internal')\n    .getImmediate();\n\n  if (app.name !== DEFAULT_ENTRY_NAME) {\n    throw ERROR_FACTORY.create(ErrorCode.FB_NOT_DEFAULT);\n  }\n  if (typeof window === 'undefined') {\n    throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n  }\n  setupApi(window);\n  const perfInstance = new PerformanceController(app, installations);\n  perfInstance._init(settings);\n\n  return perfInstance;\n};\n\nfunction registerPerformance(): void {\n  _registerComponent(\n    new Component('performance', factory, ComponentType.PUBLIC)\n  );\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterPerformance();\n\nexport { FirebasePerformance, PerformanceSettings, PerformanceTrace };\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  FirebasePerformance,\n  // The PerformanceTrace type has not changed between modular and non-modular packages.\n  PerformanceTrace\n} from '@firebase/performance';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\n\nexport class PerformanceCompatImpl\n  implements FirebasePerformanceCompat, _FirebaseService\n{\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FirebasePerformance\n  ) {}\n\n  get instrumentationEnabled(): boolean {\n    return this._delegate.instrumentationEnabled;\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    this._delegate.instrumentationEnabled = val;\n  }\n\n  get dataCollectionEnabled(): boolean {\n    return this._delegate.dataCollectionEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    this._delegate.dataCollectionEnabled = val;\n  }\n\n  trace(traceName: string): PerformanceTrace {\n    return trace(this._delegate, traceName);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType\n} from '@firebase/component';\nimport { PerformanceCompatImpl } from './performance';\nimport { name as packageName, version } from '../package.json';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\n\nfunction registerPerformanceCompat(firebaseInstance: _FirebaseNamespace): void {\n  firebaseInstance.INTERNAL.registerComponent(\n    new Component(\n      'performance-compat',\n      performanceFactory,\n      ComponentType.PUBLIC\n    )\n  );\n\n  firebaseInstance.registerVersion(packageName, version);\n}\n\nfunction performanceFactory(\n  container: ComponentContainer\n): PerformanceCompatImpl {\n  const app = container.getProvider('app-compat').getImmediate();\n  // The following call will always succeed.\n  const performance = container.getProvider('performance').getImmediate();\n\n  return new PerformanceCompatImpl(app, performance);\n}\n\nregisterPerformanceCompat(firebase as _FirebaseNamespace);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    performance: {\n      (app?: FirebaseApp): FirebasePerformanceCompat;\n    };\n  }\n  interface FirebaseApp {\n    performance(): FirebasePerformanceCompat;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from './app';\nimport './performance';\nimport { name, version } from '../package.json';\n\nfirebase.registerVersion(name, version, 'compat-lite');\n\nexport default firebase;\n"], "names": ["stringToByteArray", "str", "out", "p", "i", "length", "c", "charCodeAt", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte1", "outByte2", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "decodeString", "bytes", "pos", "c1", "String", "fromCharCode", "c2", "u", "c3", "byteArrayToString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "base64urlEncodeWithoutPadding", "utf8Bytes", "base64Encode", "replace", "base64Decode", "e", "console", "error", "deepExtend", "target", "source", "Object", "Date", "getTime", "undefined", "prop", "hasOwnProperty", "getDefaultsFromGlobal", "self", "window", "global", "getGlobal", "__FIREBASE_DEFAULTS__", "getDefaults", "process", "env", "defaultsJsonString", "JSON", "parse", "getDefaultsFromEnvVariable", "document", "match", "cookie", "decoded", "getDefaultsFromCookie", "info", "getDefaultAppConfig", "_a", "config", "Deferred", "reject", "resolve", "promise", "Promise", "wrapCallback", "callback", "value", "catch", "isWebWorker", "WorkerGlobalScope", "isIndexedDBAvailable", "indexedDB", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "request", "open", "onsuccess", "result", "close", "deleteDatabase", "onupgradeneeded", "onerror", "message", "FirebaseError", "code", "customData", "super", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "PATTERN", "_", "key", "replaceTemplate", "fullMessage", "contains", "obj", "call", "deepEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "k", "includes", "aProp", "bProp", "isObject", "thing", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "DEFAULT_ENTRY_NAME", "Provider", "container", "component", "instances", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instancesOptions", "onInitCallbacks", "get", "identifier", "normalizedIdentifier", "normalizeInstanceIdentifier", "has", "deferred", "set", "isInitialized", "shouldAutoInitialize", "instance", "getOrInitializeService", "instanceIdentifier", "getImmediate", "options", "optional", "getComponent", "setComponent", "isComponentEager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "clearInstance", "delete", "services", "from", "values", "all", "filter", "map", "INTERNAL", "_delete", "isComponentSet", "getOptions", "initialize", "opts", "onInit", "existingCallbacks", "Set", "add", "existingInstance", "invokeOnInitCallbacks", "callbacks", "ComponentContainer", "providers", "addComponent", "provider", "get<PERSON><PERSON><PERSON>", "addOrOverwriteComponent", "getProviders", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "INFO", "warn", "WARN", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "logType", "args", "logLevel", "now", "toISOString", "method", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "receiver", "IDBTransaction", "objectStoreNames", "objectStore", "wrap", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "apply", "unwrap", "storeNames", "tx", "sort", "transformCachableValue", "done", "unlisten", "removeEventListener", "complete", "DOMException", "addEventListener", "cacheDonePromiseForTransaction", "object", "IDBObjectStore", "IDBIndex", "some", "Proxy", "IDBRequest", "success", "then", "promisifyRequest", "newValue", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "openPromise", "event", "oldVersion", "newVersion", "db", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "oldTraps", "PlatformLoggerServiceImpl", "getPlatformInfoString", "isVersionServiceProvider", "library", "logString", "logger", "PLATFORM_LOG_STRING", "name$q", "appName", "name$p", "appCompatName", "name$n", "analyticsName", "name$o", "analyticsCompatName", "name$l", "appCheckName", "name$m", "appCheckCompatName", "name$k", "authName", "name$j", "authCompatName", "name$i", "databaseName", "name$h", "dataconnectName", "name$g", "databaseCompatName", "name$f", "functionsName", "name$e", "functionsCompatName", "name$d", "installationsName", "name$c", "installationsCompatName", "name$b", "messagingName", "name$a", "messagingCompatName", "name$9", "performanceName", "name$8", "performanceCompatName", "name$7", "remoteConfigName", "name$6", "remoteConfigCompatName", "name$5$1", "storageName", "name$4$1", "storageCompatName", "name$3$1", "firestoreName", "name$1$2", "firestoreCompatName", "name$2$1", "aiName", "name$r", "packageName", "_apps", "_serverApps", "_components", "_addComponent", "app", "_registerComponent", "componentName", "serverApp", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "triggerHeartbeat", "_isFirebaseApp", "ERROR_FACTORY", "FirebaseAppImpl", "_isDeleted", "_options", "assign", "_config", "_name", "_automaticDataCollectionEnabled", "automaticDataCollectionEnabled", "_container", "checkDestroyed", "isDeleted", "validateTokenTTL", "base64Token", "tokenName", "second<PERSON><PERSON>", "split", "exp", "FirebaseServerAppImpl", "serverConfig", "<PERSON><PERSON><PERSON><PERSON>", "_serverConfig", "authIdToken", "appCheckToken", "_finalizationRegistry", "FinalizationRegistry", "automaticCleanup", "_refCount", "incRefCount", "releaseOnDeref", "registerVersion", "toJSON", "refCount", "register", "decRefCount", "deleteApp", "settings", "SDK_VERSION", "initializeApp", "rawConfig", "existingApp", "newApp", "cleanupProviders", "libraryKeyOrName", "variant", "libraryMismatch", "versionMismatch", "warning", "onLog", "logCallback", "customLogLevel", "level", "arg", "toString", "stringify", "ignored", "toLowerCase", "setUserLogHandler", "for<PERSON>ach", "inst", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "createObjectStore", "originalErrorMessage", "writeHeartbeatsToIndexedDB", "heartbeatObject", "put", "computeKey", "idbGetError", "appId", "HeartbeatServiceImpl", "_heartbeatsCache", "_storage", "HeartbeatStorageImpl", "_heartbeatsCachePromise", "read", "agent", "date", "getUTCDateString", "heartbeats", "_b", "lastSentHeartbeatDate", "singleDateHeartbeat", "earliestHeartbeatIdx", "earliestHeartbeatDate", "getEarliestHeartbeatIdx", "splice", "overwrite", "getHeartbeatsHeader", "heartbeatsToSend", "unsentEntries", "heartbeatsCache", "maxSize", "slice", "heartbeatEntry", "find", "hb", "dates", "countBytes", "pop", "extractHeartbeatsForHeader", "headerString", "substring", "_canUseIndexedDBPromise", "runIndexedDBEnvironmentCheck", "idbHeartbeatObject", "readHeartbeatsFromIndexedDB", "heartbeatsObject", "existingHeartbeatsObject", "clear", "_serverAppConfig", "appOptions", "nameObj", "nameString", "s", "reduce", "hash", "Math", "imul", "hashCode", "FirebaseAppLiteImpl", "_delegate", "firebase", "removeApp", "_getService", "_DEFAULT_ENTRY_NAME", "createFirebaseNamespaceCore", "firebaseAppImpl", "apps", "namespace", "__esModule", "modularAPIs.initializeApp", "appCompat", "modularAPIs.registerVersion", "modularAPIs.setLogLevel", "modularAPIs.onLog", "modularAPIs.SDK_VERSION", "registerComponent", "componentNameWithoutCompat", "modularAPIs._registerComponent", "serviceNamespace", "appArg", "bind", "useAsService", "modularAPIs", "modularAPIs._DEFAULT_ENTRY_NAME", "defineProperty", "createFirebaseNamespaceLite", "registerCoreComponents", "t", "n", "performance", "getEntriesByType", "responseStart", "r", "readyState", "domInteractive", "domContentLoadedEventStart", "domComplete", "nodeName", "nodeType", "toUpperCase", "id", "classList", "trim", "parentNode", "o", "persisted", "timeStamp", "activationStart", "f", "prerendering", "wasDiscarded", "rating", "delta", "concat", "floor", "random", "navigationType", "d", "PerformanceObserver", "supportedEntryTypes", "getEntries", "observe", "buffered", "l", "m", "requestAnimationFrame", "visibilityState", "v", "g", "h", "T", "E", "y", "S", "setTimeout", "firstHiddenTime", "L", "M", "D", "disconnect", "startTime", "max", "reportAllChanges", "C", "hadRecentInput", "takeRecords", "sources", "node", "largestShiftTarget", "largestShiftTime", "largestShiftValue", "largestShiftSource", "largestShiftEntry", "loadState", "attribution", "x", "I", "A", "interactionId", "min", "F", "interactionCount", "P", "durationThreshold", "B", "O", "R", "q", "H", "entryType", "duration", "latency", "N", "requestIdleCallback", "W", "z", "PerformanceEventTiming", "j", "U", "V", "G", "J", "K", "Q", "X", "Y", "size", "nt", "processingEnd", "abs", "renderTime", "processingStart", "rt", "interaction<PERSON>arget", "interactionTargetElement", "interactionType", "startsWith", "interactionTime", "nextPaintTime", "processedEventEntries", "longAnimationFrameEntries", "inputDelay", "processingDuration", "presentationDelay", "it", "at", "ot", "once", "capture", "timeToFirstByte", "resourceLoadDelay", "resourceLoadDuration", "elementRenderDelay", "url", "requestStart", "responseEnd", "element", "navigationEntry", "lcpEntry", "lcpResourceEntry", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "TOKEN_EXPIRATION_BUFFER", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "responseExpiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "errorData", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "fn", "sleep", "ms", "VALID_FID_PATTERN", "generateFid", "fidByteArray", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "fid", "b64String", "array", "substr", "encode", "test", "<PERSON><PERSON><PERSON>", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "channel", "broadcastChannel", "BroadcastChannel", "onmessage", "getBroadcastChannel", "postMessage", "broadcastFidChange", "OBJECT_STORE_NAME", "oldValue", "remove", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "entry", "registrationStatus", "clearTimedOutRequest", "updateOrCreateInstallationEntry", "entryWithPromise", "navigator", "onLine", "inProgressEntry", "registrationTime", "registeredInstallationEntry", "heartbeatServiceProvider", "endpoint", "heartbeatService", "heartbeatsHeader", "body", "authVersion", "sdkVersion", "fetch", "ok", "responseValue", "authToken", "createInstallationRequest", "registerInstallation", "waitUntilFidRegistration", "triggerRegistrationIfNecessary", "updateInstallationRequest", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenExpired", "isAuthTokenValid", "updateAuthTokenRequest", "waitUntilAuthTokenRequest", "inProgressAuthToken", "requestTime", "makeAuthTokenRequestInProgressEntry", "updatedInstallationEntry", "fetchAuthTokenFromServer", "getToken", "installationsImpl", "completeInstallationRegistration", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "internalFactory", "getId", "config<PERSON><PERSON><PERSON>", "keyName", "extractAppConfig", "TRACE_MEASURE_PREFIX", "OOB_TRACE_PAGE_LOAD_PREFIX", "FIRST_CONTENTFUL_PAINT_COUNTER_NAME", "FIRST_INPUT_DELAY_COUNTER_NAME", "LARGEST_CONTENTFUL_PAINT_METRIC_NAME", "INTERACTION_TO_NEXT_PAINT_METRIC_NAME", "CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME", "CONFIG_LOCAL_STORAGE_KEY", "CONFIG_EXPIRY_LOCAL_STORAGE_KEY", "SERVICE_NAME", "consoleLogger", "apiInstance", "windowInstance", "iid", "settingsServiceInstance", "Api", "windowLocation", "location", "cookieEnabled", "localStorage", "perfMetrics", "onFirstInputDelay", "onLCP", "vitalsOnLCP", "onINP", "vitalsOnINP", "onCLS", "vitalsOnCLS", "getUrl", "href", "mark", "measure", "measureName", "mark1", "mark2", "getEntriesByName", "getTime<PERSON>rigin", "<PERSON><PERSON><PERSON><PERSON>", "timing", "navigationStart", "requiredApisAvailable", "setupObserver", "list", "entryTypes", "getInstance", "getIid", "mergeStrings", "part1", "part2", "sizeDiff", "resultArray", "SettingsService", "instrumentationEnabled", "dataCollectionEnabled", "loggingEnabled", "tracesSamplingRate", "networkRequestsSamplingRate", "logEndPointUrl", "flTransportEndpointUrl", "transportKey", "logSource", "logTraceAfterSampling", "logNetworkAfterSampling", "configTimeToLive", "getFlTransportFullUrl", "VisibilityState", "RESERVED_ATTRIBUTE_PREFIXES", "ATTRIBUTE_FORMAT_REGEX", "RegExp", "getServiceWorkerStatus", "serviceWorker", "controller", "getVisibilityState", "VISIBLE", "HIDDEN", "UNKNOWN", "getEffectiveConnectionType", "navigatorConnection", "connection", "effectiveType", "getAppId", "firebaseApp", "REMOTE_CONFIG_SDK_VERSION", "DEFAULT_CONFIGS", "FIS_AUTH_PREFIX", "getConfig", "performanceController", "expiryString", "getItem", "expiry", "configStringified", "getStoredConfig", "processConfig", "installationsService", "authTokenPromise", "authTokenVal", "getAuthTokenPromise", "getProjectId", "getApi<PERSON>ey", "Request", "Authorization", "app_instance_id", "app_instance_id_token", "app_id", "app_version", "sdk_version", "COULD_NOT_GET_CONFIG_MSG", "getRemoteConfig", "setItem", "storeConfig", "fpr_enabled", "fpr_log_source", "fpr_log_endpoint_url", "fpr_log_transport_key", "fpr_vc_network_request_sampling_rate", "fpr_vc_trace_sampling_rate", "shouldLogAfterSampling", "samplingRate", "initializationPromise", "initializationStatus", "getInitializationPromise", "handler", "getDocumentReadyComplete", "iidPromise", "iidVal", "getIidPromise", "changeInitializationStatus", "initializePerf", "remainingTries", "queue", "isTransportSetup", "processQueue", "timeOffset", "dispatchQueueEvents", "staged", "log_event", "evt", "source_extension_json_proto3", "event_time_ms", "eventTime", "flTransportFullUrl", "sendBeacon", "keepalive", "postToFlEndpoint", "request_time_ms", "client_info", "client_type", "js_client_info", "log_source", "transportHandler", "serializer", "addToQueue", "flushQueuedEvents", "sendLog", "resource", "resourceType", "send", "flush", "logTrace", "trace", "settingsService", "isAuto", "sendTraceLog", "networkRequest", "networkRequestMetric", "http_method", "httpMethod", "http_response_code", "response_payload_bytes", "responsePayloadBytes", "client_start_time_us", "startTimeUs", "time_to_response_initiated_us", "timeToResponseInitiatedUs", "time_to_response_completed_us", "timeToResponseCompletedUs", "perfMetric", "application_info", "getApplicationInfo", "network_request_metric", "serializeNetworkRequest", "traceMetric", "is_auto", "duration_us", "durationUs", "counters", "customAttributes", "getAttributes", "custom_attributes", "trace_metric", "serializeTrace", "google_app_id", "web_app_info", "page_url", "service_worker_status", "visibility_state", "effective_connection_type", "application_process_state", "createNetworkRequestEntry", "performanceEntry", "networkRequestUrl", "logEndpointUrl", "flEndpointUrl", "logNetworkRequest", "transferSize", "oobMetrics", "Trace", "traceMeasureName", "state", "api", "randomId", "traceStartMark", "traceStopMark", "traceMeasure", "calculateTraceMetrics", "start", "traceName", "stop", "record", "attributes", "metrics", "metricName", "isNaN", "incrementMetric", "counter", "numAsInteger", "putMetric", "indexOf", "isValidMetricName", "customMetricName", "providedValue", "valueAsInteger", "convertMetricValueToInteger", "getMetric", "putAttribute", "attr", "isValidName", "prefix", "isValidCustomAttributeName", "isValidValue", "isValidCustomAttributeValue", "attributeName", "attributeValue", "getAttribute", "removeAttribute", "setStartTime", "setDuration", "perfMeasureEntries", "perfMeasureEntry", "createOobTrace", "navigationTimings", "paintTimings", "webVitalMetrics", "firstInputDelay", "route", "timeOriginUs", "domContentLoadedEventEnd", "loadEventEnd", "<PERSON><PERSON><PERSON><PERSON>", "paintObject", "firstContentful<PERSON><PERSON>t", "addWebVitalMetric", "lcp", "cls", "inp", "metricKey", "<PERSON><PERSON><PERSON>", "metric", "elementAttribution", "createUserTimingTrace", "sentPageLoadTrace", "setupOobResources", "sendOobTrace", "setupOobTraces", "resources", "setupNetworkRequests", "measures", "setupUserTimingTraces", "PerformanceController", "initialized", "_init", "isAvailable", "setupApi", "perfInstance", "PerformanceCompatImpl", "performanceFactory", "firebaseInstance"], "mappings": ";;;;;;;;;;;;;;;;OAiBA,MCAMA,EAAoB,SAAUC,GAElC,MAAMC,EAAgB,GACtB,IAAIC,EAAI,EACR,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAII,OAAQD,IAAK,CACnC,IAAIE,EAAIL,EAAIM,WAAWH,GACnBE,EAAI,IACNJ,EAAIC,KAAOG,EACFA,EAAI,MACbJ,EAAIC,KAAQG,GAAK,EAAK,IACtBJ,EAAIC,KAAY,GAAJG,EAAU,KAEL,QAAZ,MAAJA,IACDF,EAAI,EAAIH,EAAII,QACyB,QAAZ,MAAxBJ,EAAIM,WAAWH,EAAI,KAGpBE,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBL,EAAIM,aAAaH,IACvDF,EAAIC,KAAQG,GAAK,GAAM,IACvBJ,EAAIC,KAASG,GAAK,GAAM,GAAM,IAC9BJ,EAAIC,KAASG,GAAK,EAAK,GAAM,IAC7BJ,EAAIC,KAAY,GAAJG,EAAU,MAEtBJ,EAAIC,KAAQG,GAAK,GAAM,IACvBJ,EAAIC,KAASG,GAAK,EAAK,GAAM,IAC7BJ,EAAIC,KAAY,GAAJG,EAAU,IAEzB,CACD,OAAOJ,CACT,EA6DaM,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKF,gBAAIC,GACF,OAAOC,KAAKF,kBAAoB,KACjC,EAKD,wBAAIG,GACF,OAAOD,KAAKF,kBAAoB,KACjC,EASDI,mBAAoC,mBAATC,KAW3B,eAAAC,CAAgBC,EAA8BC,GAC5C,IAAKC,MAAMC,QAAQH,GACjB,MAAMI,MAAM,iDAGdT,KAAKU,QAEL,MAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAEHkB,EAAS,GAEf,IAAK,IAAIvB,EAAI,EAAGA,EAAIgB,EAAMf,OAAQD,GAAK,EAAG,CACxC,MAAMwB,EAAQR,EAAMhB,GACdyB,EAAYzB,EAAI,EAAIgB,EAAMf,OAC1ByB,EAAQD,EAAYT,EAAMhB,EAAI,GAAK,EACnC2B,EAAY3B,EAAI,EAAIgB,EAAMf,OAC1B2B,EAAQD,EAAYX,EAAMhB,EAAI,GAAK,EAEnC6B,EAAWL,GAAS,EACpBM,GAAqB,EAARN,IAAiB,EAAME,GAAS,EACnD,IAAIK,GAAqB,GAARL,IAAiB,EAAME,GAAS,EAC7CI,EAAmB,GAARJ,EAEVD,IACHK,EAAW,GAENP,IACHM,EAAW,KAIfR,EAAOU,KACLX,EAAcO,GACdP,EAAcQ,GACdR,EAAcS,GACdT,EAAcU,GAEjB,CAED,OAAOT,EAAOW,KAAK,GACpB,EAUD,YAAAC,CAAanB,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBmB,KAAKpB,GAEPL,KAAKI,gBAAgBnB,EAAkBoB,GAAQC,EACvD,EAUD,YAAAoB,CAAarB,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBH,KAAKE,GA5LQ,SAAUsB,GAElC,MAAMxC,EAAgB,GACtB,IAAIyC,EAAM,EACRrC,EAAI,EACN,KAAOqC,EAAMD,EAAMrC,QAAQ,CACzB,MAAMuC,EAAKF,EAAMC,KACjB,GAAIC,EAAK,IACP1C,EAAII,KAAOuC,OAAOC,aAAaF,QAC1B,GAAIA,EAAK,KAAOA,EAAK,IAAK,CAC/B,MAAMG,EAAKL,EAAMC,KACjBzC,EAAII,KAAOuC,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALG,EACpD,MAAM,GAAIH,EAAK,KAAOA,EAAK,IAAK,CAE/B,MAGMI,IACI,EAALJ,IAAW,IAAa,GAJlBF,EAAMC,OAImB,IAAa,GAHtCD,EAAMC,OAGuC,EAAW,GAFxDD,EAAMC,MAGf,MACFzC,EAAII,KAAOuC,OAAOC,aAAa,OAAUE,GAAK,KAC9C9C,EAAII,KAAOuC,OAAOC,aAAa,OAAc,KAAJE,GAC1C,KAAM,CACL,MAAMD,EAAKL,EAAMC,KACXM,EAAKP,EAAMC,KACjBzC,EAAII,KAAOuC,OAAOC,cACT,GAALF,IAAY,IAAa,GAALG,IAAY,EAAW,GAALE,EAE3C,CACF,CACD,OAAO/C,EAAIoC,KAAK,GAClB,CA+JWY,CAAkBnC,KAAKoC,wBAAwB/B,EAAOC,GAC9D,EAiBD,uBAAA8B,CAAwB/B,EAAeC,GACrCN,KAAKU,QAEL,MAAM2B,EAAgB/B,EAClBN,KAAKH,sBACLG,KAAKL,eAEHiB,EAAmB,GAEzB,IAAK,IAAIvB,EAAI,EAAGA,EAAIgB,EAAMf,QAAU,CAClC,MAAMuB,EAAQwB,EAAchC,EAAMiC,OAAOjD,MAGnC0B,EADY1B,EAAIgB,EAAMf,OACF+C,EAAchC,EAAMiC,OAAOjD,IAAM,IACzDA,EAEF,MACM4B,EADY5B,EAAIgB,EAAMf,OACF+C,EAAchC,EAAMiC,OAAOjD,IAAM,KACzDA,EAEF,MACMkD,EADYlD,EAAIgB,EAAMf,OACF+C,EAAchC,EAAMiC,OAAOjD,IAAM,GAG3D,KAFEA,EAEW,MAATwB,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAATsB,EACrD,MAAM,IAAIC,EAGZ,MAAMtB,EAAYL,GAAS,EAAME,GAAS,EAG1C,GAFAH,EAAOU,KAAKJ,GAEE,KAAVD,EAAc,CAChB,MAAME,EAAaJ,GAAS,EAAK,IAASE,GAAS,EAGnD,GAFAL,EAAOU,KAAKH,GAEE,KAAVoB,EAAc,CAChB,MAAMnB,EAAaH,GAAS,EAAK,IAAQsB,EACzC3B,EAAOU,KAAKF,EACb,CACF,CACF,CAED,OAAOR,CACR,EAOD,KAAAF,GACE,IAAKV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAK,IAAIR,EAAI,EAAGA,EAAIW,KAAKD,aAAaT,OAAQD,IAC5CW,KAAKN,eAAeL,GAAKW,KAAKD,aAAauC,OAAOjD,GAClDW,KAAKL,eAAeK,KAAKN,eAAeL,IAAMA,EAC9CW,KAAKJ,sBAAsBP,GAAKW,KAAKC,qBAAqBqC,OAAOjD,GACjEW,KAAKH,sBAAsBG,KAAKJ,sBAAsBP,IAAMA,EAGxDA,GAAKW,KAAKF,kBAAkBR,SAC9BU,KAAKL,eAAeK,KAAKC,qBAAqBqC,OAAOjD,IAAMA,EAC3DW,KAAKH,sBAAsBG,KAAKD,aAAauC,OAAOjD,IAAMA,EAG/D,CACF;;;;;;;;;;;;;;;;OAMG,MAAOmD,UAAgC/B,MAA7C,WAAAgC,uBACWzC,KAAI0C,KAAG,yBACjB,EAKM,MASMC,EAAgC,SAAUzD,GAErD,OAX0B,SAAUA,GACpC,MAAM0D,EAAY3D,EAAkBC,GACpC,OAAOO,EAAOW,gBAAgBwC,GAAW,EAC3C,CAQSC,CAAa3D,GAAK4D,QAAQ,MAAO,GAC1C,EAWaC,EAAe,SAAU7D,GACpC,IACE,OAAOO,EAAOiC,aAAaxC,GAAK,EACjC,CAAC,MAAO8D,GACPC,QAAQC,MAAM,wBAAyBF,EACxC,CACD,OAAO,IACT,EClVgB,SAAAG,EAAWC,EAAiBC,GAC1C,KAAMA,aAAkBC,QACtB,OAAOD,EAGT,OAAQA,EAAOZ,aACb,KAAKc,KAIH,OAAO,IAAIA,KADOF,EACQG,WAE5B,KAAKF,YACYG,IAAXL,IACFA,EAAS,CAAA,GAEX,MACF,KAAK7C,MAEH6C,EAAS,GACT,MAEF,QAEE,OAAOC,EAGX,IAAK,MAAMK,KAAQL,EAEZA,EAAOM,eAAeD,IAad,cAbmCA,IAG/CN,EAAmCM,GAAQP,EACzCC,EAAmCM,GACnCL,EAAmCK,KAIxC,OAAON,CACT;;;;;;;;;;;;;;;;;ACrBA,MAAMQ,EAAwB;;;;;;;;;;;;;;;;;WCjC5B,GAAoB,oBAATC,KACT,OAAOA,KAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,MAAM,IAAItD,MAAM,kCAClB,CDwBEuD,GAAYC,sBA2CDC,EAAc,KACzB,IACE,OAEEN,KArC6B,MACjC,GAAuB,oBAAZO,cAAkD,IAAhBA,QAAQC,IACnD,OAEF,MAAMC,EAAqBF,QAAQC,IAAIH,sBACvC,OAAII,EACKC,KAAKC,MAAMF,QADpB,CAEC,EA+BGG,IA5BwB,MAC5B,GAAwB,oBAAbC,SACT,OAEF,IAAIC,EACJ,IACEA,EAAQD,SAASE,OAAOD,MAAM,gCAC/B,CAAC,MAAO1B,GAGP,MACD,CACD,MAAM4B,EAAUF,GAAS3B,EAAa2B,EAAM,IAC5C,OAAOE,GAAWN,KAAKC,MAAMK,EAAQ,EAgBjCC,EAEH,CAAC,MAAO7B,GAQP,YADAC,QAAQ6B,KAAK,+CAA+C9B,IAE7D,GA4CU+B,EAAsB,KAAyC,IAAAC,EAC1E,OAAa,QAAbA,EAAAd,WAAa,IAAAc,OAAA,EAAAA,EAAEC,MAAM;;;;;;;;;;;;;;;;;AEjJV,MAAAC,EAIX,WAAAzC,GAFAzC,KAAAmF,OAAoC,OACpCnF,KAAAoF,QAAqC,OAEnCpF,KAAKqF,QAAU,IAAIC,SAAQ,CAACF,EAASD,KACnCnF,KAAKoF,QAAUA,EACfpF,KAAKmF,OAASA,CAAmC,GAEpD,CAOD,YAAAI,CACEC,GAEA,MAAO,CAACtC,EAAOuC,KACTvC,EACFlD,KAAKmF,OAAOjC,GAEZlD,KAAKoF,QAAQK,GAES,mBAAbD,IAGTxF,KAAKqF,QAAQK,OAAM,SAIK,IAApBF,EAASlG,OACXkG,EAAStC,GAETsC,EAAStC,EAAOuC,GAEnB,CAEJ,WCqCaE,IACd,MAC+B,oBAAtBC,mBACS,oBAAT/B,MACPA,gBAAgB+B,iBAEpB,UA2FgBC,IACd,IACE,MAA4B,iBAAdC,SACf,CAAC,MAAO9C,GACP,OAAO,CACR,CACH,UASgB+C,IACd,OAAO,IAAIT,SAAQ,CAACF,EAASD,KAC3B,IACE,IAAIa,GAAoB,EACxB,MAAMC,EACJ,0DACIC,EAAUrC,KAAKiC,UAAUK,KAAKF,GACpCC,EAAQE,UAAY,KAClBF,EAAQG,OAAOC,QAEVN,GACHnC,KAAKiC,UAAUS,eAAeN,GAEhCb,GAAQ,EAAK,EAEfc,EAAQM,gBAAkB,KACxBR,GAAW,CAAK,EAGlBE,EAAQO,QAAU,WAChBtB,GAAoB,QAAbH,EAAAkB,EAAQhD,aAAK,IAAA8B,OAAA,EAAAA,EAAE0B,UAAW,GAAG,CAEvC,CAAC,MAAOxD,GACPiC,EAAOjC,EACR,IAEL,CC9JM,MAAOyD,UAAsBlG,MAIjC,WAAAgC,CAEWmE,EACTF,EAEOG,GAEPC,MAAMJ,GALG1G,KAAI4G,KAAJA,EAGF5G,KAAU6G,WAAVA,EAPA7G,KAAI0C,KAdI,gBA6BfY,OAAOyD,eAAe/G,KAAM2G,EAAcK,WAItCvG,MAAMwG,mBACRxG,MAAMwG,kBAAkBjH,KAAMkH,EAAaF,UAAUG,OAExD,EAGU,MAAAD,EAIX,WAAAzE,CACmB2E,EACAC,EACAC,GAFAtH,KAAOoH,QAAPA,EACApH,KAAWqH,YAAXA,EACArH,KAAMsH,OAANA,CACf,CAEJ,MAAAH,CACEP,KACGW,GAEH,MAAMV,EAAcU,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGxH,KAAKoH,WAAWR,IAC9Ba,EAAWzH,KAAKsH,OAAOV,GAEvBF,EAAUe,EAUpB,SAAyBA,EAAkBF,GACzC,OAAOE,EAAS3E,QAAQ4E,GAAS,CAACC,EAAGC,KACnC,MAAMnC,EAAQ8B,EAAKK,GACnB,OAAgB,MAATnC,EAAgB3D,OAAO2D,GAAS,IAAImC,KAAO,GAEtD,CAf+BC,CAAgBJ,EAAUZ,GAAc,QAE7DiB,EAAc,GAAG9H,KAAKqH,gBAAgBX,MAAYc,MAIxD,OAFc,IAAIb,EAAca,EAAUM,EAAajB,EAGxD,EAUH,MAAMa,EAAU;;;;;;;;;;;;;;;;OCtHA,SAAAK,EAA2BC,EAAQJ,GACjD,OAAOtE,OAAO0D,UAAUrD,eAAesE,KAAKD,EAAKJ,EACnD,CAuCgB,SAAAM,EAAUC,EAAWC,GACnC,GAAID,IAAMC,EACR,OAAO,EAGT,MAAMC,EAAQ/E,OAAOgF,KAAKH,GACpBI,EAAQjF,OAAOgF,KAAKF,GAC1B,IAAK,MAAMI,KAAKH,EAAO,CACrB,IAAKE,EAAME,SAASD,GAClB,OAAO,EAGT,MAAME,EAASP,EAA8BK,GACvCG,EAASP,EAA8BI,GAC7C,GAAII,EAASF,IAAUE,EAASD,IAC9B,IAAKT,EAAUQ,EAAOC,GACpB,OAAO,OAEJ,GAAID,IAAUC,EACnB,OAAO,CAEV,CAED,IAAK,MAAMH,KAAKD,EACd,IAAKF,EAAMI,SAASD,GAClB,OAAO,EAGX,OAAO,CACT,CAEA,SAASI,EAASC,GAChB,OAAiB,OAAVA,GAAmC,iBAAVA,CAClC;;;;;;;;;;;;;;;;OC/Da,MAAAC,EAiBX,WAAArG,CACWC,EACAqG,EACAC,GAFAhJ,KAAI0C,KAAJA,EACA1C,KAAe+I,gBAAfA,EACA/I,KAAIgJ,KAAJA,EAnBXhJ,KAAiBiJ,mBAAG,EAIpBjJ,KAAYkJ,aAAe,GAE3BlJ,KAAAmJ,kBAA2C,OAE3CnJ,KAAiBoJ,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADAtJ,KAAKmJ,kBAAoBG,EAClBtJ,IACR,CAED,oBAAAuJ,CAAqBN,GAEnB,OADAjJ,KAAKiJ,kBAAoBA,EAClBjJ,IACR,CAED,eAAAwJ,CAAgBC,GAEd,OADAzJ,KAAKkJ,aAAeO,EACbzJ,IACR,CAED,0BAAA0J,CAA2BlE,GAEzB,OADAxF,KAAKoJ,kBAAoB5D,EAClBxF,IACR;;;;;;;;;;;;;;;;OCpDI,MAAM2J,EAAqB;;;;;;;;;;;;;;;;OCgBrB,MAAAC,EAWX,WAAAnH,CACmBC,EACAmH,GADA7J,KAAI0C,KAAJA,EACA1C,KAAS6J,UAATA,EAZX7J,KAAS8J,UAAwB,KACxB9J,KAAA+J,UAAgD,IAAIC,IACpDhK,KAAAiK,kBAGb,IAAID,IACShK,KAAAkK,iBACf,IAAIF,IACEhK,KAAAmK,gBAAuD,IAAIH,GAK/D,CAMJ,GAAAI,CAAIC,GAEF,MAAMC,EAAuBtK,KAAKuK,4BAA4BF,GAE9D,IAAKrK,KAAKiK,kBAAkBO,IAAIF,GAAuB,CACrD,MAAMG,EAAW,IAAIvF,EAGrB,GAFAlF,KAAKiK,kBAAkBS,IAAIJ,EAAsBG,GAG/CzK,KAAK2K,cAAcL,IACnBtK,KAAK4K,uBAGL,IACE,MAAMC,EAAW7K,KAAK8K,uBAAuB,CAC3CC,mBAAoBT,IAElBO,GACFJ,EAASrF,QAAQyF,EAEpB,CAAC,MAAO7H,GAGR,CAEJ,CAED,OAAOhD,KAAKiK,kBAAkBG,IAAIE,GAAuBjF,OAC1D,CAkBD,YAAA2F,CAAaC,SAKX,MAAMX,EAAuBtK,KAAKuK,4BAChCU,aAAA,EAAAA,EAASZ,YAELa,EAAgC,QAArBlG,EAAAiG,aAAA,EAAAA,EAASC,gBAAY,IAAAlG,GAAAA,EAEtC,IACEhF,KAAK2K,cAAcL,KACnBtK,KAAK4K,uBAaA,CAEL,GAAIM,EACF,OAAO,KAEP,MAAMzK,MAAM,WAAWT,KAAK0C,wBAE/B,CAlBC,IACE,OAAO1C,KAAK8K,uBAAuB,CACjCC,mBAAoBT,GAEvB,CAAC,MAAOtH,GACP,GAAIkI,EACF,OAAO,KAEP,MAAMlI,CAET,CASJ,CAED,YAAAmI,GACE,OAAOnL,KAAK8J,SACb,CAED,YAAAsB,CAAatB,GACX,GAAIA,EAAUpH,OAAS1C,KAAK0C,KAC1B,MAAMjC,MACJ,yBAAyBqJ,EAAUpH,qBAAqB1C,KAAK0C,SAIjE,GAAI1C,KAAK8J,UACP,MAAMrJ,MAAM,iBAAiBT,KAAK0C,kCAMpC,GAHA1C,KAAK8J,UAAYA,EAGZ9J,KAAK4K,uBAAV,CAKA,GA0NJ,SAA0Cd,GACxC,MAAkC,UAA3BA,EAAUX,iBACnB;;;;;;;;;;;;;;;;OA5NQkC,CAAiBvB,GACnB,IACE9J,KAAK8K,uBAAuB,CAAEC,mBAAoBpB,GACnD,CAAC,MAAO3G,GAKR,CAMH,IAAK,MACH+H,EACAO,KACGtL,KAAKiK,kBAAkBsB,UAAW,CACrC,MAAMjB,EACJtK,KAAKuK,4BAA4BQ,GAEnC,IAEE,MAAMF,EAAW7K,KAAK8K,uBAAuB,CAC3CC,mBAAoBT,IAEtBgB,EAAiBlG,QAAQyF,EAC1B,CAAC,MAAO7H,GAGR,CACF,CAlCA,CAmCF,CAED,aAAAwI,CAAcnB,EAAqBV,GACjC3J,KAAKiK,kBAAkBwB,OAAOpB,GAC9BrK,KAAKkK,iBAAiBuB,OAAOpB,GAC7BrK,KAAK+J,UAAU0B,OAAOpB,EACvB,CAID,YAAM,GACJ,MAAMqB,EAAWnL,MAAMoL,KAAK3L,KAAK+J,UAAU6B,gBAErCtG,QAAQuG,IAAI,IACbH,EACAI,QAAO1E,GAAW,aAAcA,IAEhC2E,KAAI3E,GAAYA,EAAgB4E,SAAUP,cAC1CC,EACAI,QAAO1E,GAAW,YAAaA,IAE/B2E,KAAI3E,GAAYA,EAAgB6E,aAEtC,CAED,cAAAC,GACE,OAAyB,MAAlBlM,KAAK8J,SACb,CAED,aAAAa,CAAcN,EAAqBV,GACjC,OAAO3J,KAAK+J,UAAUS,IAAIH,EAC3B,CAED,UAAA8B,CAAW9B,EAAqBV,GAC9B,OAAO3J,KAAKkK,iBAAiBE,IAAIC,IAAe,CAAA,CACjD,CAED,UAAA+B,CAAWC,EAA0B,IACnC,MAAMpB,QAAEA,EAAU,IAAOoB,EACnB/B,EAAuBtK,KAAKuK,4BAChC8B,EAAKtB,oBAEP,GAAI/K,KAAK2K,cAAcL,GACrB,MAAM7J,MACJ,GAAGT,KAAK0C,QAAQ4H,mCAIpB,IAAKtK,KAAKkM,iBACR,MAAMzL,MAAM,aAAaT,KAAK0C,oCAGhC,MAAMmI,EAAW7K,KAAK8K,uBAAuB,CAC3CC,mBAAoBT,EACpBW,YAIF,IAAK,MACHF,EACAO,KACGtL,KAAKiK,kBAAkBsB,UAAW,CAGjCjB,IADFtK,KAAKuK,4BAA4BQ,IAEjCO,EAAiBlG,QAAQyF,EAE5B,CAED,OAAOA,CACR,CAUD,MAAAyB,CAAO9G,EAA6B6E,SAClC,MAAMC,EAAuBtK,KAAKuK,4BAA4BF,GACxDkC,EAC0C,QAA9CvH,EAAAhF,KAAKmK,gBAAgBC,IAAIE,UAAqB,IAAAtF,EAAAA,EAC9C,IAAIwH,IACND,EAAkBE,IAAIjH,GACtBxF,KAAKmK,gBAAgBO,IAAIJ,EAAsBiC,GAE/C,MAAMG,EAAmB1M,KAAK+J,UAAUK,IAAIE,GAK5C,OAJIoC,GACFlH,EAASkH,EAAkBpC,GAGtB,KACLiC,EAAkBd,OAAOjG,EAAS,CAErC,CAMO,qBAAAmH,CACN9B,EACAR,GAEA,MAAMuC,EAAY5M,KAAKmK,gBAAgBC,IAAIC,GAC3C,GAAKuC,EAGL,IAAK,MAAMpH,KAAYoH,EACrB,IACEpH,EAASqF,EAAUR,EACpB,CAAC,MAAArF,GAED,CAEJ,CAEO,sBAAA8F,EAAuBC,mBAC7BA,EAAkBE,QAClBA,EAAU,CAAE,IAKZ,IAAIJ,EAAW7K,KAAK+J,UAAUK,IAAIW,GAClC,IAAKF,GAAY7K,KAAK8J,YACpBe,EAAW7K,KAAK8J,UAAUf,gBAAgB/I,KAAK6J,UAAW,CACxDkB,oBAqD+BV,EArDmBU,EAsDjDV,IAAeV,OAAqBlG,EAAY4G,GArDjDY,YAEFjL,KAAK+J,UAAUW,IAAIK,EAAoBF,GACvC7K,KAAKkK,iBAAiBQ,IAAIK,EAAoBE,GAO9CjL,KAAK2M,sBAAsB9B,EAAWE,GAOlC/K,KAAK8J,UAAUV,mBACjB,IACEpJ,KAAK8J,UAAUV,kBACbpJ,KAAK6J,UACLkB,EACAF,EAEH,CAAC,MAAA7F,GAED,CA0BT,IAAuCqF,EAtBnC,OAAOQ,GAAY,IACpB,CAEO,2BAAAN,CACNF,EAAqBV,GAErB,OAAI3J,KAAK8J,UACA9J,KAAK8J,UAAUb,kBAAoBoB,EAAaV,EAEhDU,CAEV,CAEO,oBAAAO,GACN,QACI5K,KAAK8J,WACyB,aAAhC9J,KAAK8J,UAAUX,iBAElB,ECvVU,MAAA0D,EAGX,WAAApK,CAA6BC,GAAA1C,KAAI0C,KAAJA,EAFZ1C,KAAA8M,UAAY,IAAI9C,GAEY,CAW7C,YAAA+C,CAA6BjD,GAC3B,MAAMkD,EAAWhN,KAAKiN,YAAYnD,EAAUpH,MAC5C,GAAIsK,EAASd,iBACX,MAAM,IAAIzL,MACR,aAAaqJ,EAAUpH,yCAAyC1C,KAAK0C,QAIzEsK,EAAS5B,aAAatB,EACvB,CAED,uBAAAoD,CAAwCpD,GACrB9J,KAAKiN,YAAYnD,EAAUpH,MAC/BwJ,kBAEXlM,KAAK8M,UAAUrB,OAAO3B,EAAUpH,MAGlC1C,KAAK+M,aAAajD,EACnB,CASD,WAAAmD,CAA4BvK,GAC1B,GAAI1C,KAAK8M,UAAUtC,IAAI9H,GACrB,OAAO1C,KAAK8M,UAAU1C,IAAI1H,GAI5B,MAAMsK,EAAW,IAAIpD,EAAYlH,EAAM1C,MAGvC,OAFAA,KAAK8M,UAAUpC,IAAIhI,EAAMsK,GAElBA,CACR,CAED,YAAAG,GACE,OAAO5M,MAAMoL,KAAK3L,KAAK8M,UAAUlB,SAClC;;;;;;;;;;;;;;;;OCvCI,MAAM7B,EAAsB,OAavBqD,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAOX,CAAA,IAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpB3I,KAAQsI,EAASM,KACjBC,KAAQP,EAASQ,KACjB1K,MAASkK,EAASS,MAClBC,OAAUV,EAASW,QAMfC,EAA4BZ,EAASM,KAmBrCO,EAAgB,CACpB,CAACb,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASM,MAAO,OACjB,CAACN,EAASQ,MAAO,OACjB,CAACR,EAASS,OAAQ,SAQdK,EAAgC,CAACrD,EAAUsD,KAAYC,KAC3D,GAAID,EAAUtD,EAASwD,SACrB,OAEF,MAAMC,GAAM,IAAI/K,MAAOgL,cACjBC,EAASP,EAAcE,GAC7B,IAAIK,EAMF,MAAM,IAAI/N,MACR,8DAA8D0N,MANhElL,QAAQuL,GACN,IAAIF,OAASzD,EAASnI,WACnB0L,EAMN,EAGU,MAAAK,EAOX,WAAAhM,CAAmBC,GAAA1C,KAAI0C,KAAJA,EAUX1C,KAAS0O,UAAGV,EAsBZhO,KAAW2O,YAAeT,EAc1BlO,KAAe4O,gBAAsB,KA1C3C7E,EAAUzI,KAAKtB,KAChB,CAOD,YAAIqO,GACF,OAAOrO,KAAK0O,SACb,CAED,YAAIL,CAASQ,GACX,KAAMA,KAAOzB,GACX,MAAM,IAAI0B,UAAU,kBAAkBD,+BAExC7O,KAAK0O,UAAYG,CAClB,CAGD,WAAAE,CAAYF,GACV7O,KAAK0O,UAA2B,iBAARG,EAAmBxB,EAAkBwB,GAAOA,CACrE,CAOD,cAAIG,GACF,OAAOhP,KAAK2O,WACb,CACD,cAAIK,CAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtB9O,KAAK2O,YAAcE,CACpB,CAMD,kBAAII,GACF,OAAOjP,KAAK4O,eACb,CACD,kBAAIK,CAAeJ,GACjB7O,KAAK4O,gBAAkBC,CACxB,CAMD,KAAAvB,IAASc,GACPpO,KAAK4O,iBAAmB5O,KAAK4O,gBAAgB5O,KAAMoN,EAASG,SAAUa,GACtEpO,KAAK2O,YAAY3O,KAAMoN,EAASG,SAAUa,EAC3C,CACD,GAAAc,IAAOd,GACLpO,KAAK4O,iBACH5O,KAAK4O,gBAAgB5O,KAAMoN,EAASK,WAAYW,GAClDpO,KAAK2O,YAAY3O,KAAMoN,EAASK,WAAYW,EAC7C,CACD,IAAAtJ,IAAQsJ,GACNpO,KAAK4O,iBAAmB5O,KAAK4O,gBAAgB5O,KAAMoN,EAASM,QAASU,GACrEpO,KAAK2O,YAAY3O,KAAMoN,EAASM,QAASU,EAC1C,CACD,IAAAT,IAAQS,GACNpO,KAAK4O,iBAAmB5O,KAAK4O,gBAAgB5O,KAAMoN,EAASQ,QAASQ,GACrEpO,KAAK2O,YAAY3O,KAAMoN,EAASQ,QAASQ,EAC1C,CACD,KAAAlL,IAASkL,GACPpO,KAAK4O,iBAAmB5O,KAAK4O,gBAAgB5O,KAAMoN,EAASS,SAAUO,GACtEpO,KAAK2O,YAAY3O,KAAMoN,EAASS,SAAUO,EAC3C,EChNH,IAAIe,EACAC,EAqBJ,MAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlC,IAAIK,EAAgB,CAChB,GAAAvF,CAAIhH,EAAQM,EAAMkM,GACd,GAAIxM,aAAkByM,eAAgB,CAElC,GAAa,SAATnM,EACA,OAAO6L,EAAmBnF,IAAIhH,GAElC,GAAa,qBAATM,EACA,OAAON,EAAO0M,kBAAoBN,EAAyBpF,IAAIhH,GAGnE,GAAa,UAATM,EACA,OAAOkM,EAASE,iBAAiB,QAC3BrM,EACAmM,EAASG,YAAYH,EAASE,iBAAiB,GAE5D,CAED,OAAOE,EAAK5M,EAAOM,GACtB,EACDgH,IAAG,CAACtH,EAAQM,EAAM+B,KACdrC,EAAOM,GAAQ+B,GACR,GAEX+E,IAAG,CAACpH,EAAQM,IACJN,aAAkByM,iBACR,SAATnM,GAA4B,UAATA,IAGjBA,KAAQN,GAMvB,SAAS6M,EAAaC,GAIlB,OAAIA,IAASC,YAAYnJ,UAAUoJ,aAC7B,qBAAsBP,eAAe7I,WA7GnCoI,IACHA,EAAuB,CACpBiB,UAAUrJ,UAAUsJ,QACpBD,UAAUrJ,UAAUuJ,SACpBF,UAAUrJ,UAAUwJ,sBAqHE/H,SAASyH,GAC5B,YAAa9B,GAIhB,OADA8B,EAAKO,MAAMC,EAAO1Q,MAAOoO,GAClB4B,EAAKX,EAAiBjF,IAAIpK,MAC7C,EAEW,YAAaoO,GAGhB,OAAO4B,EAAKE,EAAKO,MAAMC,EAAO1Q,MAAOoO,GAC7C,EAvBe,SAAUuC,KAAevC,GAC5B,MAAMwC,EAAKV,EAAKjI,KAAKyI,EAAO1Q,MAAO2Q,KAAevC,GAElD,OADAoB,EAAyB9E,IAAIkG,EAAID,EAAWE,KAAOF,EAAWE,OAAS,CAACF,IACjEX,EAAKY,EACxB,CAoBA,CACA,SAASE,EAAuBrL,GAC5B,MAAqB,mBAAVA,EACAwK,EAAaxK,IAGpBA,aAAiBoK,gBAhGzB,SAAwCe,GAEpC,GAAIrB,EAAmB/E,IAAIoG,GACvB,OACJ,MAAMG,EAAO,IAAIzL,SAAQ,CAACF,EAASD,KAC/B,MAAM6L,EAAW,KACbJ,EAAGK,oBAAoB,WAAYC,GACnCN,EAAGK,oBAAoB,QAAS/N,GAChC0N,EAAGK,oBAAoB,QAAS/N,EAAM,EAEpCgO,EAAW,KACb9L,IACA4L,GAAU,EAER9N,EAAQ,KACViC,EAAOyL,EAAG1N,OAAS,IAAIiO,aAAa,aAAc,eAClDH,GAAU,EAEdJ,EAAGQ,iBAAiB,WAAYF,GAChCN,EAAGQ,iBAAiB,QAASlO,GAC7B0N,EAAGQ,iBAAiB,QAASlO,EAAM,IAGvCqM,EAAmB7E,IAAIkG,EAAIG,EAC/B,CAyEQM,CAA+B5L,GA9JhB6L,EA+JD7L,GAzJV0J,IACHA,EAAoB,CACjBgB,YACAoB,eACAC,SACAnB,UACAR,kBAZiD4B,MAAMlS,GAAM+R,aAAkB/R,IAgK5E,IAAImS,MAAMjM,EAAOkK,GAErBlK,GAlKW,IAAC6L,CAmKvB,CACA,SAAStB,EAAKvK,GAGV,GAAIA,aAAiBkM,WACjB,OA3IR,SAA0BzL,GACtB,MAAMb,EAAU,IAAIC,SAAQ,CAACF,EAASD,KAClC,MAAM6L,EAAW,KACb9K,EAAQ+K,oBAAoB,UAAWW,GACvC1L,EAAQ+K,oBAAoB,QAAS/N,EAAM,EAEzC0O,EAAU,KACZxM,EAAQ4K,EAAK9J,EAAQG,SACrB2K,GAAU,EAER9N,EAAQ,KACViC,EAAOe,EAAQhD,OACf8N,GAAU,EAEd9K,EAAQkL,iBAAiB,UAAWQ,GACpC1L,EAAQkL,iBAAiB,QAASlO,EAAM,IAe5C,OAbAmC,EACKwM,MAAMpM,IAGHA,aAAiB4K,WACjBhB,EAAiB3E,IAAIjF,EAAOS,EAC/B,IAGAR,OAAM,SAGXgK,EAAsBhF,IAAIrF,EAASa,GAC5Bb,CACX,CA4GeyM,CAAiBrM,GAG5B,GAAIgK,EAAejF,IAAI/E,GACnB,OAAOgK,EAAerF,IAAI3E,GAC9B,MAAMsM,EAAWjB,EAAuBrL,GAOxC,OAJIsM,IAAatM,IACbgK,EAAe/E,IAAIjF,EAAOsM,GAC1BrC,EAAsBhF,IAAIqH,EAAUtM,IAEjCsM,CACX,CACA,MAAMrB,EAAUjL,GAAUiK,EAAsBtF,IAAI3E,GC5KpD,SAASuM,EAAOtP,EAAMuP,GAASC,QAAEA,EAAOC,QAAEA,EAAOC,SAAEA,EAAQC,WAAEA,GAAe,IACxE,MAAMnM,EAAUJ,UAAUK,KAAKzD,EAAMuP,GAC/BK,EAActC,EAAK9J,GAoBzB,OAnBIiM,GACAjM,EAAQkL,iBAAiB,iBAAkBmB,IACvCJ,EAAQnC,EAAK9J,EAAQG,QAASkM,EAAMC,WAAYD,EAAME,WAAYzC,EAAK9J,EAAQkK,aAAcmC,EAAM,IAGvGL,GACAhM,EAAQkL,iBAAiB,WAAYmB,GAAUL,EAE/CK,EAAMC,WAAYD,EAAME,WAAYF,KAExCD,EACKT,MAAMa,IACHL,GACAK,EAAGtB,iBAAiB,SAAS,IAAMiB,MACnCD,GACAM,EAAGtB,iBAAiB,iBAAkBmB,GAAUH,EAASG,EAAMC,WAAYD,EAAME,WAAYF,IAChG,IAEA7M,OAAM,SACJ4M,CACX,CAgBA,MAAMK,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAI7I,IAC1B,SAAS8I,EAAU1P,EAAQM,GACvB,KAAMN,aAAkB+M,cAClBzM,KAAQN,GACM,iBAATM,EACP,OAEJ,GAAImP,EAAczI,IAAI1G,GAClB,OAAOmP,EAAczI,IAAI1G,GAC7B,MAAMqP,EAAiBrP,EAAKZ,QAAQ,aAAc,IAC5CkQ,EAAWtP,IAASqP,EACpBE,EAAUL,EAAanK,SAASsK,GACtC,KAEEA,KAAmBC,EAAWxB,SAAWD,gBAAgBvK,aACrDiM,IAAWN,EAAYlK,SAASsK,GAClC,OAEJ,MAAMvE,EAAS0E,eAAgBC,KAAc/E,GAEzC,MAAMwC,EAAK5Q,KAAKoQ,YAAY+C,EAAWF,EAAU,YAAc,YAC/D,IAAI7P,EAASwN,EAAGwC,MAQhB,OAPIJ,IACA5P,EAASA,EAAOiQ,MAAMjF,EAAKkF,iBAMjBhO,QAAQuG,IAAI,CACtBzI,EAAO2P,MAAmB3E,GAC1B6E,GAAWrC,EAAGG,QACd,EACZ,EAEI,OADA8B,EAAcnI,IAAIhH,EAAM8K,GACjBA,CACX,CDgCImB,EC/BS,CAAC4D,IAAc,IACrBA,EACHnJ,IAAK,CAAChH,EAAQM,EAAMkM,IAAakD,EAAU1P,EAAQM,IAAS6P,EAASnJ,IAAIhH,EAAQM,EAAMkM,GACvFpF,IAAK,CAACpH,EAAQM,MAAWoP,EAAU1P,EAAQM,IAAS6P,EAAS/I,IAAIpH,EAAQM,KD4BzD8B,CAASmK;;;;;;;;;;;;;;;;;AE9FhB,MAAA6D,EACX,WAAA/Q,CAA6BoH,GAAA7J,KAAS6J,UAATA,CAAiC,CAG9D,qBAAA4J,GAIE,OAHkBzT,KAAK6J,UAAUsD,eAI9BpB,KAAIiB,IACH,GAmBR,SAAkCA,GAChC,MAAMlD,EAAYkD,EAAS7B,eAC3B,MAAsB,aAAfrB,aAAA,EAAAA,EAAWd,KACpB,CAtBY0K,CAAyB1G,GAAW,CACtC,MAAM5F,EAAU4F,EAAShC,eACzB,MAAO,GAAG5D,EAAQuM,WAAWvM,EAAQ6K,SACtC,CACC,OAAO,IACR,IAEFnG,QAAO8H,GAAaA,IACpBrS,KAAK,IACT,qCCzBUsS,EAAS,IAAIpF,EAAO,2tBC+BpB9E,GAAqB,YAErBmK,GAAsB,CACjCC,CAACC,GAAU,YACXC,CAACC,GAAgB,mBACjBC,CAACC,GAAgB,iBACjBC,CAACC,GAAsB,wBACvBC,CAACC,IAAe,iBAChBC,CAACC,IAAqB,wBACtBC,CAACC,IAAW,YACZC,CAACC,IAAiB,mBAClBC,CAACC,IAAe,YAChBC,CAACC,IAAkB,oBACnBC,CAACC,IAAqB,mBACtBC,CAACC,IAAgB,UACjBC,CAACC,IAAsB,iBACvBC,CAACC,IAAoB,WACrBC,CAACC,IAA0B,kBAC3BC,CAACC,IAAgB,WACjBC,CAACC,IAAsB,kBACvBC,CAACC,IAAkB,YACnBC,CAACC,IAAwB,mBACzBC,CAACC,IAAmB,UACpBC,CAACC,IAAyB,iBAC1BC,CAACC,IAAc,WACfC,CAACC,IAAoB,kBACrBC,CAACC,IAAgB,WACjBC,CAACC,IAAsB,kBACvBC,CAACC,IAAS,cACV,UAAW,UACXC,CAACC,IAAc,eCjDJC,GAAQ,IAAIrN,IAKZsN,GAAc,IAAItN,IAQlBuN,GAAc,IAAIvN,IAOf,SAAAwN,GACdC,EACA3N,GAEA,IACG2N,EAAwB5N,UAAUkD,aAAajD,EACjD,CAAC,MAAO9G,GACP6Q,EAAOvG,MACL,aAAaxD,EAAUpH,4CAA4C+U,EAAI/U,OACvEM,EAEH,CACH,CAoBM,SAAU0U,GACd5N,GAEA,MAAM6N,EAAgB7N,EAAUpH,KAChC,GAAI6U,GAAY/M,IAAImN,GAKlB,OAJA9D,EAAOvG,MACL,sDAAsDqK,OAGjD,EAGTJ,GAAY7M,IAAIiN,EAAe7N,GAG/B,IAAK,MAAM2N,KAAOJ,GAAMzL,SACtB4L,GAAcC,EAAwB3N,GAGxC,IAAK,MAAM8N,KAAaN,GAAY1L,SAClC4L,GAAcI,EAAoC9N,GAGpD,OAAO,CACT,CAWgB,SAAA+N,GACdJ,EACA/U,GAEA,MAAMoV,EAAuBL,EAAwB5N,UAClDoD,YAAY,aACZjC,aAAa,CAAEE,UAAU,IAI5B,OAHI4M,GACGA,EAAoBC,mBAEnBN,EAAwB5N,UAAUoD,YAAYvK,EACxD,CA0BM,SAAUsV,GACdhQ,GAEA,YAAwCvE,IAAhCuE,EAAoBiD,OAC9B;;;;;;;;;;;;;;;;;AC3HA,MA2CagN,GAAgB,IAAI/Q,EAC/B,MACA,WA7CiC,CACjC,SACE,6EAEF,eAAyB,iCACzB,gBACE,kFACF,cAAwB,kDACxB,qBAA+B,uCAC/B,aACE,0EACF,uBACE,6EAEF,uBACE,wDACF,WACE,gFACF,UACE,qFACF,UACE,mFACF,aACE,sFACF,sCACE,0GACF,iCACE;;;;;;;;;;;;;;;;;AClCS,MAAAgR,GAcX,WAAAzV,CACEwI,EACAhG,EACA4E,GANQ7J,KAAUmY,YAAG,EAQrBnY,KAAKoY,SAAgB9U,OAAA+U,OAAA,CAAA,EAAApN,GACrBjL,KAAKsY,QAAehV,OAAA+U,OAAA,CAAA,EAAApT,GACpBjF,KAAKuY,MAAQtT,EAAOvC,KACpB1C,KAAKwY,gCACHvT,EAAOwT,+BACTzY,KAAK0Y,WAAa7O,EAClB7J,KAAK6J,UAAUkD,aACb,IAAIjE,EAAU,OAAO,IAAM9I,MAAI,UAElC,CAED,kCAAIyY,GAEF,OADAzY,KAAK2Y,iBACE3Y,KAAKwY,+BACb,CAED,kCAAIC,CAA+B5J,GACjC7O,KAAK2Y,iBACL3Y,KAAKwY,gCAAkC3J,CACxC,CAED,QAAInM,GAEF,OADA1C,KAAK2Y,iBACE3Y,KAAKuY,KACb,CAED,WAAItN,GAEF,OADAjL,KAAK2Y,iBACE3Y,KAAKoY,QACb,CAED,UAAInT,GAEF,OADAjF,KAAK2Y,iBACE3Y,KAAKsY,OACb,CAED,aAAIzO,GACF,OAAO7J,KAAK0Y,UACb,CAED,aAAIE,GACF,OAAO5Y,KAAKmY,UACb,CAED,aAAIS,CAAU/J,GACZ7O,KAAKmY,WAAatJ,CACnB,CAMS,cAAA8J,GACR,GAAI3Y,KAAK4Y,UACP,MAAMX,GAAc9Q,OAAM,cAAuB,CAAE6M,QAAShU,KAAKuY,OAEpE;;;;;;;;;;;;;;;;OCvEH,SAASM,GAAiBC,EAAqBC,GAC7C,MAAMC,EAAajW,EAAa+V,EAAYG,MAAM,KAAK,IACvD,GAAmB,OAAfD,EAIF,YAHA/V,QAAQC,MACN,qBAAqB6V,kDAKzB,QAAiBtV,IADAa,KAAKC,MAAMyU,GAAYE,IAKtC,YAHAjW,QAAQC,MACN,qBAAqB6V,sDAIgB,IAA7BzU,KAAKC,MAAMyU,GAAYE,KACvB,IAAI3V,MAAOC,WAEX,GACVP,QAAQC,MACN,qBAAqB6V,uCAG3B,CAEM,MAAOI,WACHjB,GAOR,WAAAzV,CACEwI,EACAmO,EACA1W,EACAmH,GAGA,MAAM4O,OAC4ChV,IAAhD2V,EAAaX,gCACTW,EAAaX,+BAIbxT,EAAwC,CAC5CvC,OACA+V,kCAGF,QAA4ChV,IAAvCwH,EAA4BoO,OAE/BvS,MAAMmE,EAA4BhG,EAAQ4E,OACrC,CAEL/C,MADiCmE,EACnBA,QAAShG,EAAQ4E,EAChC,CAGD7J,KAAKsZ,cAAahW,OAAA+U,OAAA,CAChBI,kCACGW,GAIDpZ,KAAKsZ,cAAcC,aACrBV,GAAiB7Y,KAAKsZ,cAAcC,YAAa,eAI/CvZ,KAAKsZ,cAAcE,eACrBX,GAAiB7Y,KAAKsZ,cAAcE,cAAe,iBAGrDxZ,KAAKyZ,sBAAwB,KACO,oBAAzBC,uBACT1Z,KAAKyZ,sBAAwB,IAAIC,sBAAqB,KACpD1Z,KAAK2Z,kBAAkB,KAI3B3Z,KAAK4Z,UAAY,EACjB5Z,KAAK6Z,YAAY7Z,KAAKsZ,cAAcQ,gBAIpC9Z,KAAKsZ,cAAcQ,oBAAiBrW,EACpC2V,EAAaU,oBAAiBrW,EAE9BsW,GAAgB3C,EAAanF,EAAS,YACvC,CAED,MAAA+H,GAEC,CAED,YAAIC,GACF,OAAOja,KAAK4Z,SACb,CAID,WAAAC,CAAY7R,GACNhI,KAAK4Y,YAGT5Y,KAAK4Z,iBACOnW,IAARuE,GAAoD,OAA/BhI,KAAKyZ,uBAC5BzZ,KAAKyZ,sBAAsBS,SAASlS,EAAKhI,MAE5C,CAGD,WAAAma,GACE,OAAIna,KAAK4Y,UACA,IAEA5Y,KAAK4Z,SACf,CAKO,gBAAAD,GACDS,GAAUpa,KAChB,CAED,YAAIqa,GAEF,OADAra,KAAK2Y,iBACE3Y,KAAKsZ,aACb,CAMS,cAAAX,GACR,GAAI3Y,KAAK4Y,UACP,MAAMX,GAAc9Q,OAAM,qBAE7B;;;;;;;;;;;;;;;;OC9GI,MAAMmT,YAoEG,SAAAC,GACdnC,EACAoC,EAAY,IAEZ,IAAIvP,EAAUmN,EAEd,GAAyB,iBAAdoC,EAAwB,CAEjCA,EAAY,CAAE9X,KADD8X,EAEd,CAED,MAAMvV,EAAM3B,OAAA+U,OAAA,CACV3V,KAAMiH,GACN8O,gCAAgC,GAC7B+B,GAEC9X,EAAOuC,EAAOvC,KAEpB,GAAoB,iBAATA,IAAsBA,EAC/B,MAAMuV,GAAc9Q,OAA8B,eAAA,CAChD6M,QAASlS,OAAOY,KAMpB,GAFAuI,IAAAA,EAAYlG,MAEPkG,EACH,MAAMgN,GAAc9Q,OAAM,cAG5B,MAAMsT,EAAcpD,GAAMjN,IAAI1H,GAC9B,GAAI+X,EAAa,CAEf,GACEvS,EAAU+C,EAASwP,EAAYxP,UAC/B/C,EAAUjD,EAAQwV,EAAYxV,QAE9B,OAAOwV,EAEP,MAAMxC,GAAc9Q,OAA+B,gBAAA,CAAE6M,QAAStR,GAEjE,CAED,MAAMmH,EAAY,IAAIgD,EAAmBnK,GACzC,IAAK,MAAMoH,KAAayN,GAAY3L,SAClC/B,EAAUkD,aAAajD,GAGzB,MAAM4Q,EAAS,IAAIxC,GAAgBjN,EAAShG,EAAQ4E,GAIpD,OAFAwN,GAAM3M,IAAIhI,EAAMgY,GAETA,CACT,CA4LOxH,eAAekH,GAAU3C,GAC9B,IAAIkD,GAAmB,EACvB,MAAMjY,EAAO+U,EAAI/U,KACjB,GAAI2U,GAAM7M,IAAI9H,GACZiY,GAAmB,EACnBtD,GAAM5L,OAAO/I,QACR,GAAI4U,GAAY9M,IAAI9H,GAAO,CACN+U,EACJ0C,eAAiB,IACrC7C,GAAY7L,OAAO/I,GACnBiY,GAAmB,EAEtB,CAEGA,UACIrV,QAAQuG,IACX4L,EAAwB5N,UACtBsD,eACApB,KAAIiB,GAAYA,EAASvB,YAE7BgM,EAAwBmB,WAAY,EAEzC,CAUgB,SAAAmB,GACda,EACA3I,EACA4I,SAIA,IAAIlH,EAAmD,QAAzC3O,EAAA8O,GAAoB8G,UAAqB,IAAA5V,EAAAA,EAAA4V,EACnDC,IACFlH,GAAW,IAAIkH,KAEjB,MAAMC,EAAkBnH,EAAQjP,MAAM,SAChCqW,EAAkB9I,EAAQvN,MAAM,SACtC,GAAIoW,GAAmBC,EAAiB,CACtC,MAAMC,EAAU,CACd,+BAA+BrH,oBAA0B1B,OAgB3D,OAdI6I,GACFE,EAAQ1Z,KACN,iBAAiBqS,sDAGjBmH,GAAmBC,GACrBC,EAAQ1Z,KAAK,OAEXyZ,GACFC,EAAQ1Z,KACN,iBAAiB2Q,2DAGrB4B,EAAOlG,KAAKqN,EAAQzZ,KAAK,KAE1B,CACDmW,GACE,IAAI5O,EACF,GAAG6K,aACH,KAAO,CAAEA,UAAS1B,aAAU,WAIlC,CASgB,SAAAgJ,GACdC,EACAjQ,GAEA,GAAoB,OAAhBiQ,GAA+C,mBAAhBA,EACjC,MAAMjD,GAAc9Q,OAAM,yBVhPd,SACd+T,EACAjQ,GAEA,IAAK,MAAMJ,KAAYd,EAAW,CAChC,IAAIoR,EAAkC,KAClClQ,GAAWA,EAAQmQ,QACrBD,EAAiB9N,EAAkBpC,EAAQmQ,QAG3CvQ,EAASoE,eADS,OAAhBiM,EACwB,KAEA,CACxBrQ,EACAuQ,KACGhN,KAEH,MAAM1H,EAAU0H,EACbrC,KAAIsP,IACH,GAAW,MAAPA,EACF,OAAO,KACF,GAAmB,iBAARA,EAChB,OAAOA,EACF,GAAmB,iBAARA,GAAmC,kBAARA,EAC3C,OAAOA,EAAIC,WACN,GAAID,aAAe5a,MACxB,OAAO4a,EAAI3U,QAEX,IACE,OAAOpC,KAAKiX,UAAUF,EACvB,CAAC,MAAOG,GACP,OAAO,IACR,CACF,IAEF1P,QAAOuP,GAAOA,IACd9Z,KAAK,KACJ6Z,IAAUD,QAAAA,EAAkBtQ,EAASwD,WACvC6M,EAAY,CACVE,MAAOhO,EAASgO,GAAOK,cACvB/U,UACA0H,OACApF,KAAM6B,EAASnI,MAElB,CAGN,CACH,CUkMEgZ,CAAkBR,EAAajQ,EACjC,CAWM,SAAU8D,GAAYV,GVpQtB,IAAsB+M,IUqQV/M,EVpQhBtE,EAAU4R,SAAQC,IAChBA,EAAK7M,YAAYqM,EAAM,GUoQ3B;;;;;;;;;;;;;;;;OCncA,MAEMS,GAAa,2BASnB,IAAIC,GAAiD,KACrD,SAASC,KA2BP,OA1BKD,KACHA,GAAY9J,EAdA,8BACG,EAagC,CAC7CG,QAAS,CAACO,EAAIF,KAMZ,GACO,IADCA,EAEJ,IACEE,EAAGsJ,kBAAkBH,GACtB,CAAC,MAAO7Y,GAIPC,QAAQ0K,KAAK3K,EACd,CACJ,IAEF0C,OAAM1C,IACP,MAAMiV,GAAc9Q,OAA0B,WAAA,CAC5C8U,qBAAsBjZ,EAAE0D,SACxB,KAGCoV,EACT,CAyBO5I,eAAegJ,GACpBzE,EACA0E,GAEA,IACE,MACMvL,SADWmL,MACH3L,YAAYyL,GAAY,aAChC9L,EAAca,EAAGb,YAAY8L,UAC7B9L,EAAYqM,IAAID,EAAiBE,GAAW5E,UAC5C7G,EAAGG,IACV,CAAC,MAAO/N,GACP,GAAIA,aAAa2D,EACfkN,EAAOlG,KAAK3K,EAAE0D,aACT,CACL,MAAM4V,EAAcrE,GAAc9Q,OAA2B,UAAA,CAC3D8U,qBAAuBjZ,aAAA,EAAAA,EAAa0D,UAEtCmN,EAAOlG,KAAK2O,EAAY5V,QACzB,CACF,CACH,CAEA,SAAS2V,GAAW5E,GAClB,MAAO,GAAGA,EAAI/U,QAAQ+U,EAAIxM,QAAQsR,OACpC;;;;;;;;;;;;;;;;OCzEa,MAAAC,GAyBX,WAAA/Z,CAA6BoH,GAAA7J,KAAS6J,UAATA,EAT7B7J,KAAgByc,iBAAiC,KAU/C,MAAMhF,EAAMzX,KAAK6J,UAAUoD,YAAY,OAAOjC,eAC9ChL,KAAK0c,SAAW,IAAIC,GAAqBlF,GACzCzX,KAAK4c,wBAA0B5c,KAAK0c,SAASG,OAAOhL,MAAKxL,IACvDrG,KAAKyc,iBAAmBpW,EACjBA,IAEV,CASD,sBAAM0R,WACJ,IACE,MAMM+E,EANiB9c,KAAK6J,UACzBoD,YAAY,mBACZjC,eAI0ByI,wBACvBsJ,EAAOC,KACb,GAAyC,OAAd,QAAvBhY,EAAAhF,KAAKyc,wBAAkB,IAAAzX,OAAA,EAAAA,EAAAiY,cACzBjd,KAAKyc,uBAAyBzc,KAAK4c,wBAEM,OAAd,QAAvBM,EAAAld,KAAKyc,wBAAkB,IAAAS,OAAA,EAAAA,EAAAD,aACzB,OAKJ,GACEjd,KAAKyc,iBAAiBU,wBAA0BJ,GAChD/c,KAAKyc,iBAAiBQ,WAAWxL,MAC/B2L,GAAuBA,EAAoBL,OAASA,IAGtD,OAOA,GAJA/c,KAAKyc,iBAAiBQ,WAAW3b,KAAK,CAAEyb,OAAMD,UAK5C9c,KAAKyc,iBAAiBQ,WAAW3d,OA5EF,GA6E/B,CACA,MAAM+d,EAsMV,SACJJ,GAEA,GAA0B,IAAtBA,EAAW3d,OACb,OAAQ,EAGV,IAAI+d,EAAuB,EACvBC,EAAwBL,EAAW,GAAGF,KAE1C,IAAK,IAAI1d,EAAI,EAAGA,EAAI4d,EAAW3d,OAAQD,IACjC4d,EAAW5d,GAAG0d,KAAOO,IACvBA,EAAwBL,EAAW5d,GAAG0d,KACtCM,EAAuBhe,GAI3B,OAAOge,CACT;;;;;;;;;;;;;;;;OAxNuCE,CAC3Bvd,KAAKyc,iBAAiBQ,YAExBjd,KAAKyc,iBAAiBQ,WAAWO,OAAOH,EAAsB,EAC/D,CAGH,OAAOrd,KAAK0c,SAASe,UAAUzd,KAAKyc,iBACrC,CAAC,MAAOzZ,GACP6Q,EAAOlG,KAAK3K,EACb,CACF,CASD,yBAAM0a,SACJ,IAKE,GAJ8B,OAA1B1d,KAAKyc,wBACDzc,KAAK4c,wBAI0B,OAAd,UAAvB5c,KAAKyc,wBAAkB,IAAAzX,OAAA,EAAAA,EAAAiY,aACqB,IAA5Cjd,KAAKyc,iBAAiBQ,WAAW3d,OAEjC,MAAO,GAET,MAAMyd,EAAOC,MAEPW,iBAAEA,EAAgBC,cAAEA,GAkChB,SACdC,EACAC,EArJuB,MA4JvB,MAAMH,EAA4C,GAElD,IAAIC,EAAgBC,EAAgBE,QACpC,IAAK,MAAMX,KAAuBS,EAAiB,CAEjD,MAAMG,EAAiBL,EAAiBM,MACtCC,GAAMA,EAAGpB,QAAUM,EAAoBN,QAEzC,GAAKkB,GAgBH,GAHAA,EAAeG,MAAM7c,KAAK8b,EAAoBL,MAG1CqB,GAAWT,GAAoBG,EAAS,CAC1CE,EAAeG,MAAME,MACrB,KACD,OAbD,GAJAV,EAAiBrc,KAAK,CACpBwb,MAAOM,EAAoBN,MAC3BqB,MAAO,CAACf,EAAoBL,QAE1BqB,GAAWT,GAAoBG,EAAS,CAG1CH,EAAiBU,MACjB,KACD,CAYHT,EAAgBA,EAAcG,MAAM,EACrC,CACD,MAAO,CACLJ,mBACAC,gBAEJ,CAhFkDU,CAC1Cte,KAAKyc,iBAAiBQ,YAElBsB,EAAe5b,EACnB2B,KAAKiX,UAAU,CAAEtJ,QAAS,EAAGgL,WAAYU,KAgB3C,OAbA3d,KAAKyc,iBAAiBU,sBAAwBJ,EAC1Ca,EAActe,OAAS,GAEzBU,KAAKyc,iBAAiBQ,WAAaW,QAI7B5d,KAAK0c,SAASe,UAAUzd,KAAKyc,oBAEnCzc,KAAKyc,iBAAiBQ,WAAa,GAE9Bjd,KAAK0c,SAASe,UAAUzd,KAAKyc,mBAE7B8B,CACR,CAAC,MAAOvb,GAEP,OADA6Q,EAAOlG,KAAK3K,GACL,EACR,CACF,EAGH,SAASga,KAGP,OAFc,IAAIzZ,MAELgL,cAAciQ,UAAU,EAAG,GAC1C,CAkDa,MAAA7B,GAEX,WAAAla,CAAmBgV,GAAAzX,KAAGyX,IAAHA,EACjBzX,KAAKye,wBAA0Bze,KAAK0e,8BACrC,CACD,kCAAMA,GACJ,QAAK7Y,KAGIE,IACJ8L,MAAK,KAAM,IACXnM,OAAM,KAAM,GAElB,CAID,UAAMmX,GAEJ,SAD8B7c,KAAKye,wBAG5B,CACL,MAAME,QD5LLzL,eACLuE,GAEA,IACE,MACM7G,SADWmL,MACH3L,YAAYyL,IACpBxV,QAAeuK,EAAGb,YAAY8L,IAAYzR,IAAIiS,GAAW5E,IAI/D,aADM7G,EAAGG,KACF1K,CACR,CAAC,MAAOrD,GACP,GAAIA,aAAa2D,EACfkN,EAAOlG,KAAK3K,EAAE0D,aACT,CACL,MAAM4V,EAAcrE,GAAc9Q,OAAyB,UAAA,CACzD8U,qBAAuBjZ,aAAA,EAAAA,EAAa0D,UAEtCmN,EAAOlG,KAAK2O,EAAY5V,QACzB,CACF,CACH,CCuKuCkY,CAA4B5e,KAAKyX,KAClE,OAAIkH,aAAkB,EAAlBA,EAAoB1B,YACf0B,EAEA,CAAE1B,WAAY,GAExB,CARC,MAAO,CAAEA,WAAY,GASxB,CAED,eAAMQ,CAAUoB,SAEd,SAD8B7e,KAAKye,wBAG5B,CACL,MAAMK,QAAiC9e,KAAK6c,OAC5C,OAAOX,GAA2Blc,KAAKyX,IAAK,CAC1C0F,sBAEE,QADAnY,EAAA6Z,EAAiB1B,6BACjB,IAAAnY,EAAAA,EAAA8Z,EAAyB3B,sBAC3BF,WAAY4B,EAAiB5B,YAEhC,CACF,CAED,SAAMxQ,CAAIoS,SAER,SAD8B7e,KAAKye,wBAG5B,CACL,MAAMK,QAAiC9e,KAAK6c,OAC5C,OAAOX,GAA2Blc,KAAKyX,IAAK,CAC1C0F,sBAEE,QADAnY,EAAA6Z,EAAiB1B,6BACjB,IAAAnY,EAAAA,EAAA8Z,EAAyB3B,sBAC3BF,WAAY,IACP6B,EAAyB7B,cACzB4B,EAAiB5B,aAGzB,CACF,EAQG,SAAUmB,GAAWP,GAEzB,OAAOlb,EAEL2B,KAAKiX,UAAU,CAAEtJ,QAAS,EAAGgL,WAAYY,KACzCve,MACJ,CC5RM,IAAiCub,MCMhB,GDLrBnD,GACE,IAAI5O,EACF,mBACAe,GAAa,IAAI2J,EAA0B3J,IAAU,YAIzD6N,GACE,IAAI5O,EACF,aACAe,GAAa,IAAI2S,GAAqB3S,IAAU,YAMpDkQ,GAAgBrX,EAAMuP,EAAS4I,IAE/Bd,GAAgBrX,EAAMuP,EAAS,WAE/B8H,GAAgB,UAAW,yHPwBb,SACdtC,EACA3N,GAEC2N,EAAwB5N,UAAUqD,wBAAwBpD,EAC7D,4BA6GgB,WACdyN,GAAYwH,OACd,wEAhBM,SACJ/W,GAEA,OAAIA,cAG2CvE,IAAvCuE,EAA0BqS,QACpC,+CArCM,SACJ5C,EACA/U,EACAqI,EAA6BpB,IAE7BkO,GAAaJ,EAAK/U,GAAM8I,cAAcT,EACxC,qCI+LgB,SAAOrI,EAAeiH,IACpC,MAAM8N,EAAMJ,GAAMjN,IAAI1H,GACtB,IAAK+U,GAAO/U,IAASiH,IAAsB5E,IACzC,OAAOwV,KAET,IAAK9C,EACH,MAAMQ,GAAc9Q,OAAwB,SAAA,CAAE6M,QAAStR,IAGzD,OAAO+U,CACT,UAMgB,WACd,OAAOlX,MAAMoL,KAAK0L,GAAMzL,SAC1B,uCAzHgB,SACdwM,EACA4G,GAEA,IjBtJyB,oBAAXlb,QAA0B6B,OiBsJpBA,IAElB,MAAMsS,GAAc9Q,OAAM,kCAO5B,IAAI8X,OAJoDxb,IAApDub,EAAiBvG,iCACnBuG,EAAiBvG,gCAAiC,GAKlDwG,EADEjH,GAAeI,GACJA,EAASnN,QAETmN,EAIf,MAAM8G,EACD5b,OAAA+U,OAAA/U,OAAA+U,OAAA,GAAA2G,GACAC,GAgBL,QAX+Bxb,IAA3Byb,EAAQpF,uBACHoF,EAAQpF,oBAUuBrW,IAApCub,EAAiBlF,gBACiB,oBAAzBJ,qBACT,MAAMzB,GAAc9Q,OAElB,sCAAA,CAAE,GAKR,MAAMgY,EAAa,GAhBF,CAACC,GACT,IAAIA,GAAGC,QACZ,CAACC,EAAM/f,IAAOggB,KAAKC,KAAK,GAAIF,GAAQ/f,EAAEC,WAAW,GAAM,GACvD,GAaoBigB,CAASnb,KAAKiX,UAAU2D,IAC1CzE,EAAcnD,GAAYlN,IAAI+U,GACpC,GAAI1E,EAIF,OAHCA,EAAsCZ,YACrCmF,EAAiBlF,gBAEZW,EAGT,MAAM5Q,EAAY,IAAIgD,EAAmBsS,GACzC,IAAK,MAAMrV,KAAayN,GAAY3L,SAClC/B,EAAUkD,aAAajD,GAGzB,MAAM4Q,EAAS,IAAIvB,GACjB8F,EACAD,EACAG,EACAtV,GAKF,OAFAyN,GAAY5M,IAAIyU,EAAYzE,GAErBA,CACT;;;;;;;;;;;;;;;;OKjRa,MAAAgF,GAGX,WAAAjd,CACWkd,EACQC,GADR5f,KAAS2f,UAATA,EACQ3f,KAAQ4f,SAARA,EAGjBpI,GACEmI,EACA,IAAI7W,EAAU,cAAc,IAAM9I,MAAI,UAEzC,CAED,kCAAIyY,GACF,OAAOzY,KAAK2f,UAAUlH,8BACvB,CAED,kCAAIA,CAA+B5J,GACjC7O,KAAKyY,+BAAiC5J,CACvC,CAED,QAAInM,GACF,OAAO1C,KAAK2f,UAAUjd,IACvB,CAED,WAAIuI,GACF,OAAOjL,KAAK2f,UAAU1U,OACvB,CAED,SAEE,OADAjL,KAAK4f,SAAS5T,SAAS6T,UAAU7f,KAAK0C,MAC/B0X,GAAUpa,KAAK2f,UACvB,CAgBD,WAAAG,CACEpd,EACAqI,EAA6BgV,IAK7B,OAHA/f,KAAK2f,UAAUhH,iBAGR3Y,KAAK2f,UAAU9V,UAAUoD,YAAYvK,GAAcsI,aAAa,CACrEX,WAAYU,GAEf;;;;;;;;;;;;;;;;OCnEH,MAWakN,GAAgB,IAAI/Q,EAC/B,aACA,WAbiC,CACjC,SACE,oFAEF,uBACE;;;;;;;;;;;;;;;;;ACUE,SAAU8Y,GACdC,GAEA,MAAMC,EAAwC,CAAA,EAKxCC,EAAgC,CAIpCC,YAAY,EACZ7F,cA8DF,SACEtP,EACAuP,EAAY,IAEZ,MAAM/C,EAAM4I,GACVpV,EACAuP,GAGF,GAAIzS,EAASmY,EAAMzI,EAAI/U,MACrB,OAAOwd,EAAKzI,EAAI/U,MAGlB,MAAM4d,EAAY,IAAIL,EAAgBxI,EAAK0I,GAE3C,OADAD,EAAKzI,EAAI/U,MAAQ4d,EACVA,CACR,EA5EC7I,MACAsC,gBAAiBwG,GACjBxR,YAAayR,GACbvF,MAAOwF,GAEPP,KAAM,KACN5F,YAAaoG,GACb1U,SAAU,CACR2U,kBA8EJ,SACE7W,GAEA,MAAM6N,EAAgB7N,EAAUpH,KAC1Bke,EAA6BjJ,EAAc7U,QAAQ,UAAW,IACpE,GACE+d,GAA+B/W,IACQ,WAAvCA,EAAUd,KACV,CAGA,MAAM8X,EAAmB,CACvBC,EAAsBtJ,OAGtB,GAA2D,mBAA/CsJ,EAAeH,GAGzB,MAAM3I,GAAc9Q,OAAsC,uBAAA,CACxD6M,QAAS2D,IAMb,OAAQoJ,EAAeH,IAA6B,OAIvBnd,IAA3BqG,EAAUZ,cACZ/F,EAAW2d,EAAkBhX,EAAUZ,cAIxCiX,EAAkBS,GAA8BE,EAIhDb,EAAgBjZ,UAAkB4Z,GAIjC,YAAaxS,GAEX,OADmBpO,KAAK8f,YAAYkB,KAAKhhB,KAAM2X,GAC7BlH,MAChBzQ,KACA8J,EAAUb,kBAAoBmF,EAAO,GAEzC,CACH,CAED,MAA8C,WAAvCtE,EAAUd,KAEZmX,EAAkBS,GACnB,IACL,EApIGf,UA4BJ,SAAmBnd,UACVwd,EAAKxd,EACb,EA7BGue,aAuIJ,SAAsBxJ,EAAkB/U,GACtC,GAAa,eAATA,EACF,OAAO,KAKT,OAFmBA,CAGpB,EA9IGwe,iBAiCJ,SAASzJ,EAAI/U,GAEX,IAAKqF,EAASmY,EADdxd,EAAOA,GAAQye,IAEb,MAAMlJ,GAAc9Q,OAAwB,SAAA,CAAE6M,QAAStR,IAEzD,OAAOwd,EAAKxd,EACb,CAyGD,OAjICyd,EAA2B,QAAIA,EAGhC7c,OAAO8d,eAAejB,EAAW,OAAQ,CACvC/V,IAmDF,WAEE,OAAO9G,OAAOgF,KAAK4X,GAAMnU,KAAIrJ,GAAQwd,EAAKxd,IAC3C,IA/BD+U,EAAS,IAAIwI,EAsGNE,CACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9LM,MAAAP,GCGU,WACd,MAAMO,EAAYH,GAA4BN,IAE9CS,EAAU7F,YAAc,GAAG6F,EAAU7F,mBAErC,MAAMqG,EAAoBR,EAAUnU,SAAS2U,kBAuB7C,OAtBAR,EAAUnU,SAAS2U,kBAMnB,SAEE7W,GAGA,GACyC,WAAvCA,EAAUd,OACTc,EAAUpH,KAAK+F,SAAS,iBACxBqB,EAAUpH,KAAK+F,SAAS,iBAEzB,MAAMhI,MAAM,GAAGiC,0DAGjB,OAAOie,EAAkB7W,EAC1B,EAEMqW,CACT,CDhCiBkB;;;;;;;;;;;;;;;;;AECX,SAAiCxG,GAErCd,kCAA+Bc,EACjC,CFFAyG,CAAuB;;;;;;;;;;;;;;;;;AGFvB1B,GAAS7F,oCAA+B,cCpBpC,IAAAwH,GAAEve,GAAEwe,GAAE,WAAW,IAAID,EAAE1d,KAAK4d,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,GAAG,GAAGH,GAAGA,EAAEI,cAAc,GAAGJ,EAAEI,cAAcF,YAAYnT,MAAM,OAAOiT,CAAC,EAAEK,GAAE,SAASL,GAAG,GAAG,YAAY9c,SAASod,WAAW,MAAM,UAAU,IAAI7e,EAAEwe,KAAI,GAAGxe,EAAE,CAAC,GAAGue,EAAEve,EAAE8e,eAAe,MAAM,UAAU,GAAG,IAAI9e,EAAE+e,4BAA4BR,EAAEve,EAAE+e,2BAA2B,MAAM,kBAAkB,GAAG,IAAI/e,EAAEgf,aAAaT,EAAEve,EAAEgf,YAAY,MAAM,oBAAoB,CAAC,MAAM,UAAU,EAAE3iB,GAAE,SAASkiB,GAAG,IAAIve,EAAEue,EAAEU,SAAS,OAAO,IAAIV,EAAEW,SAASlf,EAAEyY,cAAczY,EAAEmf,cAAcrf,QAAQ,KAAK,GAAG,EAAEqF,GAAE,SAASoZ,EAAEve,GAAG,IAAIwe,EAAE,GAAG,IAAI,KAAKD,GAAG,IAAIA,EAAEW,UAAU,CAAC,IAAIN,EAAEL,EAAEpZ,EAAEyZ,EAAEQ,GAAG,IAAIR,EAAEQ,GAAG/iB,GAAEuiB,IAAIA,EAAES,WAAWT,EAAES,UAAU5c,OAAOmc,EAAES,UAAU5c,MAAM6c,QAAQV,EAAES,UAAU5c,MAAM6c,OAAOhjB,OAAO,IAAIsiB,EAAES,UAAU5c,MAAM6c,OAAOxf,QAAQ,OAAO,KAAK,IAAI,GAAG0e,EAAEliB,OAAO6I,EAAE7I,QAAQ0D,GAAG,KAAK,EAAE,OAAOwe,GAAGrZ,EAAE,GAAGqZ,EAAEA,EAAErZ,EAAE,IAAIqZ,EAAErZ,EAAEyZ,EAAEQ,GAAG,MAAMb,EAAEK,EAAEW,UAAU,CAAC,CAAC,MAAMhB,IAAI,OAAOC,CAAC,EAAEgB,IAAG,EAAyBvgB,GAAE,SAASsf,GAAGnQ,iBAAiB,YAAU,SAAWpO,GAAGA,EAAEyf,YAAYD,GAAExf,EAAE0f,UAAUnB,EAAEve,GAAI,IAAE,EAAG,EAAEoc,GAAE,WAAW,IAAImC,EAAEC,KAAI,OAAOD,GAAGA,EAAEoB,iBAAiB,CAAC,EAAEC,GAAE,SAASrB,EAAEve,GAAG,IAAI4e,EAAEJ,KAAIniB,EAAE,WAAgK,OAAtVmjB,IAAsM,EAAEnjB,EAAE,qBAAqBuiB,IAAInd,SAASoe,cAAczD,KAAI,EAAE/f,EAAE,YAAYoF,SAASqe,aAAazjB,EAAE,UAAUuiB,EAAE5Y,OAAO3J,EAAEuiB,EAAE5Y,KAAKlG,QAAQ,KAAK,OAAa,CAACJ,KAAK6e,EAAE9b,WAAM,IAASzC,GAAG,EAAEA,EAAE+f,OAAO,OAAOC,MAAM,EAAEzX,QAAQ,GAAG6W,GAAG,MAAMa,OAAO1f,KAAK+K,MAAM,KAAK2U,OAAO1D,KAAK2D,MAAM,cAAc3D,KAAK4D,UAAU,MAAMC,eAAe/jB,EAAE,EAAEgkB,GAAE,SAAS9B,EAAEve,EAAEwe,GAAG,IAAI,GAAG8B,oBAAoBC,oBAAoB9a,SAAS8Y,GAAG,CAAC,IAAIK,EAAE,IAAI0B,qBAAqB,SAAS/B,GAAGjc,QAAQF,UAAUyM,MAAI,WAAa7O,EAAEue,EAAEiC,aAAc,GAAG,IAAG,OAAO5B,EAAE6B,QAAQngB,OAAO+U,OAAO,CAACrP,KAAKuY,EAAEmC,UAAS,GAAIlC,GAAG,CAAA,IAAKI,CAAC,CAAC,CAAC,MAAML,IAAI,EAAEoC,GAAE,SAASpC,EAAEve,EAAEwe,EAAEI,GAAG,IAAIviB,EAAE8I,EAAE,OAAO,SAASqa,GAAGxf,EAAEyC,OAAO,IAAI+c,GAAGZ,MAAMzZ,EAAEnF,EAAEyC,OAAOpG,GAAG,UAAK,IAASA,KAAKA,EAAE2D,EAAEyC,MAAMzC,EAAEggB,MAAM7a,EAAEnF,EAAE+f,OAAO,SAASxB,EAAEve,GAAG,OAAOue,EAAEve,EAAE,GAAG,OAAOue,EAAEve,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEyC,MAAM+b,GAAGD,EAAEve,GAAG,CAAC,EAAE4gB,GAAE,SAASrC,GAAGsC,uBAAuB,WAAW,OAAOA,uBAAqB,WAAa,OAAOtC,GAAI,GAAG,GAAE,EAAEniB,GAAE,SAASmiB,GAAG9c,SAAS2M,iBAAiB,oBAAkB,WAAa,WAAW3M,SAASqf,iBAAiBvC,GAAI,GAAE,EAAEwC,GAAE,SAASxC,GAAG,IAAIve,GAAE,EAAG,OAAO,WAAWA,IAAIue,IAAIve,GAAE,EAAG,CAAC,EAAEghB,IAAG,EAAEC,GAAE,WAAW,MAAM,WAAWxf,SAASqf,iBAAiBrf,SAASoe,aAAa,IAAI,CAAC,EAAEqB,GAAE,SAAS3C,GAAG,WAAW9c,SAASqf,iBAAiBE,IAAG,IAAIA,GAAE,qBAAqBzC,EAAEvY,KAAKuY,EAAEmB,UAAU,EAAEyB,KAAI,EAAEC,GAAE,WAAWhT,iBAAiB,mBAAmB8S,IAAE,GAAI9S,iBAAiB,qBAAqB8S,IAAE,EAAG,EAAEC,GAAE,WAAWlT,oBAAoB,mBAAmBiT,IAAE,GAAIjT,oBAAoB,qBAAqBiT,IAAE,EAAG,EAAEG,GAAE,WAAW,OAAOL,GAAE,IAAIA,GAAEC,KAAIG,KAAIniB,IAAG,WAAWqiB,YAAY,WAAWN,GAAEC,KAAIG,IAAI,GAAE,EAAG,KAAI,CAAC,mBAAIG,GAAkB,OAAOP,EAAC,EAAE,EAAE5b,GAAE,SAASmZ,GAAG9c,SAASoe,aAAazR,iBAAiB,sBAAsB,WAAW,OAAOmQ,GAAI,IAAE,GAAIA,GAAG,EAAEiD,GAAE,CAAC,KAAK,KAAwaC,GAAE,CAAC,GAAG,KAAKC,GAAE,SAASnD,EAAEve,IAAI,SAASue,EAAEve,GAAGA,EAAEA,GAAG,CAAA,EAAhd,SAASue,EAAEve,GAAGA,EAAEA,GAAG,CAAA,EAAGoF,IAAG,WAAW,IAAIoZ,EAAEI,EAAEyC,KAAIhlB,EAAEujB,GAAE,OAAOza,EAAEkb,GAAE,SAAO,SAAW9B,GAAGA,EAAE5F,SAAS,SAAS4F,GAAG,2BAA2BA,EAAE7e,OAAOyF,EAAEwc,aAAapD,EAAEqD,UAAUhD,EAAE2C,kBAAkBllB,EAAEoG,MAAM8Z,KAAKsF,IAAItD,EAAEqD,UAAUxF,KAAI,GAAG/f,EAAEkM,QAAQjK,KAAKigB,GAAGC,GAAE,IAAM,GAAG,IAAGrZ,IAAIqZ,EAAEmC,GAAEpC,EAAEliB,EAAEmlB,GAAExhB,EAAE8hB,kBAAkB7iB,IAAC,SAAW2f,GAAGviB,EAAEujB,GAAE,OAAOpB,EAAEmC,GAAEpC,EAAEliB,EAAEmlB,GAAExhB,EAAE8hB,kBAAkBlB,IAAC,WAAavkB,EAAEoG,MAAMgc,YAAYnT,MAAMsT,EAAEc,UAAUlB,GAAE,EAAI,GAAG,IAAI,GAAE,CAAoDuD,CAAEhB,eAAc,IAAIvC,EAAEI,EAAEgB,GAAE,MAAM,GAAGvjB,EAAE,EAAE8I,EAAE,GAAGqa,EAAE,SAASjB,GAAGA,EAAE5F,SAAO,SAAW4F,GAAG,IAAIA,EAAEyD,eAAe,CAAC,IAAIhiB,EAAEmF,EAAE,GAAGqZ,EAAErZ,EAAEA,EAAE7I,OAAO,GAAGD,GAAGkiB,EAAEqD,UAAUpD,EAAEoD,UAAU,KAAKrD,EAAEqD,UAAU5hB,EAAE4hB,UAAU,KAAKvlB,GAAGkiB,EAAE9b,MAAM0C,EAAE7G,KAAKigB,KAAKliB,EAAEkiB,EAAE9b,MAAM0C,EAAE,CAACoZ,GAAG,CAAE,IAAGliB,EAAEuiB,EAAEnc,QAAQmc,EAAEnc,MAAMpG,EAAEuiB,EAAErW,QAAQpD,EAAEqZ,IAAI,EAAEjiB,EAAE8jB,GAAE,eAAeb,GAAGjjB,IAAIiiB,EAAEmC,GAAEpC,EAAEK,EAAE6C,GAAEzhB,EAAE8hB,kBAAkB1lB,eAAcojB,EAAEjjB,EAAE0lB,eAAezD,GAAE,EAAI,IAAGvf,IAAG,WAAW5C,EAAE,EAAEuiB,EAAEgB,GAAE,MAAM,GAAGpB,EAAEmC,GAAEpC,EAAEK,EAAE6C,GAAEzhB,EAAE8hB,kBAAkBlB,eAAc,OAAOpC,GAAI,GAAG,IAAG8C,WAAW9C,EAAE,GAAI,IAAG,CAA3f,WAAugBxe,GAAG,IAAIwe,EAAE,SAASD,GAAG,IAAIve,EAAEwe,EAAE,CAAA,EAAG,GAAGD,EAAEhW,QAAQjM,OAAO,CAAC,IAAID,EAAEkiB,EAAEhW,QAAQ8T,QAAQ,SAASkC,EAAEve,GAAG,OAAOue,GAAGA,EAAE9b,MAAMzC,EAAEyC,MAAM8b,EAAEve,CAAE,IAAG,GAAG3D,GAAGA,EAAE6lB,SAAS7lB,EAAE6lB,QAAQ5lB,OAAO,CAAC,IAAIkjB,GAAGxf,EAAE3D,EAAE6lB,SAASjH,MAAI,SAAWsD,GAAG,OAAOA,EAAE4D,MAAM,IAAI5D,EAAE4D,KAAKjD,QAAS,KAAIlf,EAAE,GAAGwf,IAAIhB,EAAE,CAAC4D,mBAAmBjd,GAAEqa,EAAE2C,MAAME,iBAAiBhmB,EAAEulB,UAAUU,kBAAkBjmB,EAAEoG,MAAM8f,mBAAmB/C,EAAEgD,kBAAkBnmB,EAAEomB,UAAU7D,GAAEviB,EAAEulB,YAAY,CAAC,CAAC,OAAOthB,OAAO+U,OAAOkJ,EAAE,CAACmE,YAAYlE,GAAG,CAA/a,CAAibxe,GAAGue,EAAEC,EAAG,GAAExe,EAAE,EAA6Z2iB,GAAE,EAAEC,GAAE,IAAIpd,GAAE,EAAEqd,GAAE,SAAStE,GAAGA,EAAE5F,SAAS,SAAS4F,GAAGA,EAAEuE,gBAAgBF,GAAErG,KAAKwG,IAAIH,GAAErE,EAAEuE,eAAetd,GAAE+W,KAAKsF,IAAIrc,GAAE+Y,EAAEuE,eAAeH,GAAEnd,IAAGA,GAAEod,IAAG,EAAE,EAAE,EAAG,GAAE,EAAEI,GAAE,WAAW,OAAOzE,GAAEoE,GAAElE,YAAYwE,kBAAkB,CAAC,EAAEC,GAAE,WAAW,qBAAqBzE,aAAaF,KAAIA,GAAE8B,GAAE,QAAQwC,GAAE,CAAC7c,KAAK,QAAQ0a,UAAS,EAAGyC,kBAAkB,IAAI,EAAEC,GAAE,GAAGC,GAAE,IAAIrc,IAAIsc,GAAE,EAA8EC,GAAE,GAAGC,GAAE,SAASjF,GAAG,GAAGgF,GAAE5K,SAAO,SAAW3Y,GAAG,OAAOA,EAAEue,EAAG,IAAGA,EAAEuE,eAAe,gBAAgBvE,EAAEkF,UAAU,CAAC,IAAIzjB,EAAEojB,GAAEA,GAAE9mB,OAAO,GAAGkiB,EAAE6E,GAAEjc,IAAImX,EAAEuE,eAAe,GAAGtE,GAAG4E,GAAE9mB,OAAO,IAAIiiB,EAAEmF,SAAS1jB,EAAE2jB,QAAQ,CAAC,GAAGnF,EAAED,EAAEmF,SAASlF,EAAEmF,SAASnF,EAAEjW,QAAQ,CAACgW,GAAGC,EAAEmF,QAAQpF,EAAEmF,UAAUnF,EAAEmF,WAAWlF,EAAEmF,SAASpF,EAAEqD,YAAYpD,EAAEjW,QAAQ,GAAGqZ,WAAWpD,EAAEjW,QAAQjK,KAAKigB,OAAO,CAAC,IAAIK,EAAE,CAACQ,GAAGb,EAAEuE,cAAca,QAAQpF,EAAEmF,SAASnb,QAAQ,CAACgW,IAAI8E,GAAE3b,IAAIkX,EAAEQ,GAAGR,GAAGwE,GAAE9kB,KAAKsgB,EAAE,CAACwE,GAAEvV,MAAM,SAAS0Q,EAAEve,GAAG,OAAOA,EAAE2jB,QAAQpF,EAAEoF,OAAQ,IAAGP,GAAE9mB,OAAO,IAAI8mB,GAAE5I,OAAO,IAAI7B,SAAO,SAAW4F,GAAG,OAAO8E,GAAE5a,OAAO8V,EAAEa,GAAI,GAAE,CAAC,CAAC,EAAEwE,GAAE,SAASrF,GAAG,IAAIve,EAAEa,KAAKgjB,qBAAqBhjB,KAAKygB,WAAW9C,GAAG,EAAE,OAAOD,EAAEwC,GAAExC,GAAG,WAAW9c,SAASqf,gBAAgBvC,KAAKC,EAAExe,EAAEue,GAAGniB,GAAEmiB,IAAIC,CAAC,EAAEsF,GAAE,CAAC,IAAI,KAAKC,GAAE,SAASxF,EAAEve,GAAG,2BAA2Ba,MAAM,kBAAkBmjB,uBAAuBhgB,YAAYhE,EAAEA,GAAG,CAAA,EAAGoF,IAAC,WAAa,IAAIoZ,EAAE0E,KAAI,IAAItE,EAAEviB,EAAEujB,GAAE,OAAOza,EAAE,SAASoZ,GAAGqF,IAAC,WAAarF,EAAE5F,QAAQ6K,IAAG,IAAIxjB,EAAz8B,WAAW,IAAIue,EAAEhC,KAAKwG,IAAIK,GAAE9mB,OAAO,EAAEigB,KAAK2D,OAAO8C,KAAIM,IAAG,KAAK,OAAOF,GAAE7E,EAAE,CAAm4B0F,GAAIjkB,GAAGA,EAAE2jB,UAAUtnB,EAAEoG,QAAQpG,EAAEoG,MAAMzC,EAAE2jB,QAAQtnB,EAAEkM,QAAQvI,EAAEuI,QAAQqW,IAAK,GAAE,EAAEY,EAAEa,GAAE,QAAQlb,EAAE,CAACge,kBAAkB,QAAQ3E,EAAExe,EAAEmjB,yBAAoB,IAAS3E,EAAEA,EAAE,KAAKI,EAAE+B,GAAEpC,EAAEliB,EAAEynB,GAAE9jB,EAAE8hB,kBAAkBtC,IAAIA,EAAEiB,QAAQ,CAACza,KAAK,cAAc0a,UAAS,IAAKtkB,IAAG,WAAW+I,EAAEqa,EAAEyC,eAAerD,GAAE,EAAI,IAAG3f,IAAC,WAAaqkB,GAAEN,KAAII,GAAE9mB,OAAO,EAAE+mB,GAAEtH,QAAQ1f,EAAEujB,GAAE,OAAOhB,EAAE+B,GAAEpC,EAAEliB,EAAEynB,GAAE9jB,EAAE8hB,iBAAkB,IAAI,IAAG,EAAEoC,GAAE,GAAGC,GAAE,GAAGxf,GAAE,EAAEyf,GAAE,IAAI9X,QAAQ+X,GAAE,IAAIrd,IAAIsd,IAAG,EAAEC,GAAE,SAAShG,GAAG2F,GAAEA,GAAEjE,OAAO1B,GAAGiG,IAAG,EAAEA,GAAE,WAAWF,GAAE,IAAIA,GAAEV,GAAEa,IAAG,EAAEA,GAAE,WAAWJ,GAAEK,KAAK,IAAIL,GAAE1L,SAAO,SAAW4F,EAAEve,GAAGqjB,GAAE7b,IAAIxH,IAAIqkB,GAAE5b,OAAOzI,EAAG,IAAG,IAAIue,EAAE6E,GAAEra,KAAG,SAAWwV,GAAG,OAAO6F,GAAEhd,IAAImX,EAAEhW,QAAQ,GAAI,IAAGvI,EAAEmkB,GAAE7nB,OAAO,GAAG6nB,GAAEA,GAAErb,QAAQ,SAAS0V,EAAEI,GAAG,OAAOA,GAAG5e,GAAGue,EAAE9Y,SAAS+Y,EAAG,IAAG,IAAI,IAAIA,EAAE,IAAIhV,IAAIoV,EAAE,EAAEA,EAAEuF,GAAE7nB,OAAOsiB,IAAI,CAAC,IAAIviB,EAAE8nB,GAAEvF,GAAG+F,GAAGtoB,EAAEulB,UAAUvlB,EAAEuoB,eAAejM,SAAO,SAAW4F,GAAGC,EAAE/U,IAAI8U,EAAG,GAAE,CAAC,IAAIpZ,EAAE+e,GAAE5nB,OAAO,EAAE,GAAG4nB,GAAEA,GAAEpb,QAAM,SAAWyV,EAAEve,GAAG,OAAOue,EAAEqD,UAAUjd,IAAG3E,EAAEmF,GAAGqZ,EAAEhX,IAAI+W,EAAG,IAAG+F,IAAG,CAAC,EAAEf,GAAEjlB,eAAeigB,GAAGA,EAAEuE,eAAevE,EAAEne,SAASikB,GAAE7c,IAAI+W,EAAEuE,gBAAgBuB,GAAE3c,IAAI6W,EAAEuE,cAAcvE,EAAEne,OAAQ,IAAG,SAASme,GAAG,IAAIve,EAAEwe,EAAED,EAAEqD,UAAUrD,EAAEmF,SAAS/e,GAAE4X,KAAKsF,IAAIld,GAAE4Z,EAAEqG,eAAe,IAAI,IAAIhG,EAAEuF,GAAE7nB,OAAO,EAAEsiB,GAAG,EAAEA,IAAI,CAAC,IAAIviB,EAAE8nB,GAAEvF,GAAG,GAAGrC,KAAKsI,IAAIrG,EAAEniB,EAAEyoB,aAAa,EAAE,EAAE9kB,EAAE3D,GAAGulB,UAAUrF,KAAKwG,IAAIxE,EAAEqD,UAAU5hB,EAAE4hB,WAAW5hB,EAAE+kB,gBAAgBxI,KAAKwG,IAAIxE,EAAEwG,gBAAgB/kB,EAAE+kB,iBAAiB/kB,EAAE4kB,cAAcrI,KAAKsF,IAAItD,EAAEqG,cAAc5kB,EAAE4kB,eAAe5kB,EAAEuI,QAAQjK,KAAKigB,GAAG,KAAK,CAAC,CAACve,IAAIA,EAAE,CAAC4hB,UAAUrD,EAAEqD,UAAUmD,gBAAgBxG,EAAEwG,gBAAgBH,cAAcrG,EAAEqG,cAAcE,WAAWtG,EAAEjW,QAAQ,CAACgW,IAAI4F,GAAE7lB,KAAK0B,KAAKue,EAAEuE,eAAe,gBAAgBvE,EAAEkF,YAAYW,GAAE1c,IAAI6W,EAAEve,GAAGwkB,IAAI,IAAM,IAAWG,GAAG,SAASpG,EAAEve,GAAG,IAAI,IAAIwe,EAAEI,EAAE,GAAGviB,EAAE,EAAEmiB,EAAE0F,GAAE7nB,GAAGA,IAAI,KAAKmiB,EAAEoD,UAAUpD,EAAEkF,SAASnF,GAAG,CAAC,GAAGC,EAAEoD,UAAU5hB,EAAE,MAAM4e,EAAEtgB,KAAKkgB,EAAE,CAAC,OAAOI,CAAC,EAAEoG,GAAG,SAASzG,EAAEC,GAAGxe,KAAIA,GAAEqgB,GAAE,uBAAuBkE,KAAIR,aAAY/jB,GAAG,IAAIwe,EAAE,SAASD,GAAG,IAAIve,EAAEue,EAAEhW,QAAQ,GAAGiW,EAAE4F,GAAEhd,IAAIpH,GAAG3D,EAAE2D,EAAE+kB,gBAAgBvF,EAAEhB,EAAEoG,cAAcroB,EAAEiiB,EAAEjW,QAAQsF,MAAI,SAAW0Q,EAAEve,GAAG,OAAOue,EAAEwG,gBAAgB/kB,EAAE+kB,eAAgB,IAAG9lB,EAAE0lB,GAAG3kB,EAAE4hB,UAAUpC,GAAGpD,EAAEmC,EAAEhW,QAAQ0S,MAAI,SAAWsD,GAAG,OAAOA,EAAEne,MAAO,IAAGwf,EAAExD,GAAGA,EAAEhc,QAAQikB,GAAEjd,IAAIpH,EAAE8iB,eAAezC,EAAE,CAACrgB,EAAE4hB,UAAU5hB,EAAE0jB,SAASlE,GAAGS,OAAOhhB,EAAE8J,cAAcwV,GAAG,OAAOA,EAAEqD,UAAUrD,EAAEmF,QAAS,KAAI/C,EAAEpE,KAAKsF,IAAIpU,MAAM8O,KAAK8D,GAAGO,EAAE,CAACqE,kBAAkB9f,GAAEya,GAAGsF,yBAAyBtF,EAAEuF,gBAAgBnlB,EAAEN,KAAK0lB,WAAW,OAAO,WAAW,UAAUC,gBAAgBrlB,EAAE4hB,UAAU0D,cAAc3E,EAAE4E,sBAAsBhpB,EAAEipB,0BAA0BvmB,EAAEwmB,WAAWppB,EAAE2D,EAAE4hB,UAAU8D,mBAAmBlG,EAAEnjB,EAAEspB,kBAAkBpJ,KAAKsF,IAAIlB,EAAEnB,EAAE,GAAGiD,UAAU7D,GAAE5e,EAAE4hB,YAAY,OAAOthB,OAAO+U,OAAOkJ,EAAE,CAACmE,YAAY9B,GAAG,CAAjuB,CAAmuB5gB,GAAGue,EAAEC,EAAG,GAAEA,EAAE,EAAEoH,GAAG,CAAC,KAAK,KAAKC,GAAG,CAAA,EAAGC,GAAG,SAASvH,EAAEve,IAAI,SAASue,EAAEve,GAAGA,EAAEA,GAAG,CAAA,EAAGoF,IAAG,WAAW,IAAIoZ,EAAEI,EAAEyC,KAAIhlB,EAAEujB,GAAE,OAAOza,EAAE,SAASoZ,GAAGve,EAAE8hB,mBAAmBvD,EAAEA,EAAExD,OAAO,IAAIwD,EAAE5F,SAAO,SAAW4F,GAAGA,EAAEqD,UAAUhD,EAAE2C,kBAAkBllB,EAAEoG,MAAM8Z,KAAKsF,IAAItD,EAAEqD,UAAUxF,KAAI,GAAG/f,EAAEkM,QAAQ,CAACgW,GAAGC,IAAK,GAAE,EAAEgB,EAAEa,GAAE,2BAA2Blb,GAAG,GAAGqa,EAAE,CAAChB,EAAEmC,GAAEpC,EAAEliB,EAAEupB,GAAG5lB,EAAE8hB,kBAAkB,IAAIvlB,EAAEwkB,IAAC,WAAa8E,GAAGxpB,EAAE+iB,MAAMja,EAAEqa,EAAEyC,eAAezC,EAAEmC,aAAakE,GAAGxpB,EAAE+iB,KAAI,EAAGZ,GAAE,GAAK,IAAG,CAAC,UAAU,SAAS7F,SAAO,SAAW4F,GAAGnQ,iBAAiBmQ,GAAC,WAAa,OAAOqF,GAAErnB,EAAG,GAAE,CAACwpB,MAAK,EAAGC,SAAQ,GAAK,IAAG5pB,GAAEG,GAAG0C,IAAC,SAAW2f,GAAGviB,EAAEujB,GAAE,OAAOpB,EAAEmC,GAAEpC,EAAEliB,EAAEupB,GAAG5lB,EAAE8hB,kBAAkBlB,IAAC,WAAavkB,EAAEoG,MAAMgc,YAAYnT,MAAMsT,EAAEc,UAAUmG,GAAGxpB,EAAE+iB,KAAI,EAAGZ,GAAE,EAAI,GAAG,GAAE,CAAE,GAAE,CAAznB,EAA0nB,SAAWxe,GAAG,IAAI4e,EAAE,SAASL,GAAG,IAAIve,EAAE,CAACimB,gBAAgB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB7H,EAAE9b,OAAO,GAAG8b,EAAEhW,QAAQjM,OAAO,CAAC,IAAIsiB,EAAEJ,KAAI,GAAGI,EAAE,CAAC,IAAIviB,EAAEuiB,EAAEe,iBAAiB,EAAEH,EAAEjB,EAAEhW,QAAQgW,EAAEhW,QAAQjM,OAAO,GAAGC,EAAEijB,EAAE6G,KAAK5H,YAAYC,iBAAiB,YAAY5V,QAAM,SAAWyV,GAAG,OAAOA,EAAE7e,OAAO8f,EAAE6G,GAAI,IAAG,GAAGpnB,EAAEsd,KAAKsF,IAAI,EAAEjD,EAAED,cAActiB,GAAG+f,EAAEG,KAAKsF,IAAI5iB,EAAE1C,GAAGA,EAAE+pB,cAAc/pB,EAAEqlB,WAAWvlB,EAAE,GAAGujB,EAAErD,KAAKsF,IAAIzF,EAAE7f,EAAEA,EAAEgqB,YAAYlqB,EAAE,GAAGgkB,EAAE9D,KAAKsF,IAAIjC,EAAEJ,EAAEoC,UAAUvlB,GAAG2D,EAAE,CAACwmB,QAAQrhB,GAAEqa,EAAEgH,SAASP,gBAAgBhnB,EAAEinB,kBAAkB9J,EAAEnd,EAAEknB,qBAAqBvG,EAAExD,EAAEgK,mBAAmB/F,EAAET,EAAE6G,gBAAgB7H,EAAE8H,SAASlH,GAAGA,EAAE6G,MAAMrmB,EAAEqmB,IAAI7G,EAAE6G,KAAK9pB,IAAIyD,EAAE2mB,iBAAiBpqB,EAAE,CAAC,CAAC,OAAO+D,OAAO+U,OAAOkJ,EAAE,CAACmE,YAAY1iB,GAAG,CAAnqB,CAAqqBA,GAAGue,EAAEK,EAAG,GAAE5e,EAAE,iDCmB/oT4mB,GAAqB,IAErBC,GAAkB,KAAK5X,KACvB6X,GAAwB,SAKxBC,GAA0B,KCwB1B9R,GAAgB,IAAI/Q,EDtBV,gBACK,gBCD2C,CACrE,4BACE,kDACF,iBAA4B,2CAC5B,yBAAoC,mCACpC,iBACE,6FACF,cAAyB,kDACzB,8BACE,6EA4BE,SAAU8iB,GAAc9mB,GAC5B,OACEA,aAAiByD,GACjBzD,EAAM0D,KAAK6B,SAAQ,iBAEvB;;;;;;;;;;;;;;;;OCxCgB,SAAAwhB,IAAyBC,UAAEA,IACzC,MAAO,4DAAqCA,iBAC9C,CAEM,SAAUC,GACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,WA8DuCC,EA9DMJ,EAASG,UAgEjDE,OAAOD,EAAkB1nB,QAAQ,IAAK,SA/D3C4nB,aAAcnnB,KAAK+K,OA6DvB,IAA2Ckc,CA3D3C,CAEOtX,eAAeyX,GACpBC,EACAR,GAEA,MACMS,SADoCT,EAASU,QACpB5nB,MAC/B,OAAO+U,GAAc9Q,OAAiC,iBAAA,CACpDyjB,cACAG,WAAYF,EAAUjkB,KACtBokB,cAAeH,EAAUnkB,QACzBukB,aAAcJ,EAAUK,QAE5B,CAEgB,SAAAC,IAAW9R,OAAEA,IAC3B,OAAO,IAAI+R,QAAQ,CACjB,eAAgB,mBAChBC,OAAQ,mBACR,iBAAkBhS,GAEtB,CAEgB,SAAAiS,GACdC,GACAC,aAAEA,IAEF,MAAMC,EAAUN,GAAWI,GAE3B,OADAE,EAAQC,OAAO,gBAmCjB,SAAgCF,GAC9B,MAAO,GAAG1B,MAAyB0B,GACrC;;;;;;;;;;;;;;;;OArCkCG,CAAuBH,IAChDC,CACT,CAeOvY,eAAe0Y,GACpBC,GAEA,MAAMxlB,QAAewlB,IAErB,OAAIxlB,EAAO6kB,QAAU,KAAO7kB,EAAO6kB,OAAS,IAEnCW,IAGFxlB,CACT;;;;;;;;;;;;;;;;;ACnFM,SAAUylB,GAAMC,GACpB,OAAO,IAAIzmB,SAAcF,IACvBkf,WAAWlf,EAAS2mB,EAAG,GAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHO,MAAMC,GAAoB,oBAOjB,SAAAC,KACd,IAGE,MAAMC,EAAe,IAAIC,WAAW,KAElCtoB,KAAKuoB,QAAWvoB,KAAyCwoB,UACpDC,gBAAgBJ,GAGvBA,EAAa,GAAK,IAAcA,EAAa,GAAK,GAElD,MAAMK,EAUV,SAAgBL,GACd,MAAMM,GChC8BC,EDgCIP,EC/B5BzqB,KAAKK,OAAOC,gBAAgB0qB,IAC7B3pB,QAAQ,MAAO,KAAKA,QAAQ,MAAO,MAF1C,IAAgC2pB,EDoCpC,OAAOD,EAAUE,OAAO,EAAG,GAC7B;;;;;;;;;;;;;;;;OAhBgBC,CAAOT,GAEnB,OAAOF,GAAkBY,KAAKL,GAAOA,EApBd,EAqBxB,CAAC,MAAAvnB,GAEA,MAvBuB,EAwBxB,CACH,CEzBM,SAAU6nB,GAAOtB,GACrB,MAAO,GAAGA,EAAUvX,WAAWuX,EAAUhP,OAC3C;;;;;;;;;;;;;;;;OCDA,MAAMuQ,GAA2D,IAAI9iB,IAMrD,SAAA+iB,GAAWxB,EAAsBgB,GAC/C,MAAM3kB,EAAMilB,GAAOtB,GAEnByB,GAAuBplB,EAAK2kB,GAsD9B,SAA4B3kB,EAAa2kB,GACvC,MAAMU,EASR,YACOC,IAAoB,qBAAsBrpB,OAC7CqpB,GAAmB,IAAIC,iBAAiB,yBACxCD,GAAiBE,UAAYpqB,IAC3BgqB,GAAuBhqB,EAAEuE,KAAKK,IAAK5E,EAAEuE,KAAKglB,IAAI,GAGlD,OAAOW,EACT,CAjBkBG,GACZJ,GACFA,EAAQK,YAAY,CAAE1lB,MAAK2kB,QAkBG,IAA5BO,GAAmBpF,MAAcwF,KACnCA,GAAiB5mB,QACjB4mB,GAAmB,KAjBvB,CA3DEK,CAAmB3lB,EAAK2kB,EAC1B,CAyCA,SAASS,GAAuBplB,EAAa2kB,GAC3C,MAAM3f,EAAYkgB,GAAmB1iB,IAAIxC,GACzC,GAAKgF,EAIL,IAAK,MAAMpH,KAAYoH,EACrBpH,EAAS+mB,EAEb,CAUA,IAAIW,GAA4C;;;;;;;;;;;;;;;;;ACrEhD,MAEMM,GAAoB,+BAS1B,IAAI1R,GAA2D,KAC/D,SAASC,KAgBP,OAfKD,KACHA,GAAY9J,EAdM,kCACG,EAa+B,CAClDG,QAAS,CAACO,EAAIF,KAMZ,GACO,IADCA,EAEJE,EAAGsJ,kBAAkBwR,GACxB,KAIA1R,EACT,CAeO5I,eAAexI,GACpB6gB,EACA9lB,GAEA,MAAMmC,EAAMilB,GAAOtB,GAEb3a,SADWmL,MACH3L,YAAYod,GAAmB,aACvCzd,EAAca,EAAGb,YAAYyd,IAC7BC,QAAkB1d,EAAY3F,IAAIxC,GAQxC,aAPMmI,EAAYqM,IAAI3W,EAAOmC,SACvBgJ,EAAGG,KAEJ0c,GAAYA,EAASlB,MAAQ9mB,EAAM8mB,KACtCQ,GAAWxB,EAAW9lB,EAAM8mB,KAGvB9mB,CACT,CAGOyN,eAAewa,GAAOnC,GAC3B,MAAM3jB,EAAMilB,GAAOtB,GAEb3a,SADWmL,MACH3L,YAAYod,GAAmB,mBACvC5c,EAAGb,YAAYyd,IAAmB/hB,OAAO7D,SACzCgJ,EAAGG,IACX,CAQOmC,eAAeya,GACpBpC,EACAqC,GAEA,MAAMhmB,EAAMilB,GAAOtB,GAEb3a,SADWmL,MACH3L,YAAYod,GAAmB,aACvCpa,EAAQxC,EAAGb,YAAYyd,IACvBC,QAAiDra,EAAMhJ,IAC3DxC,GAEImK,EAAW6b,EAASH,GAa1B,YAXiBhqB,IAAbsO,QACIqB,EAAM3H,OAAO7D,SAEbwL,EAAMgJ,IAAIrK,EAAUnK,SAEtBgJ,EAAGG,MAELgB,GAAc0b,GAAYA,EAASlB,MAAQxa,EAASwa,KACtDQ,GAAWxB,EAAWxZ,EAASwa,KAG1Bxa,CACT;;;;;;;;;;;;;;;;OClFOmB,eAAe2a,GACpBC,GAEA,IAAIC,EAEJ,MAAMC,QAA0BL,GAAOG,EAAcvC,WAAW0C,IAC9D,MAAMD,EAwBV,SACEC,GAEA,MAAMC,EAA2BD,GAAY,CAC3C1B,IAAKN,KACLkC,mBAA6C,GAG/C,OAAOC,GAAqBF,EAC9B,CAjC8BG,CAAgCJ,GACpDK,EAyCV,SACER,EACAE,GAEA,GAAwC,IAApCA,EAAkBG,mBAAkD,CACtE,IAAKI,UAAUC,OAAQ,CAKrB,MAAO,CACLR,oBACAD,oBALmCzoB,QAAQH,OAC3C8S,GAAc9Q,OAA6B,gBAM9C,CAGD,MAAMsnB,EAA+C,CACnDlC,IAAKyB,EAAkBzB,IACvB4B,mBAA6C,EAC7CO,iBAAkBnrB,KAAK+K,OAEnByf,EAkBV7a,eACE4a,EACAE,GAEA,IACE,MAAMW,QCxGHzb,gBACLqY,UAAEA,EAASqD,yBAAEA,IACbrC,IAAEA,IAEF,MAAMsC,EAAW5E,GAAyBsB,GAEpCE,EAAUN,GAAWI,GAGrBuD,EAAmBF,EAAyB5jB,aAAa,CAC7DE,UAAU,IAEZ,GAAI4jB,EAAkB,CACpB,MAAMC,QAAyBD,EAAiBpR,sBAC5CqR,GACFtD,EAAQC,OAAO,oBAAqBqD,EAEvC,CAED,MAAMC,EAAO,CACXzC,MACA0C,YAAanF,GACbvN,MAAOgP,EAAUhP,MACjB2S,WAAYrF,IAGR3jB,EAAuB,CAC3BsI,OAAQ,OACRid,UACAuD,KAAM1qB,KAAKiX,UAAUyT,IAGjB5E,QAAiBwB,IAAmB,IAAMuD,MAAMN,EAAU3oB,KAChE,GAAIkkB,EAASgF,GAAI,CACf,MAAMC,QAAkDjF,EAASU,OAOjE,MANiE,CAC/DyB,IAAK8C,EAAc9C,KAAOA,EAC1B4B,mBAA2C,EAC3C3C,aAAc6D,EAAc7D,aAC5B8D,UAAWnF,GAAiCkF,EAAcC,WAG7D,CACC,YAAY3E,GAAqB,sBAAuBP,EAE5D,CD2D8CmF,CACxCzB,EACAE,GAEF,OAAOtjB,GAAIojB,EAAcvC,UAAWoD,EACrC,CAAC,MAAO3rB,GAYP,MAXIgnB,GAAchnB,IAAkC,MAA5BA,EAAE6D,WAAWkkB,iBAG7B2C,GAAOI,EAAcvC,iBAGrB7gB,GAAIojB,EAAcvC,UAAW,CACjCgB,IAAKyB,EAAkBzB,IACvB4B,mBAA6C,IAG3CnrB,CACP,CACH,CA1CgCwsB,CAC1B1B,EACAW,GAEF,MAAO,CAAET,kBAAmBS,EAAiBV,sBAC9C,CAAM,OAC+B,IAApCC,EAAkBG,mBAEX,CACLH,oBACAD,oBAAqB0B,GAAyB3B,IAGzC,CAAEE,oBAEb,CA9E6B0B,CACvB5B,EACAE,GAGF,OADAD,EAAsBO,EAAiBP,oBAChCO,EAAiBN,iBAAiB,IAG3C,MLvCyB,KKuCrBA,EAAkBzB,IAEb,CAAEyB,wBAAyBD,GAG7B,CACLC,oBACAD,sBAEJ,CA2FA7a,eAAeuc,GACb3B,GAMA,IAAII,QAAiCyB,GACnC7B,EAAcvC,WAEhB,KAA+B,IAAxB2C,EAAMC,0BAELrC,GAAM,KAEZoC,QAAcyB,GAA0B7B,EAAcvC,WAGxD,GAA4B,IAAxB2C,EAAMC,mBAAkD,CAE1D,MAAMH,kBAAEA,EAAiBD,oBAAEA,SACnBF,GAAqBC,GAE7B,OAAIC,GAIKC,CAEV,CAED,OAAOE,CACT,CAUA,SAASyB,GACPpE,GAEA,OAAOoC,GAAOpC,GAAW0C,IACvB,IAAKA,EACH,MAAMhW,GAAc9Q,OAAM,0BAE5B,OAAOinB,GAAqBH,EAAS,GAEzC,CAEA,SAASG,GAAqBF,GAC5B,OAcoE,KAHpEF,EAXmCE,GAcfC,oBAClBH,EAAkBU,iBAAmB9E,GAAqBrmB,KAAK+K,MAdxD,CACLie,IAAK2B,EAAM3B,IACX4B,mBAA6C,GAI1CD,EAGT,IACEF;;;;;;;;;;;;;;;;OAHF,CEzLO9a,eAAe0c,IACpBrE,UAAEA,EAASqD,yBAAEA,GACbZ,GAEA,MAAMa,EAuCR,SACEtD,GACAgB,IAAEA,IAEF,MAAO,GAAGtC,GAAyBsB,MAAcgB,uBACnD;;;;;;;;;;;;;;;;OA5CmBsD,CAA6BtE,EAAWyC,GAEnDvC,EAAUH,GAAmBC,EAAWyC,GAGxCc,EAAmBF,EAAyB5jB,aAAa,CAC7DE,UAAU,IAEZ,GAAI4jB,EAAkB,CACpB,MAAMC,QAAyBD,EAAiBpR,sBAC5CqR,GACFtD,EAAQC,OAAO,oBAAqBqD,EAEvC,CAED,MAAMC,EAAO,CACXc,aAAc,CACZZ,WAAYrF,GACZtN,MAAOgP,EAAUhP,QAIfrW,EAAuB,CAC3BsI,OAAQ,OACRid,UACAuD,KAAM1qB,KAAKiX,UAAUyT,IAGjB5E,QAAiBwB,IAAmB,IAAMuD,MAAMN,EAAU3oB,KAChE,GAAIkkB,EAASgF,GAAI,CAIf,OADEjF,SAFqDC,EAASU,OAIjE,CACC,YAAYH,GAAqB,sBAAuBP,EAE5D,CCnCOlX,eAAe6c,GACpBjC,EACAkC,GAAe,GAEf,IAAIC,EACJ,MAAM/B,QAAcP,GAAOG,EAAcvC,WAAW0C,IAClD,IAAKiC,GAAkBjC,GACrB,MAAMhW,GAAc9Q,OAAM,kBAG5B,MAAMgpB,EAAelC,EAASqB,UAC9B,IAAKU,GA+HT,SAA0BV,GACxB,OACqD,IAAnDA,EAAUhF,gBAKd,SAA4BgF,GAC1B,MAAMhhB,EAAM/K,KAAK+K,MACjB,OACEA,EAAMghB,EAAU5E,cAChB4E,EAAU5E,aAAe4E,EAAU/E,UAAYjc,EAAMyb,EAEzD,CAVKqG,CAAmBd,EAExB,CApIyBe,CAAiBF,GAEpC,OAAOlC,EACF,GAA8B,IAA1BkC,EAAa7F,cAGtB,OADA2F,EA0BN/c,eACE4a,EACAkC,GAMA,IAAI9B,QAAcoC,GAAuBxC,EAAcvC,WACvD,KAAoC,IAA7B2C,EAAMoB,UAAUhF,qBAEfwB,GAAM,KAEZoC,QAAcoC,GAAuBxC,EAAcvC,WAGrD,MAAM+D,EAAYpB,EAAMoB,UACxB,OAA2B,IAAvBA,EAAUhF,cAELyF,GAAiBjC,EAAekC,GAEhCV,CAEX,CAjDqBiB,CAA0BzC,EAAekC,GACjD/B,EACF,CAEL,IAAKM,UAAUC,OACb,MAAMvW,GAAc9Q,OAAM,eAG5B,MAAMsnB,EAkIZ,SACER,GAEA,MAAMuC,EAA2C,CAC/ClG,cAAwC,EACxCmG,YAAaltB,KAAK+K,OAEpB,OAAAhL,OAAA+U,OAAA/U,OAAA+U,OAAA,CAAA,EACK4V,GAAQ,CACXqB,UAAWkB,GAEf,CA7I8BE,CAAoCzC,GAE5D,OADAgC,EAsEN/c,eACE4a,EACAE,GAEA,IACE,MAAMsB,QAAkBM,GACtB9B,EACAE,GAEI2C,EACDrtB,OAAA+U,OAAA/U,OAAA+U,OAAA,CAAA,EAAA2V,GACH,CAAAsB,cAGF,aADM5kB,GAAIojB,EAAcvC,UAAWoF,GAC5BrB,CACR,CAAC,MAAOtsB,GACP,IACEgnB,GAAchnB,IACe,MAA5BA,EAAE6D,WAAWkkB,YAAkD,MAA5B/nB,EAAE6D,WAAWkkB,WAK5C,CACL,MAAM4F,EACDrtB,OAAA+U,OAAA/U,OAAA+U,OAAA,CAAA,EAAA2V,GACH,CAAAsB,UAAW,CAAEhF,cAAa,WAEtB5f,GAAIojB,EAAcvC,UAAWoF,EACpC,YAPOjD,GAAOI,EAAcvC,WAQ7B,MAAMvoB,CACP,CACH,CAtGqB4tB,CAAyB9C,EAAeW,GAChDA,CACR,KAMH,OAHkBwB,QACRA,EACL/B,EAAMoB,SAEb,CAyCA,SAASgB,GACP/E,GAEA,OAAOoC,GAAOpC,GAAW0C,IACvB,IAAKiC,GAAkBjC,GACrB,MAAMhW,GAAc9Q,OAAM,kBAG5B,MAAMgpB,EAAelC,EAASqB,UAC9B,OAqFqD,KAFpBA,EAnFDa,GAqFtB7F,eACVgF,EAAUmB,YAAc7G,GAAqBrmB,KAAK+K,MApF3ChL,OAAA+U,OAAA/U,OAAA+U,OAAA,CAAA,EAAA4V,GACH,CAAAqB,UAAW,CAAEhF,cAAa,KAIvB2D,EA4EX,IAAqCqB;;;;;;;;;;;;;;;;OA5ElB,GAEnB,CAoCA,SAASY,GACPlC,GAEA,YACwBvqB,IAAtBuqB,GACgE,IAAhEA,EAAkBG,kBAEtB;;;;;;;;;;;;;;;;;ACnJOjb,eAAe2d,GACpB/C,EACAkC,GAAe,GAEf,MAAMc,EAAoBhD,QAS5B5a,eACE4a,GAEA,MAAMC,oBAAEA,SAA8BF,GAAqBC,GAEvDC,SAEIA,CAEV;;;;;;;;;;;;;;;;OAjBQgD,CAAiCD,GAKvC,aADwBf,GAAiBe,EAAmBd,IAC3C3F,KACnB,CCWA,SAAS2G,GAAqBC,GAC5B,OAAOhZ,GAAc9Q,OAA4C,4BAAA,CAC/D8pB,aAEJ;;;;;;;;;;;;;;;;OC3BA,MAAMC,GAAqB,gBAoBrBC,GACJtnB,IAEA,MAEMikB,EAAgBjW,GAFVhO,EAAUoD,YAAY,OAAOjC,eAEDkmB,IAAoBlmB,eAM5D,MAJ8D,CAC5DomB,MAAO,IC5BJle,eAAqB4a,GAC1B,MAAMgD,EAAoBhD,GACpBE,kBAAEA,EAAiBD,oBAAEA,SAA8BF,GACvDiD,GAWF,OARI/C,EACFA,EAAoBroB,MAAMzC,QAAQC,OAIlC6sB,GAAiBe,GAAmBprB,MAAMzC,QAAQC,OAG7C8qB,EAAkBzB,GAC3B,CDaiB6E,CAAMtD,GACnB+C,SAAWb,GAA2Ba,GAAS/C,EAAekC,GAEpC,EAI5BtY,GACE,IAAI5O,EAAUooB,IAhChBrnB,IAEA,MAAM4N,EAAM5N,EAAUoD,YAAY,OAAOjC,eAEnCugB,EDfF,SAA2B9T,GAC/B,IAAKA,IAAQA,EAAIxM,QACf,MAAM+lB,GAAqB,qBAG7B,IAAKvZ,EAAI/U,KACP,MAAMsuB,GAAqB,YAI7B,MAAMK,EAA2C,CAC/C,YACA,SACA,SAGF,IAAK,MAAMC,KAAWD,EACpB,IAAK5Z,EAAIxM,QAAQqmB,GACf,MAAMN,GAAqBM,GAI/B,MAAO,CACLtd,QAASyD,EAAI/U,KACbwnB,UAAWzS,EAAIxM,QAAQif,UACvB7Q,OAAQ5B,EAAIxM,QAAQoO,OACpBkD,MAAO9E,EAAIxM,QAAQsR,MAEvB,CCboBgV,CAAiB9Z,GASnC,MANqD,CACnDA,MACA8T,YACAqD,yBAL+B/W,GAAaJ,EAAK,aAMjDxL,QAAS,IAAM3G,QAAQF,UAED,GAmBgD,WAExEsS,GACE,IAAI5O,EAtC4B,yBAwC9BqoB,GAED,YExCLpX,GAAgBrX,GAAMuP,IAEtB8H,GAAgBrX,GAAMuP,GAAS,uDCflBqI,GAAcrI,GAMduf,GAAuB,wBAEvBC,GAA6B,OAI7BC,GAAsC,OAEtCC,GAAiC,OAEjCC,GAAuC,OAGvCC,GAAwC,OAGxCC,GAAsC,OAGtCC,GAA2B,+BAE3BC,GACX,qCAGWC,GAAe,cC6Bfha,GAAgB,IAAI/Q,ED9BV,cCgCrB+qB,GA1CqE,CACrE,gBAAkC,yCAClC,gBAAkC,qCAClC,8BACE,mDACF,6BACE,kDACF,YAAuB,2BACvB,YAAuB,2BACvB,gBAA2B,+BAC3B,aAAwB,4BACxB,iBAA4B,sCAC5B,iBACE,4EACF,qBAAuB,wBACvB,yBACE,8CACF,0BACE,gDACF,6BACE,oDACF,8BACE,uEACF,sBACE,2PC3CSC,GAAgB,IAAIzjB,EAAOwjB;;;;;;;;;;;;;;;;;ACgBxC,IAAIE,GACAC,GClBAC,GCAAC,GHEJJ,GAAc7jB,SAAWjB,EAASM,KC8BrB,MAAA6kB,GAaX,WAAA9vB,CAAqBqB,GACnB,GADmB9D,KAAM8D,OAANA,GACdA,EACH,MAAMmU,GAAc9Q,OAAM,aAE5BnH,KAAKyhB,YAAc3d,EAAO2d,YAC1BzhB,KAAKsjB,oBAAsBxf,EAAOwf,oBAClCtjB,KAAKwyB,eAAiB1uB,EAAO2uB,SAC7BzyB,KAAKuuB,UAAYzqB,EAAOyqB,UACxBvuB,KAAKyE,SAAWX,EAAOW,SACnBzE,KAAKuuB,WAAavuB,KAAKuuB,UAAUmE,gBAGnC1yB,KAAK2yB,aAAe7uB,EAAO6uB,cAEzB7uB,EAAO8uB,aAAe9uB,EAAO8uB,YAAYC,oBAC3C7yB,KAAK6yB,kBAAoB/uB,EAAO8uB,YAAYC,mBAE9C7yB,KAAK8yB,MAAQC,GACb/yB,KAAKgzB,MAAQC,GACbjzB,KAAKkzB,MAAQC,EACd,CAED,MAAAC,GAEE,OAAOpzB,KAAKwyB,eAAea,KAAKpa,MAAM,KAAK,EAC5C,CAED,IAAAqa,CAAK5wB,GACE1C,KAAKyhB,aAAgBzhB,KAAKyhB,YAAY6R,MAG3CtzB,KAAKyhB,YAAY6R,KAAK5wB,EACvB,CAED,OAAA6wB,CAAQC,EAAqBC,EAAeC,GACrC1zB,KAAKyhB,aAAgBzhB,KAAKyhB,YAAY8R,SAG3CvzB,KAAKyhB,YAAY8R,QAAQC,EAAaC,EAAOC,EAC9C,CAED,gBAAAhS,CAAiB1Y,GACf,OAAKhJ,KAAKyhB,aAAgBzhB,KAAKyhB,YAAYC,iBAGpC1hB,KAAKyhB,YAAYC,iBAAiB1Y,GAFhC,EAGV,CAED,gBAAA2qB,CAAiBjxB,GACf,OAAK1C,KAAKyhB,aAAgBzhB,KAAKyhB,YAAYkS,iBAGpC3zB,KAAKyhB,YAAYkS,iBAAiBjxB,GAFhC,EAGV,CAED,aAAAkxB,GAEE,OACE5zB,KAAKyhB,cACJzhB,KAAKyhB,YAAYoS,YAAc7zB,KAAKyhB,YAAYqS,OAAOC,gBAE3D,CAED,qBAAAC,GACE,OAAK7E,OAAU7pB,SnD+GQ,oBAAdipB,WAA8BA,UAAUmE,gBmDxG5C7sB,MACHqsB,GAAcptB,KAAK,kDACZ,IARPotB,GAAcptB,KACZ,2GAEK,EAQV,CAED,aAAAmvB,CACExN,EACAjhB,GAEA,IAAKxF,KAAKsjB,oBACR,OAEe,IAAItjB,KAAKsjB,qBAAoB4Q,IAC5C,IAAK,MAAMhG,KAASgG,EAAK1Q,aAEvBhe,EAAS0oB,EACV,IAIMzK,QAAQ,CAAE0Q,WAAY,CAAC1N,IACjC,CAED,kBAAO2N,GAIL,YAHoB3wB,IAAhB0uB,KACFA,GAAc,IAAII,GAAIH,KAEjBD,EACR,ECnIa,SAAAkC,KACd,OAAOhC,EACT;;;;;;;;;;;;;;;;;AEjBgB,SAAAiC,GAAaC,EAAeC,GAC1C,MAAMC,EAAWF,EAAMj1B,OAASk1B,EAAMl1B,OACtC,GAAIm1B,EAAW,GAAKA,EAAW,EAC7B,MAAMxc,GAAc9Q,OAAM,+BAG5B,MAAMutB,EAAc,GACpB,IAAK,IAAIr1B,EAAI,EAAGA,EAAIk1B,EAAMj1B,OAAQD,IAChCq1B,EAAYpzB,KAAKizB,EAAMjyB,OAAOjD,IAC1Bm1B,EAAMl1B,OAASD,GACjBq1B,EAAYpzB,KAAKkzB,EAAMlyB,OAAOjD,IAIlC,OAAOq1B,EAAYnzB,KAAK,GAC1B;;;;;;;;;;;;;;;;ODba,MAAAozB,GAAb,WAAAlyB,GAEEzC,KAAsB40B,wBAAG,EAGzB50B,KAAqB60B,uBAAG,EAGxB70B,KAAc80B,gBAAG,EAEjB90B,KAAkB+0B,mBAAG,EACrB/0B,KAA2Bg1B,4BAAG,EAG9Bh1B,KAAci1B,eACZ,oEAGFj1B,KAAAk1B,uBAAyBZ,GACvB,mCACA,mCAGFt0B,KAAAm1B,aAAeb,GAAa,uBAAwB,uBAGpDt0B,KAASo1B,UAAG,IAGZp1B,KAAqBq1B,uBAAG,EACxBr1B,KAAuBs1B,yBAAG,EAG1Bt1B,KAAgBu1B,iBAAG,EAYpB,CAVC,qBAAAC,GACE,OAAOx1B,KAAKk1B,uBAAuBjS,OAAO,QAASjjB,KAAKm1B,aACzD,CAED,kBAAOf,GAIL,YAHgC3wB,IAA5B6uB,KACFA,GAA0B,IAAIqC,IAEzBrC,EACR;;;;;;;;;;;;;;;;OEtCH,IAAYmD,IAAZ,SAAYA,GACVA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAJD,CAAYA,KAAAA,GAIX,CAAA,IAsCD,MAAMC,GAA8B,CAAC,YAAa,UAAW,OACvDC,GAAyB,IAAIC,OAAO,kBAI1B,SAAAC,KACd,MAAMtH,EAAYgE,GAAI6B,cAAc7F,UACpC,OAAIA,aAAS,EAATA,EAAWuH,eACTvH,EAAUuH,cAAcC,WACY,EAEE,EAGH,CAE3C,CAEgB,SAAAC,KAGd,OAFiBzD,GAAI6B,cAAc3vB,SACFqf,iBAE/B,IAAK,UACH,OAAO2R,GAAgBQ,QACzB,IAAK,SACH,OAAOR,GAAgBS,OACzB,QACE,OAAOT,GAAgBU,QAE7B,CAEgB,SAAAC,KACd,MACMC,EADY9D,GAAI6B,cAAc7F,UAC+B+H,WAGnE,OADED,GAAuBA,EAAoBE,eAE3C,IAAK,UACH,OAAkD,EACpD,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,QACE,OAAuC,EAE7C;;;;;;;;;;;;;;;;;ACjGM,SAAUC,GAASC,SACvB,MAAMla,EAA2B,QAAnBvX,EAAAyxB,EAAYxrB,eAAO,IAAAjG,OAAA,EAAAA,EAAEuX,MACnC,IAAKA,EACH,MAAMtE,GAAc9Q,OAAM,aAE5B,OAAOoV,CACT;;;;;;;;;;;;;;;;;ACKA,MAAMma,GAA4B,QAa5BC,GAAmC,CACvC7B,gBAAgB,GAqBZ8B,GAAkB,8BAER,SAAAC,GACdC,EACAzE,GAEA,MAAMptB,EAeR,WACE,MAAM0tB,EAAeJ,GAAI6B,cAAczB,aACvC,IAAKA,EACH,OAEF,MAAMoE,EAAepE,EAAaqE,QAAQhF,IAC1C,KAAK+E,IA4IcE,EA5IeF,EA6I3BtM,OAAOwM,GAAU1zB,KAAK+K,QA5I3B,OA2IJ,IAAqB2oB,EAxInB,MAAMC,EAAoBvE,EAAaqE,QAAQjF,IAC/C,IAAKmF,EACH,OAEF,IAEE,OAD6C5yB,KAAKC,MAAM2yB,EAEzD,CAAC,MAAAlyB,GACA,MACD,CACH,CAnCiBmyB,GACf,OAAIlyB,GACFmyB,GAAcnyB,GACPK,QAAQF,WAqDnB,SACE0xB,EACAzE,GAGA,OL/FI,SACJgF,GAEA,MAAMC,EAAmBD,EAAqBxG,WAK9C,OAHAyG,EAAiBzlB,MAAM0lB,IAAD,IAGfD,CACT,CKsFSE,CAAoBV,EAAsBhJ,eAC9Cjc,MAAKyd,IACJ,MAAMpF,ED3GN,SAAuBuM,SAC3B,MAAMvM,EAA+B,QAAnBllB,EAAAyxB,EAAYxrB,eAAO,IAAAjG,OAAA,EAAAA,EAAEklB,UACvC,IAAKA,EACH,MAAMjS,GAAc9Q,OAAM,iBAE5B,OAAO+iB,CACT,CCqGwBuN,CAAaX,EAAsBrf,KAC/C4B,EDpGN,SAAoBod,SACxB,MAAMpd,EAA4B,QAAnBrU,EAAAyxB,EAAYxrB,eAAO,IAAAjG,OAAA,EAAAA,EAAEqU,OACpC,IAAKA,EACH,MAAMpB,GAAc9Q,OAAM,cAE5B,OAAOkS,CACT,CC8FqBqe,CAAUZ,EAAsBrf,KAEzCvR,EAAU,IAAIyxB,QADG,2DAA2DzN,mCAA2C7Q,IACjF,CAC1C7K,OAAQ,OACRid,QAAS,CAAEmM,cAAe,GAAGhB,MAAmBtH,KAEhDN,KAAM1qB,KAAKiX,UAAU,CACnBsc,gBAAiBxF,EACjByF,sBAAuBxI,EACvByI,OAAQvB,GAASM,EAAsBrf,KACvCugB,YAAa1d,GACb2d,YAAavB,OAIjB,OAAOvH,MAAMjpB,GAAS2L,MAAKuY,IACzB,GAAIA,EAASgF,GACX,OAAOhF,EAASU,OAGlB,MAAM7S,GAAc9Q,OAAM,qBAAqB,GAC/C,IAEHzB,OAAM,KACLwsB,GAAcptB,KAAKozB,GACH,GAEtB,CArFSC,CAAgBrB,EAAuBzE,GAC3CxgB,KAAKulB,IACLvlB,MACC5M,GA4BN,SAAqBA,GACnB,MAAM0tB,EAAeJ,GAAI6B,cAAczB,aACvC,IAAK1tB,IAAW0tB,EACd,OAGFA,EAAayF,QAAQrG,GAA0BztB,KAAKiX,UAAUtW,IAC9D0tB,EAAayF,QACXpG,GACAlwB,OACEyB,KAAK+K,MAC8C,GAAjDqmB,GAAgBP,cAAcmB,iBAAwB,GAAK,KAGnE,CA1CgB8C,CAAYpzB,KAEtB,QAEN,CAwCA,MAAMizB,GACJ,mDA4CF,SAASd,GACPnyB,GAEA,IAAKA,EACH,OAAOA,EAET,MAAMqtB,EAA0BqC,GAAgBP,cAC1C7oB,EAAUtG,EAAOsG,SAAW,GAqDlC,YApD4B9H,IAAxB8H,EAAQ+sB,YAGVhG,EAAwBwC,eACU,SAAhChzB,OAAOyJ,EAAQ+sB,aAIjBhG,EAAwBwC,eAAiB6B,GAAgB7B,eAEvDvpB,EAAQgtB,eACVjG,EAAwB8C,UAAY3K,OAAOlf,EAAQgtB,gBAC1C5B,GAAgBvB,YACzB9C,EAAwB8C,UAAYuB,GAAgBvB,WAGlD7pB,EAAQitB,qBACVlG,EAAwB2C,eAAiB1pB,EAAQitB,qBACxC7B,GAAgB1B,iBACzB3C,EAAwB2C,eAAiB0B,GAAgB1B,gBAIvD1pB,EAAQktB,sBACVnG,EAAwB6C,aAAe5pB,EAAQktB,sBACtC9B,GAAgBxB,eACzB7C,EAAwB6C,aAAewB,GAAgBxB,mBAGJ1xB,IAAjD8H,EAAQmtB,qCACVpG,EAAwB0C,4BAA8BvK,OACpDlf,EAAQmtB,2CAE+Cj1B,IAAhDkzB,GAAgB3B,8BACzB1C,EAAwB0C,4BACtB2B,GAAgB3B,kCAEuBvxB,IAAvC8H,EAAQotB,2BACVrG,EAAwByC,mBAAqBtK,OAC3Clf,EAAQotB,iCAEsCl1B,IAAvCkzB,GAAgB5B,qBACzBzC,EAAwByC,mBACtB4B,GAAgB5B,oBAGpBzC,EAAwB+C,sBAAwBuD,GAC9CtG,EAAwByC,oBAE1BzC,EAAwBgD,wBAA0BsD,GAChDtG,EAAwB0C,6BAEnB/vB,CACT,CAMA,SAAS2zB,GAAuBC,GAC9B,OAAOtZ,KAAK4D,UAAY0V,CAC1B;;;;;;;;;;;;;;;;OCnNA,IAEIC,GAFAC,GAA2D,EAIzD,SAAUC,GACdlC,GAOA,OALAiC,GAAkE,EAElED,GACEA,IASJ,SACEhC,GAEA,OAaF,WACE,MAAMryB,EAAW8tB,GAAI6B,cAAc3vB,SACnC,OAAO,IAAIa,SAAQF,IACjB,GAAIX,GAAoC,aAAxBA,EAASod,WAA2B,CAClD,MAAMoX,EAAU,KACc,aAAxBx0B,EAASod,aACXpd,EAASwM,oBAAoB,mBAAoBgoB,GACjD7zB,IACD,EAEHX,EAAS2M,iBAAiB,mBAAoB6nB,EAC/C,MACC7zB,GACD,GAEL,CA5BS8zB,GACJrnB,MAAK,IN7BJ,SACJwlB,GAEA,MAAM8B,EAAa9B,EAAqBjG,QAKxC,OAHA+H,EAAWtnB,MAAMunB,IACf/G,GAAM+G,CAAM,IAEPD,CACT,CMoBgBE,CAAcvC,EAAsBhJ,iBAC/Cjc,MAAKwgB,GAAOwE,GAAUC,EAAuBzE,KAC7CxgB,MACC,IAAMynB,OACN,IAAMA,MAEZ,CAnB6BC,CAAezC,GAEnCgC,EACT,CAuCA,SAASQ,KACPP,GAAwD,CAC1D;;;;;;;;;;;;;;;;OCxDA,ICoEIllB,GDpEA2lB,GAF4B,EA6B5BC,GAAsB,GAEtBC,IAA4B,EAiBhC,SAASC,GAAaC,GACpBtV,YAAW,KAELkV,IAAkB,IAIlBC,GAAMn6B,OAAS,GACjBu6B,KAEFF,GA7D6B,KA6DS,GACrCC,EACL,CAEA,SAASC,KAIP,MAAMC,EAASL,GAAMjc,OAAO,EAnEM,KAuE5Buc,EAAmBD,EAAO/tB,KAAIiuB,IAAQ,CAC1CC,6BAA8BD,EAAItzB,QAClCwzB,cAAep4B,OAAOk4B,EAAIG,gBA4B9B,SAA0B5yB,GACxB,MAAM6yB,EACJzF,GAAgBP,cAAcoB,wBAC1BxG,EAAO1qB,KAAKiX,UAAUhU,GAE5B,OAAOgnB,UAAU8L,YAAc9L,UAAU8L,WAAWD,EAAoBpL,GACpE1pB,QAAQF,UACR+pB,MAAMiL,EAAoB,CACxB5rB,OAAQ,OACRwgB,OACAsL,WAAW,IACVzoB,MACT,EA1BE0oB,CAXsC,CACpCC,gBAAiB14B,OAAOyB,KAAK+K,OAC7BmsB,YAAa,CACXC,YAAa,EACbC,eAAgB,CAAE,GAEpBC,WAAYjG,GAAgBP,cAAcgB,UAC1C2E,cAKCloB,MAAK,KACJ2nB,GAxF0B,CAwFc,IAEzC9zB,OAAM,KAGL+zB,GAAQ,IAAIK,KAAWL,IACvBD,KACAtH,GAAcptB,KAAK,eAAe00B,OAClCG,GAnG2B,IAmGW,GAE5C,UAyBgBkB,GAEdC,GAEA,MAAO,IAAI1sB,MAbb,SAAoB4rB,GAClB,IAAKA,EAAIG,YAAcH,EAAItzB,QACzB,MAAMuR,GAAc9Q,OAAM,kBAG5BsyB,GAAQ,IAAIA,GAAOO,EACrB,CASIe,CAAW,CACTr0B,QAFco0B,KAAc1sB,GAG5B+rB,UAAW52B,KAAK+K,OAChB,CAEN,CAMgB,SAAA0sB,KACd,KAAOvB,GAAMn6B,OAAS,GACpBu6B,IAEJ;;;;;;;;;;;;;;;;OCvEA,SAASoB,GACPC,EACAC,GAEKtnB,KACHA,GAAS,CACPunB,KAAMP,GAAiBC,IACvBO,MAAOL,KAGXnnB,GAAOunB,KAAKF,EAAUC,EACxB,CAEM,SAAUG,GAASC,GACvB,MAAMC,EAAkB7G,GAAgBP,eAEnCoH,EAAgB5G,wBAA0B2G,EAAME,SAIhDD,EAAgB3G,uBAA0B0G,EAAME,SAIhDlJ,GAAI6B,cAAcJ,0BF7E0C,IAA1D+E,GEkFL2C,GAAaH,GAIbvC,GAAyBuC,EAAMzE,uBAAuBjlB,MACpD,IAAM6pB,GAAaH,KACnB,IAAMG,GAAaH,KAGzB,CAQA,SAASG,GAAaH,GACpB,IAAKlH,KACH,OAGF,MAAMmH,EAAkB7G,GAAgBP,cAErCoH,EAAgB1G,gBAChB0G,EAAgBnG,uBAKnB4F,GAAQM,EAAK,EACf,CAkCA,SAAST,GACPI,EACAC,GAEA,OAAgD,IAA5CA,EAMN,SAAiCQ,GAC/B,MAAMC,EAA6C,CACjDvS,IAAKsS,EAAetS,IACpBwS,YAAaF,EAAeG,YAAc,EAC1CC,mBAAoB,IACpBC,uBAAwBL,EAAeM,qBACvCC,qBAAsBP,EAAeQ,YACrCC,8BAA+BT,EAAeU,0BAC9CC,8BAA+BX,EAAeY,2BAE1CC,EAA6B,CACjCC,iBAAkBC,GAChBf,EAAe7E,sBAAsBrf,KAEvCklB,uBAAwBf,GAE1B,OAAOt3B,KAAKiX,UAAUihB,EACxB,CAtBWI,CAAwB1B,GAwBnC,SAAwBK,GACtB,MAAMsB,EAA2B,CAC/Bn6B,KAAM64B,EAAM74B,KACZo6B,QAASvB,EAAME,OACfS,qBAAsBX,EAAMY,YAC5BY,YAAaxB,EAAMyB,YAGsB,IAAvC15B,OAAOgF,KAAKizB,EAAM0B,UAAU39B,SAC9Bu9B,EAAYI,SAAW1B,EAAM0B,UAE/B,MAAMC,EAAmB3B,EAAM4B,gBACc,IAAzC75B,OAAOgF,KAAK40B,GAAkB59B,SAChCu9B,EAAYO,kBAAoBF,GAGlC,MAAMV,EAA2B,CAC/BC,iBAAkBC,GAAmBnB,EAAMzE,sBAAsBrf,KACjE4lB,aAAcR,GAEhB,OAAOv4B,KAAKiX,UAAUihB,EACxB,CA3CSc,CAAepC,EACxB,CA4CA,SAASwB,GAAmBjG,GAC1B,MAAO,CACL8G,cAAe/G,GAASC,GACxBoB,gBAAiBxD,KACjBmJ,aAAc,CACZvF,YAAa3d,GACbmjB,SAAUlL,GAAI6B,cAAchB,SAC5BsK,sBAAuB7H,KACvB8H,iBAAkB3H,KAClB4H,0BAA2BxH,MAE7ByH,0BAA2B,EAE/B;;;;;;;;;;;;;;;;OC9MgB,SAAAC,GACdhH,EACA5I,GAEA,MAAM6P,EAAmB7P,EACzB,IAAK6P,QAAuDt6B,IAAnCs6B,EAAiBpc,cACxC,OAEF,MAAMkS,EAAatB,GAAI6B,cAAcR,gBAC/BuI,EAAc5c,KAAK2D,MACqB,KAA3C6a,EAAiBnZ,UAAYiP,IAE1BwI,EAA4B0B,EAAiBpc,cAC/CpC,KAAK2D,MAC6D,KAA/D6a,EAAiBpc,cAAgBoc,EAAiBnZ,iBAErDnhB,EACE84B,EAA4Bhd,KAAK2D,MACyB,KAA7D6a,EAAiBxU,YAAcwU,EAAiBnZ,aD2F/C,SAA4B+W,GAChC,MAAMH,EAAkB7G,GAAgBP,cAExC,IAAKoH,EAAgB5G,uBACnB,OAKF,MAAMoJ,EAAoBrC,EAAetS,IAInC4U,EAAiBzC,EAAgBvG,eAAehc,MAAM,KAAK,GAC3DilB,EAAgB1C,EAAgBtG,uBAAuBjc,MAAM,KAAK,GAEtE+kB,IAAsBC,GACtBD,IAAsBE,GAMrB1C,EAAgB1G,gBAChB0G,EAAgBlG,yBAKnB2F,GAAQU,EAAc,EACxB,CC5GEwC,CATuC,CACrCrH,wBACAzN,IAHU0U,EAAiBr7B,MAAQq7B,EAAiBr7B,KAAKuW,MAAM,KAAK,GAIpEgjB,qBAAsB8B,EAAiBK,aACvCjC,cACAE,4BACAE,6BAIJ;;;;;;;;;;;;;;;;OCtDA,MAEM8B,GAAa,CdDqB,McGtC3M,GACAC,GACAC,GACAE,GACAD;;;;;;;;;;;;;;;;;ACiBW,MAAAyM,GAoBX,WAAA77B,CACWq0B,EACAp0B,EACA+4B,GAAS,EAClB8C,GAHSv+B,KAAqB82B,sBAArBA,EACA92B,KAAI0C,KAAJA,EACA1C,KAAMy7B,OAANA,EAtBHz7B,KAAAw+B,MAA6C,EAG7Cx+B,KAAgBk9B,iBAA8B,GACtDl9B,KAAQi9B,SAAsC,GACtCj9B,KAAAy+B,IAAMlM,GAAI6B,cACVp0B,KAAA0+B,SAAWnf,KAAK2D,MAAsB,IAAhB3D,KAAK4D,UAmB5BnjB,KAAKy7B,SACRz7B,KAAK2+B,eAAiB,uBAA8B3+B,KAAK0+B,YAAY1+B,KAAK0C,OAC1E1C,KAAK4+B,cAAgB,sBAA6B5+B,KAAK0+B,YAAY1+B,KAAK0C,OACxE1C,KAAK6+B,aACHN,GACA,GAAG/M,MAAwBxxB,KAAK0+B,YAAY1+B,KAAK0C,OAE/C67B,GAGFv+B,KAAK8+B,wBAGV,CAKD,KAAAC,GACE,GAAc,IAAV/+B,KAAKw+B,MACP,MAAMvmB,GAAc9Q,OAAuC,gBAAA,CACzD63B,UAAWh/B,KAAK0C,OAGpB1C,KAAKy+B,IAAInL,KAAKtzB,KAAK2+B,gBACnB3+B,KAAKw+B,MAAK,CACX,CAMD,IAAAS,GACE,GAAc,IAAVj/B,KAAKw+B,MACP,MAAMvmB,GAAc9Q,OAAuC,gBAAA,CACzD63B,UAAWh/B,KAAK0C,OAGpB1C,KAAKw+B,MAAK,EACVx+B,KAAKy+B,IAAInL,KAAKtzB,KAAK4+B,eACnB5+B,KAAKy+B,IAAIlL,QACPvzB,KAAK6+B,aACL7+B,KAAK2+B,eACL3+B,KAAK4+B,eAEP5+B,KAAK8+B,wBACLxD,GAASt7B,KACV,CASD,MAAAk/B,CACEta,EACA8B,EACAzb,GAKA,GAAI2Z,GAAa,EACf,MAAM3M,GAAc9Q,OAA+C,8BAAA,CACjE63B,UAAWh/B,KAAK0C,OAGpB,GAAIgkB,GAAY,EACd,MAAMzO,GAAc9Q,OAA6C,6BAAA,CAC/D63B,UAAWh/B,KAAK0C,OASpB,GALA1C,KAAKg9B,WAAazd,KAAK2D,MAAiB,IAAXwD,GAC7B1mB,KAAKm8B,YAAc5c,KAAK2D,MAAkB,IAAZ0B,GAC1B3Z,GAAWA,EAAQk0B,aACrBn/B,KAAKk9B,iBAAgB55B,OAAA+U,OAAA,CAAA,EAAQpN,EAAQk0B,aAEnCl0B,GAAWA,EAAQm0B,QACrB,IAAK,MAAMC,KAAc/7B,OAAOgF,KAAK2C,EAAQm0B,SACtCE,MAAM7U,OAAOxf,EAAQm0B,QAAQC,OAChCr/B,KAAKi9B,SAASoC,GAAc9f,KAAK2D,MAC/BuH,OAAOxf,EAAQm0B,QAAQC,MAK/B/D,GAASt7B,KACV,CASD,eAAAu/B,CAAgBC,EAAiBC,EAAe,QACfh8B,IAA3BzD,KAAKi9B,SAASuC,GAChBx/B,KAAK0/B,UAAUF,EAASC,GAExBz/B,KAAK0/B,UAAUF,EAASx/B,KAAKi9B,SAASuC,GAAWC,EAEpD,CAQD,SAAAC,CAAUF,EAAiBC,GACzB,IDtJY,SAAkB/8B,EAAcs8B,GAC9C,QAAoB,IAAhBt8B,EAAKpD,QAAgBoD,EAAKpD,OAhBD,OAoB1B0/B,GACCA,EAAU5W,WAAWqJ,KACrB4M,GAAWsB,QAAQj9B,IAAS,IAC7BA,EAAK0lB,WAtBmB,KAwB7B,CC4IQwX,CAAkBJ,EAASx/B,KAAK0C,MAGlC,MAAMuV,GAAc9Q,OAA6C,6BAAA,CAC/D04B,iBAAkBL,IAHpBx/B,KAAKi9B,SAASuC,GDrId,SAAsCM,GAC1C,MAAMC,EAAyBxgB,KAAK2D,MAAM4c,GAM1C,OALIC,EAAiBD,GACnB5N,GAAcptB,KACZ,6DAA6Di7B,MAG1DA,CACT,CC6H+BC,CAA4BP,QAAAA,EAAgB,EAMxE,CAOD,SAAAQ,CAAUT,GACR,OAAOx/B,KAAKi9B,SAASuC,IAAY,CAClC,CAOD,YAAAU,CAAaC,EAAc16B,GACzB,MAAM26B,ERlGJ,SAAqC19B,GACzC,QAAoB,IAAhBA,EAAKpD,QAAgBoD,EAAKpD,OAjDE,OAoDFo2B,GAA4BjkB,MAAK4uB,GAC7D39B,EAAK0lB,WAAWiY,QAEiB39B,EAAKgC,MAAMixB,IAChD,CQ0FwB2K,CAA2BH,GACzCI,ERzFJ,SAAsC96B,GAC1C,OAAwB,IAAjBA,EAAMnG,QAAgBmG,EAAMnG,QA1DF,GA2DnC,CQuFyBkhC,CAA4B/6B,GACjD,GAAI26B,GAAeG,EACjBvgC,KAAKk9B,iBAAiBiD,GAAQ16B,MADhC,CAKA,IAAK26B,EACH,MAAMnoB,GAAc9Q,OAAyC,yBAAA,CAC3Ds5B,cAAeN,IAGnB,IAAKI,EACH,MAAMtoB,GAAc9Q,OAA0C,0BAAA,CAC5Du5B,eAAgBj7B,GATnB,CAYF,CAMD,YAAAk7B,CAAaR,GACX,OAAOngC,KAAKk9B,iBAAiBiD,EAC9B,CAED,eAAAS,CAAgBT,QACsB18B,IAAhCzD,KAAKk9B,iBAAiBiD,WAGnBngC,KAAKk9B,iBAAiBiD,EAC9B,CAED,aAAAhD,GACE,OAAY75B,OAAA+U,OAAA,CAAA,EAAArY,KAAKk9B,iBAClB,CAEO,YAAA2D,CAAajc,GACnB5kB,KAAKm8B,YAAcvX,CACpB,CAEO,WAAAkc,CAAYpa,GAClB1mB,KAAKg9B,WAAatW,CACnB,CAMO,qBAAAoY,GACN,MAAMiC,EAAqB/gC,KAAKy+B,IAAI9K,iBAAiB3zB,KAAK6+B,cACpDmC,EAAmBD,GAAsBA,EAAmB,GAC9DC,IACFhhC,KAAKg9B,WAAazd,KAAK2D,MAAkC,IAA5B8d,EAAiBta,UAC9C1mB,KAAKm8B,YAAc5c,KAAK2D,MACoC,KAAzD8d,EAAiBpc,UAAY5kB,KAAKy+B,IAAI7K,kBAG5C,CAQD,qBAAOqN,CACLnK,EACAoK,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAQ/O,GAAI6B,cAAchB,SAChC,IAAKkO,EACH,OAEF,MAAM/F,EAAQ,IAAI+C,GAChBxH,EACArF,GAA6B6P,GAC7B,GAEIC,EAAehiB,KAAK2D,MAA0C,IAApCqP,GAAI6B,cAAcR,iBAClD2H,EAAMsF,aAAaU,GAGfL,GAAqBA,EAAkB,KACzC3F,EAAMuF,YAAYvhB,KAAK2D,MAAsC,IAAhCge,EAAkB,GAAGxa,WAClD6U,EAAMmE,UACJ,iBACAngB,KAAK2D,MAA4C,IAAtCge,EAAkB,GAAGpf,iBAElCyZ,EAAMmE,UACJ,2BACAngB,KAAK2D,MAAsD,IAAhDge,EAAkB,GAAGM,2BAElCjG,EAAMmE,UACJ,eACAngB,KAAK2D,MAA0C,IAApCge,EAAkB,GAAGO,gBAMpC,GAAIN,EAAc,CAChB,MAAMO,EAAaP,EAAaljB,MAC9B0jB,GAJgB,gBAIDA,EAAYj/B,OAEzBg/B,GAAcA,EAAW9c,WAC3B2W,EAAMmE,Uf1S0B,Me4S9BngB,KAAK2D,MAA6B,IAAvBwe,EAAW9c,YAG1B,MAAMgd,EAAuBT,EAAaljB,MACxC0jB,GAZ2B,2BAYZA,EAAYj/B,OAEzBk/B,GAAwBA,EAAqBhd,WAC/C2W,EAAMmE,UACJhO,GACAnS,KAAK2D,MAAuC,IAAjC0e,EAAqBhd,YAIhCyc,GACF9F,EAAMmE,UACJ/N,GACApS,KAAK2D,MAAwB,IAAlBme,GAGhB,CAEDrhC,KAAK6hC,kBACHtG,EACA3J,Gf5TiD,ce8TjDwP,EAAgBU,KAElB9hC,KAAK6hC,kBACHtG,EACAzJ,Gf5TgD,yBe8ThDsP,EAAgBW,KAElB/hC,KAAK6hC,kBACHtG,EACA1J,GfrUkD,wBeuUlDuP,EAAgBY,KAKlB1G,GAASC,GHzOP1nB,IACFA,GAAOwnB,OG0OR,CAED,wBAAOwG,CACLtG,EACA0G,EACAC,EACAC,GAEIA,IACF5G,EAAMmE,UAAUuC,EAAW1iB,KAAK2D,MAAqB,IAAfif,EAAO18B,QACzC08B,EAAOC,oBACT7G,EAAM2E,aAAagC,EAAcC,EAAOC,oBAG7C,CAED,4BAAOC,CACLvL,EACAtD,GAQA8H,GANc,IAAIgD,GAChBxH,EACAtD,GACA,EACAA,GAGH;;;;;;;;;;;;;;;;OChXH,IAEI6N,GAFAD,GAAmC,CAAA,EACnCkB,IAA6B,EAG3B,SAAUC,GACdzL,GAGKzC,OAML/P,YAAW,IAkBb,SAAwBwS,GACtB,MAAM2H,EAAMlM,GAAI6B,cAEZ,eAAgBtwB,OAClB26B,EAAIh6B,SAAS2M,iBAAiB,YAAY,IACxCoxB,GAAa1L,KAGf2H,EAAIh6B,SAAS2M,iBAAiB,UAAU,IACtCoxB,GAAa1L,KAGjB2H,EAAIh6B,SAAS2M,iBAAiB,oBAAoB,KACX,WAAjCqtB,EAAIh6B,SAASqf,iBACf0e,GAAa1L,EACd,IAGC2H,EAAI5L,mBACN4L,EAAI5L,mBAAmBtG,IACrB8U,GAAkB9U,CAAG,IAIzBkS,EAAI3L,OAAOqP,UACTf,GAAgBU,IAAM,CACpBr8B,MAAO08B,EAAO18B,MACd28B,mBAAsC,QAAlBp9B,EAAAm9B,EAAOzc,mBAAW,IAAA1gB,OAAA,EAAAA,EAAEwkB,QACzC,IAEHiV,EAAIvL,OAAOiP,UACTf,GAAgBW,IAAM,CACpBt8B,MAAO08B,EAAO18B,MACd28B,mBAAsC,QAAlBp9B,EAAAm9B,EAAOzc,mBAAW,IAAA1gB,OAAA,EAAAA,EAAEogB,mBACzC,IAEHqZ,EAAIzL,OAAOmP,UACTf,GAAgBY,IAAM,CACpBv8B,MAAO08B,EAAO18B,MACd28B,mBAAsC,QAAlBp9B,EAAAm9B,EAAOzc,mBAAW,IAAA1gB,OAAA,EAAAA,EAAEijB,kBACzC,GAEL,CA5DmBwa,CAAe3L,IAAwB,GACxDxS,YAAW,IAIb,SACEwS,GAEA,MAAM2H,EAAMlM,GAAI6B,cACVsO,EAAYjE,EAAI/c,iBAAiB,YACvC,IAAK,MAAMwZ,KAAYwH,EACrB5E,GAA0BhH,EAAuBoE,GAEnDuD,EAAIxK,cAAc,YAAY/F,GAC5B4P,GAA0BhH,EAAuB5I,IAErD,CAfmByU,CAAqB7L,IAAwB,GAC9DxS,YAAW,IA4Db,SACEwS,GAEA,MAAM2H,EAAMlM,GAAI6B,cAEVwO,EAAWnE,EAAI/c,iBAAiB,WACtC,IAAK,MAAM6R,KAAWqP,EACpBP,GAAsBvL,EAAuBvD,GAG/CkL,EAAIxK,cAAc,WAAW/F,GAC3BmU,GAAsBvL,EAAuB5I,IAEjD,CAzEmB2U,CAAsB/L,IAAwB,GACjE,CA0EA,SAASuL,GACPvL,EACAvD,GAEA,MAAMC,EAAcD,EAAQ7wB,KAI1B8wB,EAAYhV,UAAU,EAAGgT,MACzBA,IAIF8M,GAAM+D,sBAAsBvL,EAAuBtD,EACrD,CAEA,SAASgP,GAAa1L,GACpB,IAAKwL,GAAmB,CACtBA,IAAoB,EACpB,MAAM7D,EAAMlM,GAAI6B,cACV8M,EAAoBzC,EAAI/c,iBAC5B,cAEIyf,EAAe1C,EAAI/c,iBAAiB,SAI1C4C,YAAW,KACTga,GAAM2C,eACJnK,EACAoK,EACAC,EACAC,GACAC,GACD,GACA,EACJ,CACH;;;;;;;;;;;;;;;;OCpIa,MAAAyB,GAGX,WAAArgC,CACWgV,EACAqW,GADA9tB,KAAGyX,IAAHA,EACAzX,KAAa8tB,cAAbA,EAJH9tB,KAAW+iC,aAAY,CAK3B,CAWJ,KAAAC,CAAM3oB,GACAra,KAAK+iC,mBAI+Bt/B,KAApC4W,aAAQ,EAARA,EAAUwa,yBACZ70B,KAAK60B,sBAAwBxa,EAASwa,4BAECpxB,KAArC4W,aAAQ,EAARA,EAAUua,0BACZ50B,KAAK40B,uBAAyBva,EAASua,wBAGrCrC,GAAI6B,cAAcJ,wBACpBjuB,IACG8L,MAAKoxB,IACAA,INFPvJ,KACHC,GArC+B,MAsC/BD,IAAmB,GMEXV,GAAyBh5B,MAAM6R,MAC7B,IAAM0wB,GAAkBviC,QACxB,IAAMuiC,GAAkBviC,QAE1BA,KAAK+iC,aAAc,EACpB,IAEFr9B,OAAMxC,IACLgvB,GAAcptB,KAAK,0CAA0C5B,IAAQ,IAGzEgvB,GAAcptB,KACZ,qHAIL,CAED,0BAAI8vB,CAAuB/lB,GACzB8lB,GAAgBP,cAAcQ,uBAAyB/lB,CACxD,CACD,0BAAI+lB,GACF,OAAOD,GAAgBP,cAAcQ,sBACtC,CAED,yBAAIC,CAAsBhmB,GACxB8lB,GAAgBP,cAAcS,sBAAwBhmB,CACvD,CACD,yBAAIgmB,GACF,OAAOF,GAAgBP,cAAcS,qBACtC,EC2CDnd,GACE,IAAI5O,EAAU,eAzB8B,CAC9Ce,GACEoB,QAASoP,MAGX,MAAM5C,EAAM5N,EAAUoD,YAAY,OAAOjC,eACnC8iB,EAAgBjkB,EACnBoD,YAAY,0BACZjC,eAEH,GAvEyB,cAuErByM,EAAI/U,KACN,MAAMuV,GAAc9Q,OAAM,kBAE5B,GAAsB,oBAAXrD,OACT,MAAMmU,GAAc9Q,OAAM,cf2CxB,SAAmBrD,GACvBsuB,GAAiBtuB,CACnB;;;;;;;;;;;;;;;;Oe3CEo/B,CAASp/B,QACT,MAAMq/B,EAAe,IAAIL,GAAsBrrB,EAAKqW,GAGpD,OAFAqV,EAAaH,MAAM3oB,GAEZ8oB,CAAY,GAK0C,WAE7DppB,GAAgBrX,GAAMuP,IAEtB8H,GAAgBrX,GAAMuP,GAAS;;;;;;;;;;;;;;;;;AClHpB,MAAAmxB,GAGX,WAAA3gC,CACSgV,EACEkI,GADF3f,KAAGyX,IAAHA,EACEzX,KAAS2f,UAATA,CACP,CAEJ,0BAAIiV,GACF,OAAO50B,KAAK2f,UAAUiV,sBACvB,CAED,0BAAIA,CAAuB/lB,GACzB7O,KAAK2f,UAAUiV,uBAAyB/lB,CACzC,CAED,yBAAIgmB,GACF,OAAO70B,KAAK2f,UAAUkV,qBACvB,CAED,yBAAIA,CAAsBhmB,GACxB7O,KAAK2f,UAAUkV,sBAAwBhmB,CACxC,CAED,KAAA0sB,CAAMyD,GACJ,ODoDY,SACdvd,EACA/e,GEpFI,IACJ0E,EFsFA,OADAqa,GErFAra,EFqFiCqa,IEnFjBra,EAA+BuY,UACrCvY,EAA+BuY,UAEhCvY,EFiFF,IAAIk3B,GAAM7c,EAAsC/e,EACzD,CC1DW64B,CAAMv7B,KAAK2f,UAAWqf,EAC9B,EEbH,SAASqE,GACPx5B,GAEA,MAAM4N,EAAM5N,EAAUoD,YAAY,cAAcjC,eAE1CyW,EAAc5X,EAAUoD,YAAY,eAAejC,eAEzD,OAAO,IAAIo4B,GAAsB3rB,EAAKgK,EACxC;;;;;;;;;;;;;;;;;AApBA,IAAmC6hB,OAsBT1jB,IArBP5T,SAAS2U,kBACxB,IAAI7X,EACF,qBACAu6B,GAAkB,WAKtBC,GAAiBvpB;;;;;;;;;;;;;;;;;OCfnB6F,GAAS7F,oCAA+B"}