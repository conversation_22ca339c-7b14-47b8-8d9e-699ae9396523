((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Ie,Se){try{!(function(){function M(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let x=M(Ie);class o extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,o.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,a.prototype.create)}}class a{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var a,r=t[0]||{},n=this.service+"/"+e,i=this.errors[e],i=i?(a=r,i.replace(V,(e,t)=>{var r=a[t];return null!=r?String(r):`<${t}?>`})):"Error",i=this.serviceName+`: ${i} (${n}).`;return new o(n,i,r)}}let V=/\{\$([^}]+)}/g;class e{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let $=(t,e)=>e.some(e=>t instanceof e),r,t;let n=new WeakMap,s=new WeakMap,i=new WeakMap,u=new WeakMap,c=new WeakMap;let l={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return s.get(e);if("objectStoreNames"===t)return e.objectStoreNames||i.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return p(e[t])},set(e,t,r){return e[t]=r,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function _(a){return a!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(t=t||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(a)?function(...e){return a.apply(d(this),e),p(n.get(this))}:function(...e){return p(a.apply(d(this),e))}:function(e,...t){var r=a.call(d(this),e,...t);return i.set(r,e.sort?e.sort():[e]),p(r)}}function F(e){var i,t;return"function"==typeof e?_(e):(e instanceof IDBTransaction&&(i=e,s.has(i)||(t=new Promise((e,t)=>{let r=()=>{i.removeEventListener("complete",a),i.removeEventListener("error",n),i.removeEventListener("abort",n)},a=()=>{e(),r()},n=()=>{t(i.error||new DOMException("AbortError","AbortError")),r()};i.addEventListener("complete",a),i.addEventListener("error",n),i.addEventListener("abort",n)}),s.set(i,t))),$(e,r=r||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,l):e)}function p(e){var i,t;return e instanceof IDBRequest?(i=e,(t=new Promise((e,t)=>{let r=()=>{i.removeEventListener("success",a),i.removeEventListener("error",n)},a=()=>{e(p(i.result)),r()},n=()=>{t(i.error),r()};i.addEventListener("success",a),i.addEventListener("error",n)})).then(e=>{e instanceof IDBCursor&&n.set(e,i)}).catch(()=>{}),c.set(t,i),t):u.has(e)?u.get(e):((t=F(e))!==e&&(u.set(e,t),c.set(t,e)),t)}let d=e=>c.get(e);let K=["get","getKey","getAll","getAllKeys","count"],H=["put","add","delete","clear"],f=new Map;function g(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(f.get(t))return f.get(t);let n=t.replace(/FromIndex$/,""),i=t!==n,o=H.includes(n);var r;return n in(i?IDBIndex:IDBObjectStore).prototype&&(o||K.includes(n))?(r=async function(e,...t){var r=this.transaction(e,o?"readwrite":"readonly");let a=r.store;return i&&(a=a.index(t.shift())),(await Promise.all([a[n](...t),o&&r.done]))[0]},f.set(t,r),r):void 0}}l={...h=l,get:(e,t,r)=>g(e,t)||h.get(e,t,r),has:(e,t)=>!!g(e,t)||h.has(e,t)};var h,v="@firebase/installations",w="0.6.17";let m=1e4,y="w:"+w,R="FIS_v2",W="https://firebaseinstallations.googleapis.com/v1",z=36e5;var U;let b=new a("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function J(e){return e instanceof o&&e.code.includes("request-failed")}function I({projectId:e}){return W+`/projects/${e}/installations`}function G(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function S(e,t){var r=(await t.json()).error;return b.create("request-failed",{requestName:e,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function Y({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function Z(e,{refreshToken:t}){var r=Y(e);return r.append("Authorization",(e=t,R+" "+e)),r}async function C(e){var t=await e();return 500<=t.status&&t.status<600?e():t}function Q(t){return new Promise(e=>{setTimeout(e,t)})}let X=/^[cdef][\w-]{21}$/,T="";function ee(){try{var e=new Uint8Array(17),t=((self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16,(e=>btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_"))(e).substr(0,22));return X.test(t)?t:T}catch(e){return T}}function k(e){return e.appName+"!"+e.appId}let E=new Map;function te(e,t){var r=k(e),e=(re(r,t),r),r=ae();r&&r.postMessage({key:e,fid:t}),ne()}function re(e,t){var r=E.get(e);if(r)for(var a of r)a(t)}let j=null;function ae(){return!j&&"BroadcastChannel"in self&&((j=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{re(e.data.key,e.data.fid)}),j}function ne(){0===E.size&&j&&(j.close(),j=null)}let ie="firebase-installations-database",oe=1,D="firebase-installations-store",se=null;function P(){return se=se||((e,t,{blocked:r,upgrade:a,blocking:n,terminated:i})=>{let o=indexedDB.open(e,t);var s=p(o);return a&&o.addEventListener("upgradeneeded",e=>{a(p(o.result),e.oldVersion,e.newVersion,p(o.transaction),e)}),r&&o.addEventListener("blocked",e=>r(e.oldVersion,e.newVersion,e)),s.then(e=>{i&&e.addEventListener("close",()=>i()),n&&e.addEventListener("versionchange",e=>n(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s})(ie,oe,{upgrade:(e,t)=>{0===t&&e.createObjectStore(D)}})}async function B(e,t){var r=k(e),a=(await P()).transaction(D,"readwrite"),n=a.objectStore(D),i=await n.get(r);return await n.put(t,r),await a.done,i&&i.fid===t.fid||te(e,t.fid),t}async function L(e){var t=k(e),r=(await P()).transaction(D,"readwrite");await r.objectStore(D).delete(t),await r.done}async function q(e,t){var r=k(e),a=(await P()).transaction(D,"readwrite"),n=a.objectStore(D),i=await n.get(r),o=t(i);return void 0===o?await n.delete(r):await n.put(o,r),await a.done,!o||i&&i.fid===o.fid||te(e,o.fid),o}async function N(r){let a;var e=await q(r.appConfig,e=>{var t=ce(e||{fid:ee(),registrationStatus:0}),t=((e,t)=>{var r,a;return 0===t.registrationStatus?navigator.onLine?(r={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},a=(async(t,r)=>{try{var e=await(async({appConfig:e,heartbeatServiceProvider:t},{fid:r})=>{let a=I(e);var n=Y(e),i=((i=t.getImmediate({optional:!0}))&&(i=await i.getHeartbeatsHeader())&&n.append("x-firebase-client",i),{fid:r,authVersion:R,appId:e.appId,sdkVersion:y});let o={method:"POST",headers:n,body:JSON.stringify(i)};if((n=await C(()=>fetch(a,o))).ok)return{fid:(i=await n.json()).fid||r,registrationStatus:2,refreshToken:i.refreshToken,authToken:G(i.authToken)};throw await S("Create Installation",n)})(t,r);return B(t.appConfig,e)}catch(e){throw J(e)&&409===e.customData.serverCode?await L(t.appConfig):await B(t.appConfig,{fid:r.fid,registrationStatus:0}),e}})(e,r),{installationEntry:r,registrationPromise:a}):(r=Promise.reject(b.create("app-offline")),{installationEntry:t,registrationPromise:r}):1===t.registrationStatus?{installationEntry:t,registrationPromise:(async e=>{let t=await ue(e.appConfig);for(;1===t.registrationStatus;)await Q(100),t=await ue(e.appConfig);var r,a;return 0!==t.registrationStatus?t:({installationEntry:r,registrationPromise:a}=await N(e),a||r)})(e)}:{installationEntry:t}})(r,t);return a=t.registrationPromise,t.installationEntry});return e.fid===T?{installationEntry:await a}:{installationEntry:e,registrationPromise:a}}function ue(e){return q(e,e=>{if(e)return ce(e);throw b.create("installation-not-found")})}function ce(e){var t;return 1===(t=e).registrationStatus&&t.registrationTime+m<Date.now()?{fid:e.fid,registrationStatus:0}:e}async function le({appConfig:e,heartbeatServiceProvider:t},r){[n,i]=[e,r.fid];let a=I(n)+`/${i}/authTokens:generate`;var n,i,o=Z(e,r),s=t.getImmediate({optional:!0}),s=(s&&(s=await s.getHeartbeatsHeader())&&o.append("x-firebase-client",s),{installation:{sdkVersion:y,appId:e.appId}});let u={method:"POST",headers:o,body:JSON.stringify(s)};o=await C(()=>fetch(a,u));if(o.ok)return G(await o.json());throw await S("Generate Auth Token",o)}async function O(a,n=!1){let i;var e=await q(a.appConfig,e=>{if(!de(e))throw b.create("not-registered");var t,r=e.authToken;if(n||2!==(t=r).requestStatus||(e=>{var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+z})(t)){if(1===r.requestStatus)return i=(async(e,t)=>{let r=await pe(e.appConfig);for(;1===r.authToken.requestStatus;)await Q(100),r=await pe(e.appConfig);var a=r.authToken;return 0===a.requestStatus?O(e,t):a})(a,n),e;if(navigator.onLine)return t=e,r={requestStatus:1,requestTime:Date.now()},r=Object.assign(Object.assign({},t),{authToken:r}),i=(async(t,r)=>{try{var e=await le(t,r),a=Object.assign(Object.assign({},r),{authToken:e});return await B(t.appConfig,a),e}catch(e){var n;throw!J(e)||401!==e.customData.serverCode&&404!==e.customData.serverCode?(n=Object.assign(Object.assign({},r),{authToken:{requestStatus:0}}),await B(t.appConfig,n)):await L(t.appConfig),e}})(a,r),r;throw b.create("app-offline")}return e});return i?await i:e.authToken}function pe(e){return q(e,e=>{var t,r;if(de(e))return t=e.authToken,1===(r=t).requestStatus&&r.requestTime+m<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e;throw b.create("not-registered")})}function de(e){return void 0!==e&&2===e.registrationStatus}async function fe(e){var t=e,{installationEntry:r,registrationPromise:a}=await N(t);return(a||O(t)).catch(console.error),r.fid}async function ge(e,t=!1){var r=e,a=(await(!(a=(await N(r)).registrationPromise)||!await a),await O(r,t));return a.token}async function he(e,t){[a,n]=[e,t.fid];let r=I(a)+"/"+n;var a,n;let i={method:"DELETE",headers:Z(e,t)};var o=await C(()=>fetch(r,i));if(!o.ok)throw await S("Delete Installation",o)}function ve(t,n){let i=t.appConfig;{t=i;var r=n,a=(ae(),k(t));let e=E.get(a);e||(e=new Set,E.set(a,e)),e.add(r)}return()=>{var e,t,r,a;e=i,t=n,r=k(e),(a=E.get(r))&&(a.delete(t),0===a.size&&E.delete(r),ne())}}function A(e){return b.create("missing-app-config-values",{valueName:e})}let we="installations",me=e=>{var t=e.getProvider("app").getImmediate();return{app:t,appConfig:(e=>{if(!e||!e.options)throw A("App Configuration");if(!e.name)throw A("App Name");var t;for(t of["projectId","apiKey","appId"])if(!e.options[t])throw A(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}})(t),heartbeatServiceProvider:Se._getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},ye=e=>{var t=e.getProvider("app").getImmediate();let r=Se._getProvider(t,we).getImmediate();return{getId:()=>fe(r),getToken:e=>ge(r,e)}};Se._registerComponent(new e(we,me,"PUBLIC")),Se._registerComponent(new e("installations-internal",ye,"PRIVATE")),Se.registerVersion(v,w),Se.registerVersion(v,w,"esm2017");class be{constructor(e,t){this.app=e,this._delegate=t}getId(){return fe(this._delegate)}getToken(e){return ge(this._delegate,e)}delete(){return(async e=>{var t=e.appConfig,r=await q(t,e=>{if(!e||0!==e.registrationStatus)return e});if(r){if(1===r.registrationStatus)throw b.create("delete-pending-registration");if(2===r.registrationStatus){if(!navigator.onLine)throw b.create("app-offline");await he(t,r),await L(t)}}})(this._delegate)}onIdChange(e){return ve(this._delegate,e)}}(U=x.default).INTERNAL.registerComponent(new e("installations-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("installations").getImmediate();return new be(t,r)},"PUBLIC")),U.registerVersion("@firebase/installations-compat","0.2.17")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-installations-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-installations-compat.js.map
