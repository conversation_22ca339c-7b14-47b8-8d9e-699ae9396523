((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Kd,Gd){try{!(function(){function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let i=n(Kd),s=()=>{},a=function(t){var r=[];let n=0;for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);e<128?r[n++]=e:(e<2048?r[n++]=e>>6|192:(55296==(64512&e)&&i+1<t.length&&56320==(64512&t.charCodeAt(i+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++i)),r[n++]=e>>18|240,r[n++]=e>>12&63|128):r[n++]=e>>12|224,r[n++]=e>>6&63|128),r[n++]=63&e|128)}return r},U={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let h=0;h<r.length;h+=3){var s=r[h],a=h+1<r.length,o=a?r[h+1]:0,l=h+2<r.length,u=l?r[h+2]:0;let e=(15&o)<<2|u>>6,t=63&u;l||(t=64,a)||(e=64),i.push(n[s>>2],n[(3&s)<<4|o>>4],n[e],n[t])}return i.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(a(e),t)},decodeString(r,n){if(this.HAS_NATIVE_SUPPORT&&!n)return atob(r);{var i=this.decodeStringToByteArray(r,n);var s=[];let e=0,t=0;for(;e<i.length;){var a,o,l,u=i[e++];u<128?s[t++]=String.fromCharCode(u):191<u&&u<224?(a=i[e++],s[t++]=String.fromCharCode((31&u)<<6|63&a)):239<u&&u<365?(a=((7&u)<<18|(63&i[e++])<<12|(63&i[e++])<<6|63&i[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=i[e++],l=i[e++],s[t++]=String.fromCharCode((15&u)<<12|(63&o)<<6|63&l))}return s.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let l=0;l<e.length;){var i=r[e.charAt(l++)],s=l<e.length?r[e.charAt(l)]:0,a=++l<e.length?r[e.charAt(l)]:64,o=++l<e.length?r[e.charAt(l)]:64;if(++l,null==i||null==s||null==a||null==o)throw new q;n.push(i<<2|s>>4),64!==a&&(n.push(s<<4&240|a>>2),64!==o)&&n.push(a<<6&192|o)}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class q extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let j=function(e){var t=a(e);return U.encodeByteArray(t,!0)},z=function(e){return j(e).replace(/\./g,"")},K=function(e){try{return U.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};function G(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}let $=()=>G().__FIREBASE_DEFAULTS__,Q=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},H=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&K(e[1]);return t&&JSON.parse(t)}},W=()=>{try{return s()||$()||Q()||H()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};function Y(e){return e.endsWith(".cloudworkstations.dev")}let X={};let J=!1;function Z(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&Y(window.location.host)&&X[e]!==t&&!X[e]&&!J){X[e]=t;let u="__firebase__banner";let h=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(X))(X[e]?t.emulator:t.prod).push(e);return t})().prod.length;function c(e){return"__firebase__banner__"+e}function d(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;J=!0,(e=document.getElementById(u))&&e.remove()},e}function r(){var e,t,r=(e=>{let t=document.getElementById(e),r=!1;return t||((t=document.createElement("div")).setAttribute("id",e),r=!0),{created:r,element:t}})(u),n=c("text"),i=document.getElementById(n)||document.createElement("span"),s=c("learnmore"),a=document.getElementById(s)||document.createElement("a"),o=c("preprendIcon"),l=document.getElementById(o)||document.createElementNS("http://www.w3.org/2000/svg","svg");r.created&&(r=r.element,(t=r).style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",(t=a).setAttribute("id",s),t.innerText="Learn more",t.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",t.setAttribute("target","__blank"),t.style.paddingLeft="5px",t.style.textDecoration="underline",s=d(),t=o,(e=l).setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px",r.append(l,i,a,s),document.body.appendChild(r)),h?(i.innerText="Preview backend disconnected.",l.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(l.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,i.innerText="Preview backend running in this workspace."),i.setAttribute("id",n)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",r):r()}}function ee(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function te(){var e=null==(e=W())?void 0:e.forceEnvironment;if("node"===e)return 1;if("browser"!==e)try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){}}function re(){return!te()&&navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function ne(){return!te()&&navigator.userAgent&&(navigator.userAgent.includes("Safari")||navigator.userAgent.includes("WebKit"))&&!navigator.userAgent.includes("Chrome")}class ie extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,ie.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,se.prototype.create)}}class se{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},i=this.service+"/"+e,s=this.errors[e],s=s?(n=r,s.replace(ae,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",s=this.serviceName+`: ${s} (${i}).`;return new ie(i,s,r)}}let ae=/\{\$([^}]+)}/g;function oe(e,t){if(e!==t){var r,n,i=Object.keys(e),s=Object.keys(t);for(r of i){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(le(a)&&le(o)){if(!oe(a,o))return!1}else if(a!==o)return!1}for(n of s)if(!i.includes(n))return!1}return!0}function le(e){return null!==e&&"object"==typeof e}function _(e){return e&&e._delegate?e._delegate:e}class ue{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var c;(t=c=c||{})[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT";let he={debug:c.DEBUG,verbose:c.VERBOSE,info:c.INFO,warn:c.WARN,error:c.ERROR,silent:c.SILENT},ce=c.INFO,de={[c.DEBUG]:"log",[c.VERBOSE]:"log",[c.INFO]:"info",[c.WARN]:"warn",[c.ERROR]:"error"},fe=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),i=de[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)}};var ge,me,pr,yr,vr,wr,_r,br,Ir,Tr,m,pe,e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Er=(!(function(){var e,t,s;function r(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(){}function a(e,t,r){r=r||0;var n=Array(16);if("string"==typeof t)for(var i=0;i<16;++i)n[i]=t.charCodeAt(r++)|t.charCodeAt(r++)<<8|t.charCodeAt(r++)<<16|t.charCodeAt(r++)<<24;else for(i=0;i<16;++i)n[i]=t[r++]|t[r++]<<8|t[r++]<<16|t[r++]<<24;t=e.g[0],r=e.g[1];var i=e.g[2],s=e.g[3],a=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=r+((a=t+(s^r&(i^s))+n[0]+3614090360&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[1]+3905402710&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[2]+606105819&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[3]+3250441966&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[4]+4118548399&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[5]+1200080426&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[6]+2821735955&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[7]+4249261313&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[8]+1770035416&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[9]+2336552879&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[10]+4294925233&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[11]+2304563134&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[12]+1804603682&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[13]+4254626195&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[14]+2792965006&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[15]+1236535329&4294967295)<<22&4294967295|a>>>10))+((a=t+(i^s&(r^i))+n[1]+4129170786&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[6]+3225465664&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[11]+643717713&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[0]+3921069994&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[5]+3593408605&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[10]+38016083&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[15]+3634488961&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[4]+3889429448&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[9]+568446438&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[14]+3275163606&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[3]+4107603335&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[8]+1163531501&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[13]+2850285829&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[2]+4243563512&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[7]+1735328473&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[12]+2368359562&4294967295)<<20&4294967295|a>>>12))+((a=t+(r^i^s)+n[5]+4294588738&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[8]+2272392833&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[11]+1839030562&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[14]+4259657740&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[1]+2763975236&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[4]+1272893353&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[7]+4139469664&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[10]+3200236656&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[13]+681279174&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[0]+3936430074&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[3]+3572445317&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[6]+76029189&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[9]+3654602809&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[12]+3873151461&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[15]+530742520&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[2]+3299628645&4294967295)<<23&4294967295|a>>>9))+((a=t+(i^(r|~s))+n[0]+4096336452&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[7]+1126891415&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[14]+2878612391&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[5]+4237533241&4294967295)<<21&4294967295|a>>>11))+((a=t+(i^(r|~s))+n[12]+1700485571&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[3]+2399980690&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[10]+4293915773&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[1]+2240044497&4294967295)<<21&4294967295|a>>>11))+((a=t+(i^(r|~s))+n[8]+1873313359&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[15]+4264355552&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[6]+2734768916&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[13]+1309151649&4294967295)<<21&4294967295|a>>>11))+((s=(t=r+((a=t+(i^(r|~s))+n[4]+4149444226&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[11]+3174756917&4294967295)<<10&4294967295|a>>>22))^((i=s+((a=i+(t^(s|~r))+n[2]+718787259&4294967295)<<15&4294967295|a>>>17))|~t))+n[9]+3951481745&4294967295;e.g[0]=e.g[0]+t&4294967295,e.g[1]=e.g[1]+(i+(a<<21&4294967295|a>>>11))&4294967295,e.g[2]=e.g[2]+i&4294967295,e.g[3]=e.g[3]+s&4294967295}function u(e,t){this.h=t;for(var r=[],n=!0,i=e.length-1;0<=i;i--){var s=0|e[i];n&&s==t||(r[i]=s,n=!1)}this.g=r}t=r,s=function(){this.blockSize=-1},n.prototype=s.prototype,t.D=s.prototype,t.prototype=new n,(t.prototype.constructor=t).C=function(e,t,r){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return s.prototype[t].apply(e,n)},r.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},r.prototype.u=function(e,t){for(var r=(t=void 0===t?e.length:t)-this.blockSize,n=this.B,i=this.h,s=0;s<t;){if(0==i)for(;s<=r;)a(this,e,s),s+=this.blockSize;if("string"==typeof e){for(;s<t;)if(n[i++]=e.charCodeAt(s++),i==this.blockSize){a(this,n),i=0;break}}else for(;s<t;)if(n[i++]=e[s++],i==this.blockSize){a(this,n),i=0;break}}this.h=i,this.o+=t},r.prototype.v=function(){var e=Array((this.h<56?this.blockSize:2*this.blockSize)-this.h);e[0]=128;for(var t=1;t<e.length-8;++t)e[t]=0;for(var r=8*this.o,t=e.length-8;t<e.length;++t)e[t]=255&r,r/=256;for(this.u(e),e=Array(16),t=r=0;t<4;++t)for(var n=0;n<32;n+=8)e[r++]=this.g[t]>>>n&255;return e};var i={};function o(e){return-128<=e&&e<128?(t=e,r=function(e){return new u([0|e],e<0?-1:0)},n=i,Object.prototype.hasOwnProperty.call(n,t)?n[t]:n[t]=r(t)):new u([0|e],e<0?-1:0);var t,r,n}function h(e){if(isNaN(e)||!isFinite(e))return c;if(e<0)return m(h(-e));for(var t=[],r=1,n=0;r<=e;n++)t[n]=e/r|0,r*=4294967296;return new u(t,0)}var c=o(0),l=o(1),d=o(16777216);function f(e){if(0==e.h){for(var t=0;t<e.g.length;t++)if(0!=e.g[t])return;return 1}}function g(e){return-1==e.h}function m(e){for(var t=e.g.length,r=[],n=0;n<t;n++)r[n]=~e.g[n];return new u(r,~e.h).add(l)}function p(e,t){return e.add(m(t))}function y(e,t){for(;(65535&e[t])!=e[t];)e[t+1]+=e[t]>>>16,e[t]&=65535,t++}function v(e,t){this.g=e,this.h=t}function w(e,t){if(f(t))throw Error("division by zero");if(f(e))return new v(c,c);if(g(e))return t=w(m(e),t),new v(m(t.g),m(t.h));if(g(t))return t=w(e,m(t)),new v(m(t.g),t.h);if(30<e.g.length){if(g(e)||g(t))throw Error("slowDivide_ only works with positive integers.");for(var r=l,n=t;n.l(e)<=0;)r=_(r),n=_(n);for(var i=b(r,1),s=b(n,1),n=b(n,2),r=b(r,2);!f(n);){var a=s.add(n);a.l(e)<=0&&(i=i.add(r),s=a),n=b(n,1),r=b(r,1)}return t=p(e,i.j(t)),new v(i,t)}for(i=c;0<=e.l(t);){for(r=Math.max(1,Math.floor(e.m()/t.m())),n=(n=Math.ceil(Math.log(r)/Math.LN2))<=48?1:Math.pow(2,n-48),a=(s=h(r)).j(t);g(a)||0<a.l(e);)a=(s=h(r-=n)).j(t);f(s)&&(s=l),i=i.add(s),e=p(e,a)}return new v(i,e)}function _(e){for(var t=e.g.length+1,r=[],n=0;n<t;n++)r[n]=e.i(n)<<1|e.i(n-1)>>>31;return new u(r,e.h)}function b(e,t){var r=t>>5;t%=32;for(var n=e.g.length-r,i=[],s=0;s<n;s++)i[s]=0<t?e.i(s+r)>>>t|e.i(s+r+1)<<32-t:e.i(s+r);return new u(i,e.h)}(e=u.prototype).m=function(){if(g(this))return-m(this).m();for(var e=0,t=1,r=0;r<this.g.length;r++){var n=this.i(r);e+=(0<=n?n:4294967296+n)*t,t*=4294967296}return e},e.toString=function(e){if((e=e||10)<2||36<e)throw Error("radix out of range: "+e);if(f(this))return"0";if(g(this))return"-"+m(this).toString(e);for(var t=h(Math.pow(e,6)),r=this,n="";;){var i=w(r,t).g,s=((0<(r=p(r,i.j(t))).g.length?r.g[0]:r.h)>>>0).toString(e);if(f(r=i))return s+n;for(;s.length<6;)s="0"+s;n=s+n}},e.i=function(e){return e<0?0:e<this.g.length?this.g[e]:this.h},e.l=function(e){return g(e=p(this,e))?-1:f(e)?0:1},e.abs=function(){return g(this)?m(this):this},e.add=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0,i=0;i<=t;i++){var s=n+(65535&this.i(i))+(65535&e.i(i)),a=(s>>>16)+(this.i(i)>>>16)+(e.i(i)>>>16),n=a>>>16;r[i]=(a&=65535)<<16|(s&=65535)}return new u(r,-2147483648&r[r.length-1]?-1:0)},e.j=function(e){if(f(this)||f(e))return c;if(g(this))return g(e)?m(this).j(m(e)):m(m(this).j(e));if(g(e))return m(this.j(m(e)));if(this.l(d)<0&&e.l(d)<0)return h(this.m()*e.m());for(var t=this.g.length+e.g.length,r=[],n=0;n<2*t;n++)r[n]=0;for(n=0;n<this.g.length;n++)for(var i=0;i<e.g.length;i++){var s=this.i(n)>>>16,a=65535&this.i(n),o=e.i(i)>>>16,l=65535&e.i(i);r[2*n+2*i]+=a*l,y(r,2*n+2*i),r[2*n+2*i+1]+=s*l,y(r,2*n+2*i+1),r[2*n+2*i+1]+=a*o,y(r,2*n+2*i+1),r[2*n+2*i+2]+=s*o,y(r,2*n+2*i+2)}for(n=0;n<t;n++)r[n]=r[2*n+1]<<16|r[2*n];for(n=t;n<2*t;n++)r[n]=0;return new u(r,0)},e.A=function(e){return w(this,e).h},e.and=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)&e.i(n);return new u(r,this.h&e.h)},e.or=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)|e.i(n);return new u(r,this.h|e.h)},e.xor=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)^e.i(n);return new u(r,this.h^e.h)},r.prototype.digest=r.prototype.v,r.prototype.reset=r.prototype.s,r.prototype.update=r.prototype.u,me=r,u.prototype.multiply=u.prototype.j,u.prototype.modulo=u.prototype.A,u.prototype.compare=u.prototype.l,u.prototype.toNumber=u.prototype.m,u.prototype.getBits=u.prototype.i,u.fromNumber=h,u.fromString=function e(t,r){if(0==t.length)throw Error("number format error: empty string");if((r=r||10)<2||36<r)throw Error("radix out of range: "+r);if("-"==t.charAt(0))return m(e(t.substring(1),r));if(0<=t.indexOf("-"))throw Error('number format error: interior "-" character');for(var n=h(Math.pow(r,8)),i=c,s=0;s<t.length;s+=8)var a=Math.min(8,t.length-s),o=parseInt(t.substring(s,s+a),r),i=(a<8?(a=h(Math.pow(r,a)),i.j(a)):i=i.j(n)).add(h(o));return i},ge=u}).apply(void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{}),"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{});!(function(){var e,N="function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,r){return e!=Array.prototype&&e!=Object.prototype&&(e[t]=r.value),e};var k=(e=>{e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof Er&&Er];for(var t=0;t<e.length;++t){var r=e[t];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")})(this);var t="Array.prototype.values",r=function(e){return e||function(){return r=function(e,t){return t},(t=this)instanceof String&&(t+=""),n=0,i=!1,(e={next:function(){var e;return!i&&n<t.length?(e=n++,{value:r(e,t[e]),done:!1}):{done:i=!0,value:void 0}}})[Symbol.iterator]=function(){return e},e;var t,r,n,i,e}};if(r)e:{var n=k;t=t.split(".");for(var i=0;i<t.length-1;i++){var F=t[i];if(!(F in n))break e;n=n[F]}(r=r(i=n[t=t[t.length-1]]))!=i&&null!=r&&N(n,t,{configurable:!0,writable:!0,value:r})}var B=B||{},R=this||self;function U(e){var t=typeof e;return"array"==(t="object"!=t?t:e?Array.isArray(e)?"array":t:"null")||"object"==t&&"number"==typeof e.length}function u(e){var t=typeof e;return"object"==t&&null!=e||"function"==t}function q(e,t,r){return e.call.apply(e.bind,arguments)}function j(t,r,e){var n;if(t)return 2<arguments.length?(n=Array.prototype.slice.call(arguments,2),function(){var e=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(e,n),t.apply(r,e)}):function(){return t.apply(r,arguments)};throw Error()}function p(e,t,r){return(p=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?q:j).apply(null,arguments)}function z(t){var r=Array.prototype.slice.call(arguments,1);return function(){var e=r.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function s(e,s){function t(){}t.prototype=s.prototype,e.aa=s.prototype,e.prototype=new t,(e.prototype.constructor=e).Qb=function(e,t,r){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return s.prototype[t].apply(e,n)}}function K(t){var r=t.length;if(0<r){var n=Array(r);for(let e=0;e<r;e++)n[e]=t[e];return n}return[]}function G(t){for(let e=1;e<arguments.length;e++){var r=arguments[e];if(U(r)){var n=t.length||0,i=r.length||0;t.length=n+i;for(let e=0;e<i;e++)t[n+e]=r[e]}else t.push(r)}}function O(e){return/^[\s\xa0]*$/.test(e)}function a(){var e=R.navigator;return(e=e&&e.userAgent)||""}function $(e){return $[" "](e),e}$[" "]=function(){};var Q=!(-1==a().indexOf("Gecko")||-1!=a().toLowerCase().indexOf("webkit")&&-1==a().indexOf("Edge")||-1!=a().indexOf("Trident")||-1!=a().indexOf("MSIE")||-1!=a().indexOf("Edge"));function H(e,t,r){for(var n in e)t.call(r,e[n],n,e)}function W(e){var t,r={};for(t in e)r[t]=e[t];return r}let Y="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function X(t){let r,n;for(let i=1;i<arguments.length;i++){for(r in n=arguments[i])t[r]=n[r];for(let e=0;e<Y.length;e++)r=Y[e],Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}}var J=new class{constructor(e,t){this.i=e,this.j=t,this.h=0,this.g=null}get(){let e;return 0<this.h?(this.h--,e=this.g,this.g=e.next,e.next=null):e=this.i(),e}}(()=>new Z,e=>e.reset());class Z{constructor(){this.next=this.g=this.h=null}set(e,t){this.h=e,this.g=t,this.next=null}reset(){this.next=this.g=this.h=null}}let o,l=!1,ee=new class{constructor(){this.h=this.g=null}add(e,t){var r=J.get();r.set(e,t),this.h?this.h.next=r:this.g=r,this.h=r}},te=()=>{let e=R.Promise.resolve(void 0);o=()=>{e.then(re)}};var re=()=>{for(var e;e=(()=>{let e=ee,t=null;return e.g&&(t=e.g,e.g=e.g.next,e.g||(e.h=null),t.next=null),t})();){try{e.h.call(e.g)}catch(e){(e=>{R.setTimeout(()=>{throw e},0)})(e)}var t=J;t.j(e),t.h<100&&(t.h++,e.next=t.g,t.g=e)}l=!1};function h(){this.s=this.s,this.C=this.C}function c(e,t){this.type=e,this.g=this.target=t,this.defaultPrevented=!1}h.prototype.s=!1,h.prototype.ma=function(){this.s||(this.s=!0,this.N())},h.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},c.prototype.h=function(){this.defaultPrevented=!0};var ne=(()=>{if(!R.addEventListener||!Object.defineProperty)return!1;var e=!1,t=Object.defineProperty({},"passive",{get:function(){e=!0}});try{var r=()=>{};R.addEventListener("test",r,t),R.removeEventListener("test",r,t)}catch(e){}return e})();function d(e,t){if(c.call(this,e?e.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,e){var r=this.type=e.type,n=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:null;if(this.target=e.target||e.srcElement,this.g=t,t=e.relatedTarget){if(Q){e:{try{$(t.nodeName);var i=!0;break e}catch(e){}i=!1}i||(t=null)}}else"mouseover"==r?t=e.fromElement:"mouseout"==r&&(t=e.toElement);this.relatedTarget=t,n?(this.clientX=void 0!==n.clientX?n.clientX:n.pageX,this.clientY=void 0!==n.clientY?n.clientY:n.pageY,this.screenX=n.screenX||0,this.screenY=n.screenY||0):(this.clientX=void 0!==e.clientX?e.clientX:e.pageX,this.clientY=void 0!==e.clientY?e.clientY:e.pageY,this.screenX=e.screenX||0,this.screenY=e.screenY||0),this.button=e.button,this.key=e.key||"",this.ctrlKey=e.ctrlKey,this.altKey=e.altKey,this.shiftKey=e.shiftKey,this.metaKey=e.metaKey,this.pointerId=e.pointerId||0,this.pointerType="string"==typeof e.pointerType?e.pointerType:ie[e.pointerType]||"",this.state=e.state,(this.i=e).defaultPrevented&&d.aa.h.call(this)}}s(d,c);var ie={2:"touch",3:"pen",4:"mouse"},f=(d.prototype.h=function(){d.aa.h.call(this);var e=this.i;e.preventDefault?e.preventDefault():e.returnValue=!1},"closure_listenable_"+(1e6*Math.random()|0)),se=0;function ae(e,t,r,n,i){this.listener=e,this.proxy=null,this.src=t,this.type=r,this.capture=!!n,this.ha=i,this.key=++se,this.da=this.fa=!1}function oe(e){e.da=!0,e.listener=null,e.proxy=null,e.src=null,e.ha=null}function le(e){this.src=e,this.g={},this.h=0}function ue(e,t){var r,n,i,s=t.type;s in e.g&&(r=e.g[s],(i=0<=(n=Array.prototype.indexOf.call(r,t,void 0)))&&Array.prototype.splice.call(r,n,1),i)&&(oe(t),0==e.g[s].length)&&(delete e.g[s],e.h--)}function he(e,t,r,n){for(var i=0;i<e.length;++i){var s=e[i];if(!s.da&&s.listener==t&&s.capture==!!r&&s.ha==n)return i}return-1}le.prototype.add=function(e,t,r,n,i){var s=e.toString(),a=((e=this.g[s])||(e=this.g[s]=[],this.h++),he(e,t,n,i));return-1<a?(t=e[a],r||(t.fa=!1)):((t=new ae(t,this.src,s,!!n,i)).fa=r,e.push(t)),t};var ce="closure_lm_"+(1e6*Math.random()|0),de={};function fe(e,t,r,n,i){if(n&&n.once)return function e(t,r,n,i,s){if(Array.isArray(r)){for(var a=0;a<r.length;a++)e(t,r[a],n,i,s);return null}n=_e(n);return t&&t[f]?t.L(r,n,u(i)?!!i.capture:!!i,s):ge(t,r,n,!0,i,s)}(e,t,r,n,i);if(Array.isArray(t)){for(var s=0;s<t.length;s++)fe(e,t[s],r,n,i);return null}return r=_e(r),e&&e[f]?e.K(t,r,u(n)?!!n.capture:!!n,i):ge(e,t,r,!1,n,i)}function ge(e,t,r,n,i,s){if(!t)throw Error("Invalid event type");var a=u(i)?!!i.capture:!!i,o=ve(e);if(o||(e[ce]=o=new le(e)),!(r=o.add(t,r,n,a,s)).proxy)if(n=(()=>{let r=ye;return function e(t){return r.call(e.src,e.listener,t)}})(),(r.proxy=n).src=e,n.listener=r,e.addEventListener)void 0===(i=ne?i:a)&&(i=!1),e.addEventListener(t.toString(),n,i);else if(e.attachEvent)e.attachEvent(pe(t.toString()),n);else{if(!e.addListener||!e.removeListener)throw Error("addEventListener and attachEvent are unavailable.");e.addListener(n)}return r}function me(e){var t,r,n;"number"!=typeof e&&e&&!e.da&&((t=e.src)&&t[f]?ue(t.i,e):(r=e.type,n=e.proxy,t.removeEventListener?t.removeEventListener(r,n,e.capture):t.detachEvent?t.detachEvent(pe(r),n):t.addListener&&t.removeListener&&t.removeListener(n),(r=ve(t))?(ue(r,e),0==r.h&&(r.src=null,t[ce]=null)):oe(e)))}function pe(e){return e in de?de[e]:de[e]="on"+e}function ye(e,t){var r,n;return e=!!e.da||(t=new d(t,this),r=e.listener,n=e.ha||e.src,e.fa&&me(e),r.call(n,t))}function ve(e){return(e=e[ce])instanceof le?e:null}var we="__closure_events_fn_"+(1e9*Math.random()>>>0);function _e(t){return"function"==typeof t?t:(t[we]||(t[we]=function(e){return t.handleEvent(e)}),t[we])}function g(){h.call(this),this.i=new le(this),(this.M=this).F=null}function m(e,t){var r,n=e.F;if(n)for(r=[];n;n=n.F)r.push(n);if(e=e.M,n=t.type||t,"string"==typeof t?t=new c(t,e):t instanceof c?t.target=t.target||e:(a=t,X(t=new c(n,e),a)),a=!0,r)for(var i=r.length-1;0<=i;i--)var s=t.g=r[i],a=be(s,n,!0,t)&&a;if(a=be(s=t.g=e,n,!0,t)&&a,a=be(s,n,!1,t)&&a,r)for(i=0;i<r.length;i++)a=be(s=t.g=r[i],n,!1,t)&&a}function be(e,t,r,n){if(!(t=e.i.g[String(t)]))return!0;t=t.concat();for(var i=!0,s=0;s<t.length;++s){var a,o,l=t[s];l&&!l.da&&l.capture==r&&(a=l.listener,o=l.ha||l.src,l.fa&&ue(e.i,l),i=!1!==a.call(o,n)&&i)}return i&&!n.defaultPrevented}function Ie(e,t,r){if("function"==typeof e)r&&(e=p(e,r));else{if(!e||"function"!=typeof e.handleEvent)throw Error("Invalid listener argument");e=p(e.handleEvent,e)}return 2147483647<Number(t)?-1:R.setTimeout(e,t||0)}s(g,h),g.prototype[f]=!0,g.prototype.removeEventListener=function(e,t,r,n){!function e(t,r,n,i,s){if(Array.isArray(r))for(var a=0;a<r.length;a++)e(t,r[a],n,i,s);else i=u(i)?!!i.capture:!!i,n=_e(n),t&&t[f]?(t=t.i,(r=String(r).toString())in t.g&&-1<(n=he(a=t.g[r],n,i,s))&&(oe(a[n]),Array.prototype.splice.call(a,n,1),0==a.length)&&(delete t.g[r],t.h--)):(t=t&&ve(t))&&(r=t.g[r.toString()],n=(t=-1)<(t=r?he(r,n,i,s):t)?r[t]:null)&&me(n)}(this,e,t,r,n)},g.prototype.N=function(){if(g.aa.N.call(this),this.i){var e,t=this.i;for(e in t.g){for(var r=t.g[e],n=0;n<r.length;n++)oe(r[n]);delete t.g[e],t.h--}}this.F=null},g.prototype.K=function(e,t,r,n){return this.i.add(String(e),t,!1,r,n)},g.prototype.L=function(e,t,r,n){return this.i.add(String(e),t,!0,r,n)};class Te extends h{constructor(e,t){super(),this.m=e,this.l=t,this.h=null,this.i=!1,this.g=null}j(e){this.h=arguments,this.g?this.i=!0:function e(t){t.g=Ie(()=>{t.g=null,t.i&&(t.i=!1,e(t))},t.l);var r=t.h;t.h=null,t.m.apply(null,r)}(this)}N(){super.N(),this.g&&(R.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function y(e){h.call(this),this.h=e,this.g={}}s(y,h);var Ee=[];function Se(e){H(e.g,function(e,t){this.g.hasOwnProperty(t)&&me(e)},e),e.g={}}y.prototype.N=function(){y.aa.N.call(this),Se(this)},y.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var xe=R.JSON.stringify,Ce=R.JSON.parse,Ae=class{stringify(e){return R.JSON.stringify(e,void 0)}parse(e){return R.JSON.parse(e,void 0)}};function De(){}function Ne(e){return e.h||(e.h=e.i())}function ke(){}De.prototype.h=null;var Re={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function Oe(){c.call(this,"d")}function Me(){c.call(this,"c")}s(Oe,c),s(Me,c);var v={},Le=null;function Ve(){return Le=Le||new g}function Pe(e){c.call(this,v.La,e)}function Fe(){var e=Ve();m(e,new Pe(e))}function Be(e,t){c.call(this,v.STAT_EVENT,e),this.stat=t}function M(e){var t=Ve();m(t,new Be(t,e))}function Ue(e,t){c.call(this,v.Ma,e),this.size=t}function qe(e,t){if("function"!=typeof e)throw Error("Fn must not be null and must be a function");return R.setTimeout(function(){e()},t)}function je(){this.g=!0}function L(e,t,r,n){e.info(function(){return"XMLHTTP TEXT ("+t+"): "+((e,t)=>{if(!e.g)return t;if(!t)return null;try{var r=JSON.parse(t);if(r)for(e=0;e<r.length;e++)if(Array.isArray(r[e])){var n=r[e];if(!(n.length<2)){var i=n[1];if(Array.isArray(i)&&!(i.length<1)){var s=i[0];if("noop"!=s&&"stop"!=s&&"close"!=s)for(var a=1;a<i.length;a++)i[a]=""}}}return xe(r)}catch(e){return t}})(e,r)+(n?" "+n:"")})}v.La="serverreachability",s(Pe,c),v.STAT_EVENT="statevent",s(Be,c),v.Ma="timingevent",s(Ue,c),je.prototype.xa=function(){this.g=!1},je.prototype.info=function(){};var ze={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},Ke={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function Ge(){}function w(e,t,r,n){this.j=e,this.i=t,this.l=r,this.R=n||1,this.U=new y(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new $e}function $e(){this.i=null,this.g="",this.h=!1}s(Ge,De),Ge.prototype.g=function(){return new XMLHttpRequest},Ge.prototype.i=function(){return{}};var Qe=new Ge,He={},We={};function Ye(e,t,r){e.L=1,e.v=yt(b(t)),e.m=r,e.P=!0,Xe(e,null)}function Xe(e,t){e.F=Date.now(),Ze(e),e.A=b(e.v);var r=e.A,n=e.R,i=(Array.isArray(n)||(n=[String(n)]),Nt(r.i,"t",n),e.C=0,r=e.j.J,e.h=new $e,e.g=cr(e.j,r?t:null,!e.m),0<e.O&&(e.M=new Te(p(e.Y,e,e.g),e.O)),t=e.U,r=e.g,n=e.ca,"readystatechange");Array.isArray(i)||(i&&(Ee[0]=i.toString()),i=Ee);for(var a,o,l,u,h,c,s=0;s<i.length;s++){var d=fe(r,i[s],n||t.handleEvent,!1,t.h||t);if(!d)break;t.g[d.key]=d}t=e.H?W(e.H):{},e.m?(e.u||(e.u="POST"),t["Content-Type"]="application/x-www-form-urlencoded",e.g.ea(e.A,e.u,e.m,t)):(e.u="GET",e.g.ea(e.A,e.u,null,t)),Fe(),a=e.i,o=e.u,l=e.A,u=e.l,h=e.R,c=e.m,a.info(function(){if(a.g)if(c)for(var e="",t=c.split("&"),r=0;r<t.length;r++){var n,i,s=t[r].split("=");1<s.length&&(n=s[0],s=s[1],e=2<=(i=n.split("_")).length&&"type"==i[1]?e+(n+"=")+s+"&":e+(n+"=redacted&"))}else e=null;else e=c;return"XMLHTTP REQ ("+u+") [attempt "+h+"]: "+o+"\n"+l+"\n"+e})}function Je(e){return e.g&&"GET"==e.u&&2!=e.L&&e.j.Ca}function Ze(e){e.S=Date.now()+e.I,et(e,e.I)}function et(e,t){if(null!=e.B)throw Error("WatchDog timer not null");e.B=qe(p(e.ba,e),t)}function tt(e){e.B&&(R.clearTimeout(e.B),e.B=null)}function rt(e){0==e.j.G||e.J||or(e.j,e)}function V(e){tt(e);var t=e.M;t&&"function"==typeof t.ma&&t.ma(),e.M=null,Se(e.U),e.g&&(t=e.g,e.g=null,t.abort(),t.ma())}function nt(e,t){try{var r=e.j;if(0!=r.G&&(r.g==e||lt(r.h,e)))if(!e.K&&lt(r.h,e)&&3==r.G){try{var n=r.Da.g.parse(t)}catch(e){n=null}if(Array.isArray(n)&&3==n.length){var i=n;if(0==i[0]){e:if(!r.u){if(r.g){if(!(r.g.F+3e3<e.F))break e;ar(r),Yt(r)}nr(r),M(18)}}else r.za=i[1],0<r.za-r.T&&i[2]<37500&&r.F&&0==r.v&&!r.C&&(r.C=qe(p(r.Za,r),6e3));if(ot(r.h)<=1&&r.ca){try{r.ca()}catch(e){}r.ca=void 0}}else C(r,11)}else if(!e.K&&r.g!=e||ar(r),!O(t))for(i=r.Da.g.parse(t),t=0;t<i.length;t++){var s,a,o,l,u,h,c,d,f,g,m=i[t];r.T=m[0],m=m[1],2==r.G?"c"==m[0]?(r.K=m[1],r.ia=m[2],null!=(s=m[3])&&(r.la=s,r.j.info("VER="+r.la)),null!=(a=m[4])&&(r.Aa=a,r.j.info("SVER="+r.Aa)),null!=(o=m[5])&&"number"==typeof o&&0<o&&(n=1.5*o,r.L=n,r.j.info("backChannelRequestTimeoutMs_="+n)),n=r,(l=e.g)&&(!(u=l.g?l.g.getResponseHeader("X-Client-Wire-Protocol"):null)||(h=n.h).g||-1==u.indexOf("spdy")&&-1==u.indexOf("quic")&&-1==u.indexOf("h2")||(h.j=h.l,h.g=new Set,h.h&&(ut(h,h.h),h.h=null)),n.D)&&(c=l.g?l.g.getResponseHeader("X-HTTP-Session-Id"):null)&&(n.ya=c,I(n.I,n.D,c)),r.G=3,r.l&&r.l.ua(),r.ba&&(r.R=Date.now()-e.F,r.j.info("Handshake RTT: "+r.R+"ms")),d=e,(n=r).qa=hr(n,n.J?n.ia:null,n.W),d.K?(ht(n.h,d),f=d,(g=n.L)&&(f.I=g),f.B&&(tt(f),Ze(f)),n.g=d):rr(n),0<r.i.length&&Jt(r)):"stop"!=m[0]&&"close"!=m[0]||C(r,7):3==r.G&&("stop"==m[0]||"close"==m[0]?"stop"==m[0]?C(r,7):Wt(r):"noop"!=m[0]&&r.l&&r.l.ta(m),r.v=0)}Fe()}catch(e){}}w.prototype.ca=function(e){e=e.target;var t=this.M;t&&3==P(e)?t.j():this.Y(e)},w.prototype.Y=function(e){try{if(e==this.g)e:{var t=P(this.g),r=this.g.Ba();this.g.Z();if(!(t<3)&&(3!=t||this.g&&(this.h.h||this.g.oa()||$t(this.g)))){this.J||4!=t||7==r||Fe(),tt(this);var n=this.g.Z();this.X=n;t:if(Je(this)){var i=$t(this.g),s=(e="",i.length),a=4==P(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){V(this),rt(this);var o="";break t}this.h.i=new R.TextDecoder}for(r=0;r<s;r++)this.h.h=!0,e+=this.h.i.decode(i[r],{stream:!(a&&r==s-1)});i.length=0,this.h.g+=e,this.C=0,o=this.h.g}else o=this.g.oa();if(this.o=200==n,S=this.i,x=this.u,C=this.A,A=this.l,D=this.R,N=t,k=n,S.info(function(){return"XMLHTTP RESP ("+A+") [ attempt "+D+"]: "+x+"\n"+C+"\n"+N+" "+k}),this.o){if(this.T&&!this.K){t:{if(this.g){var l,u=this.g;if((l=u.g?u.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!O(l)){var h=l;break t}}h=null}if(!(n=h)){this.o=!1,this.s=3,M(12),V(this),rt(this);break e}L(this.i,this.l,n,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,nt(this,n)}if(this.P){for(var c,d,n=!0;!this.J&&this.C<o.length;){if(I=o,E=T=void 0,T=(b=this).C,(c=-1==(E=I.indexOf("\n",T))?We:(T=Number(I.substring(T,E)),isNaN(T)?He:(E+=1)+T>I.length?We:(I=I.slice(E,E+T),b.C=E+T,I)))==We){4==t&&(this.s=4,M(14),n=!1),L(this.i,this.l,null,"[Incomplete Response]");break}if(c==He){this.s=4,M(15),L(this.i,this.l,o,"[Invalid Chunk]"),n=!1;break}L(this.i,this.l,c,null),nt(this,c)}Je(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=t||0!=o.length||this.h.h||(this.s=1,M(16),n=!1),this.o=this.o&&n,n?0<o.length&&!this.W&&(this.W=!0,(d=this.j).g==this)&&d.ba&&!d.M&&(d.j.info("Great, no buffering proxy detected. Bytes received: "+o.length),ir(d),d.M=!0,M(11)):(L(this.i,this.l,o,"[Invalid Chunked Response]"),V(this),rt(this))}else L(this.i,this.l,o,null),nt(this,o);4==t&&V(this),this.o&&!this.J&&(4==t?or(this.j,this):(this.o=!1,Ze(this)))}else{{var f=this.g;var g,m,p,y={};f=(f.g&&2<=P(f)&&f.g.getAllResponseHeaders()||"").split("\r\n");for(let e=0;e<f.length;e++)O(f[e])||(g=(e=>{for(var t=1,r=(e=e.split(":"),[]);0<t&&e.length;)r.push(e.shift()),t--;return e.length&&r.push(e.join(":")),r})(f[e]),m=g[0],"string"==typeof(g=g[1])&&(g=g.trim(),p=y[m]||[],(y[m]=p).push(g)));var v,w=y,_=function(e){return e.join(", ")};for(v in w)_.call(void 0,w[v],v,w)}400==n&&0<o.indexOf("Unknown SID")?(this.s=3,M(12)):(this.s=0,M(13)),V(this),rt(this)}}}}catch(e){}var b,I,T,E,S,x,C,A,D,N,k},w.prototype.cancel=function(){this.J=!0,V(this)},w.prototype.ba=function(){this.B=null;var e,t,r=Date.now();0<=r-this.S?(e=this.i,t=this.A,e.info(function(){return"TIMEOUT: "+t}),2!=this.L&&(Fe(),M(17)),V(this),this.s=2,rt(this)):et(this,this.S-r)};var it=class{constructor(e,t){this.g=e,this.map=t}};function st(e){this.l=e||10,e=R.PerformanceNavigationTiming?0<(e=R.performance.getEntriesByType("navigation")).length&&("hq"==e[0].nextHopProtocol||"h2"==e[0].nextHopProtocol):!!(R.chrome&&R.chrome.loadTimes&&R.chrome.loadTimes()&&R.chrome.loadTimes().wasFetchedViaSpdy),this.j=e?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function at(e){return e.h||e.g&&e.g.size>=e.j}function ot(e){return e.h?1:e.g?e.g.size:0}function lt(e,t){return e.h?e.h==t:e.g&&e.g.has(t)}function ut(e,t){e.g?e.g.add(t):e.h=t}function ht(e,t){e.h&&e.h==t?e.h=null:e.g&&e.g.has(t)&&e.g.delete(t)}function ct(t){if(null!=t.h)return t.i.concat(t.h.D);if(null==t.g||0===t.g.size)return K(t.i);{let e=t.i;for(var r of t.g.values())e=e.concat(r.D);return e}}function dt(e,t){if(e.forEach&&"function"==typeof e.forEach)e.forEach(t,void 0);else if(U(e)||"string"==typeof e)Array.prototype.forEach.call(e,t,void 0);else for(var r=(e=>{if(e.na&&"function"==typeof e.na)return e.na();if(!e.V||"function"!=typeof e.V){if("undefined"!=typeof Map&&e instanceof Map)return Array.from(e.keys());if(!("undefined"!=typeof Set&&e instanceof Set)){if(U(e)||"string"==typeof e){var t=[];e=e.length;for(var r=0;r<e;r++)t.push(r)}else for(var n in t=[],r=0,e)t[r++]=n;return t}}})(e),n=(e=>{if(e.V&&"function"==typeof e.V)return e.V();if("undefined"!=typeof Map&&e instanceof Map||"undefined"!=typeof Set&&e instanceof Set)return Array.from(e.values());if("string"==typeof e)return e.split("");if(U(e))for(var t=[],r=e.length,n=0;n<r;n++)t.push(e[n]);else for(n in t=[],r=0,e)t[r++]=e[n];return t})(e),i=n.length,s=0;s<i;s++)t.call(void 0,n[s],r&&r[s],e)}st.prototype.cancel=function(){if(this.i=ct(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(var e of this.g.values())e.cancel();this.g.clear()}};var ft=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function _(e){var t,r;this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,e instanceof _?(this.h=e.h,gt(this,e.j),this.o=e.o,this.g=e.g,mt(this,e.s),this.l=e.l,t=e.i,(r=new Ct).i=t.i,t.g&&(r.g=new Map(t.g),r.h=t.h),pt(this,r),this.m=e.m):e&&(t=String(e).match(ft))?(this.h=!1,gt(this,t[1]||"",!0),this.o=vt(t[2]||""),this.g=vt(t[3]||"",!0),mt(this,t[4]),this.l=vt(t[5]||"",!0),pt(this,t[6]||"",!0),this.m=vt(t[7]||"")):(this.h=!1,this.i=new Ct(null,this.h))}function b(e){return new _(e)}function gt(e,t,r){e.j=r?vt(t,!0):t,e.j&&(e.j=e.j.replace(/:$/,""))}function mt(e,t){if(t){if(t=Number(t),isNaN(t)||t<0)throw Error("Bad port number "+t);e.s=t}else e.s=null}function pt(e,t,r){var n,i;t instanceof Ct?(e.i=t,n=e.i,(i=e.h)&&!n.j&&(T(n),n.i=null,n.g.forEach(function(e,t){var r=t.toLowerCase();t!=r&&(At(this,t),Nt(this,r,e))},n)),n.j=i):(r||(t=wt(t,St)),e.i=new Ct(t,e.h))}function I(e,t,r){e.i.set(t,r)}function yt(e){return I(e,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),e}function vt(e,t){return e?t?decodeURI(e.replace(/%25/g,"%2525")):decodeURIComponent(e):""}function wt(e,t,r){return"string"==typeof e?(e=encodeURI(e).replace(t,_t),e=r?e.replace(/%25([0-9a-fA-F]{2})/g,"%$1"):e):null}function _t(e){return"%"+((e=e.charCodeAt(0))>>4&15).toString(16)+(15&e).toString(16)}_.prototype.toString=function(){var e=[],t=this.j,r=(t&&e.push(wt(t,It,!0),":"),this.g);return!r&&"file"!=t||(e.push("//"),(t=this.o)&&e.push(wt(t,It,!0),"@"),e.push(encodeURIComponent(String(r)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null==(r=this.s))||e.push(":",String(r)),(r=this.l)&&(this.g&&"/"!=r.charAt(0)&&e.push("/"),e.push(wt(r,"/"==r.charAt(0)?Et:Tt,!0))),(r=this.i.toString())&&e.push("?",r),(r=this.m)&&e.push("#",wt(r,xt)),e.join("")};var bt,It=/[#\/\?@]/g,Tt=/[#\?:]/g,Et=/[#\?]/g,St=/[#\?@]/g,xt=/#/g;function Ct(e,t){this.h=this.g=null,this.i=e||null,this.j=!!t}function T(r){if(!r.g&&(r.g=new Map,r.h=0,r.i)){var e=r.i,t=function(e,t){r.add(decodeURIComponent(e.replace(/\+/g," ")),t)};if(e){e=e.split("&");for(var n=0;n<e.length;n++){var i,s=e[n].indexOf("="),a=null;0<=s?(i=e[n].substring(0,s),a=e[n].substring(s+1)):i=e[n],t(i,a?decodeURIComponent(a.replace(/\+/g," ")):"")}}}}function At(e,t){T(e),t=E(e,t),e.g.has(t)&&(e.i=null,e.h-=e.g.get(t).length,e.g.delete(t))}function Dt(e,t){return T(e),t=E(e,t),e.g.has(t)}function Nt(e,t,r){At(e,t),0<r.length&&(e.i=null,e.g.set(E(e,t),K(r)),e.h+=r.length)}function E(e,t){return t=String(t),t=e.j?t.toLowerCase():t}function S(e,t,r,n,i){try{i&&(i.onload=null,i.onerror=null,i.onabort=null,i.ontimeout=null),n(r)}catch(e){}}function kt(){this.g=new Ae}function Rt(e){this.l=e.Ub||null,this.j=e.eb||!1}function Ot(e,t){g.call(this),this.D=e,this.o=t,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function Mt(e){e.j.read().then(e.Pa.bind(e)).catch(e.ga.bind(e))}function Lt(e){e.readyState=4,e.l=null,e.j=null,e.v=null,Vt(e)}function Vt(e){e.onreadystatechange&&e.onreadystatechange.call(e)}function Pt(e){let r="";return H(e,function(e,t){r=(r=r+t+":")+e+"\r\n"}),r}function Ft(e,t,r){e:{for(n in r){var n=!1;break e}n=!0}n||(r=Pt(r),"string"==typeof e?null!=r&&encodeURIComponent(String(r)):I(e,t,r))}function x(e){g.call(this),this.headers=new Map,this.o=e||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(e=Ct.prototype).add=function(e,t){T(this),this.i=null,e=E(this,e);var r=this.g.get(e);return r||this.g.set(e,r=[]),r.push(t),this.h+=1,this},e.forEach=function(r,n){T(this),this.g.forEach(function(e,t){e.forEach(function(e){r.call(n,e,t,this)},this)},this)},e.na=function(){T(this);var t=Array.from(this.g.values()),r=Array.from(this.g.keys()),n=[];for(let s=0;s<r.length;s++){var i=t[s];for(let e=0;e<i.length;e++)n.push(r[s])}return n},e.V=function(t){T(this);let r=[];if("string"==typeof t)Dt(this,t)&&(r=r.concat(this.g.get(E(this,t))));else{t=Array.from(this.g.values());for(let e=0;e<t.length;e++)r=r.concat(t[e])}return r},e.set=function(e,t){return T(this),this.i=null,Dt(this,e=E(this,e))&&(this.h-=this.g.get(e).length),this.g.set(e,[t]),this.h+=1,this},e.get=function(e,t){return e&&0<(e=this.V(e)).length?String(e[0]):t},e.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var e=[],t=Array.from(this.g.keys()),r=0;r<t.length;r++)for(var n=t[r],i=encodeURIComponent(String(n)),s=this.V(n),n=0;n<s.length;n++){var a=i;""!==s[n]&&(a+="="+encodeURIComponent(String(s[n]))),e.push(a)}return this.i=e.join("&")},s(Rt,De),Rt.prototype.g=function(){return new Ot(this.l,this.j)},Rt.prototype.i=(bt={},function(){return bt}),s(Ot,g),(e=Ot.prototype).open=function(e,t){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=e,this.A=t,this.readyState=1,Vt(this)},e.send=function(e){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;var t={headers:this.u,method:this.B,credentials:this.m,cache:void 0};e&&(t.body=e),(this.D||R).fetch(new Request(this.A,t)).then(this.Sa.bind(this),this.ga.bind(this))},e.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Lt(this)),this.readyState=0},e.Sa=function(e){if(this.g&&(this.l=e,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=e.headers,this.readyState=2,Vt(this)),this.g)&&(this.readyState=3,Vt(this),this.g))if("arraybuffer"===this.responseType)e.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==R.ReadableStream&&"body"in e){if(this.j=e.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Mt(this)}else e.text().then(this.Ra.bind(this),this.ga.bind(this))},e.Pa=function(e){var t;this.g&&(this.o&&e.value?this.response.push(e.value):!this.o&&(t=e.value||new Uint8Array(0),t=this.v.decode(t,{stream:!e.done}))&&(this.response=this.responseText+=t),(e.done?Lt:Vt)(this),3==this.readyState)&&Mt(this)},e.Ra=function(e){this.g&&(this.response=this.responseText=e,Lt(this))},e.Qa=function(e){this.g&&(this.response=e,Lt(this))},e.ga=function(){this.g&&Lt(this)},e.setRequestHeader=function(e,t){this.u.append(e,t)},e.getResponseHeader=function(e){return this.h&&this.h.get(e.toLowerCase())||""},e.getAllResponseHeaders=function(){if(!this.h)return"";for(var e=[],t=this.h.entries(),r=t.next();!r.done;)r=r.value,e.push(r[0]+": "+r[1]),r=t.next();return e.join("\r\n")},Object.defineProperty(Ot.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(e){this.m=e?"include":"same-origin"}}),s(x,g);var Bt=/^https?$/i,Ut=["POST","PUT"];function qt(e,t){e.h=!1,e.g&&(e.j=!0,e.g.abort(),e.j=!1),e.l=t,e.m=5,jt(e),Kt(e)}function jt(e){e.A||(e.A=!0,m(e,"complete"),m(e,"error"))}function zt(e){if(e.h&&void 0!==B&&(!e.v[1]||4!=P(e)||2!=e.Z()))if(e.u&&4==P(e))Ie(e.Ea,0,e);else if(m(e,"readystatechange"),4==P(e)){e.h=!1;try{var t,r,n,i=e.Z();switch(i){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var s=!0;break;default:s=!1}if((t=s)||((r=0===i)&&(!(n=String(e.D).match(ft)[1]||null)&&R.self&&R.self.location&&(n=R.self.location.protocol.slice(0,-1)),r=!Bt.test(n?n.toLowerCase():"")),t=r),t)m(e,"complete"),m(e,"success");else{e.m=6;try{var a=2<P(e)?e.g.statusText:""}catch(e){a=""}e.l=a+" ["+e.Z()+"]",jt(e)}}finally{Kt(e)}}}function Kt(e,t){if(e.g){Gt(e);var r=e.g,n=e.v[0]?()=>{}:null;e.g=null,e.v=null,t||m(e,"ready");try{r.onreadystatechange=n}catch(e){}}}function Gt(e){e.I&&(R.clearTimeout(e.I),e.I=null)}function P(e){return e.g?e.g.readyState:0}function $t(e){try{if(e.g){if("response"in e.g)return e.g.response;switch(e.H){case"":case"text":return e.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in e.g)return e.g.mozResponseArrayBuffer}}return null}catch(e){return null}}function Qt(e,t,r){return r&&r.internalChannelParams&&r.internalChannelParams[e]||t}function Ht(e){this.Aa=0,this.i=[],this.j=new je,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=Qt("failFast",!1,e),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=Qt("baseRetryDelayMs",5e3,e),this.cb=Qt("retryDelaySeedMs",1e4,e),this.Wa=Qt("forwardChannelMaxRetries",2,e),this.wa=Qt("forwardChannelRequestTimeoutMs",2e4,e),this.pa=e&&e.xmlHttpFactory||void 0,this.Xa=e&&e.Tb||void 0,this.Ca=e&&e.useFetchStreams||!1,this.L=void 0,this.J=e&&e.supportsCrossDomainXhr||!1,this.K="",this.h=new st(e&&e.concurrentRequestLimit),this.Da=new kt,this.P=e&&e.fastHandshake||!1,this.O=e&&e.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=e&&e.Rb||!1,e&&e.xa&&this.j.xa(),e&&e.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&e&&e.detectBufferingProxy||!1,this.ja=void 0,e&&e.longPollingTimeout&&0<e.longPollingTimeout&&(this.ja=e.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function Wt(e){if(Xt(e),3==e.G){var t=e.U++,r=b(e.I);if(I(r,"SID",e.K),I(r,"RID",t),I(r,"TYPE","terminate"),er(e,r),(t=new w(e,e.j,t)).L=2,t.v=yt(b(r)),r=!1,R.navigator&&R.navigator.sendBeacon)try{r=R.navigator.sendBeacon(t.v.toString(),"")}catch(e){}!r&&R.Image&&((new Image).src=t.v,r=!0),r||(t.g=cr(t.j,null),t.g.ea(t.v)),t.F=Date.now(),Ze(t)}ur(e)}function Yt(e){e.g&&(ir(e),e.g.cancel(),e.g=null)}function Xt(e){Yt(e),e.u&&(R.clearTimeout(e.u),e.u=null),ar(e),e.h.cancel(),e.s&&("number"==typeof e.s&&R.clearTimeout(e.s),e.s=null)}function Jt(e){var t;at(e.h)||e.s||(e.s=!0,t=e.Ga,o||te(),l||(o(),l=!0),ee.add(t,e),e.B=0)}function Zt(e,t){var r=t?t.l:e.U++,n=b(e.I);I(n,"SID",e.K),I(n,"RID",r),I(n,"AID",e.T),er(e,n),e.m&&e.o&&Ft(n,e.m,e.o),r=new w(e,e.j,r,e.B+1),null===e.m&&(r.H=e.o),t&&(e.i=t.D.concat(e.i)),t=tr(e,r,1e3),r.I=Math.round(.5*e.wa)+Math.round(.5*e.wa*Math.random()),ut(e.h,r),Ye(r,n,t)}function er(e,r){e.H&&H(e.H,function(e,t){I(r,t,e)}),e.l&&dt({},function(e,t){I(r,t,e)})}function tr(e,t,i){i=Math.min(e.i.length,i);var s=e.l?p(e.l.Na,e.l,e):null;e:{let r=e.i,n=-1;for(;;){var a=["count="+i];-1==n?0<i?(n=r[0].g,a.push("ofs="+n)):n=0:a.push("ofs="+n);let e=!0;for(let t=0;t<i;t++){var o=r[t].g,l=r[t].map;if((o-=n)<0)n=Math.max(0,r[t].g-100),e=!1;else try{((e,n,t)=>{let i=t||"";try{dt(e,function(e,t){let r=e;u(e)&&(r=xe(e)),n.push(i+t+"="+encodeURIComponent(r))})}catch(e){throw n.push(i+"type="+encodeURIComponent("_badmap")),e}})(l,a,"req"+o+"_")}catch(e){s&&s(l)}}if(e){s=a.join("&");break e}}}return e=e.i.splice(0,i),t.D=e,s}function rr(e){var t;e.g||e.u||(e.Y=1,t=e.Fa,o||te(),l||(o(),l=!0),ee.add(t,e),e.v=0)}function nr(e){return!(e.g||e.u||3<=e.v)&&(e.Y++,e.u=qe(p(e.Fa,e),lr(e,e.v)),e.v++,1)}function ir(e){null!=e.A&&(R.clearTimeout(e.A),e.A=null)}function sr(e){e.g=new w(e,e.j,"rpc",e.Y),null===e.m&&(e.g.H=e.o),e.g.O=0;var t=b(e.qa),r=(I(t,"RID","rpc"),I(t,"SID",e.K),I(t,"AID",e.T),I(t,"CI",e.F?"0":"1"),!e.F&&e.ja&&I(t,"TO",e.ja),I(t,"TYPE","xmlhttp"),er(e,t),e.m&&e.o&&Ft(t,e.m,e.o),e.L&&(e.g.I=e.L),e.g);e=e.ia,r.L=1,r.v=yt(b(t)),r.m=null,r.P=!0,Xe(r,e)}function ar(e){null!=e.C&&(R.clearTimeout(e.C),e.C=null)}function or(e,t){var r,n,i,s=null;if(e.g==t){ar(e),ir(e),e.g=null;var a=2}else{if(!lt(e.h,t))return;s=t.D,ht(e.h,t),a=1}if(0!=e.G)if(t.o)(1==a?(s=t.m?t.m.length:0,t=Date.now()-t.F,r=e.B,m(a=Ve(),new Ue(a,s)),Jt):rr)(e);else if(3==(r=t.s)||0==r&&0<t.X||(1!=a||(i=t,ot((n=e).h)>=n.h.j-(n.s?1:0))||(n.s?(n.i=i.D.concat(n.i),0):1==n.G||2==n.G||n.B>=(n.Va?0:n.Wa)||(n.s=qe(p(n.Ga,n,i),lr(n,n.B)),n.B++,0)))&&(2!=a||!nr(e)))switch(s&&0<s.length&&(t=e.h,t.i=t.i.concat(s)),r){case 1:C(e,5);break;case 4:C(e,10);break;case 3:C(e,6);break;default:C(e,2)}}function lr(e,t){let r=e.Ta+Math.floor(Math.random()*e.cb);return e.isActive()||(r*=2),r*t}function C(e,t){var r,n,i;e.j.info("Error code "+t),2==t?(r=p(e.fb,e),n=!(i=e.Xa),i=new _(i||"//www.google.com/images/cleardot.gif"),R.location&&"http"==R.location.protocol||gt(i,"https"),yt(i),(n?(t,r)=>{var n=new je;if(R.Image){let e=new Image;e.onload=z(S,n,"TestLoadImage: loaded",!0,r,e),e.onerror=z(S,n,"TestLoadImage: error",!1,r,e),e.onabort=z(S,n,"TestLoadImage: abort",!1,r,e),e.ontimeout=z(S,n,"TestLoadImage: timeout",!1,r,e),R.setTimeout(function(){e.ontimeout&&e.ontimeout()},1e4),e.src=t}else r(!1)}:(e,t)=>{let r=new je,n=new AbortController,i=setTimeout(()=>{n.abort(),S(r,0,!1,t)},1e4);fetch(e,{signal:n.signal}).then(e=>{clearTimeout(i),e.ok?S(r,0,!0,t):S(r,0,!1,t)}).catch(()=>{clearTimeout(i),S(r,0,!1,t)})})(i.toString(),r)):M(2),e.G=0,e.l&&e.l.sa(t),ur(e),Xt(e)}function ur(e){var t;e.G=0,e.ka=[],e.l&&(0==(t=ct(e.h)).length&&0==e.i.length||(G(e.ka,t),G(e.ka,e.i),e.h.i.length=0,K(e.i),e.i.length=0),e.l.ra())}function hr(e,t,r){var n,i,s=r instanceof _?b(r):new _(r);return""!=s.g?(t&&(s.g=t+"."+s.g),mt(s,s.s)):(s=(n=R.location).protocol,t=t?t+"."+n.hostname:n.hostname,n=+n.port,i=new _(null),s&&gt(i,s),t&&(i.g=t),n&&mt(i,n),r&&(i.l=r),s=i),r=e.D,t=e.ya,r&&t&&I(s,r,t),I(s,"VER",e.la),er(e,s),s}function cr(e,t,r){if(t&&!e.J)throw Error("Can't create secondary domain capable XhrIo object.");return(t=e.Ca&&!e.pa?new x(new Rt({eb:r})):new x(e.pa)).Ha(e.J),t}function dr(){}function fr(){}function A(e,t){g.call(this),this.g=new Ht(t),this.l=e,this.h=t&&t.messageUrlParams||null,e=t&&t.messageHeaders||null,t&&t.clientProtocolHeaderRequired&&(e?e["X-Client-Protocol"]="webchannel":e={"X-Client-Protocol":"webchannel"}),this.g.o=e,e=t&&t.initMessageHeaders||null,t&&t.messageContentType&&(e?e["X-WebChannel-Content-Type"]=t.messageContentType:e={"X-WebChannel-Content-Type":t.messageContentType}),t&&t.va&&(e?e["X-WebChannel-Client-Profile"]=t.va:e={"X-WebChannel-Client-Profile":t.va}),this.g.S=e,(e=t&&t.Sb)&&!O(e)&&(this.g.m=e),this.v=t&&t.supportsCrossDomainXhr||!1,this.u=t&&t.sendRawJson||!1,(t=t&&t.httpSessionIdParam)&&!O(t)&&(this.g.D=t,null!==(e=this.h))&&t in e&&t in(e=this.h)&&delete e[t],this.j=new D(this)}function gr(e){Oe.call(this),e.__headers__&&(this.headers=e.__headers__,this.statusCode=e.__status__,delete e.__headers__,delete e.__status__);var t=e.__sm__;if(t){e:{for(var r in t){e=r;break e}e=void 0}(this.i=e)&&(e=this.i,t=null!==t&&e in t?t[e]:void 0),this.data=t}else this.data=e}function mr(){Me.call(this),this.status=1}function D(e){this.g=e}(e=x.prototype).Ha=function(e){this.J=e},e.ea=function(e,t,r,n){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+e);t=t?t.toUpperCase():"GET",this.D=e,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=(this.o||Qe).g(),this.v=this.o?Ne(this.o):Ne(Qe),this.g.onreadystatechange=p(this.Ea,this);try{this.B=!0,this.g.open(t,String(e),!0),this.B=!1}catch(e){return void qt(this,e)}if(e=r||"",r=new Map(this.headers),n)if(Object.getPrototypeOf(n)===Object.prototype)for(var i in n)r.set(i,n[i]);else{if("function"!=typeof n.keys||"function"!=typeof n.get)throw Error("Unknown input type for opt_headers: "+String(n));for(var s of n.keys())r.set(s,n.get(s))}n=Array.from(r.keys()).find(e=>"content-type"==e.toLowerCase()),i=R.FormData&&e instanceof R.FormData,0<=Array.prototype.indexOf.call(Ut,t,void 0)&&!n&&!i&&r.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(var[a,o]of r)this.g.setRequestHeader(a,o);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Gt(this),this.u=!0,this.g.send(e),this.u=!1}catch(e){qt(this,e)}},e.abort=function(e){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=e||7,m(this,"complete"),m(this,"abort"),Kt(this))},e.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Kt(this,!0)),x.aa.N.call(this)},e.Ea=function(){this.s||(this.B||this.u||this.j?zt(this):this.bb())},e.bb=function(){zt(this)},e.isActive=function(){return!!this.g},e.Z=function(){try{return 2<P(this)?this.g.status:-1}catch(e){return-1}},e.oa=function(){try{return this.g?this.g.responseText:""}catch(e){return""}},e.Oa=function(e){var t;if(this.g)return t=this.g.responseText,e&&0==t.indexOf(e)&&(t=t.substring(e.length)),Ce(t)},e.Ba=function(){return this.m},e.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(e=Ht.prototype).la=8,e.G=1,e.connect=function(e,t,r,n){M(0),this.W=e,this.H=t||{},r&&void 0!==n&&(this.H.OSID=r,this.H.OAID=n),this.F=this.X,this.I=hr(this,null,this.W),Jt(this)},e.Ga=function(t){if(this.s)if(this.s=null,1==this.G){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;var r=new w(this,this.j,t);let e=this.o;if(this.S&&(e?X(e=W(e),this.S):e=this.S),null!==this.m||this.O||(r.H=e,e=null),this.P)e:{for(var n=0,i=0;i<this.i.length;i++){var s=this.i[i];if("__data__"in s.map&&"string"==typeof(s=s.map.__data__)?s=s.length:s=void 0,void 0===s)break;if(4096<(n+=s)){n=i;break e}if(4096===n||i===this.i.length-1){n=i+1;break e}}n=1e3}else n=1e3;n=tr(this,r,n),I(i=b(this.I),"RID",t),I(i,"CVER",22),this.D&&I(i,"X-HTTP-Session-Id",this.D),er(this,i),e&&(this.O?n="headers="+encodeURIComponent(String(Pt(e)))+"&"+n:this.m&&Ft(i,this.m,e)),ut(this.h,r),this.Ua&&I(i,"TYPE","init"),this.P?(I(i,"$req",n),I(i,"SID","null"),r.T=!0,Ye(r,i,null)):Ye(r,i,n),this.G=2}}else 3==this.G&&(t?Zt(this,t):0==this.i.length||at(this.h)||Zt(this))},e.Fa=function(){var e;this.u=null,sr(this),this.ba&&!(this.M||null==this.g||this.R<=0)&&(e=2*this.R,this.j.info("BP detection timer enabled: "+e),this.A=qe(p(this.ab,this),e))},e.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,M(10),Yt(this),sr(this))},e.Za=function(){null!=this.C&&(this.C=null,Yt(this),nr(this),M(19))},e.fb=function(e){e?(this.j.info("Successfully pinged google.com"),M(2)):(this.j.info("Failed to ping google.com"),M(1))},e.isActive=function(){return!!this.l&&this.l.isActive(this)},(e=dr.prototype).ua=function(){},e.ta=function(){},e.sa=function(){},e.ra=function(){},e.isActive=function(){return!0},e.Na=function(){},fr.prototype.g=function(e,t){return new A(e,t)},s(A,g),A.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},A.prototype.close=function(){Wt(this.g)},A.prototype.o=function(e){var t,r=this.g;"string"==typeof e?((t={}).__data__=e,e=t):this.u&&((t={}).__data__=xe(e),e=t),r.i.push(new it(r.Ya++,e)),3==r.G&&Jt(r)},A.prototype.N=function(){this.g.l=null,delete this.j,Wt(this.g),delete this.g,A.aa.N.call(this)},s(gr,Oe),s(mr,Me),s(D,dr),D.prototype.ua=function(){m(this.g,"a")},D.prototype.ta=function(e){m(this.g,new gr(e))},D.prototype.sa=function(e){m(this.g,new mr)},D.prototype.ra=function(){m(this.g,"b")},fr.prototype.createWebChannel=fr.prototype.g,A.prototype.send=A.prototype.o,A.prototype.open=A.prototype.m,Tr=function(){return new fr},Ir=Ve,br=v,_r={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},ze.NO_ERROR=0,ze.TIMEOUT=8,ze.HTTP_ERROR=6,wr=ze,Ke.COMPLETE="complete",vr=Ke,(ke.EventType=Re).OPEN="a",Re.CLOSE="b",Re.ERROR="c",Re.MESSAGE="d",g.prototype.listen=g.prototype.K,yr=ke,x.prototype.listenOnce=x.prototype.L,x.prototype.getLastError=x.prototype.Ka,x.prototype.getLastErrorCode=x.prototype.Ba,x.prototype.getStatus=x.prototype.Z,x.prototype.getResponseJson=x.prototype.Oa,x.prototype.getResponseText=x.prototype.oa,x.prototype.send=x.prototype.ea,x.prototype.setWithCredentials=x.prototype.Ha,pr=x}).apply(void 0!==Er?Er:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});let ye="@firebase/firestore";class u{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}u.UNAUTHENTICATED=new u(null),u.GOOGLE_CREDENTIALS=new u("google-credentials-uid"),u.FIRST_PARTY=new u("first-party-uid"),u.MOCK_USER=new u("mock-user");let ve="11.9.0",we=new class{constructor(e){this.name=e,this._logLevel=ce,this._logHandler=fe,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in c))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?he[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,c.DEBUG,...e),this._logHandler(this,c.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,c.VERBOSE,...e),this._logHandler(this,c.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,c.INFO,...e),this._logHandler(this,c.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,c.WARN,...e),this._logHandler(this,c.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,c.ERROR,...e),this._logHandler(this,c.ERROR,...e)}}("@firebase/firestore");function _e(){return we.logLevel}function p(e,...t){var r;we.logLevel<=c.DEBUG&&(r=t.map(Ie),we.debug(`Firestore (${ve}): `+e,...r))}function d(e,...t){var r;we.logLevel<=c.ERROR&&(r=t.map(Ie),we.error(`Firestore (${ve}): `+e,...r))}function be(e,...t){var r;we.logLevel<=c.WARN&&(r=t.map(Ie),we.warn(`Firestore (${ve}): `+e,...r))}function Ie(t){if("string"==typeof t)return t;try{return JSON.stringify(t)}catch(e){return t}}function E(e,t,r){let n="Unexpected state";"string"==typeof t?n=t:r=t,Te(e,n,r)}function Te(e,t,r){let n=`FIRESTORE (${ve}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==r)try{n+=" CONTEXT: "+JSON.stringify(r)}catch(e){n+=" CONTEXT: "+r}throw d(n),new Error(n)}function y(e,t,r,n){let i="Unexpected state";"string"==typeof r?i=r:n=r,e||Te(t,i,n)}let b={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class I extends ie{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: `+this.message}}class f{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class Ee{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization","Bearer "+e)}}class Se{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(u.UNAUTHENTICATED))}shutdown(){}}class xe{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class Ce{constructor(e){this.t=e,this.currentUser=u.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(t,r){y(void 0===this.o,42304);let n=this.i,i=e=>this.i!==n?(n=this.i,r(e)):Promise.resolve(),s=new f,a=(this.o=()=>{this.i++,this.currentUser=this.u(),s.resolve(),s=new f,t.enqueueRetryable(()=>i(this.currentUser))},()=>{let e=s;t.enqueueRetryable(async()=>{await e.promise,await i(this.currentUser)})}),o=e=>{p("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),a())};this.t.onInit(e=>o(e)),setTimeout(()=>{var e;this.auth||((e=this.t.getImmediate({optional:!0}))?o(e):(p("FirebaseAuthCredentialsProvider","Auth not yet detected"),s.resolve(),s=new f))},0),a()}getToken(){let t=this.i,e=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(e).then(e=>this.i!==t?(p("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):e?(y("string"==typeof e.accessToken,31837,{l:e}),new Ee(e.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){var e=this.auth&&this.auth.getUid();return y(null===e||"string"==typeof e,2055,{h:e}),new u(e)}}class Ae{constructor(e,t,r){this.P=e,this.T=t,this.I=r,this.type="FirstParty",this.user=u.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);var e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class De{constructor(e,t,r){this.P=e,this.T=t,this.I=r}getToken(){return Promise.resolve(new Ae(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(u.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class Ne{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&0<e.length&&this.headers.set("x-firebase-appcheck",this.value)}}class ke{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,Gd._isFirebaseServerApp(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(t,r){y(void 0===this.o,3512);let n=e=>{null!=e.error&&p("FirebaseAppCheckTokenProvider","Error getting App Check token; using placeholder token instead. Error: "+e.error.message);var t=e.token!==this.m;return this.m=e.token,p("FirebaseAppCheckTokenProvider",`Received ${t?"new":"existing"} token.`),t?r(e.token):Promise.resolve()},i=(this.o=e=>{t.enqueueRetryable(()=>n(e))},e=>{p("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)});this.V.onInit(e=>i(e)),setTimeout(()=>{var e;this.appCheck||((e=this.V.getImmediate({optional:!0}))?i(e):p("FirebaseAppCheckTokenProvider","AppCheck not yet detected"))},0)}getToken(){var e;return this.p?Promise.resolve(new Ne(this.p)):(e=this.forceRefresh,this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?(y("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new Ne(e.token)):null):Promise.resolve(null))}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}function Re(){return new TextEncoder}class Oe{static newId(){var t=62*Math.floor(256/62);let r="";for(;r.length<20;){var n=(t=>{var r="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(t);if(r&&"function"==typeof r.getRandomValues)r.getRandomValues(n);else for(let e=0;e<t;e++)n[e]=Math.floor(256*Math.random());return n})(40);for(let e=0;e<n.length;++e)r.length<20&&n[e]<t&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(n[e]%62))}return r}}function S(e,t){return e<t?-1:t<e?1:0}function Me(e,t){let r=0;for(;r<e.length&&r<t.length;){var n,i=e.codePointAt(r),s=t.codePointAt(r);if(i!==s)return!(i<128&&s<128)&&0!==(n=((e,t)=>{for(let r=0;r<e.length&&r<t.length;++r)if(e[r]!==t[r])return S(e[r],t[r]);return S(e.length,t.length)})((n=Re()).encode(Le(e,r)),n.encode(Le(t,r))))?n:S(i,s);r+=65535<i?2:1}return S(e.length,t.length)}function Le(e,t){return 65535<e.codePointAt(t)?e.substring(t,t+2):e.substring(t,t+1)}function Ve(e,r,n){return e.length===r.length&&e.every((e,t)=>n(e,r[t]))}function Pe(e){return e+"\0"}let Fe=-62135596800;class h{static now(){return h.fromMillis(Date.now())}static fromDate(e){return h.fromMillis(e.getTime())}static fromMillis(e){var t=Math.floor(e/1e3),r=Math.floor(1e6*(e-1e3*t));return new h(t,r)}constructor(e,t){if(this.seconds=e,(this.nanoseconds=t)<0)throw new I(b.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(1e9<=t)throw new I(b.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<Fe)throw new I(b.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e);if(253402300800<=e)throw new I(b.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?S(this.nanoseconds,e.nanoseconds):S(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){var e=this.seconds-Fe;return String(e).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}class g{static fromTimestamp(e){return new g(e)}static min(){return new g(new h(0,0))}static max(){return new g(new h(253402300799,999999999))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}let Be="__name__";class Ue{constructor(e,t,r){void 0===t?t=0:t>e.length&&E(637,{offset:t,range:e.length}),void 0===r?r=e.length-t:r>e.length-t&&E(1746,{length:r,range:e.length-t}),this.segments=e,this.offset=t,this.len=r}get length(){return this.len}isEqual(e){return 0===Ue.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof Ue?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return this.construct(this.segments,this.offset+(e=void 0===e?1:e),this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,r=this.limit();t<r;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(t,r){let e=Math.min(t.length,r.length);for(let n=0;n<e;n++){let e=Ue.compareSegments(t.get(n),r.get(n));if(0!==e)return e}return S(t.length,r.length)}static compareSegments(e,t){var r=Ue.isNumericId(e),n=Ue.isNumericId(t);return r&&!n?-1:!r&&n?1:r&&n?Ue.extractNumericId(e).compare(Ue.extractNumericId(t)):Me(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return ge.fromString(e.substring(4,e.length-2))}}class T extends Ue{construct(e,t,r){return new T(e,t,r)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){var t,r=[];for(t of e){if(0<=t.indexOf("//"))throw new I(b.INVALID_ARGUMENT,`Invalid segment (${t}). Paths must not contain // in them.`);r.push(...t.split("/").filter(e=>0<e.length))}return new T(r)}static emptyPath(){return new T([])}}let qe=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class v extends Ue{construct(e,t,r){return new v(e,t,r)}static isValidIdentifier(e){return qe.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),e=v.isValidIdentifier(e)?e:"`"+e+"`")).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===Be}static keyField(){return new v([Be])}static fromServerFormat(t){let e=[],r="",n=0;var i=()=>{if(0===r.length)throw new I(b.INVALID_ARGUMENT,`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);e.push(r),r=""};let s=!1;for(;n<t.length;){let e=t[n];if("\\"===e){if(n+1===t.length)throw new I(b.INVALID_ARGUMENT,"Path has trailing escape character: "+t);let e=t[n+1];if("\\"!==e&&"."!==e&&"`"!==e)throw new I(b.INVALID_ARGUMENT,"Path has invalid escape sequence: "+t);r+=e,n+=2}else"`"===e?s=!s:"."!==e||s?r+=e:i(),n++}if(i(),s)throw new I(b.INVALID_ARGUMENT,"Unterminated ` in path: "+t);return new v(e)}static emptyPath(){return new v([])}}class x{constructor(e){this.path=e}static fromPath(e){return new x(T.fromString(e))}static fromName(e){return new x(T.fromString(e).popFirst(5))}static empty(){return new x(T.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return 2<=this.path.length&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===T.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return T.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new x(new T(e.slice()))}}let je=-1;class ze{constructor(e,t,r,n){this.indexId=e,this.collectionGroup=t,this.fields=r,this.indexState=n}}function Ke(e){return e.fields.find(e=>2===e.kind)}function Ge(e){return e.fields.filter(e=>2!==e.kind)}ze.UNKNOWN_ID=-1;class $e{constructor(e,t){this.fieldPath=e,this.kind=t}}class Qe{constructor(e,t){this.sequenceNumber=e,this.offset=t}static empty(){return new Qe(0,Ye.min())}}function He(e,t){var r=e.toTimestamp().seconds,n=e.toTimestamp().nanoseconds+1,r=g.fromTimestamp(1e9===n?new h(r+1,0):new h(r,n));return new Ye(r,x.empty(),t)}function We(e){return new Ye(e.readTime,e.key,je)}class Ye{constructor(e,t,r){this.readTime=e,this.documentKey=t,this.largestBatchId=r}static min(){return new Ye(g.min(),x.empty(),je)}static max(){return new Ye(g.max(),x.empty(),je)}}function Xe(e,t){var r=e.readTime.compareTo(t.readTime);return 0!==r||0!==(r=x.comparator(e.documentKey,t.documentKey))?r:S(e.largestBatchId,t.largestBatchId)}let Je="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.";class Ze{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function et(e){if(e.code!==b.FAILED_PRECONDITION||e.message!==Je)throw e;p("LocalStore","Unexpectedly lost primary lease")}class w{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(n,i){return this.callbackAttached&&E(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(i,this.error):this.wrapSuccess(n,this.result):new w((t,r)=>{this.nextCallback=e=>{this.wrapSuccess(n,e).next(t,r)},this.catchCallback=e=>{this.wrapFailure(i,e).next(t,r)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{var t=e();return t instanceof w?t:w.resolve(t)}catch(e){return w.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):w.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):w.reject(t)}static resolve(r){return new w((e,t)=>{e(r)})}static reject(r){return new w((e,t)=>{t(r)})}static waitFor(e){return new w((t,r)=>{let n=0,i=0,s=!1;e.forEach(e=>{++n,e.next(()=>{++i,s&&i===n&&t()},e=>r(e))}),s=!0,i===n&&t()})}static or(e){let t=w.resolve(!1);for(let r of e)t=t.next(e=>e?w.resolve(e):r());return t}static forEach(e,r){let n=[];return e.forEach((e,t)=>{n.push(r.call(this,e,t))}),this.waitFor(n)}static mapArray(o,l){return new w((r,n)=>{let i=o.length,s=new Array(i),a=0;for(let e=0;e<i;e++){let t=e;l(o[t]).next(e=>{s[t]=e,++a===i&&r(s)},e=>n(e))}})}static doWhile(n,i){return new w((e,t)=>{let r=()=>{!0===n()?i().next(()=>{r()},t):e()};r()})}}let tt="SimpleDb";class rt{static open(e,t,r,n){try{return new rt(t,e.transaction(n,r))}catch(e){throw new at(t,e)}}constructor(r,e){this.action=r,this.transaction=e,this.aborted=!1,this.S=new f,this.transaction.oncomplete=()=>{this.S.resolve()},this.transaction.onabort=()=>{e.error?this.S.reject(new at(r,e.error)):this.S.resolve()},this.transaction.onerror=e=>{var t=ct(e.target.error);this.S.reject(new at(r,t))}}get D(){return this.S.promise}abort(e){e&&this.S.reject(e),this.aborted||(p(tt,"Aborting transaction:",e?e.message:"Client-initiated abort"),this.aborted=!0,this.transaction.abort())}v(){var e=this.transaction;this.aborted||"function"!=typeof e.commit||e.commit()}store(e){var t=this.transaction.objectStore(e);return new lt(t)}}class nt{static delete(e){return p(tt,"Removing database:",e),ut(G().indexedDB.deleteDatabase(e)).toPromise()}static C(){var e,t,r;return!(!(()=>{try{return"object"==typeof indexedDB}catch(e){}})()||!nt.F()&&(e=ee(),t=0<(t=nt.M(e))&&t<10,r=0<(r=it(e))&&r<4.5,0<e.indexOf("MSIE ")||0<e.indexOf("Trident/")||0<e.indexOf("Edge/")||t||r))}static F(){var e;return"undefined"!=typeof process&&"YES"===(null==(e=process.__PRIVATE_env)?void 0:e.O)}static N(e,t){return e.store(t)}static M(e){var t=e.match(/i(?:phone|pad|pod) os ([\d_]+)/i),t=t?t[1].split("_").slice(0,2).join("."):"-1";return Number(t)}constructor(e,t,r){this.name=e,this.version=t,this.B=r,this.L=null,12.2===nt.M(ee())&&d("Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.")}async k(s){return this.db||(p(tt,"Opening database:",this.name),this.db=await new Promise((r,n)=>{let i=indexedDB.open(this.name,this.version);i.onsuccess=e=>{var t=e.target.result;r(t)},i.onblocked=()=>{n(new at(s,"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed."))},i.onerror=e=>{var t=e.target.error;"VersionError"===t.name?n(new I(b.FAILED_PRECONDITION,"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.")):"InvalidStateError"===t.name?n(new I(b.FAILED_PRECONDITION,"Unable to open an IndexedDB connection. This could be due to running in a private browsing session on a browser whose private browsing sessions do not support IndexedDB: "+t)):n(new at(s,t))},i.onupgradeneeded=e=>{p(tt,'Database "'+this.name+'" requires upgrade from version:',e.oldVersion);var t=e.target.result;if(null!==this.L&&this.L!==e.oldVersion)throw new Error(`refusing to open IndexedDB database due to potential corruption of the IndexedDB database data; this corruption could be caused by clicking the "clear site data" button in a web browser; try reloading the web page to re-initialize the IndexedDB database: lastClosedDbVersion=${this.L}, event.oldVersion=${e.oldVersion}, event.newVersion=${e.newVersion}, db.version=`+t.version);this.B.q(t,i.transaction,e.oldVersion,this.version).next(()=>{p(tt,"Database upgrade to version "+this.version+" complete")})}}),this.db.addEventListener("close",e=>{var t=e.target;this.L=t.version},{passive:!0})),this.$&&(this.db.onversionchange=e=>this.$(e)),this.db}U(t){this.$=t,this.db&&(this.db.onversionchange=e=>t(e))}async runTransaction(r,e,n,i){var s="readonly"===e;let a=0;for(;;){++a;try{this.db=await this.k(r);let t=rt.open(this.db,r,s?"readonly":"readwrite",n),e=i(t).next(e=>(t.v(),e)).catch(e=>(t.abort(e),w.reject(e))).toPromise();return e.catch(()=>{}),await t.D,e}catch(r){let e=r,t="FirebaseError"!==e.name&&a<3;if(p(tt,"Transaction failed with error:",e.message,"Retrying:",t),this.close(),!t)return Promise.reject(e)}}}close(){this.db&&this.db.close(),this.db=void 0}}function it(e){var t=e.match(/Android ([\d.]+)/i),t=t?t[1].split(".").slice(0,2).join("."):"-1";return Number(t)}class st{constructor(e){this.K=e,this.W=!1,this.G=null}get isDone(){return this.W}get j(){return this.G}set cursor(e){this.K=e}done(){this.W=!0}H(e){this.G=e}delete(){return ut(this.K.delete())}}class at extends I{constructor(e,t){super(b.UNAVAILABLE,`IndexedDB transaction '${e}' failed: `+t),this.name="IndexedDbTransactionError"}}function ot(e){return"IndexedDbTransactionError"===e.name}class lt{constructor(e){this.store=e}put(e,t){let r;return ut(r=void 0!==t?(p(tt,"PUT",this.store.name,e,t),this.store.put(t,e)):(p(tt,"PUT",this.store.name,"<auto-key>",e),this.store.put(e)))}add(e){return p(tt,"ADD",this.store.name,e,e),ut(this.store.add(e))}get(t){return ut(this.store.get(t)).next(e=>(void 0===e&&(e=null),p(tt,"GET",this.store.name,t,e),e))}delete(e){return p(tt,"DELETE",this.store.name,e),ut(this.store.delete(e))}count(){return p(tt,"COUNT",this.store.name),ut(this.store.count())}J(e,t){var n=this.options(e,t),r=n.index?this.store.index(n.index):this.store;if("function"==typeof r.getAll){let e=r.getAll(n.range);return new w((t,r)=>{e.onerror=e=>{r(e.target.error)},e.onsuccess=e=>{t(e.target.result)}})}{let e=this.cursor(n),r=[];return this.Y(e,(e,t)=>{r.push(t)}).next(()=>r)}}Z(e,t){let n=this.store.getAll(e,null===t?void 0:t);return new w((t,r)=>{n.onerror=e=>{r(e.target.error)},n.onsuccess=e=>{t(e.target.result)}})}X(e,t){p(tt,"DELETE ALL",this.store.name);var r=this.options(e,t),r=(r.ee=!1,this.cursor(r));return this.Y(r,(e,t,r)=>r.delete())}te(e,t){let r;t?r=e:(r={},t=e);var n=this.cursor(r);return this.Y(n,t)}ne(i){let e=this.cursor({});return new w((r,n)=>{e.onerror=e=>{var t=ct(e.target.error);n(t)},e.onsuccess=e=>{let t=e.target.result;t?i(t.primaryKey,t.value).next(e=>{e?t.continue():r()}):r()}})}Y(e,s){let a=[];return new w((i,t)=>{e.onerror=e=>{t(e.target.error)},e.onsuccess=e=>{var n=e.target.result;if(n){let t=new st(n),r=s(n.primaryKey,n.value,t);if(r instanceof w){let e=r.catch(e=>(t.done(),w.reject(e)));a.push(e)}t.isDone?i():null===t.j?n.continue():n.continue(t.j)}else i()}}).next(()=>w.waitFor(a))}options(e,t){let r;return void 0!==e&&("string"==typeof e?r=e:t=e),{index:r,range:t}}cursor(e){let t="next";var r;return e.reverse&&(t="prev"),e.index?(r=this.store.index(e.index),e.ee?r.openKeyCursor(e.range,t):r.openCursor(e.range,t)):this.store.openCursor(e.range,t)}}function ut(e){return new w((r,n)=>{e.onsuccess=e=>{var t=e.target.result;r(t)},e.onerror=e=>{var t=ct(e.target.error);n(t)}})}let ht=!1;function ct(e){let t=nt.M(ee());if(12.2<=t&&t<13){let t="An internal error was encountered in the Indexed Database server";if(0<=e.message.indexOf(t)){let e=new I("internal",`IOS_INDEXEDDB_BUG1: IndexedDb has thrown '${t}'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.`);return ht||(ht=!0,setTimeout(()=>{throw e},0)),e}}return e}let dt="IndexBackfiller";class ft{constructor(e,t){this.asyncQueue=e,this.re=t,this.task=null}start(){this.ie(15e3)}stop(){this.task&&(this.task.cancel(),this.task=null)}get started(){return null!==this.task}ie(e){p(dt,`Scheduled in ${e}ms`),this.task=this.asyncQueue.enqueueAfterDelay("index_backfill",e,async()=>{this.task=null;try{var e=await this.re.se();p(dt,"Documents written: "+e)}catch(e){ot(e)?p(dt,"Ignoring IndexedDB error during index backfill: ",e):await et(e)}await this.ie(6e4)})}}class gt{constructor(e,t){this.localStore=e,this.persistence=t}async se(t=50){return this.persistence.runTransaction("Backfill Indexes","readwrite-primary",e=>this.oe(e,t))}oe(e,t){let r=new Set,n=t,i=!0;return w.doWhile(()=>!0===i&&0<n,()=>this.localStore.indexManager.getNextCollectionGroupToUpdate(e).next(t=>{if(null!==t&&!r.has(t))return p(dt,"Processing collection: "+t),this._e(e,t,n).next(e=>{n-=e,r.add(t)});i=!1})).next(()=>t-n)}_e(n,i,e){return this.localStore.indexManager.getMinOffsetFromCollectionGroup(n,i).next(r=>this.localStore.localDocuments.getNextDocuments(n,i,r,e).next(e=>{let t=e.changes;return this.localStore.indexManager.updateIndexEntries(n,t).next(()=>this.ae(r,e)).next(e=>(p(dt,"Updating offset: "+e),this.localStore.indexManager.updateCollectionGroup(n,i,e))).next(()=>t.size)}))}ae(e,t){let n=e;return t.changes.forEach((e,t)=>{var r=We(t);0<Xe(r,n)&&(n=r)}),new Ye(n.readTime,n.documentKey,Math.max(t.batchId,e.largestBatchId))}}class mt{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ue(e),this.ce=e=>t.writeSequenceNumber(e))}ue(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){var e=++this.previousValue;return this.ce&&this.ce(e),e}}let pt=mt.le=-1;function yt(e){return null==e}function vt(e){return 0===e&&1/e==-1/0}function wt(e){return"number"==typeof e&&Number.isInteger(e)&&!vt(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}let _t="";function o(e){let t="";for(let r=0;r<e.length;r++)0<t.length&&(t=bt(t)),t=((t,e)=>{let r=e,n=t.length;for(let i=0;i<n;i++){let e=t.charAt(i);switch(e){case"\0":r+="";break;case _t:r+="";break;default:r+=e}}return r})(e.get(r),t);return bt(t)}function bt(e){return e+_t+""}function It(r){let e=r.length;if(y(2<=e,64408,{path:r}),2===e)return y(r.charAt(0)===_t&&""===r.charAt(1),56145,{path:r}),T.emptyPath();var __PRIVATE_lastReasonableEscapeIndex=e-2,n=[];let i="";for(let a=0;a<e;){let t=r.indexOf(_t,a);switch((t<0||t>__PRIVATE_lastReasonableEscapeIndex)&&E(50515,{path:r}),r.charAt(t+1)){case"":var s=r.substring(a,t);let e;0===i.length?e=s:(i+=s,e=i,i=""),n.push(e);break;case"":i=i+r.substring(a,t)+"\0";break;case"":i+=r.substring(a,t+1);break;default:E(61167,{path:r})}a=t+2}return new T(n)}let Tt="remoteDocuments",Et="owner",St="owner",xt="mutationQueues",Ct="mutations",At="batchId",Dt="userMutationsIndex",Nt=["userId","batchId"];function kt(e,t){return[e,o(t)]}function Rt(e,t,r){return[e,o(t),r]}let Ot={},Mt="documentMutations",Lt="remoteDocumentsV14",Vt=["prefixPath","collectionGroup","readTime","documentId"],Pt="documentKeyIndex",Ft=["prefixPath","collectionGroup","documentId"],Bt="collectionGroupIndex",Ut=["collectionGroup","readTime","prefixPath","documentId"],qt="remoteDocumentGlobal",jt="remoteDocumentGlobalKey",zt="targets",Kt="queryTargetsIndex",Gt=["canonicalId","targetId"],$t="targetDocuments",Qt=["targetId","path"],Ht="documentTargetsIndex",Wt=["path","targetId"],Yt="targetGlobalKey",Xt="targetGlobal",Jt="collectionParents",Zt=["collectionId","parent"],er="clientMetadata",tr="bundles",rr="namedQueries",nr="indexConfiguration",ir="collectionGroupIndex",sr="indexState",ar=["indexId","uid"],or="sequenceNumberIndex",lr=["uid","sequenceNumber"],ur="indexEntries",hr=["indexId","uid","arrayValue","directionalValue","orderedDocumentKey","documentKey"],cr="documentKeyIndex",dr=["indexId","uid","orderedDocumentKey"],fr="documentOverlays",gr=["userId","collectionPath","documentId"],mr="collectionPathOverlayIndex",Sr=["userId","collectionPath","largestBatchId"],xr="collectionGroupOverlayIndex",Cr=["userId","collectionGroup","largestBatchId"],Ar="globals",Dr=[xt,Ct,Mt,Tt,zt,Et,Xt,$t,er,qt,Jt,tr,rr],Nr=[...Dr,fr],kr=[xt,Ct,Mt,Lt,zt,Et,Xt,$t,er,qt,Jt,tr,rr,fr],Rr=kr,Or=[...Rr,nr,sr,ur],Mr=Or,Lr=[...Or,Ar],Vr=Lr;class Pr extends Ze{constructor(e,t){super(),this.he=e,this.currentSequenceNumber=t}}function r(e,t){var r=e;return nt.N(r.he,t)}function Fr(e){let t=0;for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}function Br(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t(r,e[r])}function Ur(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}class C{constructor(e,t){this.comparator=e,this.root=t||jr.EMPTY}insert(e,t){return new C(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,jr.BLACK,null,null))}remove(e){return new C(this.comparator,this.root.remove(e,this.comparator).copy(null,null,jr.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){var r=this.comparator(e,t.key);if(0===r)return t.value;r<0?t=t.left:0<r&&(t=t.right)}return null}indexOf(e){let t=0,r=this.root;for(;!r.isEmpty();){var n=this.comparator(e,r.key);if(0===n)return t+r.left.size;r=n<0?r.left:(t+=r.left.size+1,r.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(r){this.inorderTraversal((e,t)=>(r(e,t),!1))}toString(){let r=[];return this.inorderTraversal((e,t)=>(r.push(e+":"+t),!1)),`{${r.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new qr(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new qr(this.root,e,this.comparator,!1)}getReverseIterator(){return new qr(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new qr(this.root,e,this.comparator,!0)}}class qr{constructor(e,t,r,n){this.isReverse=n,this.nodeStack=[];let i=1;for(;!e.isEmpty();)if(i=t?r(e.key,t):1,t&&n&&(i*=-1),i<0)e=this.isReverse?e.left:e.right;else{if(0===i){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop();var t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return 0<this.nodeStack.length}peek(){var e;return 0===this.nodeStack.length?null:{key:(e=this.nodeStack[this.nodeStack.length-1]).key,value:e.value}}}class jr{constructor(e,t,r,n,i){this.key=e,this.value=t,this.color=null!=r?r:jr.RED,this.left=null!=n?n:jr.EMPTY,this.right=null!=i?i:jr.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,r,n,i){return new jr(null!=e?e:this.key,null!=t?t:this.value,null!=r?r:this.color,null!=n?n:this.left,null!=i?i:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,r){var n=this,i=r(e,n.key);return(n=i<0?n.copy(null,null,null,n.left.insert(e,t,r),null):0===i?n.copy(null,t,null,null,null):n.copy(null,null,null,null,n.right.insert(e,t,r))).fixUp()}removeMin(){if(this.left.isEmpty())return jr.EMPTY;let e=this;return(e=(e=e.left.isRed()||e.left.left.isRed()?e:e.moveRedLeft()).copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let r,n=this;if(t(e,n.key)<0)n=(n=n.left.isEmpty()||n.left.isRed()||n.left.left.isRed()?n:n.moveRedLeft()).copy(null,null,null,n.left.remove(e,t),null);else{if(0===t(e,(n=(n=n.left.isRed()?n.rotateRight():n).right.isEmpty()||n.right.isRed()||n.right.left.isRed()?n:n.moveRedRight()).key)){if(n.right.isEmpty())return jr.EMPTY;r=n.right.min(),n=n.copy(r.key,r.value,null,null,n.right.removeMin())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e=(e=(e=e.right.isRed()&&!e.left.isRed()?e.rotateLeft():e).left.isRed()&&e.left.left.isRed()?e.rotateRight():e).left.isRed()&&e.right.isRed()?e.colorFlip():e}moveRedLeft(){let e=this.colorFlip();return e=e.right.left.isRed()?(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip():e}moveRedRight(){let e=this.colorFlip();return e=e.left.left.isRed()?(e=e.rotateRight()).colorFlip():e}rotateLeft(){var e=this.copy(null,null,jr.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){var e=this.copy(null,null,jr.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){var e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){var e=this.check();return Math.pow(2,e)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw E(43730,{key:this.key,value:this.value});if(this.right.isRed())throw E(14113,{key:this.key,value:this.value});var e=this.left.check();if(e!==this.right.check())throw E(27949);return e+(this.isRed()?0:1)}}jr.EMPTY=null,jr.RED=!0,jr.BLACK=!1,jr.EMPTY=new class{constructor(){this.size=0}get key(){throw E(57766)}get value(){throw E(16141)}get color(){throw E(16727)}get left(){throw E(29726)}get right(){throw E(36894)}copy(e,t,r,n,i){return this}insert(e,t,r){return new jr(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class A{constructor(e){this.comparator=e,this.data=new C(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(r){this.data.inorderTraversal((e,t)=>(r(e),!1))}forEachInRange(e,t){for(var r=this.data.getIteratorFrom(e[0]);r.hasNext();){var n=r.getNext();if(0<=this.comparator(n.key,e[1]))return;t(n.key)}}forEachWhile(e,t){for(var r=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();r.hasNext();)if(!e(r.getNext().key))return}firstAfterOrEqual(e){var t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new zr(this.data.getIterator())}getIteratorFrom(e){return new zr(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof A))return!1;if(this.size!==e.size)return!1;for(var r=this.data.getIterator(),n=e.data.getIterator();r.hasNext();){let e=r.getNext().key,t=n.getNext().key;if(0!==this.comparator(e,t))return!1}return!0}toArray(){let t=[];return this.forEach(e=>{t.push(e)}),t}toString(){let t=[];return this.forEach(e=>t.push(e)),"SortedSet("+t.toString()+")"}copy(e){var t=new A(this.comparator);return t.data=e,t}}class zr{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}function Kr(e){return e.hasNext()?e.getNext():void 0}class Gr{constructor(e){(this.fields=e).sort(v.comparator)}static empty(){return new Gr([])}unionWith(e){let t=new A(v.comparator);for(let e of this.fields)t=t.add(e);for(var r of e)t=t.add(r);return new Gr(t.toArray())}covers(e){for(var t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return Ve(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class $r extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class D{constructor(e){this.binaryString=e}static fromBase64String(e){var t=(e=>{try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new $r("Invalid base64 string: "+e):e}})(e);return new D(t)}static fromUint8Array(e){var t=(e=>{let t="";for(let r=0;r<e.length;++r)t+=String.fromCharCode(e[r]);return t})(e);return new D(t)}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return e=this.binaryString,btoa(e);var e}toUint8Array(){var e=this.binaryString,t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return S(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}D.EMPTY_BYTE_STRING=new D("");let Qr=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function Hr(t){if(y(!!t,39018),"string"!=typeof t)return{seconds:N(t.seconds),nanos:N(t.nanos)};{let e=0;var r=Qr.exec(t),r=(y(!!r,46558,{timestamp:t}),r[1]&&(r=((r=r[1])+"000000000").substr(0,9),e=Number(r)),new Date(t));return{seconds:Math.floor(r.getTime()/1e3),nanos:e}}}function N(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function Wr(e){return"string"==typeof e?D.fromBase64String(e):D.fromUint8Array(e)}let Yr="server_timestamp",Xr="__type__",Jr="__previous_value__",Zr="__local_write_time__";function en(e){var t;return(null==(t=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[Xr])?void 0:t.stringValue)===Yr}function tn(e){var t=e.mapValue.fields[Jr];return en(t)?tn(t):t}function rn(e){var t=Hr(e.mapValue.fields[Zr].timestampValue);return new h(t.seconds,t.nanos)}class nn{constructor(e,t,r,n,i,s,a,o,l,u){this.databaseId=e,this.appId=t,this.persistenceKey=r,this.host=n,this.ssl=i,this.forceLongPolling=s,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=l,this.isUsingEmulator=u}}let sn="(default)";class an{constructor(e,t){this.projectId=e,this.database=t||sn}static empty(){return new an("","")}get isDefaultDatabase(){return this.database===sn}isEqual(e){return e instanceof an&&e.projectId===this.projectId&&e.database===this.database}}let on="__type__",ln="__max__",un={mapValue:{fields:{__type__:{stringValue:ln}}}},hn="__vector__",cn="value",dn={nullValue:"NULL_VALUE"};function fn(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?en(e)?4:An(e)?9007199254740991:xn(e)?10:11:E(28295,{value:e})}function gn(t,r){if(t===r)return!0;var n,i,s,a,o,e=fn(t);if(e!==fn(r))return!1;switch(e){case 0:case 9007199254740991:return!0;case 1:return t.booleanValue===r.booleanValue;case 4:return rn(t).isEqual(rn(r));case 3:return s=r,"string"==typeof(i=t).timestampValue&&"string"==typeof s.timestampValue&&i.timestampValue.length===s.timestampValue.length?i.timestampValue===s.timestampValue:(a=Hr(i.timestampValue),o=Hr(s.timestampValue),a.seconds===o.seconds&&a.nanos===o.nanos);case 5:return t.stringValue===r.stringValue;case 6:return i=r,Wr(t.bytesValue).isEqual(Wr(i.bytesValue));case 7:return t.referenceValue===r.referenceValue;case 8:return s=r,N((n=t).geoPointValue.latitude)===N(s.geoPointValue.latitude)&&N(n.geoPointValue.longitude)===N(s.geoPointValue.longitude);case 2:return n=r,"integerValue"in(u=t)&&"integerValue"in n?N(u.integerValue)===N(n.integerValue):"doubleValue"in u&&"doubleValue"in n&&((a=N(u.doubleValue))===(o=N(n.doubleValue))?vt(a)===vt(o):isNaN(a)&&isNaN(o));case 9:return Ve(t.arrayValue.values||[],r.arrayValue.values||[],gn);case 10:case 11:var l=t,u=r,h=l.mapValue.fields||{},c=u.mapValue.fields||{};if(Fr(h)!==Fr(c))return!1;for(let e in h)if(h.hasOwnProperty(e)&&(void 0===c[e]||!gn(h[e],c[e])))return!1;return!0;default:return E(52216,{left:t})}}function mn(e,t){return void 0!==(e.values||[]).find(e=>gn(e,t))}function pn(e,n){if(e===n)return 0;var i,s,a,o,l,u,h,c,d=fn(e),t=fn(n);if(d!==t)return S(d,t);switch(d){case 0:case 9007199254740991:return 0;case 1:return S(e.booleanValue,n.booleanValue);case 2:return u=n,h=N((l=e).integerValue||l.doubleValue),c=N(u.integerValue||u.doubleValue),h<c?-1:c<h?1:h===c?0:isNaN(h)?isNaN(c)?0:-1:1;case 3:return yn(e.timestampValue,n.timestampValue);case 4:return yn(rn(e),rn(n));case 5:return Me(e.stringValue,n.stringValue);case 6:return l=e.bytesValue,u=n.bytesValue,h=Wr(l),c=Wr(u),h.compareTo(c);case 7:var f=e.referenceValue,g=n.referenceValue,m=f.split("/"),p=g.split("/");for(let t=0;t<m.length&&t<p.length;t++){let e=S(m[t],p[t]);if(0!==e)return e}return S(m.length,p.length);case 8:return f=e.geoPointValue,g=n.geoPointValue,0!==(o=S(N(f.latitude),N(g.latitude)))?o:S(N(f.longitude),N(g.longitude));case 9:return vn(e.arrayValue,n.arrayValue);case 10:return y=e.mapValue,i=n.mapValue,o=y.fields||{},s=i.fields||{},o=null==(o=o[cn])?void 0:o.arrayValue,s=null==(s=s[cn])?void 0:s.arrayValue,0!==(a=S((null==(a=null==o?void 0:o.values)?void 0:a.length)||0,(null==(a=null==s?void 0:s.values)?void 0:a.length)||0))?a:vn(o,s);case 11:var y=e.mapValue,v=n.mapValue;if(y===un.mapValue&&v===un.mapValue)return 0;if(y===un.mapValue)return 1;if(v===un.mapValue)return-1;var w=y.fields||{},_=Object.keys(w),b=v.fields||{},I=Object.keys(b);_.sort(),I.sort();for(let r=0;r<_.length&&r<I.length;++r){let e=Me(_[r],I[r]);if(0!==e)return e;var T=pn(w[_[r]],b[I[r]]);if(0!==T)return T}return S(_.length,I.length);default:throw E(23264,{Pe:d})}}function yn(e,t){var r,n,i;return"string"==typeof e&&"string"==typeof t&&e.length===t.length?S(e,t):(r=Hr(e),n=Hr(t),0!==(i=S(r.seconds,n.seconds))?i:S(r.nanos,n.nanos))}function vn(e,t){var r=e.values||[],n=t.values||[];for(let i=0;i<r.length&&i<n.length;++i){let e=pn(r[i],n[i]);if(e)return e}return S(r.length,n.length)}function wn(e){return function s(e){return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?(e=>{let t=Hr(e);return`time(${t.seconds},${t.nanos})`})(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?(e=>Wr(e).toBase64())(e.bytesValue):"referenceValue"in e?(e=>x.fromName(e).toString())(e.referenceValue):"geoPointValue"in e?(e=>`geo(${e.latitude},${e.longitude})`)(e.geoPointValue):"arrayValue"in e?(e=>{let t="[",r=!0;for(var n of e.values||[])r?r=!1:t+=",",t+=s(n);return t+"]"})(e.arrayValue):"mapValue"in e?(e=>{let t=Object.keys(e.fields||{}).sort(),r="{",n=!0;for(var i of t)n?n=!1:r+=",",r+=i+":"+s(e.fields[i]);return r+"}"})(e.mapValue):E(61005,{value:e})}(e)}function _n(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/`+t.path.canonicalString()}}function bn(e){return!!e&&"integerValue"in e}function In(e){return!!e&&"arrayValue"in e}function Tn(e){return e&&"nullValue"in e}function En(e){return e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function Sn(e){return e&&"mapValue"in e}function xn(e){var t;return(null==(t=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[on])?void 0:t.stringValue)===hn}function Cn(t){if(t.geoPointValue)return{geoPointValue:Object.assign({},t.geoPointValue)};if(t.timestampValue&&"object"==typeof t.timestampValue)return{timestampValue:Object.assign({},t.timestampValue)};if(t.mapValue){let r={mapValue:{fields:{}}};return Br(t.mapValue.fields,(e,t)=>r.mapValue.fields[e]=Cn(t)),r}if(t.arrayValue){var r={arrayValue:{values:[]}};for(let e=0;e<(t.arrayValue.values||[]).length;++e)r.arrayValue.values[e]=Cn(t.arrayValue.values[e]);return r}return Object.assign({},t)}function An(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===ln}let Dn={mapValue:{fields:{[on]:{stringValue:hn},[cn]:{arrayValue:{}}}}};function Nn(e,t){var r=pn(e.value,t.value);return 0!==r?r:e.inclusive&&!t.inclusive?-1:!e.inclusive&&t.inclusive?1:0}function kn(e,t){var r=pn(e.value,t.value);return 0!==r?r:e.inclusive&&!t.inclusive?1:!e.inclusive&&t.inclusive?-1:0}class Rn{constructor(e){this.value=e}static empty(){return new Rn({mapValue:{}})}field(r){if(r.isEmpty())return this.value;{let e=this.value;for(let t=0;t<r.length-1;++t)if(!Sn(e=(e.mapValue.fields||{})[r.get(t)]))return null;return(e=(e.mapValue.fields||{})[r.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=Cn(t)}setAll(e){let r=v.emptyPath(),n={},i=[];e.forEach((e,t)=>{if(!r.isImmediateParentOf(t)){let e=this.getFieldsMap(r);this.applyChanges(e,n,i),n={},i=[],r=t.popLast()}e?n[t.lastSegment()]=Cn(e):i.push(t.lastSegment())});var t=this.getFieldsMap(r);this.applyChanges(t,n,i)}delete(e){var t=this.field(e.popLast());Sn(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return gn(this.value,e.value)}getFieldsMap(t){let r=this.value;r.mapValue.fields||(r.mapValue={fields:{}});for(let n=0;n<t.length;++n){let e=r.mapValue.fields[t.get(n)];Sn(e)&&e.mapValue.fields||(e={mapValue:{fields:{}}},r.mapValue.fields[t.get(n)]=e),r=e}return r.mapValue.fields}applyChanges(r,e,t){Br(e,(e,t)=>r[e]=t);for(let e of t)delete r[e]}clone(){return new Rn(Cn(this.value))}}class k{constructor(e,t,r,n,i,s,a){this.key=e,this.documentType=t,this.version=r,this.readTime=n,this.createTime=i,this.data=s,this.documentState=a}static newInvalidDocument(e){return new k(e,0,g.min(),g.min(),g.min(),Rn.empty(),0)}static newFoundDocument(e,t,r,n){return new k(e,1,t,g.min(),r,n,0)}static newNoDocument(e,t){return new k(e,2,t,g.min(),g.min(),Rn.empty(),0)}static newUnknownDocument(e,t){return new k(e,3,t,g.min(),g.min(),Rn.empty(),2)}convertToFoundDocument(e,t){return!this.createTime.isEqual(g.min())||2!==this.documentType&&0!==this.documentType||(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=Rn.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=Rn.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=g.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof k&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new k(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class On{constructor(e,t){this.position=e,this.inclusive=t}}function Mn(e,t,r){let n=0;for(let a=0;a<e.position.length;a++){var i=t[a],s=e.position[a];if(n=i.field.isKeyField()?x.comparator(x.fromName(s.referenceValue),r.key):pn(s,r.data.field(i.field)),"desc"===i.dir&&(n*=-1),0!==n)break}return n}function Ln(e,t){if(null===e)return null===t;if(null===t)return!1;if(e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let r=0;r<e.position.length;r++)if(!gn(e.position[r],t.position[r]))return!1;return!0}class Vn{constructor(e,t="asc"){this.field=e,this.dir=t}}class Pn{}class R extends Pn{constructor(e,t,r){super(),this.field=e,this.op=t,this.value=r}static create(e,t,r){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,r):new Kn(e,t,r):"array-contains"===t?new Hn(e,r):"in"===t?new Wn(e,r):"not-in"===t?new Yn(e,r):"array-contains-any"===t?new Xn(e,r):new R(e,t,r)}static createKeyFieldInFilter(e,t,r){return new("in"===t?Gn:$n)(e,r)}matches(e){var t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(pn(t,this.value)):null!==t&&fn(this.value)===fn(t)&&this.matchesComparison(pn(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return 0<e;case">=":return 0<=e;default:return E(47266,{operator:this.op})}}isInequality(){return 0<=["<","<=",">",">=","!=","not-in"].indexOf(this.op)}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class O extends Pn{constructor(e,t){super(),this.filters=e,this.op=t,this.Te=null}static create(e,t){return new O(e,t)}matches(t){return Fn(this)?void 0===this.filters.find(e=>!e.matches(t)):void 0!==this.filters.find(e=>e.matches(t))}getFlattenedFilters(){return null===this.Te&&(this.Te=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.Te}getFilters(){return Object.assign([],this.filters)}}function Fn(e){return"and"===e.op}function Bn(e){return"or"===e.op}function Un(e){return qn(e)&&Fn(e)}function qn(e){for(var t of e.filters)if(t instanceof O)return!1;return!0}function jn(e,t){var r=e.filters.concat(t);return O.create(r,e.op)}function zn(e){return e instanceof R?`${(t=e).field.canonicalString()} ${t.op} `+wn(t.value):e instanceof O?(t=e).op.toString()+" {"+t.getFilters().map(zn).join(" ,")+"}":"Filter";var t}class Kn extends R{constructor(e,t,r){super(e,t,r),this.key=x.fromName(r.referenceValue)}matches(e){var t=x.comparator(e.key,this.key);return this.matchesComparison(t)}}class Gn extends R{constructor(e,t){super(e,"in",t),this.keys=Qn(0,t)}matches(t){return this.keys.some(e=>e.isEqual(t.key))}}class $n extends R{constructor(e,t){super(e,"not-in",t),this.keys=Qn(0,t)}matches(t){return!this.keys.some(e=>e.isEqual(t.key))}}function Qn(e,t){var r;return((null==(r=t.arrayValue)?void 0:r.values)||[]).map(e=>x.fromName(e.referenceValue))}class Hn extends R{constructor(e,t){super(e,"array-contains",t)}matches(e){var t=e.data.field(this.field);return In(t)&&mn(t.arrayValue,this.value)}}class Wn extends R{constructor(e,t){super(e,"in",t)}matches(e){var t=e.data.field(this.field);return null!==t&&mn(this.value.arrayValue,t)}}class Yn extends R{constructor(e,t){super(e,"not-in",t)}matches(e){var t;return!mn(this.value.arrayValue,{nullValue:"NULL_VALUE"})&&null!==(t=e.data.field(this.field))&&void 0===t.nullValue&&!mn(this.value.arrayValue,t)}}class Xn extends R{constructor(e,t){super(e,"array-contains-any",t)}matches(e){var t=e.data.field(this.field);return!(!In(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>mn(this.value.arrayValue,e))}}class Jn{constructor(e,t=null,r=[],n=[],i=null,s=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=r,this.filters=n,this.limit=i,this.startAt=s,this.endAt=a,this.Ie=null}}function Zn(e,t=null,r=[],n=[],i=null,s=null,a=null){return new Jn(e,t,r,n,i,s,a)}function ei(e){var t=e;if(null===t.Ie){let e=t.path.canonicalString();null!==t.collectionGroup&&(e+="|cg:"+t.collectionGroup),e=(e=(e+="|f:")+t.filters.map(e=>function t(e){var r;return e instanceof R?e.field.canonicalString()+e.op.toString()+wn(e.value):Un(e)?e.filters.map(e=>t(e)).join(","):(r=e.filters.map(e=>t(e)).join(","),e.op+`(${r})`)}(e)).join(",")+"|ob:")+t.orderBy.map(e=>(e=e).field.canonicalString()+e.dir).join(","),yt(t.limit)||(e=(e+="|l:")+t.limit),t.startAt&&(e=(e=(e+="|lb:")+(t.startAt.inclusive?"b:":"a:"))+t.startAt.position.map(e=>wn(e)).join(",")),t.endAt&&(e=(e=(e+="|ub:")+(t.endAt.inclusive?"a:":"b:"))+t.endAt.position.map(e=>wn(e)).join(",")),t.Ie=e}return t.Ie}function ti(e,t){if(e.limit!==t.limit)return!1;if(e.orderBy.length!==t.orderBy.length)return!1;for(let i=0;i<e.orderBy.length;i++)if(r=e.orderBy[i],n=t.orderBy[i],r.dir!==n.dir||!r.field.isEqual(n.field))return!1;var r,n;if(e.filters.length!==t.filters.length)return!1;for(let s=0;s<e.filters.length;s++)if(!function n(e,t){return e instanceof R?(r=e,(s=t)instanceof R&&r.op===s.op&&r.field.isEqual(s.field)&&gn(r.value,s.value)):e instanceof O?(r=e,(i=t)instanceof O&&r.op===i.op&&r.filters.length===i.filters.length&&r.filters.reduce((e,t,r)=>e&&n(t,i.filters[r]),!0)):void E(19439);var i,r,s}(e.filters[s],t.filters[s]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!Ln(e.startAt,t.startAt)&&Ln(e.endAt,t.endAt)}function ri(e){return x.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}function ni(e,t){return e.filters.filter(e=>e instanceof R&&e.field.isEqual(t))}function ii(e,r,n){let i=dn,s=!0;for(let n of ni(e,r)){let e=dn,t=!0;switch(n.op){case"<":case"<=":e="nullValue"in(a=n.value)?dn:"booleanValue"in a?{booleanValue:!1}:"integerValue"in a||"doubleValue"in a?{doubleValue:NaN}:"timestampValue"in a?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"stringValue"in a?{stringValue:""}:"bytesValue"in a?{bytesValue:""}:"referenceValue"in a?_n(an.empty(),x.empty()):"geoPointValue"in a?{geoPointValue:{latitude:-90,longitude:-180}}:"arrayValue"in a?{arrayValue:{}}:"mapValue"in a?xn(a)?Dn:{mapValue:{}}:E(35942,{value:a});break;case"==":case"in":case">=":e=n.value;break;case">":e=n.value,t=!1;break;case"!=":case"not-in":e=dn}Nn({value:i,inclusive:s},{value:e,inclusive:t})<0&&(i=e,s=t)}var a;if(null!==n)for(let t=0;t<e.orderBy.length;++t)if(e.orderBy[t].field.isEqual(r)){let e=n.position[t];Nn({value:i,inclusive:s},{value:e,inclusive:n.inclusive})<0&&(i=e,s=n.inclusive);break}return{value:i,inclusive:s}}function si(e,r,n){let i=un,s=!0;for(let n of ni(e,r)){let e=un,t=!0;switch(n.op){case">=":case">":e="nullValue"in(a=n.value)?{booleanValue:!1}:"booleanValue"in a?{doubleValue:NaN}:"integerValue"in a||"doubleValue"in a?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"timestampValue"in a?{stringValue:""}:"stringValue"in a?{bytesValue:""}:"bytesValue"in a?_n(an.empty(),x.empty()):"referenceValue"in a?{geoPointValue:{latitude:-90,longitude:-180}}:"geoPointValue"in a?{arrayValue:{}}:"arrayValue"in a?Dn:"mapValue"in a?xn(a)?{mapValue:{}}:un:E(61959,{value:a}),t=!1;break;case"==":case"in":case"<=":e=n.value;break;case"<":e=n.value,t=!1;break;case"!=":case"not-in":e=un}0<kn({value:i,inclusive:s},{value:e,inclusive:t})&&(i=e,s=t)}var a;if(null!==n)for(let t=0;t<e.orderBy.length;++t)if(e.orderBy[t].field.isEqual(r)){let e=n.position[t];0<kn({value:i,inclusive:s},{value:e,inclusive:n.inclusive})&&(i=e,s=n.inclusive);break}return{value:i,inclusive:s}}class ai{constructor(e,t=null,r=[],n=[],i=null,s="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=r,this.filters=n,this.limit=i,this.limitType=s,this.startAt=a,this.endAt=o,this.Ee=null,this.de=null,this.Ae=null,this.startAt,this.endAt}}function oi(e,t,r,n,i,s,a,o){return new ai(e,t,r,n,i,s,a,o)}function li(e){return new ai(e)}function ui(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function hi(e){return null!==e.collectionGroup}function ci(e){let n=e;if(null===n.Ee){n.Ee=[];let t=new Set;for(var i of n.explicitOrderBy)n.Ee.push(i),t.add(i.field.canonicalString());let r=0<n.explicitOrderBy.length?n.explicitOrderBy[n.explicitOrderBy.length-1].dir:"asc",e=(e=>{let t=new A(v.comparator);return e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t})(n);e.forEach(e=>{t.has(e.canonicalString())||e.isKeyField()||n.Ee.push(new Vn(e,r))}),t.has(v.keyField().canonicalString())||n.Ee.push(new Vn(v.keyField(),r))}return n.Ee}function di(e){var t=e;return t.de||(t.de=((e,t)=>{if("F"===e.limitType)return Zn(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);t=t.map(e=>{var t="desc"===e.dir?"asc":"desc";return new Vn(e.field,t)});var r=e.endAt?new On(e.endAt.position,e.endAt.inclusive):null,n=e.startAt?new On(e.startAt.position,e.startAt.inclusive):null;return Zn(e.path,e.collectionGroup,t,e.filters,e.limit,r,n)})(t,ci(e))),t.de}function fi(e,t){var r=e.filters.concat([t]);return new ai(e.path,e.collectionGroup,e.explicitOrderBy.slice(),r,e.limit,e.limitType,e.startAt,e.endAt)}function gi(e,t,r){return new ai(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,r,e.startAt,e.endAt)}function mi(e,t){return ti(di(e),di(t))&&e.limitType===t.limitType}function pi(e){return ei(di(e))+"|lt:"+e.limitType}function yi(e){return`Query(target=${(e=>{let t=e.path.canonicalString();return null!==e.collectionGroup&&(t+=" collectionGroup="+e.collectionGroup),0<e.filters.length&&(t+=`, filters: [${e.filters.map(e=>zn(e)).join(", ")}]`),yt(e.limit)||(t+=", limit: "+e.limit),0<e.orderBy.length&&(t+=`, orderBy: [${e.orderBy.map(e=>`${(e=e).field.canonicalString()} (${e.dir})`).join(", ")}]`),e.startAt&&(t=(t=(t+=", startAt: ")+(e.startAt.inclusive?"b:":"a:"))+e.startAt.position.map(e=>wn(e)).join(",")),`Target(${t=e.endAt?(t=(t+=", endAt: ")+(e.endAt.inclusive?"a:":"b:"))+e.endAt.position.map(e=>wn(e)).join(","):t})`})(di(e))}; limitType=${e.limitType})`}function vi(e,t){return t.isFoundDocument()&&(s=e,o=(a=t).key.path,null!==s.collectionGroup?a.key.hasCollectionId(s.collectionGroup)&&s.path.isPrefixOf(o):x.isDocumentKey(s.path)?s.path.isEqual(o):s.path.isImmediateParentOf(o))&&((e,t)=>{for(var r of ci(e))if(!r.field.isKeyField()&&null===t.data.field(r.field))return;return 1})(e,t)&&((e,t)=>{for(var r of e.filters)if(!r.matches(t))return;return 1})(e,t)&&(a=t,!(s=e).startAt||(n=s.startAt,r=ci(s),i=Mn(n,r,a),n.inclusive?i<=0:i<0))&&(!s.endAt||(r=s.endAt,n=ci(s),i=Mn(r,n,a),r.inclusive?0<=i:0<i));var r,n,i,s,a,o}function wi(e){return e.collectionGroup||(e.path.length%2==1?e.path.lastSegment():e.path.get(e.path.length-2))}function _i(e){return(t,r)=>{let n=!1;for(var i of ci(e)){let e=((e,t,r)=>{var n=e.field.isKeyField()?x.comparator(t.key,r.key):((e,t,r)=>{var n=t.data.field(e),i=r.data.field(e);return null!==n&&null!==i?pn(n,i):E(42886)})(e.field,t,r);switch(e.dir){case"asc":return n;case"desc":return-1*n;default:return E(19790,{direction:e.dir})}})(i,t,r);if(0!==e)return e;n=n||i.field.isKeyField()}return 0}}class bi{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(r){let e=this.mapKeyFn(r),n=this.inner[e];if(void 0!==n)for(let[e,t]of n)if(this.equalsFn(e,r))return t}has(e){return void 0!==this.get(e)}set(t,r){var e=this.mapKeyFn(t),n=this.inner[e];if(void 0===n)this.inner[e]=[[t,r]];else{for(let e=0;e<n.length;e++)if(this.equalsFn(n[e][0],t))return void(n[e]=[t,r]);n.push([t,r])}this.innerSize++}delete(t){var r=this.mapKeyFn(t),n=this.inner[r];if(void 0!==n)for(let e=0;e<n.length;e++)if(this.equalsFn(n[e][0],t))return 1===n.length?delete this.inner[r]:n.splice(e,1),this.innerSize--,!0;return!1}forEach(n){Br(this.inner,(e,t)=>{for(let[e,r]of t)n(e,r)})}isEmpty(){return Ur(this.inner)}size(){return this.innerSize}}let Ii=new C(x.comparator);let Ti=new C(x.comparator);function Ei(...e){let t=Ti;for(var r of e)t=t.insert(r.key,r);return t}function Si(e){let r=Ti;return e.forEach((e,t)=>r=r.insert(e,t.overlayedDocument)),r}function xi(){return new bi(e=>e.toString(),(e,t)=>e.isEqual(t))}let Ci=new C(x.comparator),Ai=new A(x.comparator);function M(...e){let t=Ai;for(var r of e)t=t.add(r);return t}let Di=new A(S);function Ni(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:vt(t)?"-0":t}}function ki(e){return{integerValue:""+e}}function Ri(e,t){return wt(t)?ki(t):Ni(e,t)}class Oi{constructor(){this._=void 0}}function Mi(e,t){return e instanceof Ui?bn(e=t)||(e=e)&&"doubleValue"in e?t:{integerValue:0}:null}class Li extends Oi{}class Vi extends Oi{constructor(e){super(),this.elements=e}}function Pi(e,t){var r=ji(t);for(let t of e.elements)r.some(e=>gn(e,t))||r.push(t);return{arrayValue:{values:r}}}class Fi extends Oi{constructor(e){super(),this.elements=e}}function Bi(e,t){let r=ji(t);for(let t of e.elements)r=r.filter(e=>!gn(e,t));return{arrayValue:{values:r}}}class Ui extends Oi{constructor(e,t){super(),this.serializer=e,this.Re=t}}function qi(e){return N(e.integerValue||e.doubleValue)}function ji(e){return In(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class zi{constructor(e,t){this.field=e,this.transform=t}}class Ki{constructor(e,t){this.version=e,this.transformResults=t}}class L{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new L}static exists(e){return new L(void 0,e)}static updateTime(e){return new L(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function Gi(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class $i{}function Qi(e,r){if(!e.hasLocalMutations||r&&0===r.fields.length)return null;if(null===r)return e.isNoDocument()?new ts(e.key,L.none()):new Yi(e.key,e.data,L.none());{var n,i=e.data,s=Rn.empty();let t=new A(v.comparator);for(n of r.fields)if(!t.has(n)){let e=i.field(n);null===e&&1<n.length&&(n=n.popLast(),e=i.field(n)),null===e?s.delete(n):s.set(n,e),t=t.add(n)}return new Xi(e.key,s,new Gr(t.toArray()),L.none())}}function Hi(e,t,r,n){return e instanceof Yi?(s=t,a=r,o=n,Gi((i=e).precondition,s)?(l=i.value.clone(),u=es(i.fieldTransforms,o,s),l.setAll(u),s.convertToFoundDocument(s.version,l).setHasLocalMutations(),null):a):e instanceof Xi?(i=t,o=r,s=n,Gi((a=e).precondition,i)?(u=es(a.fieldTransforms,s,i),(l=i.data).setAll(Ji(a)),l.setAll(u),i.convertToFoundDocument(i.version,l).setHasLocalMutations(),null===o?null:o.unionWith(a.fieldMask.fields).unionWith(a.fieldTransforms.map(e=>e.field))):o):(n=t,t=r,Gi(e.precondition,n)?(n.convertToNoDocument(n.version).setHasLocalMutations(),null):t);var i,s,a,o,l,u}function Wi(e,t){return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(r=e.fieldTransforms,n=t.fieldTransforms,!!(void 0===r&&void 0===n||r&&n&&Ve(r,n,(e,t)=>(t=t,(e=e).field.isEqual(t.field)&&(e=e.transform,t=t.transform,e instanceof Vi&&t instanceof Vi||e instanceof Fi&&t instanceof Fi?Ve(e.elements,t.elements,gn):e instanceof Ui&&t instanceof Ui?gn(e.Re,t.Re):e instanceof Li&&t instanceof Li)))))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask));var r,n}class Yi extends $i{constructor(e,t,r,n=[]){super(),this.key=e,this.value=t,this.precondition=r,this.fieldTransforms=n,this.type=0}getFieldMask(){return null}}class Xi extends $i{constructor(e,t,r,n,i=[]){super(),this.key=e,this.data=t,this.fieldMask=r,this.precondition=n,this.fieldTransforms=i,this.type=1}getFieldMask(){return this.fieldMask}}function Ji(r){let n=new Map;return r.fieldMask.fields.forEach(e=>{var t;e.isEmpty()||(t=r.data.field(e),n.set(e,t))}),n}function Zi(e,t,r){var n,i,s,a=new Map;y(e.length===r.length,32656,{Ve:r.length,me:e.length});for(let h=0;h<r.length;h++){var o=e[h],l=o.transform,u=t.data.field(o.field);a.set(o.field,(n=l,i=u,s=r[h],n instanceof Vi?Pi(n,i):n instanceof Fi?Bi(n,i):s))}return a}function es(e,r,n){var i,s,a,o,l,u,h,c=new Map;for(i of e){let e=i.transform,t=n.data.field(i.field);c.set(i.field,(s=e,a=t,o=r,h=u=l=void 0,s instanceof Li?(o=o,u=a,h={fields:{[Xr]:{stringValue:Yr},[Zr]:{timestampValue:{seconds:o.seconds,nanos:o.nanoseconds}}}},(u=u&&en(u)?tn(u):u)&&(h.fields[Jr]=u),{mapValue:h}):s instanceof Vi?Pi(s,a):s instanceof Fi?Bi(s,a):(h=Mi(o=s,a),l=qi(h)+qi(o.Re),bn(h)&&bn(o.Re)?ki(l):Ni(o.serializer,l))))}return c}class ts extends $i{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class rs extends $i{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class ns{constructor(e,t,r,n){this.batchId=e,this.localWriteTime=t,this.baseMutations=r,this.mutations=n}applyToRemoteDocument(e,t){var r,n,i,s,a,o,l,u=t.mutationResults;for(let c=0;c<this.mutations.length;c++){var h=this.mutations[c];h.key.isEqual(e.key)&&(r=h,n=e,i=u[c],l=h=o=a=s=void 0,r instanceof Yi?(a=n,o=i,h=(s=r).value.clone(),l=Zi(s.fieldTransforms,a,o.transformResults),h.setAll(l),a.convertToFoundDocument(o.version,h).setHasCommittedMutations()):r instanceof Xi?(s=n,a=i,Gi((o=r).precondition,s)?(l=Zi(o.fieldTransforms,s,a.transformResults),(h=s.data).setAll(Ji(o)),h.setAll(l),s.convertToFoundDocument(a.version,h).setHasCommittedMutations()):s.convertToUnknownDocument(a.version)):n.convertToNoDocument(i.version).setHasCommittedMutations())}}applyToLocalView(e,t){for(var r of this.baseMutations)r.key.isEqual(e.key)&&(t=Hi(r,e,t,this.localWriteTime));for(var n of this.mutations)n.key.isEqual(e.key)&&(t=Hi(n,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(n,i){let s=xi();return this.mutations.forEach(e=>{var t=n.get(e.key),r=t.overlayedDocument,t=this.applyToLocalView(r,t.mutatedFields),t=Qi(r,i.has(e.key)?null:t);null!==t&&s.set(e.key,t),r.isValidDocument()||r.convertToNoDocument(g.min())}),s}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),M())}isEqual(e){return this.batchId===e.batchId&&Ve(this.mutations,e.mutations,(e,t)=>Wi(e,t))&&Ve(this.baseMutations,e.baseMutations,(e,t)=>Wi(e,t))}}class is{constructor(e,t,r,n){this.batch=e,this.commitVersion=t,this.mutationResults=r,this.docVersions=n}static from(e,t,r){y(e.mutations.length===r.length,58842,{fe:e.mutations.length,ge:r.length});let n=Ci;var i=e.mutations;for(let s=0;s<i.length;s++)n=n.insert(i[s].key,r[s].version);return new is(e,t,r,n)}}class ss{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class as{constructor(e,t){this.count=e,this.unchangedNames=t}}function os(e){switch(e){case b.OK:return E(64938);case b.CANCELLED:case b.UNKNOWN:case b.DEADLINE_EXCEEDED:case b.RESOURCE_EXHAUSTED:case b.INTERNAL:case b.UNAVAILABLE:case b.UNAUTHENTICATED:return!1;case b.INVALID_ARGUMENT:case b.NOT_FOUND:case b.ALREADY_EXISTS:case b.PERMISSION_DENIED:case b.FAILED_PRECONDITION:case b.ABORTED:case b.OUT_OF_RANGE:case b.UNIMPLEMENTED:case b.DATA_LOSS:return!0;default:return E(15467,{code:e})}}function ls(e){if(void 0===e)return d("GRPC error has no .code"),b.UNKNOWN;switch(e){case m.OK:return b.OK;case m.CANCELLED:return b.CANCELLED;case m.UNKNOWN:return b.UNKNOWN;case m.DEADLINE_EXCEEDED:return b.DEADLINE_EXCEEDED;case m.RESOURCE_EXHAUSTED:return b.RESOURCE_EXHAUSTED;case m.INTERNAL:return b.INTERNAL;case m.UNAVAILABLE:return b.UNAVAILABLE;case m.UNAUTHENTICATED:return b.UNAUTHENTICATED;case m.INVALID_ARGUMENT:return b.INVALID_ARGUMENT;case m.NOT_FOUND:return b.NOT_FOUND;case m.ALREADY_EXISTS:return b.ALREADY_EXISTS;case m.PERMISSION_DENIED:return b.PERMISSION_DENIED;case m.FAILED_PRECONDITION:return b.FAILED_PRECONDITION;case m.ABORTED:return b.ABORTED;case m.OUT_OF_RANGE:return b.OUT_OF_RANGE;case m.UNIMPLEMENTED:return b.UNIMPLEMENTED;case m.DATA_LOSS:return b.DATA_LOSS;default:return E(39323,{code:e})}}(e=m=m||{})[e.OK=0]="OK",e[e.CANCELLED=1]="CANCELLED",e[e.UNKNOWN=2]="UNKNOWN",e[e.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",e[e.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",e[e.NOT_FOUND=5]="NOT_FOUND",e[e.ALREADY_EXISTS=6]="ALREADY_EXISTS",e[e.PERMISSION_DENIED=7]="PERMISSION_DENIED",e[e.UNAUTHENTICATED=16]="UNAUTHENTICATED",e[e.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",e[e.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",e[e.ABORTED=10]="ABORTED",e[e.OUT_OF_RANGE=11]="OUT_OF_RANGE",e[e.UNIMPLEMENTED=12]="UNIMPLEMENTED",e[e.INTERNAL=13]="INTERNAL",e[e.UNAVAILABLE=14]="UNAVAILABLE",e[e.DATA_LOSS=15]="DATA_LOSS";let us=new ge([4294967295,4294967295],0);function hs(e){var t=Re().encode(e),r=new me;return r.update(t),new Uint8Array(r.digest())}function cs(e){var t=new DataView(e.buffer),r=t.getUint32(0,!0),n=t.getUint32(4,!0),i=t.getUint32(8,!0),t=t.getUint32(12,!0);return[new ge([r,n],0),new ge([i,t],0)]}class ds{constructor(e,t,r){if(this.bitmap=e,this.padding=t,this.hashCount=r,t<0||8<=t)throw new fs("Invalid padding: "+t);if(r<0)throw new fs("Invalid hash count: "+r);if(0<e.length&&0===this.hashCount)throw new fs("Invalid hash count: "+r);if(0===e.length&&0!==t)throw new fs("Invalid padding when bitmap length is 0: "+t);this.pe=8*e.length-t,this.ye=ge.fromNumber(this.pe)}we(e,t,r){let n=e.add(t.multiply(ge.fromNumber(r)));return(n=1===n.compare(us)?new ge([n.getBits(0),n.getBits(1)],0):n).modulo(this.ye).toNumber()}be(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.pe)return!1;let t=hs(e),[r,n]=cs(t);for(let i=0;i<this.hashCount;i++){let e=this.we(r,n,i);if(!this.be(e))return!1}return!0}static create(e,t,r){let n=e%8==0?0:8-e%8,i=new Uint8Array(Math.ceil(e/8)),s=new ds(i,n,t);return r.forEach(e=>s.insert(e)),s}insert(i){if(0!==this.pe){let e=hs(i),[t,r]=cs(e);for(let n=0;n<this.hashCount;n++){let e=this.we(t,r,n);this.Se(e)}}}Se(e){var t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class fs extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class gs{constructor(e,t,r,n,i){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=r,this.documentUpdates=n,this.resolvedLimboDocuments=i}static createSynthesizedRemoteEventForCurrentChange(e,t,r){var n=new Map;return n.set(e,ms.createSynthesizedTargetChangeForCurrentChange(e,t,r)),new gs(g.min(),n,new C(S),Ii,M())}}class ms{constructor(e,t,r,n,i){this.resumeToken=e,this.current=t,this.addedDocuments=r,this.modifiedDocuments=n,this.removedDocuments=i}static createSynthesizedTargetChangeForCurrentChange(e,t,r){return new ms(r,t,M(),M(),M())}}class ps{constructor(e,t,r,n){this.De=e,this.removedTargetIds=t,this.key=r,this.ve=n}}class ys{constructor(e,t){this.targetId=e,this.Ce=t}}class vs{constructor(e,t,r=D.EMPTY_BYTE_STRING,n=null){this.state=e,this.targetIds=t,this.resumeToken=r,this.cause=n}}class ws{constructor(){this.Fe=0,this.Me=Is(),this.xe=D.EMPTY_BYTE_STRING,this.Oe=!1,this.Ne=!0}get current(){return this.Oe}get resumeToken(){return this.xe}get Be(){return 0!==this.Fe}get Le(){return this.Ne}ke(e){0<e.approximateByteSize()&&(this.Ne=!0,this.xe=e)}qe(){let r=M(),n=M(),i=M();return this.Me.forEach((e,t)=>{switch(t){case 0:r=r.add(e);break;case 2:n=n.add(e);break;case 1:i=i.add(e);break;default:E(38017,{changeType:t})}}),new ms(this.xe,this.Oe,r,n,i)}Qe(){this.Ne=!1,this.Me=Is()}$e(e,t){this.Ne=!0,this.Me=this.Me.insert(e,t)}Ue(e){this.Ne=!0,this.Me=this.Me.remove(e)}Ke(){this.Fe+=1}We(){--this.Fe,y(0<=this.Fe,3241,{Fe:this.Fe})}Ge(){this.Ne=!0,this.Oe=!0}}class _s{constructor(e){this.ze=e,this.je=new Map,this.He=Ii,this.Je=bs(),this.Ye=bs(),this.Ze=new C(S)}Xe(e){for(var t of e.De)e.ve&&e.ve.isFoundDocument()?this.et(t,e.ve):this.tt(t,e.key,e.ve);for(var r of e.removedTargetIds)this.tt(r,e.key,e.ve)}nt(r){this.forEachTarget(r,e=>{var t=this.rt(e);switch(r.state){case 0:this.it(e)&&t.ke(r.resumeToken);break;case 1:t.We(),t.Be||t.Qe(),t.ke(r.resumeToken);break;case 2:t.We(),t.Be||this.removeTarget(e);break;case 3:this.it(e)&&(t.Ge(),t.ke(r.resumeToken));break;case 4:this.it(e)&&(this.st(e),t.ke(r.resumeToken));break;default:E(56790,{state:r.state})}})}forEachTarget(e,r){0<e.targetIds.length?e.targetIds.forEach(r):this.je.forEach((e,t)=>{this.it(t)&&r(t)})}ot(n){let i=n.targetId,e=n.Ce.count,t=this._t(i);if(t){var r=t.target;if(ri(r))if(0===e){let e=new x(r.path);this.tt(i,e,k.newNoDocument(e,g.min()))}else y(1===e,20013,{expectedCount:e});else{let r=this.ut(i);if(r!==e){let e=this.ct(n),t=e?this.lt(e,n,r):1;if(0!==t){this.st(i);let e=2===t?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch";this.Ze=this.Ze.insert(i,e)}}}}}ct(e){var t=e.Ce.unchangedNames;if(!t||!t.bits)return null;var{bits:{bitmap:t="",padding:r=0},hashCount:n=0}=t;let i,s;try{i=Wr(t).toUint8Array()}catch(e){if(e instanceof $r)return be("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{s=new ds(i,r,n)}catch(e){return be(e instanceof fs?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===s.pe?null:s}lt(e,t,r){return t.Ce.count===r-this.Tt(e,t.targetId)?0:2}Tt(r,n){var e=this.ze.getRemoteKeysForTarget(n);let i=0;return e.forEach(e=>{var t=this.ze.Pt(),t=`projects/${t.projectId}/databases/${t.database}/documents/`+e.path.canonicalString();r.mightContain(t)||(this.tt(n,e,null),i++)}),i}It(n){let i=new Map,s=(this.je.forEach((e,t)=>{var r=this._t(t);if(r){if(e.current&&ri(r.target)){let e=new x(r.target.path);this.Et(e).has(t)||this.dt(t,e)||this.tt(t,e,k.newNoDocument(e,n))}e.Le&&(i.set(t,e.qe()),e.Qe())}}),M());this.Ye.forEach((e,t)=>{let r=!0;t.forEachWhile(e=>{var t=this._t(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(r=!1)}),r&&(s=s.add(e))}),this.He.forEach((e,t)=>t.setReadTime(n));var e=new gs(n,i,this.Ze,this.He,s);return this.He=Ii,this.Je=bs(),this.Ye=bs(),this.Ze=new C(S),e}et(e,t){var r;this.it(e)&&(r=this.dt(e,t.key)?2:0,this.rt(e).$e(t.key,r),this.He=this.He.insert(t.key,t),this.Je=this.Je.insert(t.key,this.Et(t.key).add(e)),this.Ye=this.Ye.insert(t.key,this.At(t.key).add(e)))}tt(e,t,r){var n;this.it(e)&&(n=this.rt(e),this.dt(e,t)?n.$e(t,1):n.Ue(t),this.Ye=this.Ye.insert(t,this.At(t).delete(e)),this.Ye=this.Ye.insert(t,this.At(t).add(e)),r)&&(this.He=this.He.insert(t,r))}removeTarget(e){this.je.delete(e)}ut(e){var t=this.rt(e).qe();return this.ze.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ke(e){this.rt(e).Ke()}rt(e){let t=this.je.get(e);return t||(t=new ws,this.je.set(e,t)),t}At(e){let t=this.Ye.get(e);return t||(t=new A(S),this.Ye=this.Ye.insert(e,t)),t}Et(e){let t=this.Je.get(e);return t||(t=new A(S),this.Je=this.Je.insert(e,t)),t}it(e){var t=null!==this._t(e);return t||p("WatchChangeAggregator","Detected inactive target",e),t}_t(e){var t=this.je.get(e);return t&&t.Be?null:this.ze.Rt(e)}st(t){this.je.set(t,new ws),this.ze.getRemoteKeysForTarget(t).forEach(e=>{this.tt(t,e,null)})}dt(e,t){return this.ze.getRemoteKeysForTarget(e).has(t)}}function bs(){return new C(x.comparator)}function Is(){return new C(x.comparator)}let Ts={asc:"ASCENDING",desc:"DESCENDING"},Es={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},Ss={and:"AND",or:"OR"};class xs{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function Cs(e,t){return e.useProto3Json||yt(t)?t:{value:t}}function As(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function Ds(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function V(e){return y(!!e,49232),g.fromTimestamp((t=Hr(e),new h(t.seconds,t.nanos)));var t}function Ns(e,t){return ks(e,t).canonicalString()}function ks(e,t){e=e;var r=new T(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?r:r.child(t)}function Rs(e){var t=T.fromString(e);return y(Js(t),10190,{key:t.toString()}),t}function Os(e,t){return Ns(e.databaseId,t.path)}function Ms(e,t){var r=Rs(t);if(r.get(1)!==e.databaseId.projectId)throw new I(b.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+r.get(1)+" vs "+e.databaseId.projectId);if(r.get(3)!==e.databaseId.database)throw new I(b.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+r.get(3)+" vs "+e.databaseId.database);return new x(Fs(r))}function Ls(e,t){return Ns(e.databaseId,t)}function Vs(e){var t=Rs(e);return 4===t.length?T.emptyPath():Fs(t)}function Ps(e){return new T(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function Fs(e){return y(4<e.length&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function Bs(e,t,r){return{name:Os(e,t),fields:r.value.mapValue.fields}}function Us(e,t,r){var n=Ms(e,t.name),i=V(t.updateTime),s=t.createTime?V(t.createTime):g.min(),a=new Rn({mapValue:{fields:t.fields}}),n=k.newFoundDocument(n,i,s,a);return r&&n.setHasCommittedMutations(),r?n.setHasCommittedMutations():n}function qs(e,t){let r;if(t instanceof Yi)r={update:Bs(e,t.key,t.value)};else if(t instanceof ts)r={delete:Os(e,t.key)};else if(t instanceof Xi)r={update:Bs(e,t.key,t.data),updateMask:(e=>{let t=[];return e.fields.forEach(e=>t.push(e.canonicalString())),{fieldPaths:t}})(t.fieldMask)};else{if(!(t instanceof rs))return E(16599,{ft:t.type});r={verify:Os(e,t.key)}}return 0<t.fieldTransforms.length&&(r.updateTransforms=t.fieldTransforms.map(e=>{var t=e.transform;if(t instanceof Li)return{fieldPath:e.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(t instanceof Vi)return{fieldPath:e.field.canonicalString(),appendMissingElements:{values:t.elements}};if(t instanceof Fi)return{fieldPath:e.field.canonicalString(),removeAllFromArray:{values:t.elements}};if(t instanceof Ui)return{fieldPath:e.field.canonicalString(),increment:t.Re};throw E(20930,{transform:e.transform})})),t.precondition.isNone||(r.currentDocument=(e=e,void 0!==(t=t.precondition).updateTime?{updateTime:(n=t.updateTime,As(e,n.toTimestamp()))}:void 0!==t.exists?{exists:t.exists}:E(27497))),r;var n}function js(i,t){let r=t.currentDocument?void 0!==(s=t.currentDocument).updateTime?L.updateTime(V(s.updateTime)):void 0!==s.exists?L.exists(s.exists):L.none():L.none(),n=t.updateTransforms?t.updateTransforms.map(r=>{{var e=i;let t=null;if("setToServerValue"in r)y("REQUEST_TIME"===r.setToServerValue,16630,{proto:r}),t=new Li;else if("appendMissingElements"in r){let e=r.appendMissingElements.values||[];t=new Vi(e)}else if("removeAllFromArray"in r){let e=r.removeAllFromArray.values||[];t=new Fi(e)}else"increment"in r?t=new Ui(e,r.increment):E(16584,{proto:r});var n=v.fromServerFormat(r.fieldPath);return new zi(n,t)}}):[];var s,a;if(t.update){t.update.name;var o=Ms(i,t.update.name),l=new Rn({mapValue:{fields:t.update.fields}});if(t.updateMask){s=t.updateMask,a=s.fieldPaths||[];let e=new Gr(a.map(e=>v.fromServerFormat(e)));return new Xi(o,l,e,r,n)}return new Yi(o,l,r,n)}if(t.delete){let e=Ms(i,t.delete);return new ts(e,r)}if(t.verify){let e=Ms(i,t.verify);return new rs(e,r)}return E(1463,{proto:t})}function zs(e,n){return e&&0<e.length?(y(void 0!==n,14353),e.map(t=>{{var r=n;let e=t.updateTime?V(t.updateTime):V(r);return e.isEqual(g.min())&&(e=V(r)),new Ki(e,t.transformResults||[])}})):[]}function Ks(e,t){return{documents:[Ls(e,t.path)]}}function Gs(e,t){var r={structuredQuery:{}},n=t.path;let i;null!==t.collectionGroup?(i=n,r.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(i=n.popLast(),r.structuredQuery.from=[{collectionId:n.lastSegment()}]),r.parent=Ls(e,i);n=(e=>{if(0!==e.length)return function r(e){return e instanceof R?(e=>{if("=="===e.op){if(En(e.value))return{unaryFilter:{field:Ys(e.field),op:"IS_NAN"}};if(Tn(e.value))return{unaryFilter:{field:Ys(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(En(e.value))return{unaryFilter:{field:Ys(e.field),op:"IS_NOT_NAN"}};if(Tn(e.value))return{unaryFilter:{field:Ys(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:Ys(e.field),op:Hs(e.op),value:e.value}}})(e):e instanceof O?(e=>{let t=e.getFilters().map(e=>r(e));return 1===t.length?t[0]:{compositeFilter:{op:Ws(e.op),filters:t}}})(e):E(54877,{filter:e})}(O.create(e,"and"))})(t.filters),n&&(r.structuredQuery.where=n),n=(e=>{if(0!==e.length)return e.map(e=>({field:Ys((e=e).field),direction:(e=e.dir,Ts[e])}))})(t.orderBy),n&&(r.structuredQuery.orderBy=n),n=Cs(e,t.limit);return null!==n&&(r.structuredQuery.limit=n),t.startAt&&(r.structuredQuery.startAt={before:(e=t.startAt).inclusive,values:e.position}),t.endAt&&(r.structuredQuery.endAt={before:!(e=t.endAt).inclusive,values:e.position}),{gt:r,parent:i}}function $s(e){let t=Vs(e.parent);var r,n=e.structuredQuery,i=n.from?n.from.length:0;let s=null;if(0<i){y(1===i,65062);let e=n.from[0];e.allDescendants?s=e.collectionId:t=t.child(e.collectionId)}let a=[],o=(n.where&&(a=(e=n.where,(i=function t(e){return void 0!==e.unaryFilter?(i=>{switch(i.unaryFilter.op){case"IS_NAN":let e=Xs(i.unaryFilter.field);return R.create(e,"==",{doubleValue:NaN});case"IS_NULL":let t=Xs(i.unaryFilter.field);return R.create(t,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let r=Xs(i.unaryFilter.field);return R.create(r,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let n=Xs(i.unaryFilter.field);return R.create(n,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return E(61313);default:return E(60726)}})(e):void 0!==e.fieldFilter?(e=>R.create(Xs(e.fieldFilter.field),(e=>{switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return E(58110);default:return E(50506)}})(e.fieldFilter.op),e.fieldFilter.value))(e):void 0!==e.compositeFilter?(e=>O.create(e.compositeFilter.filters.map(e=>t(e)),(e=>{switch(e){case"AND":return"and";case"OR":return"or";default:return E(1026)}})(e.compositeFilter.op)))(e):E(30097,{filter:e})}(e))instanceof O&&Un(i)?i.getFilters():[i])),[]),l=(n.orderBy&&(o=n.orderBy.map(e=>(e=e,new Vn(Xs(e.field),(e=>{switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}})(e.direction))))),null),u=(n.limit&&(l=(e=n.limit,yt(i="object"==typeof e?e.value:e)?null:i)),null),h=(n.startAt&&(u=(e=n.startAt,i=!!e.before,r=e.values||[],new On(r,i))),null);return n.endAt&&(h=(e=n.endAt,r=!e.before,i=e.values||[],new On(i,r))),oi(t,s,o,a,l,"F",u,h)}function Qs(e,t){var r=(e=>{switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return E(28987,{purpose:e})}})(t.purpose);return null==r?null:{"goog-listen-tags":r}}function Hs(e){return Es[e]}function Ws(e){return Ss[e]}function Ys(e){return{fieldPath:e.canonicalString()}}function Xs(e){return v.fromServerFormat(e.fieldPath)}function Js(e){return 4<=e.length&&"projects"===e.get(0)&&"databases"===e.get(2)}class Zs{constructor(e,t,r,n,i=g.min(),s=g.min(),a=D.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=r,this.sequenceNumber=n,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=s,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new Zs(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new Zs(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new Zs(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new Zs(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class ea{constructor(e){this.wt=e}}function ta(e,t){var r,n=t.key,i={prefixPath:n.getCollectionPath().popLast().toArray(),collectionGroup:n.collectionGroup,documentId:n.path.lastSegment(),readTime:ra(t.readTime),hasCommittedMutations:t.hasCommittedMutations};if(t.isFoundDocument())i.document={name:Os(e=e.wt,(r=t).key),fields:r.data.value.mapValue.fields,updateTime:As(e,r.version.toTimestamp()),createTime:As(e,r.createTime.toTimestamp())};else if(t.isNoDocument())i.noDocument={path:n.path.toArray(),readTime:na(t.version)};else{if(!t.isUnknownDocument())return E(57904,{document:t});i.unknownDocument={path:n.path.toArray(),version:na(t.version)}}return i}function ra(e){var t=e.toTimestamp();return[t.seconds,t.nanoseconds]}function na(e){var t=e.toTimestamp();return{seconds:t.seconds,nanoseconds:t.nanoseconds}}function ia(e){var t=new h(e.seconds,e.nanoseconds);return g.fromTimestamp(t)}function sa(t,r){let e=(r.baseMutations||[]).map(e=>js(t.wt,e));for(let s=0;s<r.mutations.length-1;++s){let t=r.mutations[s];if(s+1<r.mutations.length&&void 0!==r.mutations[s+1].transform){let e=r.mutations[s+1];t.updateTransforms=e.transform.fieldTransforms,r.mutations.splice(s+1,1),++s}}let n=r.mutations.map(e=>js(t.wt,e)),i=h.fromMillis(r.localWriteTimeMs);return new ns(r.batchId,i,e,n)}function aa(e){var t,r=ia(e.readTime),n=void 0!==e.lastLimboFreeSnapshotVersion?ia(e.lastLimboFreeSnapshotVersion):g.min(),i=void 0!==e.query.documents?(t=e.query,y(1===(i=t.documents.length),1966,{count:i}),di(li(Vs(t.documents[0])))):di($s(e.query));return new Zs(i,e.targetId,"TargetPurposeListen",e.lastListenSequenceNumber,r,n,D.fromBase64String(e.resumeToken))}function oa(e,t){var r=na(t.snapshotVersion),n=na(t.lastLimboFreeSnapshotVersion),i=ri(t.target)?Ks(e.wt,t.target):Gs(e.wt,t.target).gt,s=t.resumeToken.toBase64();return{targetId:t.targetId,canonicalId:ei(t.target),readTime:r,resumeToken:s,lastListenSequenceNumber:t.sequenceNumber,lastLimboFreeSnapshotVersion:n,query:i}}function la(e){var t=$s({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?gi(t,t.limit,"L"):t}function ua(e,t){return new ss(t.largestBatchId,js(e.wt,t.overlayMutation))}function ha(e,t){var r=t.path.lastSegment();return[e,o(t.path.popLast()),r]}function ca(e,t,r,n){return{indexId:e,uid:t,sequenceNumber:r,readTime:na(n.readTime),documentKey:o(n.documentKey.path),largestBatchId:n.largestBatchId}}class da{getBundleMetadata(e,t){return fa(e).get(t).next(e=>{if(e)return{id:(e=e).bundleId,createTime:ia(e.createTime),version:e.version}})}saveBundleMetadata(e,t){return fa(e).put({bundleId:(e=t).id,createTime:na(V(e.createTime)),version:e.version})}getNamedQuery(e,t){return ga(e).get(t).next(e=>{if(e)return{name:(e=e).name,query:la(e.bundledQuery),readTime:ia(e.readTime)}})}saveNamedQuery(e,t){return ga(e).put({name:(e=t).name,readTime:na(V(e.readTime)),bundledQuery:e.bundledQuery})}}function fa(e){return r(e,tr)}function ga(e){return r(e,rr)}class ma{constructor(e,t){this.serializer=e,this.userId=t}static bt(e,t){var r=t.uid||"";return new ma(e,r)}getOverlay(e,t){return pa(e).get(ha(this.userId,t)).next(e=>e?ua(this.serializer,e):null)}getOverlays(e,t){let r=xi();return w.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(n,i,e){let s=[];return e.forEach((e,t)=>{var r=new ss(i,t);s.push(this.St(n,r))}),w.waitFor(s)}removeOverlaysForBatchId(r,e,n){let t=new Set,i=(e.forEach(e=>t.add(o(e.getCollectionPath()))),[]);return t.forEach(e=>{var t=IDBKeyRange.bound([this.userId,e,n],[this.userId,e,n+1],!1,!0);i.push(pa(r).X(mr,t))}),w.waitFor(i)}getOverlaysForCollection(e,t,r){let n=xi(),i=o(t),s=IDBKeyRange.bound([this.userId,i,r],[this.userId,i,Number.POSITIVE_INFINITY],!0);return pa(e).J(mr,s).next(e=>{for(var t of e){let e=ua(this.serializer,t);n.set(e.getKey(),e)}return n})}getOverlaysForCollectionGroup(e,t,r,i){let s=xi(),a;var n=IDBKeyRange.bound([this.userId,t,r],[this.userId,t,Number.POSITIVE_INFINITY],!0);return pa(e).te({index:xr,range:n},(e,t,r)=>{var n=ua(this.serializer,t);s.size()<i||n.largestBatchId===a?(s.set(n.getKey(),n),a=n.largestBatchId):r.done()}).next(()=>s)}St(e,t){return pa(e).put(((e,t,r)=>{var[,n,i]=ha(t,r.mutation.key);return{userId:t,collectionPath:n,documentId:i,collectionGroup:r.mutation.key.getCollectionGroup(),largestBatchId:r.largestBatchId,overlayMutation:qs(e.wt,r.mutation)}})(this.serializer,this.userId,t))}}function pa(e){return r(e,fr)}class ya{Dt(e){return r(e,Ar)}getSessionToken(e){return this.Dt(e).get("sessionToken").next(e=>{var t=null==e?void 0:e.value;return t?D.fromUint8Array(t):D.EMPTY_BYTE_STRING})}setSessionToken(e,t){return this.Dt(e).put({name:"sessionToken",value:t.toUint8Array()})}}class va{constructor(){}vt(e,t){this.Ct(e,t),t.Ft()}Ct(t,r){if("nullValue"in t)this.Mt(r,5);else if("booleanValue"in t)this.Mt(r,10),r.xt(t.booleanValue?1:0);else if("integerValue"in t)this.Mt(r,15),r.xt(N(t.integerValue));else if("doubleValue"in t){var e=N(t.doubleValue);isNaN(e)?this.Mt(r,13):(this.Mt(r,15),vt(e)?r.xt(0):r.xt(e))}else if("timestampValue"in t){let e=t.timestampValue;this.Mt(r,20),"string"==typeof e&&(e=Hr(e)),r.Ot(""+(e.seconds||"")),r.xt(e.nanos||0)}else"stringValue"in t?(this.Nt(t.stringValue,r),this.Bt(r)):"bytesValue"in t?(this.Mt(r,30),r.Lt(Wr(t.bytesValue)),this.Bt(r)):"referenceValue"in t?this.kt(t.referenceValue,r):"geoPointValue"in t?(e=t.geoPointValue,this.Mt(r,45),r.xt(e.latitude||0),r.xt(e.longitude||0)):"mapValue"in t?An(t)?this.Mt(r,Number.MAX_SAFE_INTEGER):xn(t)?this.qt(t.mapValue,r):(this.Qt(t.mapValue,r),this.Bt(r)):"arrayValue"in t?(this.$t(t.arrayValue,r),this.Bt(r)):E(19022,{Ut:t})}Nt(e,t){this.Mt(t,25),this.Kt(e,t)}Kt(e,t){t.Ot(e)}Qt(e,t){var r=e.fields||{};this.Mt(t,55);for(let e of Object.keys(r))this.Nt(e,t),this.Ct(r[e],t)}qt(e,t){var r=e.fields||{},n=(this.Mt(t,53),cn),i=(null==(i=null==(i=r[n].arrayValue)?void 0:i.values)?void 0:i.length)||0;this.Mt(t,15),t.xt(N(i)),this.Nt(n,t),this.Ct(r[n],t)}$t(e,t){var r=e.values||[];this.Mt(t,50);for(let e of r)this.Ct(e,t)}kt(e,t){this.Mt(t,37),x.fromName(e).path.forEach(e=>{this.Mt(t,60),this.Kt(e,t)})}Mt(e,t){e.xt(t)}Bt(e){e.xt(2)}}va.Wt=new va;function wa(e){var t=64-(e=>{let t=0;for(let n=0;n<8;++n){var r=(e=>{if(0===e)return 8;let t=0;return e>>4||(t+=4,e<<=4),e>>6||(t+=2,e<<=2),e>>7||(t+=1),t})(255&e[n]);if(t+=r,8!==r)break}return t})(e);return Math.ceil(t/8)}class _a{constructor(){this.buffer=new Uint8Array(1024),this.position=0}Gt(e){var t=e[Symbol.iterator]();let r=t.next();for(;!r.done;)this.zt(r.value),r=t.next();this.jt()}Ht(e){var t=e[Symbol.iterator]();let r=t.next();for(;!r.done;)this.Jt(r.value),r=t.next();this.Yt()}Zt(e){for(var t of e){let e=t.charCodeAt(0);if(e<128)this.zt(e);else if(e<2048)this.zt(960|e>>>6),this.zt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.zt(480|e>>>12),this.zt(128|63&e>>>6),this.zt(128|63&e);else{let e=t.codePointAt(0);this.zt(240|e>>>18),this.zt(128|63&e>>>12),this.zt(128|63&e>>>6),this.zt(128|63&e)}}this.jt()}Xt(e){for(var t of e){let e=t.charCodeAt(0);if(e<128)this.Jt(e);else if(e<2048)this.Jt(960|e>>>6),this.Jt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.Jt(480|e>>>12),this.Jt(128|63&e>>>6),this.Jt(128|63&e);else{let e=t.codePointAt(0);this.Jt(240|e>>>18),this.Jt(128|63&e>>>12),this.Jt(128|63&e>>>6),this.Jt(128|63&e)}}this.Yt()}en(e){var t=this.tn(e),r=wa(t);this.nn(1+r),this.buffer[this.position++]=255&r;for(let n=t.length-r;n<t.length;++n)this.buffer[this.position++]=255&t[n]}rn(e){var t=this.tn(e),r=wa(t);this.nn(1+r),this.buffer[this.position++]=~(255&r);for(let n=t.length-r;n<t.length;++n)this.buffer[this.position++]=~(255&t[n])}sn(){this._n(255),this._n(255)}an(){this.un(255),this.un(255)}reset(){this.position=0}seed(e){this.nn(e.length),this.buffer.set(e,this.position),this.position+=e.length}cn(){return this.buffer.slice(0,this.position)}tn(e){e=e,(t=new DataView(new ArrayBuffer(8))).setFloat64(0,e,!1);var t,r=new Uint8Array(t.buffer),n=!!(128&r[0]);r[0]^=n?255:128;for(let i=1;i<r.length;++i)r[i]^=n?255:0;return r}zt(e){var t=255&e;0==t?(this._n(0),this._n(255)):255==t?(this._n(255),this._n(0)):this._n(t)}Jt(e){var t=255&e;0==t?(this.un(0),this.un(255)):255==t?(this.un(255),this.un(0)):this.un(e)}jt(){this._n(0),this._n(1)}Yt(){this.un(0),this.un(1)}_n(e){this.nn(1),this.buffer[this.position++]=e}un(e){this.nn(1),this.buffer[this.position++]=~e}nn(e){var t=e+this.position;if(!(t<=this.buffer.length)){let e=2*this.buffer.length;e<t&&(e=t);t=new Uint8Array(e);t.set(this.buffer),this.buffer=t}}}class ba{constructor(e){this.ln=e}Lt(e){this.ln.Gt(e)}Ot(e){this.ln.Zt(e)}xt(e){this.ln.en(e)}Ft(){this.ln.sn()}}class Ia{constructor(e){this.ln=e}Lt(e){this.ln.Ht(e)}Ot(e){this.ln.Xt(e)}xt(e){this.ln.rn(e)}Ft(){this.ln.an()}}class Ta{constructor(){this.ln=new _a,this.hn=new ba(this.ln),this.Pn=new Ia(this.ln)}seed(e){this.ln.seed(e)}Tn(e){return 0===e?this.hn:this.Pn}cn(){return this.ln.cn()}reset(){this.ln.reset()}}class Ea{constructor(e,t,r,n){this.In=e,this.En=t,this.dn=r,this.An=n}Rn(){var e=this.An.length,t=0===e||255===this.An[e-1]?e+1:e,r=new Uint8Array(t);return r.set(this.An,0),t!==e?r.set([0],this.An.length):++r[r.length-1],new Ea(this.In,this.En,this.dn,r)}Vn(e,t,r){return{indexId:this.In,uid:e,arrayValue:Ca(this.dn),directionalValue:Ca(this.An),orderedDocumentKey:Ca(t),documentKey:r.path.toArray()}}mn(e,t,r){var n=this.Vn(e,t,r);return[n.indexId,n.uid,n.arrayValue,n.directionalValue,n.orderedDocumentKey,n.documentKey]}}function Sa(e,t){var r=e.In-t.In;return 0!=r||0!==(r=xa(e.dn,t.dn))||0!==(r=xa(e.An,t.An))?r:x.comparator(e.En,t.En)}function xa(e,t){for(let n=0;n<e.length&&n<t.length;++n){var r=e[n]-t[n];if(0!=r)return r}return e.length-t.length}function Ca(r){if(ne()){var n=r;let e="";for(let t=0;t<n.length;t++)e+=String.fromCharCode(n[t]);return e}return r}function Aa(e){if("string"!=typeof e)return e;var t=e,r=new Uint8Array(t.length);for(let n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r}class Da{constructor(e){this.fn=new A((e,t)=>v.comparator(e.field,t.field)),this.collectionId=null!=e.collectionGroup?e.collectionGroup:e.path.lastSegment(),this.gn=e.orderBy,this.pn=[];for(var t of e.filters){let e=t;e.isInequality()?this.fn=this.fn.add(e):this.pn.push(e)}}get yn(){return 1<this.fn.size}wn(e){if(y(e.collectionGroup===this.collectionId,49279),this.yn)return!1;let t=Ke(e);if(void 0!==t&&!this.bn(t))return!1;var r=Ge(e);let n=new Set,i=0,s=0;for(;i<r.length&&this.bn(r[i]);++i)n=n.add(r[i].fieldPath.canonicalString());if(i!==r.length){if(0<this.fn.size){let t=this.fn.getIterator().getNext();if(!n.has(t.field.canonicalString())){let e=r[i];if(!this.Sn(t,e)||!this.Dn(this.gn[s++],e))return!1}++i}for(;i<r.length;++i){let e=r[i];if(s>=this.gn.length||!this.Dn(this.gn[s++],e))return!1}}return!0}vn(){if(this.yn)return null;let e=new A(v.comparator);var t,r,n=[];for(t of this.pn)t.field.isKeyField()||("array-contains"===t.op||"array-contains-any"===t.op?n.push(new $e(t.field,2)):e.has(t.field)||(e=e.add(t.field),n.push(new $e(t.field,0))));for(r of this.gn)r.field.isKeyField()||e.has(r.field)||(e=e.add(r.field),n.push(new $e(r.field,"asc"===r.dir?0:1)));return new ze(ze.UNKNOWN_ID,this.collectionId,n,Qe.empty())}bn(e){for(var t of this.pn)if(this.Sn(t,e))return!0;return!1}Sn(e,t){var r;return!(void 0===e||!e.field.isEqual(t.fieldPath))&&(r="array-contains"===e.op||"array-contains-any"===e.op,2===t.kind==r)}Dn(e,t){return!!e.field.isEqual(t.fieldPath)&&(0===t.kind&&"asc"===e.dir||1===t.kind&&"desc"===e.dir)}}function Na(e){var t;return 0===e.getFilters().length?[]:(t=function t(e){if(y(e instanceof R||e instanceof O,34018),e instanceof R)return e;if(1===e.filters.length)return t(e.filters[0]);let r=e.filters.map(e=>t(e));let n=O.create(r,e.op);return n=Va(n),Oa(n)?n:(y(n instanceof O,64498),y(Fn(n),40251),y(1<n.filters.length,57927),n.filters.reduce((e,t)=>Ma(e,t)))}(function t(r){var n;if(y(r instanceof R||r instanceof O,20012),r instanceof R){if(r instanceof Wn){let e=(null==(n=null==(n=r.value.arrayValue)?void 0:n.values)?void 0:n.map(e=>R.create(r.field,"==",e)))||[];return O.create(e,"or")}return r}let e=r.filters.map(e=>t(e));return O.create(e,r.op)}(e)),y(Oa(t),7391),ka(t)||Ra(t)?[t]:t.getFilters())}function ka(e){return e instanceof R}function Ra(e){return e instanceof O&&Un(e)}function Oa(e){return ka(e)||Ra(e)||(e=>{if(e instanceof O&&Bn(e)){for(var t of e.getFilters())if(!ka(t)&&!Ra(t))return!1;return!0}return!1})(e)}function Ma(e,t){var r,n;return y(e instanceof R||e instanceof O,38388),y(t instanceof R||t instanceof O,25473),Va(e instanceof R?t instanceof R?(r=e,n=t,O.create([r,n],"and")):La(e,t):t instanceof R?La(t,e):((e,t)=>{if(y(0<e.filters.length&&0<t.filters.length,48005),Fn(e)&&Fn(t))return jn(e,t.getFilters());let r=Bn(e)?e:t,n=Bn(e)?t:e,i=r.filters.map(e=>Ma(e,n));return O.create(i,"or")})(e,t))}function La(t,e){var r;return Fn(e)?jn(e,t.getFilters()):(r=e.filters.map(e=>Ma(t,e)),O.create(r,"or"))}function Va(t){if(y(t instanceof R||t instanceof O,11850),t instanceof R)return t;var e=t.getFilters();if(1===e.length)return Va(e[0]);if(qn(t))return t;let r=e.map(e=>Va(e)),n=[];return r.forEach(e=>{e instanceof R?n.push(e):e instanceof O&&(e.op===t.op?n.push(...e.filters):n.push(e))}),1===n.length?n[0]:O.create(n,t.op)}class Pa{constructor(){this.Cn=new Fa}addToCollectionParentIndex(e,t){return this.Cn.add(t),w.resolve()}getCollectionParents(e,t){return w.resolve(this.Cn.getEntries(t))}addFieldIndex(e,t){return w.resolve()}deleteFieldIndex(e,t){return w.resolve()}deleteAllFieldIndexes(e){return w.resolve()}createTargetIndexes(e,t){return w.resolve()}getDocumentsMatchingTarget(e,t){return w.resolve(null)}getIndexType(e,t){return w.resolve(0)}getFieldIndexes(e,t){return w.resolve([])}getNextCollectionGroupToUpdate(e){return w.resolve(null)}getMinOffset(e,t){return w.resolve(Ye.min())}getMinOffsetFromCollectionGroup(e,t){return w.resolve(Ye.min())}updateCollectionGroup(e,t,r){return w.resolve()}updateIndexEntries(e,t){return w.resolve()}}class Fa{constructor(){this.index={}}add(e){var t=e.lastSegment(),r=e.popLast(),n=this.index[t]||new A(T.comparator),i=!n.has(r);return this.index[t]=n.add(r),i}has(e){var t=e.lastSegment(),r=e.popLast(),t=this.index[t];return t&&t.has(r)}getEntries(e){return(this.index[e]||new A(T.comparator)).toArray()}}let Ba="IndexedDbIndexManager",Ua=new Uint8Array(0);class qa{constructor(e,t){this.databaseId=t,this.Fn=new Fa,this.Mn=new bi(e=>ei(e),(e,t)=>ti(e,t)),this.uid=e.uid||""}addToCollectionParentIndex(e,t){var r,n;return this.Fn.has(t)?w.resolve():(n=t.lastSegment(),r=t.popLast(),e.addOnCommittedListener(()=>{this.Fn.add(t)}),n={collectionId:n,parent:o(r)},ja(e).put(n))}getCollectionParents(e,r){let n=[],t=IDBKeyRange.bound([r,""],[Pe(r),""],!1,!0);return ja(e).J(t).next(e=>{for(var t of e){if(t.collectionId!==r)break;n.push(It(t.parent))}return n})}addFieldIndex(e,r){let t=Ka(e),n={indexId:r.indexId,collectionGroup:r.collectionGroup,fields:r.fields.map(e=>[e.fieldPath.canonicalString(),e.kind])};delete n.indexId;var i=t.add(n);if(r.indexState){let t=Ga(e);return i.next(e=>{t.put(ca(e,this.uid,r.indexState.sequenceNumber,r.indexState.offset))})}return i.next()}deleteFieldIndex(e,t){let r=Ka(e),n=Ga(e),i=za(e);return r.delete(t.indexId).next(()=>n.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0))).next(()=>i.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0)))}deleteAllFieldIndexes(e){let t=Ka(e),r=za(e),n=Ga(e);return t.X().next(()=>r.X()).next(()=>n.X())}createTargetIndexes(r,e){return w.forEach(this.xn(e),t=>this.getIndexType(r,t).next(e=>{if(0===e||1===e){let e=new Da(t).vn();if(null!=e)return this.addFieldIndex(r,e)}}))}getDocumentsMatchingTarget(e,c){let d=za(e),r=!0,n=new Map;return w.forEach(this.xn(c),t=>this.On(e,t).next(e=>{r=r&&!!e,n.set(t,e)})).next(()=>{if(r){let u=M(),h=[];return w.forEach(n,(e,t)=>{p(Ba,`Using index ${r=e,`id=${r.indexId}|cg=${r.collectionGroup}|f=`+r.fields.map(e=>e.fieldPath+":"+e.kind).join(",")} to execute `+ei(c));var r,n=((t,e)=>{var r=Ke(e);if(void 0!==r)for(let e of ni(t,r.fieldPath))switch(e.op){case"array-contains-any":return e.value.arrayValue.values||[];case"array-contains":return[e.value]}return null})(t,e),i=((t,r)=>{var n,i=new Map;for(n of Ge(r))for(let e of ni(t,n.fieldPath))switch(e.op){case"==":case"in":i.set(n.fieldPath.canonicalString(),e.value);break;case"not-in":case"!=":return i.set(n.fieldPath.canonicalString(),e.value),Array.from(i.values())}return null})(t,e),s=((t,e)=>{var r,n=[];let i=!0;for(r of Ge(e)){let e=(0===r.kind?ii:si)(t,r.fieldPath,t.startAt);n.push(e.value),i=i&&e.inclusive}return new On(n,i)})(t,e),a=((t,e)=>{var r,n=[];let i=!0;for(r of Ge(e)){let e=(0===r.kind?si:ii)(t,r.fieldPath,t.endAt);n.push(e.value),i=i&&e.inclusive}return new On(n,i)})(t,e),o=this.Nn(e,t,s),l=this.Nn(e,t,a),i=this.Bn(e,t,i),n=this.Ln(e.indexId,n,o,s.inclusive,l,a.inclusive,i);return w.forEach(n,e=>d.Z(e,c.limit).next(e=>{e.forEach(e=>{var t=x.fromSegments(e.documentKey);u.has(t)||(u=u.add(t),h.push(t))})}))}).next(()=>h)}return w.resolve(null)})}xn(t){let e=this.Mn.get(t);return e||(e=0===t.filters.length?[t]:Na(O.create(t.filters,"and")).map(e=>Zn(t.path,t.collectionGroup,t.orderBy,e.getFilters(),t.limit,t.startAt,t.endAt)),this.Mn.set(t,e)),e}Ln(i,s,a,o,l,u,h){let e=(null!=s?s.length:1)*Math.max(a.length,l.length),c=e/(null!=s?s.length:1),d=[];for(let f=0;f<e;++f){let t=s?this.kn(s[f/c]):Ua,e=this.qn(i,t,a[f%c],o),r=this.Qn(i,t,l[f%c],u),n=h.map(e=>this.qn(i,t,e,!0));d.push(...this.createRange(e,r,n))}return d}qn(e,t,r,n){var i=new Ea(e,x.empty(),t,r);return n?i:i.Rn()}Qn(e,t,r,n){var i=new Ea(e,x.empty(),t,r);return n?i.Rn():i}On(e,t){let n=new Da(t),r=null!=t.collectionGroup?t.collectionGroup:t.path.lastSegment();return this.getFieldIndexes(e,r).next(e=>{let t=null;for(var r of e)n.wn(r)&&(!t||r.fields.length>t.fields.length)&&(t=r);return t})}getIndexType(e,t){let r=2,n=this.xn(t);return w.forEach(n,t=>this.On(e,t).next(e=>{e?0!==r&&e.fields.length<(t=>{let r=new A(v.comparator),n=!1;for(var i of t.filters)for(let e of i.getFlattenedFilters())e.field.isKeyField()||("array-contains"===e.op||"array-contains-any"===e.op?n=!0:r=r.add(e.field));for(let n of t.orderBy)n.field.isKeyField()||(r=r.add(n.field));return r.size+(n?1:0)})(t)&&(r=1):r=0})).next(()=>null!==t.limit&&1<n.length&&2===r?1:r)}$n(e,t){var r,n=new Ta;for(r of Ge(e)){let e=t.data.field(r.fieldPath);if(null==e)return null;var i=n.Tn(r.kind);va.Wt.vt(e,i)}return n.cn()}kn(e){var t=new Ta;return va.Wt.vt(e,t.Tn(0)),t.cn()}Un(e,t){var r,n=new Ta;return va.Wt.vt(_n(this.databaseId,t),n.Tn(0===(r=Ge(e)).length?0:r[r.length-1].kind)),n.cn()}Bn(e,n,i){if(null===i)return[];let s=[],a=(s.push(new Ta),0);for(var o of Ge(e)){let t=i[a++];for(let r of s)if(this.Kn(n,o.fieldPath)&&In(t))s=this.Wn(s,o,t);else{let e=r.Tn(o.kind);va.Wt.vt(t,e)}}return this.Gn(s)}Nn(e,t,r){return this.Bn(e,t,r.position)}Gn(e){var t=[];for(let r=0;r<e.length;++r)t[r]=e[r].cn();return t}Wn(r,n,e){let i=[...r],s=[];for(let r of e.arrayValue.values||[])for(let t of i){let e=new Ta;e.seed(t.cn()),va.Wt.vt(r,e.Tn(n.kind)),s.push(e)}return s}Kn(e,t){return!!e.filters.find(e=>e instanceof R&&e.field.isEqual(t)&&("in"===e.op||"not-in"===e.op))}getFieldIndexes(e,t){let r=Ka(e),n=Ga(e);return(t?r.J(ir,IDBKeyRange.bound(t,t)):r.J()).next(e=>{let s=[];return w.forEach(e,i=>n.get([i.indexId,this.uid]).next(e=>{var t,r,n;s.push((t=i,r=(e=e)?new Qe(e.sequenceNumber,new Ye(ia(e.readTime),new x(It(e.documentKey)),e.largestBatchId)):Qe.empty(),n=t.fields.map(([e,t])=>new $e(v.fromServerFormat(e),t)),new ze(t.indexId,t.collectionGroup,n,r)))})).next(()=>s)})}getNextCollectionGroupToUpdate(e){return this.getFieldIndexes(e).next(e=>0===e.length?null:(e.sort((e,t)=>{var r=e.indexState.sequenceNumber-t.indexState.sequenceNumber;return 0!=r?r:S(e.collectionGroup,t.collectionGroup)}),e[0].collectionGroup))}updateCollectionGroup(e,r,n){let i=Ka(e),s=Ga(e);return this.zn(e).next(t=>i.J(ir,IDBKeyRange.bound(r,r)).next(e=>w.forEach(e,e=>s.put(ca(e.indexId,this.uid,t,n)))))}updateIndexEntries(i,e){let r=new Map;return w.forEach(e,(t,n)=>{var e=r.get(t.collectionGroup);return(e?w.resolve(e):this.getFieldIndexes(i,t.collectionGroup)).next(e=>(r.set(t.collectionGroup,e),w.forEach(e,r=>this.jn(i,t,r).next(e=>{var t=this.Hn(n,r);return e.isEqual(t)?w.resolve():this.Jn(i,n,r,e,t)}))))})}Yn(e,t,r,n){return za(e).put(n.Vn(this.uid,this.Un(r,t.key),t.key))}Zn(e,t,r,n){return za(e).delete(n.mn(this.uid,this.Un(r,t.key),t.key))}jn(e,r,n){var t=za(e);let i=new A(Sa);return t.te({index:cr,range:IDBKeyRange.only([n.indexId,this.uid,Ca(this.Un(n,r))])},(e,t)=>{i=i.add(new Ea(n.indexId,r,Aa(t.arrayValue),Aa(t.directionalValue)))}).next(()=>i)}Hn(t,r){let n=new A(Sa);var i=this.$n(r,t);if(null!=i){let e=Ke(r);if(null!=e){var s=t.data.field(e.fieldPath);if(In(s))for(let e of s.arrayValue.values||[])n=n.add(new Ea(r.indexId,t.key,this.kn(e),i))}else n=n.add(new Ea(r.indexId,t.key,Ua,i))}return n}Jn(t,r,s,e,a){p(Ba,"Updating index entries for document '%s'",r.key);let o=[];{var l=Sa,u=e=>{o.push(this.Yn(t,r,s,e))},h=e=>{o.push(this.Zn(t,r,s,e))},c=e.getIterator(),d=a.getIterator();let n=Kr(c),i=Kr(d);for(;n||i;){let t=!1,r=!1;if(n&&i){let e=l(n,i);e<0?r=!0:0<e&&(t=!0)}else null!=n?r=!0:t=!0;t?(u(i),i=Kr(d)):r?(h(n),n=Kr(c)):(n=Kr(c),i=Kr(d))}}return w.waitFor(o)}zn(e){let n=1;return Ga(e).te({index:or,reverse:!0,range:IDBKeyRange.upperBound([this.uid,Number.MAX_SAFE_INTEGER])},(e,t,r)=>{r.done(),n=t.sequenceNumber+1}).next(()=>n)}createRange(r,n,e){e=e.sort((e,t)=>Sa(e,t)).filter((e,t,r)=>!t||0!==Sa(e,r[t-1]));var i=[];i.push(r);for(let s of e){let e=Sa(s,r),t=Sa(s,n);if(0===e)i[0]=r.Rn();else if(0<e&&t<0)i.push(s),i.push(s.Rn());else if(0<t)break}i.push(n);let s=[];for(let a=0;a<i.length;a+=2){if(this.Xn(i[a],i[a+1]))return[];let e=i[a].mn(this.uid,Ua,x.empty()),t=i[a+1].mn(this.uid,Ua,x.empty());s.push(IDBKeyRange.bound(e,t))}return s}Xn(e,t){return 0<Sa(e,t)}getMinOffsetFromCollectionGroup(e,t){return this.getFieldIndexes(e,t).next($a)}getMinOffset(t,e){return w.mapArray(this.xn(e),e=>this.On(t,e).next(e=>e||E(44426))).next($a)}}function ja(e){return r(e,Jt)}function za(e){return r(e,ur)}function Ka(e){return r(e,nr)}function Ga(e){return r(e,sr)}function $a(e){y(0!==e.length,28825);let t=e[0].indexState.offset,r=t.largestBatchId;for(let i=1;i<e.length;i++){var n=e[i].indexState.offset;Xe(n,t)<0&&(t=n),r<n.largestBatchId&&(r=n.largestBatchId)}return new Ye(t.readTime,t.documentKey,r)}let Qa={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class Ha{static withCacheSize(e){return new Ha(e,Ha.DEFAULT_COLLECTION_PERCENTILE,Ha.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,r){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=r}}function Wa(t,r,n){let e=t.store(Ct),i=t.store(Mt),s=[],a=IDBKeyRange.only(n.batchId),o=0;var l=e.te({range:a},(e,t,r)=>(o++,r.delete()));s.push(l.next(()=>{y(1===o,47070,{batchId:n.batchId})}));let u=[];for(let t of n.mutations){let e=Rt(r,t.key.path,n.batchId);s.push(i.delete(e)),u.push(t.key)}return w.waitFor(s).next(()=>u)}function Ya(e){if(!e)return 0;let t;if(e.document)t=e.document;else if(e.unknownDocument)t=e.unknownDocument;else{if(!e.noDocument)throw E(14731);t=e.noDocument}return JSON.stringify(t).length}Ha.DEFAULT_COLLECTION_PERCENTILE=10,Ha.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,Ha.DEFAULT=new Ha(41943040,Ha.DEFAULT_COLLECTION_PERCENTILE,Ha.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),Ha.DISABLED=new Ha(-1,0,0);class Xa{constructor(e,t,r,n){this.userId=e,this.serializer=t,this.indexManager=r,this.referenceDelegate=n,this.er={}}static bt(e,t,r,n){y(""!==e.uid,64387);var i=e.isAuthenticated()?e.uid:"";return new Xa(i,t,r,n)}checkEmpty(e){let n=!0;var t=IDBKeyRange.bound([this.userId,Number.NEGATIVE_INFINITY],[this.userId,Number.POSITIVE_INFINITY]);return Za(e).te({index:Dt,range:t},(e,t,r)=>{n=!1,r.done()}).next(()=>n)}addMutationBatch(c,d,f,g){let m=eo(c),p=Za(c);return p.add({}).next(t=>{y("number"==typeof t,49019);let e=new ns(t,d,f,g),r=(i=this.serializer,s=this.userId,a=e,o=a.baseMutations.map(e=>qs(i.wt,e)),l=a.mutations.map(e=>qs(i.wt,e)),{userId:s,batchId:a.batchId,localWriteTimeMs:a.localWriteTime.toMillis(),baseMutations:o,mutations:l}),n=[];var i,s,a,o,l;let u=new A((e,t)=>S(e.canonicalString(),t.canonicalString()));for(let h of g){let e=Rt(this.userId,h.key.path,t);u=u.add(h.key.path.popLast()),n.push(p.put(r)),n.push(m.put(e,Ot))}return u.forEach(e=>{n.push(this.indexManager.addToCollectionParentIndex(c,e))}),c.addOnCommittedListener(()=>{this.er[t]=e.keys()}),w.waitFor(n).next(()=>e)})}lookupMutationBatch(e,t){return Za(e).get(t).next(e=>e?(y(e.userId===this.userId,48,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),sa(this.serializer,e)):null)}tr(e,r){return this.er[r]?w.resolve(this.er[r]):this.lookupMutationBatch(e,r).next(e=>{var t;return e?(t=e.keys(),this.er[r]=t):null})}getNextMutationBatchAfterBatchId(e,t){let n=t+1,r=IDBKeyRange.lowerBound([this.userId,n]),i=null;return Za(e).te({index:Dt,range:r},(e,t,r)=>{t.userId===this.userId&&(y(t.batchId>=n,47524,{nr:n}),i=sa(this.serializer,t)),r.done()}).next(()=>i)}getHighestUnacknowledgedBatchId(e){var t=IDBKeyRange.upperBound([this.userId,Number.POSITIVE_INFINITY]);let n=pt;return Za(e).te({index:Dt,range:t,reverse:!0},(e,t,r)=>{n=t.batchId,r.done()}).next(()=>n)}getAllMutationBatches(e){var t=IDBKeyRange.bound([this.userId,pt],[this.userId,Number.POSITIVE_INFINITY]);return Za(e).J(Dt,t).next(e=>e.map(e=>sa(this.serializer,e)))}getAllMutationBatchesAffectingDocumentKey(o,l){let e=kt(this.userId,l.path),t=IDBKeyRange.lowerBound(e),u=[];return eo(o).te({range:t},(t,e,r)=>{let[n,i,s]=t,a=It(i);if(n===this.userId&&l.path.isEqual(a))return Za(o).get(s).next(e=>{if(!e)throw E(61480,{rr:t,batchId:s});y(e.userId===this.userId,10503,"Unexpected user for mutation batch",{userId:e.userId,batchId:s}),u.push(sa(this.serializer,e))});r.done()}).next(()=>u)}getAllMutationBatchesAffectingDocumentKeys(t,e){let o=new A(S),r=[];return e.forEach(a=>{var e=kt(this.userId,a.path),e=IDBKeyRange.lowerBound(e),e=eo(t).te({range:e},(e,t,r)=>{var[n,i,s]=e,i=It(i);n===this.userId&&a.path.isEqual(i)?o=o.add(s):r.done()});r.push(e)}),w.waitFor(r).next(()=>this.ir(t,o))}getAllMutationBatchesAffectingQuery(e,t){let a=t.path,o=a.length+1,r=kt(this.userId,a),n=IDBKeyRange.lowerBound(r),l=new A(S);return eo(e).te({range:n},(e,t,r)=>{var[n,i,s]=e,i=It(i);n===this.userId&&a.isPrefixOf(i)?i.length===o&&(l=l.add(s)):r.done()}).next(()=>this.ir(e,l))}ir(e,t){let r=[],n=[];return t.forEach(t=>{n.push(Za(e).get(t).next(e=>{if(null===e)throw E(35274,{batchId:t});y(e.userId===this.userId,9748,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),r.push(sa(this.serializer,e))}))}),w.waitFor(n).next(()=>r)}removeMutationBatch(t,r){return Wa(t.he,this.userId,r).next(e=>(t.addOnCommittedListener(()=>{this.sr(r.batchId)}),w.forEach(e,e=>this.referenceDelegate.markPotentiallyOrphaned(t,e))))}sr(e){delete this.er[e]}performConsistencyCheck(r){return this.checkEmpty(r).next(e=>{if(!e)return w.resolve();let t=IDBKeyRange.lowerBound([this.userId]),n=[];return eo(r).te({range:t},(t,e,r)=>{if(t[0]===this.userId){let e=It(t[1]);n.push(e)}else r.done()}).next(()=>{y(0===n.length,56720,{_r:n.map(e=>e.canonicalString())})})})}containsKey(e,t){return Ja(e,this.userId,t)}ar(e){return to(e).get(this.userId).next(e=>e||{userId:this.userId,lastAcknowledgedBatchId:pt,lastStreamToken:""})}}function Ja(e,s,t){let r=kt(s,t.path),a=r[1],n=IDBKeyRange.lowerBound(r),o=!1;return eo(e).te({range:n,ee:!0},(e,t,r)=>{var[n,i,,]=e;n===s&&i===a&&(o=!0),r.done()}).next(()=>o)}function Za(e){return r(e,Ct)}function eo(e){return r(e,Mt)}function to(e){return r(e,xt)}class ro{constructor(e){this.ur=e}next(){return this.ur+=2,this.ur}static cr(){return new ro(0)}static lr(){return new ro(-1)}}class no{constructor(e,t){this.referenceDelegate=e,this.serializer=t}allocateTargetId(r){return this.hr(r).next(e=>{var t=new ro(e.highestTargetId);return e.highestTargetId=t.next(),this.Pr(r,e).next(()=>e.highestTargetId)})}getLastRemoteSnapshotVersion(e){return this.hr(e).next(e=>g.fromTimestamp(new h(e.lastRemoteSnapshotVersion.seconds,e.lastRemoteSnapshotVersion.nanoseconds)))}getHighestSequenceNumber(e){return this.hr(e).next(e=>e.highestListenSequenceNumber)}setTargetsMetadata(t,r,n){return this.hr(t).next(e=>(e.highestListenSequenceNumber=r,n&&(e.lastRemoteSnapshotVersion=n.toTimestamp()),e.highestListenSequenceNumber<r&&(e.highestListenSequenceNumber=r),this.Pr(t,e)))}addTargetData(t,r){return this.Tr(t,r).next(()=>this.hr(t).next(e=>(e.targetCount+=1,this.Ir(r,e),this.Pr(t,e))))}updateTargetData(e,t){return this.Tr(e,t)}removeTargetData(t,e){return this.removeMatchingKeysForTargetId(t,e.targetId).next(()=>io(t).delete(e.targetId)).next(()=>this.hr(t)).next(e=>(y(0<e.targetCount,8065),--e.targetCount,this.Pr(t,e)))}removeTargets(n,i,s){let a=0,o=[];return io(n).te((e,t)=>{var r=aa(t);r.sequenceNumber<=i&&null===s.get(r.targetId)&&(a++,o.push(this.removeTargetData(n,r)))}).next(()=>w.waitFor(o)).next(()=>a)}forEachTarget(e,n){return io(e).te((e,t)=>{var r=aa(t);n(r)})}hr(e){return so(e).get(Yt).next(e=>(y(null!==e,2888),e))}Pr(e,t){return so(e).put(Yt,t)}Tr(e,t){return io(e).put(oa(this.serializer,t))}Ir(e,t){let r=!1;return e.targetId>t.highestTargetId&&(t.highestTargetId=e.targetId,r=!0),t.highestListenSequenceNumber<e.sequenceNumber&&(t.highestListenSequenceNumber=e.sequenceNumber,r=!0),r}getTargetCount(e){return this.hr(e).next(e=>e.targetCount)}getTargetData(e,i){var t=ei(i),t=IDBKeyRange.bound([t,Number.NEGATIVE_INFINITY],[t,Number.POSITIVE_INFINITY]);let s=null;return io(e).te({range:t,index:Kt},(e,t,r)=>{var n=aa(t);ti(i,n.target)&&(s=n,r.done())}).next(()=>s)}addMatchingKeys(r,e,n){let i=[],s=ao(r);return e.forEach(e=>{var t=o(e.path);i.push(s.put({targetId:n,path:t})),i.push(this.referenceDelegate.addReference(r,n,e))}),w.waitFor(i)}removeMatchingKeys(r,e,n){let i=ao(r);return w.forEach(e,e=>{var t=o(e.path);return w.waitFor([i.delete([n,t]),this.referenceDelegate.removeReference(r,n,e)])})}removeMatchingKeysForTargetId(e,t){var r=ao(e),n=IDBKeyRange.bound([t],[t+1],!1,!0);return r.delete(n)}getMatchingKeysForTargetId(e,t){var r=IDBKeyRange.bound([t],[t+1],!1,!0),n=ao(e);let i=M();return n.te({range:r,ee:!0},(e,t,r)=>{var n=It(e[1]),n=new x(n);i=i.add(n)}).next(()=>i)}containsKey(e,t){var r=o(t.path),r=IDBKeyRange.bound([r],[Pe(r)],!1,!0);let n=0;return ao(e).te({index:Ht,ee:!0,range:r},([e],t,r)=>{0!==e&&(n++,r.done())}).next(()=>0<n)}Rt(e,t){return io(e).get(t).next(e=>e?aa(e):null)}}function io(e){return r(e,zt)}function so(e){return r(e,Xt)}function ao(e){return r(e,$t)}let oo="LruGarbageCollector";function lo([e,t],[r,n]){var i=S(e,r);return 0===i?S(t,n):i}class uo{constructor(e){this.Er=e,this.buffer=new A(lo),this.dr=0}Ar(){return++this.dr}Rr(e){var t=[e,this.Ar()];if(this.buffer.size<this.Er)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();lo(t,e)<0&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class ho{constructor(e,t,r){this.garbageCollector=e,this.asyncQueue=t,this.localStore=r,this.Vr=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.mr(6e4)}stop(){this.Vr&&(this.Vr.cancel(),this.Vr=null)}get started(){return null!==this.Vr}mr(e){p(oo,`Garbage collection scheduled in ${e}ms`),this.Vr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.Vr=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){ot(e)?p(oo,"Ignoring IndexedDB error during garbage collection: ",e):await et(e)}await this.mr(3e5)})}}class co{constructor(e,t){this.gr=e,this.params=t}calculateTargetCount(e,t){return this.gr.pr(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return w.resolve(mt.le);let r=new uo(t);return this.gr.forEachTarget(e,e=>r.Rr(e.sequenceNumber)).next(()=>this.gr.yr(e,e=>r.Rr(e))).next(()=>r.maxValue)}removeTargets(e,t,r){return this.gr.removeTargets(e,t,r)}removeOrphanedDocuments(e,t){return this.gr.removeOrphanedDocuments(e,t)}collect(t,r){return-1===this.params.cacheSizeCollectionThreshold?(p("LruGarbageCollector","Garbage collection skipped; disabled"),w.resolve(Qa)):this.getCacheSize(t).next(e=>e<this.params.cacheSizeCollectionThreshold?(p("LruGarbageCollector",`Garbage collection skipped; Cache size ${e} is lower than threshold `+this.params.cacheSizeCollectionThreshold),Qa):this.wr(t,r))}getCacheSize(e){return this.gr.getCacheSize(e)}wr(t,r){let n,i,s,a,o,l,u,h=Date.now();return this.calculateTargetCount(t,this.params.percentileToCollect).next(e=>(i=e>this.params.maximumSequenceNumbersToCollect?(p("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from `+e),this.params.maximumSequenceNumbersToCollect):e,a=Date.now(),this.nthSequenceNumber(t,i))).next(e=>(n=e,o=Date.now(),this.removeTargets(t,n,r))).next(e=>(s=e,l=Date.now(),this.removeOrphanedDocuments(t,n))).next(e=>(u=Date.now(),_e()<=c.DEBUG&&p("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${a-h}ms
	Determined least recently used ${i} in `+(o-a)+"ms\n"+`	Removed ${s} targets in `+(l-o)+"ms\n"+`	Removed ${e} documents in `+(u-l)+"ms\n"+`Total Duration: ${u-h}ms`),w.resolve({didRun:!0,sequenceNumbersCollected:i,targetsRemoved:s,documentsRemoved:e})))}}function fo(e,t){return new co(e,t)}class go{constructor(e,t){this.db=e,this.garbageCollector=fo(this,t)}pr(e){let r=this.br(e);return this.db.getTargetCache().getTargetCount(e).next(t=>r.next(e=>t+e))}br(e){let t=0;return this.yr(e,e=>{t++}).next(()=>t)}forEachTarget(e,t){return this.db.getTargetCache().forEachTarget(e,t)}yr(e,r){return this.Sr(e,(e,t)=>r(t))}addReference(e,t,r){return mo(e,r)}removeReference(e,t,r){return mo(e,r)}removeTargets(e,t,r){return this.db.getTargetCache().removeTargets(e,t,r)}markPotentiallyOrphaned(e,t){return mo(e,t)}Dr(e,r){{var n=e,i=r;let t=!1;return to(n).ne(e=>Ja(n,e,i).next(e=>(e&&(t=!0),w.resolve(!e)))).next(()=>t)}}removeOrphanedDocuments(r,n){let i=this.db.getRemoteDocumentCache().newChangeBuffer(),s=[],a=0;return this.Sr(r,(t,e)=>{if(e<=n){let e=this.Dr(r,t).next(e=>{if(!e)return a++,i.getEntry(r,t).next(()=>(i.removeEntry(t,g.min()),ao(r).delete([0,o(t.path)])))});s.push(e)}}).next(()=>w.waitFor(s)).next(()=>i.apply(r)).next(()=>a)}removeTarget(e,t){var r=t.withSequenceNumber(e.currentSequenceNumber);return this.db.getTargetCache().updateTargetData(e,r)}updateLimboDocument(e,t){return mo(e,t)}Sr(e,n){var t=ao(e);let i,s=mt.le;return t.te({index:Ht},([e],{path:t,sequenceNumber:r})=>{0===e?(s!==mt.le&&n(new x(It(i)),s),s=r,i=t):s=mt.le}).next(()=>{s!==mt.le&&n(new x(It(i)),s)})}getCacheSize(e){return this.db.getRemoteDocumentCache().getSize(e)}}function mo(e,t){return ao(e).put((e=e.currentSequenceNumber,{targetId:0,path:o(t.path),sequenceNumber:e}))}class po{constructor(){this.changes=new bi(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,k.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();var r=this.changes.get(t);return void 0!==r?w.resolve(r):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class yo{constructor(e){this.serializer=e}setIndexManager(e){this.indexManager=e}addEntry(e,t,r){return bo(e).put(r)}removeEntry(e,t,r){return bo(e).delete((e=r,[(n=t.path.toArray()).slice(0,n.length-2),n[n.length-2],ra(e),n[n.length-1]]));var n}updateMetadata(t,r){return this.getMetadata(t).next(e=>(e.byteSize+=r,this.vr(t,e)))}getEntry(e,r){let n=k.newInvalidDocument(r);return bo(e).te({index:Pt,range:IDBKeyRange.only(Io(r))},(e,t)=>{n=this.Cr(r,t)}).next(()=>n)}Fr(e,r){let n={size:0,document:k.newInvalidDocument(r)};return bo(e).te({index:Pt,range:IDBKeyRange.only(Io(r))},(e,t)=>{n={document:this.Cr(r,t),size:Ya(t)}}).next(()=>n)}getEntries(e,t){let n=Ii;return this.Mr(e,t,(e,t)=>{var r=this.Cr(e,t);n=n.insert(e,r)}).next(()=>n)}Or(e,t){let n=Ii,i=new C(x.comparator);return this.Mr(e,t,(e,t)=>{var r=this.Cr(e,t);n=n.insert(e,r),i=i.insert(e,Ya(t))}).next(()=>({documents:n,Nr:i}))}Mr(e,t,i){if(t.isEmpty())return w.resolve();let r=new A(Eo),n=(t.forEach(e=>r=r.add(e)),IDBKeyRange.bound(Io(r.first()),Io(r.last()))),s=r.getIterator(),a=s.getNext();return bo(e).te({index:Pt,range:n},(e,t,r)=>{for(var n=x.fromSegments([...t.prefixPath,t.collectionGroup,t.documentId]);a&&Eo(a,n)<0;)i(a,null),a=s.getNext();a&&a.isEqual(n)&&(i(a,t),a=s.hasNext()?s.getNext():null),a?r.H(Io(a)):r.done()}).next(()=>{for(;a;)i(a,null),a=s.hasNext()?s.getNext():null})}getDocumentsMatchingQuery(e,n,t,i,s){var r=n.path,a=[r.popLast().toArray(),r.lastSegment(),ra(t.readTime),t.documentKey.path.isEmpty()?"":t.documentKey.path.lastSegment()],r=[r.popLast().toArray(),r.lastSegment(),[Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],""];return bo(e).J(IDBKeyRange.bound(a,r,!0)).next(e=>{null!=s&&s.incrementDocumentReadCount(e.length);let t=Ii;for(let r of e){let e=this.Cr(x.fromSegments(r.prefixPath.concat(r.collectionGroup,r.documentId)),r);e.isFoundDocument()&&(vi(n,e)||i.has(e.key))&&(t=t.insert(e.key,e))}return t})}getAllFromCollectionGroup(e,t,r,i){let s=Ii;var n=To(t,r),a=To(t,Ye.max());return bo(e).te({index:Bt,range:IDBKeyRange.bound(n,a,!0)},(e,t,r)=>{var n=this.Cr(x.fromSegments(t.prefixPath.concat(t.collectionGroup,t.documentId)),t);(s=s.insert(n.key,n)).size===i&&r.done()}).next(()=>s)}newChangeBuffer(e){return new wo(this,!!e&&e.trackRemovals)}getSize(e){return this.getMetadata(e).next(e=>e.byteSize)}getMetadata(e){return _o(e).get(jt).next(e=>(y(!!e,20021),e))}vr(e,t){return _o(e).put(jt,t)}Cr(e,t){if(t){let e=((e,r)=>{let n;if(r.document)n=Us(e.wt,r.document,!!r.hasCommittedMutations);else if(r.noDocument){let e=x.fromSegments(r.noDocument.path),t=ia(r.noDocument.readTime);n=k.newNoDocument(e,t),r.hasCommittedMutations&&n.setHasCommittedMutations()}else{if(!r.unknownDocument)return E(56709);{let e=x.fromSegments(r.unknownDocument.path),t=ia(r.unknownDocument.version);n=k.newUnknownDocument(e,t)}}return r.readTime&&n.setReadTime((e=r.readTime,t=new h(e[0],e[1]),g.fromTimestamp(t))),n;var t})(this.serializer,t);if(!e.isNoDocument()||!e.version.isEqual(g.min()))return e}return k.newInvalidDocument(e)}}function vo(e){return new yo(e)}class wo extends po{constructor(e,t){super(),this.Br=e,this.trackRemovals=t,this.Lr=new bi(e=>e.toString(),(e,t)=>e.isEqual(t))}applyChanges(s){let a=[],o=0,l=new A((e,t)=>S(e.canonicalString(),t.canonicalString()));return this.changes.forEach((t,r)=>{var e=this.Lr.get(t);if(a.push(this.Br.removeEntry(s,t,e.readTime)),r.isValidDocument()){var n=ta(this.Br.serializer,r),i=(l=l.add(t.path.popLast()),Ya(n));o+=i-e.size,a.push(this.Br.addEntry(s,t,n))}else if(o-=e.size,this.trackRemovals){let e=ta(this.Br.serializer,r.convertToNoDocument(g.min()));a.push(this.Br.addEntry(s,t,e))}}),l.forEach(e=>{a.push(this.Br.indexManager.addToCollectionParentIndex(s,e))}),a.push(this.Br.updateMetadata(s,o)),w.waitFor(a)}getFromCache(e,t){return this.Br.Fr(e,t).next(e=>(this.Lr.set(t,{size:e.size,readTime:e.document.readTime}),e.document))}getAllFromCache(e,t){return this.Br.Or(e,t).next(({documents:r,Nr:e})=>(e.forEach((e,t)=>{this.Lr.set(e,{size:t,readTime:r.get(e).readTime})}),r))}}function _o(e){return r(e,qt)}function bo(e){return r(e,Lt)}function Io(e){var t=e.path.toArray();return[t.slice(0,t.length-2),t[t.length-2],t[t.length-1]]}function To(e,t){var r=t.documentKey.path.toArray();return[e,ra(t.readTime),r.slice(0,r.length-2),0<r.length?r[r.length-1]:""]}function Eo(e,t){var r=e.path.toArray(),n=t.path.toArray();let i=0;for(let s=0;s<r.length-2&&s<n.length-2;++s)if(i=S(r[s],n[s]))return i;return(i=S(r.length,n.length))||(i=S(r[r.length-2],n[n.length-2]))||S(r[r.length-1],n[n.length-1])}class So{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class xo{constructor(e,t,r,n){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=r,this.indexManager=n}getDocument(t,r){let n=null;return this.documentOverlayCache.getOverlay(t,r).next(e=>(n=e,this.remoteDocumentCache.getEntry(t,r))).next(e=>(null!==n&&Hi(n.mutation,e,Gr.empty(),h.now()),e))}getDocuments(t,e){return this.remoteDocumentCache.getEntries(t,e).next(e=>this.getLocalViewOfDocuments(t,e,M()).next(()=>e))}getLocalViewOfDocuments(e,t,r=M()){let n=xi();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,r).next(e=>{let r=Ei();return e.forEach((e,t)=>{r=r.insert(e,t.overlayedDocument)}),r}))}getOverlayedDocuments(e,t){let r=xi();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,M()))}populateOverlays(e,r,t){let n=[];return t.forEach(e=>{r.has(e)||n.push(e)}),this.documentOverlayCache.getOverlays(e,n).next(e=>{e.forEach((e,t)=>{r.set(e,t)})})}computeViews(e,t,n,i){let s=Ii,a=xi(),o=xi();return t.forEach((e,t)=>{var r=n.get(t.key);i.has(t.key)&&(void 0===r||r.mutation instanceof Xi)?s=s.insert(t.key,t):void 0!==r?(a.set(t.key,r.mutation.getFieldMask()),Hi(r.mutation,t,r.mutation.getFieldMask(),h.now())):a.set(t.key,Gr.empty())}),this.recalculateAndSaveOverlays(e,s).next(e=>(e.forEach((e,t)=>a.set(e,t)),t.forEach((e,t)=>{var r;return o.set(e,new So(t,null!=(r=a.get(e))?r:null))}),o))}recalculateAndSaveOverlays(a,o){let l=xi(),u=new C((e,t)=>e-t),h=M();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(a,o).next(e=>{for(let n of e)n.keys().forEach(e=>{var t,r=o.get(e);null!==r&&(t=l.get(e)||Gr.empty(),t=n.applyToLocalView(r,t),l.set(e,t),r=(u.get(n.batchId)||M()).add(e),u=u.insert(n.batchId,r))})}).next(()=>{for(var i=[],s=u.getReverseIterator();s.hasNext();){let e=s.getNext(),t=e.key,r=e.value,n=xi();r.forEach(e=>{var t;h.has(e)||(null!==(t=Qi(o.get(e),l.get(e)))&&n.set(e,t),h=h.add(e))}),i.push(this.documentOverlayCache.saveOverlays(a,t,n))}return w.waitFor(i)}).next(()=>l)}recalculateAndSaveOverlaysForDocumentKeys(t,e){return this.remoteDocumentCache.getEntries(t,e).next(e=>this.recalculateAndSaveOverlays(t,e))}getDocumentsMatchingQuery(e,t,r,n){return i=t,x.isDocumentKey(i.path)&&null===i.collectionGroup&&0===i.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):hi(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,r,n):this.getDocumentsMatchingCollectionQuery(e,t,r,n);var i}getNextDocuments(s,t,a,o){return this.remoteDocumentCache.getAllFromCollectionGroup(s,t,a,o).next(r=>{var e=0<o-r.size?this.documentOverlayCache.getOverlaysForCollectionGroup(s,t,a.largestBatchId,o-r.size):w.resolve(xi());let n=je,i=r;return e.next(e=>w.forEach(e,(t,e)=>(n<e.largestBatchId&&(n=e.largestBatchId),r.get(t)?w.resolve():this.remoteDocumentCache.getEntry(s,t).next(e=>{i=i.insert(t,e)}))).next(()=>this.populateOverlays(s,e,r)).next(()=>this.computeViews(s,i,e,M())).next(e=>({batchId:n,changes:Si(e)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new x(t)).next(e=>{let t=Ei();return t=e.isFoundDocument()?t.insert(e.key,e):t})}getDocumentsMatchingCollectionGroupQuery(n,i,s,a){let o=i.collectionGroup,l=Ei();return this.indexManager.getCollectionParents(n,o).next(e=>w.forEach(e,e=>{t=i,e=e.child(o);var t,r=new ai(e,null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(n,r,s,a).next(e=>{e.forEach((e,t)=>{l=l.insert(e,t)})})}).next(()=>l))}getDocumentsMatchingCollectionQuery(t,s,r,n){let a;return this.documentOverlayCache.getOverlaysForCollection(t,s.path,r.largestBatchId).next(e=>(a=e,this.remoteDocumentCache.getDocumentsMatchingQuery(t,s,r,a,n))).next(n=>{a.forEach((e,t)=>{var r=t.getKey();null===n.get(r)&&(n=n.insert(r,k.newInvalidDocument(r)))});let i=Ei();return n.forEach((e,t)=>{var r=a.get(e);void 0!==r&&Hi(r.mutation,t,Gr.empty(),h.now()),vi(s,t)&&(i=i.insert(e,t))}),i})}}class Co{constructor(e){this.serializer=e,this.kr=new Map,this.qr=new Map}getBundleMetadata(e,t){return w.resolve(this.kr.get(t))}saveBundleMetadata(e,t){return this.kr.set(t.id,{id:t.id,version:t.version,createTime:V(t.createTime)}),w.resolve()}getNamedQuery(e,t){return w.resolve(this.qr.get(t))}saveNamedQuery(e,t){return this.qr.set(t.name,{name:(t=t).name,query:la(t.bundledQuery),readTime:V(t.readTime)}),w.resolve()}}class Ao{constructor(){this.overlays=new C(x.comparator),this.Qr=new Map}getOverlay(e,t){return w.resolve(this.overlays.get(t))}getOverlays(e,t){let r=xi();return w.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(r,n,e){return e.forEach((e,t)=>{this.St(r,n,t)}),w.resolve()}removeOverlaysForBatchId(e,t,r){var n=this.Qr.get(r);return void 0!==n&&(n.forEach(e=>this.overlays=this.overlays.remove(e)),this.Qr.delete(r)),w.resolve()}getOverlaysForCollection(e,r,n){let i=xi(),s=r.length+1,t=new x(r.child("")),a=this.overlays.getIteratorFrom(t);for(;a.hasNext();){let e=a.getNext().value,t=e.getKey();if(!r.isPrefixOf(t.path))break;t.path.length===s&&e.largestBatchId>n&&i.set(e.getKey(),e)}return w.resolve(i)}getOverlaysForCollectionGroup(e,r,n,t){let i=new C((e,t)=>e-t);for(var s=this.overlays.getIterator();s.hasNext();){let t=s.getNext().value;if(t.getKey().getCollectionGroup()===r&&t.largestBatchId>n){let e=i.get(t.largestBatchId);null===e&&(e=xi(),i=i.insert(t.largestBatchId,e)),e.set(t.getKey(),t)}}let a=xi(),o=i.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=t)););return w.resolve(a)}St(e,t,r){var n=this.overlays.get(r.key);if(null!==n){let e=this.Qr.get(n.largestBatchId).delete(r.key);this.Qr.set(n.largestBatchId,e)}this.overlays=this.overlays.insert(r.key,new ss(t,r));let i=this.Qr.get(t);void 0===i&&(i=M(),this.Qr.set(t,i)),this.Qr.set(t,i.add(r.key))}}class Do{constructor(){this.sessionToken=D.EMPTY_BYTE_STRING}getSessionToken(e){return w.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,w.resolve()}}class No{constructor(){this.$r=new A(l.Ur),this.Kr=new A(l.Wr)}isEmpty(){return this.$r.isEmpty()}addReference(e,t){var r=new l(e,t);this.$r=this.$r.add(r),this.Kr=this.Kr.add(r)}Gr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.zr(new l(e,t))}jr(e,t){e.forEach(e=>this.removeReference(e,t))}Hr(e){let t=new x(new T([])),r=new l(t,e),n=new l(t,e+1),i=[];return this.Kr.forEachInRange([r,n],e=>{this.zr(e),i.push(e.key)}),i}Jr(){this.$r.forEach(e=>this.zr(e))}zr(e){this.$r=this.$r.delete(e),this.Kr=this.Kr.delete(e)}Yr(e){var t=new x(new T([])),r=new l(t,e),t=new l(t,e+1);let n=M();return this.Kr.forEachInRange([r,t],e=>{n=n.add(e.key)}),n}containsKey(e){var t=new l(e,0),t=this.$r.firstAfterOrEqual(t);return null!==t&&e.isEqual(t.key)}}class l{constructor(e,t){this.key=e,this.Zr=t}static Ur(e,t){return x.comparator(e.key,t.key)||S(e.Zr,t.Zr)}static Wr(e,t){return S(e.Zr,t.Zr)||x.comparator(e.key,t.key)}}class ko{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.nr=1,this.Xr=new A(l.Ur)}checkEmpty(e){return w.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,r,n){var i=this.nr,s=(this.nr++,0<this.mutationQueue.length&&this.mutationQueue[this.mutationQueue.length-1],new ns(i,t,r,n));this.mutationQueue.push(s);for(let t of n)this.Xr=this.Xr.add(new l(t.key,i)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return w.resolve(s)}lookupMutationBatch(e,t){return w.resolve(this.ei(t))}getNextMutationBatchAfterBatchId(e,t){var r=this.ti(t+1),r=r<0?0:r;return w.resolve(this.mutationQueue.length>r?this.mutationQueue[r]:null)}getHighestUnacknowledgedBatchId(){return w.resolve(0===this.mutationQueue.length?pt:this.nr-1)}getAllMutationBatches(e){return w.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let r=new l(t,0),n=new l(t,Number.POSITIVE_INFINITY),i=[];return this.Xr.forEachInRange([r,n],e=>{var t=this.ei(e.Zr);i.push(t)}),w.resolve(i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new A(S);return t.forEach(e=>{var t=new l(e,0),r=new l(e,Number.POSITIVE_INFINITY);this.Xr.forEachInRange([t,r],e=>{n=n.add(e.Zr)})}),w.resolve(this.ni(n))}getAllMutationBatchesAffectingQuery(e,t){let r=t.path,n=r.length+1,i=r;x.isDocumentKey(i)||(i=i.child(""));var s=new l(new x(i),0);let a=new A(S);return this.Xr.forEachWhile(e=>{var t=e.key.path;return!!r.isPrefixOf(t)&&(t.length===n&&(a=a.add(e.Zr)),!0)},s),w.resolve(this.ni(a))}ni(e){let r=[];return e.forEach(e=>{var t=this.ei(e);null!==t&&r.push(t)}),r}removeMutationBatch(r,n){y(0===this.ri(n.batchId,"removed"),55003),this.mutationQueue.shift();let i=this.Xr;return w.forEach(n.mutations,e=>{var t=new l(e.key,n.batchId);return i=i.delete(t),this.referenceDelegate.markPotentiallyOrphaned(r,e.key)}).next(()=>{this.Xr=i})}sr(e){}containsKey(e,t){var r=new l(t,0),r=this.Xr.firstAfterOrEqual(r);return w.resolve(t.isEqual(r&&r.key))}performConsistencyCheck(e){return this.mutationQueue.length,w.resolve()}ri(e,t){return this.ti(e)}ti(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}ei(e){var t=this.ti(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class Ro{constructor(e){this.ii=e,this.docs=new C(x.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){var r=t.key,n=this.docs.get(r),n=n?n.size:0,i=this.ii(t);return this.docs=this.docs.insert(r,{document:t.mutableCopy(),size:i}),this.size+=i-n,this.indexManager.addToCollectionParentIndex(e,r.path.popLast())}removeEntry(e){var t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){var r=this.docs.get(t);return w.resolve(r?r.document.mutableCopy():k.newInvalidDocument(t))}getEntries(e,t){let r=Ii;return t.forEach(e=>{var t=this.docs.get(e);r=r.insert(e,t?t.document.mutableCopy():k.newInvalidDocument(e))}),w.resolve(r)}getDocumentsMatchingQuery(e,r,n,i){let s=Ii,a=r.path,t=new x(a.child("__id-9223372036854775808__")),o=this.docs.getIteratorFrom(t);for(;o.hasNext();){let{key:e,value:{document:t}}=o.getNext();if(!a.isPrefixOf(e.path))break;e.path.length>a.length+1||Xe(We(t),n)<=0||(i.has(t.key)||vi(r,t))&&(s=s.insert(t.key,t.mutableCopy()))}return w.resolve(s)}getAllFromCollectionGroup(e,t,r,n){E(9500)}si(e,t){return w.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new Oo(this)}getSize(e){return w.resolve(this.size)}}class Oo extends po{constructor(e){super(),this.Br=e}applyChanges(r){let n=[];return this.changes.forEach((e,t)=>{t.isValidDocument()?n.push(this.Br.addEntry(r,t)):this.Br.removeEntry(e)}),w.waitFor(n)}getFromCache(e,t){return this.Br.getEntry(e,t)}getAllFromCache(e,t){return this.Br.getEntries(e,t)}}class Mo{constructor(e){this.persistence=e,this.oi=new bi(e=>ei(e),ti),this.lastRemoteSnapshotVersion=g.min(),this.highestTargetId=0,this._i=0,this.ai=new No,this.targetCount=0,this.ui=ro.cr()}forEachTarget(e,r){return this.oi.forEach((e,t)=>r(t)),w.resolve()}getLastRemoteSnapshotVersion(e){return w.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return w.resolve(this._i)}allocateTargetId(e){return this.highestTargetId=this.ui.next(),w.resolve(this.highestTargetId)}setTargetsMetadata(e,t,r){return r&&(this.lastRemoteSnapshotVersion=r),t>this._i&&(this._i=t),w.resolve()}Tr(e){this.oi.set(e.target,e);var t=e.targetId;t>this.highestTargetId&&(this.ui=new ro(t),this.highestTargetId=t),e.sequenceNumber>this._i&&(this._i=e.sequenceNumber)}addTargetData(e,t){return this.Tr(t),this.targetCount+=1,w.resolve()}updateTargetData(e,t){return this.Tr(t),w.resolve()}removeTargetData(e,t){return this.oi.delete(t.target),this.ai.Hr(t.targetId),--this.targetCount,w.resolve()}removeTargets(r,n,i){let s=0,a=[];return this.oi.forEach((e,t)=>{t.sequenceNumber<=n&&null===i.get(t.targetId)&&(this.oi.delete(e),a.push(this.removeMatchingKeysForTargetId(r,t.targetId)),s++)}),w.waitFor(a).next(()=>s)}getTargetCount(e){return w.resolve(this.targetCount)}getTargetData(e,t){var r=this.oi.get(t)||null;return w.resolve(r)}addMatchingKeys(e,t,r){return this.ai.Gr(t,r),w.resolve()}removeMatchingKeys(t,e,r){this.ai.jr(e,r);let n=this.persistence.referenceDelegate,i=[];return n&&e.forEach(e=>{i.push(n.markPotentiallyOrphaned(t,e))}),w.waitFor(i)}removeMatchingKeysForTargetId(e,t){return this.ai.Hr(t),w.resolve()}getMatchingKeysForTargetId(e,t){var r=this.ai.Yr(t);return w.resolve(r)}containsKey(e,t){return w.resolve(this.ai.containsKey(t))}}class Lo{constructor(e,t){this.ci={},this.overlays={},this.li=new mt(0),this.hi=!1,this.hi=!0,this.Pi=new Do,this.referenceDelegate=e(this),this.Ti=new Mo(this),this.indexManager=new Pa,this.remoteDocumentCache=(e=e=>this.referenceDelegate.Ii(e),new Ro(e)),this.serializer=new ea(t),this.Ei=new Co(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.hi=!1,Promise.resolve()}get started(){return this.hi}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new Ao,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let r=this.ci[e.toKey()];return r||(r=new ko(t,this.referenceDelegate),this.ci[e.toKey()]=r),r}getGlobalsCache(){return this.Pi}getTargetCache(){return this.Ti}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Ei}runTransaction(e,t,r){p("MemoryPersistence","Starting transaction:",e);let n=new Vo(this.li.next());return this.referenceDelegate.di(),r(n).next(e=>this.referenceDelegate.Ai(n).next(()=>e)).toPromise().then(e=>(n.raiseOnCommittedEvent(),e))}Ri(t,r){return w.or(Object.values(this.ci).map(e=>()=>e.containsKey(t,r)))}}class Vo extends Ze{constructor(e){super(),this.currentSequenceNumber=e}}class Po{constructor(e){this.persistence=e,this.Vi=new No,this.mi=null}static fi(e){return new Po(e)}get gi(){if(this.mi)return this.mi;throw E(60996)}addReference(e,t,r){return this.Vi.addReference(r,t),this.gi.delete(r.toString()),w.resolve()}removeReference(e,t,r){return this.Vi.removeReference(r,t),this.gi.add(r.toString()),w.resolve()}markPotentiallyOrphaned(e,t){return this.gi.add(t.toString()),w.resolve()}removeTarget(e,t){this.Vi.Hr(t.targetId).forEach(e=>this.gi.add(e.toString()));let r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.gi.add(e.toString()))}).next(()=>r.removeTargetData(e,t))}di(){this.mi=new Set}Ai(r){let n=this.persistence.getRemoteDocumentCache().newChangeBuffer();return w.forEach(this.gi,e=>{let t=x.fromPath(e);return this.pi(r,t).next(e=>{e||n.removeEntry(t,g.min())})}).next(()=>(this.mi=null,n.apply(r)))}updateLimboDocument(e,t){return this.pi(e,t).next(e=>{e?this.gi.delete(t.toString()):this.gi.add(t.toString())})}Ii(e){return 0}pi(e,t){return w.or([()=>w.resolve(this.Vi.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Ri(e,t)])}}class Fo{constructor(e,t){this.persistence=e,this.yi=new bi(e=>o(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=fo(this,t)}static fi(e,t){return new Fo(e,t)}di(){}Ai(e){return w.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}pr(e){let r=this.br(e);return this.persistence.getTargetCache().getTargetCount(e).next(t=>r.next(e=>t+e))}br(e){let t=0;return this.yr(e,e=>{t++}).next(()=>t)}yr(r,n){return w.forEach(this.yi,(e,t)=>this.Dr(r,e,t).next(e=>e?w.resolve():n(t)))}removeTargets(e,t,r){return this.persistence.getTargetCache().removeTargets(e,t,r)}removeOrphanedDocuments(e,r){let n=0,t=this.persistence.getRemoteDocumentCache(),i=t.newChangeBuffer();return t.si(e,t=>this.Dr(e,t,r).next(e=>{e||(n++,i.removeEntry(t,g.min()))})).next(()=>i.apply(e)).next(()=>n)}markPotentiallyOrphaned(e,t){return this.yi.set(t,e.currentSequenceNumber),w.resolve()}removeTarget(e,t){var r=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,r)}addReference(e,t,r){return this.yi.set(r,e.currentSequenceNumber),w.resolve()}removeReference(e,t,r){return this.yi.set(r,e.currentSequenceNumber),w.resolve()}updateLimboDocument(e,t){return this.yi.set(t,e.currentSequenceNumber),w.resolve()}Ii(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function n(e){switch(fn(e)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:var t=tn(e);return t?16+n(t):16;case 5:return 2*e.stringValue.length;case 6:return Wr(e.bytesValue).approximateByteSize();case 7:return e.referenceValue.length;case 9:return(e.arrayValue.values||[]).reduce((e,t)=>e+n(t),0);case 10:case 11:{var i=e.mapValue;let r=0;return Br(i.fields,(e,t)=>{r+=e.length+n(t)}),r}default:throw E(13486,{value:e})}}(e.data.value)),t}Dr(e,t,r){return w.or([()=>this.persistence.Ri(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{var e=this.yi.get(t);return w.resolve(void 0!==e&&r<e)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class Bo{constructor(e){this.serializer=e}q(t,e,r,n){let s=new rt("createOrUpgrade",e);var i;r<1&&1<=n&&(t.createObjectStore(Et),(i=t).createObjectStore(xt,{keyPath:"userId"}),i.createObjectStore(Ct,{keyPath:At,autoIncrement:!0}).createIndex(Dt,Nt,{unique:!0}),i.createObjectStore(Mt),Uo(t),t.createObjectStore(Tt));let a=w.resolve();return r<3&&3<=n&&(0!==r&&((i=t).deleteObjectStore($t),i.deleteObjectStore(zt),i.deleteObjectStore(Xt),Uo(t)),a=a.next(()=>{return e=s,t=e.store(Xt),r={highestTargetId:0,highestListenSequenceNumber:0,lastRemoteSnapshotVersion:g.min().toTimestamp(),targetCount:0},t.put(Yt,r);var e,t,r})),r<4&&4<=n&&(a=(a=0!==r?a.next(()=>{return n=t,(i=s).store(Ct).J().next(e=>{n.deleteObjectStore(Ct),n.createObjectStore(Ct,{keyPath:At,autoIncrement:!0}).createIndex(Dt,Nt,{unique:!0});let t=i.store(Ct),r=e.map(e=>t.put(e));return w.waitFor(r)});var n,i}):a).next(()=>{t.createObjectStore(er,{keyPath:"clientId"})})),r<5&&5<=n&&(a=a.next(()=>this.wi(s))),r<6&&6<=n&&(a=a.next(()=>(t.createObjectStore(qt),this.bi(s)))),r<7&&7<=n&&(a=a.next(()=>this.Si(s))),r<8&&8<=n&&(a=a.next(()=>this.Di(t,s))),r<9&&9<=n&&(a=a.next(()=>{var e;(e=t).objectStoreNames.contains("remoteDocumentChanges")&&e.deleteObjectStore("remoteDocumentChanges")})),r<10&&10<=n&&(a=a.next(()=>this.Ci(s))),r<11&&11<=n&&(a=a.next(()=>{t.createObjectStore(tr,{keyPath:"bundleId"}),t.createObjectStore(rr,{keyPath:"name"})})),r<12&&12<=n&&(a=a.next(()=>{var e;(e=t.createObjectStore(fr,{keyPath:gr})).createIndex(mr,Sr,{unique:!1}),e.createIndex(xr,Cr,{unique:!1})})),r<13&&13<=n&&(a=a.next(()=>{var e;(e=t.createObjectStore(Lt,{keyPath:Vt})).createIndex(Pt,Ft),e.createIndex(Bt,Ut)}).next(()=>this.Fi(t,s)).next(()=>t.deleteObjectStore(Tt))),r<14&&14<=n&&(a=a.next(()=>this.Mi(t,s))),r<15&&15<=n&&(a=a.next(()=>{var e;(e=t).createObjectStore(nr,{keyPath:"indexId",autoIncrement:!0}).createIndex(ir,"collectionGroup",{unique:!1}),e.createObjectStore(sr,{keyPath:ar}).createIndex(or,lr,{unique:!1}),e.createObjectStore(ur,{keyPath:hr}).createIndex(cr,dr,{unique:!1})})),r<16&&16<=n&&(a=a.next(()=>{e.objectStore(sr).clear()}).next(()=>{e.objectStore(ur).clear()})),r<17&&17<=n&&(a=a.next(()=>{t.createObjectStore(Ar,{keyPath:"name"})})),a=r<18&&18<=n&&ne()?a.next(()=>{e.objectStore(sr).clear()}).next(()=>{e.objectStore(ur).clear()}):a}bi(t){let r=0;return t.store(Tt).te((e,t)=>{r+=Ya(t)}).next(()=>{var e={byteSize:r};return t.store(qt).put(jt,e)})}wi(n){let e=n.store(xt),t=n.store(Ct);return e.J().next(e=>w.forEach(e,r=>{var e=IDBKeyRange.bound([r.userId,pt],[r.userId,r.lastAcknowledgedBatchId]);return t.J(Dt,e).next(e=>w.forEach(e,e=>{y(e.userId===r.userId,18650,"Cannot process batch from unexpected user",{batchId:e.batchId});var t=sa(this.serializer,e);return Wa(n,r.userId,t).next(()=>{})}))}))}Si(e){let a=e.store($t),t=e.store(Tt);return e.store(Xt).get(Yt).next(i=>{let s=[];return t.te((e,t)=>{let r=new T(e),n=[0,o(r)];s.push(a.get(n).next(e=>e?w.resolve():(e=r,a.put({targetId:0,path:o(e),sequenceNumber:i.highestListenSequenceNumber}))))}).next(()=>w.waitFor(s))})}Di(e,t){e.createObjectStore(Jt,{keyPath:Zt});let n=t.store(Jt),i=new Fa,s=r=>{if(i.add(r)){let e=r.lastSegment(),t=r.popLast();return n.put({collectionId:e,parent:o(t)})}};return t.store(Tt).te({ee:!0},(e,t)=>{var r=new T(e);return s(r.popLast())}).next(()=>t.store(Mt).te({ee:!0},([,e],t)=>{var r=It(e);return s(r.popLast())}))}Ci(e){let n=e.store(zt);return n.te((e,t)=>{var r=aa(t),r=oa(this.serializer,r);return n.put(r)})}Fi(e,s){let t=s.store(Tt),a=[];return t.te((e,t)=>{var r,n=s.store(Lt),i=((r=t).document?new x(T.fromString(r.document.name).popFirst(5)):r.noDocument?x.fromSegments(r.noDocument.path):r.unknownDocument?x.fromSegments(r.unknownDocument.path):E(36783)).path.toArray(),i={prefixPath:i.slice(0,i.length-2),collectionGroup:i[i.length-2],documentId:i[i.length-1],readTime:t.readTime||[0,0],unknownDocument:t.unknownDocument,noDocument:t.noDocument,document:t.document,hasCommittedMutations:!!t.hasCommittedMutations};a.push(n.put(i))}).next(()=>w.waitFor(a))}Mi(e,s){let t=s.store(Ct),a=vo(this.serializer),o=new Lo(Po.fi,this.serializer.wt);return t.J().next(e=>{let n=new Map;return e.forEach(e=>{let t,r=null!=(t=n.get(e.userId))?t:M();sa(this.serializer,e).keys().forEach(e=>r=r.add(e)),n.set(e.userId,r)}),w.forEach(n,(e,t)=>{var r=new u(t),n=ma.bt(this.serializer,r),i=o.getIndexManager(r),r=Xa.bt(r,this.serializer,i,o.referenceDelegate);return new xo(a,r,n,i).recalculateAndSaveOverlaysForDocumentKeys(new Pr(s,mt.le),e).next()})})}}function Uo(e){e.createObjectStore($t,{keyPath:Qt}).createIndex(Ht,Wt,{unique:!0}),e.createObjectStore(zt,{keyPath:"targetId"}).createIndex(Kt,Gt,{unique:!0}),e.createObjectStore(Xt)}let qo="IndexedDbPersistence",jo="Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.";class zo{constructor(e,t,r,n,i,s,a,o,l,u,h=18){if(this.allowTabSynchronization=e,this.persistenceKey=t,this.clientId=r,this.xi=i,this.window=s,this.document=a,this.Oi=l,this.Ni=u,this.Bi=h,this.li=null,this.hi=!1,this.isPrimary=!1,this.networkEnabled=!0,this.Li=null,this.inForeground=!1,this.ki=null,this.qi=null,this.Qi=Number.NEGATIVE_INFINITY,this.$i=e=>Promise.resolve(),!zo.C())throw new I(b.UNIMPLEMENTED,"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.");this.referenceDelegate=new go(this,n),this.Ui=t+"main",this.serializer=new ea(o),this.Ki=new nt(this.Ui,this.Bi,new Bo(this.serializer)),this.Pi=new ya,this.Ti=new no(this.referenceDelegate,this.serializer),this.remoteDocumentCache=vo(this.serializer),this.Ei=new da,this.window&&this.window.localStorage?this.Wi=this.window.localStorage:(this.Wi=null,!1===u&&d(qo,"LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page."))}start(){return this.Gi().then(()=>{if(this.isPrimary||this.allowTabSynchronization)return this.zi(),this.ji(),this.Hi(),this.runTransaction("getHighestListenSequenceNumber","readonly",e=>this.Ti.getHighestSequenceNumber(e));throw new I(b.FAILED_PRECONDITION,jo)}).then(e=>{this.li=new mt(e,this.Oi)}).then(()=>{this.hi=!0}).catch(e=>(this.Ki&&this.Ki.close(),Promise.reject(e)))}Ji(t){return this.$i=async e=>{if(this.started)return t(e)},t(this.isPrimary)}setDatabaseDeletedListener(t){this.Ki.U(async e=>{null===e.newVersion&&await t()})}setNetworkEnabled(e){this.networkEnabled!==e&&(this.networkEnabled=e,this.xi.enqueueAndForget(async()=>{this.started&&await this.Gi()}))}Gi(){return this.runTransaction("updateClientMetadataAndTryBecomePrimary","readwrite",t=>Go(t).put({clientId:this.clientId,updateTimeMs:Date.now(),networkEnabled:this.networkEnabled,inForeground:this.inForeground}).next(()=>{if(this.isPrimary)return this.Yi(t).next(e=>{e||(this.isPrimary=!1,this.xi.enqueueRetryable(()=>this.$i(!1)))})}).next(()=>this.Zi(t)).next(e=>this.isPrimary&&!e?this.Xi(t).next(()=>!1):!!e&&this.es(t).next(()=>!0))).catch(e=>{if(ot(e))return p(qo,"Failed to extend owner lease: ",e),this.isPrimary;if(this.allowTabSynchronization)return p(qo,"Releasing owner lease after error during lease refresh",e),!1;throw e}).then(e=>{this.isPrimary!==e&&this.xi.enqueueRetryable(()=>this.$i(e)),this.isPrimary=e})}Yi(e){return Ko(e).get(St).next(e=>w.resolve(this.ts(e)))}ns(e){return Go(e).delete(this.clientId)}async rs(){if(this.isPrimary&&!this.ss(this.Qi,18e5)){this.Qi=Date.now();var e=await this.runTransaction("maybeGarbageCollectMultiClientState","readwrite-primary",e=>{let n=r(e,er);return n.J().next(e=>{let t=this._s(e,18e5),r=e.filter(e=>-1===t.indexOf(e));return w.forEach(r,e=>n.delete(e.clientId)).next(()=>r)})}).catch(()=>[]);if(this.Wi)for(var t of e)this.Wi.removeItem(this.us(t.clientId))}}Hi(){this.qi=this.xi.enqueueAfterDelay("client_metadata_refresh",4e3,()=>this.Gi().then(()=>this.rs()).then(()=>this.Hi()))}ts(e){return!!e&&e.ownerId===this.clientId}Zi(t){return this.Ni?w.resolve(!0):Ko(t).get(St).next(e=>{if(null!==e&&this.ss(e.leaseTimestampMs,5e3)&&!this.cs(e.ownerId)){if(this.ts(e)&&this.networkEnabled)return!0;if(!this.ts(e)){if(e.allowTabSynchronization)return!1;throw new I(b.FAILED_PRECONDITION,jo)}}return!(!this.networkEnabled||!this.inForeground)||Go(t).J().next(e=>void 0===this._s(e,5e3).find(e=>{if(this.clientId!==e.clientId){var t=!this.networkEnabled&&e.networkEnabled,r=!this.inForeground&&e.inForeground,n=this.networkEnabled===e.networkEnabled;if(t||r&&n)return!0}return!1}))}).next(e=>(this.isPrimary!==e&&p(qo,`Client ${e?"is":"is not"} eligible for a primary lease.`),e))}async shutdown(){this.hi=!1,this.ls(),this.qi&&(this.qi.cancel(),this.qi=null),this.hs(),this.Ps(),await this.Ki.runTransaction("shutdown","readwrite",[Et,er],e=>{let t=new Pr(e,mt.le);return this.Xi(t).next(()=>this.ns(t))}),this.Ki.close(),this.Ts()}_s(e,t){return e.filter(e=>this.ss(e.updateTimeMs,t)&&!this.cs(e.clientId))}Is(){return this.runTransaction("getActiveClients","readonly",e=>Go(e).J().next(e=>this._s(e,18e5).map(e=>e.clientId)))}get started(){return this.hi}getGlobalsCache(){return this.Pi}getMutationQueue(e,t){return Xa.bt(e,this.serializer,t,this.referenceDelegate)}getTargetCache(){return this.Ti}getRemoteDocumentCache(){return this.remoteDocumentCache}getIndexManager(e){return new qa(e,this.serializer.wt.databaseId)}getDocumentOverlayCache(e){return ma.bt(this.serializer,e)}getBundleCache(){return this.Ei}runTransaction(t,r,n){p(qo,"Starting transaction:",t);var e,i="readonly"===r?"readonly":"readwrite",s=18===(e=this.Bi)?Vr:17===e?Lr:16===e?Mr:15===e?Or:14===e?Rr:13===e?kr:12===e?Nr:11===e?Dr:void E(60245);let a;return this.Ki.runTransaction(t,i,s,e=>(a=new Pr(e,this.li?this.li.next():mt.le),"readwrite-primary"===r?this.Yi(a).next(e=>!!e||this.Zi(a)).next(e=>{if(e)return n(a);throw d(`Failed to obtain primary lease for action '${t}'.`),this.isPrimary=!1,this.xi.enqueueRetryable(()=>this.$i(!1)),new I(b.FAILED_PRECONDITION,Je)}).next(e=>this.es(a).next(()=>e)):this.Es(a).next(()=>n(a)))).then(e=>(a.raiseOnCommittedEvent(),e))}Es(e){return Ko(e).get(St).next(e=>{if(null!==e&&this.ss(e.leaseTimestampMs,5e3)&&!this.cs(e.ownerId)&&!this.ts(e)&&!(this.Ni||this.allowTabSynchronization&&e.allowTabSynchronization))throw new I(b.FAILED_PRECONDITION,jo)})}es(e){var t={ownerId:this.clientId,allowTabSynchronization:this.allowTabSynchronization,leaseTimestampMs:Date.now()};return Ko(e).put(St,t)}static C(){return nt.C()}Xi(e){let t=Ko(e);return t.get(St).next(e=>this.ts(e)?(p(qo,"Releasing primary lease."),t.delete(St)):w.resolve())}ss(e,t){var r=Date.now();return!(e<r-t||r<e&&(d(`Detected an update time that is in the future: ${e} > `+r),1))}zi(){null!==this.document&&"function"==typeof this.document.addEventListener&&(this.ki=()=>{this.xi.enqueueAndForget(()=>(this.inForeground="visible"===this.document.visibilityState,this.Gi()))},this.document.addEventListener("visibilitychange",this.ki),this.inForeground="visible"===this.document.visibilityState)}hs(){this.ki&&(this.document.removeEventListener("visibilitychange",this.ki),this.ki=null)}ji(){var e;"function"==typeof(null==(e=this.window)?void 0:e.addEventListener)&&(this.Li=()=>{this.ls();var e=/(?:Version|Mobile)\/1[456]/;re()&&(navigator.appVersion.match(e)||navigator.userAgent.match(e))&&this.xi.enterRestrictedMode(!0),this.xi.enqueueAndForget(()=>this.shutdown())},this.window.addEventListener("pagehide",this.Li))}Ps(){this.Li&&(this.window.removeEventListener("pagehide",this.Li),this.Li=null)}cs(e){var t;try{var r=null!==(null==(t=this.Wi)?void 0:t.getItem(this.us(e)));return p(qo,`Client '${e}' ${r?"is":"is not"} zombied in LocalStorage`),r}catch(e){return d(qo,"Failed to get zombied client id.",e),!1}}ls(){if(this.Wi)try{this.Wi.setItem(this.us(this.clientId),String(Date.now()))}catch(e){d("Failed to set zombie client id.",e)}}Ts(){if(this.Wi)try{this.Wi.removeItem(this.us(this.clientId))}catch(e){}}us(e){return`firestore_zombie_${this.persistenceKey}_`+e}}function Ko(e){return r(e,Et)}function Go(e){return r(e,er)}function $o(e,t){let r=e.projectId;return e.isDefaultDatabase||(r+="."+e.database),"firestore/"+t+"/"+r+"/"}class Qo{constructor(e,t,r,n){this.targetId=e,this.fromCache=t,this.ds=r,this.As=n}static Rs(e,t){let r=M(),n=M();for(let e of t.docChanges)switch(e.type){case 0:r=r.add(e.doc.key);break;case 1:n=n.add(e.doc.key)}return new Qo(e,t.fromCache,r,n)}}class Ho{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class Wo{constructor(){this.Vs=!1,this.fs=!1,this.gs=100,this.ps=re()?8:0<it(ee())?6:4}initialize(e,t){this.ys=e,this.indexManager=t,this.Vs=!0}getDocumentsMatchingQuery(r,n,e,t){let i={result:null};return this.ws(r,n).next(e=>{i.result=e}).next(()=>{if(!i.result)return this.bs(r,n,t,e).next(e=>{i.result=e})}).next(()=>{if(!i.result){let t=new Ho;return this.Ss(r,n,t).next(e=>{if(i.result=e,this.fs)return this.Ds(r,n,t,e.size)})}}).next(()=>i.result)}Ds(e,t,r,n){return r.documentReadCount<this.gs?(_e()<=c.DEBUG&&p("QueryEngine","SDK will not create cache indexes for query:",yi(t),"since it only creates cache indexes for collection contains","more than or equal to",this.gs,"documents"),w.resolve()):(_e()<=c.DEBUG&&p("QueryEngine","Query:",yi(t),"scans",r.documentReadCount,"local documents and returns",n,"documents as results."),r.documentReadCount>this.ps*n?(_e()<=c.DEBUG&&p("QueryEngine","The SDK decides to create cache indexes for query:",yi(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,di(t))):w.resolve())}ws(i,s){if(ui(s))return w.resolve(null);let t=di(s);return this.indexManager.getIndexType(i,t).next(e=>0===e?null:(null!==s.limit&&1===e&&(s=gi(s,null,"F"),t=di(s)),this.indexManager.getDocumentsMatchingTarget(i,t).next(e=>{let n=M(...e);return this.ys.getDocuments(i,n).next(r=>this.indexManager.getMinOffset(i,t).next(e=>{var t=this.vs(s,r);return this.Cs(s,t,n,e.readTime)?this.ws(i,gi(s,null,"F")):this.Fs(i,t,s,e)}))})))}bs(r,n,i,s){return ui(n)||s.isEqual(g.min())?w.resolve(null):this.ys.getDocuments(r,i).next(e=>{var t=this.vs(n,e);return this.Cs(n,t,i,s)?w.resolve(null):(_e()<=c.DEBUG&&p("QueryEngine","Re-using previous result from %s to execute query: %s",s.toString(),yi(n)),this.Fs(r,t,n,He(s,je)).next(e=>e))})}vs(r,e){let n=new A(_i(r));return e.forEach((e,t)=>{vi(r,t)&&(n=n.add(t))}),n}Cs(e,t,r,n){var i;return null!==e.limit&&(r.size!==t.size||!!(i="F"===e.limitType?t.last():t.first())&&(i.hasPendingWrites||0<i.version.compareTo(n)))}Ss(e,t,r){return _e()<=c.DEBUG&&p("QueryEngine","Using full collection scan to execute query:",yi(t)),this.ys.getDocumentsMatchingQuery(e,t,Ye.min(),r)}Fs(e,r,t,n){return this.ys.getDocumentsMatchingQuery(e,t,n).next(t=>(r.forEach(e=>{t=t.insert(e.key,e)}),t))}}let Yo="LocalStore",Xo=3e8;class Jo{constructor(e,t,r,n){this.persistence=e,this.Ms=t,this.serializer=n,this.xs=new C(S),this.Os=new bi(e=>ei(e),ti),this.Ns=new Map,this.Bs=e.getRemoteDocumentCache(),this.Ti=e.getTargetCache(),this.Ei=e.getBundleCache(),this.Ls(r)}Ls(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new xo(this.Bs,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Bs.setIndexManager(this.indexManager),this.Ms.initialize(this.localDocuments,this.indexManager)}collectGarbage(t){return this.persistence.runTransaction("Collect garbage","readwrite-primary",e=>t.collect(e,this.xs))}}function Zo(e,t,r,n){return new Jo(e,t,r,n)}async function el(e,t){let o=e;return o.persistence.runTransaction("Handle user change","readonly",s=>{let a;return o.mutationQueue.getAllMutationBatches(s).next(e=>(a=e,o.Ls(t),o.mutationQueue.getAllMutationBatches(s))).next(e=>{let t=[],r=[],n=M();for(let i of a){t.push(i.batchId);for(let e of i.mutations)n=n.add(e.key)}for(let i of e){r.push(i.batchId);for(let e of i.mutations)n=n.add(e.key)}return o.localDocuments.getDocuments(s,n).next(e=>({ks:e,removedBatchIds:t,addedBatchIds:r}))})})}function tl(e,n){let i=e;return i.persistence.runTransaction("Acknowledge batch","readwrite-primary",e=>{let t=n.batch.keys(),r=i.Bs.newChangeBuffer({trackRemovals:!0});return((e,t,n,i)=>{let s=n.batch,r=s.keys(),a=w.resolve();return r.forEach(r=>{a=a.next(()=>i.getEntry(t,r)).next(e=>{var t=n.docVersions.get(r);y(null!==t,48541),e.version.compareTo(t)<0&&(s.applyToRemoteDocument(e,n),e.isValidDocument())&&(e.setReadTime(n.commitVersion),i.addEntry(e))})}),a.next(()=>e.mutationQueue.removeMutationBatch(t,s))})(i,e,n,r).next(()=>r.apply(e)).next(()=>i.mutationQueue.performConsistencyCheck(e)).next(()=>i.documentOverlayCache.removeOverlaysForBatchId(e,t,n.batch.batchId)).next(()=>i.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,(e=>{let t=M();for(let r=0;r<e.mutationResults.length;++r)0<e.mutationResults[r].transformResults.length&&(t=t.add(e.batch.mutations[r].key));return t})(n))).next(()=>i.localDocuments.getDocuments(e,t))})}function rl(e){let t=e;return t.persistence.runTransaction("Get last remote snapshot version","readonly",e=>t.Ti.getLastRemoteSnapshotVersion(e))}function nl(e,u){let h=e,c=u.snapshotVersion,d=h.xs;return h.persistence.runTransaction("Apply remote event","readwrite-primary",o=>{let e=h.Bs.newChangeBuffer({trackRemovals:!0}),l=(d=h.xs,[]),t=(u.targetChanges.forEach((t,r)=>{var n,i,s,a=d.get(r);if(a){l.push(h.Ti.removeMatchingKeys(o,t.removedDocuments,r).next(()=>h.Ti.addMatchingKeys(o,t.addedDocuments,r)));let e=a.withSequenceNumber(o.currentSequenceNumber);null!==u.targetMismatches.get(r)?e=e.withResumeToken(D.EMPTY_BYTE_STRING,g.min()).withLastLimboFreeSnapshotVersion(g.min()):0<t.resumeToken.approximateByteSize()&&(e=e.withResumeToken(t.resumeToken,c)),d=d.insert(r,e),n=a,i=e,s=t,(0===n.resumeToken.approximateByteSize()||i.snapshotVersion.toMicroseconds()-n.snapshotVersion.toMicroseconds()>=Xo||0<s.addedDocuments.size+s.modifiedDocuments.size+s.removedDocuments.size)&&l.push(h.Ti.updateTargetData(o,e))}}),Ii),r=M();if(u.documentUpdates.forEach(e=>{u.resolvedLimboDocuments.has(e)&&l.push(h.persistence.referenceDelegate.updateLimboDocument(o,e))}),l.push(il(o,e,u.documentUpdates).next(e=>{t=e.qs,r=e.Qs})),!c.isEqual(g.min())){let e=h.Ti.getLastRemoteSnapshotVersion(o).next(e=>h.Ti.setTargetsMetadata(o,o.currentSequenceNumber,c));l.push(e)}return w.waitFor(l).next(()=>e.apply(o)).next(()=>h.localDocuments.getLocalViewOfDocuments(o,t,r)).next(()=>t)}).then(e=>(h.xs=d,e))}function il(e,s,t){let r=M(),a=M();return t.forEach(e=>r=r.add(e)),s.getEntries(e,r).next(n=>{let i=Ii;return t.forEach((e,t)=>{var r=n.get(e);t.isFoundDocument()!==r.isFoundDocument()&&(a=a.add(e)),t.isNoDocument()&&t.version.isEqual(g.min())?(s.removeEntry(e,t.readTime),i=i.insert(e,t)):!r.isValidDocument()||0<t.version.compareTo(r.version)||0===t.version.compareTo(r.version)&&r.hasPendingWrites?(s.addEntry(t),i=i.insert(e,t)):p(Yo,"Ignoring outdated watch update for ",e,". Current version:",r.version," Watch version:",t.version)}),{qs:i,Qs:a}})}function sl(e,n){let i=e;return i.persistence.runTransaction("Allocate target","readwrite",t=>{let r;return i.Ti.getTargetData(t,n).next(e=>e?(r=e,w.resolve(r)):i.Ti.allocateTargetId(t).next(e=>(r=new Zs(n,e,"TargetPurposeListen",t.currentSequenceNumber),i.Ti.addTargetData(t,r).next(()=>r))))}).then(e=>{var t=i.xs.get(e.targetId);return(null===t||0<e.snapshotVersion.compareTo(t.snapshotVersion))&&(i.xs=i.xs.insert(e.targetId,e),i.Os.set(n,e.targetId)),e})}async function al(e,t,r){let n=e,i=n.xs.get(t),s=r?"readwrite":"readwrite-primary";try{r||await n.persistence.runTransaction("Release target",s,e=>n.persistence.referenceDelegate.removeTarget(e,i))}catch(e){if(!ot(e))throw e;p(Yo,`Failed to update sequence numbers for target ${t}: `+e)}n.xs=n.xs.remove(t),n.Os.delete(i.target)}function ol(e,a,o){let l=e,u=g.min(),h=M();return l.persistence.runTransaction("Execute query","readwrite",t=>{return e=l,r=t,n=di(a),(void 0!==(s=(i=e).Os.get(n))?w.resolve(i.xs.get(s)):i.Ti.getTargetData(r,n)).next(e=>{if(e)return u=e.lastLimboFreeSnapshotVersion,l.Ti.getMatchingKeysForTargetId(t,e.targetId).next(e=>{h=e})}).next(()=>l.Ms.getDocumentsMatchingQuery(t,a,o?u:g.min(),o?h:M())).next(e=>(hl(l,wi(a),e),{documents:e,$s:h}));var e,r,n,i,s})}function ll(e,t){let r=e,n=r.Ti,i=r.xs.get(t);return i?Promise.resolve(i.target):r.persistence.runTransaction("Get target data","readonly",e=>n.Rt(e,t).next(e=>e?e.target:null))}function ul(e,t){let r=e,n=r.Ns.get(t)||g.min();return r.persistence.runTransaction("Get new document changes","readonly",e=>r.Bs.getAllFromCollectionGroup(e,t,He(n,je),Number.MAX_SAFE_INTEGER)).then(e=>(hl(r,t,e),e))}function hl(e,t,r){let n=e.Ns.get(t)||g.min();r.forEach((e,t)=>{0<t.readTime.compareTo(n)&&(n=t.readTime)}),e.Ns.set(t,n)}let cl="firestore_clients";function dl(e,t){return cl+`_${e}_`+t}let fl="firestore_mutations";function gl(e,t,r){let n=fl+`_${e}_`+r;return t.isAuthenticated()&&(n+="_"+t.uid),n}let ml="firestore_targets";function pl(e,t){return ml+`_${e}_`+t}let yl="SharedClientState";class vl{constructor(e,t,r,n){this.user=e,this.batchId=t,this.state=r,this.error=n}static Gs(e,t,r){var n=JSON.parse(r);let i,s="object"==typeof n&&-1!==["pending","acknowledged","rejected"].indexOf(n.state)&&(void 0===n.error||"object"==typeof n.error);return s&&n.error&&(s="string"==typeof n.error.message&&"string"==typeof n.error.code)&&(i=new I(n.error.code,n.error.message)),s?new vl(e,t,n.state,i):(d(yl,`Failed to parse mutation state for ID '${t}': `+r),null)}zs(){var e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class wl{constructor(e,t,r){this.targetId=e,this.state=t,this.error=r}static Gs(e,t){var r=JSON.parse(t);let n,i="object"==typeof r&&-1!==["not-current","current","rejected"].indexOf(r.state)&&(void 0===r.error||"object"==typeof r.error);return i&&r.error&&(i="string"==typeof r.error.message&&"string"==typeof r.error.code)&&(n=new I(r.error.code,r.error.message)),i?new wl(e,r.state,n):(d(yl,`Failed to parse target state for ID '${e}': `+t),null)}zs(){var e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class _l{constructor(e,t){this.clientId=e,this.activeTargetIds=t}static Gs(e,t){var r=JSON.parse(t);let n="object"==typeof r&&r.activeTargetIds instanceof Array,i=Di;for(let s=0;n&&s<r.activeTargetIds.length;++s)n=wt(r.activeTargetIds[s]),i=i.add(r.activeTargetIds[s]);return n?new _l(e,i):(d(yl,`Failed to parse client data for instance '${e}': `+t),null)}}class bl{constructor(e,t){this.clientId=e,this.onlineState=t}static Gs(e){var t=JSON.parse(e);return"object"==typeof t&&-1!==["Unknown","Online","Offline"].indexOf(t.onlineState)&&"string"==typeof t.clientId?new bl(t.clientId,t.onlineState):(d(yl,"Failed to parse online state: "+e),null)}}class Il{constructor(){this.activeTargetIds=Di}js(e){this.activeTargetIds=this.activeTargetIds.add(e)}Hs(e){this.activeTargetIds=this.activeTargetIds.delete(e)}zs(){var e={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(e)}}class Tl{constructor(e,t,r,n,i){this.window=e,this.xi=t,this.persistenceKey=r,this.Js=n,this.syncEngine=null,this.onlineStateHandler=null,this.sequenceNumberHandler=null,this.Ys=this.Zs.bind(this),this.Xs=new C(S),this.started=!1,this.eo=[];var s=r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");this.storage=this.window.localStorage,this.currentUser=i,this.no=dl(this.persistenceKey,this.Js),this.ro="firestore_sequence_number_"+this.persistenceKey,this.Xs=this.Xs.insert(this.Js,new Il),this.io=new RegExp(`^${cl}_${s}_([^_]*)$`),this.so=new RegExp(`^${fl}_${s}_(\\d+)(?:_(.*))?$`),this.oo=new RegExp(`^${ml}_${s}_(\\d+)$`),this._o="firestore_online_state_"+this.persistenceKey,this.ao="firestore_bundle_loaded_v2_"+this.persistenceKey,this.window.addEventListener("storage",this.Ys)}static C(e){return!(!e||!e.localStorage)}async start(){let e=await this.syncEngine.Is();for(let r of e)if(r!==this.Js){let e=this.getItem(dl(this.persistenceKey,r));var t;e&&(t=_l.Gs(r,e))&&(this.Xs=this.Xs.insert(t.clientId,t))}this.uo();let r=this.storage.getItem(this._o);if(r){let e=this.co(r);e&&this.lo(e)}for(let e of this.eo)this.Zs(e);this.eo=[],this.window.addEventListener("pagehide",()=>this.shutdown()),this.started=!0}writeSequenceNumber(e){this.setItem(this.ro,JSON.stringify(e))}getAllActiveQueryTargets(){return this.ho(this.Xs)}isActiveQueryTarget(r){let n=!1;return this.Xs.forEach((e,t)=>{t.activeTargetIds.has(r)&&(n=!0)}),n}addPendingMutation(e){this.Po(e,"pending")}updateMutationState(e,t,r){this.Po(e,t,r),this.To(e)}addLocalQueryTarget(t,e=!0){let r="not-current";if(this.isActiveQueryTarget(t)){let e=this.storage.getItem(pl(this.persistenceKey,t));var n;e&&(n=wl.Gs(t,e))&&(r=n.state)}return e&&this.Io.js(t),this.uo(),r}removeLocalQueryTarget(e){this.Io.Hs(e),this.uo()}isLocalQueryTarget(e){return this.Io.activeTargetIds.has(e)}clearQueryState(e){this.removeItem(pl(this.persistenceKey,e))}updateQueryState(e,t,r){this.Eo(e,t,r)}handleUserChange(e,t,r){t.forEach(e=>{this.To(e)}),this.currentUser=e,r.forEach(e=>{this.addPendingMutation(e)})}setOnlineState(e){this.Ao(e)}notifyBundleLoaded(e){this.Ro(e)}shutdown(){this.started&&(this.window.removeEventListener("storage",this.Ys),this.removeItem(this.no),this.started=!1)}getItem(e){var t=this.storage.getItem(e);return p(yl,"READ",e,t),t}setItem(e,t){p(yl,"SET",e,t),this.storage.setItem(e,t)}removeItem(e){p(yl,"REMOVE",e),this.storage.removeItem(e)}Zs(e){let t=e;t.storageArea===this.storage&&(p(yl,"EVENT",t.key,t.newValue),t.key===this.no?d("Received WebStorage notification for local change. Another client might have garbage-collected our state"):this.xi.enqueueRetryable(async()=>{if(this.started){if(null!==t.key){if(this.io.test(t.key))return null==t.newValue?(e=this.Vo(t.key),this.mo(e,null)):(e=this.fo(t.key,t.newValue))?this.mo(e.clientId,e):void 0;if(this.so.test(t.key)){if(null!==t.newValue){var e=this.po(t.key,t.newValue);if(e)return this.yo(e)}}else if(this.oo.test(t.key)){if(null!==t.newValue){var e=this.wo(t.key,t.newValue);if(e)return this.bo(e)}}else if(t.key===this._o){if(null!==t.newValue){var e=this.co(t.newValue);if(e)return this.lo(e)}}else t.key===this.ro?(e=(e=>{let t=mt.le;if(null!=e)try{var r=JSON.parse(e);y("number"==typeof r,30636,{So:e}),t=r}catch(e){d(yl,"Failed to read sequence number from WebStorage",e)}return t})(t.newValue))!==mt.le&&this.sequenceNumberHandler(e):t.key===this.ao&&(e=this.Do(t.newValue),await Promise.all(e.map(e=>this.syncEngine.vo(e))))}}else this.eo.push(t)}))}get Io(){return this.Xs.get(this.Js)}uo(){this.setItem(this.no,this.Io.zs())}Po(e,t,r){var n=new vl(this.currentUser,e,t,r),i=gl(this.persistenceKey,this.currentUser,e);this.setItem(i,n.zs())}To(e){var t=gl(this.persistenceKey,this.currentUser,e);this.removeItem(t)}Ao(e){var t={clientId:this.Js,onlineState:e};this.storage.setItem(this._o,JSON.stringify(t))}Eo(e,t,r){var n=pl(this.persistenceKey,e),i=new wl(e,t,r);this.setItem(n,i.zs())}Ro(e){var t=JSON.stringify(Array.from(e));this.setItem(this.ao,t)}Vo(e){var t=this.io.exec(e);return t?t[1]:null}fo(e,t){var r=this.Vo(e);return _l.Gs(r,t)}po(e,t){var r=this.so.exec(e),n=Number(r[1]),r=void 0!==r[2]?r[2]:null;return vl.Gs(new u(r),n,t)}wo(e,t){var r=this.oo.exec(e),r=Number(r[1]);return wl.Gs(r,t)}co(e){return bl.Gs(e)}Do(e){return JSON.parse(e)}async yo(e){if(e.user.uid===this.currentUser.uid)return this.syncEngine.Co(e.batchId,e.state,e.error);p(yl,"Ignoring mutation for non-active user "+e.user.uid)}bo(e){return this.syncEngine.Fo(e.targetId,e.state,e.error)}mo(e,t){let r=t?this.Xs.insert(e,t):this.Xs.remove(e),n=this.ho(this.Xs),i=this.ho(r),s=[],a=[];return i.forEach(e=>{n.has(e)||s.push(e)}),n.forEach(e=>{i.has(e)||a.push(e)}),this.syncEngine.Mo(s,a).then(()=>{this.Xs=r})}lo(e){this.Xs.get(e.clientId)&&this.onlineStateHandler(e.onlineState)}ho(e){let r=Di;return e.forEach((e,t)=>{r=r.unionWith(t.activeTargetIds)}),r}}class El{constructor(){this.xo=new Il,this.Oo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,r){}addLocalQueryTarget(e,t=!0){return t&&this.xo.js(e),this.Oo[e]||"not-current"}updateQueryState(e,t,r){this.Oo[e]=t}removeLocalQueryTarget(e){this.xo.Hs(e)}isLocalQueryTarget(e){return this.xo.activeTargetIds.has(e)}clearQueryState(e){delete this.Oo[e]}getAllActiveQueryTargets(){return this.xo.activeTargetIds}isActiveQueryTarget(e){return this.xo.activeTargetIds.has(e)}start(){return this.xo=new Il,Promise.resolve()}handleUserChange(e,t,r){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class Sl{No(e){}shutdown(){}}let xl="ConnectivityMonitor";class Cl{constructor(){this.Bo=()=>this.Lo(),this.ko=()=>this.qo(),this.Qo=[],this.$o()}No(e){this.Qo.push(e)}shutdown(){window.removeEventListener("online",this.Bo),window.removeEventListener("offline",this.ko)}$o(){window.addEventListener("online",this.Bo),window.addEventListener("offline",this.ko)}Lo(){p(xl,"Network connectivity changed: AVAILABLE");for(var e of this.Qo)e(0)}qo(){p(xl,"Network connectivity changed: UNAVAILABLE");for(var e of this.Qo)e(1)}static C(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let Al=null;function Dl(){return null===Al?Al=268435456+Math.round(2147483648*Math.random()):Al++,"0x"+Al.toString(16)}let Nl="RestConnection",kl={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class Rl{constructor(e){this.Zo=e.Zo,this.Xo=e.Xo}e_(e){this.t_=e}n_(e){this.r_=e}i_(e){this.s_=e}onMessage(e){this.o_=e}close(){this.Xo()}send(e){this.Zo(e)}__(){this.t_()}a_(){this.r_()}u_(e){this.s_(e)}c_(e){this.o_(e)}}let Ol="WebChannelConnection";class Ml extends class{get Uo(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;var t=e.ssl?"https":"http",r=encodeURIComponent(this.databaseId.projectId),n=encodeURIComponent(this.databaseId.database);this.Ko=t+"://"+e.host,this.Wo=`projects/${r}/databases/`+n,this.Go=this.databaseId.database===sn?"project_id="+r:`project_id=${r}&database_id=`+n}zo(t,e,r,n,i){let s=Dl(),a=this.jo(t,e.toUriEncodedString());p(Nl,`Sending RPC '${t}' ${s}:`,a,r);var o={"google-cloud-resource-prefix":this.Wo,"x-goog-request-params":this.Go},l=(this.Ho(o,n,i),new URL(a)).host,l=Y(l);return this.Jo(t,a,o,r,l).then(e=>(p(Nl,`Received RPC '${t}' ${s}: `,e),e),e=>{throw be(Nl,`RPC '${t}' ${s} failed with error: `,e,"url: ",a,"request:",r),e})}Yo(e,t,r,n,i,s){return this.zo(e,t,r,n,i)}Ho(r,e,t){r["X-Goog-Api-Client"]="gl-js/ fire/"+ve,r["Content-Type"]="text/plain",this.databaseInfo.appId&&(r["X-Firebase-GMPID"]=this.databaseInfo.appId),e&&e.headers.forEach((e,t)=>r[t]=e),t&&t.headers.forEach((e,t)=>r[t]=e)}jo(e,t){var r=kl[e];return this.Ko+`/v1/${t}:`+r}terminate(){}}{constructor(e){super(e),this.l_=[],this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Jo(l,t,r,n,e){let u=Dl();return new Promise((s,a)=>{let o=new pr;o.setWithCredentials(!0),o.listenOnce(vr.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case wr.NO_ERROR:var e=o.getResponseJson();p(Ol,`XHR for RPC '${l}' ${u} received:`,JSON.stringify(e)),s(e);break;case wr.TIMEOUT:p(Ol,`RPC '${l}' ${u} timed out`),a(new I(b.DEADLINE_EXCEEDED,"Request time out"));break;case wr.HTTP_ERROR:var t=o.getStatus();if(p(Ol,`RPC '${l}' ${u} failed with status:`,t,"response text:",o.getResponseText()),0<t){let e=o.getResponseJson();var r=null==(e=Array.isArray(e)?e[0]:e)?void 0:e.error;if(r&&r.status&&r.message){n=r.status,i=n.toLowerCase().replace(/_/g,"-");let e=0<=Object.values(b).indexOf(i)?i:b.UNKNOWN;a(new I(e,r.message))}else a(new I(b.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new I(b.UNAVAILABLE,"Connection failed."));break;default:E(9055,{h_:l,streamId:u,P_:o.getLastErrorCode(),T_:o.getLastError()})}}finally{p(Ol,`RPC '${l}' ${u} completed.`)}var n,i});var e=JSON.stringify(n);p(Ol,`RPC '${l}' ${u} sending request:`,n),o.send(t,"POST",e,r,15)})}I_(i,e,t){let s=Dl(),r=[this.Ko,"/","google.firestore.v1.Firestore","/",i,"/channel"],n=Tr(),a=Ir(),o={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/`+this.databaseId.database},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},l=this.longPollingOptions.timeoutSeconds;void 0!==l&&(o.longPollingTimeout=Math.round(1e3*l)),this.useFetchStreams&&(o.useFetchStreams=!0),this.Ho(o.initMessageHeaders,e,t),o.encodeInitMessageHeaders=!0;var u=r.join("");p(Ol,`Creating RPC '${i}' stream ${s}: `+u,o);let h=n.createWebChannel(u,o),c=(this.E_(h),!1),d=!1,f=new Rl({Zo:e=>{d?p(Ol,`Not sending because RPC '${i}' stream ${s} is closed:`,e):(c||(p(Ol,`Opening RPC '${i}' stream ${s} transport.`),h.open(),c=!0),p(Ol,`RPC '${i}' stream ${s} sending:`,e),h.send(e))},Xo:()=>h.close()}),g=(e,t,r)=>{e.listen(t,e=>{try{r(e)}catch(e){setTimeout(()=>{throw e},0)}})};return g(h,yr.EventType.OPEN,()=>{d||(p(Ol,`RPC '${i}' stream ${s} transport opened.`),f.__())}),g(h,yr.EventType.CLOSE,()=>{d||(d=!0,p(Ol,`RPC '${i}' stream ${s} transport closed`),f.u_(),this.d_(h))}),g(h,yr.EventType.ERROR,e=>{d||(d=!0,be(Ol,`RPC '${i}' stream ${s} transport errored. Name:`,e.name,"Message:",e.message),f.u_(new I(b.UNAVAILABLE,"The operation could not be completed")))}),g(h,yr.EventType.MESSAGE,e=>{if(!d){var t=e.data[0],n=(y(!!t,16349),t),n=(null==n?void 0:n.error)||(null==(n=n[0])?void 0:n.error);if(n){p(Ol,`RPC '${i}' stream ${s} received error:`,n);let e=n.status,t=(e=>{var t=m[e];if(void 0!==t)return ls(t)})(e),r=n.message;void 0===t&&(t=b.INTERNAL,r="Unknown error status: "+e+" with message "+n.message),d=!0,f.u_(new I(t,r)),h.close()}else p(Ol,`RPC '${i}' stream ${s} received:`,t),f.c_(t)}}),g(a,br.STAT_EVENT,e=>{e.stat===_r.PROXY?p(Ol,`RPC '${i}' stream ${s} detected buffering proxy`):e.stat===_r.NOPROXY&&p(Ol,`RPC '${i}' stream ${s} detected no buffering proxy`)}),setTimeout(()=>{f.a_()},0),f}terminate(){this.l_.forEach(e=>e.close()),this.l_=[]}E_(e){this.l_.push(e)}d_(t){this.l_=this.l_.filter(e=>e===t)}}function Ll(){return"undefined"!=typeof window?window:null}function Vl(){return"undefined"!=typeof document?document:null}function Pl(e){return new xs(e,!0)}class Fl{constructor(e,t,r=1e3,n=1.5,i=6e4){this.xi=e,this.timerId=t,this.A_=r,this.R_=n,this.V_=i,this.m_=0,this.f_=null,this.g_=Date.now(),this.reset()}reset(){this.m_=0}p_(){this.m_=this.V_}y_(e){this.cancel();var t=Math.floor(this.m_+this.w_()),r=Math.max(0,Date.now()-this.g_),n=Math.max(0,t-r);0<n&&p("ExponentialBackoff",`Backing off for ${n} ms (base delay: ${this.m_} ms, delay with jitter: ${t} ms, last attempt: ${r} ms ago)`),this.f_=this.xi.enqueueAfterDelay(this.timerId,n,()=>(this.g_=Date.now(),e())),this.m_*=this.R_,this.m_<this.A_&&(this.m_=this.A_),this.m_>this.V_&&(this.m_=this.V_)}b_(){null!==this.f_&&(this.f_.skipDelay(),this.f_=null)}cancel(){null!==this.f_&&(this.f_.cancel(),this.f_=null)}w_(){return(Math.random()-.5)*this.m_}}let Bl="PersistentStream";class Ul{constructor(e,t,r,n,i,s,a,o){this.xi=e,this.S_=r,this.D_=n,this.connection=i,this.authCredentialsProvider=s,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.v_=0,this.C_=null,this.F_=null,this.stream=null,this.M_=0,this.x_=new Fl(e,t)}O_(){return 1===this.state||5===this.state||this.N_()}N_(){return 2===this.state||3===this.state}start(){this.M_=0,4!==this.state?this.auth():this.B_()}async stop(){this.O_()&&await this.close(0)}L_(){this.state=0,this.x_.reset()}k_(){this.N_()&&null===this.C_&&(this.C_=this.xi.enqueueAfterDelay(this.S_,6e4,()=>this.q_()))}Q_(e){this.U_(),this.stream.send(e)}async q_(){if(this.N_())return this.close(0)}U_(){this.C_&&(this.C_.cancel(),this.C_=null)}K_(){this.F_&&(this.F_.cancel(),this.F_=null)}async close(e,t){this.U_(),this.K_(),this.x_.cancel(),this.v_++,4!==e?this.x_.reset():t&&t.code===b.RESOURCE_EXHAUSTED?(d(t.toString()),d("Using maximum backoff delay to prevent overloading the backend."),this.x_.p_()):t&&t.code===b.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.W_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.i_(t)}W_(){}auth(){this.state=1;let e=this.G_(this.v_),r=this.v_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,t])=>{this.v_===r&&this.z_(e,t)},t=>{e(()=>{var e=new I(b.UNKNOWN,"Fetching auth token failed: "+t.message);return this.j_(e)})})}z_(e,t){let r=this.G_(this.v_);this.stream=this.H_(e,t),this.stream.e_(()=>{r(()=>this.listener.e_())}),this.stream.n_(()=>{r(()=>(this.state=2,this.F_=this.xi.enqueueAfterDelay(this.D_,1e4,()=>(this.N_()&&(this.state=3),Promise.resolve())),this.listener.n_()))}),this.stream.i_(e=>{r(()=>this.j_(e))}),this.stream.onMessage(e=>{r(()=>1==++this.M_?this.J_(e):this.onNext(e))})}B_(){this.state=5,this.x_.y_(async()=>{this.state=0,this.start()})}j_(e){return p(Bl,"close with error: "+e),this.stream=null,this.close(4,e)}G_(t){return e=>{this.xi.enqueueAndForget(()=>this.v_===t?e():(p(Bl,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class ql extends Ul{constructor(e,t,r,n,i,s){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,r,n,s),this.serializer=i}H_(e,t){return this.connection.I_("Listen",e,t)}J_(e){return this.onNext(e)}onNext(e){this.x_.reset();var t=((e,t)=>{let r;if("targetChange"in t){t.targetChange;var n="NO_CHANGE"===(l=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===l?1:"REMOVE"===l?2:"CURRENT"===l?3:"RESET"===l?4:E(39313,{state:l}),i=t.targetChange.targetIds||[],s=(l=t.targetChange.resumeToken,e.useProto3Json?(y(void 0===l||"string"==typeof l,58123),D.fromBase64String(l||"")):(y(void 0===l||l instanceof Buffer||l instanceof Uint8Array,16193),D.fromUint8Array(l||new Uint8Array))),a=t.targetChange.cause,a=a&&(a=void 0===(l=a).code?b.UNKNOWN:ls(l.code),new I(a,l.message||""));r=new vs(n,i,s,a||null)}else if("documentChange"in t){t.documentChange;var n=t.documentChange,i=(n.document,n.document.name,n.document.updateTime,Ms(e,n.document.name)),s=V(n.document.updateTime),a=n.document.createTime?V(n.document.createTime):g.min(),o=new Rn({mapValue:{fields:n.document.fields}}),i=k.newFoundDocument(i,s,a,o),s=n.targetIds||[],o=n.removedTargetIds||[];r=new ps(s,o,i.key,i)}else if("documentDelete"in t){t.documentDelete;n=t.documentDelete,s=(n.document,Ms(e,n.document)),o=n.readTime?V(n.readTime):g.min(),i=k.newNoDocument(s,o),s=n.removedTargetIds||[];r=new ps([],s,i.key,i)}else if("documentRemove"in t){t.documentRemove;o=t.documentRemove,n=(o.document,Ms(e,o.document)),s=o.removedTargetIds||[];r=new ps([],s,n,null)}else{if(!("filter"in t))return E(11601,{Vt:t});{t.filter;let e=t.filter;e.targetId;var{count:i=0,unchangedNames:o}=e,s=new as(i,o),n=e.targetId;r=new ys(n,s)}}var l;return r})(this.serializer,e),r="targetChange"in(e=e)&&(!(r=e.targetChange).targetIds||!r.targetIds.length)&&r.readTime?V(r.readTime):g.min();return this.listener.Y_(t,r)}Z_(e){var t={},r=(t.database=Ps(this.serializer),t.addTarget=((t,r)=>{var n;let e=r.target;if((n=ri(e)?{documents:Ks(t,e)}:{query:Gs(t,e).gt}).targetId=r.targetId,0<r.resumeToken.approximateByteSize()){n.resumeToken=Ds(t,r.resumeToken);let e=Cs(t,r.expectedCount);null!==e&&(n.expectedCount=e)}else if(0<r.snapshotVersion.compareTo(g.min())){n.readTime=As(t,r.snapshotVersion.toTimestamp());let e=Cs(t,r.expectedCount);null!==e&&(n.expectedCount=e)}return n})(this.serializer,e),Qs(this.serializer,e));r&&(t.labels=r),this.Q_(t)}X_(e){var t={};t.database=Ps(this.serializer),t.removeTarget=e,this.Q_(t)}}class jl extends Ul{constructor(e,t,r,n,i,s){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,r,n,s),this.serializer=i}get ea(){return 0<this.M_}start(){this.lastStreamToken=void 0,super.start()}W_(){this.ea&&this.ta([])}H_(e,t){return this.connection.I_("Write",e,t)}J_(e){return y(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,y(!e.writeResults||0===e.writeResults.length,55816),this.listener.na()}onNext(e){y(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.x_.reset();var t=zs(e.writeResults,e.commitTime),r=V(e.commitTime);return this.listener.ra(r,t)}ia(){var e={};e.database=Ps(this.serializer),this.Q_(e)}ta(e){var t={streamToken:this.lastStreamToken,writes:e.map(e=>qs(this.serializer,e))};this.Q_(t)}}class zl extends class{}{constructor(e,t,r,n){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=r,this.serializer=n,this.sa=!1}oa(){if(this.sa)throw new I(b.FAILED_PRECONDITION,"The client has already been terminated.")}zo(r,n,i,s){return this.oa(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([e,t])=>this.connection.zo(r,ks(n,i),s,e,t)).catch(e=>{throw"FirebaseError"===e.name?(e.code===b.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new I(b.UNKNOWN,e.toString())})}Yo(r,n,i,s,a){return this.oa(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([e,t])=>this.connection.Yo(r,ks(n,i),s,e,t,a)).catch(e=>{throw"FirebaseError"===e.name?(e.code===b.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new I(b.UNKNOWN,e.toString())})}terminate(){this.sa=!0,this.connection.terminate()}}class Kl{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this._a=0,this.aa=null,this.ua=!0}ca(){0===this._a&&(this.la("Unknown"),this.aa=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this.aa=null,this.ha("Backend didn't respond within 10 seconds."),this.la("Offline"),Promise.resolve())))}Pa(e){"Online"===this.state?this.la("Unknown"):(this._a++,1<=this._a&&(this.Ta(),this.ha("Connection failed 1 times. Most recent error: "+e.toString()),this.la("Offline")))}set(e){this.Ta(),this._a=0,"Online"===e&&(this.ua=!1),this.la(e)}la(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}ha(e){var t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.ua?(d(t),this.ua=!1):p("OnlineStateTracker",t)}Ta(){null!==this.aa&&(this.aa.cancel(),this.aa=null)}}let Gl="RemoteStore";class $l{constructor(e,t,r,n,i){this.localStore=e,this.datastore=t,this.asyncQueue=r,this.remoteSyncer={},this.Ia=[],this.Ea=new Map,this.da=new Set,this.Aa=[],this.Ra=i,this.Ra.No(e=>{r.enqueueAndForget(async()=>{var e;tu(this)&&(p(Gl,"Restarting streams for network reachability change."),(e=this).da.add(4),await Hl(e),e.Va.set("Unknown"),e.da.delete(4),await Ql(e))})}),this.Va=new Kl(r,n)}}async function Ql(e){if(tu(e))for(var t of e.Aa)await t(!0)}async function Hl(e){for(var t of e.Aa)await t(!1)}function Wl(e,t){var r=e;r.Ea.has(t.targetId)||(r.Ea.set(t.targetId,t),eu(r)?Zl(r):hu(r).N_()&&Xl(r,t))}function Yl(e,t){var r=e,n=hu(r);r.Ea.delete(t),n.N_()&&Jl(r,t),0===r.Ea.size&&(n.N_()?n.k_():tu(r)&&r.Va.set("Unknown"))}function Xl(e,t){var r;e.ma.Ke(t.targetId),(0<t.resumeToken.approximateByteSize()||0<t.snapshotVersion.compareTo(g.min()))&&(r=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size,t=t.withExpectedCount(r)),hu(e).Z_(t)}function Jl(e,t){e.ma.Ke(t),hu(e).X_(t)}function Zl(t){t.ma=new _s({getRemoteKeysForTarget:e=>t.remoteSyncer.getRemoteKeysForTarget(e),Rt:e=>t.Ea.get(e)||null,Pt:()=>t.datastore.serializer.databaseId}),hu(t).start(),t.Va.ca()}function eu(e){return tu(e)&&!hu(e).O_()&&0<e.Ea.size}function tu(e){return 0===e.da.size}function ru(e){e.ma=void 0}async function nu(e,t,r){if(!ot(t))throw t;e.da.add(1),await Hl(e),e.Va.set("Offline"),r=r||(()=>rl(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{p(Gl,"Retrying IndexedDB access"),await r(),e.da.delete(1),await Ql(e)})}function iu(t,r){return r().catch(e=>nu(t,e,r))}async function su(e){var t,r,n,i,s=e,a=cu(s);let o=0<s.Ia.length?s.Ia[s.Ia.length-1].batchId:pt;for(;tu(i=s)&&i.Ia.length<10;)try{let e=await((e,t)=>{let r=e;return r.persistence.runTransaction("Get next mutation batch","readonly",e=>(void 0===t&&(t=pt),r.mutationQueue.getNextMutationBatchAfterBatchId(e,t)))})(s.localStore,o);if(null===e){0===s.Ia.length&&a.k_();break}o=e.batchId,t=s,r=e,n=void 0,t.Ia.push(r),(n=cu(t)).N_()&&n.ea&&n.ta(r.mutations)}catch(e){await nu(s,e)}au(s)&&ou(s)}function au(e){return tu(e)&&!cu(e).O_()&&0<e.Ia.length}function ou(e){cu(e).start()}async function lu(e,t){var r=e,n=(r.asyncQueue.verifyOperationInProgress(),p(Gl,"RemoteStore received new credentials"),tu(r));r.da.add(3),await Hl(r),n&&r.Va.set("Unknown"),await r.remoteSyncer.handleCredentialChange(t),r.da.delete(3),await Ql(r)}async function uu(e,t){var r=e;t?(r.da.delete(2),await Ql(r)):(r.da.add(2),await Hl(r),r.Va.set("Unknown"))}function hu(t){return t.fa||(t.fa=(e=t.datastore,r=t.asyncQueue,n={e_:(async function(e){e.Va.set("Online")}).bind(null,t),n_:(async function(r){r.Ea.forEach((e,t)=>{Xl(r,e)})}).bind(null,t),i_:(async function(e,t){ru(e),eu(e)?(e.Va.Pa(t),Zl(e)):e.Va.set("Unknown")}).bind(null,t),Y_:(async function(t,e,r){if(t.Va.set("Online"),e instanceof vs&&2===e.state&&e.cause)try{var n,i=t,s=e,a=s.cause;for(n of s.targetIds)i.Ea.has(n)&&(await i.remoteSyncer.rejectListen(n,a),i.Ea.delete(n),i.ma.removeTarget(n))}catch(r){p(Gl,"Failed to remove targets %s: %s ",e.targetIds.join(","),r),await nu(t,r)}else if(e instanceof ps?t.ma.Xe(e):e instanceof ys?t.ma.ot(e):t.ma.nt(e),!r.isEqual(g.min()))try{let e=await rl(t.localStore);0<=r.compareTo(e)&&(l=r,(u=(o=t).ma.It(l)).targetChanges.forEach((e,t)=>{var r;0<e.resumeToken.approximateByteSize()&&(r=o.Ea.get(t))&&o.Ea.set(t,r.withResumeToken(e.resumeToken,l))}),u.targetMismatches.forEach((e,t)=>{var r=o.Ea.get(e);r&&(o.Ea.set(e,r.withResumeToken(D.EMPTY_BYTE_STRING,r.snapshotVersion)),Jl(o,e),r=new Zs(r.target,e,t,r.sequenceNumber),Xl(o,r))}),await o.remoteSyncer.applyRemoteEvent(u))}catch(e){p(Gl,"Failed to raise snapshot:",e),await nu(t,e)}var o,l,u}).bind(null,t)},(i=e).oa(),new ql(r,i.connection,i.authCredentials,i.appCheckCredentials,i.serializer,n)),t.Aa.push(async e=>{e?(t.fa.L_(),eu(t)?Zl(t):t.Va.set("Unknown")):(await t.fa.stop(),ru(t))})),t.fa;var e,r,n,i}function cu(t){return t.ga||(t.ga=(e=t.datastore,r=t.asyncQueue,n={e_:()=>Promise.resolve(),n_:(async function(e){cu(e).ia()}).bind(null,t),i_:(async function(e,t){if(t&&cu(e).ea){var r=e,n=t;if(os(t=n.code)&&t!==b.ABORTED){let e=r.Ia.shift();cu(r).L_(),await iu(r,()=>r.remoteSyncer.rejectFailedWrite(e.batchId,n)),await su(r)}await 0}au(e)&&ou(e)}).bind(null,t),na:(async function(e){var t,r=cu(e);for(t of e.Ia)r.ta(t.mutations)}).bind(null,t),ra:(async function(e,t,r){let n=e.Ia.shift(),i=is.from(n,t,r);await iu(e,()=>e.remoteSyncer.applySuccessfulWrite(i)),await su(e)}).bind(null,t)},(i=e).oa(),new jl(r,i.connection,i.authCredentials,i.appCheckCredentials,i.serializer,n)),t.Aa.push(async e=>{e?(t.ga.L_(),await su(t)):(await t.ga.stop(),0<t.Ia.length&&(p(Gl,`Stopping write stream with ${t.Ia.length} pending writes`),t.Ia=[]))})),t.ga;var e,r,n,i}class du{constructor(e,t,r,n,i){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=r,this.op=n,this.removalCallback=i,this.deferred=new f,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,r,n,i){var s=Date.now()+r,s=new du(e,t,s,n,i);return s.start(r),s}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new I(b.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function fu(e,t){if(d("AsyncQueue",t+": "+e),ot(e))return new I(b.UNAVAILABLE,t+": "+e);throw e}class gu{static emptySet(e){return new gu(e.comparator)}constructor(r){this.comparator=r?(e,t)=>r(e,t)||x.comparator(e.key,t.key):(e,t)=>x.comparator(e.key,t.key),this.keyedMap=Ei(),this.sortedSet=new C(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){var t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(r){this.sortedSet.inorderTraversal((e,t)=>(r(e),!1))}add(e){var t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){var t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof gu))return!1;if(this.size!==e.size)return!1;for(var r=this.sortedSet.getIterator(),n=e.sortedSet.getIterator();r.hasNext();){let e=r.getNext().key,t=n.getNext().key;if(!e.isEqual(t))return!1}return!0}toString(){let t=[];return this.forEach(e=>{t.push(e.toString())}),0===t.length?"DocumentSet ()":"DocumentSet (\n  "+t.join("  \n")+"\n)"}copy(e,t){var r=new gu;return r.comparator=this.comparator,r.keyedMap=e,r.sortedSet=t,r}}class mu{constructor(){this.pa=new C(x.comparator)}track(e){var t=e.doc.key,r=this.pa.get(t);!r||0!==e.type&&3===r.type?this.pa=this.pa.insert(t,e):3===e.type&&1!==r.type?this.pa=this.pa.insert(t,{type:r.type,doc:e.doc}):2===e.type&&2===r.type?this.pa=this.pa.insert(t,{type:2,doc:e.doc}):2===e.type&&0===r.type?this.pa=this.pa.insert(t,{type:0,doc:e.doc}):1===e.type&&0===r.type?this.pa=this.pa.remove(t):1===e.type&&2===r.type?this.pa=this.pa.insert(t,{type:1,doc:r.doc}):0===e.type&&1===r.type?this.pa=this.pa.insert(t,{type:2,doc:e.doc}):E(63341,{Vt:e,ya:r})}wa(){let r=[];return this.pa.inorderTraversal((e,t)=>{r.push(t)}),r}}class pu{constructor(e,t,r,n,i,s,a,o,l){this.query=e,this.docs=t,this.oldDocs=r,this.docChanges=n,this.mutatedKeys=i,this.fromCache=s,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=l}static fromInitialDocuments(e,t,r,n,i){let s=[];return t.forEach(e=>{s.push({type:0,doc:e})}),new pu(e,t,gu.emptySet(t),s,r,n,!0,!1,i)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&mi(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;var t=this.docChanges,r=e.docChanges;if(t.length!==r.length)return!1;for(let n=0;n<t.length;n++)if(t[n].type!==r[n].type||!t[n].doc.isEqual(r[n].doc))return!1;return!0}}class yu{constructor(){this.ba=void 0,this.Sa=[]}Da(){return this.Sa.some(e=>e.va())}}class vu{constructor(){this.queries=wu(),this.onlineState="Unknown",this.Ca=new Set}terminate(){var e,r,t,n;e=this,r=new I(b.ABORTED,"Firestore shutting down"),n=(t=e).queries,t.queries=wu(),n.forEach((e,t)=>{for(let e of t.Sa)e.onError(r)})}}function wu(){return new bi(e=>pi(e),mi)}async function _u(t,r){let e=t,n=3;var i=r.query;let s=e.queries.get(i);s?!s.Da()&&r.va()&&(n=2):(s=new yu,n=r.va()?0:1);try{switch(n){case 0:s.ba=await e.onListen(i,!0);break;case 1:s.ba=await e.onListen(i,!1);break;case 2:await e.onFirstRemoteStoreListen(i)}}catch(t){let e=fu(t,`Initialization of query '${yi(r.query)}' failed`);return void r.onError(e)}e.queries.set(i,s),s.Sa.push(r),r.Fa(e.onlineState),s.ba&&r.Ma(s.ba)&&Iu(e)}async function bu(e,t){var r=e,n=t.query;let i=3;var s=r.queries.get(n);if(s){let e=s.Sa.indexOf(t);0<=e&&(s.Sa.splice(e,1),0===s.Sa.length?i=t.va()?0:1:!s.Da()&&t.va()&&(i=2))}switch(i){case 0:return r.queries.delete(n),r.onUnlisten(n,!0);case 1:return r.queries.delete(n),r.onUnlisten(n,!1);case 2:return r.onLastRemoteStoreUnlisten(n);default:return}}function Iu(e){e.Ca.forEach(e=>{e.next()})}(pe=pe||{}).xa="default",pe.Cache="cache";class Tu{constructor(e,t,r){this.query=e,this.Oa=t,this.Na=!1,this.Ba=null,this.onlineState="Unknown",this.options=r||{}}Ma(t){if(!this.options.includeMetadataChanges){let e=[];for(var r of t.docChanges)3!==r.type&&e.push(r);t=new pu(t.query,t.docs,t.oldDocs,e,t.mutatedKeys,t.fromCache,t.syncStateChanged,!0,t.hasCachedResults)}let e=!1;return this.Na?this.La(t)&&(this.Oa.next(t),e=!0):this.ka(t,this.onlineState)&&(this.qa(t),e=!0),this.Ba=t,e}onError(e){this.Oa.error(e)}Fa(e){this.onlineState=e;let t=!1;return this.Ba&&!this.Na&&this.ka(this.Ba,e)&&(this.qa(this.Ba),t=!0),t}ka(e,t){return!e.fromCache||!this.va()||(!this.options.Qa||!("Offline"!==t))&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}La(e){var t;return 0<e.docChanges.length||(t=this.Ba&&this.Ba.hasPendingWrites!==e.hasPendingWrites,!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges)}qa(e){e=pu.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.Na=!0,this.Oa.next(e)}va(){return this.options.source!==pe.Cache}}class Eu{constructor(e,t){this.$a=e,this.byteLength=t}Ua(){return"metadata"in this.$a}}class Su{constructor(e){this.serializer=e}Us(e){return Ms(this.serializer,e)}Ks(e){return e.metadata.exists?Us(this.serializer,e.document,!1):k.newNoDocument(this.Us(e.metadata.name),this.Ws(e.metadata.readTime))}Ws(e){return V(e)}}class xu{constructor(e,t,r){this.Ka=e,this.localStore=t,this.serializer=r,this.queries=[],this.documents=[],this.collectionGroups=new Set,this.progress=Cu(e)}Wa(e){this.progress.bytesLoaded+=e.byteLength;let t=this.progress.documentsLoaded;var r;return e.$a.namedQuery?this.queries.push(e.$a.namedQuery):e.$a.documentMetadata?(this.documents.push({metadata:e.$a.documentMetadata}),e.$a.documentMetadata.exists||++t,r=T.fromString(e.$a.documentMetadata.name),this.collectionGroups.add(r.get(r.length-2))):e.$a.document&&(this.documents[this.documents.length-1].document=e.$a.document,++t),t!==this.progress.documentsLoaded?(this.progress.documentsLoaded=t,Object.assign({},this.progress)):null}Ga(e){let r=new Map,n=new Su(this.serializer);for(var i of e)if(i.metadata.queries){let e=n.Us(i.metadata.name);for(let t of i.metadata.queries){var s=(r.get(t)||M()).add(e);r.set(t,s)}}return r}async complete(){let e=await(async(r,n,e,t)=>{let i=r,s=M(),a=Ii;for(let r of e){let e=n.Us(r.metadata.name),t=(r.document&&(s=s.add(e)),n.Ks(r));t.setReadTime(n.Ws(r.metadata.readTime)),a=a.insert(e,t)}let o=i.Bs.newChangeBuffer({trackRemovals:!0}),l=await sl(i,(r=t,di(li(T.fromString("__bundle__/docs/"+r)))));return i.persistence.runTransaction("Apply bundle documents","readwrite",t=>il(t,o,a).next(e=>(o.apply(t),e)).next(e=>i.Ti.removeMatchingKeysForTargetId(t,l.targetId).next(()=>i.Ti.addMatchingKeys(t,s,l.targetId)).next(()=>i.localDocuments.getLocalViewOfDocuments(t,e.qs,e.Qs)).next(()=>e.qs)))})(this.localStore,new Su(this.serializer),this.documents,this.Ka.id),t=this.Ga(this.documents);for(let e of this.queries)await(async(e,r,n=M())=>{let i=await sl(e,di(la(r.bundledQuery))),s=e;return s.persistence.runTransaction("Save named query","readwrite",e=>{var t=V(r.readTime);return 0<=i.snapshotVersion.compareTo(t)?s.Ei.saveNamedQuery(e,r):(t=i.withResumeToken(D.EMPTY_BYTE_STRING,t),s.xs=s.xs.insert(t.targetId,t),s.Ti.updateTargetData(e,t).next(()=>s.Ti.removeMatchingKeysForTargetId(e,i.targetId)).next(()=>s.Ti.addMatchingKeys(e,n,i.targetId)).next(()=>s.Ei.saveNamedQuery(e,r)))})})(this.localStore,e,t.get(e.name));return this.progress.taskState="Success",{progress:this.progress,za:this.collectionGroups,ja:e}}}function Cu(e){return{taskState:"Running",documentsLoaded:0,bytesLoaded:0,totalDocuments:e.totalDocuments,totalBytes:e.totalBytes}}class Au{constructor(e){this.key=e}}class Du{constructor(e){this.key=e}}class Nu{constructor(e,t){this.query=e,this.Ha=t,this.Ja=null,this.hasCachedResults=!1,this.current=!1,this.Ya=M(),this.mutatedKeys=M(),this.Za=_i(e),this.Xa=new gu(this.Za)}get eu(){return this.Ha}tu(e,t){let o=t?t.nu:new mu,l=(t||this).Xa,u=(t||this).mutatedKeys,h=l,c=!1,d="F"===this.query.limitType&&l.size===this.query.limit?l.last():null,f="L"===this.query.limitType&&l.size===this.query.limit?l.first():null;if(e.inorderTraversal((e,t)=>{var r=l.get(e),n=vi(this.query,t)?t:null,i=!!r&&this.mutatedKeys.has(r.key),s=!!n&&(n.hasLocalMutations||this.mutatedKeys.has(n.key)&&n.hasCommittedMutations);let a=!1;r&&n?r.data.isEqual(n.data)?i!==s&&(o.track({type:3,doc:n}),a=!0):!this.ru(r,n)&&(o.track({type:2,doc:n}),a=!0,d&&0<this.Za(n,d)||f&&this.Za(n,f)<0)&&(c=!0):!r&&n?(o.track({type:0,doc:n}),a=!0):r&&!n&&(o.track({type:1,doc:r}),a=!0,d||f)&&(c=!0),a&&(u=n?(h=h.add(n),s?u.add(e):u.delete(e)):(h=h.delete(e),u.delete(e)))}),null!==this.query.limit)for(;h.size>this.query.limit;){let e="F"===this.query.limitType?h.last():h.first();h=h.delete(e.key),u=u.delete(e.key),o.track({type:1,doc:e})}return{Xa:h,nu:o,Cs:c,mutatedKeys:u}}ru(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,r,n){var i=this.Xa,s=(this.Xa=e.Xa,this.mutatedKeys=e.mutatedKeys,e.nu.wa()),a=(s.sort((e,t)=>{return r=e.type,n=t.type,(i=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return E(20277,{Vt:e})}})(r)-i(n)||this.Za(e.doc,t.doc);var r,n,i}),this.iu(r),n=null!=n&&n,t&&!n?this.su():[]),o=0===this.Ya.size&&this.current&&!n?1:0,l=o!==this.Ja;return this.Ja=o,0!==s.length||l?{snapshot:new pu(this.query,e.Xa,i,s,e.mutatedKeys,0==o,l,!1,!!r&&0<r.resumeToken.approximateByteSize()),ou:a}:{ou:a}}Fa(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({Xa:this.Xa,nu:new mu,mutatedKeys:this.mutatedKeys,Cs:!1},!1)):{ou:[]}}_u(e){return!this.Ha.has(e)&&!!this.Xa.has(e)&&!this.Xa.get(e).hasLocalMutations}iu(e){e&&(e.addedDocuments.forEach(e=>this.Ha=this.Ha.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.Ha=this.Ha.delete(e)),this.current=e.current)}su(){if(!this.current)return[];let t=this.Ya,r=(this.Ya=M(),this.Xa.forEach(e=>{this._u(e.key)&&(this.Ya=this.Ya.add(e.key))}),[]);return t.forEach(e=>{this.Ya.has(e)||r.push(new Du(e))}),this.Ya.forEach(e=>{t.has(e)||r.push(new Au(e))}),r}au(e){this.Ha=e.$s,this.Ya=M();var t=this.tu(e.documents);return this.applyChanges(t,!0)}uu(){return pu.fromInitialDocuments(this.query,this.Xa,this.mutatedKeys,0===this.Ja,this.hasCachedResults)}}let ku="SyncEngine";class Ru{constructor(e,t,r){this.query=e,this.targetId=t,this.view=r}}class Ou{constructor(e){this.key=e,this.cu=!1}}class Mu{constructor(e,t,r,n,i,s){this.localStore=e,this.remoteStore=t,this.eventManager=r,this.sharedClientState=n,this.currentUser=i,this.maxConcurrentLimboResolutions=s,this.lu={},this.hu=new bi(e=>pi(e),mi),this.Pu=new Map,this.Tu=new Set,this.Iu=new C(x.comparator),this.Eu=new Map,this.du=new No,this.Au={},this.Ru=new Map,this.Vu=ro.lr(),this.onlineState="Unknown",this.mu=void 0}get isPrimaryClient(){return!0===this.mu}}async function Lu(e,t,r,n){var i=await sl(e.localStore,di(t)),s=i.targetId,a=e.sharedClientState.addLocalQueryTarget(s,r);let o;return n&&(o=await Vu(e,t,s,"current"===a,i.resumeToken)),e.isPrimaryClient&&r&&Wl(e.remoteStore,i),o}async function Vu(n,e,t,r,i){n.fu=(e,t,r)=>(async(e,t,r,n)=>{let i=t.view.tu(r);i.Cs&&(i=await ol(e.localStore,t.query,!1).then(({documents:e})=>t.view.tu(e,i)));var s=n&&n.targetChanges.get(t.targetId),a=n&&null!=n.targetMismatches.get(t.targetId),s=t.view.applyChanges(i,e.isPrimaryClient,s,a);return $u(e,t.targetId,s.ou),s.snapshot})(n,e,t,r);var s=await ol(n.localStore,e,!0),a=new Nu(e,s.$s),s=a.tu(s.documents),o=ms.createSynthesizedTargetChangeForCurrentChange(t,r&&"Offline"!==n.onlineState,i),s=a.applyChanges(s,n.isPrimaryClient,o),o=($u(n,t,s.ou),new Ru(e,t,a));return n.hu.set(e,o),n.Pu.has(t)?n.Pu.get(t).push(e):n.Pu.set(t,[e]),s.snapshot}async function Pu(t,e,r){var n=Zu(t);try{let t=await((e,i)=>{let s=e,a=h.now(),o=i.reduce((e,t)=>e.add(t.key),M()),l,u;return s.persistence.runTransaction("Locally write mutations","readwrite",n=>{let t=Ii,r=M();return s.Bs.getEntries(n,o).next(e=>{(t=e).forEach((e,t)=>{t.isValidDocument()||(r=r.add(e))})}).next(()=>s.localDocuments.getOverlayedDocuments(n,t)).next(e=>{l=e;var t=[];for(let r of i){let e=((e,r)=>{let n=null;for(var i of e.fieldTransforms){let e=r.data.field(i.field),t=Mi(i.transform,e||null);null!=t&&(n=null===n?Rn.empty():n).set(i.field,t)}return n||null})(r,l.get(r.key).overlayedDocument);null!=e&&t.push(new Xi(r.key,e,function i(e){let s=[];return Br(e.fields,(e,r)=>{var n=new v([e]);if(Sn(r)){let t=i(r.mapValue).fields;if(0===t.length)s.push(n);else for(let e of t)s.push(n.child(e))}else s.push(n)}),new Gr(s)}(e.value.mapValue),L.exists(!0)))}return s.mutationQueue.addMutationBatch(n,a,t,i)}).next(e=>{var t=(u=e).applyToLocalDocumentSet(l,r);return s.documentOverlayCache.saveOverlays(n,e.batchId,t)})}).then(()=>({batchId:u.batchId,changes:Si(l)}))})(n.localStore,e);n.sharedClientState.addPendingMutation(t.batchId);{var i=n;var s=t.batchId;var a=r;let e=i.Au[i.currentUser.toKey()];e=(e=e||new C(S)).insert(s,a),i.Au[i.currentUser.toKey()]=e}await Hu(n,t.changes),await su(n.remoteStore)}catch(t){let e=fu(t,"Failed to persist write");r.reject(e)}}async function Fu(e,t){let n=e;try{let e=await nl(n.localStore,t);t.targetChanges.forEach((e,t)=>{var r=n.Eu.get(t);r&&(y(e.addedDocuments.size+e.modifiedDocuments.size+e.removedDocuments.size<=1,22616),0<e.addedDocuments.size?r.cu=!0:0<e.modifiedDocuments.size?y(r.cu,14607):0<e.removedDocuments.size&&(y(r.cu,42227),r.cu=!1))}),await Hu(n,e,t)}catch(e){await et(e)}}function Bu(e,i,t){var s=e;if(s.isPrimaryClient&&0===t||!s.isPrimaryClient&&1===t){let n=[];s.hu.forEach((e,t)=>{var r=t.view.Fa(i);r.snapshot&&n.push(r.snapshot)});{t=s.eventManager;var a=i;var o=t;o.onlineState=a;let r=!1;o.queries.forEach((e,t)=>{for(let e of t.Sa)e.Fa(a)&&(r=!0)}),r&&Iu(o)}n.length&&s.lu.Y_(n),s.onlineState=i,s.isPrimaryClient&&s.sharedClientState.setOnlineState(i)}}async function Uu(e,t,r){var n=e;try{let e=await((e,n)=>{let i=e;return i.persistence.runTransaction("Reject batch","readwrite-primary",t=>{let r;return i.mutationQueue.lookupMutationBatch(t,n).next(e=>(y(null!==e,37113),r=e.keys(),i.mutationQueue.removeMutationBatch(t,e))).next(()=>i.mutationQueue.performConsistencyCheck(t)).next(()=>i.documentOverlayCache.removeOverlaysForBatchId(t,r,n)).next(()=>i.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(t,r)).next(()=>i.localDocuments.getDocuments(t,r))})})(n.localStore,t);zu(n,t,r),ju(n,t),n.sharedClientState.updateMutationState(t,"rejected",r),await Hu(n,e)}catch(r){await et(r)}}async function qu(t,r){let n=t;tu(n.remoteStore)||p(ku,"The network is disabled. The task returned by 'awaitPendingWrites()' will not complete until the network is enabled.");try{let e=await(e=>{let t=e;return t.persistence.runTransaction("Get highest unacknowledged batch id","readonly",e=>t.mutationQueue.getHighestUnacknowledgedBatchId(e))})(n.localStore);var i;e===pt?r.resolve():((i=n.Ru.get(e)||[]).push(r),n.Ru.set(e,i))}catch(t){let e=fu(t,"Initialization of waitForPendingWrites() operation failed");r.reject(e)}}function ju(e,t){(e.Ru.get(t)||[]).forEach(e=>{e.resolve()}),e.Ru.delete(t)}function zu(e,t,r){var n=e;let i=n.Au[n.currentUser.toKey()];if(i){let e=i.get(t);e&&(r?e.reject(r):e.resolve(),i=i.remove(t)),n.Au[n.currentUser.toKey()]=i}}function Ku(t,e,r=null){t.sharedClientState.removeLocalQueryTarget(e);for(var n of t.Pu.get(e))t.hu.delete(n),r&&t.lu.gu(n,r);t.Pu.delete(e),t.isPrimaryClient&&t.du.Hr(e).forEach(e=>{t.du.containsKey(e)||Gu(t,e)})}function Gu(e,t){e.Tu.delete(t.path.canonicalString());var r=e.Iu.get(t);null!==r&&(Yl(e.remoteStore,r),e.Iu=e.Iu.remove(t),e.Eu.delete(r),Qu(e))}function $u(e,t,r){for(var n of r)n instanceof Au?(e.du.addReference(n.key,t),i=e,s=n,o=a=void 0,a=s.key,o=a.path.canonicalString(),i.Iu.get(a)||i.Tu.has(o)||(p(ku,"New document in limbo: "+a),i.Tu.add(o),Qu(i))):n instanceof Du?(p(ku,"Document no longer in limbo: "+n.key),e.du.removeReference(n.key,t),e.du.containsKey(n.key)||Gu(e,n.key)):E(19791,{pu:n});var i,s,a,o}function Qu(e){for(;0<e.Tu.size&&e.Iu.size<e.maxConcurrentLimboResolutions;){var t=e.Tu.values().next().value,t=(e.Tu.delete(t),new x(T.fromString(t))),r=e.Vu.next();e.Eu.set(r,new Ou(t)),e.Iu=e.Iu.insert(t,r),Wl(e.remoteStore,new Zs(di(li(t.path)),r,"TargetPurposeLimboResolution",mt.le))}}async function Hu(e,t,i){let s=e,a=[],o=[],r=[];if(!s.hu.isEmpty()){s.hu.forEach((e,n)=>{r.push(s.fu(n,t,i).then(t=>{var r;if((t||i)&&s.isPrimaryClient){let e=t?!t.fromCache:null==(r=null==i?void 0:i.targetChanges.get(n.targetId))?void 0:r.current;s.sharedClientState.updateQueryState(n.targetId,e?"current":"not-current")}if(t){a.push(t);let e=Qo.Rs(n.targetId,t);o.push(e)}}))}),await Promise.all(r),s.lu.Y_(a);{var n=s.localStore,l=o;let i=n;try{await i.persistence.runTransaction("notifyLocalViewChanges","readwrite",r=>w.forEach(l,t=>w.forEach(t.ds,e=>i.persistence.referenceDelegate.addReference(r,t.targetId,e)).next(()=>w.forEach(t.As,e=>i.persistence.referenceDelegate.removeReference(r,t.targetId,e)))))}catch(n){if(!ot(n))throw n;p(Yo,"Failed to update sequence numbers: "+n)}for(let e of l){let n=e.targetId;if(!e.fromCache){let e=i.xs.get(n),t=e.snapshotVersion,r=e.withLastLimboFreeSnapshotVersion(t);i.xs=i.xs.insert(n,r)}}}}}async function Wu(e,t,r,n){var i=e,s=await((e,r)=>{let n=e,i=n.mutationQueue;return n.persistence.runTransaction("Lookup mutation documents","readonly",t=>i.tr(t,r).next(e=>e?n.localDocuments.getDocuments(t,e):w.resolve(null)))})(i.localStore,t);null!==s?("pending"===r?await su(i.remoteStore):"acknowledged"===r||"rejected"===r?(zu(i,t,n||null),ju(i,t),i.localStore.mutationQueue.sr(t)):E(6720,"Unknown batchState",{yu:r}),await Hu(i,s)):p(ku,"Cannot apply mutation batch with id: "+t)}async function Yu(r,e){var n,i,s,a=r,o=[],l=[];for(let r of e){let t,e=a.Pu.get(r);if(e&&0!==e.length){t=await sl(a.localStore,di(e[0]));for(let r of e){let e=a.hu.get(r),t=(n=e,s=i=void 0,s=await ol((i=a).localStore,n.query,!0),s=n.view.au(s),i.isPrimaryClient&&$u(i,n.targetId,s.ou),await s);t.snapshot&&l.push(t.snapshot)}}else{let e=await ll(a.localStore,r);t=await sl(a.localStore,e),await Vu(a,Xu(e),r,!1,t.resumeToken)}o.push(t)}return a.lu.Y_(l),o}function Xu(e){return oi(e.path,e.collectionGroup,e.orderBy,e.filters,e.limit,"F",e.startAt,e.endAt)}function Ju(e){var t=e;return t.remoteStore.remoteSyncer.applyRemoteEvent=Fu.bind(null,t),t.remoteStore.remoteSyncer.getRemoteKeysForTarget=(function(e,t){let n=e,r=n.Eu.get(t);if(r&&r.cu)return M().add(r.key);{let r=M(),e=n.Pu.get(t);if(e)for(let t of e){let e=n.hu.get(t);r=r.unionWith(e.view.eu)}return r}}).bind(null,t),t.remoteStore.remoteSyncer.rejectListen=(async function(e,n,t){let i=e,r=(i.sharedClientState.updateQueryState(n,"rejected",t),i.Eu.get(n)),s=r&&r.key;if(s){let e=new C(x.comparator),t=(e=e.insert(s,k.newNoDocument(s,g.min())),M().add(s)),r=new gs(g.min(),new Map,new C(S),e,t);await Fu(i,r),i.Iu=i.Iu.remove(s),i.Eu.delete(n),Qu(i)}else await al(i.localStore,n,!1).then(()=>Ku(i,n,t)).catch(et)}).bind(null,t),t.lu.Y_=(function(r,e){var n=r;let i=!1;for(let r of e){let e=r.query,t=n.queries.get(e);if(t){for(let e of t.Sa)e.Ma(r)&&(i=!0);t.ba=r}}i&&Iu(n)}).bind(null,t.eventManager),t.lu.gu=(function(e,t,r){var n=e,i=n.queries.get(t);if(i)for(let e of i.Sa)e.onError(r);n.queries.delete(t)}).bind(null,t.eventManager),t}function Zu(e){var t=e;return t.remoteStore.remoteSyncer.applySuccessfulWrite=(async function(e,t){var r=e,n=t.batch.batchId;try{let e=await tl(r.localStore,t);zu(r,n,null),ju(r,n),r.sharedClientState.updateMutationState(n,"acknowledged"),await Hu(r,e)}catch(e){await et(e)}}).bind(null,t),t.remoteStore.remoteSyncer.rejectFailedWrite=Uu.bind(null,t),t}function eh(e,t,r){let n=e;(async(e,r,n)=>{try{var i=await r.getMetadata();if(await((e,t)=>{let r=e,n=V(t.createTime);return r.persistence.runTransaction("hasNewerBundle","readonly",e=>r.Ei.getBundleMetadata(e,t.id)).then(e=>!!e&&0<=e.createTime.compareTo(n))})(e.localStore,i))return await r.close(),n._completeWith({taskState:"Success",documentsLoaded:i.totalDocuments,bytesLoaded:i.totalBytes,totalDocuments:i.totalDocuments,totalBytes:i.totalBytes}),Promise.resolve(new Set);n._updateProgress(Cu(i));var s=new xu(i,e.localStore,r.serializer);let t=await r.wu();for(;t;){let e=await s.Wa(t);e&&n._updateProgress(e),t=await r.wu()}var a=await s.complete();return await Hu(e,a.ja,void 0),await((e,t)=>{let r=e;return r.persistence.runTransaction("Save bundle","readwrite",e=>r.Ei.saveBundleMetadata(e,t))})(e.localStore,i),n._completeWith(a.progress),Promise.resolve(a.za)}catch(e){return be(ku,"Loading bundle failed with "+e),n._failWith(e),Promise.resolve(new Set)}})(n,t,r).then(e=>{n.sharedClientState.notifyBundleLoaded(e)})}class th{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=Pl(e.databaseInfo.databaseId),this.sharedClientState=this.bu(e),this.persistence=this.Su(e),await this.persistence.start(),this.localStore=this.Du(e),this.gcScheduler=this.vu(e,this.localStore),this.indexBackfillerScheduler=this.Cu(e,this.localStore)}vu(e,t){return null}Cu(e,t){return null}Du(e){return Zo(this.persistence,new Wo,e.initialUser,this.serializer)}Su(e){return new Lo(Po.fi,this.serializer)}bu(e){return new El}async terminate(){var e;null!=(e=this.gcScheduler)&&e.stop(),null!=(e=this.indexBackfillerScheduler)&&e.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}th.provider={build:()=>new th};class rh extends th{constructor(e){super(),this.cacheSizeBytes=e}vu(e,t){y(this.persistence.referenceDelegate instanceof Fo,46915);var r=this.persistence.referenceDelegate.garbageCollector;return new ho(r,e.asyncQueue,t)}Su(e){let t=void 0!==this.cacheSizeBytes?Ha.withCacheSize(this.cacheSizeBytes):Ha.DEFAULT;return new Lo(e=>Fo.fi(e,t),this.serializer)}}class nh extends th{constructor(e,t,r){super(),this.Fu=e,this.cacheSizeBytes=t,this.forceOwnership=r,this.kind="persistent",this.synchronizeTabs=!1}async initialize(e){await super.initialize(e),await this.Fu.initialize(this,e),await Zu(this.Fu.syncEngine),await su(this.Fu.remoteStore),await this.persistence.Ji(()=>(this.gcScheduler&&!this.gcScheduler.started&&this.gcScheduler.start(),this.indexBackfillerScheduler&&!this.indexBackfillerScheduler.started&&this.indexBackfillerScheduler.start(),Promise.resolve()))}Du(e){return Zo(this.persistence,new Wo,e.initialUser,this.serializer)}vu(e,t){var r=this.persistence.referenceDelegate.garbageCollector;return new ho(r,e.asyncQueue,t)}Cu(e,t){var r=new gt(t,this.persistence);return new ft(e.asyncQueue,r)}Su(e){var t=$o(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),r=void 0!==this.cacheSizeBytes?Ha.withCacheSize(this.cacheSizeBytes):Ha.DEFAULT;return new zo(this.synchronizeTabs,t,e.clientId,r,e.asyncQueue,Ll(),Vl(),this.serializer,this.sharedClientState,!!this.forceOwnership)}bu(e){return new El}}class ih extends nh{constructor(e,t){super(e,t,!1),this.Fu=e,this.cacheSizeBytes=t,this.synchronizeTabs=!0}async initialize(e){await super.initialize(e);var t=this.Fu.syncEngine;this.sharedClientState instanceof Tl&&(this.sharedClientState.syncEngine={Co:Wu.bind(null,t),Fo:(async function(e,r,n,t){var i=e;if(i.mu)p(ku,"Ignoring unexpected query state notification.");else{var s=i.Pu.get(r);if(s&&0<s.length)switch(n){case"current":case"not-current":{let e=await ul(i.localStore,wi(s[0])),t=gs.createSynthesizedRemoteEventForCurrentChange(r,"current"===n,D.EMPTY_BYTE_STRING);await Hu(i,e,t);break}case"rejected":await al(i.localStore,r,!0),Ku(i,r,t);break;default:E(64155,n)}}}).bind(null,t),Mo:(async function(e,t,n){let i=Ju(e);if(i.mu){for(let r of t)if(i.Pu.has(r)&&i.sharedClientState.isActiveQueryTarget(r))p(ku,"Adding an already active target "+r);else{let e=await ll(i.localStore,r),t=await sl(i.localStore,e);await Vu(i,Xu(e),t.targetId,!1,t.resumeToken),Wl(i.remoteStore,t)}for(let r of n)i.Pu.has(r)&&await al(i.localStore,r,!1).then(()=>{Yl(i.remoteStore,r),Ku(i,r)}).catch(et)}}).bind(null,t),Is:(function(e){return e.localStore.persistence.Is()}).bind(null,t),vo:(async function(e,t){let r=e;return ul(r.localStore,t).then(e=>Hu(r,e))}).bind(null,t)},await this.sharedClientState.start()),await this.persistence.Ji(async e=>{{var t=this.Fu.syncEngine,s=e;let i=t;if(Ju(i),Zu(i),!0===s&&!0!==i.mu){let e=i.sharedClientState.getAllActiveQueryTargets(),t=await Yu(i,e.toArray());i.mu=!0,await uu(i.remoteStore,!0);for(let e of t)Wl(i.remoteStore,e)}else if(!1===s&&!1!==i.mu){let r=[],n=Promise.resolve();i.Pu.forEach((e,t)=>{i.sharedClientState.isLocalQueryTarget(t)?r.push(t):n=n.then(()=>(Ku(i,t),al(i.localStore,t,!0))),Yl(i.remoteStore,t)}),await n,await Yu(i,r);{s=i;let r=s;r.Eu.forEach((e,t)=>{Yl(r.remoteStore,t)}),r.du.Jr(),r.Eu=new Map,r.Iu=new C(x.comparator)}i.mu=!1,await uu(i.remoteStore,!1)}}await 0,this.gcScheduler&&(e&&!this.gcScheduler.started?this.gcScheduler.start():e||this.gcScheduler.stop()),this.indexBackfillerScheduler&&(e&&!this.indexBackfillerScheduler.started?this.indexBackfillerScheduler.start():e||this.indexBackfillerScheduler.stop())})}bu(e){var t,r=Ll();if(Tl.C(r))return t=$o(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),new Tl(r,e.asyncQueue,t,e.clientId,e.initialUser);throw new I(b.UNIMPLEMENTED,"IndexedDB persistence is only available on platforms that support LocalStorage.")}}class sh{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>Bu(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=(async function(e,t){var r,n,i=e;if(!i.currentUser.isEqual(t)){p(ku,"User change. New user:",t.toKey());let e=await el(i.localStore,t);i.currentUser=t,n="'waitForPendingWrites' promise is rejected due to a user change.",(r=i).Ru.forEach(e=>{e.forEach(e=>{e.reject(new I(b.CANCELLED,n))})}),r.Ru.clear(),i.sharedClientState.handleUserChange(t,e.removedBatchIds,e.addedBatchIds),await Hu(i,e.ks)}}).bind(null,this.syncEngine),await uu(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new vu}createDatastore(e){var t,r,n,i=Pl(e.databaseInfo.databaseId),s=(t=e.databaseInfo,new Ml(t));return t=e.authCredentials,e=e.appCheckCredentials,r=s,n=i,new zl(t,e,r,n)}createRemoteStore(e){return t=this.localStore,r=this.datastore,e=e.asyncQueue,n=e=>Bu(this.syncEngine,e,0),i=new(Cl.C()?Cl:Sl),new $l(t,r,e,n,i);var t,r,n,i}createSyncEngine(e,t){return r=this.localStore,n=this.remoteStore,i=this.eventManager,s=this.sharedClientState,a=e.initialUser,e=e.maxConcurrentLimboResolutions,t=t,o=new Mu(r,n,i,s,a,e),t&&(o.mu=!0),o;var r,n,i,s,a,o}async terminate(){var e,t;e=this.remoteStore,t=e,p(Gl,"RemoteStore shutting down."),t.da.add(5),await Hl(t),t.Ra.shutdown(),await!t.Va.set("Unknown"),null!=(t=this.datastore)&&t.terminate(),null!=(t=this.eventManager)&&t.terminate()}}function ah(t,r=10240){let n=0;return{async read(){var e;return n<t.byteLength?(e={value:t.slice(n,n+r),done:!1},n+=r,e):{done:!0}},async cancel(){},releaseLock(){},closed:Promise.resolve()}}sh.provider={build:()=>new sh};class oh{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.Mu(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.Mu(this.observer.error,e):d("Uncaught Error in snapshot listener:",e.toString()))}xu(){this.muted=!0}Mu(e,t){setTimeout(()=>{this.muted||e(t)},0)}}class lh{constructor(e,t){this.Ou=e,this.serializer=t,this.metadata=new f,this.buffer=new Uint8Array,this.Nu=new TextDecoder("utf-8"),this.Bu().then(e=>{e&&e.Ua()?this.metadata.resolve(e.$a.metadata):this.metadata.reject(new Error(`The first element of the bundle is not a metadata, it is
             `+JSON.stringify(null==e?void 0:e.$a)))},e=>this.metadata.reject(e))}close(){return this.Ou.cancel()}async getMetadata(){return this.metadata.promise}async wu(){return await this.getMetadata(),this.Bu()}async Bu(){var e,t,r=await this.Lu();return null===r?null:(t=this.Nu.decode(r),e=Number(t),isNaN(e)&&this.ku(`length string (${t}) is not valid number`),t=await this.qu(e),new Eu(JSON.parse(t),r.length+e))}Qu(){return this.buffer.findIndex(e=>e==="{".charCodeAt(0))}async Lu(){for(;this.Qu()<0&&!await this.$u(););var e,t;return 0===this.buffer.length?null:((e=this.Qu())<0&&this.ku("Reached the end of bundle when a length string is expected."),t=this.buffer.slice(0,e),this.buffer=this.buffer.slice(e),t)}async qu(e){for(;this.buffer.length<e;)await this.$u()&&this.ku("Reached the end of bundle when more is expected.");var t=this.Nu.decode(this.buffer.slice(0,e));return this.buffer=this.buffer.slice(e),t}ku(e){throw this.Ou.cancel(),new Error("Invalid bundle format: "+e)}async $u(){var e,t=await this.Ou.read();return t.done||((e=new Uint8Array(this.buffer.length+t.value.length)).set(this.buffer),e.set(t.value,this.buffer.length),this.buffer=e),t.done}}class uh{constructor(e){this.datastore=e,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastTransactionError=null,this.writtenDocs=new Set}async lookup(e){if(this.ensureCommitNotCalled(),0<this.mutations.length)throw this.lastTransactionError=new I(b.INVALID_ARGUMENT,"Firestore transactions require all reads to be executed before all writes."),this.lastTransactionError;var t=await(async(e,t)=>{let l=e,r={documents:t.map(e=>Os(l.serializer,e))},n=await l.Yo("BatchGetDocuments",l.serializer.databaseId,T.emptyPath(),r,t.length),u=new Map,i=(n.forEach(e=>{t=l.serializer;var t,r,n,i,s,a,o="found"in(e=e)?(r=t,y(!!(n=e).found,43571),n.found.name,n.found.updateTime,i=Ms(r,n.found.name),s=V(n.found.updateTime),o=n.found.createTime?V(n.found.createTime):g.min(),a=new Rn({mapValue:{fields:n.found.fields}}),k.newFoundDocument(i,s,o,a)):"missing"in e?(r=t,y(!!(n=e).missing,3894),y(!!n.readTime,22933),i=Ms(r,n.missing),s=V(n.readTime),k.newNoDocument(i,s)):E(7234,{result:e});u.set(o.key.toString(),o)}),[]);return t.forEach(e=>{var t=u.get(e.toString());y(!!t,55234,{key:e}),i.push(t)}),i})(this.datastore,e);return t.forEach(e=>this.recordVersion(e)),t}set(e,t){this.write(t.toMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}update(e,t){try{this.write(t.toMutation(e,this.preconditionForUpdate(e)))}catch(e){this.lastTransactionError=e}this.writtenDocs.add(e.toString())}delete(e){this.write(new ts(e,this.precondition(e))),this.writtenDocs.add(e.toString())}async commit(){if(this.ensureCommitNotCalled(),this.lastTransactionError)throw this.lastTransactionError;let t=this.readVersions;this.mutations.forEach(e=>{t.delete(e.key.toString())}),t.forEach((e,t)=>{var r=x.fromPath(t);this.mutations.push(new rs(r,this.precondition(r)))});{var r=this.datastore,n=this.mutations;let t=r,e={writes:n.map(e=>qs(t.serializer,e))};await t.zo("Commit",t.serializer.databaseId,T.emptyPath(),e)}await 0,this.committed=!0}recordVersion(e){let t;if(e.isFoundDocument())t=e.version;else{if(!e.isNoDocument())throw E(50498,{Uu:e.constructor.name});t=g.min()}var r=this.readVersions.get(e.key.toString());if(r){if(!t.isEqual(r))throw new I(b.ABORTED,"Document version changed between two reads.")}else this.readVersions.set(e.key.toString(),t)}precondition(e){var t=this.readVersions.get(e.toString());return!this.writtenDocs.has(e.toString())&&t?t.isEqual(g.min())?L.exists(!1):L.updateTime(t):L.none()}preconditionForUpdate(e){var t=this.readVersions.get(e.toString());if(this.writtenDocs.has(e.toString())||!t)return L.exists(!0);if(t.isEqual(g.min()))throw new I(b.INVALID_ARGUMENT,"Can't update a document that doesn't exist.");return L.updateTime(t)}write(e){this.ensureCommitNotCalled(),this.mutations.push(e)}ensureCommitNotCalled(){}}class hh{constructor(e,t,r,n,i){this.asyncQueue=e,this.datastore=t,this.options=r,this.updateFunction=n,this.deferred=i,this.Ku=r.maxAttempts,this.x_=new Fl(this.asyncQueue,"transaction_retry")}Wu(){--this.Ku,this.Gu()}Gu(){this.x_.y_(async()=>{let t=new uh(this.datastore),e=this.zu(t);e&&e.then(e=>{this.asyncQueue.enqueueAndForget(()=>t.commit().then(()=>{this.deferred.resolve(e)}).catch(e=>{this.ju(e)}))}).catch(e=>{this.ju(e)})})}zu(e){try{var t=this.updateFunction(e);return!yt(t)&&t.catch&&t.then?t:(this.deferred.reject(Error("Transaction callback must return a Promise")),null)}catch(e){return this.deferred.reject(e),null}}ju(e){0<this.Ku&&this.Hu(e)?(--this.Ku,this.asyncQueue.enqueueAndForget(()=>(this.Gu(),Promise.resolve()))):this.deferred.reject(e)}Hu(e){var t;return"FirebaseError"===e.name&&("aborted"===(t=e.code)||"failed-precondition"===t||"already-exists"===t||!os(t))}}let ch="FirestoreClient";class dh{constructor(e,t,r,n,i){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=r,this.databaseInfo=n,this.user=u.UNAUTHENTICATED,this.clientId=Oe.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=i,this.authCredentials.start(r,async e=>{p(ch,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(r,e=>(p(ch,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let r=new f;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),r.resolve()}catch(e){var t=fu(e,"Failed to shutdown persistence");r.reject(t)}}),r.promise}}async function fh(e,t){e.asyncQueue.verifyOperationInProgress(),p(ch,"Initializing OfflineComponentProvider");var r=e.configuration;await t.initialize(r);let n=r.initialUser;e.setCredentialChangeListener(async e=>{n.isEqual(e)||(await el(t.localStore,e),n=e)}),t.persistence.setDatabaseDeletedListener(()=>e.terminate()),e._offlineComponents=t}async function gh(e,r){e.asyncQueue.verifyOperationInProgress();var t=await mh(e);p(ch,"Initializing OnlineComponentProvider"),await r.initialize(t,e.configuration),e.setCredentialChangeListener(e=>lu(r.remoteStore,e)),e.setAppCheckTokenChangeListener((e,t)=>lu(r.remoteStore,t)),e._onlineComponents=r}async function mh(t){if(!t._offlineComponents)if(t._uninitializedComponentsProvider){p(ch,"Using user provided OfflineComponentProvider");try{await fh(t,t._uninitializedComponentsProvider._offline)}catch(e){var r=e;if(!("FirebaseError"===(n=r).name?n.code===b.FAILED_PRECONDITION||n.code===b.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&n instanceof DOMException)||22===n.code||20===n.code||11===n.code))throw r;be("Error using user provided cache. Falling back to memory cache: "+r),await fh(t,new th)}}else p(ch,"Using default OfflineComponentProvider"),await fh(t,new rh(void 0));var n;return t._offlineComponents}async function ph(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(p(ch,"Using user provided OnlineComponentProvider"),await gh(e,e._uninitializedComponentsProvider._online)):(p(ch,"Using default OnlineComponentProvider"),await gh(e,new sh))),e._onlineComponents}function yh(e){return mh(e).then(e=>e.persistence)}function vh(e){return mh(e).then(e=>e.localStore)}function wh(e){return ph(e).then(e=>e.remoteStore)}function _h(e){return ph(e).then(e=>e.syncEngine)}async function bh(e){var t=await ph(e),r=t.eventManager;return r.onListen=(async function(e,t,r=!0){var n=Ju(e);let i;var s=n.hu.get(t);return i=s?(n.sharedClientState.addLocalQueryTarget(s.targetId),s.view.uu()):await Lu(n,t,r,!0)}).bind(null,t.syncEngine),r.onUnlisten=(async function(e,t,r){let n=e,i=n.hu.get(t),s=n.Pu.get(i.targetId);1<s.length?(n.Pu.set(i.targetId,s.filter(e=>!mi(e,t))),n.hu.delete(t)):n.isPrimaryClient?(n.sharedClientState.removeLocalQueryTarget(i.targetId),n.sharedClientState.isActiveQueryTarget(i.targetId)||await al(n.localStore,i.targetId,!1).then(()=>{n.sharedClientState.clearQueryState(i.targetId),r&&Yl(n.remoteStore,i.targetId),Ku(n,i.targetId)}).catch(et)):(Ku(n,i.targetId),await al(n.localStore,i.targetId,!0))}).bind(null,t.syncEngine),r.onFirstRemoteStoreListen=(async function(e,t){await Lu(Ju(e),t,!0,!1)}).bind(null,t.syncEngine),r.onLastRemoteStoreUnlisten=(async function(e,t){var r=e,n=r.hu.get(t),i=r.Pu.get(n.targetId);r.isPrimaryClient&&1===i.length&&(r.sharedClientState.removeLocalQueryTarget(n.targetId),Yl(r.remoteStore,n.targetId))}).bind(null,t.syncEngine),r}function Ih(r){return r.asyncQueue.enqueue(async()=>{var e=await yh(r),t=await wh(r);return e.setNetworkEnabled(!1),(async e=>{var t=e;t.da.add(0),await Hl(t),t.Va.set("Offline")})(t)})}function Th(e,t){let r=new f;return e.asyncQueue.enqueueAndForget(async()=>(async(e,t,r)=>{try{var n=await((e,t)=>{let r=e;return r.persistence.runTransaction("read document","readonly",e=>r.localDocuments.getDocument(e,t))})(e,t);n.isFoundDocument()?r.resolve(n):n.isNoDocument()?r.resolve(null):r.reject(new I(b.UNAVAILABLE,"Failed to get document from cache. (However, this document may exist on the server. Run again without setting 'source' in the GetOptions to attempt to retrieve the document from the server.)"))}catch(e){n=fu(e,`Failed to get document '${t} from cache`);r.reject(n)}})(await vh(e),t,r)),r.promise}function Eh(e,t,u={}){let h=new f;return e.asyncQueue.enqueueAndForget(async()=>{{var i=await bh(e),s=e.asyncQueue,a=t,o=u,l=h;let r=new oh({next:e=>{r.xu(),s.enqueueAndForget(()=>bu(i,n));var t=e.docs.has(a);!t&&e.fromCache?l.reject(new I(b.UNAVAILABLE,"Failed to get document because the client is offline.")):t&&e.fromCache&&o&&"server"===o.source?l.reject(new I(b.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):l.resolve(e)},error:e=>l.reject(e)}),n=new Tu(li(a.path),r,{includeMetadataChanges:!0,Qa:!0});return _u(i,n)}}),h.promise}function Sh(e,t){let r=new f;return e.asyncQueue.enqueueAndForget(async()=>(async(e,t,r)=>{try{var n=await ol(e,t,!0),i=new Nu(t,n.$s),s=i.tu(n.documents),a=i.applyChanges(s,!1);r.resolve(a.snapshot)}catch(e){n=fu(e,`Failed to execute query '${t} against cache`);r.reject(n)}})(await vh(e),t,r)),r.promise}function xh(o,l,u={}){let h=new f;return o.asyncQueue.enqueueAndForget(async()=>{{var n=await bh(o),i=o.asyncQueue,e=l,s=u,a=h;let t=new oh({next:e=>{t.xu(),i.enqueueAndForget(()=>bu(n,r)),e.fromCache&&"server"===s.source?a.reject(new I(b.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):a.resolve(e)},error:e=>a.reject(e)}),r=new Tu(e,t,{includeMetadataChanges:!0,Qa:!0});return _u(n,r)}}),h.promise}function Ch(r,e){let n=new oh(e);return r.asyncQueue.enqueueAndForget(async()=>{return e=await bh(r),t=n,e.Ca.add(t),void t.next();var e,t}),()=>{n.xu(),r.asyncQueue.enqueueAndForget(async()=>{var e,t;e=await bh(r),t=n,e.Ca.delete(t)})}}function Ah(e,t,r,n){r=r,t=Pl(t),s="string"==typeof r?Re().encode(r):r,r=((e,t)=>{if(e instanceof Uint8Array)return ah(e,t);if(e instanceof ArrayBuffer)return ah(new Uint8Array(e),t);if(e instanceof ReadableStream)return e.getReader();throw new Error("Source of `toByteStreamReader` has to be a ArrayBuffer or ReadableStream")})(s),t=t;let i=new lh(r,t);var s;e.asyncQueue.enqueueAndForget(async()=>{eh(await _h(e),i,n)})}function Dh(n,i){return n.asyncQueue.enqueue(async()=>{{var e=await vh(n),r=i;let t=e;return t.persistence.runTransaction("Get named query","readonly",e=>t.Ei.getNamedQuery(e,r))}})}function Nh(e){var t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let kh=new Map;function Rh(e,t,r){if(!r)throw new I(b.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function Oh(e,t,r,n){if(!0===t&&!0===n)throw new I(b.INVALID_ARGUMENT,e+` and ${r} cannot be used together.`)}function Mh(e){if(!x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function Lh(e){if(x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function Vh(e){var t,r;return void 0===e?"undefined":null===e?"null":"string"==typeof e?(20<e.length&&(e=e.substring(0,20)+"..."),JSON.stringify(e)):"number"==typeof e||"boolean"==typeof e?""+e:"object"==typeof e?e instanceof Array?"an array":(t=(r=e).constructor?r.constructor.name:null)?`a custom ${t} object`:"an object":"function"==typeof e?"a function":E(12329,{type:typeof e})}function P(e,t){if((e="_delegate"in e?e._delegate:e)instanceof t)return e;if(t.name===e.constructor.name)throw new I(b.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");var r=Vh(e);throw new I(b.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: `+r)}function Ph(e,t){if(t<=0)throw new I(b.INVALID_ARGUMENT,`Function ${e}() requires a positive number, but it was: ${t}.`)}let Fh="firestore.googleapis.com";class Bh{constructor(e){var t;if(void 0===e.host){if(void 0!==e.ssl)throw new I(b.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=Fh,this.ssl=!0}else this.host=e.host,this.ssl=null==(t=e.ssl)||t;if(this.isUsingEmulator=void 0!==e.emulatorOptions,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=41943040;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new I(b.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}Oh("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=Nh(null!=(t=e.experimentalLongPollingOptions)?t:{});var r=this.experimentalLongPollingOptions;if(void 0!==r.timeoutSeconds){if(isNaN(r.timeoutSeconds))throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (must not be NaN)`);if(r.timeoutSeconds<5)throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (minimum allowed value is 5)`);if(30<r.timeoutSeconds)throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (maximum allowed value is 30)`)}this.useFetchStreams=!!e.useFetchStreams}isEqual(e){return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,r=e.experimentalLongPollingOptions,t.timeoutSeconds===r.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams;var t,r}}class Uh{constructor(e,t,r,n){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=r,this._app=n,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new Bh({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(this._app)return this._app;throw new I(b.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available")}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new I(b.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new Bh(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=(e=>{if(!e)return new Se;switch(e.type){case"firstParty":return new De(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new I(b.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}})(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return e=this,(t=kh.get(e))&&(p("ComponentProvider","Removing Datastore"),kh.delete(e),t.terminate()),Promise.resolve();var e,t}}function qh(n,e,t,i={}){n=P(n,Uh);let r=Y(e),s=n._getSettings(),a=Object.assign(Object.assign({},s),{emulatorOptions:n._getEmulatorOptions()}),o=e+":"+t;r&&((async e=>(await fetch(e,{credentials:"include"})).ok)("https://"+o),Z("Firestore",!0)),s.host!==Fh&&s.host!==o&&be("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");var l=Object.assign(Object.assign({},s),{host:o,ssl:r,emulatorOptions:i});if(!oe(l,a)&&(n._setSettings(l),i.mockUserToken)){let t,r;if("string"==typeof i.mockUserToken)t=i.mockUserToken,r=u.MOCK_USER;else{t=((e,t)=>{if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');var r=t||"demo-project",n=e.iat||0,i=e.sub||e.user_id;if(i)return r=Object.assign({iss:"https://securetoken.google.com/"+r,aud:r,iat:n,exp:n+3600,auth_time:n,sub:i,user_id:i,firebase:{sign_in_provider:"custom",identities:{}}},e),[z(JSON.stringify({alg:"none",type:"JWT"})),z(JSON.stringify(r)),""].join(".");throw new Error("mockUserToken must contain 'sub' or 'user_id' field!")})(i.mockUserToken,null==(l=n._app)?void 0:l.options.projectId);let e=i.mockUserToken.sub||i.mockUserToken.user_id;if(!e)throw new I(b.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");r=new u(e)}n._authCredentials=new xe(new Ee(t,r))}}class jh{constructor(e,t,r){this.converter=t,this._query=r,this.type="query",this.firestore=e}withConverter(e){return new jh(this.firestore,e,this._query)}}class F{constructor(e,t,r){this.converter=t,this._key=r,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new zh(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new F(this.firestore,e,this._key)}}class zh extends jh{constructor(e,t,r){super(e,t,li(r)),this._path=r,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){var e=this._path.popLast();return e.isEmpty()?null:new F(this.firestore,null,new x(e))}withConverter(e){return new zh(this.firestore,e,this._path)}}function Kh(e,t,...r){var n;if(e=_(e),Rh("collection","path",t),e instanceof Uh)return Lh(n=T.fromString(t,...r)),new zh(e,null,n);if(e instanceof F||e instanceof zh)return Lh(n=e._path.child(T.fromString(t,...r))),new zh(e.firestore,null,n);throw new I(b.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore")}function Gh(e,t,...r){var n;if(e=_(e),Rh("doc","path",t=1===arguments.length?Oe.newId():t),e instanceof Uh)return Mh(n=T.fromString(t,...r)),new F(e,null,new x(n));if(e instanceof F||e instanceof zh)return Mh(n=e._path.child(T.fromString(t,...r))),new F(e.firestore,e instanceof zh?e.converter:null,new x(n));throw new I(b.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore")}function $h(e,t){return e=_(e),t=_(t),(e instanceof F||e instanceof zh)&&(t instanceof F||t instanceof zh)&&e.firestore===t.firestore&&e.path===t.path&&e.converter===t.converter}function Qh(e,t){return e=_(e),t=_(t),e instanceof jh&&t instanceof jh&&e.firestore===t.firestore&&mi(e._query,t._query)&&e.converter===t.converter}let Hh="AsyncQueue";class Wh{constructor(e=Promise.resolve()){this.Ju=[],this.Yu=!1,this.Zu=[],this.Xu=null,this.ec=!1,this.tc=!1,this.nc=[],this.x_=new Fl(this,"async_queue_retry"),this.rc=()=>{var e=Vl();e&&p(Hh,"Visibility state changed to "+e.visibilityState),this.x_.b_()},this.sc=e;var t=Vl();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.rc)}get isShuttingDown(){return this.Yu}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.oc(),this._c(e)}enterRestrictedMode(e){var t;this.Yu||(this.Yu=!0,this.tc=e||!1,(t=Vl())&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.rc))}enqueue(e){if(this.oc(),this.Yu)return new Promise(()=>{});let t=new f;return this._c(()=>this.Yu&&this.tc?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Ju.push(e),this.ac()))}async ac(){if(0!==this.Ju.length){try{await this.Ju[0](),this.Ju.shift(),this.x_.reset()}catch(e){if(!ot(e))throw e;p(Hh,"Operation failed with retryable error: "+e)}0<this.Ju.length&&this.x_.y_(()=>this.ac())}}_c(e){var t=this.sc.then(()=>(this.ec=!0,e().catch(e=>{throw this.Xu=e,this.ec=!1,d("INTERNAL UNHANDLED ERROR: ",Yh(e)),e}).then(e=>(this.ec=!1,e))));return this.sc=t}enqueueAfterDelay(e,t,r){this.oc(),-1<this.nc.indexOf(e)&&(t=0);var n=du.createAndSchedule(this,e,t,r,e=>this.uc(e));return this.Zu.push(n),n}oc(){this.Xu&&E(47125,{cc:Yh(this.Xu)})}verifyOperationInProgress(){}async lc(){for(var e;await(e=this.sc),e!==this.sc;);}hc(e){for(var t of this.Zu)if(t.timerId===e)return!0;return!1}Pc(t){return this.lc().then(()=>{this.Zu.sort((e,t)=>e.targetTimeMs-t.targetTimeMs);for(var e of this.Zu)if(e.skipDelay(),"all"!==t&&e.timerId===t)break;return this.lc()})}Tc(e){this.nc.push(e)}uc(e){var t=this.Zu.indexOf(e);this.Zu.splice(t,1)}}function Yh(e){let t=e.message||"";return t=e.stack?e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack:t}function Xh(t){var r=t,t=["next","error","complete"];if("object"==typeof r&&null!==r){var n=r;for(let e of t)if(e in n&&"function"==typeof n[e])return 1}}class Jh{constructor(){this._progressObserver={},this._taskCompletionResolver=new f,this._lastProgress={taskState:"Running",totalBytes:0,totalDocuments:0,bytesLoaded:0,documentsLoaded:0}}onProgress(e,t,r){this._progressObserver={next:e,error:t,complete:r}}catch(e){return this._taskCompletionResolver.promise.catch(e)}then(e,t){return this._taskCompletionResolver.promise.then(e,t)}_completeWith(e){this._updateProgress(e),this._progressObserver.complete&&this._progressObserver.complete(),this._taskCompletionResolver.resolve(e)}_failWith(e){this._lastProgress.taskState="Error",this._progressObserver.next&&this._progressObserver.next(this._lastProgress),this._progressObserver.error&&this._progressObserver.error(e),this._taskCompletionResolver.reject(e)}_updateProgress(e){this._lastProgress=e,this._progressObserver.next&&this._progressObserver.next(e)}}var Zh,ec,t,tc;class B extends Uh{constructor(e,t,r,n){super(e,t,r,n),this.type="firestore",this._queue=new Wh,this._persistenceKey=(null==n?void 0:n.name)||"[DEFAULT]"}async _terminate(){var e;this._firestoreClient&&(e=this._firestoreClient.terminate(),this._queue=new Wh(e),this._firestoreClient=void 0,await e)}}function rc(e){if(e._terminated)throw new I(b.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||nc(e),e._firestoreClient}function nc(e){var t,r,n,i,s,a=e._freezeSettings(),o=(i=e._databaseId,t=(null==(o=e._app)?void 0:o.options.appId)||"",r=e._persistenceKey,n=a,new nn(i,t,r,n.host,n.ssl,n.experimentalForceLongPolling,n.experimentalAutoDetectLongPolling,Nh(n.experimentalLongPollingOptions),n.useFetchStreams,n.isUsingEmulator));e._componentsProvider||null!=(s=a.localCache)&&s._offlineComponentProvider&&null!=(s=a.localCache)&&s._onlineComponentProvider&&(e._componentsProvider={_offline:a.localCache._offlineComponentProvider,_online:a.localCache._onlineComponentProvider}),e._firestoreClient=new dh(e._authCredentials,e._appCheckCredentials,e._queue,o,e._componentsProvider&&(i=e._componentsProvider,s=null==i?void 0:i._online.build(),{_offline:null==i?void 0:i._offline.build(s),_online:s}))}function ic(e,t,r){if((e=P(e,B))._firestoreClient||e._terminated)throw new I(b.FAILED_PRECONDITION,"Firestore has already been started and persistence can no longer be enabled. You can only enable persistence before calling any other methods on a Firestore object.");if(e._componentsProvider||e._getSettings().localCache)throw new I(b.FAILED_PRECONDITION,"SDK cache is already specified.");e._componentsProvider={_online:t,_offline:r},nc(e)}function sc(r){if(r._initialized&&!r._terminated)throw new I(b.FAILED_PRECONDITION,"Persistence can only be cleared before a Firestore instance is initialized or after it is terminated.");let n=new f;return r._queue.enqueueAndForgetEvenWhileRestricted(async()=>{try{e=$o(r._databaseId,r._persistenceKey),await(nt.C()?(t=e+"main",void await nt.delete(t)):Promise.resolve()),n.resolve()}catch(e){n.reject(e)}var e,t}),n.promise}function ac(e){return(r=rc(e=P(e,B))).asyncQueue.enqueue(async()=>{var e=await yh(r),t=await wh(r);return e.setNetworkEnabled(!0),(e=t).da.delete(0),Ql(e)});var r}class oc{constructor(e){this._byteString=e}static fromBase64String(e){try{return new oc(D.fromBase64String(e))}catch(e){throw new I(b.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new oc(D.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}}class lc{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new I(b.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new v(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class uc{constructor(e){this._methodName=e}}class hc{constructor(e,t){if(!isFinite(e)||e<-90||90<e)throw new I(b.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||180<t)throw new I(b.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(e){return S(this._lat,e._lat)||S(this._long,e._long)}}class cc{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){var t=this._values,r=e._values;if(t.length!==r.length)return!1;for(let n=0;n<t.length;++n)if(t[n]!==r[n])return!1;return!0}}let dc=/^__.*__$/;class fc{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return null!==this.fieldMask?new Xi(e,this.data,this.fieldMask,t,this.fieldTransforms):new Yi(e,this.data,t,this.fieldTransforms)}}class gc{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return new Xi(e,this.data,this.fieldMask,t,this.fieldTransforms)}}function mc(e){switch(e){case 0:case 2:case 1:return 1;case 3:case 4:return;default:throw E(40011,{Ic:e})}}class pc{constructor(e,t,r,n,i,s){this.settings=e,this.databaseId=t,this.serializer=r,this.ignoreUndefinedProperties=n,void 0===i&&this.Ec(),this.fieldTransforms=i||[],this.fieldMask=s||[]}get path(){return this.settings.path}get Ic(){return this.settings.Ic}dc(e){return new pc(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}Ac(e){var t=null==(t=this.path)?void 0:t.child(e),t=this.dc({path:t,Rc:!1});return t.Vc(e),t}mc(e){var t=null==(t=this.path)?void 0:t.child(e),t=this.dc({path:t,Rc:!1});return t.Ec(),t}fc(e){return this.dc({path:void 0,Rc:!0})}gc(e){return Vc(e,this.settings.methodName,this.settings.yc||!1,this.path,this.settings.wc)}contains(t){return void 0!==this.fieldMask.find(e=>t.isPrefixOf(e))||void 0!==this.fieldTransforms.find(e=>t.isPrefixOf(e.field))}Ec(){if(this.path)for(let e=0;e<this.path.length;e++)this.Vc(this.path.get(e))}Vc(e){if(0===e.length)throw this.gc("Document fields must not be empty");if(mc(this.Ic)&&dc.test(e))throw this.gc('Document fields cannot begin and end with "__"')}}class yc{constructor(e,t,r){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=r||Pl(e)}bc(e,t,r,n=!1){return new pc({Ic:e,methodName:t,wc:r,path:v.emptyPath(),Rc:!1,yc:n},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function vc(e){var t=e._freezeSettings(),r=Pl(e._databaseId);return new yc(e._databaseId,!!t.ignoreUndefinedProperties,r)}function wc(e,n,i,t,r,s={}){var a=e.bc(s.merge||s.mergeFields?2:0,n,i,r),o=(Rc("Data must be an object, but it was:",a,t),Nc(t,a));let l,u;if(s.merge)l=new Gr(a.fieldMask),u=a.fieldTransforms;else if(s.mergeFields){let t=[];for(let r of s.mergeFields){let e=Oc(n,r,i);if(!a.contains(e))throw new I(b.INVALID_ARGUMENT,`Field '${e}' is specified in your field mask but missing from your input data.`);Pc(t,e)||t.push(e)}l=new Gr(t),u=a.fieldTransforms.filter(e=>l.covers(e.field))}else l=null,u=a.fieldTransforms;return new fc(new Rn(o),l,u)}class _c extends uc{_toFieldTransform(e){if(2!==e.Ic)throw 1===e.Ic?e.gc(this._methodName+"() can only appear at the top level of your update data"):e.gc(this._methodName+"() cannot be used with set() unless you pass {merge:true}");return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof _c}}function bc(e,t,r){return new pc({Ic:3,wc:t.settings.wc,methodName:e._methodName,Rc:r},t.databaseId,t.serializer,t.ignoreUndefinedProperties)}class Ic extends uc{_toFieldTransform(e){return new zi(e.path,new Li)}isEqual(e){return e instanceof Ic}}class Tc extends uc{constructor(e,t){super(e),this.Sc=t}_toFieldTransform(e){let t=bc(this,e,!0),r=this.Sc.map(e=>Dc(e,t)),n=new Vi(r);return new zi(e.path,n)}isEqual(e){return e instanceof Tc&&oe(this.Sc,e.Sc)}}class Ec extends uc{constructor(e,t){super(e),this.Sc=t}_toFieldTransform(e){let t=bc(this,e,!0),r=this.Sc.map(e=>Dc(e,t)),n=new Fi(r);return new zi(e.path,n)}isEqual(e){return e instanceof Ec&&oe(this.Sc,e.Sc)}}class Sc extends uc{constructor(e,t){super(e),this.Dc=t}_toFieldTransform(e){var t=new Ui(e.serializer,Ri(e.serializer,this.Dc));return new zi(e.path,t)}isEqual(e){return e instanceof Sc&&this.Dc===e.Dc}}function xc(e,i,s,t){let a=e.bc(1,i,s),o=(Rc("Data must be an object, but it was:",a,t),[]),l=Rn.empty();Br(t,(e,t)=>{var r=Lc(i,e,s),n=(t=_(t),a.mc(r));if(t instanceof _c)o.push(r);else{let e=Dc(t,n);null!=e&&(o.push(r),l.set(r,e))}});var r=new Gr(o);return new gc(l,r,a.fieldTransforms)}function Cc(e,t,r,n,i,s){var a=e.bc(1,t,r),o=[Oc(t,n,r)],l=[i];if(s.length%2!=0)throw new I(b.INVALID_ARGUMENT,`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let f=0;f<s.length;f+=2)o.push(Oc(t,s[f])),l.push(s[f+1]);var u=[],h=Rn.empty();for(let g=o.length-1;0<=g;--g)if(!Pc(u,o[g])){let t=o[g];var c=_(l[g]);let r=a.mc(t);if(c instanceof _c)u.push(t);else{let e=Dc(c,r);null!=e&&(u.push(t),h.set(t,e))}}var d=new Gr(u);return new gc(h,d,a.fieldTransforms)}function Ac(e,t,r,n=!1){return Dc(r,e.bc(n?4:3,t))}function Dc(e,r){if(kc(e=_(e)))return Rc("Unsupported field value:",r,e),Nc(e,r);if(e instanceof uc){{var t=e;var n=r;if(!mc(n.Ic))throw n.gc(t._methodName+"() can only be used with update() and set()");if(!n.path)throw n.gc(t._methodName+"() is not currently supported inside arrays");var i=t._toFieldTransform(n);i&&n.fieldTransforms.push(i)}return null}if(void 0===e&&r.ignoreUndefinedProperties)return null;if(r.path&&r.fieldMask.push(r.path),e instanceof Array){if(r.settings.Rc&&4!==r.Ic)throw r.gc("Nested arrays are not supported");{var s,a=r,o=[];let t=0;for(s of e){let e=Dc(s,a.fc(t));null==e&&(e={nullValue:"NULL_VALUE"}),o.push(e),t++}return{arrayValue:{values:o}}}}var l,t=e,n=r;if(null===(t=_(t)))return{nullValue:"NULL_VALUE"};if("number"==typeof t)return Ri(n.serializer,t);if("boolean"==typeof t)return{booleanValue:t};if("string"==typeof t)return{stringValue:t};if(t instanceof Date)return u=h.fromDate(t),{timestampValue:As(n.serializer,u)};if(t instanceof h)return u=new h(t.seconds,1e3*Math.floor(t.nanoseconds/1e3)),{timestampValue:As(n.serializer,u)};if(t instanceof hc)return{geoPointValue:{latitude:t.latitude,longitude:t.longitude}};if(t instanceof oc)return{bytesValue:Ds(n.serializer,t._byteString)};if(t instanceof F){var u=n.databaseId,i=t.firestore._databaseId;if(i.isEqual(u))return{referenceValue:Ns(t.firestore._databaseId||n.databaseId,t._key.path)};throw n.gc(`Document reference is for database ${i.projectId}/${i.database} but should be for database ${u.projectId}/`+u.database)}if(t instanceof cc)return e=t,l=n,{mapValue:{fields:{[on]:{stringValue:hn},[cn]:{arrayValue:{values:e.toArray().map(e=>{if("number"!=typeof e)throw l.gc("VectorValues must only contain numeric values.");return Ni(l.serializer,e)})}}}}};throw n.gc("Unsupported field value: "+Vh(t))}function Nc(e,n){let i={};return Ur(e)?n.path&&0<n.path.length&&n.fieldMask.push(n.path):Br(e,(e,t)=>{var r=Dc(t,n.Ac(e));null!=r&&(i[e]=r)}),{mapValue:{fields:i}}}function kc(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof h||e instanceof hc||e instanceof oc||e instanceof F||e instanceof uc||e instanceof cc)}function Rc(e,t,r){var n,i;if(!kc(r)||"object"!=typeof(i=r)||null===i||Object.getPrototypeOf(i)!==Object.prototype&&null!==Object.getPrototypeOf(i))throw"an object"===(n=Vh(r))?t.gc(e+" a custom object"):t.gc(e+" "+n)}function Oc(e,t,r){if((t=_(t))instanceof lc)return t._internalPath;if("string"==typeof t)return Lc(e,t);throw Vc("Field path arguments must be of type string or ",e,!1,void 0,r)}let Mc=new RegExp("[~\\*/\\[\\]]");function Lc(t,r,n){if(0<=r.search(Mc))throw Vc(`Invalid field path (${r}). Paths must not contain '~', '*', '/', '[', or ']'`,t,!1,void 0,n);try{return new lc(...r.split("."))._internalPath}catch(e){throw Vc(`Invalid field path (${r}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,t,!1,void 0,n)}}function Vc(e,t,r,n,i){var s=n&&!n.isEmpty(),a=void 0!==i;let o=`Function ${t}() called with invalid data`,l=(r&&(o+=" (via `toFirestore()`)"),o+=". ","");return(s||a)&&(l+=" (found",s&&(l+=" in field "+n),a&&(l+=" in document "+i),l+=")"),new I(b.INVALID_ARGUMENT,o+e+l)}function Pc(e,t){return e.some(e=>e.isEqual(t))}class Fc{constructor(e,t,r,n,i){this._firestore=e,this._userDataWriter=t,this._key=r,this._document=n,this._converter=i}get id(){return this._key.path.lastSegment()}get ref(){return new F(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){var e;if(this._document)return this._converter?(e=new Bc(this._firestore,this._userDataWriter,this._key,this._document,null),this._converter.fromFirestore(e)):this._userDataWriter.convertValue(this._document.data.value)}get(e){if(this._document){var t=this._document.data.field(Uc("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class Bc extends Fc{data(){return super.data()}}function Uc(e,t){return"string"==typeof t?Lc(e,t):(t instanceof lc?t:t._delegate)._internalPath}function qc(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new I(b.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}class jc{}class zc extends jc{}function Kc(e,t,...r){let n=[];t instanceof jc&&n.push(t);var t=n=n.concat(r),i=t.filter(e=>!1).length,s=t.filter(e=>e instanceof Gc).length;if(1<i||0<i&&0<s)throw new I(b.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.");for(let t of n)e=t._apply(e);return e}class Gc extends zc{constructor(e,t,r){super(),this._field=e,this._op=t,this._value=r,this.type="where"}static _create(e,t,r){return new Gc(e,t,r)}_apply(e){var t=this._parse(e);return Zc(e._query,t),new jh(e.firestore,e.converter,fi(e._query,t))}_parse(e){var t=vc(e.firestore);{var n=e._query,i="where",s=t,a=e.firestore._databaseId,o=(e=this._field,this._op),l=this._value;let r;if(e.isKeyField()){if("array-contains"===o||"array-contains-any"===o)throw new I(b.INVALID_ARGUMENT,`Invalid Query. You can't perform '${o}' queries on documentId().`);if("in"===o||"not-in"===o){Jc(l,o);let e=[];for(let t of l)e.push(Xc(a,n,t));r={arrayValue:{values:e}}}else r=Xc(a,n,l)}else"in"!==o&&"not-in"!==o&&"array-contains-any"!==o||Jc(l,o),r=Ac(s,i,l,"in"===o||"not-in"===o);return R.create(e,o,r)}}}(class extends jc{});class $c extends zc{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new $c(e,t)}_apply(e){var t=((e,t,r)=>{if(null!==e.startAt)throw new I(b.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new I(b.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new Vn(t,r)})(e._query,this._field,this._direction);return new jh(e.firestore,e.converter,(t=(e=e._query).explicitOrderBy.concat([t]),new ai(e.path,e.collectionGroup,t,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)))}}class Qc extends zc{constructor(e,t,r){super(),this.type=e,this._limit=t,this._limitType=r}static _create(e,t,r){return new Qc(e,t,r)}_apply(e){return new jh(e.firestore,e.converter,gi(e._query,this._limit,this._limitType))}}class Hc extends zc{constructor(e,t,r){super(),this.type=e,this._docOrFields=t,this._inclusive=r}static _create(e,t,r){return new Hc(e,t,r)}_apply(e){var t,r=Yc(e,this.type,this._docOrFields,this._inclusive);return new jh(e.firestore,e.converter,(e=e._query,t=r,new ai(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,t,e.endAt)))}}class Wc extends zc{constructor(e,t,r){super(),this.type=e,this._docOrFields=t,this._inclusive=r}static _create(e,t,r){return new Wc(e,t,r)}_apply(e){var t,r=Yc(e,this.type,this._docOrFields,this._inclusive);return new jh(e.firestore,e.converter,(e=e._query,t=r,new ai(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,e.startAt,t)))}}function Yc(e,r,n,i){if(n[0]=_(n[0]),n[0]instanceof Fc){var s=e._query,a=e.firestore._databaseId,o=r,l=n[0]._document,u=i;if(!l)throw new I(b.NOT_FOUND,`Can't use a DocumentSnapshot that doesn't exist for ${o}().`);var h=[];for(let t of ci(s))if(t.field.isKeyField())h.push(_n(a,l.key));else{let e=l.data.field(t.field);if(en(e))throw new I(b.INVALID_ARGUMENT,'Invalid query. You are trying to start or end a query using a document for which the field "'+t.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===e){let e=t.field.canonicalString();throw new I(b.INVALID_ARGUMENT,`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`)}h.push(e)}return new On(h,u)}var t=vc(e.firestore),c=e._query,d=e.firestore._databaseId,f=t,g=r,m=n,o=i,p=c.explicitOrderBy;if(m.length>p.length)throw new I(b.INVALID_ARGUMENT,`Too many arguments provided to ${g}(). The number of arguments must be less than or equal to the number of orderBy() clauses`);var y=[];for(let w=0;w<m.length;w++){var v=m[w];if(p[w].field.isKeyField()){if("string"!=typeof v)throw new I(b.INVALID_ARGUMENT,`Invalid query. Expected a string for document ID in ${g}(), but got a `+typeof v);if(!hi(c)&&-1!==v.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${g}() must be a plain document ID, but '${v}' contains a slash.`);let e=c.path.child(T.fromString(v));if(!x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${g}() must result in a valid document path, but '${e}' is not because it contains an odd number of segments.`);let t=new x(e);y.push(_n(d,t))}else{let e=Ac(f,g,v);y.push(e)}}return new On(y,o)}function Xc(e,t,r){if("string"==typeof(r=_(r))){if(""===r)throw new I(b.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!hi(t)&&-1!==r.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${r}' contains a '/' character.`);var n=t.path.child(T.fromString(r));if(x.isDocumentKey(n))return _n(e,new x(n));throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${n}' is not because it has an odd number of segments (${n.length}).`)}if(r instanceof F)return _n(e,r._key);throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${Vh(r)}.`)}function Jc(e,t){if(!Array.isArray(e)||0===e.length)throw new I(b.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function Zc(e,t){var r=((t,r)=>{for(var n of t)for(let e of n.getFlattenedFilters())if(0<=r.indexOf(e.op))return e.op;return null})(e.filters,(e=>{switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}})(t.op));if(null!==r)throw r===t.op?new I(b.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new I(b.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${r.toString()}' filters.`)}class ed{convertValue(e,t="none"){switch(fn(e)){case 0:return null;case 1:return e.booleanValue;case 2:return N(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(Wr(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw E(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,r="none"){let n={};return Br(e,(e,t)=>{n[e]=this.convertValue(t,r)}),n}convertVectorValue(e){var t=null==(t=null==(t=null==(t=e.fields)?void 0:t[cn].arrayValue)?void 0:t.values)?void 0:t.map(e=>N(e.doubleValue));return new cc(t)}convertGeoPoint(e){return new hc(N(e.latitude),N(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":var r=tn(e);return null==r?null:this.convertValue(r,t);case"estimate":return this.convertTimestamp(rn(e));default:return null}}convertTimestamp(e){var t=Hr(e);return new h(t.seconds,t.nanos)}convertDocumentKey(e,t){var r=T.fromString(e),n=(y(Js(r),9688,{name:e}),new an(r.get(1),r.get(3))),r=new x(r.popFirst(5));return n.isEqual(t)||d(`Document ${r} contains a document reference within a different database (${n.projectId}/${n.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),r}}function td(e,t,r){return e?r&&(r.merge||r.mergeFields)?e.toFirestore(t,r):e.toFirestore(t):t}class rd extends ed{constructor(e){super(),this.firestore=e}convertBytes(e){return new oc(e)}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return new F(this.firestore,null,t)}}class nd{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class id extends Fc{constructor(e,t,r,n,i,s){super(e,t,r,n,s),this._firestore=e,this._firestoreImpl=e,this.metadata=i}exists(){return super.exists()}data(e={}){var t;if(this._document)return this._converter?(t=new sd(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null),this._converter.fromFirestore(t,e)):this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}get(e,t={}){if(this._document){var r=this._document.data.field(Uc("DocumentSnapshot.get",e));if(null!==r)return this._userDataWriter.convertValue(r,t.serverTimestamps)}}}class sd extends id{data(e={}){return super.data(e)}}class ad{constructor(e,t,r,n){this._firestore=e,this._userDataWriter=t,this._snapshot=n,this.metadata=new nd(n.hasPendingWrites,n.fromCache),this.query=r}get docs(){let t=[];return this.forEach(e=>t.push(e)),t}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(t,r){this._snapshot.docs.forEach(e=>{t.call(r,new sd(this._firestore,this._userDataWriter,e.key,e,new nd(this._snapshot.mutatedKeys.has(e.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){var t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new I(b.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=((s,t)=>{if(s._snapshot.oldDocs.isEmpty()){let r=0;return s._snapshot.docChanges.map(e=>{var t=new sd(s._firestore,s._userDataWriter,e.doc.key,e.doc,new nd(s._snapshot.mutatedKeys.has(e.doc.key),s._snapshot.fromCache),s.query.converter);return e.doc,{type:"added",doc:t,oldIndex:-1,newIndex:r++}})}{let i=s._snapshot.oldDocs;return s._snapshot.docChanges.filter(e=>t||3!==e.type).map(e=>{var t=new sd(s._firestore,s._userDataWriter,e.doc.key,e.doc,new nd(s._snapshot.mutatedKeys.has(e.doc.key),s._snapshot.fromCache),s.query.converter);let r=-1,n=-1;return 0!==e.type&&(r=i.indexOf(e.doc.key),i=i.delete(e.doc.key)),1!==e.type&&(i=i.add(e.doc),n=i.indexOf(e.doc.key)),{type:(e=>{switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return E(61501,{type:e})}})(e.type),doc:t,oldIndex:r,newIndex:n}})}})(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}}function od(e,t){return e instanceof id&&t instanceof id?e._firestore===t._firestore&&e._key.isEqual(t._key)&&(null===e._document?null===t._document:e._document.isEqual(t._document))&&e._converter===t._converter:e instanceof ad&&t instanceof ad&&e._firestore===t._firestore&&Qh(e.query,t.query)&&e.metadata.isEqual(t.metadata)&&e._snapshot.isEqual(t._snapshot)}class ld extends ed{constructor(e){super(),this.firestore=e}convertBytes(e){return new oc(e)}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return new F(this.firestore,null,t)}}function ud(e,t,r){e=P(e,F);var n=P(e.firestore,B),i=td(e.converter,t,r);return dd(n,[wc(vc(n),"setDoc",e._key,i,null!==e.converter,r).toMutation(e._key,L.none())])}function hd(e,t,r,...n){e=P(e,F);var i=P(e.firestore,B),s=vc(i);return dd(i,[("string"==typeof(t=_(t))||t instanceof lc?Cc(s,"updateDoc",e._key,t,r,n):xc(s,"updateDoc",e._key,t)).toMutation(e._key,L.exists(!0))])}function cd(n,...i){var t,r;n=_(n);let e={includeMetadataChanges:!1,source:"default"},s=0;"object"!=typeof i[s]||Xh(i[s])||(e=i[s],s++);var a={includeMetadataChanges:e.includeMetadataChanges,source:e.source};if(Xh(i[s])){let e=i[s];i[s]=null==(r=e.next)?void 0:r.bind(e),i[s+1]=null==(t=e.error)?void 0:t.bind(e),i[s+2]=null==(r=e.complete)?void 0:r.bind(e)}let o,l,u;if(n instanceof F)l=P(n.firestore,B),u=li(n._key.path),o={next:e=>{i[s]&&i[s](fd(l,n,e))},error:i[s+1],complete:i[s+2]};else{let t=P(n,jh),r=(l=P(t.firestore,B),u=t._query,new ld(l));o={next:e=>{i[s]&&i[s](new ad(l,r,t,e))},error:i[s+1],complete:i[s+2]},qc(n._query)}{var h=rc(l),c=u,d=a,f=o;let e=new oh(f),t=new Tu(c,e,d);return h.asyncQueue.enqueueAndForget(async()=>_u(await bh(h),t)),()=>{e.xu(),h.asyncQueue.enqueueAndForget(async()=>bu(await bh(h),t))}}}function dd(t,r){{var n=rc(t),i=r;let e=new f;return n.asyncQueue.enqueueAndForget(async()=>Pu(await _h(n),i,e)),e.promise}}function fd(e,t,r){var n=r.docs.get(t._key),i=new ld(e);return new id(e,i,t._key,n,new nd(r.hasPendingWrites,r.fromCache),t.converter)}let gd={maxAttempts:5};class md{constructor(e,t){this._firestore=e,this._commitHandler=t,this._mutations=[],this._committed=!1,this._dataReader=vc(e)}set(e,t,r){this._verifyNotCommitted();var n=pd(e,this._firestore),i=td(n.converter,t,r),i=wc(this._dataReader,"WriteBatch.set",n._key,i,null!==n.converter,r);return this._mutations.push(i.toMutation(n._key,L.none())),this}update(e,t,r,...n){this._verifyNotCommitted();var i=pd(e,this._firestore),s="string"==typeof(t=_(t))||t instanceof lc?Cc(this._dataReader,"WriteBatch.update",i._key,t,r,n):xc(this._dataReader,"WriteBatch.update",i._key,t);return this._mutations.push(s.toMutation(i._key,L.exists(!0))),this}delete(e){this._verifyNotCommitted();var t=pd(e,this._firestore);return this._mutations=this._mutations.concat(new ts(t._key,L.none())),this}commit(){return this._verifyNotCommitted(),this._committed=!0,0<this._mutations.length?this._commitHandler(this._mutations):Promise.resolve()}_verifyNotCommitted(){if(this._committed)throw new I(b.FAILED_PRECONDITION,"A write batch can no longer be used after commit() has been called.")}}function pd(e,t){if((e=_(e)).firestore!==t)throw new I(b.INVALID_ARGUMENT,"Provided document reference is from a different Firestore instance.");return e}class yd extends class{constructor(e,t){this._firestore=e,this._transaction=t,this._dataReader=vc(e)}get(e){let r=pd(e,this._firestore),n=new rd(this._firestore);return this._transaction.lookup([r._key]).then(e=>{if(!e||1!==e.length)return E(24041);var t=e[0];if(t.isFoundDocument())return new Fc(this._firestore,n,t.key,t,r.converter);if(t.isNoDocument())return new Fc(this._firestore,n,r._key,null,r.converter);throw E(18433,{doc:t})})}set(e,t,r){var n=pd(e,this._firestore),i=td(n.converter,t,r),i=wc(this._dataReader,"Transaction.set",n._key,i,null!==n.converter,r);return this._transaction.set(n._key,i),this}update(e,t,r,...n){var i=pd(e,this._firestore),s="string"==typeof(t=_(t))||t instanceof lc?Cc(this._dataReader,"Transaction.update",i._key,t,r,n):xc(this._dataReader,"Transaction.update",i._key,t);return this._transaction.update(i._key,s),this}delete(e){var t=pd(e,this._firestore);return this._transaction.delete(t._key),this}}{constructor(e,t){super(e,t),this._firestore=e}get(e){let t=pd(e,this._firestore),r=new ld(this._firestore);return super.get(e).then(e=>new id(this._firestore,r,t._key,e._document,new nd(!1,!1),t.converter))}}function vd(r,n,e){r=P(r,B);var i=Object.assign(Object.assign({},gd),e);if(i.maxAttempts<1)throw new I(b.INVALID_ARGUMENT,"Max attempts must be at least 1");{var s=rc(r),a=e=>n(new yd(r,e)),o=i;let t=new f;return s.asyncQueue.enqueueAndForget(async()=>{var e=await ph(s).then(e=>e.datastore);new hh(s.asyncQueue,e,o,a,t).Wu()}),t.promise}}ec=!0,t=Gd.SDK_VERSION,ve=t,Gd._registerComponent(new ue("firestore",(e,{instanceIdentifier:t,options:r})=>{var n=e.getProvider("app").getImmediate(),n=new B(new Ce(e.getProvider("auth-internal")),new ke(n,e.getProvider("app-check-internal")),((e,t)=>{if(Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))return new an(e.options.projectId,t);throw new I(b.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.')})(n,t),n);return r=Object.assign({useFetchStreams:ec},r),n._setSettings(r),n},"PUBLIC").setMultipleInstances(!0)),Gd.registerVersion(ye,"4.7.17",Zh),Gd.registerVersion(ye,"4.7.17","esm2017");function wd(e,t){if(void 0===t)return{merge:!1};if(void 0!==t.mergeFields&&void 0!==t.merge)throw new I("invalid-argument",`Invalid options passed to function ${e}(): You cannot `+'specify both "merge" and "mergeFields".');return t}function _d(){if("undefined"==typeof Uint8Array)throw new I("unimplemented","Uint8Arrays are not available in this environment.")}function bd(){if("undefined"==typeof atob)throw new I("unimplemented","Blobs are unavailable in Firestore in this environment.")}class Id{constructor(e){this._delegate=e}static fromBase64String(e){return bd(),new Id(oc.fromBase64String(e))}static fromUint8Array(e){return _d(),new Id(oc.fromUint8Array(e))}toBase64(){return bd(),this._delegate.toBase64()}toUint8Array(){return _d(),this._delegate.toUint8Array()}isEqual(e){return this._delegate.isEqual(e._delegate)}toString(){return"Blob(base64: "+this.toBase64()+")"}}function Td(e){var t=["next","error","complete"];if("object"==typeof e&&null!==e){var r,n=e;for(r of t)if(r in n&&"function"==typeof n[r])return 1}}class Ed{enableIndexedDbPersistence(e,r){{e=e._delegate;var n={forceOwnership:r};be("enableIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead.");let t=e._freezeSettings();return ic(e,sh.provider,{build:e=>new nh(e,t.cacheSizeBytes,null==n?void 0:n.forceOwnership)}),Promise.resolve()}}enableMultiTabIndexedDbPersistence(e){return(async e=>{be("enableMultiTabIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead.");let t=e._freezeSettings();ic(e,sh.provider,{build:e=>new ih(e,t.cacheSizeBytes)})})(e._delegate)}clearIndexedDbPersistence(e){return sc(e._delegate)}}class Sd{constructor(e,t,r){this._delegate=t,this._persistenceProvider=r,this.INTERNAL={delete:()=>this.terminate()},e instanceof an||(this._appCompat=e)}get _databaseId(){return this._delegate._databaseId}settings(e){var t=this._delegate._getSettings();e.merge||t.host===e.host||be("You are overriding the original host. If you did not intend to override your settings, use {merge: true}."),e.merge&&delete(e=Object.assign(Object.assign({},t),e)).merge,this._delegate._setSettings(e)}useEmulator(e,t,r={}){qh(this._delegate,e,t,r)}enableNetwork(){return ac(this._delegate)}disableNetwork(){return Ih(rc(P(this._delegate,B)))}enablePersistence(e){let t=!1,r=!1;return e&&(t=!!e.synchronizeTabs,r=!!e.experimentalForceOwningTab,Oh("synchronizeTabs",t,"experimentalForceOwningTab",r)),t?this._persistenceProvider.enableMultiTabIndexedDbPersistence(this):this._persistenceProvider.enableIndexedDbPersistence(this,r)}clearPersistence(){return this._persistenceProvider.clearIndexedDbPersistence(this)}terminate(){return this._appCompat&&(this._appCompat._removeServiceInstance("firestore-compat"),this._appCompat._removeServiceInstance("firestore")),this._delegate._delete()}waitForPendingWrites(){var t=this._delegate;{var r=rc(t=P(t,B));let e=new f;return r.asyncQueue.enqueueAndForget(async()=>qu(await _h(r),e)),e.promise}}onSnapshotsInSync(e){return t=this._delegate,e=e,Ch(rc(t=P(t,B)),Xh(e)?e:{next:e});var t}get app(){if(this._appCompat)return this._appCompat;throw new I("failed-precondition","Firestore was not initialized using the Firebase SDK. 'app' is not available")}collection(e){try{return new Bd(this,Kh(this._delegate,e))}catch(e){throw kd(e,"collection()","Firestore.collection()")}}doc(e){try{return new Nd(this,Gh(this._delegate,e))}catch(e){throw kd(e,"doc()","Firestore.doc()")}}collectionGroup(e){try{return new Vd(this,((e,t)=>{if(e=P(e,Uh),Rh("collectionGroup","collection id",t),0<=t.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid collection ID '${t}' passed to function collectionGroup(). Collection IDs must not contain '/'.`);return new jh(e,null,(e=t,new ai(T.emptyPath(),e)))})(this._delegate,e))}catch(e){throw kd(e,"collectionGroup()","Firestore.collectionGroup()")}}runTransaction(t){return vd(this._delegate,e=>t(new Cd(this,e)))}batch(){return rc(this._delegate),new Ad(new md(this._delegate,e=>dd(this._delegate,e)))}loadBundle(e){return t=this._delegate,e=e,r=rc(t=P(t,B)),n=new Jh,Ah(r,t._databaseId,e,n),n;var t,r,n}namedQuery(e){return t=this._delegate,e=e,Dh(rc(t=P(t,B)),e).then(e=>e?new jh(t,null,e.query):null).then(e=>e?new Vd(this,e):null);var t}}class xd extends ed{constructor(e){super(),this.firestore=e}convertBytes(e){return new Id(new oc(e))}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return Nd.forKey(t,this.firestore,null)}}class Cd{constructor(e,t){this._firestore=e,this._delegate=t,this._userDataWriter=new xd(e)}get(e){let t=Ud(e);return this._delegate.get(t).then(e=>new Md(this._firestore,new id(this._firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,t.converter)))}set(e,t,r){var n=Ud(e);return r?(wd("Transaction.set",r),this._delegate.set(n,t,r)):this._delegate.set(n,t),this}update(e,t,r,...n){var i=Ud(e);return 2===arguments.length?this._delegate.update(i,t):this._delegate.update(i,t,r,...n),this}delete(e){var t=Ud(e);return this._delegate.delete(t),this}}class Ad{constructor(e){this._delegate=e}set(e,t,r){var n=Ud(e);return r?(wd("WriteBatch.set",r),this._delegate.set(n,t,r)):this._delegate.set(n,t),this}update(e,t,r,...n){var i=Ud(e);return 2===arguments.length?this._delegate.update(i,t):this._delegate.update(i,t,r,...n),this}delete(e){var t=Ud(e);return this._delegate.delete(t),this}commit(){return this._delegate.commit()}}class Dd{constructor(e,t,r){this._firestore=e,this._userDataWriter=t,this._delegate=r}fromFirestore(e,t){var r=new sd(this._firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,null);return this._delegate.fromFirestore(new Ld(this._firestore,r),null!=t?t:{})}toFirestore(e,t){return t?this._delegate.toFirestore(e,t):this._delegate.toFirestore(e)}static getInstance(e,t){var r=Dd.INSTANCES;let n=r.get(e),i=(n||(n=new WeakMap,r.set(e,n)),n.get(t));return i||(i=new Dd(e,new xd(e),t),n.set(t,i)),i}}Dd.INSTANCES=new WeakMap;class Nd{constructor(e,t){this.firestore=e,this._delegate=t,this._userDataWriter=new xd(e)}static forPath(e,t,r){if(e.length%2!=0)throw new I("invalid-argument","Invalid document reference. Document references must have an even number of segments, but "+e.canonicalString()+" has "+e.length);return new Nd(t,new F(t._delegate,r,new x(e)))}static forKey(e,t,r){return new Nd(t,new F(t._delegate,r,e))}get id(){return this._delegate.id}get parent(){return new Bd(this.firestore,this._delegate.parent)}get path(){return this._delegate.path}collection(e){try{return new Bd(this.firestore,Kh(this._delegate,e))}catch(e){throw kd(e,"collection()","DocumentReference.collection()")}}isEqual(e){return(e=_(e))instanceof F&&$h(this._delegate,e)}set(e,t){t=wd("DocumentReference.set",t);try{return t?ud(this._delegate,e,t):ud(this._delegate,e)}catch(e){throw kd(e,"setDoc()","DocumentReference.set()")}}update(e,t,...r){try{return 1===arguments.length?hd(this._delegate,e):hd(this._delegate,e,t,...r)}catch(e){throw kd(e,"updateDoc()","DocumentReference.update()")}}delete(){return dd(P((e=this._delegate).firestore,B),[new ts(e._key,L.none())]);var e}onSnapshot(...e){var t=Rd(e),r=Od(e,e=>new Md(this.firestore,new id(this.firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,this._delegate.converter)));return cd(this._delegate,t,r)}get(e){let t;return(t=("cache"===(null==e?void 0:e.source)?t=>{t=P(t,F);let r=P(t.firestore,B),e=rc(r),n=new ld(r);return Th(e,t._key).then(e=>new id(r,n,t._key,e,new nd(null!==e&&e.hasLocalMutations,!0),t.converter))}:"server"===(null==e?void 0:e.source)?t=>{t=P(t,F);let r=P(t.firestore,B);return Eh(rc(r),t._key,{source:"server"}).then(e=>fd(r,t,e))}:t=>{t=P(t,F);let r=P(t.firestore,B);return Eh(rc(r),t._key).then(e=>fd(r,t,e))})(this._delegate)).then(e=>new Md(this.firestore,new id(this.firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,this._delegate.converter)))}withConverter(e){return new Nd(this.firestore,e?this._delegate.withConverter(Dd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}function kd(e,t,r){return e.message=e.message.replace(t,r),e}function Rd(e){for(var t of e)if("object"==typeof t&&!Td(t))return t;return{}}function Od(e,t){let r,n,i;return i=Td(e[0])?e[0]:Td(e[1])?e[1]:"function"==typeof e[0]?{next:e[0],error:e[1],complete:e[2]}:{next:e[1],error:e[2],complete:e[3]},{next:e=>{i.next&&i.next(t(e))},error:null==(r=i.error)?void 0:r.bind(i),complete:null==(n=i.complete)?void 0:n.bind(i)}}class Md{constructor(e,t){this._firestore=e,this._delegate=t}get ref(){return new Nd(this._firestore,this._delegate.ref)}get id(){return this._delegate.id}get metadata(){return this._delegate.metadata}get exists(){return this._delegate.exists()}data(e){return this._delegate.data(e)}get(e,t){return this._delegate.get(e,t)}isEqual(e){return od(this._delegate,e._delegate)}}class Ld extends Md{data(e){var t=this._delegate.data(e);return this._delegate._converter||(e="Document in a QueryDocumentSnapshot should exist",void 0!==t)||E(57014,e),t}}class Vd{constructor(e,t){this.firestore=e,this._delegate=t,this._userDataWriter=new xd(e)}where(e,t,r){try{return new Vd(this.firestore,Kc(this._delegate,(n=r,i=t,s=Uc("where",e),Gc._create(s,i,n))))}catch(e){throw kd(e,/(orderBy|where)\(\)/,"Query.$1()")}var n,i,s}orderBy(e,t){try{return new Vd(this.firestore,Kc(this._delegate,([r,n="asc"]=[e,t],i=n,s=Uc("orderBy",r),$c._create(s,i))))}catch(e){throw kd(e,/(orderBy|where)\(\)/,"Query.$1()")}var r,n,i,s}limit(e){try{return new Vd(this.firestore,Kc(this._delegate,(Ph("limit",t=e),Qc._create("limit",t,"F"))))}catch(e){throw kd(e,"limit()","Query.limit()")}var t}limitToLast(e){try{return new Vd(this.firestore,Kc(this._delegate,(Ph("limitToLast",t=e),Qc._create("limitToLast",t,"L"))))}catch(e){throw kd(e,"limitToLast()","Query.limitToLast()")}var t}startAt(...e){try{return new Vd(this.firestore,Kc(this._delegate,([...t]=[...e],Hc._create("startAt",t,!0))))}catch(e){throw kd(e,"startAt()","Query.startAt()")}var t}startAfter(...e){try{return new Vd(this.firestore,Kc(this._delegate,([...t]=[...e],Hc._create("startAfter",t,!1))))}catch(e){throw kd(e,"startAfter()","Query.startAfter()")}var t}endBefore(...e){try{return new Vd(this.firestore,Kc(this._delegate,([...t]=[...e],Wc._create("endBefore",t,!1))))}catch(e){throw kd(e,"endBefore()","Query.endBefore()")}var t}endAt(...e){try{return new Vd(this.firestore,Kc(this._delegate,([...t]=[...e],Wc._create("endAt",t,!0))))}catch(e){throw kd(e,"endAt()","Query.endAt()")}var t}isEqual(e){return Qh(this._delegate,e._delegate)}get(e){let t;return(t=("cache"===(null==e?void 0:e.source)?t=>{t=P(t,jh);let r=P(t.firestore,B),e=rc(r),n=new ld(r);return Sh(e,t._query).then(e=>new ad(r,n,t,e))}:"server"===(null==e?void 0:e.source)?t=>{t=P(t,jh);let r=P(t.firestore,B),e=rc(r),n=new ld(r);return xh(e,t._query,{source:"server"}).then(e=>new ad(r,n,t,e))}:t=>{t=P(t,jh);let r=P(t.firestore,B),e=rc(r),n=new ld(r);return qc(t._query),xh(e,t._query).then(e=>new ad(r,n,t,e))})(this._delegate)).then(e=>new Fd(this.firestore,new ad(this.firestore._delegate,this._userDataWriter,this._delegate,e._snapshot)))}onSnapshot(...e){var t=Rd(e),r=Od(e,e=>new Fd(this.firestore,new ad(this.firestore._delegate,this._userDataWriter,this._delegate,e._snapshot)));return cd(this._delegate,t,r)}withConverter(e){return new Vd(this.firestore,e?this._delegate.withConverter(Dd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}class Pd{constructor(e,t){this._firestore=e,this._delegate=t}get type(){return this._delegate.type}get doc(){return new Ld(this._firestore,this._delegate.doc)}get oldIndex(){return this._delegate.oldIndex}get newIndex(){return this._delegate.newIndex}}class Fd{constructor(e,t){this._firestore=e,this._delegate=t}get query(){return new Vd(this._firestore,this._delegate.query)}get metadata(){return this._delegate.metadata}get size(){return this._delegate.size}get empty(){return this._delegate.empty}get docs(){return this._delegate.docs.map(e=>new Ld(this._firestore,e))}docChanges(e){return this._delegate.docChanges(e).map(e=>new Pd(this._firestore,e))}forEach(t,r){this._delegate.forEach(e=>{t.call(r,new Ld(this._firestore,e))})}isEqual(e){return od(this._delegate,e._delegate)}}class Bd extends Vd{constructor(e,t){super(e,t),this.firestore=e,this._delegate=t}get id(){return this._delegate.id}get path(){return this._delegate.path}get parent(){var e=this._delegate.parent;return e?new Nd(this.firestore,e):null}doc(e){try{return void 0===e?new Nd(this.firestore,Gh(this._delegate)):new Nd(this.firestore,Gh(this._delegate,e))}catch(e){throw kd(e,"doc()","CollectionReference.doc()")}}add(e){return((e,t)=>{let r=P(e.firestore,B),n=Gh(e),i=td(e.converter,t);return dd(r,[wc(vc(e.firestore),"addDoc",n._key,i,null!==e.converter,{}).toMutation(n._key,L.exists(!1))]).then(()=>n)})(this._delegate,e).then(e=>new Nd(this.firestore,e))}isEqual(e){return $h(this._delegate,e._delegate)}withConverter(e){return new Bd(this.firestore,e?this._delegate.withConverter(Dd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}function Ud(e){return P(e,F)}class qd{static serverTimestamp(){var e=new Ic("serverTimestamp");return e._methodName="FieldValue.serverTimestamp",new qd(e)}static delete(){var e=new _c("deleteField");return e._methodName="FieldValue.delete",new qd(e)}static arrayUnion(...e){[...e]=[...e];var t=new Tc("arrayUnion",e);return t._methodName="FieldValue.arrayUnion",new qd(t)}static arrayRemove(...e){[...e]=[...e];var t=new Ec("arrayRemove",e);return t._methodName="FieldValue.arrayRemove",new qd(t)}static increment(e){e=e;var t=new Sc("increment",e);return t._methodName="FieldValue.increment",new qd(t)}constructor(e){this._delegate=e}isEqual(e){return this._delegate.isEqual(e._delegate)}}let jd={Firestore:Sd,GeoPoint:hc,Timestamp:h,Blob:Id,Transaction:Cd,WriteBatch:Ad,DocumentReference:Nd,DocumentSnapshot:Md,Query:Vd,QueryDocumentSnapshot:Ld,QuerySnapshot:Fd,CollectionReference:Bd,FieldPath:class zd{constructor(...e){this._delegate=new lc(...e)}static documentId(){return new zd(v.keyField().canonicalString())}isEqual(e){return(e=_(e))instanceof lc&&this._delegate._internalPath.isEqual(e._internalPath)}},FieldValue:qd,setLogLevel:function(e){e=e,we.setLogLevel(e)},CACHE_SIZE_UNLIMITED:-1};t=i.default,tc=(e,t)=>new Sd(e,t,new Ed),t.INTERNAL.registerComponent(new ue("firestore-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("firestore").getImmediate();return tc(t,r)},"PUBLIC").setServiceProps(Object.assign({},jd))),t.registerVersion("@firebase/firestore-compat","0.3.52")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-firestore-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-firestore-compat.js.map
