{"version": 3, "file": "firebase-data-connect.js", "sources": ["../util/src/url.ts", "../util/src/emulator.ts", "../util/src/errors.ts", "../component/src/component.ts", "../logger/src/logger.ts", "../data-connect/src/core/version.ts", "../data-connect/src/core/AppCheckTokenProvider.ts", "../data-connect/src/core/error.ts", "../data-connect/src/logger.ts", "../data-connect/src/core/FirebaseAuthProvider.ts", "../data-connect/src/api/Reference.ts", "../data-connect/src/util/encoder.ts", "../data-connect/src/core/QueryManager.ts", "../data-connect/src/util/map.ts", "../data-connect/src/network/transport/index.ts", "../data-connect/src/util/url.ts", "../data-connect/src/network/fetch.ts", "../data-connect/src/network/transport/rest.ts", "../data-connect/src/api/Mutation.ts", "../data-connect/src/api/DataConnect.ts", "../data-connect/src/api/query.ts", "../data-connect/src/util/validateArgs.ts", "../data-connect/src/api.browser.ts", "../data-connect/src/register.ts", "../data-connect/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nexport function isCloudWorkstation(host: string): boolean {\n  return host.endsWith('.cloudworkstations.dev');\n}\n\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nexport async function pingServer(endpoint: string): Promise<boolean> {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\nimport { isCloudWorkstation } from './url';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n\ninterface EmulatorStatusMap {\n  [name: string]: boolean;\n}\nconst emulatorStatus: EmulatorStatusMap = {};\n\ninterface EmulatorSummary {\n  prod: string[];\n  emulator: string[];\n}\n\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary(): EmulatorSummary {\n  const summary: EmulatorSummary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\n\nfunction getOrCreateEl(id: string): { created: boolean; element: HTMLElement } {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return { created, element: parentDiv };\n}\n\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nexport function updateEmulatorBanner(\n  name: string,\n  isRunningEmulator: boolean\n): void {\n  if (\n    typeof window === 'undefined' ||\n    typeof document === 'undefined' ||\n    !isCloudWorkstation(window.location.host) ||\n    emulatorStatus[name] === isRunningEmulator ||\n    emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n    previouslyDismissed\n  ) {\n    return;\n  }\n\n  emulatorStatus[name] = isRunningEmulator;\n\n  function prefixedId(id: string): string {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n\n  function tearDown(): void {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n\n  function setupBannerStyles(bannerEl: HTMLElement): void {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n\n  function setupIconStyles(prependIcon: SVGElement, iconId: string): void {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n\n  function setupCloseBtn(): HTMLSpanElement {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n\n  function setupLinkStyles(\n    learnMoreLink: HTMLAnchorElement,\n    learnMoreId: string\n  ): void {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href =\n      'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n\n  function setupDom(): void {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText: HTMLSpanElement =\n      document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink: HTMLAnchorElement =\n      (document.getElementById(learnMoreId) as HTMLAnchorElement) ||\n      document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon: SVGElement =\n      (document.getElementById(\n        prependIconId\n      ) as HTMLOrSVGElement as SVGElement) ||\n      document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** The semver (www.semver.org) version of the SDK. */\nexport let SDK_VERSION = '';\n\n/**\n * SDK_VERSION should be set before any database instance is created\n * @internal\n */\nexport function setSDKVersion(version: string): void {\n  SDK_VERSION = version;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _isFirebaseServerApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  AppCheckTokenListener,\n  AppCheckTokenResult,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\n\n/**\n * @internal\n * Abstraction around AppCheck's token fetching capabilities.\n */\nexport class AppCheckTokenProvider {\n  private appCheck?: FirebaseAppCheckInternal;\n  private serverAppAppCheckToken?: string;\n  constructor(\n    app: FirebaseApp,\n    private appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.appCheck = appCheckProvider?.getImmediate({ optional: true });\n    if (!this.appCheck) {\n      void appCheckProvider\n        ?.get()\n        .then(appCheck => (this.appCheck = appCheck))\n        .catch();\n    }\n  }\n\n  getToken(): Promise<AppCheckTokenResult> {\n    if (this.serverAppAppCheckToken) {\n      return Promise.resolve({ token: this.serverAppAppCheckToken });\n    }\n\n    if (!this.appCheck) {\n      return new Promise<AppCheckTokenResult>((resolve, reject) => {\n        // Support delayed initialization of FirebaseAppCheck. This allows our\n        // customers to initialize the RTDB SDK before initializing Firebase\n        // AppCheck and ensures that all requests are authenticated if a token\n        // becomes available before the timoeout below expires.\n        setTimeout(() => {\n          if (this.appCheck) {\n            this.getToken().then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this.appCheck.getToken();\n  }\n\n  addTokenChangeListener(listener: AppCheckTokenListener): void {\n    void this.appCheckProvider\n      ?.get()\n      .then(appCheck => appCheck.addTokenListener(listener));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nexport type DataConnectErrorCode =\n  | 'other'\n  | 'already-initialized'\n  | 'not-initialized'\n  | 'not-supported'\n  | 'invalid-argument'\n  | 'partial-error'\n  | 'unauthorized';\n\nexport type Code = DataConnectErrorCode;\n\nexport const Code = {\n  OTHER: 'other' as DataConnectErrorCode,\n  ALREADY_INITIALIZED: 'already-initialized' as DataConnectErrorCode,\n  NOT_INITIALIZED: 'not-initialized' as DataConnect<PERSON><PERSON>rCode,\n  NOT_SUPPORTED: 'not-supported' as DataConnect<PERSON>rrorCode,\n  INVALID_ARGUMENT: 'invalid-argument' as DataConnectErrorCode,\n  PARTIAL_ERROR: 'partial-error' as DataConnectErrorCode,\n  UNAUTHORIZED: 'unauthorized' as DataConnectErrorCode\n};\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectError extends FirebaseError {\n  /** @internal */\n  readonly name: string = 'DataConnectError';\n\n  constructor(code: Code, message: string) {\n    super(code, message);\n\n    // Ensure the instanceof operator works as expected on subclasses of Error.\n    // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#custom_error_types\n    // and https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    Object.setPrototypeOf(this, DataConnectError.prototype);\n  }\n\n  /** @internal */\n  toString(): string {\n    return `${this.name}[code=${this.code}]: ${this.message}`;\n  }\n}\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectOperationError extends DataConnectError {\n  /** @internal */\n  readonly name: string = 'DataConnectOperationError';\n\n  /** The response received from the backend. */\n  readonly response: DataConnectOperationFailureResponse;\n\n  /** @hideconstructor */\n  constructor(message: string, response: DataConnectOperationFailureResponse) {\n    super(Code.PARTIAL_ERROR, message);\n    this.response = response;\n  }\n}\n\nexport interface DataConnectOperationFailureResponse {\n  // The \"data\" provided by the backend in the response message.\n  //\n  // Will be `undefined` if no \"data\" was provided in the response message.\n  // Otherwise, will be `null` if `null` was explicitly specified as the \"data\"\n  // in the response message. Otherwise, will be the value of the \"data\"\n  // specified as the \"data\" in the response message\n  readonly data?: Record<string, unknown> | null;\n\n  // The list of errors provided by the backend in the response message.\n  readonly errors: DataConnectOperationFailureResponseErrorInfo[];\n}\n\n// Information about the error, as provided in the response from the backend.\n// See https://spec.graphql.org/draft/#sec-Errors\nexport interface DataConnectOperationFailureResponseErrorInfo {\n  // The error message.\n  readonly message: string;\n\n  // The path of the field in the response data to which this error relates.\n  // String values in this array refer to field names. Numeric values in this\n  // array always satisfy `Number.isInteger()` and refer to the index in an\n  // array.\n  readonly path: Array<string | number>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Logger, LogLevelString } from '@firebase/logger';\n\nimport { SDK_VERSION } from './core/version';\n\nconst logger = new Logger('@firebase/data-connect');\nexport function setLogLevel(logLevel: LogLevelString): void {\n  logger.setLogLevel(logLevel);\n}\nexport function logDebug(msg: string): void {\n  logger.debug(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n\nexport function logError(msg: string): void {\n  logger.error(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName,\n  FirebaseAuthTokenData\n} from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { logDebug, logError } from '../logger';\n\n// @internal\nexport interface AuthTokenProvider {\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null>;\n  addTokenChangeListener(listener: AuthTokenListener): void;\n}\nexport type AuthTokenListener = (token: string | null) => void;\n\n// @internal\nexport class FirebaseAuthProvider implements AuthTokenProvider {\n  private _auth: FirebaseAuthInternal;\n  constructor(\n    private _appName: string,\n    private _options: FirebaseOptions,\n    private _authProvider: Provider<FirebaseAuthInternalName>\n  ) {\n    this._auth = _authProvider.getImmediate({ optional: true })!;\n    if (!this._auth) {\n      _authProvider.onInit(auth => (this._auth = auth));\n    }\n  }\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null> {\n    if (!this._auth) {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          if (this._auth) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this._auth.getToken(forceRefresh).catch(error => {\n      if (error && error.code === 'auth/token-not-initialized') {\n        logDebug(\n          'Got auth/token-not-initialized error.  Treating as null token.'\n        );\n        return null;\n      } else {\n        logError(\n          'Error received when attempting to retrieve token: ' +\n            JSON.stringify(error)\n        );\n        return Promise.reject(error);\n      }\n    });\n  }\n  addTokenChangeListener(listener: AuthTokenListener): void {\n    this._auth?.addAuthTokenListener(listener);\n  }\n  removeTokenChangeListener(listener: (token: string | null) => void): void {\n    this._authProvider\n      .get()\n      .then(auth => auth.removeAuthTokenListener(listener))\n      .catch(err => logError(err));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnect, DataConnectOptions } from './DataConnect';\nexport const QUERY_STR = 'query';\nexport const MUTATION_STR = 'mutation';\nexport type ReferenceType = typeof QUERY_STR | typeof MUTATION_STR;\n\nexport const SOURCE_SERVER = 'SERVER';\nexport const SOURCE_CACHE = 'CACHE';\nexport type DataSource = typeof SOURCE_CACHE | typeof SOURCE_SERVER;\n\nexport interface OpResult<Data> {\n  data: Data;\n  source: DataSource;\n  fetchTime: string;\n}\n\nexport interface OperationRef<_Data, Variables> {\n  name: string;\n  variables: Variables;\n  refType: ReferenceType;\n  dataConnect: DataConnect;\n}\n\nexport interface DataConnectResult<Data, Variables> extends OpResult<Data> {\n  ref: OperationRef<Data, Variables>;\n  // future metadata\n}\n\n/**\n * Serialized RefInfo as a result of `QueryResult.toJSON().refInfo`\n */\nexport interface RefInfo<Variables> {\n  name: string;\n  variables: Variables;\n  connectorConfig: DataConnectOptions;\n}\n/**\n * Serialized Ref as a result of `QueryResult.toJSON()`\n */\nexport interface SerializedRef<Data, Variables> extends OpResult<Data> {\n  refInfo: RefInfo<Variables>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type HmacImpl = (obj: unknown) => string;\nexport let encoderImpl: HmacImpl;\nexport function setEncoder(encoder: HmacImpl): void {\n  encoderImpl = encoder;\n}\nsetEncoder(o => JSON.stringify(o));\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DataConnectSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryPromise,\n  QueryRef,\n  QueryResult\n} from '../api/query';\nimport {\n  OperationRef,\n  QUERY_STR,\n  OpResult,\n  SerializedRef,\n  SOURCE_SERVER,\n  DataSource,\n  SOURCE_CACHE\n} from '../api/Reference';\nimport { logDebug } from '../logger';\nimport { DataConnectTransport } from '../network';\nimport { encoderImpl } from '../util/encoder';\nimport { setIfNotExists } from '../util/map';\n\nimport { Code, DataConnectError } from './error';\n\ninterface TrackedQuery<Data, Variables> {\n  ref: Omit<OperationRef<Data, Variables>, 'dataConnect'>;\n  subscriptions: Array<DataConnectSubscription<Data, Variables>>;\n  currentCache: OpResult<Data> | null;\n  lastError: DataConnectError | null;\n}\n\nfunction getRefSerializer<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>,\n  data: Data,\n  source: DataSource\n) {\n  return function toJSON(): SerializedRef<Data, Variables> {\n    return {\n      data,\n      refInfo: {\n        name: queryRef.name,\n        variables: queryRef.variables,\n        connectorConfig: {\n          projectId: queryRef.dataConnect.app.options.projectId!,\n          ...queryRef.dataConnect.getSettings()\n        }\n      },\n      fetchTime: Date.now().toLocaleString(),\n      source\n    };\n  };\n}\n\nexport class QueryManager {\n  _queries: Map<string, TrackedQuery<unknown, unknown>>;\n  constructor(private transport: DataConnectTransport) {\n    this._queries = new Map();\n  }\n  track<Data, Variables>(\n    queryName: string,\n    variables: Variables,\n    initialCache?: OpResult<Data>\n  ): TrackedQuery<Data, Variables> {\n    const ref: TrackedQuery<Data, Variables>['ref'] = {\n      name: queryName,\n      variables,\n      refType: QUERY_STR\n    };\n    const key = encoderImpl(ref);\n    const newTrackedQuery: TrackedQuery<Data, Variables> = {\n      ref,\n      subscriptions: [],\n      currentCache: initialCache || null,\n      lastError: null\n    };\n    // @ts-ignore\n    setIfNotExists(this._queries, key, newTrackedQuery);\n    return this._queries.get(key) as TrackedQuery<Data, Variables>;\n  }\n  addSubscription<Data, Variables>(\n    queryRef: OperationRef<Data, Variables>,\n    onResultCallback: OnResultSubscription<Data, Variables>,\n    onErrorCallback?: OnErrorSubscription,\n    initialCache?: OpResult<Data>\n  ): () => void {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key) as TrackedQuery<\n      Data,\n      Variables\n    >;\n    const subscription = {\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback\n    };\n    const unsubscribe = (): void => {\n      const trackedQuery = this._queries.get(key)!;\n      trackedQuery.subscriptions = trackedQuery.subscriptions.filter(\n        sub => sub !== subscription\n      );\n    };\n    if (initialCache && trackedQuery.currentCache !== initialCache) {\n      logDebug('Initial cache found. Comparing dates.');\n      if (\n        !trackedQuery.currentCache ||\n        (trackedQuery.currentCache &&\n          compareDates(\n            trackedQuery.currentCache.fetchTime,\n            initialCache.fetchTime\n          ))\n      ) {\n        trackedQuery.currentCache = initialCache;\n      }\n    }\n    if (trackedQuery.currentCache !== null) {\n      const cachedData = trackedQuery.currentCache.data;\n      onResultCallback({\n        data: cachedData,\n        source: SOURCE_CACHE,\n        ref: queryRef as QueryRef<Data, Variables>,\n        toJSON: getRefSerializer(\n          queryRef as QueryRef<Data, Variables>,\n          trackedQuery.currentCache.data,\n          SOURCE_CACHE\n        ),\n        fetchTime: trackedQuery.currentCache.fetchTime\n      });\n      if (trackedQuery.lastError !== null && onErrorCallback) {\n        onErrorCallback(undefined);\n      }\n    }\n\n    trackedQuery.subscriptions.push({\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback,\n      unsubscribe\n    });\n    if (!trackedQuery.currentCache) {\n      logDebug(\n        `No cache available for query ${\n          queryRef.name\n        } with variables ${JSON.stringify(\n          queryRef.variables\n        )}. Calling executeQuery.`\n      );\n      const promise = this.executeQuery(queryRef as QueryRef<Data, Variables>);\n      // We want to ignore the error and let subscriptions handle it\n      promise.then(undefined, err => {});\n    }\n    return unsubscribe;\n  }\n  executeQuery<Data, Variables>(\n    queryRef: QueryRef<Data, Variables>\n  ): QueryPromise<Data, Variables> {\n    if (queryRef.refType !== QUERY_STR) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        `ExecuteQuery can only execute query operation`\n      );\n    }\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key)!;\n    const result = this.transport.invokeQuery<Data, Variables>(\n      queryRef.name,\n      queryRef.variables\n    );\n    const newR = result.then(\n      res => {\n        const fetchTime = new Date().toString();\n        const result: QueryResult<Data, Variables> = {\n          ...res,\n          source: SOURCE_SERVER,\n          ref: queryRef,\n          toJSON: getRefSerializer(queryRef, res.data, SOURCE_SERVER),\n          fetchTime\n        };\n        trackedQuery.subscriptions.forEach(subscription => {\n          subscription.userCallback(result);\n        });\n        trackedQuery.currentCache = {\n          data: res.data,\n          source: SOURCE_CACHE,\n          fetchTime\n        };\n        return result;\n      },\n      err => {\n        trackedQuery.lastError = err;\n        trackedQuery.subscriptions.forEach(subscription => {\n          if (subscription.errCallback) {\n            subscription.errCallback(err);\n          }\n        });\n        throw err;\n      }\n    );\n\n    return newR;\n  }\n  enableEmulator(host: string, port: number): void {\n    this.transport.useEmulator(host, port);\n  }\n}\nfunction compareDates(str1: string, str2: string): boolean {\n  const date1 = new Date(str1);\n  const date2 = new Date(str2);\n  return date1.getTime() < date2.getTime();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function setIfNotExists<T>(\n  map: Map<string, T>,\n  key: string,\n  val: T\n): void {\n  if (!map.has(key)) {\n    map.set(key, val);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\n\n/**\n * enum representing different flavors of the SDK used by developers\n * use the CallerSdkType for type-checking, and the CallerSdkTypeEnum for value-checking/assigning\n */\nexport type CallerSdkType =\n  | 'Base' // Core JS SDK\n  | 'Generated' // Generated JS SDK\n  | 'TanstackReactCore' // Tanstack non-generated React SDK\n  | 'GeneratedReact' // Generated React SDK\n  | 'TanstackAngularCore' // Tanstack non-generated Angular SDK\n  | 'GeneratedAngular'; // Generated Angular SDK\nexport const CallerSdkTypeEnum = {\n  Base: 'Base', // Core JS SDK\n  Generated: 'Generated', // Generated JS SDK\n  TanstackReactCore: 'TanstackReactCore', // Tanstack non-generated React SDK\n  GeneratedReact: 'GeneratedReact', // Tanstack non-generated Angular SDK\n  TanstackAngularCore: 'TanstackAngularCore', // Tanstack non-generated Angular SDK\n  GeneratedAngular: 'GeneratedAngular' // Generated Angular SDK\n} as const;\n\n/**\n * @internal\n */\nexport interface DataConnectTransport {\n  invokeQuery<T, U>(\n    queryName: string,\n    body?: U\n  ): Promise<{ data: T; errors: Error[] }>;\n  invokeMutation<T, U>(\n    queryName: string,\n    body?: U\n  ): Promise<{ data: T; errors: Error[] }>;\n  useEmulator(host: string, port?: number, sslEnabled?: boolean): void;\n  onTokenChanged: (token: string | null) => void;\n  _setCallerSdkType(callerSdkType: CallerSdkType): void;\n}\n\n/**\n * @internal\n */\nexport type TransportClass = new (\n  options: DataConnectOptions,\n  apiKey?: string,\n  appId?: string,\n  authProvider?: AuthTokenProvider,\n  appCheckProvider?: AppCheckTokenProvider,\n  transportOptions?: TransportOptions,\n  _isUsingGen?: boolean,\n  _callerSdkType?: CallerSdkType\n) => DataConnectTransport;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\nimport { logError } from '../logger';\n\nexport function urlBuilder(\n  projectConfig: DataConnectOptions,\n  transportOptions: TransportOptions\n): string {\n  const { connector, location, projectId: project, service } = projectConfig;\n  const { host, sslEnabled, port } = transportOptions;\n  const protocol = sslEnabled ? 'https' : 'http';\n  const realHost = host || `firebasedataconnect.googleapis.com`;\n  let baseUrl = `${protocol}://${realHost}`;\n  if (typeof port === 'number') {\n    baseUrl += `:${port}`;\n  } else if (typeof port !== 'undefined') {\n    logError('Port type is of an invalid type');\n    throw new DataConnectError(\n      Code.INVALID_ARGUMENT,\n      'Incorrect type for port passed in!'\n    );\n  }\n  return `${baseUrl}/v1/projects/${project}/locations/${location}/services/${service}/connectors/${connector}`;\n}\nexport function addToken(url: string, apiKey?: string): string {\n  if (!apiKey) {\n    return url;\n  }\n  const newUrl = new URL(url);\n  newUrl.searchParams.append('key', apiKey);\n  return newUrl.toString();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isCloudWorkstation } from '@firebase/util';\n\nimport {\n  Code,\n  DataConnectError,\n  DataConnectOperationError,\n  DataConnectOperationFailureResponse\n} from '../core/error';\nimport { SDK_VERSION } from '../core/version';\nimport { logError } from '../logger';\n\nimport { CallerSdkType, CallerSdkTypeEnum } from './transport';\n\nlet connectFetch: typeof fetch | null = globalThis.fetch;\nexport function initializeFetch(fetchImpl: typeof fetch): void {\n  connectFetch = fetchImpl;\n}\nfunction getGoogApiClientValue(\n  _isUsingGen: boolean,\n  _callerSdkType: CallerSdkType\n): string {\n  let str = 'gl-js/ fire/' + SDK_VERSION;\n  if (\n    _callerSdkType !== CallerSdkTypeEnum.Base &&\n    _callerSdkType !== CallerSdkTypeEnum.Generated\n  ) {\n    str += ' js/' + _callerSdkType.toLowerCase();\n  } else if (_isUsingGen || _callerSdkType === CallerSdkTypeEnum.Generated) {\n    str += ' js/gen';\n  }\n  return str;\n}\nexport interface DataConnectFetchBody<T> {\n  name: string;\n  operationName: string;\n  variables: T;\n}\nexport function dcFetch<T, U>(\n  url: string,\n  body: DataConnectFetchBody<U>,\n  { signal }: AbortController,\n  appId: string | null,\n  accessToken: string | null,\n  appCheckToken: string | null,\n  _isUsingGen: boolean,\n  _callerSdkType: CallerSdkType,\n  _isUsingEmulator: boolean\n): Promise<{ data: T; errors: Error[] }> {\n  if (!connectFetch) {\n    throw new DataConnectError(Code.OTHER, 'No Fetch Implementation detected!');\n  }\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'X-Goog-Api-Client': getGoogApiClientValue(_isUsingGen, _callerSdkType)\n  };\n  if (accessToken) {\n    headers['X-Firebase-Auth-Token'] = accessToken;\n  }\n  if (appId) {\n    headers['x-firebase-gmpid'] = appId;\n  }\n  if (appCheckToken) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n  const bodyStr = JSON.stringify(body);\n  const fetchOptions: RequestInit = {\n    body: bodyStr,\n    method: 'POST',\n    headers,\n    signal\n  };\n  if (isCloudWorkstation(url) && _isUsingEmulator) {\n    fetchOptions.credentials = 'include';\n  }\n\n  return connectFetch(url, fetchOptions)\n    .catch(err => {\n      throw new DataConnectError(\n        Code.OTHER,\n        'Failed to fetch: ' + JSON.stringify(err)\n      );\n    })\n    .then(async response => {\n      let jsonResponse = null;\n      try {\n        jsonResponse = await response.json();\n      } catch (e) {\n        throw new DataConnectError(Code.OTHER, JSON.stringify(e));\n      }\n      const message = getMessage(jsonResponse);\n      if (response.status >= 400) {\n        logError(\n          'Error while performing request: ' + JSON.stringify(jsonResponse)\n        );\n        if (response.status === 401) {\n          throw new DataConnectError(Code.UNAUTHORIZED, message);\n        }\n        throw new DataConnectError(Code.OTHER, message);\n      }\n      return jsonResponse;\n    })\n    .then(res => {\n      if (res.errors && res.errors.length) {\n        const stringified = JSON.stringify(res.errors);\n        const response: DataConnectOperationFailureResponse = {\n          errors: res.errors,\n          data: res.data\n        };\n        throw new DataConnectOperationError(\n          'DataConnect error while performing request: ' + stringified,\n          response\n        );\n      }\n      return res;\n    });\n}\ninterface MessageObject {\n  message?: string;\n}\nfunction getMessage(obj: MessageObject): string {\n  if ('message' in obj) {\n    return obj.message;\n  }\n  return JSON.stringify(obj);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { DataConnectError, Code } from '../../core/error';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\nimport { logDebug } from '../../logger';\nimport { addToken, urlBuilder } from '../../util/url';\nimport { dcFetch } from '../fetch';\n\nimport { CallerSdkType, CallerSdkTypeEnum, DataConnectTransport } from '.';\n\nexport class RESTTransport implements DataConnectTransport {\n  private _host = '';\n  private _port: number | undefined;\n  private _location = 'l';\n  private _connectorName = '';\n  private _secure = true;\n  private _project = 'p';\n  private _serviceName: string;\n  private _accessToken: string | null = null;\n  private _appCheckToken: string | null = null;\n  private _lastToken: string | null = null;\n  private _isUsingEmulator = false;\n  constructor(\n    options: DataConnectOptions,\n    private apiKey?: string | undefined,\n    private appId?: string,\n    private authProvider?: AuthTokenProvider | undefined,\n    private appCheckProvider?: AppCheckTokenProvider | undefined,\n    transportOptions?: TransportOptions | undefined,\n    private _isUsingGen = false,\n    private _callerSdkType: CallerSdkType = CallerSdkTypeEnum.Base\n  ) {\n    if (transportOptions) {\n      if (typeof transportOptions.port === 'number') {\n        this._port = transportOptions.port;\n      }\n      if (typeof transportOptions.sslEnabled !== 'undefined') {\n        this._secure = transportOptions.sslEnabled;\n      }\n      this._host = transportOptions.host;\n    }\n    const { location, projectId: project, connector, service } = options;\n    if (location) {\n      this._location = location;\n    }\n    if (project) {\n      this._project = project;\n    }\n    this._serviceName = service;\n    if (!connector) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        'Connector Name required!'\n      );\n    }\n    this._connectorName = connector;\n    this.authProvider?.addTokenChangeListener(token => {\n      logDebug(`New Token Available: ${token}`);\n      this._accessToken = token;\n    });\n    this.appCheckProvider?.addTokenChangeListener(result => {\n      const { token } = result;\n      logDebug(`New App Check Token Available: ${token}`);\n      this._appCheckToken = token;\n    });\n  }\n  get endpointUrl(): string {\n    return urlBuilder(\n      {\n        connector: this._connectorName,\n        location: this._location,\n        projectId: this._project,\n        service: this._serviceName\n      },\n      { host: this._host, sslEnabled: this._secure, port: this._port }\n    );\n  }\n  useEmulator(host: string, port?: number, isSecure?: boolean): void {\n    this._host = host;\n    this._isUsingEmulator = true;\n    if (typeof port === 'number') {\n      this._port = port;\n    }\n    if (typeof isSecure !== 'undefined') {\n      this._secure = isSecure;\n    }\n  }\n  onTokenChanged(newToken: string | null): void {\n    this._accessToken = newToken;\n  }\n\n  async getWithAuth(forceToken = false): Promise<string> {\n    let starterPromise: Promise<string | null> = new Promise(resolve =>\n      resolve(this._accessToken)\n    );\n    if (this.appCheckProvider) {\n      this._appCheckToken = (await this.appCheckProvider.getToken())?.token;\n    }\n    if (this.authProvider) {\n      starterPromise = this.authProvider\n        .getToken(/*forceToken=*/ forceToken)\n        .then(data => {\n          if (!data) {\n            return null;\n          }\n          this._accessToken = data.accessToken;\n          return this._accessToken;\n        });\n    } else {\n      starterPromise = new Promise(resolve => resolve(''));\n    }\n    return starterPromise;\n  }\n\n  _setLastToken(lastToken: string | null): void {\n    this._lastToken = lastToken;\n  }\n\n  withRetry<T>(\n    promiseFactory: () => Promise<{ data: T; errors: Error[] }>,\n    retry = false\n  ): Promise<{ data: T; errors: Error[] }> {\n    let isNewToken = false;\n    return this.getWithAuth(retry)\n      .then(res => {\n        isNewToken = this._lastToken !== res;\n        this._lastToken = res;\n        return res;\n      })\n      .then(promiseFactory)\n      .catch(err => {\n        // Only retry if the result is unauthorized and the last token isn't the same as the new one.\n        if (\n          'code' in err &&\n          err.code === Code.UNAUTHORIZED &&\n          !retry &&\n          isNewToken\n        ) {\n          logDebug('Retrying due to unauthorized');\n          return this.withRetry(promiseFactory, true);\n        }\n        throw err;\n      });\n  }\n\n  // TODO(mtewani): Update U to include shape of body defined in line 13.\n  invokeQuery: <T, U>(\n    queryName: string,\n    body?: U\n  ) => Promise<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    queryName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n\n    // TODO(mtewani): Update to proper value\n    const withAuth = this.withRetry(() =>\n      dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeQuery`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: queryName,\n          variables: body\n        },\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen,\n        this._callerSdkType,\n        this._isUsingEmulator\n      )\n    );\n    return withAuth;\n  };\n  invokeMutation: <T, U>(\n    queryName: string,\n    body?: U\n  ) => Promise<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    mutationName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    const taskResult = this.withRetry(() => {\n      return dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeMutation`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: mutationName,\n          variables: body\n        },\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen,\n        this._callerSdkType,\n        this._isUsingEmulator\n      );\n    });\n    return taskResult;\n  };\n\n  _setCallerSdkType(callerSdkType: CallerSdkType): void {\n    this._callerSdkType = callerSdkType;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectTransport } from '../network/transport';\n\nimport { DataConnect } from './DataConnect';\nimport {\n  DataConnectResult,\n  MUTATION_STR,\n  OperationRef,\n  SOURCE_SERVER\n} from './Reference';\n\nexport interface MutationRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof MUTATION_STR;\n}\n\n/**\n * Creates a `MutationRef`\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n */\nexport function mutationRef<Data>(\n  dcInstance: DataConnect,\n  mutationName: string\n): MutationRef<Data, undefined>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables: Variables\n): MutationRef<Data, Variables>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n * @returns `MutationRef`\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables?: Variables\n): MutationRef<Data, Variables> {\n  dcInstance.setInitialized();\n  const ref: MutationRef<Data, Variables> = {\n    dataConnect: dcInstance,\n    name: mutationName,\n    refType: MUTATION_STR,\n    variables: variables as Variables\n  };\n  return ref;\n}\n\n/**\n * @internal\n */\nexport class MutationManager {\n  private _inflight: Array<Promise<unknown>> = [];\n  constructor(private _transport: DataConnectTransport) {}\n  executeMutation<Data, Variables>(\n    mutationRef: MutationRef<Data, Variables>\n  ): MutationPromise<Data, Variables> {\n    const result = this._transport.invokeMutation<Data, Variables>(\n      mutationRef.name,\n      mutationRef.variables\n    );\n    const withRefPromise = result.then(res => {\n      const obj: MutationResult<Data, Variables> = {\n        ...res, // Double check that the result is result.data, not just result\n        source: SOURCE_SERVER,\n        ref: mutationRef,\n        fetchTime: Date.now().toLocaleString()\n      };\n      return obj;\n    });\n    this._inflight.push(result);\n    const removePromise = (): Array<Promise<unknown>> =>\n      (this._inflight = this._inflight.filter(promise => promise !== result));\n    result.then(removePromise, removePromise);\n    return withRefPromise;\n  }\n}\n\n/**\n * Mutation Result from `executeMutation`\n */\nexport interface MutationResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: MutationRef<Data, Variables>;\n}\n/**\n * Mutation return value from `executeMutation`\n */\nexport interface MutationPromise<Data, Variables>\n  extends Promise<MutationResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Mutation\n * @param mutationRef mutation to execute\n * @returns `MutationRef`\n */\nexport function executeMutation<Data, Variables>(\n  mutationRef: MutationRef<Data, Variables>\n): MutationPromise<Data, Variables> {\n  return mutationRef.dataConnect._mutationManager.executeMutation(mutationRef);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  _getProvider,\n  _removeServiceInstance,\n  getApp\n} from '@firebase/app';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\n\nimport { AppCheckTokenProvider } from '../core/AppCheckTokenProvider';\nimport { Code, DataConnectError } from '../core/error';\nimport {\n  AuthTokenProvider,\n  FirebaseAuthProvider\n} from '../core/FirebaseAuthProvider';\nimport { QueryManager } from '../core/QueryManager';\nimport { logDebug, logError } from '../logger';\nimport {\n  CallerSdkType,\n  CallerSdkTypeEnum,\n  DataConnectTransport,\n  TransportClass\n} from '../network';\nimport { RESTTransport } from '../network/transport/rest';\n\nimport { MutationManager } from './Mutation';\n\n/**\n * Connector Config for calling Data Connect backend.\n */\nexport interface ConnectorConfig {\n  location: string;\n  connector: string;\n  service: string;\n}\n\n/**\n * Options to connect to emulator\n */\nexport interface TransportOptions {\n  host: string;\n  sslEnabled?: boolean;\n  port?: number;\n}\n\nconst FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR =\n  'FIREBASE_DATA_CONNECT_EMULATOR_HOST';\n\n/**\n *\n * @param fullHost\n * @returns TransportOptions\n * @internal\n */\nexport function parseOptions(fullHost: string): TransportOptions {\n  const [protocol, hostName] = fullHost.split('://');\n  const isSecure = protocol === 'https';\n  const [host, portAsString] = hostName.split(':');\n  const port = Number(portAsString);\n  return { host, port, sslEnabled: isSecure };\n}\n/**\n * DataConnectOptions including project id\n */\nexport interface DataConnectOptions extends ConnectorConfig {\n  projectId: string;\n}\n\n/**\n * Class representing Firebase Data Connect\n */\nexport class DataConnect {\n  _queryManager!: QueryManager;\n  _mutationManager!: MutationManager;\n  isEmulator = false;\n  _initialized = false;\n  private _transport!: DataConnectTransport;\n  private _transportClass: TransportClass | undefined;\n  private _transportOptions?: TransportOptions;\n  private _authTokenProvider?: AuthTokenProvider;\n  _isUsingGeneratedSdk: boolean = false;\n  _callerSdkType: CallerSdkType = CallerSdkTypeEnum.Base;\n  private _appCheckTokenProvider?: AppCheckTokenProvider;\n  // @internal\n  constructor(\n    public readonly app: FirebaseApp,\n    // TODO(mtewani): Replace with _dataConnectOptions in the future\n    private readonly dataConnectOptions: DataConnectOptions,\n    private readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    private readonly _appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (typeof process !== 'undefined' && process.env) {\n      const host = process.env[FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR];\n      if (host) {\n        logDebug('Found custom host. Using emulator');\n        this.isEmulator = true;\n        this._transportOptions = parseOptions(host);\n      }\n    }\n  }\n  // @internal\n  _useGeneratedSdk(): void {\n    if (!this._isUsingGeneratedSdk) {\n      this._isUsingGeneratedSdk = true;\n    }\n  }\n  _setCallerSdkType(callerSdkType: CallerSdkType): void {\n    this._callerSdkType = callerSdkType;\n    if (this._initialized) {\n      this._transport._setCallerSdkType(callerSdkType);\n    }\n  }\n  _delete(): Promise<void> {\n    _removeServiceInstance(\n      this.app,\n      'data-connect',\n      JSON.stringify(this.getSettings())\n    );\n    return Promise.resolve();\n  }\n\n  // @internal\n  getSettings(): ConnectorConfig {\n    const copy = JSON.parse(JSON.stringify(this.dataConnectOptions));\n    delete copy.projectId;\n    return copy;\n  }\n\n  // @internal\n  setInitialized(): void {\n    if (this._initialized) {\n      return;\n    }\n    if (this._transportClass === undefined) {\n      logDebug('transportClass not provided. Defaulting to RESTTransport.');\n      this._transportClass = RESTTransport;\n    }\n\n    if (this._authProvider) {\n      this._authTokenProvider = new FirebaseAuthProvider(\n        this.app.name,\n        this.app.options,\n        this._authProvider\n      );\n    }\n    if (this._appCheckProvider) {\n      this._appCheckTokenProvider = new AppCheckTokenProvider(\n        this.app,\n        this._appCheckProvider\n      );\n    }\n\n    this._initialized = true;\n    this._transport = new this._transportClass(\n      this.dataConnectOptions,\n      this.app.options.apiKey,\n      this.app.options.appId,\n      this._authTokenProvider,\n      this._appCheckTokenProvider,\n      undefined,\n      this._isUsingGeneratedSdk,\n      this._callerSdkType\n    );\n    if (this._transportOptions) {\n      this._transport.useEmulator(\n        this._transportOptions.host,\n        this._transportOptions.port,\n        this._transportOptions.sslEnabled\n      );\n    }\n    this._queryManager = new QueryManager(this._transport);\n    this._mutationManager = new MutationManager(this._transport);\n  }\n\n  // @internal\n  enableEmulator(transportOptions: TransportOptions): void {\n    if (\n      this._initialized &&\n      !areTransportOptionsEqual(this._transportOptions, transportOptions)\n    ) {\n      logError('enableEmulator called after initialization');\n      throw new DataConnectError(\n        Code.ALREADY_INITIALIZED,\n        'DataConnect instance already initialized!'\n      );\n    }\n    this._transportOptions = transportOptions;\n    this.isEmulator = true;\n  }\n}\n\n/**\n * @internal\n * @param transportOptions1\n * @param transportOptions2\n * @returns\n */\nexport function areTransportOptionsEqual(\n  transportOptions1: TransportOptions,\n  transportOptions2: TransportOptions\n): boolean {\n  return (\n    transportOptions1.host === transportOptions2.host &&\n    transportOptions1.port === transportOptions2.port &&\n    transportOptions1.sslEnabled === transportOptions2.sslEnabled\n  );\n}\n\n/**\n * Connect to the DataConnect Emulator\n * @param dc Data Connect instance\n * @param host host of emulator server\n * @param port port of emulator server\n * @param sslEnabled use https\n */\nexport function connectDataConnectEmulator(\n  dc: DataConnect,\n  host: string,\n  port?: number,\n  sslEnabled = false\n): void {\n  // Workaround to get cookies in Firebase Studio\n  if (isCloudWorkstation(host)) {\n    void pingServer(`https://${host}${port ? `:${port}` : ''}`);\n    updateEmulatorBanner('Data Connect', true);\n  }\n  dc.enableEmulator({ host, port, sslEnabled });\n}\n\n/**\n * Initialize DataConnect instance\n * @param options ConnectorConfig\n */\nexport function getDataConnect(options: ConnectorConfig): DataConnect;\n/**\n * Initialize DataConnect instance\n * @param app FirebaseApp to initialize to.\n * @param options ConnectorConfig\n */\nexport function getDataConnect(\n  app: FirebaseApp,\n  options: ConnectorConfig\n): DataConnect;\nexport function getDataConnect(\n  appOrOptions: FirebaseApp | ConnectorConfig,\n  optionalOptions?: ConnectorConfig\n): DataConnect {\n  let app: FirebaseApp;\n  let dcOptions: ConnectorConfig;\n  if ('location' in appOrOptions) {\n    dcOptions = appOrOptions;\n    app = getApp();\n  } else {\n    dcOptions = optionalOptions!;\n    app = appOrOptions;\n  }\n\n  if (!app || Object.keys(app).length === 0) {\n    app = getApp();\n  }\n  const provider = _getProvider(app, 'data-connect');\n  const identifier = JSON.stringify(dcOptions);\n  if (provider.isInitialized(identifier)) {\n    const dcInstance = provider.getImmediate({ identifier });\n    const options = provider.getOptions(identifier);\n    const optionsValid = Object.keys(options).length > 0;\n    if (optionsValid) {\n      logDebug('Re-using cached instance');\n      return dcInstance;\n    }\n  }\n  validateDCOptions(dcOptions);\n\n  logDebug('Creating new DataConnect instance');\n  // Initialize with options.\n  return provider.initialize({\n    instanceIdentifier: identifier,\n    options: dcOptions\n  });\n}\n\n/**\n *\n * @param dcOptions\n * @returns {void}\n * @internal\n */\nexport function validateDCOptions(dcOptions: ConnectorConfig): boolean {\n  const fields = ['connector', 'location', 'service'];\n  if (!dcOptions) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'DC Option Required');\n  }\n  fields.forEach(field => {\n    if (dcOptions[field] === null || dcOptions[field] === undefined) {\n      throw new DataConnectError(Code.INVALID_ARGUMENT, `${field} Required`);\n    }\n  });\n  return true;\n}\n\n/**\n * Delete DataConnect instance\n * @param dataConnect DataConnect instance\n * @returns\n */\nexport function terminate(dataConnect: DataConnect): Promise<void> {\n  return dataConnect._delete();\n  // TODO(mtewani): Stop pending tasks\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectError } from '../core/error';\n\nimport { DataConnect, getDataConnect } from './DataConnect';\nimport {\n  OperationRef,\n  QUERY_STR,\n  DataConnectResult,\n  SerializedRef\n} from './Reference';\n\n/**\n * Signature for `OnResultSubscription` for `subscribe`\n */\nexport type OnResultSubscription<Data, Variables> = (\n  res: QueryResult<Data, Variables>\n) => void;\n/**\n * Signature for `OnErrorSubscription` for `subscribe`\n */\nexport type OnErrorSubscription = (err?: DataConnectError) => void;\n/**\n * Signature for unsubscribe from `subscribe`\n */\nexport type QueryUnsubscribe = () => void;\n/**\n * Representation of user provided subscription options.\n */\nexport interface DataConnectSubscription<Data, Variables> {\n  userCallback: OnResultSubscription<Data, Variables>;\n  errCallback?: (e?: DataConnectError) => void;\n  unsubscribe: () => void;\n}\n\n/**\n * QueryRef object\n */\nexport interface QueryRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof QUERY_STR;\n}\n/**\n * Result of `executeQuery`\n */\nexport interface QueryResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: QueryRef<Data, Variables>;\n  toJSON: () => SerializedRef<Data, Variables>;\n}\n/**\n * Promise returned from `executeQuery`\n */\nexport interface QueryPromise<Data, Variables>\n  extends Promise<QueryResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Query\n * @param queryRef query to execute.\n * @returns `QueryPromise`\n */\nexport function executeQuery<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>\n): QueryPromise<Data, Variables> {\n  return queryRef.dataConnect._queryManager.executeQuery(queryRef);\n}\n\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @returns `QueryRef`\n */\nexport function queryRef<Data>(\n  dcInstance: DataConnect,\n  queryName: string\n): QueryRef<Data, undefined>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables: Variables\n): QueryRef<Data, Variables>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @param initialCache initial cache to use for client hydration\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables?: Variables,\n  initialCache?: QueryResult<Data, Variables>\n): QueryRef<Data, Variables> {\n  dcInstance.setInitialized();\n  dcInstance._queryManager.track(queryName, variables, initialCache);\n  return {\n    dataConnect: dcInstance,\n    refType: QUERY_STR,\n    name: queryName,\n    variables\n  };\n}\n/**\n * Converts serialized ref to query ref\n * @param serializedRef ref to convert to `QueryRef`\n * @returns `QueryRef`\n */\nexport function toQueryRef<Data, Variables>(\n  serializedRef: SerializedRef<Data, Variables>\n): QueryRef<Data, Variables> {\n  const {\n    refInfo: { name, variables, connectorConfig }\n  } = serializedRef;\n  return queryRef(getDataConnect(connectorConfig), name, variables);\n}\n/**\n * `OnCompleteSubscription`\n */\nexport type OnCompleteSubscription = () => void;\n/**\n * Representation of full observer options in `subscribe`\n */\nexport interface SubscriptionOptions<Data, Variables> {\n  onNext?: OnResultSubscription<Data, Variables>;\n  onErr?: OnErrorSubscription;\n  onComplete?: OnCompleteSubscription;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ConnectorConfig,\n  DataConnect,\n  getDataConnect\n} from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\ninterface ParsedArgs<Variables> {\n  dc: DataConnect;\n  vars: Variables;\n}\n\n/**\n * The generated SDK will allow the user to pass in either the variable or the data connect instance with the variable,\n * and this function validates the variables and returns back the DataConnect instance and variables based on the arguments passed in.\n * @param connectorConfig\n * @param dcOrVars\n * @param vars\n * @param validateVars\n * @returns {DataConnect} and {Variables} instance\n * @internal\n */\nexport function validateArgs<Variables extends object>(\n  connectorConfig: ConnectorConfig,\n  dcOrVars?: DataConnect | Variables,\n  vars?: Variables,\n  validateVars?: boolean\n): ParsedArgs<Variables> {\n  let dcInstance: DataConnect;\n  let realVars: Variables;\n  if (dcOrVars && 'enableEmulator' in dcOrVars) {\n    dcInstance = dcOrVars as DataConnect;\n    realVars = vars;\n  } else {\n    dcInstance = getDataConnect(connectorConfig);\n    realVars = dcOrVars as Variables;\n  }\n  if (!dcInstance || (!realVars && validateVars)) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Variables required.');\n  }\n  return { dc: dcInstance, vars: realVars };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OnCompleteSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryRef,\n  QueryUnsubscribe,\n  SubscriptionOptions,\n  toQueryRef\n} from './api/query';\nimport { OpResult, SerializedRef } from './api/Reference';\nimport { DataConnectError, Code } from './core/error';\n\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observer observer object to use for subscribing.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observer: SubscriptionOptions<Data, Variables>\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param onNext Callback to call when result comes back.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  onNext: OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observerOrOnNext observer object or next function.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observerOrOnNext:\n    | SubscriptionOptions<Data, Variables>\n    | OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe {\n  let ref: QueryRef<Data, Variables>;\n  let initialCache: OpResult<Data> | undefined;\n  if ('refInfo' in queryRefOrSerializedResult) {\n    const serializedRef: SerializedRef<Data, Variables> =\n      queryRefOrSerializedResult;\n    const { data, source, fetchTime } = serializedRef;\n    initialCache = {\n      data,\n      source,\n      fetchTime\n    };\n    ref = toQueryRef(serializedRef);\n  } else {\n    ref = queryRefOrSerializedResult;\n  }\n  let onResult: OnResultSubscription<Data, Variables> | undefined = undefined;\n  if (typeof observerOrOnNext === 'function') {\n    onResult = observerOrOnNext;\n  } else {\n    onResult = observerOrOnNext.onNext;\n    onError = observerOrOnNext.onErr;\n    onComplete = observerOrOnNext.onComplete;\n  }\n  if (!onResult) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Must provide onNext');\n  }\n  return ref.dataConnect._queryManager.addSubscription(\n    ref,\n    onResult,\n    onError,\n    initialCache\n  );\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { Component, ComponentType } from '@firebase/component';\n\nimport { name, version } from '../package.json';\nimport { setSDKVersion } from '../src/core/version';\n\nimport { DataConnect, ConnectorConfig } from './api/DataConnect';\nimport { Code, DataConnectError } from './core/error';\n\nexport function registerDataConnect(variant?: string): void {\n  setSDKVersion(SDK_VERSION);\n  _registerComponent(\n    new Component(\n      'data-connect',\n      (container, { instanceIdentifier: settings, options }) => {\n        const app = container.getProvider('app').getImmediate()!;\n        const authProvider = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        let newOpts = options as ConnectorConfig;\n        if (settings) {\n          newOpts = JSON.parse(settings);\n        }\n        if (!app.options.projectId) {\n          throw new DataConnectError(\n            Code.INVALID_ARGUMENT,\n            'Project ID must be provided. Did you pass in a proper projectId to initializeApp?'\n          );\n        }\n        return new DataConnect(\n          app,\n          { ...newOpts, projectId: app.options.projectId! },\n          authProvider,\n          appCheckProvider\n        );\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * Firebase Data Connect\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DataConnect } from './api/DataConnect';\nimport { registerDataConnect } from './register';\n\nexport * from './api';\nexport * from './api.browser';\n\nregisterDataConnect();\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'data-connect': DataConnect;\n  }\n}\n"], "names": ["isCloudWorkstation", "host", "endsWith", "emulatorStatus", "previouslyDismissed", "updateEmulatorBanner", "name", "isRunningEmulator", "window", "document", "location", "prefixedId", "id", "bannerId", "showError", "getEmulatorSummary", "summary", "prod", "emulator", "key", "Object", "keys", "push", "length", "setupCloseBtn", "closeBtn", "createElement", "style", "cursor", "marginLeft", "fontSize", "innerHTML", "onclick", "tearDown", "element", "getElementById", "remove", "setupDom", "banner", "getOrCreateEl", "parentDiv", "created", "setAttribute", "firebaseTextId", "firebaseText", "learnMoreId", "learnMoreLink", "prependIconId", "prependIcon", "createElementNS", "bannerEl", "setupBannerStyles", "display", "background", "position", "bottom", "left", "padding", "borderRadius", "alignItems", "setupLinkStyles", "innerText", "href", "paddingLeft", "textDecoration", "setupIconStyles", "iconId", "append", "body", "append<PERSON><PERSON><PERSON>", "readyState", "addEventListener", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "value", "String", "fullMessage", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "SDK_VERSION", "AppCheckTokenProvider", "app", "appCheckProvider", "_isFirebaseServerApp", "settings", "appCheckToken", "serverAppAppCheckToken", "appCheck", "getImmediate", "optional", "get", "then", "catch", "getToken", "Promise", "resolve", "token", "reject", "setTimeout", "addTokenChangeListener", "listener", "_a", "addTokenListener", "Code", "OTHER", "ALREADY_INITIALIZED", "NOT_INITIALIZED", "NOT_SUPPORTED", "INVALID_ARGUMENT", "PARTIAL_ERROR", "UNAUTHORIZED", "DataConnectError", "toString", "DataConnectOperationError", "response", "logger", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "logDebug", "msg", "logError", "FirebaseAuthProvider", "_appName", "_options", "_authProvider", "_auth", "onInit", "auth", "forceRefresh", "JSON", "stringify", "addAuthTokenListener", "removeTokenChangeListener", "removeAuthTokenListener", "err", "QUERY_STR", "MUTATION_STR", "SOURCE_SERVER", "SOURCE_CACHE", "encoderImpl", "getRefSerializer", "queryRef", "source", "toJSON", "refInfo", "variables", "connectorConfig", "projectId", "dataConnect", "options", "getSettings", "fetchTime", "toLocaleString", "<PERSON><PERSON><PERSON><PERSON>", "encoder", "o", "QueryManager", "transport", "_queries", "Map", "track", "queryName", "initialCache", "ref", "refType", "newTrackedQuery", "subscriptions", "currentCache", "lastError", "setIfNotExists", "map", "has", "set", "addSubscription", "onResultCallback", "onError<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "subscription", "userCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsubscribe", "filter", "sub", "compareDates", "str1", "str2", "date1", "date2", "getTime", "undefined", "execute<PERSON>uery", "invoke<PERSON><PERSON>y", "res", "result", "assign", "for<PERSON>ach", "enableEmulator", "port", "useEmulator", "CallerSdkTypeEnum", "Base", "Generated", "TanstackReactCore", "GeneratedReact", "TanstackAngularCore", "GeneratedAngular", "addToken", "url", "<PERSON><PERSON><PERSON><PERSON>", "newUrl", "URL", "searchParams", "connectFetch", "globalThis", "fetch", "getGoogApiClientValue", "_isUsingGen", "_callerSdkType", "str", "toLowerCase", "dcFetch", "signal", "appId", "accessToken", "_isUsingEmulator", "headers", "fetchOptions", "credentials", "async", "jsonResponse", "json", "e", "getMessage", "obj", "status", "stringified", "RESTTransport", "authProvider", "transportOptions", "_host", "_location", "_connectorName", "_secure", "_project", "_accessToken", "_appCheckToken", "_lastToken", "abortController", "AbortController", "withRetry", "endpointUrl", "_serviceName", "operationName", "invokeMutation", "mutationName", "_port", "sslEnabled", "project", "connector", "_b", "urlBuilder", "projectConfig", "baseUrl", "isSecure", "onTokenChanged", "newToken", "getWithAuth", "forceToken", "<PERSON><PERSON><PERSON><PERSON>", "_setLastToken", "lastToken", "promiseFactory", "retry", "isNewToken", "_setCallerSdkType", "callerSdkType", "mutationRef", "dcInstance", "setInitialized", "MutationManager", "_transport", "_inflight", "executeMutation", "withRefPromise", "removePromise", "promise", "_mutationManager", "parseOptions", "fullHost", "protocol", "hostName", "split", "portAsString", "Number", "DataConnect", "dataConnectOptions", "_appCheckProvider", "isEmulator", "_initialized", "_isUsingGeneratedSdk", "process", "env", "_transportOptions", "_useGeneratedSdk", "_delete", "_removeServiceInstance", "copy", "parse", "_transportClass", "_authTokenProvider", "_appCheckTokenProvider", "_queryManager", "areTransportOptionsEqual", "transportOptions1", "transportOptions2", "connectDataConnectEmulator", "dc", "pingServer", "endpoint", "ok", "getDataConnect", "appOrOptions", "optionalOptions", "dcOptions", "getApp", "provider", "_get<PERSON><PERSON><PERSON>", "identifier", "isInitialized", "getOptions", "validateDCOptions", "initialize", "instanceIdentifier", "field", "terminate", "toQueryRef", "serializedRef", "validateArgs", "dcOrVars", "vars", "validateVars", "realVars", "subscribe", "queryRefOrSerializedResult", "observerOrOnNext", "onError", "onComplete", "onResult", "onNext", "onErr", "registerDataConnect", "variant", "setSDKVersion", "version", "_registerComponent", "container", "get<PERSON><PERSON><PERSON>", "newOpts", "registerVersion"], "mappings": "wKAqBM,SAAUA,mBAAmBC,GACjC,OAAOA,EAAKC,SAAS,yBACvB,CC4HA,MAAMC,EAAoC,CAAA,EAkC1C,IAAIC,GAAsB,EAOV,SAAAC,qBACdC,EACAC,GAEA,GACoB,oBAAXC,QACa,oBAAbC,WACNT,mBAAmBQ,OAAOE,SAAST,OACpCE,EAAeG,KAAUC,GACzBJ,EAAeG,IACfF,EAEA,OAKF,SAASO,WAAWC,GAClB,MAAO,uBAAuBA,GAC/B,CAJDT,EAAeG,GAAQC,EAKvB,MAAMM,EAAW,qBAEXC,EAvDR,SAASC,qBACP,MAAMC,EAA2B,CAC/BC,KAAM,GACNC,SAAU,IAEZ,IAAK,MAAMC,KAAOC,OAAOC,KAAKlB,GACxBA,EAAegB,GACjBH,EAAQE,SAASI,KAAKH,GAEtBH,EAAQC,KAAKK,KAAKH,GAGtB,OAAOH,CACT,CAyCkBD,GACUE,KAAKM,OAAS,EA6BxC,SAASC,gBACP,MAAMC,EAAWhB,SAASiB,cAAc,QASxC,OARAD,EAASE,MAAMC,OAAS,UACxBH,EAASE,MAAME,WAAa,OAC5BJ,EAASE,MAAMG,SAAW,OAC1BL,EAASM,UAAY,WACrBN,EAASO,QAAU,KACjB5B,GAAsB,EAlC1B,SAAS6B,WACP,MAAMC,EAAUzB,SAAS0B,eAAetB,GACpCqB,GACFA,EAAQE,QAEX,CA8BGH,EAAU,EAELR,CACR,CAeD,SAASY,WACP,MAAMC,EAhGV,SAASC,cAAc3B,GACrB,IAAI4B,EAAY/B,SAAS0B,eAAevB,GACpC6B,GAAU,EAMd,OALKD,IACHA,EAAY/B,SAASiB,cAAc,OACnCc,EAAUE,aAAa,KAAM9B,GAC7B6B,GAAU,GAEL,CAAEA,UAASP,QAASM,EAC7B,CAuFmBD,CAAc1B,GACvB8B,EAAiBhC,WAAW,QAC5BiC,EACJnC,SAAS0B,eAAeQ,IAAmBlC,SAASiB,cAAc,QAC9DmB,EAAclC,WAAW,aACzBmC,EACHrC,SAAS0B,eAAeU,IACzBpC,SAASiB,cAAc,KACnBqB,EAAgBpC,WAAW,gBAC3BqC,EACHvC,SAAS0B,eACRY,IAEFtC,SAASwC,gBAAgB,6BAA8B,OACzD,GAAIX,EAAOG,QAAS,CAElB,MAAMS,EAAWZ,EAAOJ,SA/D5B,SAASiB,kBAAkBD,GACzBA,EAASvB,MAAMyB,QAAU,OACzBF,EAASvB,MAAM0B,WAAa,UAC5BH,EAASvB,MAAM2B,SAAW,QAC1BJ,EAASvB,MAAM4B,OAAS,MACxBL,EAASvB,MAAM6B,KAAO,MACtBN,EAASvB,MAAM8B,QAAU,OACzBP,EAASvB,MAAM+B,aAAe,MAC9BR,EAASvB,MAAMgC,WAAa,QAC7B,CAuDGR,CAAkBD,GA/BtB,SAASU,gBACPd,EACAD,GAEAC,EAAcJ,aAAa,KAAMG,GACjCC,EAAce,UAAY,aAC1Bf,EAAcgB,KACZ,uEACFhB,EAAcJ,aAAa,SAAU,WACrCI,EAAcnB,MAAMoC,YAAc,MAClCjB,EAAcnB,MAAMqC,eAAiB,WACtC,CAqBGJ,CAAgBd,EAAeD,GAC/B,MAAMpB,EAAWD,iBAvDrB,SAASyC,gBAAgBjB,EAAyBkB,GAChDlB,EAAYN,aAAa,QAAS,MAClCM,EAAYN,aAAa,KAAMwB,GAC/BlB,EAAYN,aAAa,SAAU,MACnCM,EAAYN,aAAa,UAAW,aACpCM,EAAYN,aAAa,OAAQ,QACjCM,EAAYrB,MAAME,WAAa,MAChC,CAiDGoC,CAAgBjB,EAAaD,GAC7BG,EAASiB,OAAOnB,EAAaJ,EAAcE,EAAerB,GAC1DhB,SAAS2D,KAAKC,YAAYnB,EAC3B,CAEGpC,GACF8B,EAAaiB,UAAY,gCACzBb,EAAYjB,UAAY,wnBASxBiB,EAAYjB,UAAY,0hDAQxBa,EAAaiB,UAAY,8CAE3BjB,EAAaF,aAAa,KAAMC,EACjC,CAC2B,YAAxBlC,SAAS6D,WACX9D,OAAO+D,iBAAiB,mBAAoBlC,UAE5CA,UAEJ,CCtPM,MAAOmC,sBAAsBC,MAIjC,WAAAC,CAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIzE,KAdI,gBA6Bfc,OAAO4D,eAAeD,KAAMP,cAAcS,WAItCR,MAAMS,mBACRT,MAAMS,kBAAkBH,KAAMI,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAT,CACmBW,EACAC,EACAC,GAFAR,KAAOM,QAAPA,EACAN,KAAWO,YAAXA,EACAP,KAAMQ,OAANA,CACf,CAEJ,MAAAH,CACET,KACGa,GAEH,MAAMX,EAAcW,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGV,KAAKM,WAAWV,IAC9Be,EAAWX,KAAKQ,OAAOZ,GAEvBC,EAAUc,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAG3E,KACnC,MAAM4E,EAAQP,EAAKrE,GACnB,OAAgB,MAAT4E,EAAgBC,OAAOD,GAAS,IAAI5E,KAAO,GAEtD,CAf+BwE,CAAgBD,EAAUb,GAAc,QAE7DoB,EAAc,GAAGlB,KAAKO,gBAAgBV,MAAYa,MAIxD,OAFc,IAAIjB,cAAciB,EAAUQ,EAAapB,EAGxD,EAUH,MAAMgB,EAAU,gBC3GH,MAAAK,UAiBX,WAAAxB,CACWpE,EACA6F,EACAC,GAFArB,KAAIzE,KAAJA,EACAyE,KAAeoB,gBAAfA,EACApB,KAAIqB,KAAJA,EAnBXrB,KAAiBsB,mBAAG,EAIpBtB,KAAYuB,aAAe,GAE3BvB,KAAAwB,kBAA2C,OAE3CxB,KAAiByB,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADA3B,KAAKwB,kBAAoBG,EAClB3B,IACR,CAED,oBAAA4B,CAAqBN,GAEnB,OADAtB,KAAKsB,kBAAoBA,EAClBtB,IACR,CAED,eAAA6B,CAAgBC,GAEd,OADA9B,KAAKuB,aAAeO,EACb9B,IACR,CAED,0BAAA+B,CAA2BC,GAEzB,OADAhC,KAAKyB,kBAAoBO,EAClBhC,IACR,MCfSiC,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAOX,CAAA,IAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAI/D,MACR,8DAA8DyD,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAAS3H,WACnB6H,EAMN,6CCxGI,IAAIO,EAAc,GCYZ,MAAAC,sBAGX,WAAAjE,CACEkE,EACQC,GAAA9D,KAAgB8D,iBAAhBA,EAEJC,EAAqBF,IAAQA,EAAIG,SAASC,gBAC5CjE,KAAKkE,uBAAyBL,EAAIG,SAASC,eAE7CjE,KAAKmE,SAAWL,aAAA,EAAAA,EAAkBM,aAAa,CAAEC,UAAU,IACtDrE,KAAKmE,UACHL,SAAAA,EACDQ,MACDC,MAAKJ,GAAanE,KAAKmE,SAAWA,IAClCK,OAEN,CAED,QAAAC,GACE,OAAIzE,KAAKkE,uBACAQ,QAAQC,QAAQ,CAAEC,MAAO5E,KAAKkE,yBAGlClE,KAAKmE,SAeHnE,KAAKmE,SAASM,WAdZ,IAAIC,SAA6B,CAACC,EAASE,KAKhDC,YAAW,KACL9E,KAAKmE,SACPnE,KAAKyE,WAAWF,KAAKI,EAASE,GAE9BF,EAAQ,KACT,GACA,EAAE,GAIV,CAED,sBAAAI,CAAuBC,SAEjB,QADCC,EAAAjF,KAAK8D,wBACN,IAAAmB,GAAAA,EAAAX,MACDC,MAAKJ,GAAYA,EAASe,iBAAiBF,IAC/C,EC9CU,MAAAG,EAAO,CAClBC,MAAO,QACPC,oBAAqB,sBACrBC,gBAAiB,kBACjBC,cAAe,gBACfC,iBAAkB,mBAClBC,cAAe,gBACfC,aAAc,gBAIV,MAAOC,yBAAyBlG,cAIpC,WAAAE,CAAYC,EAAYC,GACtBE,MAAMH,EAAMC,GAHLG,KAAIzE,KAAW,mBAQtBc,OAAO4D,eAAeD,KAAM2F,iBAAiBzF,UAC9C,CAGD,QAAA0F,GACE,MAAO,GAAG5F,KAAKzE,aAAayE,KAAKJ,UAAUI,KAAKH,SACjD,EAIG,MAAOgG,kCAAkCF,iBAQ7C,WAAAhG,CAAYE,EAAiBiG,GAC3B/F,MAAMoF,EAAKM,cAAe5F,GAPnBG,KAAIzE,KAAW,4BAQtByE,KAAK8F,SAAWA,CACjB,ECpDH,MAAMC,EAAS,IJyGF,MAAAC,OAOX,WAAArG,CAAmBpE,GAAAyE,KAAIzE,KAAJA,EAUXyE,KAASiG,UAAGlD,EAsBZ/C,KAAWkG,YAAejD,kBAc1BjD,KAAemG,gBAAsB,IAzC5C,CAOD,YAAI9C,GACF,OAAOrD,KAAKiG,SACb,CAED,YAAI5C,CAAS+C,GACX,KAAMA,KAAOnE,GACX,MAAM,IAAIoE,UAAU,kBAAkBD,+BAExCpG,KAAKiG,UAAYG,CAClB,CAGD,WAAAE,CAAYF,GACVpG,KAAKiG,UAA2B,iBAARG,EAAmBlE,EAAkBkE,GAAOA,CACrE,CAOD,cAAIG,GACF,OAAOvG,KAAKkG,WACb,CACD,cAAIK,CAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtBrG,KAAKkG,YAAcE,CACpB,CAMD,kBAAII,GACF,OAAOxG,KAAKmG,eACb,CACD,kBAAIK,CAAeJ,GACjBpG,KAAKmG,gBAAkBC,CACxB,CAMD,KAAAjE,IAASiB,GACPpD,KAAKmG,iBAAmBnG,KAAKmG,gBAAgBnG,KAAMiC,EAASG,SAAUgB,GACtEpD,KAAKkG,YAAYlG,KAAMiC,EAASG,SAAUgB,EAC3C,CACD,GAAAqD,IAAOrD,GACLpD,KAAKmG,iBACHnG,KAAKmG,gBAAgBnG,KAAMiC,EAASK,WAAYc,GAClDpD,KAAKkG,YAAYlG,KAAMiC,EAASK,WAAYc,EAC7C,CACD,IAAAb,IAAQa,GACNpD,KAAKmG,iBAAmBnG,KAAKmG,gBAAgBnG,KAAMiC,EAASO,QAASY,GACrEpD,KAAKkG,YAAYlG,KAAMiC,EAASO,QAASY,EAC1C,CACD,IAAAX,IAAQW,GACNpD,KAAKmG,iBAAmBnG,KAAKmG,gBAAgBnG,KAAMiC,EAASS,QAASU,GACrEpD,KAAKkG,YAAYlG,KAAMiC,EAASS,QAASU,EAC1C,CACD,KAAAT,IAASS,GACPpD,KAAKmG,iBAAmBnG,KAAKmG,gBAAgBnG,KAAMiC,EAASW,SAAUQ,GACtEpD,KAAKkG,YAAYlG,KAAMiC,EAASW,SAAUQ,EAC3C,GI9LuB,0BACpB,SAAUkD,YAAYjD,GAC1B0C,EAAOO,YAAYjD,EACrB,CACM,SAAUqD,SAASC,GACvBZ,EAAO5D,MAAM,gBAAgBwB,OAAiBgD,IAChD,CAEM,SAAUC,SAASD,GACvBZ,EAAOpD,MAAM,gBAAgBgB,OAAiBgD,IAChD,CCKa,MAAAE,qBAEX,WAAAlH,CACUmH,EACAC,EACAC,GAFAhH,KAAQ8G,SAARA,EACA9G,KAAQ+G,SAARA,EACA/G,KAAagH,cAAbA,EAERhH,KAAKiH,MAAQD,EAAc5C,aAAa,CAAEC,UAAU,IAC/CrE,KAAKiH,OACRD,EAAcE,QAAOC,GAASnH,KAAKiH,MAAQE,GAE9C,CACD,QAAA1C,CAAS2C,GACP,OAAKpH,KAAKiH,MAWHjH,KAAKiH,MAAMxC,SAAS2C,GAAc5C,OAAM7B,GACzCA,GAAwB,+BAAfA,EAAM/C,MACjB8G,SACE,kEAEK,OAEPE,SACE,qDACES,KAAKC,UAAU3E,IAEZ+B,QAAQG,OAAOlC,MArBjB,IAAI+B,SAAQ,CAACC,EAASE,KAC3BC,YAAW,KACL9E,KAAKiH,MACPjH,KAAKyE,SAAS2C,GAAc7C,KAAKI,EAASE,GAE1CF,EAAQ,KACT,GACA,EAAE,GAiBV,CACD,sBAAAI,CAAuBC,SACX,QAAVC,EAAAjF,KAAKiH,aAAK,IAAAhC,GAAAA,EAAEsC,qBAAqBvC,EAClC,CACD,yBAAAwC,CAA0BxC,GACxBhF,KAAKgH,cACF1C,MACAC,MAAK4C,GAAQA,EAAKM,wBAAwBzC,KAC1CR,OAAMkD,GAAOd,SAASc,IAC1B,EChEU,MAAAC,EAAY,QACZC,EAAe,WAGfC,EAAgB,SAChBC,EAAe,QCLrB,IAAIC,EC8BX,SAASC,iBACPC,EACAxH,EACAyH,GAEA,OAAO,SAASC,SACd,MAAO,CACL1H,OACA2H,QAAS,CACP7M,KAAM0M,EAAS1M,KACf8M,UAAWJ,EAASI,UACpBC,+BACEC,UAAWN,EAASO,YAAY3E,IAAI4E,QAAQF,WACzCN,EAASO,YAAYE,gBAG5BC,UAAWpF,KAAKD,MAAMsF,iBACtBV,SAEJ,CACF,EDjDM,SAAUW,WAAWC,GACzBf,EAAce,CAChB,CACAD,EAAWE,GAAK1B,KAAKC,UAAUyB,KCgDlB,MAAAC,aAEX,WAAArJ,CAAoBsJ,GAAAjJ,KAASiJ,UAATA,EAClBjJ,KAAKkJ,SAAW,IAAIC,GACrB,CACD,KAAAC,CACEC,EACAhB,EACAiB,GAEA,MAAMC,EAA4C,CAChDhO,KAAM8N,EACNhB,YACAmB,QAAS7B,GAELvL,EAAM2L,EAAYwB,GAClBE,EAAiD,CACrDF,MACAG,cAAe,GACfC,aAAcL,GAAgB,KAC9BM,UAAW,MAIb,OC7EY,SAAAC,eACdC,EACA1N,EACAgK,GAEK0D,EAAIC,IAAI3N,IACX0N,EAAIE,IAAI5N,EAAKgK,EAEjB,CDoEIyD,CAAe7J,KAAKkJ,SAAU9M,EAAKqN,GAC5BzJ,KAAKkJ,SAAS5E,IAAIlI,EAC1B,CACD,eAAA6N,CACEhC,EACAiC,EACAC,EACAb,GAEA,MAAMlN,EAAM2L,EAAY,CACtBxM,KAAM0M,EAAS1M,KACf8M,UAAWJ,EAASI,UACpBmB,QAAS7B,IAELyC,EAAepK,KAAKkJ,SAAS5E,IAAIlI,GAIjCiO,EAAe,CACnBC,aAAcJ,EACdK,YAAaJ,GAETK,YAAc,KAClB,MAAMJ,EAAepK,KAAKkJ,SAAS5E,IAAIlI,GACvCgO,EAAaV,cAAgBU,EAAaV,cAAce,QACtDC,GAAOA,IAAQL,GAChB,EAeH,GAbIf,GAAgBc,EAAaT,eAAiBL,IAChD5C,SAAS,2CAEN0D,EAAaT,cACbS,EAAaT,cAsGtB,SAASgB,aAAaC,EAAcC,GAClC,MAAMC,EAAQ,IAAIvH,KAAKqH,GACjBG,EAAQ,IAAIxH,KAAKsH,GACvB,OAAOC,EAAME,UAAYD,EAAMC,SACjC,CAzGUL,CACEP,EAAaT,aAAahB,UAC1BW,EAAaX,cAGjByB,EAAaT,aAAeL,IAGE,OAA9Bc,EAAaT,aAAuB,CAEtCO,EAAiB,CACfzJ,KAFiB2J,EAAaT,aAAalJ,KAG3CyH,OAAQJ,EACRyB,IAAKtB,EACLE,OAAQH,iBACNC,EACAmC,EAAaT,aAAalJ,KAC1BqH,GAEFa,UAAWyB,EAAaT,aAAahB,YAER,OAA3ByB,EAAaR,WAAsBO,GACrCA,OAAgBc,EAEnB,CAOD,GALAb,EAAaV,cAAcnN,KAAK,CAC9B+N,aAAcJ,EACdK,YAAaJ,EACbK,2BAEGJ,EAAaT,aAAc,CAC9BjD,SACE,gCACEuB,EAAS1M,uBACQ8L,KAAKC,UACtBW,EAASI,qCAGGrI,KAAKkL,aAAajD,GAE1B1D,UAAK0G,GAAWvD,OACzB,CACD,OAAO8C,WACR,CACD,YAAAU,CACEjD,GAEA,GAAIA,EAASuB,UAAY7B,EACvB,MAAM,IAAIhC,iBACRR,EAAKK,iBACL,iDAGJ,MAAMpJ,EAAM2L,EAAY,CACtBxM,KAAM0M,EAAS1M,KACf8M,UAAWJ,EAASI,UACpBmB,QAAS7B,IAELyC,EAAepK,KAAKkJ,SAAS5E,IAAIlI,GAoCvC,OAnCe4D,KAAKiJ,UAAUkC,YAC5BlD,EAAS1M,KACT0M,EAASI,WAES9D,MAClB6G,IACE,MAAMzC,GAAY,IAAIpF,MAAOqC,WACvByF,EACDhP,OAAAiP,OAAAjP,OAAAiP,OAAA,GAAAF,GACH,CAAAlD,OAAQL,EACR0B,IAAKtB,EACLE,OAAQH,iBAAiBC,EAAUmD,EAAI3K,KAAMoH,GAC7Cc,cAUF,OARAyB,EAAaV,cAAc6B,SAAQlB,IACjCA,EAAaC,aAAae,EAAO,IAEnCjB,EAAaT,aAAe,CAC1BlJ,KAAM2K,EAAI3K,KACVyH,OAAQJ,EACRa,aAEK0C,CAAM,IAEf3D,IAOE,MANA0C,EAAaR,UAAYlC,EACzB0C,EAAaV,cAAc6B,SAAQlB,IAC7BA,EAAaE,aACfF,EAAaE,YAAY7C,EAC1B,IAEGA,CAAG,GAKd,CACD,cAAA8D,CAAetQ,EAAcuQ,GAC3BzL,KAAKiJ,UAAUyC,YAAYxQ,EAAMuQ,EAClC,EEjMU,MAAAE,EAAoB,CAC/BC,KAAM,OACNC,UAAW,YACXC,kBAAmB,oBACnBC,eAAgB,iBAChBC,oBAAqB,sBACrBC,iBAAkB,oBCGJ,SAAAC,SAASC,EAAaC,GACpC,IAAKA,EACH,OAAOD,EAET,MAAME,EAAS,IAAIC,IAAIH,GAEvB,OADAE,EAAOE,aAAanN,OAAO,MAAOgN,GAC3BC,EAAOzG,UAChB,CClBA,IAAI4G,EAAoCC,WAAWC,MAInD,SAASC,sBACPC,EACAC,GAEA,IAAIC,EAAM,eAAiBnJ,EAS3B,OAPEkJ,IAAmBlB,EAAkBC,MACrCiB,IAAmBlB,EAAkBE,UAErCiB,GAAO,OAASD,EAAeE,eACtBH,GAAeC,IAAmBlB,EAAkBE,aAC7DiB,GAAO,WAEFA,CACT,CAMM,SAAUE,QACdb,EACA9M,GACA4N,OAAEA,GACFC,EACAC,EACAlJ,EACA2I,EACAC,EACAO,GAEA,IAAKZ,EACH,MAAM,IAAI7G,iBAAiBR,EAAKC,MAAO,qCAEzC,MAAMiI,EAAuB,CAC3B,eAAgB,mBAChB,oBAAqBV,sBAAsBC,EAAaC,IAEtDM,IACFE,EAAQ,yBAA2BF,GAEjCD,IACFG,EAAQ,oBAAsBH,GAE5BjJ,IACFoJ,EAAQ,uBAAyBpJ,GAEnC,MACMqJ,EAA4B,CAChCjO,KAFcgI,KAAKC,UAAUjI,GAG7BoE,OAAQ,OACR4J,UACAJ,UAMF,OAJIhS,mBAAmBkR,IAAQiB,IAC7BE,EAAaC,YAAc,WAGtBf,EAAaL,EAAKmB,GACtB9I,OAAMkD,IACL,MAAM,IAAI/B,iBACRR,EAAKC,MACL,oBAAsBiC,KAAKC,UAAUI,GACtC,IAEFnD,MAAKiJ,MAAM1H,IACV,IAAI2H,EAAe,KACnB,IACEA,QAAqB3H,EAAS4H,MAC/B,CAAC,MAAOC,GACP,MAAM,IAAIhI,iBAAiBR,EAAKC,MAAOiC,KAAKC,UAAUqG,GACvD,CACD,MAAM9N,EA8BZ,SAAS+N,WAAWC,GAClB,GAAI,YAAaA,EACf,OAAOA,EAAIhO,QAEb,OAAOwH,KAAKC,UAAUuG,EACxB,CAnCsBD,CAAWH,GAC3B,GAAI3H,EAASgI,QAAU,IAAK,CAI1B,GAHAlH,SACE,mCAAqCS,KAAKC,UAAUmG,IAE9B,MAApB3H,EAASgI,OACX,MAAM,IAAInI,iBAAiBR,EAAKO,aAAc7F,GAEhD,MAAM,IAAI8F,iBAAiBR,EAAKC,MAAOvF,EACxC,CACD,OAAO4N,CAAY,IAEpBlJ,MAAK6G,IACJ,GAAIA,EAAI5K,QAAU4K,EAAI5K,OAAOhE,OAAQ,CACnC,MAAMuR,EAAc1G,KAAKC,UAAU8D,EAAI5K,QACjCsF,EAAgD,CACpDtF,OAAQ4K,EAAI5K,OACZC,KAAM2K,EAAI3K,MAEZ,MAAM,IAAIoF,0BACR,+CAAiDkI,EACjDjI,EAEH,CACD,OAAOsF,CAAG,GAEhB,CCzGa,MAAA4C,cAYX,WAAArO,CACE8I,EACQ2D,EACAc,EACAe,EACAnK,EACRoK,EACQtB,GAAc,EACdC,EAAgClB,EAAkBC,cANlD5L,KAAMoM,OAANA,EACApM,KAAKkN,MAALA,EACAlN,KAAYiO,aAAZA,EACAjO,KAAgB8D,iBAAhBA,EAEA9D,KAAW4M,YAAXA,EACA5M,KAAc6M,eAAdA,EAnBF7M,KAAKmO,MAAG,GAERnO,KAASoO,UAAG,IACZpO,KAAcqO,eAAG,GACjBrO,KAAOsO,SAAG,EACVtO,KAAQuO,SAAG,IAEXvO,KAAYwO,aAAkB,KAC9BxO,KAAcyO,eAAkB,KAChCzO,KAAU0O,WAAkB,KAC5B1O,KAAgBoN,kBAAG,EA6H3BpN,KAAAmL,YAG6C,CAC3C9B,EACAhK,KAEA,MAAMsP,EAAkB,IAAIC,gBAoB5B,OAjBiB5O,KAAK6O,WAAU,IAC9B7B,QACEd,SAAS,GAAGlM,KAAK8O,2BAA4B9O,KAAKoM,QAClD,CACE7Q,KAAM,YAAYyE,KAAKuO,sBAAsBvO,KAAKoO,sBAAsBpO,KAAK+O,2BAA2B/O,KAAKqO,iBAC7GW,cAAe3F,EACfhB,UAAWhJ,GAEbsP,EACA3O,KAAKkN,MACLlN,KAAKwO,aACLxO,KAAKyO,eACLzO,KAAK4M,YACL5M,KAAK6M,eACL7M,KAAKoN,mBAGM,EAEjBpN,KAAAiP,eAG6C,CAC3CC,EACA7P,KAEA,MAAMsP,EAAkB,IAAIC,gBAkB5B,OAjBmB5O,KAAK6O,WAAU,IACzB7B,QACLd,SAAS,GAAGlM,KAAK8O,8BAA+B9O,KAAKoM,QACrD,CACE7Q,KAAM,YAAYyE,KAAKuO,sBAAsBvO,KAAKoO,sBAAsBpO,KAAK+O,2BAA2B/O,KAAKqO,iBAC7GW,cAAeE,EACf7G,UAAWhJ,GAEbsP,EACA3O,KAAKkN,MACLlN,KAAKwO,aACLxO,KAAKyO,eACLzO,KAAK4M,YACL5M,KAAK6M,eACL7M,KAAKoN,mBAGQ,EAxKbc,IACmC,iBAA1BA,EAAiBzC,OAC1BzL,KAAKmP,MAAQjB,EAAiBzC,WAEW,IAAhCyC,EAAiBkB,aAC1BpP,KAAKsO,QAAUJ,EAAiBkB,YAElCpP,KAAKmO,MAAQD,EAAiBhT,MAEhC,MAAMS,SAAEA,EAAU4M,UAAW8G,EAAOC,UAAEA,EAAShP,QAAEA,GAAYmI,EAQ7D,GAPI9M,IACFqE,KAAKoO,UAAYzS,GAEf0T,IACFrP,KAAKuO,SAAWc,GAElBrP,KAAK+O,aAAezO,GACfgP,EACH,MAAM,IAAI3J,iBACRR,EAAKK,iBACL,4BAGJxF,KAAKqO,eAAiBiB,EACL,QAAjBrK,EAAAjF,KAAKiO,oBAAY,IAAAhJ,GAAAA,EAAEF,wBAAuBH,IACxC8B,SAAS,wBAAwB9B,KACjC5E,KAAKwO,aAAe5J,CAAK,IAEN,QAArB2K,EAAAvP,KAAK8D,wBAAgB,IAAAyL,GAAAA,EAAExK,wBAAuBsG,IAC5C,MAAMzG,MAAEA,GAAUyG,EAClB3E,SAAS,kCAAkC9B,KAC3C5E,KAAKyO,eAAiB7J,CAAK,GAE9B,CACD,eAAIkK,GACF,OF/DY,SAAAU,WACdC,EACAvB,GAEA,MAAMoB,UAAEA,EAAS3T,SAAEA,EAAU4M,UAAW8G,EAAO/O,QAAEA,GAAYmP,GACvDvU,KAAEA,EAAIkU,WAAEA,EAAU3D,KAAEA,GAASyC,EAGnC,IAAIwB,EAAU,GAFGN,EAAa,QAAU,YACvBlU,GAAQ,uCAEzB,GAAoB,iBAATuQ,EACTiE,GAAW,IAAIjE,SACV,QAAoB,IAATA,EAEhB,MADA7E,SAAS,mCACH,IAAIjB,iBACRR,EAAKK,iBACL,sCAGJ,MAAO,GAAGkK,iBAAuBL,eAAqB1T,cAAqB2E,gBAAsBgP,GACnG,CE4CWE,CACL,CACEF,UAAWtP,KAAKqO,eAChB1S,SAAUqE,KAAKoO,UACf7F,UAAWvI,KAAKuO,SAChBjO,QAASN,KAAK+O,cAEhB,CAAE7T,KAAM8E,KAAKmO,MAAOiB,WAAYpP,KAAKsO,QAAS7C,KAAMzL,KAAKmP,OAE5D,CACD,WAAAzD,CAAYxQ,EAAcuQ,EAAekE,GACvC3P,KAAKmO,MAAQjT,EACb8E,KAAKoN,kBAAmB,EACJ,iBAAT3B,IACTzL,KAAKmP,MAAQ1D,QAES,IAAbkE,IACT3P,KAAKsO,QAAUqB,EAElB,CACD,cAAAC,CAAeC,GACb7P,KAAKwO,aAAeqB,CACrB,CAED,iBAAMC,CAAYC,GAAa,SAC7B,IAAIC,EAAyC,IAAItL,SAAQC,GACvDA,EAAQ3E,KAAKwO,gBAkBf,OAhBIxO,KAAK8D,mBACP9D,KAAKyO,eAAyD,QAAxCxJ,QAAOjF,KAAK8D,iBAAiBW,kBAAW,IAAAQ,OAAA,EAAAA,EAAEL,OAGhEoL,EADEhQ,KAAKiO,aACUjO,KAAKiO,aACnBxJ,SAAyBsL,GACzBxL,MAAK9D,GACCA,GAGLT,KAAKwO,aAAe/N,EAAK0M,YAClBnN,KAAKwO,cAHH,OAMI,IAAI9J,SAAQC,GAAWA,EAAQ,MAE3CqL,CACR,CAED,aAAAC,CAAcC,GACZlQ,KAAK0O,WAAawB,CACnB,CAED,SAAArB,CACEsB,EACAC,GAAQ,GAER,IAAIC,GAAa,EACjB,OAAOrQ,KAAK8P,YAAYM,GACrB7L,MAAK6G,IACJiF,EAAarQ,KAAK0O,aAAetD,EACjCpL,KAAK0O,WAAatD,EACXA,KAER7G,KAAK4L,GACL3L,OAAMkD,IAEL,GACE,SAAUA,GACVA,EAAI9H,OAASuF,EAAKO,eACjB0K,GACDC,EAGA,OADA3J,SAAS,gCACF1G,KAAK6O,UAAUsB,GAAgB,GAExC,MAAMzI,CAAG,GAEd,CA4DD,iBAAA4I,CAAkBC,GAChBvQ,KAAK6M,eAAiB0D,CACvB,ECnKa,SAAAC,YACdC,EACAvB,EACA7G,GAEAoI,EAAWC,iBAOX,MAN0C,CACxClI,YAAaiI,EACblV,KAAM2T,EACN1F,QAAS5B,EACTS,UAAWA,EAGf,CAKa,MAAAsI,gBAEX,WAAAhR,CAAoBiR,GAAA5Q,KAAU4Q,WAAVA,EADZ5Q,KAAS6Q,UAA4B,EACW,CACxD,eAAAC,CACEN,GAEA,MAAMnF,EAASrL,KAAK4Q,WAAW3B,eAC7BuB,EAAYjV,KACZiV,EAAYnI,WAER0I,EAAiB1F,EAAO9G,MAAK6G,GAE5B/O,OAAAiP,OAAAjP,OAAAiP,OAAA,CAAA,EAAAF,GAAG,CACNlD,OAAQL,EACR0B,IAAKiH,EACL7H,UAAWpF,KAAKD,MAAMsF,qBAI1B5I,KAAK6Q,UAAUtU,KAAK8O,GACpB,MAAM2F,cAAgB,IACnBhR,KAAK6Q,UAAY7Q,KAAK6Q,UAAUpG,QAAOwG,GAAWA,IAAY5F,IAEjE,OADAA,EAAO9G,KAAKyM,cAAeA,eACpBD,CACR,EAuBG,SAAUD,gBACdN,GAEA,OAAOA,EAAYhI,YAAY0I,iBAAiBJ,gBAAgBN,EAClE,CCnDM,SAAUW,aAAaC,GAC3B,MAAOC,EAAUC,GAAYF,EAASG,MAAM,OACtC5B,EAAwB,UAAb0B,GACVnW,EAAMsW,GAAgBF,EAASC,MAAM,KAE5C,MAAO,CAAErW,OAAMuQ,KADFgG,OAAOD,GACCpC,WAAYO,EACnC,CAWa,MAAA+B,YAaX,WAAA/R,CACkBkE,EAEC8N,EACA3K,EACA4K,GAEjB,GANgB5R,KAAG6D,IAAHA,EAEC7D,KAAkB2R,mBAAlBA,EACA3R,KAAagH,cAAbA,EACAhH,KAAiB4R,kBAAjBA,EAfnB5R,KAAU6R,YAAG,EACb7R,KAAY8R,cAAG,EAKf9R,KAAoB+R,sBAAY,EAChC/R,KAAA6M,eAAgClB,EAAkBC,KAUzB,oBAAZoG,SAA2BA,QAAQC,IAAK,CACjD,MAAM/W,EAAO8W,QAAQC,IAA2C,oCAC5D/W,IACFwL,SAAS,qCACT1G,KAAK6R,YAAa,EAClB7R,KAAKkS,kBAAoBf,aAAajW,GAEzC,CACF,CAED,gBAAAiX,GACOnS,KAAK+R,uBACR/R,KAAK+R,sBAAuB,EAE/B,CACD,iBAAAzB,CAAkBC,GAChBvQ,KAAK6M,eAAiB0D,EAClBvQ,KAAK8R,cACP9R,KAAK4Q,WAAWN,kBAAkBC,EAErC,CACD,OAAA6B,GAME,OALAC,EACErS,KAAK6D,IACL,eACAwD,KAAKC,UAAUtH,KAAK0I,gBAEfhE,QAAQC,SAChB,CAGD,WAAA+D,GACE,MAAM4J,EAAOjL,KAAKkL,MAAMlL,KAAKC,UAAUtH,KAAK2R,qBAE5C,cADOW,EAAK/J,UACL+J,CACR,CAGD,cAAA5B,GACM1Q,KAAK8R,oBAGoB7G,IAAzBjL,KAAKwS,kBACP9L,SAAS,6DACT1G,KAAKwS,gBAAkBxE,eAGrBhO,KAAKgH,gBACPhH,KAAKyS,mBAAqB,IAAI5L,qBAC5B7G,KAAK6D,IAAItI,KACTyE,KAAK6D,IAAI4E,QACTzI,KAAKgH,gBAGLhH,KAAK4R,oBACP5R,KAAK0S,uBAAyB,IAAI9O,sBAChC5D,KAAK6D,IACL7D,KAAK4R,oBAIT5R,KAAK8R,cAAe,EACpB9R,KAAK4Q,WAAa,IAAI5Q,KAAKwS,gBACzBxS,KAAK2R,mBACL3R,KAAK6D,IAAI4E,QAAQ2D,OACjBpM,KAAK6D,IAAI4E,QAAQyE,MACjBlN,KAAKyS,mBACLzS,KAAK0S,4BACLzH,EACAjL,KAAK+R,qBACL/R,KAAK6M,gBAEH7M,KAAKkS,mBACPlS,KAAK4Q,WAAWlF,YACd1L,KAAKkS,kBAAkBhX,KACvB8E,KAAKkS,kBAAkBzG,KACvBzL,KAAKkS,kBAAkB9C,YAG3BpP,KAAK2S,cAAgB,IAAI3J,aAAahJ,KAAK4Q,YAC3C5Q,KAAKkR,iBAAmB,IAAIP,gBAAgB3Q,KAAK4Q,YAClD,CAGD,cAAApF,CAAe0C,GACb,GACElO,KAAK8R,eACJc,yBAAyB5S,KAAKkS,kBAAmBhE,GAGlD,MADAtH,SAAS,8CACH,IAAIjB,iBACRR,EAAKE,oBACL,6CAGJrF,KAAKkS,kBAAoBhE,EACzBlO,KAAK6R,YAAa,CACnB,EASa,SAAAe,yBACdC,EACAC,GAEA,OACED,EAAkB3X,OAAS4X,EAAkB5X,MAC7C2X,EAAkBpH,OAASqH,EAAkBrH,MAC7CoH,EAAkBzD,aAAe0D,EAAkB1D,UAEvD,CASM,SAAU2D,2BACdC,EACA9X,EACAuQ,EACA2D,GAAa,GAGTnU,mBAAmBC,MnBvNlBsS,eAAeyF,WAAWC,GAI/B,aAHqBxG,MAAMwG,EAAU,CACnC3F,YAAa,aAED4F,EAChB,CmBmNSF,CAAW,WAAW/X,IAAOuQ,EAAO,IAAIA,IAAS,MACtDnQ,qBAAqB,gBAAgB,IAEvC0X,EAAGxH,eAAe,CAAEtQ,OAAMuQ,OAAM2D,cAClC,CAgBgB,SAAAgE,eACdC,EACAC,GAEA,IAAIzP,EACA0P,EACA,aAAcF,GAChBE,EAAYF,EACZxP,EAAM2P,MAEND,EAAYD,EACZzP,EAAMwP,GAGHxP,GAAmC,IAA5BxH,OAAOC,KAAKuH,GAAKrH,SAC3BqH,EAAM2P,KAER,MAAMC,EAAWC,aAAa7P,EAAK,gBAC7B8P,EAAatM,KAAKC,UAAUiM,GAClC,GAAIE,EAASG,cAAcD,GAAa,CACtC,MAAMlD,EAAagD,EAASrP,aAAa,CAAEuP,eACrClL,EAAUgL,EAASI,WAAWF,GAEpC,GADqBtX,OAAOC,KAAKmM,GAASjM,OAAS,EAGjD,OADAkK,SAAS,4BACF+J,CAEV,CAKD,OAJAqD,kBAAkBP,GAElB7M,SAAS,qCAEF+M,EAASM,WAAW,CACzBC,mBAAoBL,EACpBlL,QAAS8K,GAEb,CAQM,SAAUO,kBAAkBP,GAEhC,IAAKA,EACH,MAAM,IAAI5N,iBAAiBR,EAAKK,iBAAkB,sBAOpD,MATe,CAAC,YAAa,WAAY,WAIlC+F,SAAQ0I,IACb,GAAyB,OAArBV,EAAUU,SAAwChJ,IAArBsI,EAAUU,GACzC,MAAM,IAAItO,iBAAiBR,EAAKK,iBAAkB,GAAGyO,aACtD,KAEI,CACT,CAOM,SAAUC,UAAU1L,GACxB,OAAOA,EAAY4J,SAErB,CC7PM,SAAUlH,aACdjD,GAEA,OAAOA,EAASO,YAAYmK,cAAczH,aAAajD,EACzD,CAgCM,SAAUA,SACdwI,EACApH,EACAhB,EACAiB,GAIA,OAFAmH,EAAWC,iBACXD,EAAWkC,cAAcvJ,MAAMC,EAAWhB,EAAWiB,GAC9C,CACLd,YAAaiI,EACbjH,QAAS7B,EACTpM,KAAM8N,EACNhB,YAEJ,CAMM,SAAU8L,WACdC,GAEA,MACEhM,SAAS7M,KAAEA,EAAI8M,UAAEA,EAASC,gBAAEA,IAC1B8L,EACJ,OAAOnM,SAASmL,eAAe9K,GAAkB/M,EAAM8M,EACzD,CCvGM,SAAUgM,aACd/L,EACAgM,EACAC,EACAC,GAEA,IAAI/D,EACAgE,EAQJ,GAPIH,GAAY,mBAAoBA,GAClC7D,EAAa6D,EACbG,EAAWF,IAEX9D,EAAa2C,eAAe9K,GAC5BmM,EAAWH,IAER7D,IAAgBgE,GAAYD,EAC/B,MAAM,IAAI7O,iBAAiBR,EAAKK,iBAAkB,uBAEpD,MAAO,CAAEwN,GAAIvC,EAAY8D,KAAME,EACjC,CCQM,SAAUC,UACdC,EAGAC,EAGAC,EACAC,GAEA,IAAIvL,EACAD,EAcAyL,EAbJ,GAAI,YAAaJ,EAA4B,CAC3C,MAAMP,EACJO,GACIlU,KAAEA,EAAIyH,OAAEA,EAAMS,UAAEA,GAAcyL,EACpC9K,EAAe,CACb7I,OACAyH,SACAS,aAEFY,EAAM4K,WAAWC,EAClB,MACC7K,EAAMoL,EAUR,GAPgC,mBAArBC,EACTG,EAAWH,GAEXG,EAAWH,EAAiBI,OAC5BH,EAAUD,EAAiBK,MACdL,EAAiBE,aAE3BC,EACH,MAAM,IAAIpP,iBAAiBR,EAAKK,iBAAkB,uBAEpD,OAAO+D,EAAIf,YAAYmK,cAAc1I,gBACnCV,EACAwL,EACAF,EACAvL,EAEJ,EC7EM,SAAU4L,oBAAoBC,IlBN9B,SAAUC,cAAcC,GAC5B1R,EAAc0R,CAChB,CkBKED,CAAczR,GACd2R,EACE,IAAInU,UACF,gBACA,CAACoU,GAAavB,mBAAoBhQ,EAAUyE,cAC1C,MAAM5E,EAAM0R,EAAUC,YAAY,OAAOpR,eACnC6J,EAAesH,EAAUC,YAAY,iBACrC1R,EAAmByR,EAAUC,YAAY,sBAC/C,IAAIC,EAAUhN,EAId,GAHIzE,IACFyR,EAAUpO,KAAKkL,MAAMvO,KAElBH,EAAI4E,QAAQF,UACf,MAAM,IAAI5C,iBACRR,EAAKK,iBACL,qFAGJ,OAAO,IAAIkM,YACT7N,EACKxH,OAAAiP,OAAAjP,OAAAiP,OAAA,GAAAmK,GAAO,CAAElN,UAAW1E,EAAI4E,QAAQF,YACrC0F,EACAnK,EACD,aAGHlC,sBAAqB,IAEzB8T,EAAgBna,EAAM8Z,EAASF,GAE/BO,EAAgBna,EAAM8Z,EAAS,UACjC,CClCAH", "preExistingComment": "firebase-data-connect.js.map"}