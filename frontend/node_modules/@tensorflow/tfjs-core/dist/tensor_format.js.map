{"version": 3, "file": "tensor_format.js", "sourceRoot": "", "sources": ["../src/tensor_format.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAGH,+BAAyE;AAEzE,8DAA8D;AAC9D,IAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,yEAAyE;AACzE,IAAM,0BAA0B,GAAG,CAAC,CAAC;AACrC,wCAAwC;AACxC,IAAM,qBAAqB,GAAG,CAAC,CAAC;AAEhC,SAAgB,cAAc,CAC1B,IAAyB,EAAE,KAAe,EAAE,KAAe,EAC3D,OAAgB;IAClB,IAAM,OAAO,GAAG,qBAAc,CAAC,KAAK,CAAC,CAAC;IACtC,IAAM,SAAS,GAAG,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACvE,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAC5E,IAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzB,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,IAAI,CAAC,cAAY,KAAO,CAAC,CAAC;QAChC,KAAK,CAAC,IAAI,CAAC,aAAW,IAAM,CAAC,CAAC;QAC9B,KAAK,CAAC,IAAI,CAAC,eAAa,KAAK,MAAG,CAAC,CAAC;QAClC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACzB;IACD,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,GAAG,CAAC,EAAV,CAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AAhBD,wCAgBC;AAED,SAAS,uBAAuB,CAC5B,IAAyB,EAAE,KAAe,EAAE,KAAe,EAC3D,OAAiB;IACnB,IAAM,CAAC,GAAG,oBAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,IAAM,SAAS,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAM,cAAc,GAChB,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE7D,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,OAAO,EAAE,GAAG,EAAE,EAAE;YAC1C,IAAM,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;YAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;gBAChC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CACnB,SAAS,CAAC,CAAC,CAAC,EACZ,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;aAC/D;SACF;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,WAAW,CAChB,GAAmC,EAAE,GAAW,EAAE,KAAe;IACnE,IAAI,MAAc,CAAC;IACnB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,GAAM,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,QAAK;aAC3D,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAAG,CAAA,CAAC;KAC7D;SAAM,IAAI,eAAQ,CAAC,GAAG,CAAC,EAAE;QACxB,MAAM,GAAG,MAAI,GAAG,MAAG,CAAC;KACrB;SAAM,IAAI,KAAK,KAAK,MAAM,EAAE;QAC3B,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;KAC/B;SAAM;QACL,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;KACpE;IAED,OAAO,eAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,eAAe,CAAC,CAAS;IAChC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;AACpC,CAAC;AAED,SAAS,iBAAiB,CACtB,IAAyB,EAAE,KAAe,EAAE,KAAe,EAC3D,OAAiB,EAAE,SAAmB,EAAE,MAAa;IAAb,uBAAA,EAAA,aAAa;IACvD,IAAM,iBAAiB,GAAG,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAExD,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,IAAI,KAAK,KAAK,WAAW,EAAE;YACzB,IAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/C,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;SACjD;QACD,IAAI,KAAK,KAAK,MAAM,EAAE;YACpB,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAW,CAAC,CAAC,CAAC;SAC7C;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC7B;IAED,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,IAAI,IAAI,GAAG,qBAAqB,EAAE;YAChC,IAAM,aAAa,GAAG,0BAA0B,GAAG,iBAAiB,CAAC;YAErE,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CACtB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;YAClC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAiC,IAAI,CAAC,KAAK,CAChE,CAAC,IAAI,GAAG,0BAA0B,CAAC,GAAG,iBAAiB,EACvD,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC;YAC/B,IAAI,KAAK,KAAK,WAAW,EAAE;gBACzB,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBAC3C,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;aAC1C;YACD,OAAO;gBACL,GAAG;oBACH,SAAS,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAnC,CAAmC,CAAC;yBACvD,IAAI,CAAC,IAAI,CAAC;oBACf,SAAS;oBACT,QAAQ;yBACH,GAAG,CACA,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,WAAW,CACjB,CAAC,EAAE,SAAS,CAAC,IAAI,GAAG,0BAA0B,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EADrD,CACqD,CAAC;yBACnE,IAAI,CAAC,IAAI,CAAC;oBACf,GAAG;aACJ,CAAC;SACH;QACD,IAAM,WAAW,GACb,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAgB,IAAI,CAAC,CAAC;QAE5D,OAAO;YACL,GAAG;gBACH,WAAW,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAnC,CAAmC,CAAC;qBACzD,IAAI,CAAC,IAAI,CAAC;gBACf,GAAG;SACJ,CAAC;KACH;IAED,+BAA+B;IAC/B,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChC,IAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC;IAC9C,IAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,IAAI,IAAI,GAAG,qBAAqB,EAAE;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAA0B,EAAE,CAAC,EAAE,EAAE;YACnD,IAAM,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;YACzB,IAAM,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC;YAC3B,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,iBAAiB,CAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAC9D,KAAK,CAAC,YAAY,CAAC,EAAE;SAC1B;QACD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,0BAA0B,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC7D,IAAM,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;YACzB,IAAM,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC;YAC3B,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,iBAAiB,CAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAC9D,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE;SACnC;KACF;SAAM;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAM,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;YACzB,IAAM,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC;YAC3B,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,iBAAiB,CAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAC9D,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE;SACnC;KACF;IACD,IAAM,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAClC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;KACjC;IACD,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;QAC7B,UAAU,IAAI,IAAI,CAAC;KACpB;IACD,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACnB,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IACrE,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAAC,IACU;IACrC,IAAM,aAAa,GAA4B,EAAE,CAAC;IAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACvC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAqB,CAAC,CAAC;KAChE;IACD,OAAO,aAAa,CAAC;AACvB,CAAC"}