{"name": "@mui/x-internals", "version": "8.5.1", "author": "MUI Team", "description": "Utility functions for the MUI X packages (internal use only).", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com/x/", "sideEffects": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "publishConfig": {"access": "public", "directory": "build"}, "keywords": ["react", "react-component", "material-ui", "mui", "mui-x", "utils"], "repository": {"type": "git", "url": "git+https://github.com/mui/mui-x.git", "directory": "packages/x-internals"}, "dependencies": {"@babel/runtime": "^7.27.4", "@mui/utils": "^7.1.1"}, "peerDependencies": {"@mui/system": "^5.15.14 || ^6.0.0 || ^7.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "engines": {"node": ">=14.0.0"}, "private": false, "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}}