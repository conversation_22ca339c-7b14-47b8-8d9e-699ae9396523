{"name": "@mui/icons-material", "version": "7.1.1", "author": "MUI Team", "description": "Material Design icons distributed as SVG React components.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "material-ui", "material design", "icons"], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/mui-icons-material"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/material-ui/material-icons/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.27.1"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "@mui/material": "^7.1.1"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=14.0.0"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./*": {"import": {"types": "./esm/*.d.ts", "default": "./esm/*.js"}, "require": {"types": "./*.d.ts", "default": "./*.js"}}, "./esm": null, "./utils/*": null}, "private": false, "module": "./esm/index.js"}