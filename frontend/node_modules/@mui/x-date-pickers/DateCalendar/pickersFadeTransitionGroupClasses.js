"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.pickersFadeTransitionGroupClasses = exports.getPickersFadeTransitionGroupUtilityClass = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
const getPickersFadeTransitionGroupUtilityClass = slot => (0, _generateUtilityClass.default)('MuiPickersFadeTransitionGroup', slot);
exports.getPickersFadeTransitionGroupUtilityClass = getPickersFadeTransitionGroupUtilityClass;
const pickersFadeTransitionGroupClasses = exports.pickersFadeTransitionGroupClasses = (0, _generateUtilityClasses.default)('MuiPickersFadeTransitionGroup', ['root']);