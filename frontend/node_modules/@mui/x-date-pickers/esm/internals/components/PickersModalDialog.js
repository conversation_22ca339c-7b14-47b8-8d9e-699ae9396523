import _extends from "@babel/runtime/helpers/esm/extends";
import * as React from 'react';
import DialogContent from '@mui/material/DialogContent';
import Fade from '@mui/material/Fade';
import MuiDialog, { dialogClasses } from '@mui/material/Dialog';
import { styled } from '@mui/material/styles';
import { DIALOG_WIDTH } from "../constants/dimensions.js";
import { usePickerContext } from "../../hooks/index.js";
import { usePickerPrivateContext } from "../hooks/usePickerPrivateContext.js";
import { jsx as _jsx } from "react/jsx-runtime";
const PickersModalDialogRoot = styled(MuiDialog)({
  [`& .${dialogClasses.container}`]: {
    outline: 0
  },
  [`& .${dialogClasses.paper}`]: {
    outline: 0,
    minWidth: DIALOG_WIDTH
  }
});
const PickersModalDialogContent = styled(DialogContent)({
  '&:first-of-type': {
    padding: 0
  }
});
export function PickersModalDialog(props) {
  const {
    children,
    slots,
    slotProps
  } = props;
  const {
    open
  } = usePickerContext();
  const {
    dismissViews,
    onPopperExited
  } = usePickerPrivateContext();
  const Dialog = slots?.dialog ?? PickersModalDialogRoot;
  const Transition = slots?.mobileTransition ?? Fade;
  return /*#__PURE__*/_jsx(Dialog, _extends({
    open: open,
    onClose: () => {
      dismissViews();
      onPopperExited?.();
    }
  }, slotProps?.dialog, {
    TransitionComponent: Transition,
    TransitionProps: slotProps?.mobileTransition,
    PaperComponent: slots?.mobilePaper,
    PaperProps: slotProps?.mobilePaper,
    children: /*#__PURE__*/_jsx(PickersModalDialogContent, {
      children: children
    })
  }));
}