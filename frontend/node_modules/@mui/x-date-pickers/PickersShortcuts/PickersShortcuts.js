"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PickersShortcuts = PickersShortcuts;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _styles = require("@mui/material/styles");
var _propTypes = _interopRequireDefault(require("prop-types"));
var _List = _interopRequireDefault(require("@mui/material/List"));
var _ListItem = _interopRequireDefault(require("@mui/material/ListItem"));
var _Chip = _interopRequireDefault(require("@mui/material/Chip"));
var _dimensions = require("../internals/constants/dimensions");
var _hooks = require("../hooks");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["items", "changeImportance"],
  _excluded2 = ["getValue"];
const PickersShortcutsRoot = (0, _styles.styled)(_List.default, {
  name: 'MuiPickersLayout',
  slot: 'Shortcuts'
})({});

/**
 * Demos:
 *
 * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)
 *
 * API:
 *
 * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)
 */
function PickersShortcuts(props) {
  const {
      items,
      changeImportance = 'accept'
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const {
    setValue
  } = (0, _hooks.usePickerActionsContext)();
  const isValidValue = (0, _hooks.useIsValidValue)();
  if (items == null || items.length === 0) {
    return null;
  }
  const resolvedItems = items.map(_ref => {
    let {
        getValue
      } = _ref,
      item = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded2);
    const newValue = getValue({
      isValid: isValidValue
    });
    return (0, _extends2.default)({}, item, {
      label: item.label,
      onClick: () => {
        setValue(newValue, {
          changeImportance,
          shortcut: item
        });
      },
      disabled: !isValidValue(newValue)
    });
  });
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(PickersShortcutsRoot, (0, _extends2.default)({
    dense: true,
    sx: [{
      maxHeight: _dimensions.VIEW_HEIGHT,
      maxWidth: 200,
      overflow: 'auto'
    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]
  }, other, {
    children: resolvedItems.map(item => {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ListItem.default, {
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Chip.default, (0, _extends2.default)({}, item))
      }, item.id ?? item.label);
    })
  }));
}
process.env.NODE_ENV !== "production" ? PickersShortcuts.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Importance of the change when picking a shortcut:
   * - "accept": fires `onChange`, fires `onAccept` and closes the Picker.
   * - "set": fires `onChange` but do not fire `onAccept` and does not close the Picker.
   * @default "accept"
   */
  changeImportance: _propTypes.default.oneOf(['accept', 'set']),
  className: _propTypes.default.string,
  component: _propTypes.default.elementType,
  /**
   * If `true`, compact vertical padding designed for keyboard and mouse input is used for
   * the list and list items.
   * The prop is available to descendant components as the `dense` context.
   * @default false
   */
  dense: _propTypes.default.bool,
  /**
   * If `true`, vertical padding is removed from the list.
   * @default false
   */
  disablePadding: _propTypes.default.bool,
  /**
   * Ordered array of shortcuts to display.
   * If empty, does not display the shortcuts.
   * @default []
   */
  items: _propTypes.default.arrayOf(_propTypes.default.shape({
    getValue: _propTypes.default.func.isRequired,
    id: _propTypes.default.string,
    label: _propTypes.default.string.isRequired
  })),
  style: _propTypes.default.object,
  /**
   * The content of the subheader, normally `ListSubheader`.
   */
  subheader: _propTypes.default.node,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object])
} : void 0;