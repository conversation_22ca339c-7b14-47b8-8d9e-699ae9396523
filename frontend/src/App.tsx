/**
 * Composant principal de l'application PresencePro
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { UserRole } from './types';
import MainLayout from './components/Layout/MainLayout';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import AdminDashboard from './pages/AdminDashboard';
import TeacherDashboard from './pages/TeacherDashboard';
import StudentDashboard from './pages/StudentDashboard';
import AttendanceSessionPage from './pages/AttendanceSessionPage';
import FaceRecognitionTestPage from './pages/FaceRecognitionTestPage';
import SupabaseTest from './components/Firebase/FirebaseTest';
import SupabaseTestSimple from './pages/SupabaseTestSimple';

// Configuration du thème Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: 8,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
  },
});

// Composant pour protéger les routes authentifiées avec vérification de rôle
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredRole }) => {
  const { user, loading, isAuthenticated, getRoleBasedRoute } = useAuth();

  if (loading) {
    return <div>Chargement...</div>;
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  // Vérifier le rôle requis si spécifié
  if (requiredRole && user.role !== requiredRole) {
    // Rediriger vers le dashboard approprié selon le rôle de l'utilisateur
    const userDashboard = getRoleBasedRoute();
    return <Navigate to={userDashboard} replace />;
  }

  return <MainLayout>{children}</MainLayout>;
};

// Composant pour rediriger les utilisateurs connectés vers leur dashboard
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading, isAuthenticated, getRoleBasedRoute } = useAuth();

  if (loading) {
    return <div>Chargement...</div>;
  }

  if (isAuthenticated && user) {
    // Rediriger vers le dashboard approprié selon le rôle
    const userDashboard = getRoleBasedRoute();
    return <Navigate to={userDashboard} replace />;
  }

  return <>{children}</>;
};

// Composant principal des routes
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Route publique - Page de connexion */}
      <Route
        path="/login"
        element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        }
      />

      {/* Routes protégées par rôle */}

      {/* Dashboard Administrateur */}
      <Route
        path="/admin-dashboard"
        element={
          <ProtectedRoute requiredRole={UserRole.ADMIN}>
            <AdminDashboard />
          </ProtectedRoute>
        }
      />

      {/* Dashboard Professeur */}
      <Route
        path="/teacher-dashboard"
        element={
          <ProtectedRoute requiredRole={UserRole.TEACHER}>
            <TeacherDashboard />
          </ProtectedRoute>
        }
      />

      {/* Dashboard Étudiant */}
      <Route
        path="/student-dashboard"
        element={
          <ProtectedRoute requiredRole={UserRole.STUDENT}>
            <StudentDashboard />
          </ProtectedRoute>
        }
      />

      {/* Routes protégées générales */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        }
      />

      <Route
        path="/attendance-session"
        element={
          <ProtectedRoute>
            <AttendanceSessionPage />
          </ProtectedRoute>
        }
      />

      {/* Routes de test */}
      <Route
        path="/face-recognition-test"
        element={<FaceRecognitionTestPage />}
      />

      <Route
        path="/supabase-test"
        element={<SupabaseTestSimple />}
      />

      <Route
        path="/firebase-test"
        element={
          <ProtectedRoute>
            <SupabaseTest />
          </ProtectedRoute>
        }
      />

      {/* Redirection par défaut vers la page de connexion */}
      <Route path="/" element={<Navigate to="/login" replace />} />

      {/* Route 404 - Rediriger vers login */}
      <Route path="*" element={<Navigate to="/login" replace />} />
    </Routes>
  );
};

// Composant principal de l'application
const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <AppRoutes />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
