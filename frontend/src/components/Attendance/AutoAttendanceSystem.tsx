import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Alert,
  Stack,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import FaceDetectionCamera from '../FaceRecognition/FaceDetectionCamera';
import { supabaseService } from '../../services/supabaseService';
import { 
  Course, 
  User, 
  AttendanceRecord, 
  AttendanceStatus, 
  AttendanceMethod,
  FaceDetectionResult 
} from '../../types';

interface AutoAttendanceSystemProps {
  course: Course;
  onAttendanceUpdate?: (records: AttendanceRecord[]) => void;
}

interface AttendanceSession {
  id: string;
  course: Course;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'cancelled';
  detectedStudents: Set<string>;
  attendanceRecords: AttendanceRecord[];
}

const AutoAttendanceSystem: React.FC<AutoAttendanceSystemProps> = ({
  course,
  onAttendanceUpdate
}) => {
  const [session, setSession] = useState<AttendanceSession | null>(null);
  const [students, setStudents] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [lateThresholdMinutes, setLateThresholdMinutes] = useState(15);
  const [detectionHistory, setDetectionHistory] = useState<{
    userId: string;
    timestamp: Date;
    confidence: number;
  }[]>([]);

  /**
   * Charge la liste des étudiants du cours
   */
  const loadStudents = useCallback(async () => {
    try {
      setLoading(true);
      // Ici, on devrait charger les étudiants inscrits au cours
      // Pour l'instant, on charge tous les étudiants
      const allUsers = await supabaseService.getUsers();
      const courseStudents = allUsers.filter(user => user.role === 'student');
      setStudents(courseStudents);
    } catch (err) {
      console.error('Erreur chargement étudiants:', err);
      setError('Impossible de charger la liste des étudiants');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Démarre une session de présence automatique
   */
  const startSession = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const newSession: AttendanceSession = {
        id: `session_${Date.now()}`,
        course,
        startTime: new Date(),
        status: 'active',
        detectedStudents: new Set(),
        attendanceRecords: []
      };

      setSession(newSession);
      setDetectionHistory([]);

    } catch (err) {
      console.error('Erreur démarrage session:', err);
      setError('Impossible de démarrer la session');
    } finally {
      setLoading(false);
    }
  }, [course]);

  /**
   * Arrête la session de présence
   */
  const stopSession = useCallback(async () => {
    if (!session) return;

    try {
      setLoading(true);

      // Marquer les étudiants non détectés comme absents
      const absentStudents = students.filter(
        student => !session.detectedStudents.has(student.id)
      );

      const absentRecords = await Promise.all(
        absentStudents.map(async (student) => {
          const attendanceData: Omit<AttendanceRecord, 'id'> = {
            student,
            course,
            date: new Date().toISOString().split('T')[0],
            time: new Date().toISOString(),
            status: AttendanceStatus.ABSENT,
            method: AttendanceMethod.FACE_RECOGNITION,
            notes: 'Marqué automatiquement comme absent',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          const recordId = await supabaseService.recordAttendance(attendanceData);
          return { ...attendanceData, id: recordId };
        })
      );

      const updatedSession = {
        ...session,
        endTime: new Date(),
        status: 'completed' as const,
        attendanceRecords: [...session.attendanceRecords, ...absentRecords]
      };

      setSession(updatedSession);

      if (onAttendanceUpdate) {
        onAttendanceUpdate(updatedSession.attendanceRecords);
      }

    } catch (err) {
      console.error('Erreur arrêt session:', err);
      setError('Erreur lors de l\'arrêt de la session');
    } finally {
      setLoading(false);
    }
  }, [session, students, course, onAttendanceUpdate]);

  /**
   * Traite une détection de visage
   */
  const handleFaceDetection = useCallback(async (result: FaceDetectionResult) => {
    if (!session || session.status !== 'active') return;

    for (const detection of result.detectedUsers) {
      const { user, confidence } = detection;
      
      // Vérifier si c'est un étudiant du cours
      const student = students.find(s => s.username === user.username);
      if (!student) continue;

      // Vérifier si déjà détecté dans cette session
      if (session.detectedStudents.has(student.id)) continue;

      // Ajouter à l'historique de détection
      setDetectionHistory(prev => [...prev, {
        userId: student.id,
        timestamp: new Date(),
        confidence
      }]);

      // Déterminer le statut (présent ou en retard)
      const now = new Date();
      const sessionStart = session.startTime;
      const minutesLate = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));
      
      const status = minutesLate <= lateThresholdMinutes 
        ? AttendanceStatus.PRESENT 
        : AttendanceStatus.LATE;

      try {
        // Enregistrer la présence
        const attendanceData: Omit<AttendanceRecord, 'id'> = {
          student,
          course,
          date: now.toISOString().split('T')[0],
          time: now.toISOString(),
          status,
          method: AttendanceMethod.FACE_RECOGNITION,
          confidence,
          notes: `Détecté automatiquement (confiance: ${Math.round(confidence * 100)}%)`,
          createdAt: now.toISOString(),
          updatedAt: now.toISOString()
        };

        const recordId = await supabaseService.recordAttendance(attendanceData);
        const newRecord = { ...attendanceData, id: recordId };

        // Mettre à jour la session
        setSession(prev => {
          if (!prev) return prev;
          
          const updatedSession = {
            ...prev,
            detectedStudents: new Set(Array.from(prev.detectedStudents).concat(student.id)),
            attendanceRecords: [...prev.attendanceRecords, newRecord]
          };

          if (onAttendanceUpdate) {
            onAttendanceUpdate(updatedSession.attendanceRecords);
          }

          return updatedSession;
        });

      } catch (err) {
        console.error('Erreur enregistrement présence:', err);
      }
    }
  }, [session, students, course, lateThresholdMinutes, onAttendanceUpdate]);

  // Charger les étudiants au montage
  useEffect(() => {
    loadStudents();
  }, [loadStudents]);

  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return <CheckCircleIcon color="success" />;
      case AttendanceStatus.LATE:
        return <WarningIcon color="warning" />;
      case AttendanceStatus.ABSENT:
        return <CancelIcon color="error" />;
      default:
        return <ScheduleIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'success';
      case AttendanceStatus.LATE:
        return 'warning';
      case AttendanceStatus.ABSENT:
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Stack spacing={3}>
      {/* En-tête de contrôle */}
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Système de Présence Automatique
            </Typography>
            
            <Stack direction="row" spacing={2}>
              <IconButton onClick={() => setSettingsOpen(true)}>
                <SettingsIcon />
              </IconButton>
              
              {!session ? (
                <Button
                  variant="contained"
                  startIcon={<PlayIcon />}
                  onClick={startSession}
                  disabled={loading}
                >
                  Démarrer Session
                </Button>
              ) : session.status === 'active' ? (
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<StopIcon />}
                  onClick={stopSession}
                  disabled={loading}
                >
                  Arrêter Session
                </Button>
              ) : (
                <Chip 
                  label="Session Terminée" 
                  color="success" 
                  icon={<CheckCircleIcon />}
                />
              )}
            </Stack>
          </Box>

          {/* Informations du cours */}
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <Chip label={`Cours: ${course.name}`} color="primary" />
            <Chip label={`Étudiants: ${students.length}`} color="secondary" />
            {session && (
              <>
                <Chip 
                  label={`Détectés: ${session.detectedStudents.size}`} 
                  color="success" 
                />
                <Chip 
                  label={`Début: ${session.startTime.toLocaleTimeString()}`} 
                  color="info" 
                />
              </>
            )}
          </Stack>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError(null)}>
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Caméra de reconnaissance */}
      {session?.status === 'active' && (
        <FaceDetectionCamera
          onDetection={handleFaceDetection}
          autoDetect={true}
          showControls={false}
        />
      )}

      {/* Tableau des présences */}
      {session && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              État des Présences
            </Typography>
            
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Étudiant</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Heure</TableCell>
                    <TableCell>Confiance</TableCell>
                    <TableCell>Méthode</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {students.map((student) => {
                    const record = session.attendanceRecords.find(
                      r => r.student.id === student.id
                    );
                    
                    return (
                      <TableRow key={student.id}>
                        <TableCell>
                          {student.firstName} {student.lastName}
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1} alignItems="center">
                            {record ? (
                              <>
                                {getStatusIcon(record.status)}
                                <Chip
                                  label={record.status}
                                  color={getStatusColor(record.status) as any}
                                  size="small"
                                />
                              </>
                            ) : session.status === 'active' ? (
                              <Chip label="En attente" color="default" size="small" />
                            ) : (
                              <>
                                {getStatusIcon(AttendanceStatus.ABSENT)}
                                <Chip label="Absent" color="error" size="small" />
                              </>
                            )}
                          </Stack>
                        </TableCell>
                        <TableCell>
                          {record ? new Date(record.time).toLocaleTimeString() : '-'}
                        </TableCell>
                        <TableCell>
                          {record?.confidence ? `${Math.round(record.confidence * 100)}%` : '-'}
                        </TableCell>
                        <TableCell>
                          {record ? record.method : '-'}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Dialog des paramètres */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)}>
        <DialogTitle>Paramètres de Session</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1, minWidth: 300 }}>
            <FormControl fullWidth>
              <InputLabel>Seuil de Retard (minutes)</InputLabel>
              <Select
                value={lateThresholdMinutes}
                onChange={(e) => setLateThresholdMinutes(Number(e.target.value))}
                label="Seuil de Retard (minutes)"
              >
                <MenuItem value={5}>5 minutes</MenuItem>
                <MenuItem value={10}>10 minutes</MenuItem>
                <MenuItem value={15}>15 minutes</MenuItem>
                <MenuItem value={20}>20 minutes</MenuItem>
                <MenuItem value={30}>30 minutes</MenuItem>
              </Select>
            </FormControl>
            
            <Typography variant="body2" color="text.secondary">
              Les étudiants arrivant après ce délai seront marqués comme "En retard"
            </Typography>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
};

export default AutoAttendanceSystem;
