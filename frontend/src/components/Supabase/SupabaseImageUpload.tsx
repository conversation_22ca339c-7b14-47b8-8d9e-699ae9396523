import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Alert,
  CircularProgress,
  Avatar,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Person as PersonIcon,
  Face as FaceIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { supabaseService } from '../../services/supabaseService';

interface UploadedImage {
  url: string;
  type: 'profile' | 'face';
  uploadedAt: Date;
}

const SupabaseImageUpload: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);

  // Simuler un ID utilisateur pour les tests
  const testUserId = 'test-user-123';

  const handleFileUpload = async (file: File, type: 'profile' | 'face') => {
    if (!file) return;

    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      setMessage({ type: 'error', text: 'Veuillez sélectionner un fichier image valide.' });
      return;
    }

    // Vérifier la taille (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setMessage({ type: 'error', text: 'La taille du fichier ne doit pas dépasser 5MB.' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      let imageUrl: string;
      
      if (type === 'profile') {
        imageUrl = await supabaseService.uploadProfileImage(testUserId, file);
      } else {
        imageUrl = await supabaseService.uploadFaceImage(testUserId, file);
      }

      // Ajouter l'image à la liste
      const newImage: UploadedImage = {
        url: imageUrl,
        type,
        uploadedAt: new Date()
      };

      setUploadedImages(prev => [...prev, newImage]);
      setMessage({ 
        type: 'success', 
        text: `Image ${type === 'profile' ? 'de profil' : 'faciale'} uploadée avec succès !` 
      });

    } catch (error) {
      console.error('Erreur upload:', error);
      setMessage({ 
        type: 'error', 
        text: `Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}` 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteImage = async (imageUrl: string) => {
    setLoading(true);
    try {
      await supabaseService.deleteImage(imageUrl);
      setUploadedImages(prev => prev.filter(img => img.url !== imageUrl));
      setMessage({ type: 'success', text: 'Image supprimée avec succès !' });
    } catch (error) {
      console.error('Erreur suppression:', error);
      setMessage({ 
        type: 'error', 
        text: `Erreur lors de la suppression: ${error instanceof Error ? error.message : 'Erreur inconnue'}` 
      });
    } finally {
      setLoading(false);
    }
  };

  const FileUploadButton: React.FC<{ type: 'profile' | 'face'; icon: React.ReactNode; label: string }> = ({ type, icon, label }) => (
    <Button
      variant="contained"
      component="label"
      startIcon={icon}
      disabled={loading}
      sx={{ mb: 2, width: '100%' }}
    >
      {label}
      <input
        type="file"
        hidden
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleFileUpload(file, type);
          }
        }}
      />
    </Button>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Test Upload Images Supabase
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Testez l'upload d'images vers le bucket Supabase "images"
      </Typography>

      {/* Messages */}
      {message && (
        <Alert severity={message.type} sx={{ mb: 3 }} onClose={() => setMessage(null)}>
          {message.text}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Section Upload */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upload d'Images
              </Typography>
              
              <FileUploadButton
                type="profile"
                icon={<PersonIcon />}
                label="Upload Image de Profil"
              />
              
              <FileUploadButton
                type="face"
                icon={<FaceIcon />}
                label="Upload Image Faciale"
              />

              {loading && (
                <Box display="flex" justifyContent="center" mt={2}>
                  <CircularProgress />
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Section Images Uploadées */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Images Uploadées ({uploadedImages.length})
              </Typography>
              
              {uploadedImages.length === 0 ? (
                <Typography color="text.secondary">
                  Aucune image uploadée pour le moment
                </Typography>
              ) : (
                <Box>
                  {uploadedImages.map((image, index) => (
                    <Paper key={index} sx={{ p: 2, mb: 2 }}>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          src={image.url}
                          sx={{ width: 60, height: 60 }}
                        />
                        <Box flex={1}>
                          <Typography variant="subtitle2">
                            {image.type === 'profile' ? 'Image de Profil' : 'Image Faciale'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {image.uploadedAt.toLocaleString()}
                          </Typography>
                        </Box>
                        <Button
                          size="small"
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={() => handleDeleteImage(image.url)}
                          disabled={loading}
                        >
                          Supprimer
                        </Button>
                      </Box>
                    </Paper>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Informations techniques */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Informations Techniques
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="body2" component="div">
            <strong>Bucket Supabase:</strong> images<br />
            <strong>Politiques configurées:</strong><br />
            • SELECT: Lecture publique des images<br />
            • INSERT: Upload pour utilisateurs authentifiés<br />
            <strong>Formats acceptés:</strong> JPG, PNG, GIF, WebP<br />
            <strong>Taille max:</strong> 5MB<br />
            <strong>ID utilisateur test:</strong> {testUserId}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SupabaseImageUpload;
