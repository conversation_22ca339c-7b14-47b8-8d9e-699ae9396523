import React, { useState, useEffect } from 'react';
import { supabase } from '../config/supabase';
import { Box, Button, Typography, Alert, CircularProgress } from '@mui/material';

const TestConnection: React.FC = () => {
  const [status, setStatus] = useState<string>('En attente...');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testConnection = async () => {
    setLoading(true);
    setResults([]);
    setStatus('Test en cours...');

    try {
      // Test 1: Connexion de base
      addResult('🧪 Test de connexion de base...');
      const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true });
      
      if (error) {
        addResult(`❌ Erreur de connexion: ${error.message}`);
        setStatus('Échec de connexion');
        return;
      }
      
      addResult(`✅ Connexion réussie! Nombre d'utilisateurs: ${data}`);

      // Test 2: Session actuelle
      addResult('🔍 Vérification de la session...');
      const { data: session } = await supabase.auth.getSession();
      addResult(`Session: ${session.session ? 'Connecté' : 'Non connecté'}`);

      // Test 3: Tentative de connexion
      addResult('🔐 Test de connexion admin...');
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'admin123'
      });

      if (authError) {
        addResult(`❌ Erreur de connexion: ${authError.message}`);
      } else {
        addResult(`✅ Connexion réussie pour: ${authData.user?.email}`);
        
        // Test 4: Récupération du profil
        addResult('👤 Récupération du profil...');
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('email', '<EMAIL>')
          .single();

        if (userError) {
          addResult(`❌ Erreur profil: ${userError.message}`);
        } else {
          addResult(`✅ Profil trouvé: ${userData?.email} (${userData?.role})`);
        }

        // Déconnexion
        await supabase.auth.signOut();
        addResult('👋 Déconnexion effectuée');
      }

      setStatus('Test terminé');
    } catch (error) {
      addResult(`❌ Erreur générale: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
      setStatus('Erreur');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Test automatique au chargement
    testConnection();
  }, []);

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Test de Connexion Supabase
      </Typography>
      
      <Box mb={2}>
        <Typography variant="h6">
          Statut: {status}
        </Typography>
        {loading && <CircularProgress size={20} />}
      </Box>

      <Button 
        variant="contained" 
        onClick={testConnection} 
        disabled={loading}
        sx={{ mb: 2 }}
      >
        Relancer le test
      </Button>

      <Box>
        <Typography variant="h6" gutterBottom>
          Résultats:
        </Typography>
        {results.map((result, index) => (
          <Alert 
            key={index} 
            severity={result.includes('❌') ? 'error' : result.includes('✅') ? 'success' : 'info'}
            sx={{ mb: 1 }}
          >
            {result}
          </Alert>
        ))}
      </Box>
    </Box>
  );
};

export default TestConnection;
