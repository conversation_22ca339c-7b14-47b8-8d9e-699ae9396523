import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Stack,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Videocam as VideocamIcon,
  VideocamOff as VideocamOffIcon,
  PhotoCamera as PhotoCameraIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { faceRecognitionService } from '../../services/faceRecognitionService';
import { FaceDetectionResult, User } from '../../types';

interface FaceDetectionCameraProps {
  onDetection?: (result: FaceDetectionResult) => void;
  onUserDetected?: (users: User[]) => void;
  autoDetect?: boolean;
  showControls?: boolean;
  width?: number;
  height?: number;
}

const FaceDetectionCamera: React.FC<FaceDetectionCameraProps> = ({
  onDetection,
  onUserDetected,
  autoDetect = true,
  showControls = true,
  width = 640,
  height = 480
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [isStreaming, setIsStreaming] = useState(false);
  const [isDetecting, setIsDetecting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastDetection, setLastDetection] = useState<FaceDetectionResult | null>(null);
  const [stats, setStats] = useState(faceRecognitionService.getStats());
  const [settingsOpen, setSettingsOpen] = useState(false);

  /**
   * Initialise la caméra
   */
  const startCamera = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Demander l'accès à la caméra
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: width },
          height: { ideal: height },
          facingMode: 'user'
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsStreaming(true);
      }

    } catch (err) {
      console.error('Erreur accès caméra:', err);
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
    } finally {
      setLoading(false);
    }
  }, [width, height]);

  /**
   * Arrête la caméra
   */
  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setIsStreaming(false);
    stopDetection();
  }, []);

  /**
   * Démarre la détection automatique
   */
  const startDetection = useCallback(async () => {
    if (!isStreaming || isDetecting) return;

    try {
      setIsDetecting(true);
      setError(null);

      // Initialiser le service si nécessaire
      await faceRecognitionService.initialize();
      setStats(faceRecognitionService.getStats());

      // Démarrer la détection en boucle
      detectionIntervalRef.current = setInterval(async () => {
        if (videoRef.current && canvasRef.current) {
          await performDetection();
        }
      }, 1000); // Détection toutes les secondes

    } catch (err) {
      console.error('Erreur démarrage détection:', err);
      setError('Erreur lors du démarrage de la détection');
      setIsDetecting(false);
    }
  }, [isStreaming, isDetecting]);

  /**
   * Arrête la détection automatique
   */
  const stopDetection = useCallback(() => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    setIsDetecting(false);
  }, []);

  /**
   * Effectue une détection sur l'image actuelle
   */
  const performDetection = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return;

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) return;

      // Capturer l'image de la vidéo
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0);

      // Effectuer la détection
      const result = await faceRecognitionService.detectFaces(canvas);
      
      setLastDetection(result);
      
      // Callbacks
      if (onDetection) {
        onDetection(result);
      }
      
      if (onUserDetected && result.detectedUsers.length > 0) {
        const users = result.detectedUsers.map(detection => detection.user);
        onUserDetected(users);
      }

      // Dessiner les rectangles de détection
      drawDetections(ctx, result);

    } catch (err) {
      console.error('Erreur détection:', err);
    }
  }, [onDetection, onUserDetected]);

  /**
   * Dessine les rectangles de détection sur le canvas
   */
  const drawDetections = (ctx: CanvasRenderingContext2D, result: FaceDetectionResult) => {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    // Redessiner l'image vidéo
    if (videoRef.current) {
      ctx.drawImage(videoRef.current, 0, 0);
    }

    // Dessiner les détections
    result.detectedUsers.forEach(detection => {
      const { boundingBox, confidence, user } = detection;
      
      // Rectangle de détection
      ctx.strokeStyle = confidence > 0.7 ? '#4caf50' : '#ff9800';
      ctx.lineWidth = 3;
      ctx.strokeRect(boundingBox.x, boundingBox.y, boundingBox.width, boundingBox.height);
      
      // Label avec nom et confiance
      const label = `${user.firstName} ${user.lastName} (${Math.round(confidence * 100)}%)`;
      ctx.fillStyle = confidence > 0.7 ? '#4caf50' : '#ff9800';
      ctx.fillRect(boundingBox.x, boundingBox.y - 30, ctx.measureText(label).width + 10, 25);
      
      ctx.fillStyle = 'white';
      ctx.font = '14px Arial';
      ctx.fillText(label, boundingBox.x + 5, boundingBox.y - 10);
    });
  };

  /**
   * Prend une photo manuelle
   */
  const takeSnapshot = useCallback(async () => {
    await performDetection();
  }, [performDetection]);

  // Initialisation et nettoyage
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  // Auto-démarrage de la détection
  useEffect(() => {
    if (isStreaming && autoDetect && !isDetecting) {
      startDetection();
    }
  }, [isStreaming, autoDetect, isDetecting, startDetection]);

  return (
    <Card>
      <CardContent>
        <Stack spacing={2}>
          {/* En-tête */}
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Reconnaissance Faciale
            </Typography>
            
            {showControls && (
              <Stack direction="row" spacing={1}>
                <IconButton onClick={() => setSettingsOpen(true)}>
                  <SettingsIcon />
                </IconButton>
                
                <Button
                  variant={isStreaming ? "outlined" : "contained"}
                  startIcon={isStreaming ? <VideocamOffIcon /> : <VideocamIcon />}
                  onClick={isStreaming ? stopCamera : startCamera}
                  disabled={loading}
                >
                  {isStreaming ? 'Arrêter' : 'Démarrer'}
                </Button>
              </Stack>
            )}
          </Box>

          {/* Messages d'erreur */}
          {error && (
            <Alert severity="error" onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          {/* Statistiques */}
          <Stack direction="row" spacing={1} flexWrap="wrap">
            <Chip 
              label={`${stats.registeredUsers} utilisateurs`} 
              color="primary" 
              size="small" 
            />
            <Chip 
              label={`${stats.totalEncodings} encodages`} 
              color="secondary" 
              size="small" 
            />
            {isDetecting && (
              <Chip 
                label="Détection active" 
                color="success" 
                size="small"
                icon={<CircularProgress size={16} />}
              />
            )}
          </Stack>

          {/* Zone vidéo */}
          <Box position="relative" display="inline-block">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              style={{
                width: '100%',
                maxWidth: width,
                height: 'auto',
                borderRadius: 8,
                display: isStreaming ? 'block' : 'none'
              }}
            />
            
            <canvas
              ref={canvasRef}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                borderRadius: 8
              }}
            />

            {loading && (
              <Box
                position="absolute"
                top="50%"
                left="50%"
                sx={{ transform: 'translate(-50%, -50%)' }}
              >
                <CircularProgress />
              </Box>
            )}
          </Box>

          {/* Contrôles */}
          {showControls && isStreaming && (
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="outlined"
                startIcon={<PhotoCameraIcon />}
                onClick={takeSnapshot}
              >
                Capturer
              </Button>
              
              <Button
                variant={isDetecting ? "contained" : "outlined"}
                startIcon={<RefreshIcon />}
                onClick={isDetecting ? stopDetection : startDetection}
                color={isDetecting ? "error" : "primary"}
              >
                {isDetecting ? 'Arrêter Détection' : 'Démarrer Détection'}
              </Button>
            </Stack>
          )}

          {/* Dernière détection */}
          {lastDetection && (
            <Alert 
              severity={lastDetection.detectedUsers.length > 0 ? "success" : "info"}
              sx={{ mt: 2 }}
            >
              <Typography variant="body2">
                <strong>Dernière détection:</strong> {lastDetection.message}
              </Typography>
              {lastDetection.detectedUsers.length > 0 && (
                <Box mt={1}>
                  {lastDetection.detectedUsers.map((detection, index) => (
                    <Chip
                      key={index}
                      label={`${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`}
                      color="success"
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>
              )}
            </Alert>
          )}
        </Stack>
      </CardContent>

      {/* Dialog des paramètres */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)}>
        <DialogTitle>Paramètres de Reconnaissance</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ pt: 1 }}>
            <Typography variant="body2">
              <strong>Modèles chargés:</strong> {stats.isInitialized ? 'Oui' : 'Non'}
            </Typography>
            <Typography variant="body2">
              <strong>Utilisateurs enregistrés:</strong> {stats.registeredUsers}
            </Typography>
            <Typography variant="body2">
              <strong>Total encodages:</strong> {stats.totalEncodings}
            </Typography>
            <Typography variant="body2">
              <strong>Résolution:</strong> {width}x{height}
            </Typography>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};

export default FaceDetectionCamera;
