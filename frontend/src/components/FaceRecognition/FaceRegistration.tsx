import React, { useRef, useState, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>l,
  StepContent,
  CircularProgress,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PhotoCamera as PhotoCameraIcon,
  Face as FaceIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { faceRecognitionService } from '../../services/faceRecognitionService';
import { User } from '../../types';

interface FaceRegistrationProps {
  user: User;
  onRegistrationComplete?: (success: boolean) => void;
  showExistingEncodings?: boolean;
}

const FaceRegistration: React.FC<FaceRegistrationProps> = ({
  user,
  onRegistrationComplete,
  showExistingEncodings = true
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const [activeStep, setActiveStep] = useState(0);
  const [isStreaming, setIsStreaming] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [capturedImages, setCapturedImages] = useState<string[]>([]);
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const steps = [
    {
      label: 'Préparation',
      description: 'Positionnez-vous face à la caméra dans un endroit bien éclairé'
    },
    {
      label: 'Capture d\'images',
      description: 'Prenez 3-5 photos de votre visage sous différents angles'
    },
    {
      label: 'Validation',
      description: 'Vérifiez et confirmez vos photos faciales'
    }
  ];

  /**
   * Démarre la caméra
   */
  const startCamera = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsStreaming(true);
        setActiveStep(1);
      }

    } catch (err) {
      console.error('Erreur accès caméra:', err);
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Arrête la caméra
   */
  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setIsStreaming(false);
  }, []);

  /**
   * Capture une image depuis la vidéo
   */
  const captureImage = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return;

    try {
      setLoading(true);
      setError(null);

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) return;

      // Capturer l'image
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0);

      // Convertir en blob puis en URL
      canvas.toBlob((blob) => {
        if (blob) {
          const imageUrl = URL.createObjectURL(blob);
          setCapturedImages(prev => [...prev, imageUrl]);
          
          if (capturedImages.length >= 2) { // 3 images au total
            setActiveStep(2);
          }
        }
      }, 'image/jpeg', 0.8);

    } catch (err) {
      console.error('Erreur capture:', err);
      setError('Erreur lors de la capture d\'image');
    } finally {
      setLoading(false);
    }
  }, [capturedImages.length]);

  /**
   * Supprime une image capturée
   */
  const removeImage = (index: number) => {
    setCapturedImages(prev => {
      const newImages = [...prev];
      URL.revokeObjectURL(newImages[index]); // Libérer la mémoire
      newImages.splice(index, 1);
      return newImages;
    });
    
    if (capturedImages.length <= 3) {
      setActiveStep(1);
    }
  };

  /**
   * Upload depuis un fichier
   */
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      setError('Veuillez sélectionner un fichier image valide');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Créer une URL pour l'image
      const imageUrl = URL.createObjectURL(file);
      setCapturedImages(prev => [...prev, imageUrl]);

      // Enregistrer directement l'encodage
      const img = new Image();
      img.onload = async () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const result = await faceRecognitionService.registerFaceEncoding(
          user.id,
          canvas,
          file
        );

        if (result.success) {
          setSuccess('Photo faciale enregistrée avec succès !');
          setRegistrationComplete(true);
          if (onRegistrationComplete) {
            onRegistrationComplete(true);
          }
        } else {
          setError(result.message);
        }
      };
      img.src = imageUrl;

    } catch (err) {
      console.error('Erreur upload:', err);
      setError('Erreur lors du traitement de l\'image');
    } finally {
      setLoading(false);
    }
  }, [user.id, onRegistrationComplete]);

  /**
   * Confirme l'enregistrement des images
   */
  const confirmRegistration = async () => {
    if (capturedImages.length === 0) return;

    try {
      setLoading(true);
      setError(null);

      // Enregistrer chaque image capturée
      let successCount = 0;
      
      for (let i = 0; i < capturedImages.length; i++) {
        const imageUrl = capturedImages[i];
        
        // Convertir l'URL en canvas
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        await new Promise((resolve, reject) => {
          img.onload = async () => {
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) return reject('Canvas non supporté');

              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0);

              const result = await faceRecognitionService.registerFaceEncoding(
                user.id,
                canvas
              );

              if (result.success) {
                successCount++;
              }
              resolve(result);
            } catch (err) {
              reject(err);
            }
          };
          img.onerror = reject;
          img.src = imageUrl;
        });
      }

      if (successCount > 0) {
        setSuccess(`${successCount} photo(s) faciale(s) enregistrée(s) avec succès !`);
        setRegistrationComplete(true);
        stopCamera();
        
        if (onRegistrationComplete) {
          onRegistrationComplete(true);
        }
      } else {
        setError('Aucune photo n\'a pu être enregistrée');
      }

    } catch (err) {
      console.error('Erreur enregistrement:', err);
      setError('Erreur lors de l\'enregistrement des photos faciales');
    } finally {
      setLoading(false);
      setConfirmDialogOpen(false);
    }
  };

  /**
   * Recommence le processus
   */
  const restart = () => {
    // Nettoyer les URLs d'objets
    capturedImages.forEach(url => URL.revokeObjectURL(url));
    setCapturedImages([]);
    setActiveStep(0);
    setRegistrationComplete(false);
    setSuccess(null);
    setError(null);
    stopCamera();
  };

  // Nettoyage au démontage
  React.useEffect(() => {
    return () => {
      stopCamera();
      capturedImages.forEach(url => URL.revokeObjectURL(url));
    };
  }, [stopCamera, capturedImages]);

  if (registrationComplete) {
    return (
      <Card>
        <CardContent>
          <Stack spacing={3} alignItems="center">
            <CheckCircleIcon color="success" sx={{ fontSize: 64 }} />
            <Typography variant="h6" align="center">
              Enregistrement Facial Terminé !
            </Typography>
            <Typography color="text.secondary" align="center">
              Vos photos faciales ont été enregistrées avec succès. 
              Vous pouvez maintenant être reconnu automatiquement lors des sessions de présence.
            </Typography>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={restart}
            >
              Enregistrer de Nouvelles Photos
            </Button>
          </Stack>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Enregistrement Facial
        </Typography>
        
        <Typography color="text.secondary" sx={{ mb: 3 }}>
          Enregistrez vos photos faciales pour être reconnu automatiquement 
          lors des sessions de présence.
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>{step.label}</StepLabel>
              <StepContent>
                <Typography sx={{ mb: 2 }}>{step.description}</Typography>
                
                {index === 0 && (
                  <Stack spacing={2}>
                    <Button
                      variant="contained"
                      startIcon={<PhotoCameraIcon />}
                      onClick={startCamera}
                      disabled={loading}
                    >
                      {loading ? <CircularProgress size={20} /> : 'Démarrer la Caméra'}
                    </Button>
                    
                    <Typography variant="body2" color="text.secondary">
                      Ou
                    </Typography>
                    
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                    <Button
                      variant="outlined"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={loading}
                    >
                      Choisir une Photo
                    </Button>
                  </Stack>
                )}

                {index === 1 && isStreaming && (
                  <Stack spacing={2}>
                    <Box position="relative" display="inline-block">
                      <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        muted
                        style={{
                          width: '100%',
                          maxWidth: 400,
                          borderRadius: 8
                        }}
                      />
                      <canvas
                        ref={canvasRef}
                        style={{ display: 'none' }}
                      />
                    </Box>
                    
                    <Button
                      variant="contained"
                      startIcon={<FaceIcon />}
                      onClick={captureImage}
                      disabled={loading || capturedImages.length >= 5}
                    >
                      Capturer ({capturedImages.length}/5)
                    </Button>
                  </Stack>
                )}

                {index === 2 && (
                  <Stack spacing={2}>
                    <Typography variant="subtitle2">
                      Photos capturées ({capturedImages.length})
                    </Typography>
                    
                    <Stack direction="row" spacing={2} flexWrap="wrap">
                      {capturedImages.map((imageUrl, imgIndex) => (
                        <Box key={imgIndex} position="relative">
                          <Avatar
                            src={imageUrl}
                            sx={{ width: 80, height: 80 }}
                          />
                          <Button
                            size="small"
                            color="error"
                            onClick={() => removeImage(imgIndex)}
                            sx={{
                              position: 'absolute',
                              top: -8,
                              right: -8,
                              minWidth: 24,
                              width: 24,
                              height: 24,
                              borderRadius: '50%'
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </Button>
                        </Box>
                      ))}
                    </Stack>

                    <Button
                      variant="contained"
                      onClick={() => setConfirmDialogOpen(true)}
                      disabled={capturedImages.length === 0 || loading}
                    >
                      Confirmer l'Enregistrement
                    </Button>
                  </Stack>
                )}
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </CardContent>

      {/* Dialog de confirmation */}
      <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>
        <DialogTitle>Confirmer l'Enregistrement</DialogTitle>
        <DialogContent>
          <Typography>
            Voulez-vous enregistrer ces {capturedImages.length} photo(s) faciale(s) ?
            Cette action remplacera vos anciens enregistrements faciaux.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>Annuler</Button>
          <Button 
            onClick={confirmRegistration}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Confirmer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};

export default FaceRegistration;
