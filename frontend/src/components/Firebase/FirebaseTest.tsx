/**
 * Composant de test pour Firebase
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import { firebaseService } from '../../services/firebaseService';
import { useFirebase } from '../../hooks/useFirebase';
import { UserRole } from '../../types';

const FirebaseTest: React.FC = () => {
  const { firebaseUser, user, loading, error, signIn, signUp, logout } = useFirebase();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  
  // Formulaire de test
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('test123456');

  useEffect(() => {
    testFirebaseConnection();
  }, []);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()} - ${message}`]);
  };

  const testFirebaseConnection = async () => {
    setIsTestingConnection(true);
    addTestResult('🔄 Test de connexion Firebase...');

    try {
      // Test 1: Connexion à Firestore
      const stats = await firebaseService.getGlobalStats();
      addTestResult('✅ Connexion Firestore réussie');
      addTestResult(`📊 Statistiques: ${JSON.stringify(stats)}`);

      // Test 2: Test de lecture des utilisateurs
      const users = await firebaseService.getUsers();
      addTestResult(`👥 ${users.length} utilisateurs trouvés`);

      // Test 3: Test de lecture des cours
      const courses = await firebaseService.getCourses();
      addTestResult(`📚 ${courses.length} cours trouvés`);

      addTestResult('✅ Tous les tests Firebase réussis !');
    } catch (err: any) {
      addTestResult(`❌ Erreur Firebase: ${err.message}`);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleTestSignUp = async () => {
    try {
      addTestResult('🔄 Test d\'inscription...');
      await signUp(email, password, {
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.STUDENT,
        username: 'testuser'
      });
      addTestResult('✅ Inscription réussie');
    } catch (err: any) {
      addTestResult(`❌ Erreur d'inscription: ${err.message}`);
    }
  };

  const handleTestSignIn = async () => {
    try {
      addTestResult('🔄 Test de connexion...');
      await signIn(email, password);
      addTestResult('✅ Connexion réussie');
    } catch (err: any) {
      addTestResult(`❌ Erreur de connexion: ${err.message}`);
    }
  };

  const handleTestLogout = async () => {
    try {
      addTestResult('🔄 Test de déconnexion...');
      await logout();
      addTestResult('✅ Déconnexion réussie');
    } catch (err: any) {
      addTestResult(`❌ Erreur de déconnexion: ${err.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        Test Firebase - PresencePro
      </Typography>

      {/* État de connexion Firebase */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            État de la connexion Firebase
          </Typography>
          
          {loading && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <CircularProgress size={20} />
              <Typography>Chargement...</Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {firebaseUser ? (
            <Alert severity="success">
              <Typography variant="subtitle2">
                Utilisateur connecté: {firebaseUser.email}
              </Typography>
              {user && (
                <Typography variant="body2">
                  Profil: {user.fullName} ({user.roleDisplay})
                </Typography>
              )}
            </Alert>
          ) : (
            <Alert severity="info">
              Aucun utilisateur connecté
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Tests d'authentification */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Tests d'authentification
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              label="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              size="small"
              sx={{ flex: 1 }}
            />
            <TextField
              label="Mot de passe"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              size="small"
              sx={{ flex: 1 }}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button 
              variant="contained" 
              onClick={handleTestSignUp}
              disabled={loading}
            >
              Test Inscription
            </Button>
            <Button 
              variant="contained" 
              onClick={handleTestSignIn}
              disabled={loading}
            >
              Test Connexion
            </Button>
            <Button 
              variant="outlined" 
              onClick={handleTestLogout}
              disabled={loading || !firebaseUser}
            >
              Test Déconnexion
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Tests de connexion */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Tests de connexion Firebase
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <Button 
              variant="contained" 
              onClick={testFirebaseConnection}
              disabled={isTestingConnection}
            >
              {isTestingConnection ? 'Test en cours...' : 'Tester la connexion'}
            </Button>
            <Button 
              variant="outlined" 
              onClick={clearResults}
            >
              Effacer les résultats
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Résultats des tests */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Résultats des tests
          </Typography>
          
          {testResults.length === 0 ? (
            <Typography color="text.secondary">
              Aucun test exécuté
            </Typography>
          ) : (
            <List dense>
              {testResults.map((result, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemText 
                    primary={result}
                    primaryTypographyProps={{ 
                      variant: 'body2',
                      fontFamily: 'monospace'
                    }}
                  />
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default FirebaseTest;
