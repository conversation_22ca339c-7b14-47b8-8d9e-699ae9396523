import React, { useState } from 'react';
import { Box, <PERSON><PERSON>, <PERSON>pography, <PERSON><PERSON>ield, Alert } from '@mui/material';
import { supabase } from '../config/supabase';

const DebugAuth: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testLogin = async () => {
    setLoading(true);
    setResult('');

    try {
      console.log('🔐 Tentative de connexion avec:', email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('❌ Erreur de connexion:', error);
        setResult(`Erreur: ${error.message}`);
        return;
      }

      console.log('✅ Connexion réussie:', data);
      setResult(`Connexion réussie! User ID: ${data.user?.id}, Email: ${data.user?.email}`);

      // Vérifier la session
      const { data: session } = await supabase.auth.getSession();
      console.log('📋 Session actuelle:', session);

    } catch (err) {
      console.error('❌ Erreur générale:', err);
      setResult(`Erreur générale: ${err instanceof Error ? err.message : 'Erreur inconnue'}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogout = async () => {
    try {
      await supabase.auth.signOut();
      setResult('Déconnexion effectuée');
      console.log('👋 Déconnexion effectuée');
    } catch (err) {
      console.error('❌ Erreur de déconnexion:', err);
    }
  };

  const checkSession = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      console.log('📋 Session actuelle:', session);
      setResult(session ? `Session active: ${session.user?.email}` : 'Aucune session active');
    } catch (err) {
      console.error('❌ Erreur de vérification de session:', err);
    }
  };

  return (
    <Box p={3} maxWidth={600}>
      <Typography variant="h4" gutterBottom>
        Debug Authentification
      </Typography>
      
      <Box mb={2}>
        <TextField
          fullWidth
          label="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Mot de passe"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          margin="normal"
        />
      </Box>

      <Box mb={2} display="flex" gap={2}>
        <Button 
          variant="contained" 
          onClick={testLogin} 
          disabled={loading}
        >
          {loading ? 'Connexion...' : 'Se connecter'}
        </Button>
        
        <Button 
          variant="outlined" 
          onClick={testLogout}
        >
          Se déconnecter
        </Button>
        
        <Button 
          variant="outlined" 
          onClick={checkSession}
        >
          Vérifier session
        </Button>
      </Box>

      {result && (
        <Alert severity={result.includes('Erreur') ? 'error' : 'success'}>
          {result}
        </Alert>
      )}

      <Box mt={3}>
        <Typography variant="body2" color="text.secondary">
          Ouvrez la console du navigateur pour voir les logs détaillés.
        </Typography>
      </Box>
    </Box>
  );
};

export default DebugAuth;
