/**
 * <PERSON>u latéral pour PresencePro
 */

import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Box,
  Collapse,
} from '@mui/material';
import {
  Dashboard,
  People,
  School,
  EventNote,
  Assessment,
  Settings,
  Face,
  PersonAdd,
  GroupAdd,
  BookOnline,
  CalendarToday,
  BarChart,
  PieChart,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
  width: number;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactElement;
  path: string;
  roles: UserRole[];
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Tableau de bord',
    icon: <Dashboard />,
    path: '/dashboard',
    roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],
  },
  {
    id: 'users',
    label: 'Gestion des utilisateurs',
    icon: <People />,
    path: '/users',
    roles: [UserRole.ADMIN],
    children: [
      {
        id: 'users-list',
        label: 'Liste des utilisateurs',
        icon: <People />,
        path: '/users',
        roles: [UserRole.ADMIN],
      },
      {
        id: 'users-create',
        label: 'Créer un utilisateur',
        icon: <PersonAdd />,
        path: '/users/create',
        roles: [UserRole.ADMIN],
      },
      {
        id: 'groups',
        label: 'Groupes d\'étudiants',
        icon: <GroupAdd />,
        path: '/groups',
        roles: [UserRole.ADMIN],
      },
    ],
  },
  {
    id: 'courses',
    label: 'Gestion des cours',
    icon: <School />,
    path: '/courses',
    roles: [UserRole.ADMIN, UserRole.TEACHER],
    children: [
      {
        id: 'courses-list',
        label: 'Liste des cours',
        icon: <BookOnline />,
        path: '/courses',
        roles: [UserRole.ADMIN, UserRole.TEACHER],
      },
      {
        id: 'courses-create',
        label: 'Créer un cours',
        icon: <School />,
        path: '/courses/create',
        roles: [UserRole.ADMIN],
      },
      {
        id: 'schedule',
        label: 'Planning',
        icon: <CalendarToday />,
        path: '/schedule',
        roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],
      },
    ],
  },
  {
    id: 'attendance',
    label: 'Gestion des présences',
    icon: <EventNote />,
    path: '/attendance',
    roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],
    children: [
      {
        id: 'attendance-take',
        label: 'Prendre les présences',
        icon: <Face />,
        path: '/attendance/take',
        roles: [UserRole.TEACHER],
      },
      {
        id: 'attendance-view',
        label: 'Consulter les présences',
        icon: <EventNote />,
        path: '/attendance',
        roles: [UserRole.ADMIN, UserRole.TEACHER, UserRole.STUDENT],
      },
    ],
  },
  {
    id: 'reports',
    label: 'Rapports et statistiques',
    icon: <Assessment />,
    path: '/reports',
    roles: [UserRole.ADMIN, UserRole.TEACHER],
    children: [
      {
        id: 'reports-attendance',
        label: 'Rapports de présence',
        icon: <BarChart />,
        path: '/reports/attendance',
        roles: [UserRole.ADMIN, UserRole.TEACHER],
      },
      {
        id: 'reports-stats',
        label: 'Statistiques globales',
        icon: <PieChart />,
        path: '/reports/statistics',
        roles: [UserRole.ADMIN],
      },
    ],
  },
  {
    id: 'settings',
    label: 'Paramètres',
    icon: <Settings />,
    path: '/settings',
    roles: [UserRole.ADMIN],
  },
  {
    id: 'firebase-test',
    label: 'Test Firebase',
    icon: <Assessment />,
    path: '/firebase-test',
    roles: [UserRole.ADMIN],
  },
];

const Sidebar: React.FC<SidebarProps> = ({ open, onClose, width }) => {
  const { user } = useAuth();
  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);

  const handleExpandClick = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const hasAccess = (item: MenuItem): boolean => {
    if (!user) return false;
    return item.roles.includes(user.role);
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    if (!hasAccess(item)) return null;

    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              if (hasChildren) {
                handleExpandClick(item.id);
              } else {
                // Navigation vers la page
                console.log(`Navigation vers: ${item.path}`);
                onClose();
              }
            }}
            sx={{
              pl: 2 + level * 2,
              minHeight: 48,
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 40,
                color: 'inherit',
              }}
            >
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.label}
              primaryTypographyProps={{
                fontSize: level > 0 ? '0.875rem' : '1rem',
                fontWeight: level === 0 ? 'medium' : 'normal',
              }}
            />
            {hasChildren && (
              isExpanded ? <ExpandLess /> : <ExpandMore />
            )}
          </ListItemButton>
        </ListItem>

        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children?.map(child => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawerContent = (
    <Box sx={{ overflow: 'auto' }}>
      <Toolbar />
      <List>
        {menuItems.map(item => renderMenuItem(item))}
      </List>
    </Box>
  );

  return (
    <Box
      component="nav"
      sx={{ width: { sm: width }, flexShrink: { sm: 0 } }}
    >
      {/* Drawer temporaire pour mobile */}
      <Drawer
        variant="temporary"
        open={open}
        onClose={onClose}
        ModalProps={{
          keepMounted: true, // Meilleure performance sur mobile
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: width,
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* Drawer permanent pour desktop */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: width,
          },
        }}
        open
      >
        {drawerContent}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
