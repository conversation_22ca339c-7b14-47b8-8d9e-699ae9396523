/**
 * Barre de navigation principale pour PresencePro
 */

import React, { useState } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Button,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Menu as MenuIcon,
  AccountCircle,
  Settings,
  Logout,
  School,
  Notifications,
  Badge,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';

interface NavbarProps {
  onMenuClick: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ onMenuClick }) => {
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    handleProfileMenuClose();
    await logout();
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return '#f44336'; // Rouge
      case UserRole.TEACHER:
        return '#2196f3'; // Bleu
      case UserRole.STUDENT:
        return '#4caf50'; // Vert
      default:
        return '#757575'; // Gris
    }
  };

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return 'Administrateur';
      case UserRole.TEACHER:
        return 'Enseignant';
      case UserRole.STUDENT:
        return 'Étudiant';
      default:
        return 'Utilisateur';
    }
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        {/* Bouton menu hamburger */}
        <IconButton
          color="inherit"
          aria-label="ouvrir le menu"
          onClick={onMenuClick}
          edge="start"
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>

        {/* Logo et titre */}
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
          <School sx={{ mr: 1 }} />
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ fontWeight: 'bold' }}
          >
            PresencePro
          </Typography>
        </Box>

        {/* Espace flexible */}
        <Box sx={{ flexGrow: 1 }} />

        {/* Notifications */}
        <IconButton color="inherit" sx={{ mr: 1 }}>
          <Badge badgeContent={3} color="error">
            <Notifications />
          </Badge>
        </IconButton>

        {/* Informations utilisateur */}
        {user && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Nom et rôle de l'utilisateur */}
            <Box sx={{ mr: 2, textAlign: 'right', display: { xs: 'none', sm: 'block' } }}>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                {user.fullName || `${user.firstName} ${user.lastName}`}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  color: getRoleColor(user.role),
                  fontWeight: 'medium',
                }}
              >
                {getRoleLabel(user.role)}
              </Typography>
            </Box>

            {/* Avatar et menu */}
            <IconButton
              size="large"
              aria-label="compte utilisateur"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleProfileMenuOpen}
              color="inherit"
            >
              {user.profilePicture ? (
                <Avatar
                  src={user.profilePicture}
                  alt={user.fullName}
                  sx={{ width: 32, height: 32 }}
                />
              ) : (
                <Avatar sx={{ width: 32, height: 32, bgcolor: getRoleColor(user.role) }}>
                  {user.firstName?.charAt(0)?.toUpperCase() || 'U'}
                </Avatar>
              )}
            </IconButton>

            {/* Menu déroulant du profil */}
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleProfileMenuClose}
              sx={{ mt: 1 }}
            >
              {/* Informations utilisateur dans le menu */}
              <Box sx={{ px: 2, py: 1, minWidth: 200 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  {user.fullName || `${user.firstName} ${user.lastName}`}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user.email}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: getRoleColor(user.role),
                    fontWeight: 'medium',
                  }}
                >
                  {getRoleLabel(user.role)}
                </Typography>
              </Box>

              <Divider />

              {/* Options du menu */}
              <MenuItem onClick={handleProfileMenuClose}>
                <ListItemIcon>
                  <AccountCircle fontSize="small" />
                </ListItemIcon>
                <ListItemText>Mon profil</ListItemText>
              </MenuItem>

              <MenuItem onClick={handleProfileMenuClose}>
                <ListItemIcon>
                  <Settings fontSize="small" />
                </ListItemIcon>
                <ListItemText>Paramètres</ListItemText>
              </MenuItem>

              <Divider />

              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <Logout fontSize="small" />
                </ListItemIcon>
                <ListItemText>Se déconnecter</ListItemText>
              </MenuItem>
            </Menu>
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
