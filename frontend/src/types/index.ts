/**
 * Types TypeScript pour PresencePro
 */

// Types d'utilisateur
export enum UserRole {
  ADMIN = 'admin',
  TEACHER = 'teacher',
  STUDENT = 'student'
}

export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: UserRole;
  roleDisplay: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  profilePicture?: string;
  isActive: boolean;
  dateJoined: string;
  lastLogin?: string;
}

export interface UserProfile {
  user: User;
  studentId?: string;
  employeeId?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  languagePreference: string;
  createdAt: string;
  updatedAt: string;
}

// Types d'authentification
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  token?: string;
  permissions?: string[];
}

// Types de cours
export interface Course {
  id: string;
  name: string;
  code: string;
  description?: string;
  teacher?: User;
  studentGroup: StudentGroup;
  schedule: CourseSchedule[];
  academicYear: string;
  semester: string;
  credits: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CourseSchedule {
  id: string;
  dayOfWeek: number; // 0 = Dimanche, 1 = Lundi, etc.
  startTime: string;
  endTime: string;
  room?: string;
  building?: string;
}

// Types de groupes d'étudiants
export interface StudentGroup {
  id: string;
  name: string;
  description?: string;
  academicYear: string;
  level: string;
  specialization?: string;
  studentCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Types de présence
export interface AttendanceRecord {
  id: string;
  student?: User;
  course?: Course;
  date: string;
  time: string;
  status: AttendanceStatus;
  method: AttendanceMethod;
  confidence?: number; // Pour la reconnaissance faciale
  notes?: string;
  createdAt: string;
  updatedAt?: string;
}

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  EXCUSED = 'excused'
}

export enum AttendanceMethod {
  MANUAL = 'manual',
  FACE_RECOGNITION = 'face_recognition',
  QR_CODE = 'qr_code'
}

// Types de session de cours
export interface CourseSession {
  id: string;
  course: Course;
  date: string;
  startTime: string;
  endTime: string;
  room?: string;
  building?: string;
  status: SessionStatus;
  attendanceRecords: AttendanceRecord[];
  createdAt: string;
  updatedAt: string;
}

export enum SessionStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Types de reconnaissance faciale
export interface FaceEncoding {
  id: string;
  user: User;
  encoding: number[];
  confidence: number;
  imageUrl?: string;
  createdAt: string;
  isActive: boolean;
}

export interface FaceDetectionResult {
  success: boolean;
  detectedUsers: {
    user: User;
    confidence: number;
    boundingBox: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }[];
  message?: string;
}

// Types de rapports
export interface AttendanceReport {
  student: User;
  course: Course;
  totalSessions: number;
  presentSessions: number;
  absentSessions: number;
  lateSessions: number;
  attendanceRate: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

export interface CourseAttendanceStats {
  course: Course;
  totalStudents: number;
  averageAttendanceRate: number;
  sessionStats: {
    date: string;
    presentCount: number;
    absentCount: number;
    lateCount: number;
    attendanceRate: number;
  }[];
}

// Types d'interface utilisateur
export interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalCourses: number;
  todaySessions: number;
  averageAttendanceRate: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'attendance' | 'course' | 'user' | 'system';
  message: string;
  timestamp: string;
  user?: User;
  course?: Course;
}

// Types de notification
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
}

// Types d'API
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Types de formulaires
export interface CreateUserForm {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  password: string;
  passwordConfirm: string;
}

export interface UpdateUserForm {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  isActive: boolean;
}

export interface CreateCourseForm {
  name: string;
  code: string;
  description?: string;
  teacherId: string;
  studentGroupId: string;
  academicYear: string;
  semester: string;
  credits: number;
  schedule: Omit<CourseSchedule, 'id'>[];
}

// Types de contexte
export interface AuthContextType {
  user: User | null;
  token: string | null;
  permissions: string[];
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  hasPermission: (permission: string) => boolean;
}

export interface ThemeContextType {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
  language: string;
  setLanguage: (language: string) => void;
}
