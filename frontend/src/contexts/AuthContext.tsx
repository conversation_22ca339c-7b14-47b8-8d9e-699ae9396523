/**
 * Contexte d'authentification pour PresencePro
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, LoginCredentials, AuthContextType, UserRole } from '../types';
import { apiService } from '../services/api';

// État initial
interface AuthState {
  user: User | null;
  token: string | null;
  permissions: string[];
  isLoading: boolean;
  isInitialized: boolean;
}

const initialState: AuthState = {
  user: null,
  token: null,
  permissions: [],
  isLoading: false,
  isInitialized: false,
};

// Actions
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string; permissions: string[] } }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'SET_INITIALIZED'; payload: boolean };

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        permissions: action.payload.permissions,
        isLoading: false,
        isInitialized: true,
      };
    
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        permissions: [],
        isLoading: false,
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload };
    
    default:
      return state;
  }
}

// Contexte
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialisation - vérifier si l'utilisateur est déjà connecté
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('authToken');
      const userData = localStorage.getItem('user');

      if (token && userData) {
        try {
          // Vérifier si le token est toujours valide
          const response = await apiService.getCurrentUser();
          if (response.success && response.data) {
            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: {
                user: response.data,
                token,
                permissions: JSON.parse(localStorage.getItem('permissions') || '[]'),
              },
            });
          } else {
            // Token invalide, nettoyer le localStorage
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            localStorage.removeItem('permissions');
          }
        } catch (error) {
          // Erreur de vérification, nettoyer le localStorage
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          localStorage.removeItem('permissions');
        }
      }
      
      dispatch({ type: 'SET_INITIALIZED', payload: true });
    };

    initializeAuth();
  }, []);

  // Fonction de connexion
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const response = await apiService.login(credentials);
      
      if (response.success && response.user && response.token) {
        // Sauvegarder dans le localStorage
        localStorage.setItem('authToken', response.token);
        localStorage.setItem('user', JSON.stringify(response.user));
        localStorage.setItem('permissions', JSON.stringify(response.permissions || []));

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            user: response.user,
            token: response.token,
            permissions: response.permissions || [],
          },
        });

        return true;
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  };

  // Fonction de déconnexion
  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      // Nettoyer le localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      localStorage.removeItem('permissions');

      dispatch({ type: 'LOGOUT' });
    }
  };

  // Fonction de mise à jour de l'utilisateur
  const updateUser = (userData: Partial<User>) => {
    dispatch({ type: 'UPDATE_USER', payload: userData });
    
    // Mettre à jour le localStorage
    if (state.user) {
      const updatedUser = { ...state.user, ...userData };
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  // Fonction pour vérifier les permissions
  const hasPermission = (permission: string): boolean => {
    return state.permissions.includes(permission);
  };

  const value: AuthContextType = {
    user: state.user,
    token: state.token,
    permissions: state.permissions,
    isLoading: state.isLoading,
    login,
    logout,
    updateUser,
    hasPermission,
  };

  // Ne pas rendre les enfants tant que l'initialisation n'est pas terminée
  if (!state.isInitialized) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontSize: '18px'
      }}>
        Chargement...
      </div>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook personnalisé pour utiliser le contexte d'authentification
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth doit être utilisé dans un AuthProvider');
  }
  return context;
};

export default AuthContext;
