/**
 * Contexte d'authentification pour PresencePro avec Supabase
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../config/supabase';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { User, UserRole } from '../types';
import { supabaseService } from '../services/supabaseService';

interface AuthContextType {
  user: User | null;
  supabaseUser: SupabaseUser | null;
  loading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;
  signOut: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<{ user: User | null; error: string | null }>;
  getRoleBasedRoute: () => string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Crée un utilisateur temporaire basé sur l'email
 * Utilisé quand l'utilisateur existe dans Supabase Auth mais pas dans notre table
 */
function createTempUserFromEmail(email: string): User {
  // Déterminer le rôle basé sur l'email
  let role: UserRole;
  let firstName: string;
  let lastName: string;

  if (email.includes('admin')) {
    role = UserRole.ADMIN;
    firstName = 'Admin';
    lastName = 'PresencePro';
  } else if (email.includes('martin') || email.includes('teacher')) {
    role = UserRole.TEACHER;
    firstName = 'Jean';
    lastName = 'Martin';
  } else {
    role = UserRole.STUDENT;
    firstName = 'Étudiant';
    lastName = 'Temporaire';
  }

  return {
    id: email, // Utiliser l'email comme ID temporaire
    username: email.split('@')[0],
    email,
    firstName,
    lastName,
    fullName: `${firstName} ${lastName}`,
    role,
    roleDisplay: role === UserRole.ADMIN ? 'Administrateur' :
                role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',
    isActive: true,
    dateJoined: new Date().toISOString()
  };
}

// Provider
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Fonction pour obtenir la route basée sur le rôle
  const getRoleBasedRoute = (): string => {
    if (!user) return '/login';

    switch (user.role) {
      case UserRole.ADMIN:
        return '/admin-dashboard';
      case UserRole.TEACHER:
        return '/teacher-dashboard';
      case UserRole.STUDENT:
        return '/student-dashboard';
      default:
        return '/login';
    }
  };

  // Initialisation - vérifier si l'utilisateur est déjà connecté
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Obtenir la session actuelle
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          setSupabaseUser(session.user);

          // Récupérer les données utilisateur depuis notre base
          try {
            const userData = await supabaseService.getUserByEmail(session.user.email!);
            setUser(userData);
          } catch (error) {
            console.error('Erreur lors de la récupération des données utilisateur:', error);
          }
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setSupabaseUser(session.user);
          try {
            const userData = await supabaseService.getUserByEmail(session.user.email!);
            setUser(userData);
          } catch (error) {
            console.error('Erreur lors de la récupération des données utilisateur:', error);
          }
        } else if (event === 'SIGNED_OUT') {
          setSupabaseUser(null);
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Fonction de connexion
  const signIn = async (email: string, password: string): Promise<{ user: User | null; error: string | null }> => {
    try {
      setLoading(true);
      console.log('🔐 Tentative de connexion pour:', email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('❌ Erreur d\'authentification Supabase:', error);
        return { user: null, error: error.message };
      }

      if (data.user) {
        console.log('✅ Authentification Supabase réussie pour:', data.user.email);
        setSupabaseUser(data.user);

        try {
          console.log('🔍 Recherche du profil utilisateur dans la base...');
          const userData = await supabaseService.getUserByEmail(data.user.email!);
          console.log('✅ Profil utilisateur trouvé:', userData);
          setUser(userData);
          return { user: userData, error: null };
        } catch (userError) {
          console.error('❌ Erreur lors de la récupération du profil utilisateur:', userError);

          // Créer un utilisateur temporaire basé sur l'email pour permettre la connexion
          console.log('🔧 Création d\'un profil utilisateur temporaire...');
          const tempUser = createTempUserFromEmail(data.user.email!);
          setUser(tempUser);
          return { user: tempUser, error: null };
        }
      }

      return { user: null, error: 'Erreur de connexion' };
    } catch (error) {
      console.error('❌ Erreur générale de connexion:', error);
      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };
    } finally {
      setLoading(false);
    }
  };

  // Fonction d'inscription
  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {
    try {
      setLoading(true);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        return { user: null, error: error.message };
      }

      if (data.user) {
        // Créer l'utilisateur dans notre base de données
        try {
          const newUser: Omit<User, 'id'> = {
            username: userData.username || email.split('@')[0],
            email,
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            role: userData.role || UserRole.STUDENT,
            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),
            roleDisplay: userData.role === UserRole.ADMIN ? 'Administrateur' :
                        userData.role === UserRole.TEACHER ? 'Professeur' : 'Étudiant',
            isActive: true,
            dateJoined: new Date().toISOString(),
          };

          const createdUser = await supabaseService.createUser(newUser);
          setUser(createdUser);
          return { user: createdUser, error: null };
        } catch (userError) {
          return { user: null, error: 'Erreur lors de la création du profil utilisateur' };
        }
      }

      return { user: null, error: 'Erreur lors de l\'inscription' };
    } catch (error) {
      return { user: null, error: error instanceof Error ? error.message : 'Erreur inconnue' };
    } finally {
      setLoading(false);
    }
  };

  // Fonction de déconnexion
  const signOut = async (): Promise<void> => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setSupabaseUser(null);
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  // Fonction de mise à jour du profil
  const updateProfile = async (userData: Partial<User>): Promise<{ user: User | null; error: string | null }> => {
    if (!user) {
      return { user: null, error: 'Aucun utilisateur connecté' };
    }

    try {
      const updatedUser = await supabaseService.updateUser(user.id, userData);
      setUser(updatedUser);
      return { user: updatedUser, error: null };
    } catch (error) {
      return { user: null, error: error instanceof Error ? error.message : 'Erreur de mise à jour' };
    }
  };

  const value: AuthContextType = {
    user,
    supabaseUser,
    loading,
    isAuthenticated: !!user,
    signIn,
    signUp,
    signOut,
    updateProfile,
    getRoleBasedRoute,
  };

  // Afficher un loader pendant l'initialisation
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px'
      }}>
        Chargement...
      </div>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook personnalisé pour utiliser le contexte d'authentification
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth doit être utilisé dans un AuthProvider');
  }
  return context;
};

export default AuthContext;
