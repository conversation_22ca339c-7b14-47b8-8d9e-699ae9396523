/**
 * Service Supabase pour PresencePro
 */

import { supabase } from '../config/supabase';
import { User, Course, AttendanceRecord, UserRole } from '../types';

class SupabaseService {
  
  // ==================== GESTION DES UTILISATEURS ====================
  
  /**
   * Récupère tous les utilisateurs
   */
  async getUsers(): Promise<User[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data?.map(user => this.mapUserFromDB(user)) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      throw error;
    }
  }

  /**
   * Récupère un utilisateur par ID
   */
  async getUserById(userId: string): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }
      
      return data ? this.mapUserFromDB(data) : null;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur:', error);
      throw error;
    }
  }

  /**
   * Crée un nouvel utilisateur
   */
  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    try {
      const dbUser = this.mapUserToDB(userData);

      const { data, error } = await supabase
        .from('users')
        .insert([dbUser])
        .select()
        .single();

      if (error) throw error;

      return this.mapUserFromDB(data);
    } catch (error) {
      console.error('Erreur lors de la création de l\'utilisateur:', error);
      throw error;
    }
  }

  /**
   * Crée un nouvel utilisateur avec un ID spécifique (pour l'authentification)
   */
  async createUserWithId(userId: string, userData: Omit<User, 'id'>): Promise<void> {
    try {
      const dbUser = this.mapUserToDB(userData);
      
      const { error } = await supabase
        .from('users')
        .insert([{ ...dbUser, id: userId }]);

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de la création de l\'utilisateur avec ID:', error);
      throw error;
    }
  }

  /**
   * Met à jour un utilisateur
   */
  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    try {
      const dbUser = this.mapUserToDB(userData);

      const { data, error } = await supabase
        .from('users')
        .update({ ...dbUser, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      return this.mapUserFromDB(data);
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'utilisateur:', error);
      throw error;
    }
  }

  /**
   * Supprime un utilisateur
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'utilisateur:', error);
      throw error;
    }
  }

  // ==================== GESTION DES COURS ====================

  /**
   * Récupère tous les cours avec leurs relations
   */
  async getCourses(): Promise<Course[]> {
    try {
      const { data, error } = await supabase
        .from('courses')
        .select(`
          *,
          teacher:teacher_id(id, username, email, first_name, last_name, role),
          student_group:student_group_id(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data?.map(course => this.mapCourseFromDB(course)) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des cours:', error);
      throw error;
    }
  }

  /**
   * Récupère les cours d'un enseignant
   */
  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {
    try {
      const { data, error } = await supabase
        .from('courses')
        .select(`
          *,
          teacher:teacher_id(id, username, email, first_name, last_name, role),
          student_group:student_group_id(*)
        `)
        .eq('teacher_id', teacherId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data?.map(course => this.mapCourseFromDB(course)) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des cours de l\'enseignant:', error);
      throw error;
    }
  }

  /**
   * Crée un nouveau cours
   */
  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {
    try {
      const dbCourse = this.mapCourseToDB(courseData);
      
      const { data, error } = await supabase
        .from('courses')
        .insert([dbCourse])
        .select()
        .single();

      if (error) throw error;
      
      return data.id;
    } catch (error) {
      console.error('Erreur lors de la création du cours:', error);
      throw error;
    }
  }

  // ==================== GESTION DES PRÉSENCES ====================

  /**
   * Enregistre une présence
   */
  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {
    try {
      const dbAttendance = this.mapAttendanceToDB(attendanceData);
      
      const { data, error } = await supabase
        .from('attendance')
        .insert([dbAttendance])
        .select()
        .single();

      if (error) throw error;
      
      return data.id;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la présence:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les présences
   */
  async getAttendanceRecords(): Promise<AttendanceRecord[]> {
    try {
      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          student:student_id(id, username, email, first_name, last_name, role),
          course:course_id(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data?.map(attendance => this.mapAttendanceFromDB(attendance)) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des présences:', error);
      throw error;
    }
  }

  /**
   * Récupère les présences d'un cours
   */
  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {
    try {
      let query = supabase
        .from('attendance')
        .select(`
          *,
          student:student_id(id, username, email, first_name, last_name, role),
          course:course_id(id, name, code)
        `)
        .eq('course_id', courseId);
      
      if (date) {
        query = query.eq('date', date);
      }
      
      query = query.order('created_at', { ascending: false });
      
      const { data, error } = await query;

      if (error) throw error;
      
      return data?.map(attendance => this.mapAttendanceFromDB(attendance)) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des présences:', error);
      throw error;
    }
  }

  /**
   * Récupère les présences d'un étudiant
   */
  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {
    try {
      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          student:student_id(id, username, email, first_name, last_name, role),
          course:course_id(id, name, code)
        `)
        .eq('student_id', studentId)
        .order('date', { ascending: false });

      if (error) throw error;
      
      return data?.map(attendance => this.mapAttendanceFromDB(attendance)) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des présences de l\'étudiant:', error);
      throw error;
    }
  }

  // ==================== GESTION DES FICHIERS ====================

  /**
   * Upload une image de profil
   */
  async uploadProfileImage(userId: string, file: File): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/profile.${fileExt}`;
      const filePath = `profile-images/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Erreur lors de l\'upload de l\'image:', error);
      throw error;
    }
  }

  /**
   * Upload une image pour la reconnaissance faciale
   */
  async uploadFaceImage(userId: string, file: File): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/${Date.now()}.${fileExt}`;
      const filePath = `face-images/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Erreur lors de l\'upload de l\'image faciale:', error);
      throw error;
    }
  }

  /**
   * Supprime une image
   */
  async deleteImage(imageUrl: string): Promise<void> {
    try {
      // Extraire le chemin du fichier depuis l'URL
      const urlParts = imageUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part === 'images');
      if (bucketIndex === -1) throw new Error('URL d\'image invalide');
      
      const filePath = urlParts.slice(bucketIndex + 1).join('/');

      const { error } = await supabase.storage
        .from('images')
        .remove([filePath]);

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'image:', error);
      throw error;
    }
  }

  // ==================== STATISTIQUES ====================

  /**
   * Récupère les statistiques globales
   */
  async getGlobalStats(): Promise<any> {
    try {
      // Compter les utilisateurs par rôle
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('role');

      if (usersError) throw usersError;

      // Compter les cours
      const { count: coursesCount, error: coursesError } = await supabase
        .from('courses')
        .select('*', { count: 'exact', head: true });

      if (coursesError) throw coursesError;

      // Compter les présences
      const { count: attendanceCount, error: attendanceError } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true });

      if (attendanceError) throw attendanceError;

      const stats = {
        totalUsers: users?.length || 0,
        totalStudents: users?.filter(user => user.role === UserRole.STUDENT).length || 0,
        totalTeachers: users?.filter(user => user.role === UserRole.TEACHER).length || 0,
        totalAdmins: users?.filter(user => user.role === UserRole.ADMIN).length || 0,
        totalCourses: coursesCount || 0,
        totalAttendances: attendanceCount || 0
      };

      return stats;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  // ==================== MÉTHODES DE MAPPING ====================

  private mapUserFromDB(dbUser: any): User {
    const firstName = dbUser.first_name || '';
    const lastName = dbUser.last_name || '';
    const role = dbUser.role || 'student';

    return {
      id: dbUser.id,
      username: dbUser.username || '',
      email: dbUser.email || '',
      firstName,
      lastName,
      fullName: `${firstName} ${lastName}`.trim() || 'Utilisateur',
      role: role as UserRole,
      roleDisplay: this.getRoleDisplay(role as UserRole),
      phoneNumber: dbUser.phone_number || undefined,
      dateOfBirth: dbUser.date_of_birth || undefined,
      address: dbUser.address || undefined,
      profilePicture: dbUser.profile_picture || undefined,
      isActive: dbUser.is_active !== false,
      dateJoined: dbUser.created_at || new Date().toISOString(),
      lastLogin: dbUser.last_login || undefined
    };
  }

  private mapUserToDB(user: Partial<User>): any {
    return {
      username: user.username,
      email: user.email,
      first_name: user.firstName,
      last_name: user.lastName,
      role: user.role,
      phone_number: user.phoneNumber,
      date_of_birth: user.dateOfBirth,
      address: user.address,
      profile_picture: user.profilePicture,
      is_active: user.isActive,
      last_login: user.lastLogin
    };
  }

  private mapCourseFromDB(dbCourse: any): Course {
    return {
      id: dbCourse.id,
      name: dbCourse.name || '',
      code: dbCourse.code || '',
      description: dbCourse.description || '',
      teacher: dbCourse.teacher ? this.mapUserFromDB(dbCourse.teacher) : undefined,
      studentGroup: dbCourse.student_group || undefined,
      schedule: dbCourse.schedule || [],
      academicYear: dbCourse.academic_year || '',
      semester: dbCourse.semester || '',
      credits: dbCourse.credits || 0,
      isActive: dbCourse.is_active !== false,
      createdAt: dbCourse.created_at || new Date().toISOString(),
      updatedAt: dbCourse.updated_at || new Date().toISOString()
    };
  }

  private mapCourseToDB(course: Partial<Course>): any {
    return {
      name: course.name,
      code: course.code,
      description: course.description,
      teacher_id: course.teacher?.id,
      student_group_id: course.studentGroup?.id,
      schedule: course.schedule,
      academic_year: course.academicYear,
      semester: course.semester,
      credits: course.credits,
      is_active: course.isActive
    };
  }

  private mapAttendanceFromDB(dbAttendance: any): AttendanceRecord {
    return {
      id: dbAttendance.id,
      student: dbAttendance.student ? this.mapUserFromDB(dbAttendance.student) : undefined,
      course: dbAttendance.course || undefined,
      date: dbAttendance.date || '',
      time: dbAttendance.time || '',
      status: dbAttendance.status || 'absent',
      method: dbAttendance.method || 'manual',
      confidence: dbAttendance.confidence || undefined,
      notes: dbAttendance.notes || undefined,
      createdAt: dbAttendance.created_at || new Date().toISOString(),
      updatedAt: dbAttendance.updated_at
    };
  }

  private mapAttendanceToDB(attendance: Partial<AttendanceRecord>): any {
    return {
      student_id: attendance.student?.id,
      course_id: attendance.course?.id,
      date: attendance.date,
      time: attendance.time,
      status: attendance.status,
      method: attendance.method,
      confidence: attendance.confidence,
      notes: attendance.notes
    };
  }



  /**
   * Récupère un utilisateur par email
   */
  async getUserByEmail(email: string): Promise<User> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Utilisateur non trouvé');

      return this.mapUserFromDB(data);
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur par email:', error);
      throw error;
    }
  }

  private getRoleDisplay(role: UserRole | string): string {
    const roleStr = typeof role === 'string' ? role : role;

    switch (roleStr) {
      case UserRole.ADMIN:
      case 'admin':
        return 'Administrateur';
      case UserRole.TEACHER:
      case 'teacher':
        return 'Professeur';
      case UserRole.STUDENT:
      case 'student':
        return 'Étudiant';
      default:
        return 'Utilisateur';
    }
  }
}

// Instance singleton du service Supabase
export const supabaseService = new SupabaseService();
export default supabaseService;
