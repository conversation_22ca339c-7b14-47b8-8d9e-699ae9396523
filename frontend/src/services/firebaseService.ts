/**
 * Service Firebase pour PresencePro
 */

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';
import { db, storage } from '../config/firebase';
import { User, Course, AttendanceRecord, StudentGroup } from '../types';

class FirebaseService {
  
  // ==================== GESTION DES UTILISATEURS ====================
  
  /**
   * Récupère tous les utilisateurs
   */
  async getUsers(): Promise<User[]> {
    try {
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);
      return usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as User));
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      throw error;
    }
  }

  /**
   * Récupère un utilisateur par ID
   */
  async getUserById(userId: string): Promise<User | null> {
    try {
      const userDoc = doc(db, 'users', userId);
      const userSnapshot = await getDoc(userDoc);
      
      if (userSnapshot.exists()) {
        return {
          id: userSnapshot.id,
          ...userSnapshot.data()
        } as User;
      }
      return null;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur:', error);
      throw error;
    }
  }

  /**
   * Crée un nouvel utilisateur
   */
  async createUser(userData: Omit<User, 'id'>): Promise<string> {
    try {
      const usersCollection = collection(db, 'users');
      const docRef = await addDoc(usersCollection, {
        ...userData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Erreur lors de la création de l\'utilisateur:', error);
      throw error;
    }
  }

  /**
   * Met à jour un utilisateur
   */
  async updateUser(userId: string, userData: Partial<User>): Promise<void> {
    try {
      const userDoc = doc(db, 'users', userId);
      await updateDoc(userDoc, {
        ...userData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'utilisateur:', error);
      throw error;
    }
  }

  /**
   * Supprime un utilisateur
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const userDoc = doc(db, 'users', userId);
      await deleteDoc(userDoc);
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'utilisateur:', error);
      throw error;
    }
  }

  // ==================== GESTION DES COURS ====================

  /**
   * Récupère tous les cours
   */
  async getCourses(): Promise<Course[]> {
    try {
      const coursesCollection = collection(db, 'courses');
      const coursesSnapshot = await getDocs(coursesCollection);
      return coursesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Course));
    } catch (error) {
      console.error('Erreur lors de la récupération des cours:', error);
      throw error;
    }
  }

  /**
   * Récupère les cours d'un enseignant
   */
  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {
    try {
      const coursesCollection = collection(db, 'courses');
      const q = query(coursesCollection, where('teacherId', '==', teacherId));
      const coursesSnapshot = await getDocs(q);
      return coursesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Course));
    } catch (error) {
      console.error('Erreur lors de la récupération des cours de l\'enseignant:', error);
      throw error;
    }
  }

  /**
   * Crée un nouveau cours
   */
  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {
    try {
      const coursesCollection = collection(db, 'courses');
      const docRef = await addDoc(coursesCollection, {
        ...courseData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Erreur lors de la création du cours:', error);
      throw error;
    }
  }

  // ==================== GESTION DES PRÉSENCES ====================

  /**
   * Enregistre une présence
   */
  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {
    try {
      const attendanceCollection = collection(db, 'attendance');
      const docRef = await addDoc(attendanceCollection, {
        ...attendanceData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la présence:', error);
      throw error;
    }
  }

  /**
   * Récupère les présences d'un cours
   */
  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {
    try {
      const attendanceCollection = collection(db, 'attendance');
      let q = query(attendanceCollection, where('courseId', '==', courseId));
      
      if (date) {
        q = query(q, where('date', '==', date));
      }
      
      q = query(q, orderBy('createdAt', 'desc'));
      
      const attendanceSnapshot = await getDocs(q);
      return attendanceSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as AttendanceRecord));
    } catch (error) {
      console.error('Erreur lors de la récupération des présences:', error);
      throw error;
    }
  }

  /**
   * Récupère les présences d'un étudiant
   */
  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {
    try {
      const attendanceCollection = collection(db, 'attendance');
      const q = query(
        attendanceCollection, 
        where('studentId', '==', studentId),
        orderBy('date', 'desc')
      );
      
      const attendanceSnapshot = await getDocs(q);
      return attendanceSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as AttendanceRecord));
    } catch (error) {
      console.error('Erreur lors de la récupération des présences de l\'étudiant:', error);
      throw error;
    }
  }

  // ==================== GESTION DES FICHIERS ====================

  /**
   * Upload une image de profil
   */
  async uploadProfileImage(userId: string, file: File): Promise<string> {
    try {
      const imageRef = ref(storage, `profile-images/${userId}/${file.name}`);
      const snapshot = await uploadBytes(imageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      return downloadURL;
    } catch (error) {
      console.error('Erreur lors de l\'upload de l\'image:', error);
      throw error;
    }
  }

  /**
   * Upload une image pour la reconnaissance faciale
   */
  async uploadFaceImage(userId: string, file: File): Promise<string> {
    try {
      const imageRef = ref(storage, `face-images/${userId}/${Date.now()}_${file.name}`);
      const snapshot = await uploadBytes(imageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      return downloadURL;
    } catch (error) {
      console.error('Erreur lors de l\'upload de l\'image faciale:', error);
      throw error;
    }
  }

  /**
   * Supprime une image
   */
  async deleteImage(imageUrl: string): Promise<void> {
    try {
      const imageRef = ref(storage, imageUrl);
      await deleteObject(imageRef);
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'image:', error);
      throw error;
    }
  }

  // ==================== STATISTIQUES ====================

  /**
   * Récupère les statistiques globales
   */
  async getGlobalStats(): Promise<any> {
    try {
      // Compter les utilisateurs par rôle
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);
      const users = usersSnapshot.docs.map(doc => doc.data());
      
      const stats = {
        totalUsers: users.length,
        totalStudents: users.filter(user => user.role === 'student').length,
        totalTeachers: users.filter(user => user.role === 'teacher').length,
        totalAdmins: users.filter(user => user.role === 'admin').length,
        totalCourses: 0,
      };

      // Compter les cours
      const coursesCollection = collection(db, 'courses');
      const coursesSnapshot = await getDocs(coursesCollection);
      stats.totalCourses = coursesSnapshot.size;

      return stats;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }
}

// Instance singleton du service Firebase
export const firebaseService = new FirebaseService();
export default firebaseService;
