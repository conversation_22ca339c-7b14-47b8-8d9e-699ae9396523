/**
 * Service Supabase pour PresencePro (anciennement Firebase)
 * Maintient la compatibilité avec l'interface Firebase
 */

import { supabaseService } from './supabaseService';
import { User, Course, AttendanceRecord } from '../types';

class FirebaseService {

  // ==================== GESTION DES UTILISATEURS ====================

  /**
   * Récupère tous les utilisateurs
   */
  async getUsers(): Promise<User[]> {
    return supabaseService.getUsers();
  }

  /**
   * Récupère un utilisateur par ID
   */
  async getUserById(userId: string): Promise<User | null> {
    return supabaseService.getUserById(userId);
  }

  /**
   * Crée un nouvel utilisateur
   */
  async createUser(userData: Omit<User, 'id'>): Promise<string> {
    return supabaseService.createUser(userData);
  }

  /**
   * Met à jour un utilisateur
   */
  async updateUser(userId: string, userData: Partial<User>): Promise<void> {
    return supabaseService.updateUser(userId, userData);
  }

  /**
   * Supprime un utilisateur
   */
  async deleteUser(userId: string): Promise<void> {
    return supabaseService.deleteUser(userId);
  }

  // ==================== GESTION DES COURS ====================

  /**
   * Récupère tous les cours
   */
  async getCourses(): Promise<Course[]> {
    return supabaseService.getCourses();
  }

  /**
   * Récupère les cours d'un enseignant
   */
  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {
    return supabaseService.getCoursesByTeacher(teacherId);
  }

  /**
   * Crée un nouveau cours
   */
  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {
    return supabaseService.createCourse(courseData);
  }

  // ==================== GESTION DES PRÉSENCES ====================

  /**
   * Enregistre une présence
   */
  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {
    return supabaseService.recordAttendance(attendanceData);
  }

  /**
   * Récupère les présences d'un cours
   */
  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {
    return supabaseService.getAttendanceByCourse(courseId, date);
  }

  /**
   * Récupère les présences d'un étudiant
   */
  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {
    return supabaseService.getAttendanceByStudent(studentId);
  }

  // ==================== GESTION DES FICHIERS ====================

  /**
   * Upload une image de profil
   */
  async uploadProfileImage(userId: string, file: File): Promise<string> {
    return supabaseService.uploadProfileImage(userId, file);
  }

  /**
   * Upload une image pour la reconnaissance faciale
   */
  async uploadFaceImage(userId: string, file: File): Promise<string> {
    return supabaseService.uploadFaceImage(userId, file);
  }

  /**
   * Supprime une image
   */
  async deleteImage(imageUrl: string): Promise<void> {
    return supabaseService.deleteImage(imageUrl);
  }

  // ==================== STATISTIQUES ====================

  /**
   * Récupère les statistiques globales
   */
  async getGlobalStats(): Promise<any> {
    return supabaseService.getGlobalStats();
  }
}

// Instance singleton du service Firebase (maintenant Supabase)
export const firebaseService = new FirebaseService();
export default firebaseService;
