/**
 * Service API pour PresencePro
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  ApiResponse, 
  LoginCredentials, 
  AuthResponse, 
  User, 
  Course, 
  AttendanceRecord,
  PaginatedResponse
} from '../types';

// Configuration de base de l'API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Intercepteur pour ajouter le token d'authentification
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Intercepteur pour gérer les réponses et erreurs
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expiré ou invalide
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Méthodes d'authentification
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await this.api.post<AuthResponse>('/auth/login/', credentials);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur de connexion');
    }
  }

  async logout(): Promise<ApiResponse> {
    try {
      const response = await this.api.post<ApiResponse>('/auth/logout/');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur de déconnexion');
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await this.api.get<ApiResponse<User>>('/auth/me/');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du profil');
    }
  }

  async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await this.api.put<ApiResponse<User>>('/auth/profile/update/', userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du profil');
    }
  }

  async checkUsernameAvailability(username: string): Promise<ApiResponse<{ available: boolean }>> {
    try {
      const response = await this.api.post<ApiResponse<{ available: boolean }>>('/auth/check-username/', { username });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la vérification du nom d\'utilisateur');
    }
  }

  // Méthodes de gestion des utilisateurs
  async getUsers(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<User>> {
    try {
      const response = await this.api.get<PaginatedResponse<User>>(`/users/?page=${page}&page_size=${pageSize}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des utilisateurs');
    }
  }

  async createUser(userData: any): Promise<ApiResponse<User>> {
    try {
      const response = await this.api.post<ApiResponse<User>>('/users/', userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'utilisateur');
    }
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await this.api.put<ApiResponse<User>>(`/users/${userId}/`, userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour de l\'utilisateur');
    }
  }

  async deleteUser(userId: string): Promise<ApiResponse> {
    try {
      const response = await this.api.delete<ApiResponse>(`/users/${userId}/`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'utilisateur');
    }
  }

  // Méthodes de gestion des cours
  async getCourses(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<Course>> {
    try {
      const response = await this.api.get<PaginatedResponse<Course>>(`/courses/?page=${page}&page_size=${pageSize}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des cours');
    }
  }

  async getCourse(courseId: string): Promise<ApiResponse<Course>> {
    try {
      const response = await this.api.get<ApiResponse<Course>>(`/courses/${courseId}/`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du cours');
    }
  }

  async createCourse(courseData: any): Promise<ApiResponse<Course>> {
    try {
      const response = await this.api.post<ApiResponse<Course>>('/courses/', courseData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création du cours');
    }
  }

  async updateCourse(courseId: string, courseData: Partial<Course>): Promise<ApiResponse<Course>> {
    try {
      const response = await this.api.put<ApiResponse<Course>>(`/courses/${courseId}/`, courseData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du cours');
    }
  }

  async deleteCourse(courseId: string): Promise<ApiResponse> {
    try {
      const response = await this.api.delete<ApiResponse>(`/courses/${courseId}/`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression du cours');
    }
  }

  // Méthodes de gestion des présences
  async getAttendanceRecords(courseId?: string, studentId?: string, date?: string): Promise<ApiResponse<AttendanceRecord[]>> {
    try {
      const params = new URLSearchParams();
      if (courseId) params.append('course', courseId);
      if (studentId) params.append('student', studentId);
      if (date) params.append('date', date);

      const response = await this.api.get<ApiResponse<AttendanceRecord[]>>(`/attendance/?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des présences');
    }
  }

  async markAttendance(attendanceData: any): Promise<ApiResponse<AttendanceRecord>> {
    try {
      const response = await this.api.post<ApiResponse<AttendanceRecord>>('/attendance/', attendanceData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'enregistrement de la présence');
    }
  }

  async updateAttendance(attendanceId: string, attendanceData: Partial<AttendanceRecord>): Promise<ApiResponse<AttendanceRecord>> {
    try {
      const response = await this.api.put<ApiResponse<AttendanceRecord>>(`/attendance/${attendanceId}/`, attendanceData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour de la présence');
    }
  }

  // Méthodes de reconnaissance faciale
  async uploadFaceImage(userId: string, imageFile: File): Promise<ApiResponse> {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('user_id', userId);

      const response = await this.api.post<ApiResponse>('/face-detection/upload/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'upload de l\'image');
    }
  }

  async detectFaces(imageFile: File): Promise<ApiResponse> {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await this.api.post<ApiResponse>('/face-detection/detect/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la détection des visages');
    }
  }

  // Méthodes de rapports
  async getAttendanceReport(courseId?: string, studentId?: string, startDate?: string, endDate?: string): Promise<ApiResponse> {
    try {
      const params = new URLSearchParams();
      if (courseId) params.append('course', courseId);
      if (studentId) params.append('student', studentId);
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);

      const response = await this.api.get<ApiResponse>(`/attendance/reports/?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la génération du rapport');
    }
  }

  // Méthodes utilitaires
  async uploadFile(file: File, path: string): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.api.post<ApiResponse<{ url: string }>>(`/upload/${path}/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'upload du fichier');
    }
  }
}

// Instance singleton du service API
export const apiService = new ApiService();
export default apiService;
