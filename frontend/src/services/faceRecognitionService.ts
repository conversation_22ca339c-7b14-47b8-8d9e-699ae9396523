/**
 * Service de reconnaissance faciale pour PresencePro
 * Utilise face-api.js pour la détection et reconnaissance faciale
 */

import * as faceapi from 'face-api.js';
import { supabase } from '../config/supabase';
import { FaceEncoding, FaceDetectionResult, User } from '../types';

class FaceRecognitionService {
  private isInitialized = false;
  private labeledDescriptors: faceapi.LabeledFaceDescriptors[] = [];

  /**
   * Initialise les modèles de reconnaissance faciale
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('Chargement des modèles de reconnaissance faciale...');
      
      // Charger les modèles depuis un CDN
      const MODEL_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';
      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
        faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
        faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL),
        faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL),
        faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL)
      ]);

      this.isInitialized = true;
      console.log('✅ Modèles de reconnaissance faciale chargés');
      
      // Charger les encodages existants
      await this.loadFaceEncodings();
      
    } catch (error) {
      console.error('❌ Erreur lors du chargement des modèles:', error);
      throw new Error('Impossible de charger les modèles de reconnaissance faciale');
    }
  }

  /**
   * Charge tous les encodages faciaux depuis Supabase
   */
  async loadFaceEncodings(): Promise<void> {
    try {
      const { data: encodings, error } = await supabase
        .from('face_encodings')
        .select(`
          *,
          user:user_id(id, username, email, first_name, last_name)
        `)
        .eq('is_active', true);

      if (error) throw error;

      this.labeledDescriptors = [];

      // Grouper les encodages par utilisateur
      const userEncodings = new Map<string, { user: User; descriptors: Float32Array[] }>();

      encodings?.forEach(encoding => {
        const userId = encoding.user.id;
        if (!userEncodings.has(userId)) {
          userEncodings.set(userId, {
            user: encoding.user,
            descriptors: []
          });
        }
        
        // Convertir l'encodage en Float32Array
        const descriptor = new Float32Array(encoding.encoding);
        userEncodings.get(userId)!.descriptors.push(descriptor);
      });

      // Créer les LabeledFaceDescriptors
      userEncodings.forEach(({ user, descriptors }, userId) => {
        if (descriptors.length > 0) {
          const label = `${user.firstName} ${user.lastName} (${user.username})`;
          this.labeledDescriptors.push(
            new faceapi.LabeledFaceDescriptors(label, descriptors)
          );
        }
      });

      console.log(`✅ ${this.labeledDescriptors.length} utilisateurs chargés pour la reconnaissance`);
      
    } catch (error) {
      console.error('❌ Erreur lors du chargement des encodages:', error);
    }
  }

  /**
   * Détecte et reconnaît les visages dans une image
   */
  async detectFaces(
    imageElement: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement
  ): Promise<FaceDetectionResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Détecter les visages avec landmarks et descripteurs
      const detections = await faceapi
        .detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceDescriptors();

      if (detections.length === 0) {
        return {
          success: true,
          detectedUsers: [],
          message: 'Aucun visage détecté'
        };
      }

      // Créer un matcher pour la reconnaissance
      const faceMatcher = new faceapi.FaceMatcher(this.labeledDescriptors, 0.6);
      
      const detectedUsers = detections.map(detection => {
        const bestMatch = faceMatcher.findBestMatch(detection.descriptor);
        
        // Extraire les informations utilisateur du label
        const isMatch = bestMatch.distance < 0.6;
        const label = isMatch ? bestMatch.label : 'Inconnu';
        
        let user: User | null = null;
        if (isMatch && label !== 'unknown') {
          // Parser le label pour extraire les infos utilisateur
          const match = label.match(/^(.+) \((.+)\)$/);
          if (match) {
            const [, fullName, username] = match;
            const [firstName, ...lastNameParts] = fullName.split(' ');
            user = {
              id: username, // Temporaire, sera résolu plus tard
              username,
              firstName,
              lastName: lastNameParts.join(' '),
              fullName,
              email: '', // Sera résolu plus tard
              role: 'student' as any,
              roleDisplay: 'Étudiant',
              isActive: true,
              dateJoined: new Date().toISOString()
            };
          }
        }

        return {
          user: user!,
          confidence: Math.round((1 - bestMatch.distance) * 100) / 100,
          boundingBox: {
            x: detection.detection.box.x,
            y: detection.detection.box.y,
            width: detection.detection.box.width,
            height: detection.detection.box.height
          }
        };
      }).filter(result => result.user !== null);

      return {
        success: true,
        detectedUsers,
        message: `${detectedUsers.length} utilisateur(s) reconnu(s)`
      };

    } catch (error) {
      console.error('❌ Erreur lors de la détection:', error);
      return {
        success: false,
        detectedUsers: [],
        message: `Erreur de détection: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      };
    }
  }

  /**
   * Enregistre un nouvel encodage facial pour un utilisateur
   */
  async registerFaceEncoding(
    userId: string,
    imageElement: HTMLImageElement | HTMLCanvasElement,
    imageFile?: File
  ): Promise<{ success: boolean; message: string; encodingId?: string }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Détecter le visage dans l'image
      const detection = await faceapi
        .detectSingleFace(imageElement, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detection) {
        return {
          success: false,
          message: 'Aucun visage détecté dans l\'image'
        };
      }

      // Upload de l'image si fournie
      let imageUrl: string | undefined;
      if (imageFile) {
        try {
          const fileExt = imageFile.name.split('.').pop();
          const fileName = `${userId}/face_${Date.now()}.${fileExt}`;
          const filePath = `face-images/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from('images')
            .upload(filePath, imageFile);

          if (uploadError) throw uploadError;

          const { data } = supabase.storage
            .from('images')
            .getPublicUrl(filePath);

          imageUrl = data.publicUrl;
        } catch (uploadError) {
          console.warn('Erreur upload image:', uploadError);
        }
      }

      // Sauvegarder l'encodage en base
      const { data, error } = await supabase
        .from('face_encodings')
        .insert([{
          user_id: userId,
          encoding: Array.from(detection.descriptor),
          confidence: 0.9, // Confiance par défaut pour un encodage manuel
          image_url: imageUrl,
          is_active: true
        }])
        .select()
        .single();

      if (error) throw error;

      // Recharger les encodages
      await this.loadFaceEncodings();

      return {
        success: true,
        message: 'Encodage facial enregistré avec succès',
        encodingId: data.id
      };

    } catch (error) {
      console.error('❌ Erreur lors de l\'enregistrement:', error);
      return {
        success: false,
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      };
    }
  }

  /**
   * Supprime tous les encodages d'un utilisateur
   */
  async deleteFaceEncodings(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('face_encodings')
        .update({ is_active: false })
        .eq('user_id', userId);

      if (error) throw error;

      // Recharger les encodages
      await this.loadFaceEncodings();
      
    } catch (error) {
      console.error('❌ Erreur lors de la suppression:', error);
      throw error;
    }
  }

  /**
   * Obtient les statistiques de reconnaissance
   */
  getStats(): {
    isInitialized: boolean;
    registeredUsers: number;
    totalEncodings: number;
  } {
    const totalEncodings = this.labeledDescriptors.reduce(
      (sum, descriptor) => sum + descriptor.descriptors.length,
      0
    );

    return {
      isInitialized: this.isInitialized,
      registeredUsers: this.labeledDescriptors.length,
      totalEncodings
    };
  }

  /**
   * Redimensionne une image pour optimiser la détection
   */
  static resizeImage(
    canvas: HTMLCanvasElement,
    maxWidth: number = 640,
    maxHeight: number = 480
  ): void {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;
    
    // Calculer les nouvelles dimensions en gardant le ratio
    let newWidth = width;
    let newHeight = height;
    
    if (width > maxWidth) {
      newWidth = maxWidth;
      newHeight = (height * maxWidth) / width;
    }
    
    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = (newWidth * maxHeight) / newHeight;
    }

    // Redimensionner si nécessaire
    if (newWidth !== width || newHeight !== height) {
      const imageData = ctx.getImageData(0, 0, width, height);
      canvas.width = newWidth;
      canvas.height = newHeight;
      ctx.putImageData(imageData, 0, 0);
    }
  }
}

// Instance singleton
export const faceRecognitionService = new FaceRecognitionService();
export default faceRecognitionService;
