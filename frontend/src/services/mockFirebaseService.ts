/**
 * Service Firebase simulé pour le développement local
 * Utilise localStorage pour simuler Firestore
 */

import { User, Course, AttendanceRecord, UserRole } from '../types';

class MockFirebaseService {
  private getFromStorage<T>(key: string): T[] {
    const data = localStorage.getItem(`presencepro_${key}`);
    return data ? JSON.parse(data) : [];
  }

  private saveToStorage<T>(key: string, data: T[]): void {
    localStorage.setItem(`presencepro_${key}`, JSON.stringify(data));
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  // ==================== GESTION DES UTILISATEURS ====================

  async getUsers(): Promise<User[]> {
    // Simuler un délai r<PERSON>eau
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let users = this.getFromStorage<User>('users');
    
    // Créer des données de test si vide
    if (users.length === 0) {
      users = [
        {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'System',
          fullName: 'Admin System',
          role: UserRole.ADMIN,
          roleDisplay: 'Administrateur',
          isActive: true,
          dateJoined: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        },
        {
          id: '2',
          username: 'prof.martin',
          email: '<EMAIL>',
          firstName: 'Jean',
          lastName: 'Martin',
          fullName: 'Jean Martin',
          role: UserRole.TEACHER,
          roleDisplay: 'Enseignant',
          isActive: true,
          dateJoined: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        },
        {
          id: '3',
          username: 'marie.dubois',
          email: '<EMAIL>',
          firstName: 'Marie',
          lastName: 'Dubois',
          fullName: 'Marie Dubois',
          role: UserRole.STUDENT,
          roleDisplay: 'Étudiant',
          isActive: true,
          dateJoined: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        }
      ];
      this.saveToStorage('users', users);
    }
    
    return users;
  }

  async getUserById(userId: string): Promise<User | null> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const users = await this.getUsers();
    return users.find(user => user.id === userId) || null;
  }

  async createUser(userData: Omit<User, 'id'>): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const users = await this.getUsers();
    const newUser: User = {
      ...userData,
      id: this.generateId(),
      dateJoined: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    };
    users.push(newUser);
    this.saveToStorage('users', users);
    return newUser.id;
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const users = await this.getUsers();
    const index = users.findIndex(user => user.id === userId);
    if (index !== -1) {
      users[index] = { ...users[index], ...userData };
      this.saveToStorage('users', users);
    }
  }

  async deleteUser(userId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const users = await this.getUsers();
    const filteredUsers = users.filter(user => user.id !== userId);
    this.saveToStorage('users', filteredUsers);
  }

  // ==================== GESTION DES COURS ====================

  async getCourses(): Promise<Course[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let courses = this.getFromStorage<Course>('courses');
    
    // Créer des données de test si vide
    if (courses.length === 0) {
      const users = await this.getUsers();
      const teacher = users.find(u => u.role === UserRole.TEACHER);
      
      if (teacher) {
        courses = [
          {
            id: '1',
            name: 'Mathématiques Avancées',
            code: 'MATH301',
            description: 'Cours de mathématiques niveau avancé',
            teacher: teacher,
            studentGroup: {
              id: '1',
              name: 'L3 Informatique',
              description: 'Licence 3 Informatique',
              academicYear: '2023-2024',
              level: 'L3',
              specialization: 'Informatique',
              studentCount: 25,
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            schedule: [
              {
                id: '1',
                dayOfWeek: 1, // Lundi
                startTime: '09:00',
                endTime: '11:00',
                room: 'A101',
                building: 'Bâtiment A'
              }
            ],
            academicYear: '2023-2024',
            semester: 'S1',
            credits: 6,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ];
        this.saveToStorage('courses', courses);
      }
    }
    
    return courses;
  }

  async getCoursesByTeacher(teacherId: string): Promise<Course[]> {
    const courses = await this.getCourses();
    return courses.filter(course => course.teacher?.id === teacherId);
  }

  async createCourse(courseData: Omit<Course, 'id'>): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const courses = await this.getCourses();
    const newCourse: Course = {
      ...courseData,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    courses.push(newCourse);
    this.saveToStorage('courses', courses);
    return newCourse.id;
  }

  // ==================== GESTION DES PRÉSENCES ====================

  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id'>): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const attendances = this.getFromStorage<AttendanceRecord>('attendance');
    const newAttendance: AttendanceRecord = {
      ...attendanceData,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    attendances.push(newAttendance);
    this.saveToStorage('attendance', attendances);
    return newAttendance.id;
  }

  async getAttendanceByCourse(courseId: string, date?: string): Promise<AttendanceRecord[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const attendances = this.getFromStorage<AttendanceRecord>('attendance');
    return attendances.filter(att => {
      const matchesCourse = att.course?.id === courseId;
      const matchesDate = !date || att.date === date;
      return matchesCourse && matchesDate;
    });
  }

  async getAttendanceByStudent(studentId: string): Promise<AttendanceRecord[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const attendances = this.getFromStorage<AttendanceRecord>('attendance');
    return attendances.filter(att => att.student?.id === studentId);
  }

  // ==================== GESTION DES FICHIERS ====================

  async uploadProfileImage(userId: string, file: File): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    // Simuler un URL d'image
    return `https://mock-storage.presencepro.com/profile-images/${userId}/${file.name}`;
  }

  async uploadFaceImage(userId: string, file: File): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    // Simuler un URL d'image
    return `https://mock-storage.presencepro.com/face-images/${userId}/${Date.now()}_${file.name}`;
  }

  async deleteImage(imageUrl: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    // Simuler la suppression
    console.log(`Image supprimée : ${imageUrl}`);
  }

  // ==================== STATISTIQUES ====================

  async getGlobalStats(): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const users = await this.getUsers();
    const courses = await this.getCourses();
    const attendances = this.getFromStorage<AttendanceRecord>('attendance');
    
    return {
      totalUsers: users.length,
      totalStudents: users.filter(user => user.role === UserRole.STUDENT).length,
      totalTeachers: users.filter(user => user.role === UserRole.TEACHER).length,
      totalAdmins: users.filter(user => user.role === UserRole.ADMIN).length,
      totalCourses: courses.length,
      totalAttendances: attendances.length
    };
  }

  // ==================== MÉTHODES UTILITAIRES ====================

  async clearAllData(): Promise<void> {
    localStorage.removeItem('presencepro_users');
    localStorage.removeItem('presencepro_courses');
    localStorage.removeItem('presencepro_attendance');
    console.log('Toutes les données simulées ont été effacées');
  }

  async seedTestData(): Promise<void> {
    await this.clearAllData();
    await this.getUsers(); // Cela va créer les données de test
    await this.getCourses(); // Cela va créer les données de test
    console.log('Données de test créées');
  }
}

// Instance singleton du service Firebase simulé
export const mockFirebaseService = new MockFirebaseService();
export default mockFirebaseService;
