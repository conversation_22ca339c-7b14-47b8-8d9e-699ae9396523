import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  TextField,
  CircularProgress,
  Avatar,
  Paper,
  Divider
} from '@mui/material';
import {
  Login as LoginIcon,
  PersonAdd as PersonAddIcon,
  Logout as LogoutIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { supabase } from '../config/supabase';
import { supabaseService } from '../services/supabaseService';
import type { User } from '@supabase/supabase-js';

const SupabaseTestSimple: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);

  useEffect(() => {
    // Vérifier la session actuelle
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      setMessage({ type: 'success', text: 'Connexion réussie !' });
      setEmail('');
      setPassword('');
    } catch (error) {
      console.error('Erreur de connexion:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Erreur de connexion' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async () => {
    setLoading(true);
    setMessage(null);

    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) throw error;

      setMessage({ 
        type: 'success', 
        text: 'Compte créé ! Vérifiez votre email pour confirmer votre compte.' 
      });
      setEmail('');
      setPassword('');
    } catch (error) {
      console.error('Erreur d\'inscription:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Erreur d\'inscription' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setMessage({ type: 'success', text: 'Déconnexion réussie !' });
    } catch (error) {
      console.error('Erreur de déconnexion:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Erreur de déconnexion' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file: File, type: 'profile' | 'face') => {
    if (!file || !user) return;

    setLoading(true);
    setMessage(null);

    try {
      let imageUrl: string;
      
      if (type === 'profile') {
        imageUrl = await supabaseService.uploadProfileImage(user.id, file);
      } else {
        imageUrl = await supabaseService.uploadFaceImage(user.id, file);
      }

      setUploadedImages(prev => [...prev, imageUrl]);
      setMessage({ 
        type: 'success', 
        text: `Image ${type === 'profile' ? 'de profil' : 'faciale'} uploadée avec succès !` 
      });

    } catch (error) {
      console.error('Erreur upload:', error);
      setMessage({ 
        type: 'error', 
        text: `Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}` 
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom align="center">
        Test Supabase Simple
      </Typography>

      {message && (
        <Alert severity={message.type} sx={{ mb: 3 }} onClose={() => setMessage(null)}>
          {message.text}
        </Alert>
      )}

      {/* Configuration */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Configuration Supabase
          </Typography>
          <Typography variant="body2">
            <strong>Project URL:</strong> {process.env.REACT_APP_SUPABASE_URL || 'Non configuré'}<br />
            <strong>Anon Key:</strong> {process.env.REACT_APP_SUPABASE_ANON_KEY ? 'Configuré' : 'Non configuré'}
          </Typography>
        </CardContent>
      </Card>

      {user ? (
        // Utilisateur connecté
        <Box>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Utilisateur Connecté
              </Typography>
              <Typography variant="body1">
                <strong>Email:</strong> {user.email}<br />
                <strong>ID:</strong> {user.id}
              </Typography>
              <Button
                variant="contained"
                color="error"
                startIcon={<LogoutIcon />}
                onClick={handleLogout}
                disabled={loading}
                sx={{ mt: 2 }}
              >
                Se Déconnecter
              </Button>
            </CardContent>
          </Card>

          {/* Upload d'images */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upload d'Images
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="contained"
                  component="label"
                  startIcon={<CloudUploadIcon />}
                  disabled={loading}
                  sx={{ mr: 2 }}
                >
                  Upload Image de Profil
                  <input
                    type="file"
                    hidden
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload(file, 'profile');
                    }}
                  />
                </Button>

                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<CloudUploadIcon />}
                  disabled={loading}
                >
                  Upload Image Faciale
                  <input
                    type="file"
                    hidden
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload(file, 'face');
                    }}
                  />
                </Button>
              </Box>

              {loading && <CircularProgress size={20} />}

              {uploadedImages.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Images uploadées ({uploadedImages.length})
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    {uploadedImages.map((url, index) => (
                      <Avatar
                        key={index}
                        src={url}
                        sx={{ width: 80, height: 80 }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      ) : (
        // Formulaire de connexion
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Authentification
            </Typography>
            
            <form onSubmit={handleLogin}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                margin="normal"
                required
              />
              <TextField
                fullWidth
                label="Mot de passe"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                margin="normal"
                required
              />
              
              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}
                >
                  Se Connecter
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={handleSignup}
                  disabled={loading}
                  startIcon={<PersonAddIcon />}
                >
                  S'Inscrire
                </Button>
              </Box>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Instructions
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="body2">
            1. Créez un compte ou connectez-vous<br />
            2. Une fois connecté, testez l'upload d'images<br />
            3. Vérifiez que les images apparaissent dans le bucket Supabase
          </Typography>
        </CardContent>
      </Card>
    </Container>
  );
};

export default SupabaseTestSimple;
