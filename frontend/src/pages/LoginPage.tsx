import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Stack,
  IconButton,
  InputAdornment
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  School as SchoolIcon,
  Face as FaceIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const { signIn, isAuthenticated, getRoleBasedRoute } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Rediriger si déjà connecté
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || getRoleBasedRoute();
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location, getRoleBasedRoute]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion avec:', email);
      const result = await signIn(email, password);
      console.log('📊 Résultat de la connexion:', result);

      if (result.error) {
        console.error('❌ Erreur de connexion:', result.error);
        setError(result.error);
      } else if (result.user) {
        console.log('✅ Utilisateur connecté:', result.user);
        // Redirection automatique vers le dashboard approprié
        const targetRoute = getRoleBasedRoute();
        console.log('🎯 Redirection vers:', targetRoute);
        navigate(targetRoute, { replace: true });
      } else {
        console.warn('⚠️ Aucun utilisateur retourné');
        setError('Erreur de connexion - aucun utilisateur retourné');
      }
    } catch (err) {
      console.error('❌ Exception lors de la connexion:', err);
      setError('Erreur de connexion. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async (role: 'admin' | 'teacher' | 'student') => {
    setError(null);
    setLoading(true);

    // Comptes de démonstration
    const demoAccounts = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      teacher: { email: '<EMAIL>', password: 'teacher123' },
      student: { email: '<EMAIL>', password: 'student123' }
    };

    const account = demoAccounts[role];

    try {
      const result = await signIn(account.email, account.password);

      if (result.error) {
        setError(result.error);
      } else if (result.user) {
        const targetRoute = getRoleBasedRoute();
        navigate(targetRoute, { replace: true });
      }
    } catch (err) {
      setError('Erreur de connexion avec le compte de démonstration.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ py: 8 }}>
      <Box display="flex" flexDirection="column" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center" mb={2}>
          <FaceIcon sx={{ fontSize: 40, color: 'primary.main', mr: 1 }} />
          <SchoolIcon sx={{ fontSize: 40, color: 'secondary.main' }} />
        </Box>
        <Typography variant="h3" component="h1" gutterBottom align="center">
          PresencePro
        </Typography>
        <Typography variant="h6" color="text.secondary" align="center">
          Système de Gestion de Présence par Reconnaissance Faciale
        </Typography>
      </Box>

      <Paper elevation={3} sx={{ p: 4 }}>
        <Typography variant="h4" component="h2" gutterBottom align="center">
          Connexion
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            margin="normal"
            required
            autoComplete="email"
            autoFocus
            disabled={loading}
          />

          <TextField
            fullWidth
            label="Mot de passe"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            margin="normal"
            required
            autoComplete="current-password"
            disabled={loading}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2, py: 1.5 }}
            disabled={loading || !email || !password}
          >
            {loading ? <CircularProgress size={24} /> : 'Se connecter'}
          </Button>
        </Box>

        <Divider sx={{ my: 3 }}>
          <Typography variant="body2" color="text.secondary">
            Comptes de démonstration
          </Typography>
        </Divider>

        <Stack spacing={2}>
          <Card variant="outlined">
            <CardContent sx={{ py: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="subtitle2" color="error.main">
                    Administrateur
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Accès complet au système
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={() => handleDemoLogin('admin')}
                  disabled={loading}
                  size="small"
                >
                  Connexion Admin
                </Button>
              </Box>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent sx={{ py: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="subtitle2" color="primary.main">
                    Professeur
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Gestion des cours et présences
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => handleDemoLogin('teacher')}
                  disabled={loading}
                  size="small"
                >
                  Connexion Prof
                </Button>
              </Box>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent sx={{ py: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="subtitle2" color="secondary.main">
                    Étudiant
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Consultation des présences
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => handleDemoLogin('student')}
                  disabled={loading}
                  size="small"
                >
                  Connexion Étudiant
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Stack>

        <Box mt={4} textAlign="center">
          <Typography variant="body2" color="text.secondary">
            Utilisez les comptes de démonstration ci-dessus pour tester les différents rôles
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default LoginPage;
