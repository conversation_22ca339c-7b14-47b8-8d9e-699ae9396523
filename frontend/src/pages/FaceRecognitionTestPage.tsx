import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Stack,
  Button,
  Alert,
  Tabs,
  Tab,
  Box,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Face as FaceIcon,
  PhotoCamera as PhotoCameraIcon,
  Videocam as VideocamIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import FaceDetectionCamera from '../components/FaceRecognition/FaceDetectionCamera';
import FaceRegistration from '../components/FaceRecognition/FaceRegistration';
import { faceRecognitionService } from '../services/faceRecognitionService';
import { supabaseService } from '../services/supabaseService';
import { User, FaceDetectionResult } from '../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`face-tabpanel-${index}`}
      aria-labelledby={`face-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const FaceRecognitionTestPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [detectionResults, setDetectionResults] = useState<FaceDetectionResult[]>([]);
  const [systemStats, setSystemStats] = useState(faceRecognitionService.getStats());
  const [settingsOpen, setSettingsOpen] = useState(false);

  /**
   * Charge les utilisateurs
   */
  const loadUsers = async () => {
    try {
      setLoading(true);
      const usersData = await supabaseService.getUsers();
      setUsers(usersData);
      
      // Simuler un utilisateur connecté (premier étudiant)
      const student = usersData.find(u => u.role === 'student');
      if (student) {
        setCurrentUser(student);
      }
    } catch (err) {
      console.error('Erreur chargement utilisateurs:', err);
      setError('Impossible de charger les utilisateurs');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gère les résultats de détection
   */
  const handleDetection = (result: FaceDetectionResult) => {
    setDetectionResults(prev => [result, ...prev.slice(0, 9)]); // Garder les 10 derniers
    
    if (result.detectedUsers.length > 0) {
      setSuccess(`${result.detectedUsers.length} utilisateur(s) détecté(s)`);
    }
  };

  /**
   * Initialise le système de reconnaissance
   */
  const initializeSystem = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await faceRecognitionService.initialize();
      setSystemStats(faceRecognitionService.getStats());
      setSuccess('Système de reconnaissance initialisé avec succès');
      
    } catch (err) {
      console.error('Erreur initialisation:', err);
      setError('Erreur lors de l\'initialisation du système');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Recharge les encodages
   */
  const reloadEncodings = async () => {
    try {
      setLoading(true);
      await faceRecognitionService.loadFaceEncodings();
      setSystemStats(faceRecognitionService.getStats());
      setSuccess('Encodages rechargés avec succès');
    } catch (err) {
      console.error('Erreur rechargement:', err);
      setError('Erreur lors du rechargement des encodages');
    } finally {
      setLoading(false);
    }
  };

  // Charger les données au montage
  useEffect(() => {
    loadUsers();
    initializeSystem();
  }, []);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        Test du Système de Reconnaissance Faciale
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Interface de test complète pour le système de reconnaissance faciale de PresencePro.
        Testez l'enregistrement, la détection et la reconnaissance des visages.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Statistiques du système */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">État du Système</Typography>
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => setSettingsOpen(true)}
            >
              Paramètres
            </Button>
          </Box>
          
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <Chip 
              label={systemStats.isInitialized ? "Initialisé" : "Non initialisé"} 
              color={systemStats.isInitialized ? "success" : "error"}
            />
            <Chip 
              label={`${systemStats.registeredUsers} utilisateurs enregistrés`} 
              color="primary" 
            />
            <Chip 
              label={`${systemStats.totalEncodings} encodages`} 
              color="secondary" 
            />
            <Chip 
              label={`${users.length} utilisateurs total`} 
              color="info" 
            />
          </Stack>
        </CardContent>
      </Card>

      {/* Onglets */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Enregistrement" icon={<PhotoCameraIcon />} />
          <Tab label="Détection" icon={<VideocamIcon />} />
          <Tab label="Résultats" icon={<AnalyticsIcon />} />
        </Tabs>
      </Box>

      {/* Contenu des onglets */}
      <TabPanel value={tabValue} index={0}>
        {/* Enregistrement facial */}
        <Stack spacing={3}>
          <Typography variant="h6">Enregistrement Facial</Typography>
          
          {currentUser ? (
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Utilisateur actuel: {currentUser.firstName} {currentUser.lastName}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Enregistrez vos photos faciales pour être reconnu automatiquement.
                </Typography>
                
                <FaceRegistration
                  user={currentUser}
                  onRegistrationComplete={(success) => {
                    if (success) {
                      reloadEncodings();
                    }
                  }}
                />
              </CardContent>
            </Card>
          ) : (
            <Alert severity="info">
              Aucun utilisateur sélectionné. Connectez-vous pour enregistrer vos photos faciales.
            </Alert>
          )}
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Détection en temps réel */}
        <Stack spacing={3}>
          <Typography variant="h6">Détection en Temps Réel</Typography>
          
          <FaceDetectionCamera
            onDetection={handleDetection}
            autoDetect={true}
            showControls={true}
          />
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Résultats et historique */}
        <Stack spacing={3}>
          <Typography variant="h6">Historique des Détections</Typography>
          
          {detectionResults.length === 0 ? (
            <Alert severity="info">
              Aucune détection enregistrée. Utilisez l'onglet "Détection" pour commencer.
            </Alert>
          ) : (
            <Stack spacing={2}>
              {detectionResults.map((result, index) => (
                <Card key={index}>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="subtitle2">
                        Détection #{detectionResults.length - index}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date().toLocaleTimeString()}
                      </Typography>
                    </Box>
                    
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {result.message}
                    </Typography>
                    
                    {result.detectedUsers.length > 0 && (
                      <Stack direction="row" spacing={1} flexWrap="wrap">
                        {result.detectedUsers.map((detection, userIndex) => (
                          <Chip
                            key={userIndex}
                            label={`${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`}
                            color={detection.confidence > 0.7 ? "success" : "warning"}
                            size="small"
                            icon={<FaceIcon />}
                          />
                        ))}
                      </Stack>
                    )}
                  </CardContent>
                </Card>
              ))}
            </Stack>
          )}
        </Stack>
      </TabPanel>

      {/* Dialog des paramètres */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Paramètres du Système</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                État du Système
              </Typography>
              <Stack spacing={1}>
                <Typography variant="body2">
                  <strong>Modèles chargés:</strong> {systemStats.isInitialized ? 'Oui' : 'Non'}
                </Typography>
                <Typography variant="body2">
                  <strong>Utilisateurs enregistrés:</strong> {systemStats.registeredUsers}
                </Typography>
                <Typography variant="body2">
                  <strong>Total encodages:</strong> {systemStats.totalEncodings}
                </Typography>
              </Stack>
            </Box>

            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Actions
              </Typography>
              <Stack spacing={2}>
                <Button
                  variant="outlined"
                  onClick={reloadEncodings}
                  disabled={loading}
                >
                  Recharger les Encodages
                </Button>
                <Button
                  variant="outlined"
                  onClick={initializeSystem}
                  disabled={loading}
                >
                  Réinitialiser le Système
                </Button>
              </Stack>
            </Box>

            <Alert severity="info">
              <Typography variant="body2">
                <strong>Instructions:</strong><br />
                1. Enregistrez d'abord vos photos faciales<br />
                2. Testez la détection en temps réel<br />
                3. Vérifiez les résultats dans l'historique
              </Typography>
            </Alert>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default FaceRecognitionTestPage;
