import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Stack,
  Chip,
  <PERSON>ton,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  PersonAdd as PersonAddIcon,
  Login as LoginIcon,
  MoreVert as MoreVertIcon,
  Face as FaceIcon,
  PhotoCamera as PhotoCameraIcon,
  Videocam as VideocamIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';
import { useAuth } from '../contexts/AuthContext';
import { supabaseService } from '../services/supabaseService';
import { faceRecognitionService } from '../services/faceRecognitionService';
import FaceRegistration from '../components/FaceRecognition/FaceRegistration';
import FaceDetectionCamera from '../components/FaceRecognition/FaceDetectionCamera';
import { User, Course, AttendanceRecord, DashboardStats, UserRole } from '../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AdminDashboard: React.FC = () => {
  const { user: currentUser, signOut } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // États pour la gestion multi-utilisateurs
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [loginAsDialogOpen, setLoginAsDialogOpen] = useState(false);
  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);
  const [liveRecognitionOpen, setLiveRecognitionOpen] = useState(false);
  const [studentPhotoManagementOpen, setStudentPhotoManagementOpen] = useState(false);

  // Données pour les graphiques
  const [attendanceChartData, setAttendanceChartData] = useState<any[]>([]);
  const [courseStatsData, setCourseStatsData] = useState<any[]>([]);

  /**
   * Charge toutes les données du dashboard
   */
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Charger les données en parallèle
      const [usersData, coursesData, attendanceData] = await Promise.all([
        supabaseService.getUsers(),
        supabaseService.getCourses(),
        supabaseService.getAttendanceRecords()
      ]);

      setUsers(usersData);
      setCourses(coursesData);
      setAttendanceRecords(attendanceData);

      // Calculer les statistiques
      const dashboardStats: DashboardStats = {
        totalStudents: usersData.filter(u => u.role === UserRole.STUDENT).length,
        totalTeachers: usersData.filter(u => u.role === UserRole.TEACHER).length,
        totalCourses: coursesData.length,
        todaySessions: 0, // À calculer selon les sessions du jour
        averageAttendanceRate: calculateAverageAttendanceRate(attendanceData),
        recentActivity: [] // À implémenter
      };

      setStats(dashboardStats);

      // Préparer les données pour les graphiques
      prepareChartData(attendanceData, coursesData);

    } catch (err) {
      console.error('Erreur chargement dashboard:', err);
      setError('Impossible de charger les données du dashboard');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Calcule le taux de présence moyen
   */
  const calculateAverageAttendanceRate = (records: AttendanceRecord[]): number => {
    if (records.length === 0) return 0;
    
    const presentRecords = records.filter(r => r.status === 'present' || r.status === 'late');
    return Math.round((presentRecords.length / records.length) * 100);
  };

  /**
   * Prépare les données pour les graphiques
   */
  const prepareChartData = (attendance: AttendanceRecord[], courses: Course[]) => {
    // Données de présence par jour (7 derniers jours)
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    const attendanceByDay = last7Days.map(date => {
      const dayRecords = attendance.filter(r => r.date === date);
      const present = dayRecords.filter(r => r.status === 'present').length;
      const late = dayRecords.filter(r => r.status === 'late').length;
      const absent = dayRecords.filter(r => r.status === 'absent').length;
      
      return {
        date: new Date(date).toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' }),
        present,
        late,
        absent,
        total: dayRecords.length,
        rate: dayRecords.length > 0 ? Math.round(((present + late) / dayRecords.length) * 100) : 0
      };
    });

    setAttendanceChartData(attendanceByDay);

    // Statistiques par cours
    const courseStats = courses.map(course => {
      const courseRecords = attendance.filter(r => r.course.id === course.id);
      const present = courseRecords.filter(r => r.status === 'present').length;
      const late = courseRecords.filter(r => r.status === 'late').length;
      const absent = courseRecords.filter(r => r.status === 'absent').length;
      
      return {
        name: course.name.length > 15 ? course.name.substring(0, 15) + '...' : course.name,
        present,
        late,
        absent,
        total: courseRecords.length,
        rate: courseRecords.length > 0 ? Math.round(((present + late) / courseRecords.length) * 100) : 0
      };
    });

    setCourseStatsData(courseStats);
  };

  /**
   * Se connecter en tant qu'autre utilisateur
   */
  const loginAsUser = async (user: User) => {
    try {
      // Ici, on implémenterait la logique de connexion en tant qu'autre utilisateur
      // Pour l'instant, on simule juste
      console.log(`Connexion en tant que ${user.firstName} ${user.lastName}`);
      setLoginAsDialogOpen(false);
      setSelectedUser(null);
      
      // Rediriger vers le dashboard approprié selon le rôle
      // window.location.href = user.role === 'teacher' ? '/teacher-dashboard' : '/student-dashboard';
      
    } catch (err) {
      console.error('Erreur connexion utilisateur:', err);
      setError('Impossible de se connecter en tant que cet utilisateur');
    }
  };

  /**
   * Ouvre le menu utilisateur
   */
  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setUserMenuAnchor(event.currentTarget);
    setSelectedUser(user);
  };

  // Charger les données au montage
  useEffect(() => {
    loadDashboardData();
  }, []);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* En-tête avec actions de reconnaissance faciale */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h3" gutterBottom>
            Dashboard Administrateur
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Bienvenue, {currentUser?.firstName} {currentUser?.lastName}
          </Typography>
        </Box>

        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<PhotoCameraIcon />}
            onClick={() => setStudentPhotoManagementOpen(true)}
            color="primary"
          >
            Gérer Photos Étudiants
          </Button>
          <Button
            variant="contained"
            startIcon={<VideocamIcon />}
            onClick={() => setLiveRecognitionOpen(true)}
            color="secondary"
          >
            Reconnaissance Live
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={signOut}
          >
            Déconnexion
          </Button>
        </Stack>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Statistiques générales */}
      {stats && (
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>
          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <PeopleIcon color="primary" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.totalStudents}</Typography>
                  <Typography color="text.secondary">Étudiants</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <SchoolIcon color="secondary" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.totalTeachers}</Typography>
                  <Typography color="text.secondary">Professeurs</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <DashboardIcon color="success" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.totalCourses}</Typography>
                  <Typography color="text.secondary">Cours</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AnalyticsIcon color="warning" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.averageAttendanceRate}%</Typography>
                  <Typography color="text.secondary">Taux Présence</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      )}

      {/* Onglets */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Vue d'ensemble" icon={<DashboardIcon />} />
          <Tab label="Utilisateurs" icon={<PeopleIcon />} />
          <Tab label="Cours" icon={<SchoolIcon />} />
          <Tab label="Photos Étudiants" icon={<PhotoCameraIcon />} />
          <Tab label="Reconnaissance Live" icon={<VideocamIcon />} />
        </Tabs>
      </Box>

      {/* Contenu des onglets */}
      <TabPanel value={tabValue} index={0}>
        {/* Vue d'ensemble avec graphiques */}
        <Stack spacing={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Évolution des Présences (7 derniers jours)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={attendanceChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="rate" stroke="#8884d8" name="Taux %" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Présences par Cours
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={courseStatsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="present" stackId="a" fill="#4caf50" name="Présent" />
                  <Bar dataKey="late" stackId="a" fill="#ff9800" name="Retard" />
                  <Bar dataKey="absent" stackId="a" fill="#f44336" name="Absent" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Gestion des utilisateurs */}
        <Stack spacing={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Gestion des Utilisateurs</Typography>
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={() => {/* Ouvrir dialog création utilisateur */}}
            >
              Nouvel Utilisateur
            </Button>
          </Box>

          <Stack spacing={2}>
            {users.map((user) => (
              <Card key={user.id}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Stack direction="row" spacing={2} alignItems="center">
                      <Box>
                        <Typography variant="h6">
                          {user.firstName} {user.lastName}
                        </Typography>
                        <Typography color="text.secondary">
                          {user.email} • {user.roleDisplay}
                        </Typography>
                      </Box>
                      <Chip 
                        label={user.role} 
                        color={user.role === 'admin' ? 'error' : user.role === 'teacher' ? 'primary' : 'secondary'}
                        size="small"
                      />
                    </Stack>
                    
                    <IconButton onClick={(e) => handleUserMenuClick(e, user)}>
                      <MoreVertIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Stack>
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Gestion des cours */}
        <Typography variant="h6" gutterBottom>Gestion des Cours</Typography>
        <Stack spacing={2}>
          {courses.map((course) => (
            <Card key={course.id}>
              <CardContent>
                <Typography variant="h6">{course.name}</Typography>
                <Typography color="text.secondary">
                  {course.description}
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                  <Chip label={`${course.credits} crédits`} size="small" />
                  <Chip label={course.semester} size="small" />
                </Stack>
              </CardContent>
            </Card>
          ))}
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* Gestion des photos étudiants */}
        <Typography variant="h6" gutterBottom>Gestion des Photos Étudiants</Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Instructions:</strong><br />
            1. Sélectionnez un étudiant dans la liste ci-dessous<br />
            2. Ajoutez ses photos faciales par upload ou capture caméra<br />
            3. Le système détectera automatiquement les visages et créera les encodages<br />
            4. Les photos seront associées au nom et prénom de l'étudiant
          </Typography>
        </Alert>

        <Stack spacing={3}>
          {/* Liste des étudiants */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sélectionner un Étudiant
              </Typography>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Photo</TableCell>
                      <TableCell>Nom</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Encodages</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.filter(u => u.role === UserRole.STUDENT).map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <Avatar sx={{ width: 40, height: 40 }}>
                            {student.firstName[0]}{student.lastName[0]}
                          </Avatar>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {student.firstName} {student.lastName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {student.username}
                          </Typography>
                        </TableCell>
                        <TableCell>{student.email}</TableCell>
                        <TableCell>
                          <Chip
                            label="0 photos"
                            size="small"
                            color="default"
                          />
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <Button
                              size="small"
                              variant="outlined"
                              startIcon={<PhotoCameraIcon />}
                              onClick={() => {
                                setSelectedUser(student);
                                setFaceRegistrationOpen(true);
                              }}
                            >
                              Ajouter Photos
                            </Button>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => {
                                // Supprimer les encodages de l'étudiant
                                if (window.confirm(`Supprimer toutes les photos de ${student.firstName} ${student.lastName} ?`)) {
                                  faceRecognitionService.deleteFaceEncodings(student.id);
                                }
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        {/* Reconnaissance live */}
        <Typography variant="h6" gutterBottom>Reconnaissance Faciale en Temps Réel</Typography>

        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Fonctionnalités:</strong><br />
            • Détection automatique des visages en temps réel<br />
            • Reconnaissance des étudiants avec leurs noms<br />
            • Enregistrement automatique des présences<br />
            • Affichage du niveau de confiance de la reconnaissance
          </Typography>
        </Alert>

        <Card>
          <CardContent>
            <FaceDetectionCamera
              onDetection={(result) => {
                console.log('Détection admin:', result);
                // Ici on pourrait traiter les résultats de détection
              }}
              autoDetect={true}
              showControls={true}
            />
          </CardContent>
        </Card>
      </TabPanel>

      {/* Menu utilisateur */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={() => setUserMenuAnchor(null)}
      >
        <MenuItem onClick={() => setLoginAsDialogOpen(true)}>
          <LoginIcon sx={{ mr: 1 }} />
          Se connecter en tant que
        </MenuItem>
        <MenuItem onClick={() => {/* Éditer utilisateur */}}>
          <SettingsIcon sx={{ mr: 1 }} />
          Modifier
        </MenuItem>
      </Menu>

      {/* Dialog connexion en tant qu'utilisateur */}
      <Dialog open={loginAsDialogOpen} onClose={() => setLoginAsDialogOpen(false)}>
        <DialogTitle>Se connecter en tant qu'utilisateur</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Typography>
              Voulez-vous vous connecter en tant que{' '}
              <strong>{selectedUser.firstName} {selectedUser.lastName}</strong> ?
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLoginAsDialogOpen(false)}>Annuler</Button>
          <Button
            onClick={() => selectedUser && loginAsUser(selectedUser)}
            variant="contained"
          >
            Se connecter
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog gestion photos étudiants */}
      <Dialog
        open={studentPhotoManagementOpen}
        onClose={() => setStudentPhotoManagementOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" spacing={1}>
            <PhotoCameraIcon />
            <Typography variant="h6">
              Gestion des Photos Étudiants
            </Typography>
          </Stack>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Sélectionnez un étudiant et ajoutez ses photos faciales pour la reconnaissance automatique.
          </Typography>

          {/* Sélecteur d'étudiant */}
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Sélectionner un étudiant</InputLabel>
            <Select
              value={selectedUser?.id || ''}
              onChange={(e) => {
                const student = users.find(u => u.id === e.target.value);
                setSelectedUser(student || null);
              }}
              label="Sélectionner un étudiant"
            >
              {users.filter(u => u.role === UserRole.STUDENT).map((student) => (
                <MenuItem key={student.id} value={student.id}>
                  {student.firstName} {student.lastName} ({student.email})
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Composant d'enregistrement facial */}
          {selectedUser && (
            <FaceRegistration
              user={selectedUser}
              onRegistrationComplete={(success) => {
                if (success) {
                  // Recharger les statistiques
                  console.log('Photos ajoutées avec succès pour', selectedUser.firstName);
                }
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setStudentPhotoManagementOpen(false);
            setSelectedUser(null);
          }}>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog reconnaissance live */}
      <Dialog
        open={liveRecognitionOpen}
        onClose={() => setLiveRecognitionOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" spacing={1}>
            <VideocamIcon />
            <Typography variant="h6">
              Reconnaissance Faciale en Temps Réel
            </Typography>
          </Stack>
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Mode Administrateur:</strong> Utilisez cette fonction pour tester la reconnaissance
              faciale et enregistrer les présences en temps réel. Les étudiants détectés seront
              automatiquement marqués présents.
            </Typography>
          </Alert>

          <FaceDetectionCamera
            onDetection={(result) => {
              console.log('Détection admin:', result);
              if (result.detectedUsers.length > 0) {
                // Ici on pourrait automatiquement enregistrer les présences
                result.detectedUsers.forEach(detection => {
                  console.log(`Étudiant détecté: ${detection.user.firstName} ${detection.user.lastName} (${Math.round(detection.confidence * 100)}%)`);
                });
              }
            }}
            autoDetect={true}
            showControls={true}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLiveRecognitionOpen(false)}>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog enregistrement facial (pour utilisateur sélectionné) */}
      <Dialog
        open={faceRegistrationOpen}
        onClose={() => setFaceRegistrationOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Enregistrement Facial
          {selectedUser && ` - ${selectedUser.firstName} ${selectedUser.lastName}`}
        </DialogTitle>
        <DialogContent>
          {selectedUser ? (
            <FaceRegistration
              user={selectedUser}
              onRegistrationComplete={(success) => {
                if (success) {
                  console.log('Enregistrement terminé pour', selectedUser.firstName);
                  // Optionnel: recharger les données
                }
              }}
            />
          ) : (
            <Alert severity="warning">
              Aucun utilisateur sélectionné
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setFaceRegistrationOpen(false);
            setSelectedUser(null);
          }}>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AdminDashboard;
