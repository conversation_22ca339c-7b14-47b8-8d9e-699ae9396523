import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Stack,
  Chip,
  Button,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  PersonAdd as PersonAddIcon,
  Login as LoginIcon,
  MoreVert as MoreVertIcon,
  Face as FaceIcon
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { supabaseService } from '../services/supabaseService';
import { faceRecognitionService } from '../services/faceRecognitionService';
import { User, Course, AttendanceRecord, DashboardStats, UserRole } from '../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AdminDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // États pour la gestion multi-utilisateurs
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [loginAsDialogOpen, setLoginAsDialogOpen] = useState(false);
  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);

  // Données pour les graphiques
  const [attendanceChartData, setAttendanceChartData] = useState<any[]>([]);
  const [courseStatsData, setCourseStatsData] = useState<any[]>([]);

  /**
   * Charge toutes les données du dashboard
   */
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Charger les données en parallèle
      const [usersData, coursesData, attendanceData] = await Promise.all([
        supabaseService.getUsers(),
        supabaseService.getCourses(),
        supabaseService.getAttendanceRecords()
      ]);

      setUsers(usersData);
      setCourses(coursesData);
      setAttendanceRecords(attendanceData);

      // Calculer les statistiques
      const dashboardStats: DashboardStats = {
        totalStudents: usersData.filter(u => u.role === UserRole.STUDENT).length,
        totalTeachers: usersData.filter(u => u.role === UserRole.TEACHER).length,
        totalCourses: coursesData.length,
        todaySessions: 0, // À calculer selon les sessions du jour
        averageAttendanceRate: calculateAverageAttendanceRate(attendanceData),
        recentActivity: [] // À implémenter
      };

      setStats(dashboardStats);

      // Préparer les données pour les graphiques
      prepareChartData(attendanceData, coursesData);

    } catch (err) {
      console.error('Erreur chargement dashboard:', err);
      setError('Impossible de charger les données du dashboard');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Calcule le taux de présence moyen
   */
  const calculateAverageAttendanceRate = (records: AttendanceRecord[]): number => {
    if (records.length === 0) return 0;
    
    const presentRecords = records.filter(r => r.status === 'present' || r.status === 'late');
    return Math.round((presentRecords.length / records.length) * 100);
  };

  /**
   * Prépare les données pour les graphiques
   */
  const prepareChartData = (attendance: AttendanceRecord[], courses: Course[]) => {
    // Données de présence par jour (7 derniers jours)
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    const attendanceByDay = last7Days.map(date => {
      const dayRecords = attendance.filter(r => r.date === date);
      const present = dayRecords.filter(r => r.status === 'present').length;
      const late = dayRecords.filter(r => r.status === 'late').length;
      const absent = dayRecords.filter(r => r.status === 'absent').length;
      
      return {
        date: new Date(date).toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' }),
        present,
        late,
        absent,
        total: dayRecords.length,
        rate: dayRecords.length > 0 ? Math.round(((present + late) / dayRecords.length) * 100) : 0
      };
    });

    setAttendanceChartData(attendanceByDay);

    // Statistiques par cours
    const courseStats = courses.map(course => {
      const courseRecords = attendance.filter(r => r.course.id === course.id);
      const present = courseRecords.filter(r => r.status === 'present').length;
      const late = courseRecords.filter(r => r.status === 'late').length;
      const absent = courseRecords.filter(r => r.status === 'absent').length;
      
      return {
        name: course.name.length > 15 ? course.name.substring(0, 15) + '...' : course.name,
        present,
        late,
        absent,
        total: courseRecords.length,
        rate: courseRecords.length > 0 ? Math.round(((present + late) / courseRecords.length) * 100) : 0
      };
    });

    setCourseStatsData(courseStats);
  };

  /**
   * Se connecter en tant qu'autre utilisateur
   */
  const loginAsUser = async (user: User) => {
    try {
      // Ici, on implémenterait la logique de connexion en tant qu'autre utilisateur
      // Pour l'instant, on simule juste
      console.log(`Connexion en tant que ${user.firstName} ${user.lastName}`);
      setLoginAsDialogOpen(false);
      setSelectedUser(null);
      
      // Rediriger vers le dashboard approprié selon le rôle
      // window.location.href = user.role === 'teacher' ? '/teacher-dashboard' : '/student-dashboard';
      
    } catch (err) {
      console.error('Erreur connexion utilisateur:', err);
      setError('Impossible de se connecter en tant que cet utilisateur');
    }
  };

  /**
   * Ouvre le menu utilisateur
   */
  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setUserMenuAnchor(event.currentTarget);
    setSelectedUser(user);
  };

  // Charger les données au montage
  useEffect(() => {
    loadDashboardData();
  }, []);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        Dashboard Administrateur
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistiques générales */}
      {stats && (
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>
          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <PeopleIcon color="primary" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.totalStudents}</Typography>
                  <Typography color="text.secondary">Étudiants</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <SchoolIcon color="secondary" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.totalTeachers}</Typography>
                  <Typography color="text.secondary">Professeurs</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <DashboardIcon color="success" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.totalCourses}</Typography>
                  <Typography color="text.secondary">Cours</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1 }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AnalyticsIcon color="warning" fontSize="large" />
                <Box>
                  <Typography variant="h4">{stats.averageAttendanceRate}%</Typography>
                  <Typography color="text.secondary">Taux Présence</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      )}

      {/* Onglets */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Vue d'ensemble" icon={<DashboardIcon />} />
          <Tab label="Utilisateurs" icon={<PeopleIcon />} />
          <Tab label="Cours" icon={<SchoolIcon />} />
          <Tab label="Reconnaissance Faciale" icon={<FaceIcon />} />
        </Tabs>
      </Box>

      {/* Contenu des onglets */}
      <TabPanel value={tabValue} index={0}>
        {/* Vue d'ensemble avec graphiques */}
        <Stack spacing={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Évolution des Présences (7 derniers jours)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={attendanceChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="rate" stroke="#8884d8" name="Taux %" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Présences par Cours
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={courseStatsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="present" stackId="a" fill="#4caf50" name="Présent" />
                  <Bar dataKey="late" stackId="a" fill="#ff9800" name="Retard" />
                  <Bar dataKey="absent" stackId="a" fill="#f44336" name="Absent" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Gestion des utilisateurs */}
        <Stack spacing={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Gestion des Utilisateurs</Typography>
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={() => {/* Ouvrir dialog création utilisateur */}}
            >
              Nouvel Utilisateur
            </Button>
          </Box>

          <Stack spacing={2}>
            {users.map((user) => (
              <Card key={user.id}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Stack direction="row" spacing={2} alignItems="center">
                      <Box>
                        <Typography variant="h6">
                          {user.firstName} {user.lastName}
                        </Typography>
                        <Typography color="text.secondary">
                          {user.email} • {user.roleDisplay}
                        </Typography>
                      </Box>
                      <Chip 
                        label={user.role} 
                        color={user.role === 'admin' ? 'error' : user.role === 'teacher' ? 'primary' : 'secondary'}
                        size="small"
                      />
                    </Stack>
                    
                    <IconButton onClick={(e) => handleUserMenuClick(e, user)}>
                      <MoreVertIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Stack>
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Gestion des cours */}
        <Typography variant="h6" gutterBottom>Gestion des Cours</Typography>
        <Stack spacing={2}>
          {courses.map((course) => (
            <Card key={course.id}>
              <CardContent>
                <Typography variant="h6">{course.name}</Typography>
                <Typography color="text.secondary">
                  {course.description}
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                  <Chip label={`${course.credits} crédits`} size="small" />
                  <Chip label={course.semester} size="small" />
                </Stack>
              </CardContent>
            </Card>
          ))}
        </Stack>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* Reconnaissance faciale */}
        <Typography variant="h6" gutterBottom>Système de Reconnaissance Faciale</Typography>
        <Card>
          <CardContent>
            <Stack spacing={2}>
              <Typography>
                Statistiques du système de reconnaissance faciale
              </Typography>
              {/* Ici on afficherait les stats du service de reconnaissance */}
              <Button
                variant="outlined"
                onClick={() => setFaceRegistrationOpen(true)}
              >
                Gérer les Encodages Faciaux
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Menu utilisateur */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={() => setUserMenuAnchor(null)}
      >
        <MenuItem onClick={() => setLoginAsDialogOpen(true)}>
          <LoginIcon sx={{ mr: 1 }} />
          Se connecter en tant que
        </MenuItem>
        <MenuItem onClick={() => {/* Éditer utilisateur */}}>
          <SettingsIcon sx={{ mr: 1 }} />
          Modifier
        </MenuItem>
      </Menu>

      {/* Dialog connexion en tant qu'utilisateur */}
      <Dialog open={loginAsDialogOpen} onClose={() => setLoginAsDialogOpen(false)}>
        <DialogTitle>Se connecter en tant qu'utilisateur</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Typography>
              Voulez-vous vous connecter en tant que{' '}
              <strong>{selectedUser.firstName} {selectedUser.lastName}</strong> ?
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLoginAsDialogOpen(false)}>Annuler</Button>
          <Button 
            onClick={() => selectedUser && loginAsUser(selectedUser)}
            variant="contained"
          >
            Se connecter
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AdminDashboard;
