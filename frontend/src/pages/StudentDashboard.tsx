import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Card,
  CardContent,
  Stack,
  Box,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Face as FaceIcon,
  PhotoCamera as PhotoCameraIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { supabaseService } from '../services/supabaseService';
import FaceRegistration from '../components/FaceRecognition/FaceRegistration';
import { AttendanceRecord, Course, AttendanceStatus } from '../types';

const StudentDashboard: React.FC = () => {
  const { user, signOut } = useAuth();
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);

  /**
   * Charge les données de l'étudiant
   */
  const loadStudentData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Charger les présences de l'étudiant
      const studentAttendance = await supabaseService.getAttendanceByStudent(user.id);
      setAttendanceRecords(studentAttendance);

      // Charger les cours (pour l'instant tous les cours)
      const allCourses = await supabaseService.getCourses();
      setCourses(allCourses);

    } catch (err) {
      console.error('Erreur chargement données étudiant:', err);
      setError('Impossible de charger vos données');
    } finally {
      setLoading(false);
    }
  };

  // Charger les données au montage
  useEffect(() => {
    loadStudentData();
  }, [user]);

  /**
   * Calcule les statistiques de présence
   */
  const getAttendanceStats = () => {
    const total = attendanceRecords.length;
    const present = attendanceRecords.filter(r => r.status === AttendanceStatus.PRESENT).length;
    const late = attendanceRecords.filter(r => r.status === AttendanceStatus.LATE).length;
    const absent = attendanceRecords.filter(r => r.status === AttendanceStatus.ABSENT).length;
    const attendanceRate = total > 0 ? Math.round(((present + late) / total) * 100) : 0;

    return { total, present, late, absent, attendanceRate };
  };

  /**
   * Obtient l'icône pour un statut de présence
   */
  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return <CheckCircleIcon color="success" />;
      case AttendanceStatus.LATE:
        return <WarningIcon color="warning" />;
      case AttendanceStatus.ABSENT:
        return <CancelIcon color="error" />;
      default:
        return <ScheduleIcon color="disabled" />;
    }
  };

  /**
   * Obtient la couleur pour un statut de présence
   */
  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'success';
      case AttendanceStatus.LATE:
        return 'warning';
      case AttendanceStatus.ABSENT:
        return 'error';
      default:
        return 'default';
    }
  };

  const stats = getAttendanceStats();

  if (!user) {
    return (
      <Container>
        <Alert severity="error">
          Utilisateur non connecté
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* En-tête */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h3" gutterBottom>
            Dashboard Étudiant
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Bienvenue, {user.firstName} {user.lastName}
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<FaceIcon />}
            onClick={() => setFaceRegistrationOpen(true)}
          >
            Gérer mes Photos
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={signOut}
          >
            Déconnexion
          </Button>
        </Stack>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Statistiques de présence */}
      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <CheckCircleIcon color="success" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.present}</Typography>
                <Typography color="text.secondary">Présences</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <WarningIcon color="warning" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.late}</Typography>
                <Typography color="text.secondary">Retards</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <CancelIcon color="error" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.absent}</Typography>
                <Typography color="text.secondary">Absences</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ScheduleIcon color="primary" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.attendanceRate}%</Typography>
                <Typography color="text.secondary">Taux Présence</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Stack>

      {/* Historique des présences */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Historique des Présences
          </Typography>
          
          {attendanceRecords.length === 0 ? (
            <Alert severity="info">
              Aucun enregistrement de présence trouvé.
            </Alert>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Cours</TableCell>
                    <TableCell>Heure</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Méthode</TableCell>
                    <TableCell>Notes</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {attendanceRecords
                    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                    .map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        {new Date(record.date).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {record.course?.name || 'Cours inconnu'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {record.course?.code || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {new Date(record.time).toLocaleTimeString('fr-FR')}
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1} alignItems="center">
                          {getStatusIcon(record.status)}
                          <Chip
                            label={record.status}
                            color={getStatusColor(record.status) as any}
                            size="small"
                          />
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={record.method}
                          variant="outlined"
                          size="small"
                          icon={record.method === 'face_recognition' ? <FaceIcon /> : undefined}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {record.notes || '-'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Dialog d'enregistrement facial */}
      <Dialog 
        open={faceRegistrationOpen} 
        onClose={() => setFaceRegistrationOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" spacing={1}>
            <PhotoCameraIcon />
            <Typography variant="h6">
              Gestion de mes Photos Faciales
            </Typography>
          </Stack>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Enregistrez vos photos faciales pour être reconnu automatiquement 
            lors des sessions de présence.
          </Typography>
          
          <FaceRegistration
            user={user}
            onRegistrationComplete={(success) => {
              if (success) {
                // Optionnel: recharger les données ou afficher un message
                console.log('Photos faciales mises à jour avec succès');
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFaceRegistrationOpen(false)}>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default StudentDashboard;
