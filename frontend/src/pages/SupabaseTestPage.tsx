import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Chip,
  Alert,
  Divider,
  Stack
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Storage as StorageIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import SupabaseAuth from '../components/Supabase/SupabaseAuth';
import SupabaseImageUpload from '../components/Supabase/SupabaseImageUpload';
import { supabase } from '../config/supabase';
import type { User } from '@supabase/supabase-js';

const SupabaseTestPage: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');

  useEffect(() => {
    // Vérifier la connexion Supabase
    const checkConnection = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) throw error;
        setConnectionStatus('connected');
        setUser(data.session?.user ?? null);
      } catch (error) {
        console.error('Erreur de connexion Supabase:', error);
        setConnectionStatus('error');
      }
    };

    checkConnection();

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const StatusCard: React.FC<{ 
    title: string; 
    status: 'success' | 'error' | 'warning'; 
    description: string;
    icon: React.ReactNode;
  }> = ({ title, status, description, icon }) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          {icon}
          <Typography variant="h6">{title}</Typography>
          <Chip
            label={status === 'success' ? 'OK' : status === 'error' ? 'Erreur' : 'Attention'}
            color={status}
            size="small"
          />
        </Box>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom align="center">
        Test Supabase - PresencePro
      </Typography>
      
      <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
        Page de test pour vérifier l'intégration Supabase : authentification et upload d'images
      </Typography>

      {/* Status de connexion */}
      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>
        <StatusCard
          title="Connexion Supabase"
          status={connectionStatus === 'connected' ? 'success' : connectionStatus === 'error' ? 'error' : 'warning'}
          description={
            connectionStatus === 'connected'
              ? 'Connexion établie avec succès'
              : connectionStatus === 'error'
              ? 'Erreur de connexion à Supabase'
              : 'Vérification en cours...'
          }
          icon={connectionStatus === 'connected' ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
        />

        <StatusCard
          title="Authentification"
          status={user ? 'success' : 'warning'}
          description={
            user
              ? `Connecté en tant que ${user.email}`
              : 'Aucun utilisateur connecté'
          }
          icon={<SecurityIcon color={user ? 'success' : 'warning'} />}
        />
      </Stack>

      {/* Configuration actuelle */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Configuration Supabase
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Stack spacing={2}>
            <Typography variant="body2">
              <strong>Project URL:</strong><br />
              {process.env.REACT_APP_SUPABASE_URL || 'Non configuré'}
            </Typography>
            <Typography variant="body2">
              <strong>Anon Key:</strong><br />
              {process.env.REACT_APP_SUPABASE_ANON_KEY ?
                `${process.env.REACT_APP_SUPABASE_ANON_KEY.substring(0, 20)}...` :
                'Non configuré'
              }
            </Typography>
          </Stack>
        </CardContent>
      </Card>

      {connectionStatus === 'error' && (
        <Alert severity="error" sx={{ mb: 4 }}>
          <Typography variant="h6">Erreur de connexion Supabase</Typography>
          <Typography>
            Vérifiez que les variables d'environnement REACT_APP_SUPABASE_URL et REACT_APP_SUPABASE_ANON_KEY 
            sont correctement configurées dans le fichier .env
          </Typography>
        </Alert>
      )}

      {/* Section Authentification */}
      <Stack direction={{ xs: 'column', lg: 'row' }} spacing={4}>
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <SecurityIcon />
              <Typography variant="h5">Authentification</Typography>
            </Box>
            <SupabaseAuth />
          </CardContent>
        </Card>

        {/* Section Upload d'Images */}
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <StorageIcon />
              <Typography variant="h5">Upload d'Images</Typography>
            </Box>

            {!user ? (
              <Alert severity="info">
                Connectez-vous pour tester l'upload d'images
              </Alert>
            ) : (
              <SupabaseImageUpload />
            )}
          </CardContent>
        </Card>
      </Stack>

      {/* Instructions */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Instructions de Test
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="body2" component="div">
            <strong>1. Authentification :</strong><br />
            • Créez un compte ou connectez-vous avec un compte existant<br />
            • Vérifiez que l'authentification fonctionne correctement<br /><br />
            
            <strong>2. Upload d'Images :</strong><br />
            • Une fois connecté, testez l'upload d'images de profil et faciales<br />
            • Vérifiez que les images sont bien stockées dans le bucket Supabase<br />
            • Testez la suppression d'images<br /><br />
            
            <strong>3. Politiques de Sécurité :</strong><br />
            • Les uploads nécessitent une authentification (politique INSERT)<br />
            • La lecture des images est publique (politique SELECT)<br />
            • Les images sont stockées dans le bucket "images"
          </Typography>
        </CardContent>
      </Card>
    </Container>
  );
};

export default SupabaseTestPage;
