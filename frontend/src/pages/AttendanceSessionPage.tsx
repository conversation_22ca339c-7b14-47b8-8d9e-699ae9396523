import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Stack,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Schedule as ScheduleIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import AutoAttendanceSystem from '../components/Attendance/AutoAttendanceSystem';
import { supabaseService } from '../services/supabaseService';
import { Course, AttendanceRecord, User } from '../types';

const AttendanceSessionPage: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [sessionActive, setSessionActive] = useState(false);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);

  /**
   * Charge les cours disponibles
   */
  const loadCourses = async () => {
    try {
      setLoading(true);
      const coursesData = await supabaseService.getCourses();
      setCourses(coursesData);
    } catch (err) {
      console.error('Erreur chargement cours:', err);
      setError('Impossible de charger les cours');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gère la mise à jour des enregistrements de présence
   */
  const handleAttendanceUpdate = (records: AttendanceRecord[]) => {
    setAttendanceRecords(records);
  };

  /**
   * Génère un rapport de session
   */
  const generateSessionReport = () => {
    if (!selectedCourse || attendanceRecords.length === 0) return;

    const present = attendanceRecords.filter(r => r.status === 'present').length;
    const late = attendanceRecords.filter(r => r.status === 'late').length;
    const absent = attendanceRecords.filter(r => r.status === 'absent').length;
    const total = attendanceRecords.length;
    const attendanceRate = total > 0 ? Math.round(((present + late) / total) * 100) : 0;

    return {
      course: selectedCourse,
      total,
      present,
      late,
      absent,
      attendanceRate,
      records: attendanceRecords
    };
  };

  // Charger les données au montage
  useEffect(() => {
    loadCourses();
  }, []);

  const sessionReport = generateSessionReport();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        Session de Présence Automatique
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Lancez une session de reconnaissance faciale pour enregistrer automatiquement 
        les présences des étudiants.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Stack spacing={4}>
        {/* Sélection du cours */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Configuration de la Session
            </Typography>
            
            <Stack spacing={3}>
              <FormControl fullWidth>
                <InputLabel>Sélectionner un cours</InputLabel>
                <Select
                  value={selectedCourse?.id || ''}
                  onChange={(e) => {
                    const course = courses.find(c => c.id === e.target.value);
                    setSelectedCourse(course || null);
                  }}
                  label="Sélectionner un cours"
                  disabled={sessionActive}
                >
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name} - {course.semester}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {selectedCourse && (
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6">{selectedCourse.name}</Typography>
                    <Typography color="text.secondary" gutterBottom>
                      {selectedCourse.description}
                    </Typography>
                    <Stack direction="row" spacing={1}>
                      <Chip label={`${selectedCourse.credits} crédits`} size="small" />
                      <Chip label={selectedCourse.semester} size="small" />
                      <Chip 
                        label={`Professeur: ${selectedCourse.teacher?.firstName} ${selectedCourse.teacher?.lastName}`} 
                        size="small" 
                      />
                    </Stack>
                  </CardContent>
                </Card>
              )}

              {!sessionActive && selectedCourse && (
                <Alert severity="info">
                  <Typography variant="body2">
                    <strong>Instructions:</strong><br />
                    1. Assurez-vous que les étudiants ont enregistré leurs photos faciales<br />
                    2. Cliquez sur "Démarrer Session" pour lancer la reconnaissance<br />
                    3. Les présences seront enregistrées automatiquement<br />
                    4. Arrêtez la session quand tous les étudiants sont arrivés
                  </Typography>
                </Alert>
              )}
            </Stack>
          </CardContent>
        </Card>

        {/* Système de présence automatique */}
        {selectedCourse && (
          <AutoAttendanceSystem
            course={selectedCourse}
            onAttendanceUpdate={handleAttendanceUpdate}
          />
        )}

        {/* Résumé de session */}
        {sessionReport && attendanceRecords.length > 0 && (
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Résumé de la Session
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AnalyticsIcon />}
                  onClick={() => setReportDialogOpen(true)}
                >
                  Rapport Détaillé
                </Button>
              </Box>

              <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
                <Card variant="outlined" sx={{ flex: 1 }}>
                  <CardContent>
                    <Typography variant="h4" color="success.main">
                      {sessionReport.present}
                    </Typography>
                    <Typography color="text.secondary">Présents</Typography>
                  </CardContent>
                </Card>

                <Card variant="outlined" sx={{ flex: 1 }}>
                  <CardContent>
                    <Typography variant="h4" color="warning.main">
                      {sessionReport.late}
                    </Typography>
                    <Typography color="text.secondary">En Retard</Typography>
                  </CardContent>
                </Card>

                <Card variant="outlined" sx={{ flex: 1 }}>
                  <CardContent>
                    <Typography variant="h4" color="error.main">
                      {sessionReport.absent}
                    </Typography>
                    <Typography color="text.secondary">Absents</Typography>
                  </CardContent>
                </Card>

                <Card variant="outlined" sx={{ flex: 1 }}>
                  <CardContent>
                    <Typography variant="h4" color="primary.main">
                      {sessionReport.attendanceRate}%
                    </Typography>
                    <Typography color="text.secondary">Taux de Présence</Typography>
                  </CardContent>
                </Card>
              </Stack>
            </CardContent>
          </Card>
        )}

        {/* Instructions d'utilisation */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Guide d'Utilisation
            </Typography>
            
            <Stack spacing={2}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  1. Préparation
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Assurez-vous que tous les étudiants ont enregistré leurs photos faciales
                  • Vérifiez que la caméra fonctionne correctement
                  • Sélectionnez le cours approprié
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  2. Démarrage de Session
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Cliquez sur "Démarrer Session" au début du cours
                  • La caméra se lance automatiquement
                  • Les étudiants sont détectés et marqués présents en temps réel
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  3. Gestion des Retards
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Les étudiants arrivant après le délai configuré sont marqués "En retard"
                  • Le seuil par défaut est de 15 minutes (configurable)
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  4. Fin de Session
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Cliquez sur "Arrêter Session" quand tous les étudiants sont arrivés
                  • Les étudiants non détectés sont automatiquement marqués absents
                  • Un rapport de session est généré
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Stack>

      {/* Dialog du rapport détaillé */}
      <Dialog 
        open={reportDialogOpen} 
        onClose={() => setReportDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Rapport de Session Détaillé</DialogTitle>
        <DialogContent>
          {sessionReport && (
            <Stack spacing={3}>
              <Box>
                <Typography variant="h6">{sessionReport.course.name}</Typography>
                <Typography color="text.secondary">
                  Session du {new Date().toLocaleDateString('fr-FR')}
                </Typography>
              </Box>

              <Stack direction="row" spacing={2}>
                <Chip 
                  label={`${sessionReport.present} Présents`} 
                  color="success" 
                />
                <Chip 
                  label={`${sessionReport.late} En Retard`} 
                  color="warning" 
                />
                <Chip 
                  label={`${sessionReport.absent} Absents`} 
                  color="error" 
                />
                <Chip 
                  label={`${sessionReport.attendanceRate}% Présence`} 
                  color="primary" 
                />
              </Stack>

              <Typography variant="body2" color="text.secondary">
                Cette session a été enregistrée automatiquement via reconnaissance faciale.
                Les données sont sauvegardées dans la base de données Supabase.
              </Typography>
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReportDialogOpen(false)}>Fermer</Button>
          <Button variant="contained" onClick={() => {
            // Ici on pourrait exporter le rapport
            console.log('Export rapport:', sessionReport);
          }}>
            Exporter
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AttendanceSessionPage;
