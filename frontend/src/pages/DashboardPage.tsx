/**
 * Page du tableau de bord pour PresencePro
 */

import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  LinearProgress,
  Paper,
  IconButton,
} from '@mui/material';
import {
  People,
  School,
  EventNote,
  TrendingUp,
  Person,
  AccessTime,
  CheckCircle,
  Cancel,
  Refresh,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { UserRole, DashboardStats, ActivityItem } from '../types';

// Composant pour les cartes de statistiques
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="overline">
            {title}
          </Typography>
          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
            {value}
          </Typography>
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <TrendingUp
                sx={{
                  color: trend.isPositive ? 'success.main' : 'error.main',
                  mr: 0.5,
                  fontSize: 16,
                }}
              />
              <Typography
                variant="body2"
                sx={{
                  color: trend.isPositive ? 'success.main' : 'error.main',
                  fontWeight: 'medium',
                }}
              >
                {trend.isPositive ? '+' : ''}{trend.value}%
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar
          sx={{
            backgroundColor: color,
            width: 56,
            height: 56,
          }}
        >
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

// Composant pour les activités récentes
interface RecentActivityProps {
  activities: ActivityItem[];
}

const RecentActivity: React.FC<RecentActivityProps> = ({ activities }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
          Activité récente
        </Typography>
        <IconButton size="small">
          <Refresh />
        </IconButton>
      </Box>
      <List sx={{ maxHeight: 300, overflow: 'auto' }}>
        {activities.map((activity) => (
          <ListItem key={activity.id} sx={{ px: 0 }}>
            <ListItemAvatar>
              <Avatar
                sx={{
                  backgroundColor: getActivityColor(activity.type),
                  width: 32,
                  height: 32,
                }}
              >
                {getActivityIcon(activity.type)}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={activity.message}
              secondary={formatTimestamp(activity.timestamp)}
              primaryTypographyProps={{ variant: 'body2' }}
              secondaryTypographyProps={{ variant: 'caption' }}
            />
          </ListItem>
        ))}
      </List>
    </CardContent>
  </Card>
);

// Fonctions utilitaires
const getActivityColor = (type: string) => {
  switch (type) {
    case 'attendance': return '#4caf50';
    case 'course': return '#2196f3';
    case 'user': return '#ff9800';
    case 'system': return '#9c27b0';
    default: return '#757575';
  }
};

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'attendance': return <CheckCircle sx={{ fontSize: 16 }} />;
    case 'course': return <School sx={{ fontSize: 16 }} />;
    case 'user': return <Person sx={{ fontSize: 16 }} />;
    case 'system': return <AccessTime sx={{ fontSize: 16 }} />;
    default: return <EventNote sx={{ fontSize: 16 }} />;
  }
};

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'À l\'instant';
  if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
  if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)} h`;
  return date.toLocaleDateString('fr-FR');
};

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simuler le chargement des données
    const loadDashboardData = async () => {
      setLoading(true);
      
      // Données simulées - à remplacer par des appels API réels
      const mockStats: DashboardStats = {
        totalStudents: 1250,
        totalTeachers: 45,
        totalCourses: 120,
        todaySessions: 8,
        averageAttendanceRate: 87.5,
        recentActivity: [
          {
            id: '1',
            type: 'attendance',
            message: 'Présence enregistrée pour le cours de Mathématiques',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            type: 'course',
            message: 'Nouveau cours créé : Physique Quantique',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            type: 'user',
            message: 'Nouvel étudiant inscrit : Marie Dubois',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          },
          {
            id: '4',
            type: 'attendance',
            message: 'Rapport de présence généré pour la semaine',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
        ],
      };

      // Simuler un délai de chargement
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats(mockStats);
      setLoading(false);
    };

    loadDashboardData();
  }, []);

  const getStatsForRole = () => {
    if (!stats || !user) return [];

    const baseStats = [
      {
        title: 'Taux de présence moyen',
        value: `${stats.averageAttendanceRate}%`,
        icon: <TrendingUp />,
        color: '#4caf50',
        trend: { value: 2.5, isPositive: true },
      },
    ];

    switch (user.role) {
      case UserRole.ADMIN:
        return [
          {
            title: 'Total étudiants',
            value: stats.totalStudents,
            icon: <People />,
            color: '#2196f3',
            trend: { value: 5.2, isPositive: true },
          },
          {
            title: 'Total enseignants',
            value: stats.totalTeachers,
            icon: <Person />,
            color: '#ff9800',
          },
          {
            title: 'Total cours',
            value: stats.totalCourses,
            icon: <School />,
            color: '#9c27b0',
            trend: { value: 1.8, isPositive: true },
          },
          ...baseStats,
        ];
      
      case UserRole.TEACHER:
        return [
          {
            title: 'Mes cours',
            value: 6,
            icon: <School />,
            color: '#2196f3',
          },
          {
            title: 'Sessions aujourd\'hui',
            value: stats.todaySessions,
            icon: <EventNote />,
            color: '#ff9800',
          },
          {
            title: 'Étudiants total',
            value: 180,
            icon: <People />,
            color: '#9c27b0',
          },
          ...baseStats,
        ];
      
      case UserRole.STUDENT:
        return [
          {
            title: 'Mes cours',
            value: 8,
            icon: <School />,
            color: '#2196f3',
          },
          {
            title: 'Cours aujourd\'hui',
            value: 3,
            icon: <EventNote />,
            color: '#ff9800',
          },
          {
            title: 'Ma présence',
            value: '92%',
            icon: <CheckCircle />,
            color: '#4caf50',
            trend: { value: 3.1, isPositive: true },
          },
          {
            title: 'Absences ce mois',
            value: 2,
            icon: <Cancel />,
            color: '#f44336',
          },
        ];
      
      default:
        return baseStats;
    }
  };

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
          Tableau de bord
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        Tableau de bord
      </Typography>

      <Grid container spacing={3}>
        {/* Cartes de statistiques */}
        {getStatsForRole().map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard {...stat} />
          </Grid>
        ))}

        {/* Activité récente */}
        <Grid item xs={12} md={6}>
          <RecentActivity activities={stats?.recentActivity || []} />
        </Grid>

        {/* Graphique ou autres widgets */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', mb: 2 }}>
                Présences de la semaine
              </Typography>
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  Graphique des présences à venir...
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
