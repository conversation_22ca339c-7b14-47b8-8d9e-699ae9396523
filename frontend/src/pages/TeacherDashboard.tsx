import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  <PERSON>ack,
  Box,
  Alert,
  Chip,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress
} from '@mui/material';
import {
  School as SchoolIcon,
  People as PeopleIcon,
  Analytics as AnalyticsIcon,
  Face as FaceIcon,
  PlayArrow as PlayIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { supabaseService } from '../services/supabaseService';
import AutoAttendanceSystem from '../components/Attendance/AutoAttendanceSystem';
import { Course, AttendanceRecord, User, AttendanceStatus } from '../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`teacher-tabpanel-${index}`}
      aria-labelledby={`teacher-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TeacherDashboard: React.FC = () => {
  const { user, signOut } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [students, setStudents] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [attendanceSessionOpen, setAttendanceSessionOpen] = useState(false);

  /**
   * Charge les données du professeur
   */
  const loadTeacherData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Charger les cours du professeur
      const allCourses = await supabaseService.getCourses();
      const teacherCourses = allCourses.filter(course => 
        course.teacher?.id === user.id
      );
      setCourses(teacherCourses);

      // Charger tous les étudiants
      const allUsers = await supabaseService.getUsers();
      const studentUsers = allUsers.filter(u => u.role === 'student');
      setStudents(studentUsers);

      // Charger les présences pour les cours du professeur
      if (teacherCourses.length > 0) {
        const allAttendance = await Promise.all(
          teacherCourses.map(course => 
            supabaseService.getAttendanceByCourse(course.id)
          )
        );
        const flatAttendance = allAttendance.flat();
        setAttendanceRecords(flatAttendance);
      }

    } catch (err) {
      console.error('Erreur chargement données professeur:', err);
      setError('Impossible de charger vos données');
    } finally {
      setLoading(false);
    }
  };

  // Charger les données au montage
  useEffect(() => {
    loadTeacherData();
  }, [user]);

  /**
   * Calcule les statistiques générales
   */
  const getGeneralStats = () => {
    const totalSessions = attendanceRecords.length;
    const present = attendanceRecords.filter(r => r.status === AttendanceStatus.PRESENT).length;
    const late = attendanceRecords.filter(r => r.status === AttendanceStatus.LATE).length;
    const absent = attendanceRecords.filter(r => r.status === AttendanceStatus.ABSENT).length;
    const attendanceRate = totalSessions > 0 ? Math.round(((present + late) / totalSessions) * 100) : 0;

    return {
      totalCourses: courses.length,
      totalStudents: students.length,
      totalSessions,
      attendanceRate
    };
  };

  /**
   * Obtient l'icône pour un statut de présence
   */
  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return <CheckCircleIcon color="success" />;
      case AttendanceStatus.LATE:
        return <WarningIcon color="warning" />;
      case AttendanceStatus.ABSENT:
        return <CancelIcon color="error" />;
      default:
        return null;
    }
  };

  /**
   * Obtient la couleur pour un statut de présence
   */
  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'success';
      case AttendanceStatus.LATE:
        return 'warning';
      case AttendanceStatus.ABSENT:
        return 'error';
      default:
        return 'default';
    }
  };

  const stats = getGeneralStats();

  if (!user) {
    return (
      <Container>
        <Alert severity="error">
          Utilisateur non connecté
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* En-tête */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h3" gutterBottom>
            Dashboard Professeur
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Bienvenue, Prof. {user.firstName} {user.lastName}
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<FaceIcon />}
            onClick={() => setAttendanceSessionOpen(true)}
            disabled={courses.length === 0}
          >
            Session de Présence
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={signOut}
          >
            Déconnexion
          </Button>
        </Stack>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Statistiques générales */}
      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mb: 4 }}>
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <SchoolIcon color="primary" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.totalCourses}</Typography>
                <Typography color="text.secondary">Mes Cours</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <PeopleIcon color="secondary" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.totalStudents}</Typography>
                <Typography color="text.secondary">Étudiants</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <AnalyticsIcon color="success" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.totalSessions}</Typography>
                <Typography color="text.secondary">Sessions</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <CheckCircleIcon color="warning" fontSize="large" />
              <Box>
                <Typography variant="h4">{stats.attendanceRate}%</Typography>
                <Typography color="text.secondary">Taux Présence</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Stack>

      {/* Onglets */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Mes Cours" icon={<SchoolIcon />} />
          <Tab label="Présences Récentes" icon={<AnalyticsIcon />} />
          <Tab label="Étudiants" icon={<PeopleIcon />} />
        </Tabs>
      </Box>

      {/* Contenu des onglets */}
      <TabPanel value={tabValue} index={0}>
        {/* Mes cours */}
        <Typography variant="h6" gutterBottom>
          Mes Cours
        </Typography>
        
        {courses.length === 0 ? (
          <Alert severity="info">
            Aucun cours assigné. Contactez l'administrateur pour vous assigner des cours.
          </Alert>
        ) : (
          <Stack spacing={2}>
            {courses.map((course) => (
              <Card key={course.id} variant="outlined">
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Box>
                      <Typography variant="h6">{course.name}</Typography>
                      <Typography color="text.secondary" gutterBottom>
                        {course.description}
                      </Typography>
                      <Stack direction="row" spacing={1}>
                        <Chip label={course.code} size="small" />
                        <Chip label={`${course.credits} crédits`} size="small" />
                        <Chip label={course.semester} size="small" />
                      </Stack>
                    </Box>
                    
                    <Button
                      variant="outlined"
                      startIcon={<PlayIcon />}
                      onClick={() => {
                        setSelectedCourse(course);
                        setAttendanceSessionOpen(true);
                      }}
                    >
                      Démarrer Session
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Stack>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Présences récentes */}
        <Typography variant="h6" gutterBottom>
          Présences Récentes
        </Typography>
        
        {attendanceRecords.length === 0 ? (
          <Alert severity="info">
            Aucun enregistrement de présence trouvé.
          </Alert>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Étudiant</TableCell>
                  <TableCell>Cours</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Méthode</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {attendanceRecords
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .slice(0, 20) // Afficher les 20 derniers
                  .map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      {new Date(record.date).toLocaleDateString('fr-FR')}
                    </TableCell>
                    <TableCell>
                      {record.student.firstName} {record.student.lastName}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {record.course.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1} alignItems="center">
                        {getStatusIcon(record.status)}
                        <Chip
                          label={record.status}
                          color={getStatusColor(record.status) as any}
                          size="small"
                        />
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={record.method}
                        variant="outlined"
                        size="small"
                        icon={record.method === 'face_recognition' ? <FaceIcon /> : undefined}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Liste des étudiants */}
        <Typography variant="h6" gutterBottom>
          Liste des Étudiants
        </Typography>
        
        <Stack spacing={2}>
          {students.map((student) => (
            <Card key={student.id} variant="outlined">
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="h6">
                      {student.firstName} {student.lastName}
                    </Typography>
                    <Typography color="text.secondary">
                      {student.email} • {student.username}
                    </Typography>
                  </Box>
                  
                  <Chip 
                    label={student.isActive ? 'Actif' : 'Inactif'} 
                    color={student.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
              </CardContent>
            </Card>
          ))}
        </Stack>
      </TabPanel>

      {/* Dialog de session de présence */}
      <Dialog 
        open={attendanceSessionOpen} 
        onClose={() => setAttendanceSessionOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Session de Présence Automatique
          {selectedCourse && ` - ${selectedCourse.name}`}
        </DialogTitle>
        <DialogContent>
          {selectedCourse ? (
            <AutoAttendanceSystem
              course={selectedCourse}
              onAttendanceUpdate={(records) => {
                // Mettre à jour les enregistrements locaux
                setAttendanceRecords(prev => [...records, ...prev]);
              }}
            />
          ) : courses.length > 0 ? (
            <AutoAttendanceSystem
              course={courses[0]}
              onAttendanceUpdate={(records) => {
                setAttendanceRecords(prev => [...records, ...prev]);
              }}
            />
          ) : (
            <Alert severity="warning">
              Aucun cours disponible pour démarrer une session.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setAttendanceSessionOpen(false);
            setSelectedCourse(null);
          }}>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TeacherDashboard;
