/**
 * Configuration Firebase pour PresencePro
 */

import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Configuration Firebase pour PresencePro
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || "AIzaSyCMoA8Up7238cV0siW9-zOUvh4M9cz0_UE",
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "presencepro-3f21f.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "presencepro-3f21f",
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || "presencepro-3f21f.firebasestorage.app",
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "691712514199",
  appId: process.env.REACT_APP_FIREBASE_APP_ID || "1:691712514199:web:abb3656875005cb23831d2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);

// Initialiser les services Firebase
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;
