/**
 * Hook personnalisé pour utiliser Firebase dans PresencePro
 */

import { useState, useEffect } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { firebaseService } from '../services/firebaseService';
import { User, UserRole } from '../types';

interface UseFirebaseReturn {
  // État d'authentification
  firebaseUser: FirebaseUser | null;
  user: User | null;
  loading: boolean;
  error: string | null;
  
  // Méthodes d'authentification
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<void>;
  logout: () => Promise<void>;
  
  // Méthodes de données
  refreshUserData: () => Promise<void>;
}

export const useFirebase = (): UseFirebaseReturn => {
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Écouter les changements d'authentification Firebase
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      setError(null);
      
      try {
        if (firebaseUser) {
          setFirebaseUser(firebaseUser);
          // Récupérer les données utilisateur depuis Firestore
          const userData = await firebaseService.getUserById(firebaseUser.uid);
          setUser(userData);
        } else {
          setFirebaseUser(null);
          setUser(null);
        }
      } catch (err) {
        console.error('Erreur lors de la récupération des données utilisateur:', err);
        setError('Erreur lors de la récupération des données utilisateur');
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  // Connexion
  const signIn = async (email: string, password: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await signInWithEmailAndPassword(auth, email, password);
      // L'utilisateur sera automatiquement mis à jour via onAuthStateChanged
    } catch (err: any) {
      console.error('Erreur de connexion:', err);
      setError(getFirebaseErrorMessage(err.code));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Inscription
  const signUp = async (email: string, password: string, userData: Partial<User>): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Créer le profil utilisateur dans Firestore
      const newUserData: Omit<User, 'id'> = {
        username: userData.username || email.split('@')[0],
        email: firebaseUser.email || email,
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),
        role: userData.role || UserRole.STUDENT,
        roleDisplay: userData.roleDisplay || 'Étudiant',
        phoneNumber: userData.phoneNumber,
        dateOfBirth: userData.dateOfBirth,
        address: userData.address,
        profilePicture: userData.profilePicture,
        isActive: true,
        dateJoined: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      };
      
      await firebaseService.createUser(newUserData);
      
    } catch (err: any) {
      console.error('Erreur d\'inscription:', err);
      setError(getFirebaseErrorMessage(err.code));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Déconnexion
  const logout = async (): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await signOut(auth);
      // L'utilisateur sera automatiquement mis à jour via onAuthStateChanged
    } catch (err: any) {
      console.error('Erreur de déconnexion:', err);
      setError('Erreur lors de la déconnexion');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Actualiser les données utilisateur
  const refreshUserData = async (): Promise<void> => {
    if (!firebaseUser) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const userData = await firebaseService.getUserById(firebaseUser.uid);
      setUser(userData);
    } catch (err) {
      console.error('Erreur lors de l\'actualisation des données:', err);
      setError('Erreur lors de l\'actualisation des données');
    } finally {
      setLoading(false);
    }
  };

  return {
    firebaseUser,
    user,
    loading,
    error,
    signIn,
    signUp,
    logout,
    refreshUserData
  };
};

// Fonction utilitaire pour traduire les erreurs Firebase
const getFirebaseErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'Aucun utilisateur trouvé avec cette adresse email';
    case 'auth/wrong-password':
      return 'Mot de passe incorrect';
    case 'auth/email-already-in-use':
      return 'Cette adresse email est déjà utilisée';
    case 'auth/weak-password':
      return 'Le mot de passe doit contenir au moins 6 caractères';
    case 'auth/invalid-email':
      return 'Adresse email invalide';
    case 'auth/too-many-requests':
      return 'Trop de tentatives. Veuillez réessayer plus tard';
    case 'auth/network-request-failed':
      return 'Erreur de connexion réseau';
    default:
      return 'Une erreur est survenue. Veuillez réessayer';
  }
};

export default useFirebase;
