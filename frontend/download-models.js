/**
 * Script pour télécharger automatiquement les modèles face-api.js
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const MODELS_DIR = path.join(__dirname, 'public', 'models');
const BASE_URL = 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/';

const MODELS = [
  'tiny_face_detector_model-weights_manifest.json',
  'tiny_face_detector_model-shard1',
  'face_landmark_68_model-weights_manifest.json',
  'face_landmark_68_model-shard1',
  'face_recognition_model-weights_manifest.json',
  'face_recognition_model-shard1',
  'face_recognition_model-shard2',
  'face_expression_model-weights_manifest.json',
  'face_expression_model-shard1',
  'ssd_mobilenetv1_model-weights_manifest.json',
  'ssd_mobilenetv1_model-shard1',
  'ssd_mobilenetv1_model-shard2'
];

/**
 * Télécharge un fichier
 */
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(destination, () => {}); // Supprimer le fichier en cas d'erreur
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * Vérifie si un fichier existe
 */
function fileExists(filePath) {
  try {
    return fs.statSync(filePath).isFile();
  } catch (err) {
    return false;
  }
}

/**
 * Formate la taille en octets
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Fonction principale
 */
async function downloadModels() {
  console.log('🚀 Téléchargement des modèles face-api.js...\n');
  
  // Créer le dossier models s'il n'existe pas
  if (!fs.existsSync(MODELS_DIR)) {
    fs.mkdirSync(MODELS_DIR, { recursive: true });
    console.log('📁 Dossier models créé');
  }
  
  let totalSize = 0;
  let downloadedCount = 0;
  let skippedCount = 0;
  
  for (const model of MODELS) {
    const url = BASE_URL + model;
    const destination = path.join(MODELS_DIR, model);
    
    // Vérifier si le fichier existe déjà
    if (fileExists(destination)) {
      const stats = fs.statSync(destination);
      console.log(`⏭️  ${model} (${formatBytes(stats.size)}) - déjà présent`);
      totalSize += stats.size;
      skippedCount++;
      continue;
    }
    
    try {
      console.log(`⬇️  Téléchargement de ${model}...`);
      await downloadFile(url, destination);
      
      const stats = fs.statSync(destination);
      console.log(`✅ ${model} (${formatBytes(stats.size)}) - téléchargé`);
      totalSize += stats.size;
      downloadedCount++;
      
    } catch (error) {
      console.error(`❌ Erreur lors du téléchargement de ${model}:`, error.message);
    }
  }
  
  console.log('\n📊 Résumé:');
  console.log(`   • Fichiers téléchargés: ${downloadedCount}`);
  console.log(`   • Fichiers déjà présents: ${skippedCount}`);
  console.log(`   • Taille totale: ${formatBytes(totalSize)}`);
  
  if (downloadedCount + skippedCount === MODELS.length) {
    console.log('\n🎉 Tous les modèles sont prêts !');
    console.log('   Vous pouvez maintenant utiliser la reconnaissance faciale.');
  } else {
    console.log('\n⚠️  Certains modèles n\'ont pas pu être téléchargés.');
    console.log('   Vérifiez votre connexion internet et réessayez.');
  }
}

// Exécuter le script
if (require.main === module) {
  downloadModels().catch(console.error);
}

module.exports = { downloadModels };
