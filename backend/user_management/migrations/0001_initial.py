# Generated by Django 4.2.20 on 2025-06-10 17:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nom du groupe')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('academic_year', models.CharField(max_length=20, verbose_name='Année académique')),
                ('level', models.Char<PERSON>ield(help_text='Ex: L1, L2, L3, M1, M2', max_length=50, verbose_name='Niveau')),
                ('specialization', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True, verbose_name='Spécialisation')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
            ],
            options={
                'verbose_name': "Groupe d'étudiants",
                'verbose_name_plural': "Groupes d'étudiants",
                'ordering': ['academic_year', 'level', 'name'],
                'unique_together': {('name', 'academic_year')},
            },
        ),
        migrations.CreateModel(
            name='TeacherDepartment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Nom du département')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='Code du département')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email du département')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Téléphone')),
                ('office_location', models.CharField(blank=True, max_length=100, null=True, verbose_name='Localisation du bureau')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('head_of_department', models.ForeignKey(blank=True, limit_choices_to={'role': 'teacher'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Département',
                'verbose_name_plural': 'Départements',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='TeacherProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name="Numéro d'employé")),
                ('position', models.CharField(help_text='Ex: Professeur, Maître de conférences, Chargé de cours', max_length=100, verbose_name='Poste')),
                ('specialization', models.CharField(max_length=200, verbose_name='Spécialisation')),
                ('qualifications', models.TextField(blank=True, null=True, verbose_name='Qualifications')),
                ('research_interests', models.TextField(blank=True, null=True, verbose_name='Intérêts de recherche')),
                ('office_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Numéro de bureau')),
                ('office_hours', models.TextField(blank=True, null=True, verbose_name='Heures de bureau')),
                ('professional_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email professionnel')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name="Date d'embauche")),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='teachers', to='user_management.teacherdepartment')),
                ('user', models.OneToOneField(limit_choices_to={'role': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='teacher_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Profil enseignant',
                'verbose_name_plural': 'Profils enseignants',
                'ordering': ['user__last_name', 'user__first_name'],
            },
        ),
        migrations.CreateModel(
            name='StudentGroupMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='Date de début')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='Date de fin')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='user_management.studentgroup')),
                ('student', models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='group_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Appartenance au groupe',
                'verbose_name_plural': 'Appartenances aux groupes',
                'ordering': ['-start_date'],
                'unique_together': {('student', 'group')},
            },
        ),
    ]
